import re
from collections import defaultdict

import babel
import requests
import time
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import translation
from phonenumber_field.widgets import REGION_CODE_TO_COUNTRY_CODE

from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.dictionaries.models.countries import Country
from muaytax.email_notifications.utils import get_mail_signature_muaytax
from muaytax.utils.env_resources import logo_url_head_muaytax


def get_economic_activity_iae_seller(seller):
    economic_activity = EconomicActivity.objects.all()

    def remove_letters(string):
        # expresión regular para eliminar letras y guiones en los primeros 3 caracteres
        # de los códigos de las actividades económicas
        return re.sub(r'^[a-zA-Z-]{3}', '', string)

    # obtiene los códigos sin el iso_code, no repetidos, de las actividades económicas
    # de los paises iva contratados del vendedor

    code_list = set([
        remove_letters(x) for x in
        seller.vat_seller.filter(is_contracted=True).values_list(
            'activity_sellervat__sellervat_activity_iae', flat=True
        ) if x
    ])
    # obtiene las actividades económicas de los códigos sin el iso_code
    economic_activity = economic_activity.filter(code__in=code_list)
    return economic_activity


def get_economic_activity_iae_seller_not_check_contracted(seller):
    economic_activity = EconomicActivity.objects.all()

    def remove_letters(string):
        # expresión regular para eliminar letras y guiones en los primeros 3 caracteres
        # de los códigos de las actividades económicas
        return re.sub(r'^[a-zA-Z-]{3}', '', string)

    # obtiene los códigos sin el iso_code, no repetidos, de las actividades económicas
    # de los paises iva contratados del vendedor

    code_list = set([
        remove_letters(x) for x in
        seller.vat_seller.values_list(
            'activity_sellervat__sellervat_activity_iae', flat=True
        ) if x
    ])
    # obtiene las actividades económicas de los códigos sin el iso_code
    economic_activity = economic_activity.filter(code__in=code_list)
    return economic_activity


def get_economic_activity_iae_seller_not_remove(seller):
    economic_activity = EconomicActivity.objects.all()

    # obtiene los códigos no repetidos de las actividades económicas
    # de los paises iva contratados del vendedor
    code_list = set([
        x for x in
        seller.vat_seller.filter(is_contracted=True).values_list(
            'activity_sellervat__sellervat_activity_iae', flat=True
        ) if x
    ])
    # obtiene las actividades económicas de los códigos sin el iso_code
    economic_activity = economic_activity.filter(code__in=code_list)
    return economic_activity


def get_economic_activity_iae_seller_not_remove_allvats(seller):
    economic_activity = EconomicActivity.objects.all()

    # obtiene los códigos no repetidos de las actividades económicas
    # de los paises iva contratados del vendedor
    code_list = set([
        x for x in
        seller.vat_seller.values_list(
            'activity_sellervat__sellervat_activity_iae', flat=True
        ) if x
    ])
    # obtiene las actividades económicas de los códigos sin el iso_code
    economic_activity = economic_activity.filter(code__in=code_list)
    return economic_activity


def get_economic_activity_iae_seller_for_txt_amz(seller):
    economic_activity = EconomicActivity.objects.all()

    def remove_letters(string):
        # expresión regular para eliminar letras y guiones en los primeros 3 caracteres
        # de los códigos de las actividades económicas
        return re.sub(r'^[a-zA-Z-]{3}', '', string)

    # obtiene los códigos sin el iso_code, no repetidos, de las actividades económicas
    # de los paises iva del vendedor
    if seller.contracted_accounting_txt == True:
        code_list = set([
            remove_letters(x) for x in
            seller.vat_seller.all().values_list(
                'activity_sellervat__sellervat_activity_iae', flat=True
            ) if x
        ])
    else:
        code_list = set([
            remove_letters(x) for x in
            seller.vat_seller.filter(is_contracted=True).values_list(
                'activity_sellervat__sellervat_activity_iae', flat=True
            ) if x
        ])

    # obtiene las actividades económicas de los códigos sin el iso_code
    economic_activity = economic_activity.filter(code__in=code_list)
    return economic_activity


def get_economic_activity_iae_seller_for_txt_amz_not_check_contracted(seller):
    economic_activity = EconomicActivity.objects.all()

    def remove_letters(string):
        # expresión regular para eliminar letras y guiones en los primeros 3 caracteres
        # de los códigos de las actividades económicas
        return re.sub(r'^[a-zA-Z-]{3}', '', string)

    # obtiene los códigos sin el iso_code, no repetidos, de las actividades económicas
    # de los paises iva del vendedor
    if seller.contracted_accounting_txt == True:
        code_list = set([
            remove_letters(x) for x in
            seller.vat_seller.all().values_list(
                'activity_sellervat__sellervat_activity_iae', flat=True
            ) if x
        ])
    else:
        code_list = set([
            remove_letters(x) for x in
            seller.vat_seller.values_list(
                'activity_sellervat__sellervat_activity_iae', flat=True
            ) if x
        ])

    # obtiene las actividades económicas de los códigos sin el iso_code
    economic_activity = economic_activity.filter(code__in=code_list)
    return economic_activity

def get_regime_list(seller, iae_list):
    if not iae_list:  # Si iae_list está vacío --> todos los regímenes
        regime_set = {
            activity.regime for vat in seller.vat_seller.filter(vat_country__iso_code='ES') for activity in
            vat.activity_sellervat.all()
        }
    else:
        regime_set = set()
        # Para cada código en iae_list, filtra los regímenes | agrégalos al conjunto
        for iae in iae_list:
            regime_set.update({
                activity.regime for vat in seller.vat_seller.filter(vat_country__iso_code='ES') for activity in
                vat.activity_sellervat.filter(sellervat_activity_iae__code__endswith=iae)
            })
    
    return "/".join(e.description for e in sorted(regime_set, key=lambda x: x.code)) if regime_set else ""

def localized_choices():
    language = translation.get_language() or settings.LANGUAGE_CODE
    choices = [("", "---------")]
    locale_name = translation.to_locale(language)
    locale = babel.Locale(locale_name)
    for region_code, country_code in REGION_CODE_TO_COUNTRY_CODE.items():
        region_name = locale.territories.get(region_code)
        if region_name:
            choices.append((region_code, f"{region_name} (+{country_code})"))
    # ordena las opciones por la segunda columna de las tuplas del array choices
    choices.sort(key=lambda x: x[1])
    return choices


def check_vies():
    sellervats = SellerVat.objects.filter(is_contracted=True, vat_vies=True, vat_number__isnull=False)
    seller_grouped = defaultdict(list)

    for vats in sellervats:
        # Asignamos el VIES
        vies = vats.vat_number

        # Asignamos el pais
        country = vats.vat_country.iso_code

        # Montamo la URL de la peticion
        url = f"https://ec.europa.eu/taxation_customs/vies/rest-api/ms/{country}/vat/{vies}"

        # Espera 1 Segundos
        time.sleep(1)

        # Realizamos la peticion
        rq = requests.get(url)

        # Si la peticion es correcta
        if rq.ok:
            # Obtenemos el JSON de respuesta
            resp = rq.json()

            # Si el vies no es valido
            if not resp.get('isValid', True):
                # Espera 3 Segundos
                time.sleep(3)

                # Realizamos una segunda peticion para asegurarnos que la respuesta es la misma
                rq2 = requests.get(url)

                # Si la peticion es correcta
                if rq2.ok:
                    # Obtenemos el JSON de respuesta
                    resp2 = rq2.json()

                    # Si el vies no es valido
                    if not resp2.get('isValid', True):

                        # Agrupa los vat por seller en un defaultdict
                        seller_grouped[vats.seller].append(vats)

                        # Actualiza el vies a False
                        # vats.vat_vies = False
                        # vats.save()


    if seller_grouped:
        message = render_to_string("emails/scheduled_emails/vies_notification.html", {
            'sellervats': seller_grouped.items(),
            'mail_signature': get_mail_signature_muaytax(),
            'logo_head_muaytax': logo_url_head_muaytax()
        })

        send_mail(
            'Vies desactivado',
            'Se ha desactivado el VIES de los vendedores por no tener número de IVA',
            '<EMAIL>',
            ['<EMAIL>','<EMAIL>'],
            html_message=message
        )

def clean_nif(nif):
    """
    Limpia el ISO CODE de un NIF/CIF
    """
    if nif is not None:
        char=nif.replace(" ", "")[:2]
        char=char.upper() if char.isalpha() else char
        country = Country.objects.filter(iso_code=char).first()
        if country:
            nif = nif.replace(country.iso_code, "")
    else:
        nif = ''
    return nif

