{% extends "layouts/base.html" %}
{% load  static crispy_forms_tags crispy_forms_field %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <style>
    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
  </style>
{% endblock %}
{% block title %}Servicios Contratados{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title d-flex align-items-center">
            <h5>
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              {{ object.name }}
              ( {{ object.user.email }} )
            </h5>
          </div>
          <ul class="breadcrumb mt-3">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' object.shortname %}">
                {{ object.name }}
              </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Servicios Contratados</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data"
            action="{% url 'app_sellers:contracted_services' object.shortname %}">
        {% csrf_token %}
        <div class="row">
          {% for field in form %}
            <div class="col-md-6">
              <div id="div_{{ field.auto_id }}" class="mb-3">
                {% if field.label %}
                  <div class="row">
                    <div class="col-md-12 d-flex align-items-center">
                      <label for="{{ field.id_for_label }}"
                             class="flex-grow-auto col-form-label{% if field.field.required %} requiredField{% endif %}"
                             title="{{ field.label }}">
                        {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
                      </label>
                      {% if field.help_text %}
                        <i class="col-auto col-form-label feather icon-info tooltip-icon ms-2"
                           title="{{ field.help_text }}"></i>
                      {% endif %}
                    </div>
                  </div>
                {% endif %}
                {% if field.errors %}
                  {% crispy_field field 'class' 'form-control is-invalid' %}
                  <div class="invalid-feedback">
                    {% for error in field.errors %}
                      <span class="text-danger">{{ error }}</span>
                    {% endfor %}
                  </div>
                {% else %}
                  {% crispy_field field 'class' 'form-control' %}
                {% endif %}
              </div>
            </div>
          {% endfor %}
        </div>
        <div class="control-group">
          <div class="controls">
            <button type="submit" class="btn btn-primary">Actualizar</button>
          </div>
        </div>
      </form>
      <h5 class="card-title">Listado de Países IVA Contratados:</h5>
      <table id="iva-contracted-country-table" class="table nowrap">
        <thead class="table-head">
        <tr>
          <th>País IVA</th>
          <th>Número IVA</th>
          <th style="width:5%;">STEUERNUMMER o SIRET</th>
          <th style="width:10%;">Fecha Alta</th>
          <th style="width:10%;">Fecha Baja</th>
          <th style="width:10%;">Fecha Contratación</th>
        </tr>
        </thead>
        <tbody>
        {% for iva_country in iva_contracted %}
          <tr>
            <td class="align-middle">
              <span>{{ iva_country.vat_country }}</span>
            </td>
            <td class="align-middle">
              <span>{% firstof iva_country.vat_number "No tiene" %}</span>
            </td>
            <td class="align-middle">
              <span>{% firstof iva_country.steuernummer iva_country.siret "No tiene" %}</span>
            </td>
            <td class="align-middle">
              {% if iva_country.activation_date %}
                <span>{{ iva_country.activation_date|date:"d/M/Y"|lower }}</span>
              {% else %}
                <span class="text-center">-</span>
              {% endif %}
            </td>
            <td class="align-middle">
              {% if iva_country.deactivation_date %}
                <span>{{ iva_country.deactivation_date|date:"d/M/Y"|lower }}</span>
              {% else %}
                <span class="text-center">-</span>
              {% endif %}
            </td>
            <td class="align-middle">
              {% if iva_country.contracting_date %}
                <span>{{ iva_country.contracting_date|date:"d/M/Y"|lower }}</span>
              {% else %}
                <span class="text-center">-</span>
              {% endif %}
            </td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
  <script>
    $('#iva-contracted-country-table').DataTable({
      responsive: true,
      paging: false,
      searching: true,
      ordering: true,
      truncation: true,
      info: true,
      footer: true,
      language: {
        lengthMenu: "_MENU_",
        zeroRecords: "No se han encontrado países contratados.",
        info: "_TOTAL_ resultados. ",
        search: "Buscar:",
        infoEmpty: "No hay resultados que coincidan con su búsqueda.",
        infoFiltered: ""
      },
      fixedHeader: true,
    });
  </script>
{% endblock javascripts %}
