import glob
import json
import os

import psycopg2
from django.conf import settings
from django.db import transaction

from muaytax.users.models import User
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.tax_responsability import TaxResponsability
from muaytax.dictionaries.models.transaction_type import TransactionType
from muaytax.dictionaries.models.txt_status import TXTStatus
from muaytax.dictionaries.models.vat_rates import VatRates
from muaytax.dictionaries.models.account_expenses import AccountExpenses
from muaytax.dictionaries.models.account_expenses_sub import SubAccountExpenses
from muaytax.dictionaries.models.account_sales import AccountSales
from muaytax.dictionaries.models.account_sales_sub import SubAccountSales
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.currencies import Currency
from muaytax.dictionaries.models.customer_type import CustomerType
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.dictionaries.models.identifier_type import IdentifierType
from muaytax.dictionaries.models.invoice_category import InvoiceCategory
from muaytax.dictionaries.models.invoice_discard_reason import InvoiceDiscardReason
from muaytax.dictionaries.models.invoice_status import InvoiceStatus
from muaytax.dictionaries.models.invoice_type import InvoiceType
from muaytax.dictionaries.models.marketplaces import Marketplaces
from muaytax.dictionaries.models.model import Model
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.operation_type import OperationType
from muaytax.dictionaries.models.partner_type import PartnerType
from muaytax.dictionaries.models.payment_methods import PaymentMethod
from muaytax.dictionaries.models.period import Period
from muaytax.dictionaries.models.presented_model_results import PresentedModelResults
from muaytax.dictionaries.models.provider_type import ProviderType
from muaytax.dictionaries.models.sellervat_epigraph_regime import SellerVatEpigraphRegime
from muaytax.dictionaries.models.sellervat_es_status_activation import SellerVatEsStatusActivation
from muaytax.dictionaries.models.sellervat_es_status_alta_iae import SellerVatEsStatusAltaIae
from muaytax.dictionaries.models.sellervat_es_status_c_digital import SellerVatEsStatusCDigital
from muaytax.dictionaries.models.sellervat_es_status_eori import SellerVatEsStatusEori
from muaytax.dictionaries.models.sellervat_es_status_vies import SellerVatEsStatusVies
from muaytax.dictionaries.models.sellervat_period import SellerVatPeriod
from muaytax.dictionaries.models.sellervat_quarter import SellerVatQuarter
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_status_process_color import SellerVatStatusProcessColor
from muaytax.dictionaries.models.presented_model_results import PresentedModelResults
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.invoice_discard_reason import InvoiceDiscardReason
from muaytax.dictionaries.models.sellervat_period import SellerVatPeriod
from muaytax.dictionaries.models.sellervat_quarter import SellerVatQuarter
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_es_status_activation import SellerVatEsStatusActivation
from muaytax.dictionaries.models.sellervat_es_status_alta_iae import SellerVatEsStatusAltaIae
from muaytax.dictionaries.models.sellervat_es_status_c_digital import SellerVatEsStatusCDigital
from muaytax.dictionaries.models.sellervat_es_status_vies import SellerVatEsStatusVies
from muaytax.dictionaries.models.sellervat_es_status_eori import SellerVatEsStatusEori
from muaytax.dictionaries.models.sellervat_type import SellerVatType
from muaytax.dictionaries.models.bank_movement_status import BankMovementStatus
from muaytax.dictionaries.models.bank_movement_template import BankMovementTemplate
from muaytax.dictionaries.models.bank_type import BankType
from muaytax.dictionaries.models.importer_bankmovement_status import BankMovementImporterStatus
from muaytax.dictionaries.models.booking_subjects import BookingSubject
from muaytax.dictionaries.models.contract_type import ContractType
from muaytax.dictionaries.models.situation_seller_rental import SituationSellerRental
from muaytax.dictionaries.models.type_road import TypeRoad
from muaytax.dictionaries.models.province_code import ProvinceCode
from muaytax.dictionaries.models.municipality_code import MunicipalityCode
from muaytax.dictionaries.models.muaytax_departments import MuaytaxDepartment
from muaytax.dictionaries.models.movement_status import MovementStatus
from muaytax.dictionaries.models.states_usa import StatesUSA
from muaytax.dictionaries.models.general_settings import GeneralSettings
from muaytax.dictionaries.models.product_service import ProductService
from muaytax.dictionaries.models.permissions_in_class import Permissions_in_class
from muaytax.dictionaries.models.service_type import ServiceType
from muaytax.dictionaries.models.rap_category import RapCategory
from muaytax.dictionaries.models.type_form import TypeForm
from muaytax.dictionaries.models.task_type import TaskType
from muaytax.dictionaries.models.store_products import StoreProduct
from muaytax.dictionaries.models.us_zip_codes import UsZipCodes
from muaytax.dictionaries.models.official_amortization_table import AmortizationCoefficient

# Function to Connect to Database
def connect_to_database():
    try:
        dbname = os.getenv("POSTGRES_DB")
        user = os.getenv("POSTGRES_USER")
        password = os.getenv("POSTGRES_PASSWORD")
        host = os.getenv("POSTGRES_HOST")
        port = os.getenv("POSTGRES_PORT")
        # print(f"Conectando a la base de datos... postgres://{user}:{password}@{host}:{port}/{dbname}")
        connection = psycopg2.connect(
            dbname=dbname,
            user=user,
            password=password,
            host=host,
            port=port
        )
        print("Conexión exitosa a la base de datos.")
        return connection
    except (Exception, psycopg2.Error) as e:
        print(f"Error al conectar a la base de datos: {e}")
        return None


# Function to Execute SQL Scripts from Scrit_Folder
def execute_sql_scripts(connection, script_folder):
    os.chdir(script_folder)
    script_files = glob.glob("*.sql")
    error = False
    for script_file in sorted(script_files):
        try:
            with open(script_file, 'r') as file:
                script = file.read()

            with connection.cursor() as cursor:
                cursor.execute(script)
                connection.commit()
                print(f"Script {script_file} ejecutado correctamente.")

        except (Exception, psycopg2.Error) as e:
            print(f"Error al ejecutar el script {script_file}: {e}")
            error = True
            connection.rollback()
            print("Break...")
            break

    if error == True:
        print("[FAILED] Error al ejecutar los scripts.")


# Function to Load All Scripts of Functions
def load_all_function_scripts():
    script_folder = str(settings.ROOT_DIR / "database" / "scripts" / "functions")
    print(script_folder)
    # script_folder = "database/scripts/functions"
    connection = connect_to_database()

    if connection:
        execute_sql_scripts(connection, script_folder)
        connection.close()


# Function to Read JSON Files
def _get_data_json(folder, file):
    path = str(settings.ROOT_DIR / "muaytax" / folder / "data" / f"{file}.json")

    with open(path) as f:
        a = f.read()
        data = json.loads(a)
    return data


def _get_data_dictionary_json(file):
    return _get_data_json("dictionaries", file)


# Create Users
def create_user():
    data = _get_data_json("users", "users")
    with transaction.atomic():
        for item in data:
            print(item)
            User.objects.update_or_create(
                id=item.pop("id"),
                defaults=item,
            )


# Create Dictionaries
def create_dic_account_expenses():
    data = _get_data_dictionary_json("account_expenses")
    with transaction.atomic():
        for item in data:
            print(item)
            AccountExpenses.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_subaccount_expenses():
    data = _get_data_dictionary_json("account_expenses_sub")
    with transaction.atomic():
        for item in data:
            print(item)
            SubAccountExpenses.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_account_sales():
    data = _get_data_dictionary_json("account_sales")
    with transaction.atomic():
        for item in data:
            print(item)
            AccountSales.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_booking_subjects():
    data = _get_data_dictionary_json("booking_subjects")
    with transaction.atomic():
        for item in data:
            print(item)
            BookingSubject.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_contract_type():
    data = _get_data_dictionary_json("contract_type")
    with transaction.atomic():
        for item in data:
            print(item)
            ContractType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_subaccount_sales():
    data = _get_data_dictionary_json("account_sales_sub")
    with transaction.atomic():
        for item in data:
            print(item)
            SubAccountSales.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_countries():
    data = _get_data_dictionary_json("countries")
    with transaction.atomic():
        for item in data:
            print(item)
            Country.objects.update_or_create(
                iso_code=item.pop("iso_code"),
                defaults=item,
            )


def create_dic_currencies():
    data = _get_data_dictionary_json("currencies")
    with transaction.atomic():
        for item in data:
            print(item)
            Currency.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_customer_type():
    data = _get_data_dictionary_json("customer_type")
    with transaction.atomic():
        for item in data:
            print(item)
            CustomerType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_provider_type():
    data = _get_data_dictionary_json("provider_type")
    with transaction.atomic():
        for item in data:
            print(item)
            ProviderType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_transaction_type():
    data = _get_data_dictionary_json("transaction_type")
    with transaction.atomic():
        for item in data:
            print(item)
            TransactionType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_vat_rates():
    data = _get_data_dictionary_json("vat_rates")
    with transaction.atomic():
        for item in data:
            print(item)
            VatRates.objects.update_or_create(
                country_code=item.pop("country_code"),
                defaults=item,
            )


def create_dic_identifier_type():
    data = _get_data_dictionary_json("identifier_type")
    with transaction.atomic():
        for item in data:
            print(item)
            IdentifierType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_partner_type():
    data = _get_data_dictionary_json("partner_type")
    with transaction.atomic():
        for item in data:
            print(item)
            PartnerType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_invoice_status():
    data = _get_data_dictionary_json("invoice_status")
    with transaction.atomic():
        for item in data:
            print(item)
            InvoiceStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_marketplaces():
    data = _get_data_dictionary_json("marketplaces")
    with transaction.atomic():
        for item in data:
            print(item)
            Marketplaces.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_operation_type():
    data = _get_data_dictionary_json("operation_type")
    with transaction.atomic():
        for item in data:
            print(item)
            OperationType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_invoice_category():
    data = _get_data_dictionary_json("invoice_category")
    with transaction.atomic():
        for item in data:
            print(item)
            InvoiceCategory.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_payment_method():
    data = _get_data_dictionary_json("payment_method")
    with transaction.atomic():
        for item in data:
            print(item)
            PaymentMethod.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_tax_responsability():
    data = _get_data_dictionary_json("tax_responsability")
    with transaction.atomic():
        for item in data:
            print(item)
            TaxResponsability.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_economic_activity():
    data = _get_data_dictionary_json("economic_activity")
    with transaction.atomic():
        for item in data:
            print(item)
            EconomicActivity.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_invoice_type():
    data = _get_data_dictionary_json("invoice_type")
    with transaction.atomic():
        for item in data:
            print(item)
            InvoiceType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_period():
    data = _get_data_dictionary_json("period")
    with transaction.atomic():
        for item in data:
            print(item)
            Period.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_model():
    data = _get_data_dictionary_json("model")
    with transaction.atomic():
        for item in data:
            print(item)
            Model.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_txt_status():
    data = _get_data_dictionary_json("txt_status")
    with transaction.atomic():
        for item in data:
            print(item)
            TXTStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_status():
    data = _get_data_dictionary_json("sellervat_status")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_document_type():
    data = _get_data_dictionary_json("document_type")
    with transaction.atomic():
        for item in data:
            print(item)
            DocumentType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_period():
    data = _get_data_dictionary_json("sellervat_period")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatPeriod.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_quarter():
    data = _get_data_dictionary_json("sellervat_quarter")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatQuarter.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_invoice_discard_reason():
    data = _get_data_dictionary_json("invoice_discard_reason")
    with transaction.atomic():
        for item in data:
            print(item)
            InvoiceDiscardReason.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_status_process():
    data = _get_data_dictionary_json("sellervat_status_process")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatStatusProcess.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_status_process_color():
    data = _get_data_dictionary_json("sellervat_status_process_color")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatStatusProcessColor.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_es_status_activation():
    data = _get_data_dictionary_json("sellervat_es_status_activation")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatEsStatusActivation.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_es_status_alta_iae():
    data = _get_data_dictionary_json("sellervat_es_status_alta_iae")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatEsStatusAltaIae.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_es_status_c_digital():
    data = _get_data_dictionary_json("sellervat_es_status_c_digital")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatEsStatusCDigital.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_es_status_vies():
    data = _get_data_dictionary_json("sellervat_es_status_vies")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatEsStatusVies.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_es_status_eori():
    data = _get_data_dictionary_json("sellervat_es_status_eori")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatEsStatusEori.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_sellervat_type():
    data = _get_data_dictionary_json("sellervat_type")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_model_status():
    data = _get_data_dictionary_json("model_status")
    with transaction.atomic():
        for item in data:
            print(item)
            ModelStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )


def create_dic_presented_model_results():
    data = _get_data_dictionary_json("presented_model_results")
    with transaction.atomic():
        for item in data:
            print(item)
            PresentedModelResults.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
def create_dic_bank_type():
    data = _get_data_dictionary_json("bank_type")
    with transaction.atomic():
        for item in data:
            print(item)
            BankType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
def create_dic_movement_status():
    data = _get_data_dictionary_json("movement_status")
    with transaction.atomic():
        for item in data:
            print(item)
            MovementStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_bank_movement_status():
    data = _get_data_dictionary_json("bank_movement_status")
    with transaction.atomic():
        for item in data:
            print(item)
            BankMovementStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
def create_dic_importer_bankmovement_status():
    data = _get_data_dictionary_json("importer_bankmovement_status")
    with transaction.atomic():
        for item in data:
            print(item)
            BankMovementImporterStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
def create_dic_bank_movement_template():
    data = _get_data_dictionary_json("bank_movement_template")
    with transaction.atomic():
        for item in data:
            print(item)
            BankMovementTemplate.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_reconciliation_type():
    data = _get_data_dictionary_json("reconciliation_type")
    with transaction.atomic():
        for item in data:
            print(item)
            ReconciliationType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_accounting_account():
    data = _get_data_dictionary_json("accounting_account")
    with transaction.atomic():
        for item in data:
            print(item)
            AccountingAccount.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_sellervat_epigraph_regime():
    data = _get_data_dictionary_json("sellervat_epigraph_regime")
    with transaction.atomic():
        for item in data:
            print(item)
            SellerVatEpigraphRegime.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_situation_seller_rental():
    data = _get_data_dictionary_json("situation_seller_rental")
    with transaction.atomic():
        for item in data:
            print(item)
            SituationSellerRental.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_type_road_seller_rental():
    data = _get_data_dictionary_json("type_road")
    with transaction.atomic():
        for item in data:
            print(item)
            TypeRoad.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_province_code():
    data = _get_data_dictionary_json("province_code")
    with transaction.atomic():
        for item in data:
            print(item)
            ProvinceCode.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_municipality_code():
    data = _get_data_dictionary_json("municipality_code")
    with transaction.atomic():
        for item in data:
            print(item)
            MunicipalityCode.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_muaytax_departments():
    data = _get_data_dictionary_json("muaytax_departments")
    with transaction.atomic():
        for item in data:
            print(item)
            MuaytaxDepartment.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_states_usa():
    data = _get_data_dictionary_json("states_usa")
    with transaction.atomic():
        for item in data:
            print(item)
            StatesUSA.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_general_settings():
    data = _get_data_dictionary_json("general_settings")
    with transaction.atomic():
        for item in data:
            print(item)
            GeneralSettings.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_product_service():
    data = _get_data_dictionary_json("product_service")
    with transaction.atomic():
        for item in data:
            print(item)
            ProductService.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_permissions_in_class():
    data = _get_data_dictionary_json("permissions_in_class")
    with transaction.atomic():
        for item in data:
            print(item)
            Permissions_in_class.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_services():
    data = _get_data_dictionary_json("service_type")
    with transaction.atomic():
        for item in data:
            print(item)
            ServiceType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_task_type():
    data = _get_data_dictionary_json("tasks_type")
    with transaction.atomic():
        for item in data:
            print(item)
            TaskType.objects.update_or_create(
                task_code=item.pop("task_code"),
                defaults=item,
            )

def create_dic_rap_category():
    data = _get_data_dictionary_json("rap_category")
    with transaction.atomic():
        for item in data:
            print(item)
            RapCategory.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_type_form():
    data = _get_data_dictionary_json("type_form")
    with transaction.atomic():
        for item in data:
            print(item)
            TypeForm.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_store_products():
    data = _get_data_dictionary_json("store_products")
    with transaction.atomic():
        for item in data:
            print(item)
            StoreProduct.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )

def create_dic_us_zip_codes():
    data = _get_data_dictionary_json("us_zip_codes")
    with transaction.atomic():
        for item in data:
            print(item)
            UsZipCodes.objects.update_or_create(
                zip_code=item.pop("zip"),
                defaults=item,
            )  

def create_dic_amortization_coefficient_codes():
    data = _get_data_dictionary_json("official_amortization_table")
    with transaction.atomic():
        for item in data:
            print(item)
            AmortizationCoefficient.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            ) 

# Load all dictionaries
def load_all_dictionaies():
    # create_user()
    create_dic_countries()
    create_dic_currencies()
    create_dic_account_expenses()
    create_dic_subaccount_expenses()
    create_dic_account_sales()
    create_dic_subaccount_sales()
    create_dic_countries()
    create_dic_currencies()
    create_dic_customer_type()
    create_dic_provider_type()
    create_dic_transaction_type()
    create_dic_vat_rates()
    create_dic_identifier_type()
    create_dic_partner_type()
    create_dic_invoice_status()
    create_dic_marketplaces()
    create_dic_operation_type()
    create_dic_invoice_category()
    create_dic_payment_method()
    create_dic_tax_responsability()
    create_dic_economic_activity()
    create_dic_invoice_type()
    create_dic_period()
    create_dic_model()
    create_dic_txt_status()
    create_dic_sellervat_status()
    create_dic_document_type()
    create_dic_sellervat_period()
    create_dic_sellervat_quarter()
    create_dic_invoice_discard_reason()
    create_dic_sellervat_status_process()
    create_dic_sellervat_es_status_activation()
    create_dic_sellervat_es_status_alta_iae()
    create_dic_sellervat_es_status_c_digital()
    create_dic_sellervat_es_status_vies()
    create_dic_sellervat_es_status_eori()
    create_dic_model_status()
    create_dic_presented_model_results()
    create_dic_sellervat_type()
    create_dic_sellervat_status_process_color()
    create_dic_bank_type()
    create_dic_movement_status()
    create_dic_bank_movement_status()
    create_dic_importer_bankmovement_status()
    create_dic_bank_movement_template()
    create_dic_reconciliation_type()
    create_dic_accounting_account()
    create_dic_booking_subjects()
    create_dic_contract_type()
    create_dic_situation_seller_rental()
    create_dic_type_road_seller_rental()
    create_dic_province_code()
    # create_dic_municipality_code()
    create_dic_muaytax_departments()
    create_dic_states_usa()
    create_dic_general_settings()
    create_dic_product_service()
    create_dic_permissions_in_class()
    create_dic_services()
    create_dic_rap_category()
    create_dic_type_form()
    create_dic_task_type()
    create_dic_store_products()
    create_dic_amortization_coefficient_codes()
    # create_dic_us_zip_codes() #TODO: comentar cuando se rellene la tabla (hay mas de 40000 registros)


# Create Media Folders
def create_media_folders():
    # Create Folders in muaytax/media
    folders = [
        "generated_models",
        "generated_txt",
        "xls_invoice_list",
        "xls_entry_list",
        "xls_model_111",
        "uploads",
        "uploads/presented_models",
        "uploads/presented_models_recepipt",
        "uploads/presented_models/substitute_presentation",
        "uploads/nrc_receipt",
        "uploads/qr_verifactu"
    ]

    for folder in folders:
        new_folder = f"{settings.MEDIA_ROOT}/{folder}"
        if not os.path.exists(new_folder):
            print(f"new folder: {new_folder}")
            os.makedirs(new_folder)


# Initialize Function: Load all dictionaries + Load All Functions Scripts
def initialize():
    create_media_folders()
    load_all_function_scripts()
    load_all_dictionaies()
