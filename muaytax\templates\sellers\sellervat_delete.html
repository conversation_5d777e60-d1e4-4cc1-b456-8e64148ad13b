{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Eliminar Pais IVA{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
           <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name|title}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:vat_list' seller.shortname  %}">Paises IVA</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:vat_detail' seller.shortname object.pk %}"> {{ object.name | title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Eliminar</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="text-center">  
          <h1>Eliminar País IVA</h1>
          <p>¿Está seguro de que desea eliminar el País IVA "{{ object.vat_country }}"?</p>
          <p><b>País:</b> {{ object.vat_country }}</p>
          <p><b>IVA:</b> {{ object.vat_number }}</p>
          <p><b>Vies:</b> {{ object.vat_vies }}</p>
          <div class="control-group">
            <div class="controls">
              <button type="submit" class="btn btn-danger">Eliminar</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
{% endblock content %}
