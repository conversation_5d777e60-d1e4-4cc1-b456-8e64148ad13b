import json
from datetime import datetime

from django.db import connection
from django.db.models import Q
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponseRedirect, JsonResponse
from django.views.generic import ListView
from django_datatables_view.base_datatable_view import BaseDatatableView
from muaytax.users.permissions import IsManagerRolePermission, IsSellerShortnamePermission
from muaytax.app_sellers.models import Seller, SellerVat
from muaytax.app_invoices.models import Invoice
from muaytax.users.models import User
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models import SellerVatStatus, SellerVatStatusProcess, SellerVatEsStatusActivation, \
    SellerVatEsStatusAltaIae, SellerVatEsStatusCDigital, SellerVatEsStatusVies, SellerVatEsStatusEori, SellerVatType

from muaytax.utils.general import get_first_and_last_date, get_range_dates_gb, get_presentation_type_gb


class SellerManagementView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller

    # checks the country for the template
    def get_template_names(self):
        country = self.kwargs["country"].upper()
        if country == 'ES':
            if self.get_period(self.request) == '0A':
                return ["sellers/seller_management_es_yearly.html"]
            else:
                return ["sellers/seller_es-management.html"]
        elif country == 'DE':
            return ['sellers/seller_management_de.html']
        elif country == 'GB':
            return ['sellers/seller_management_gb.html']
        elif country == 'US':
            return ['gestoria/seller_management_usa.html']
        elif country == 'PL':
            return ['sellers/seller_vat-poland.html']
        elif country == 'CZ':
            return ['sellers/seller_vat-czech.html']
       #  TO DO: Manejar el error aqu�. No lo manejo por tratar de mantener los cambios at�micos. 4 de abril de 2025

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        year = self.get_year(self.request)
        period = self.get_period(self.request)
        entity = self.get_entity(self.request)
        context['period'] = period
        context['year'] = year
        context['entity'] = entity
        return context

    def get_year(self, request):
        year = request.GET.get('year')
        month = datetime.now().month
        if year is None:
            year = datetime.now().year
            if month == 1:
                year = year - 1
        return year

    def get_period(self, request):
        period = request.GET.get('period')
        if period is None:
            month = datetime.now().month
            if 2 <= month <= 4:
                period = "Q1"
            elif 5 <= month <= 7:
                period = "Q2"
            elif 8 <= month <= 10:
                period = "Q3"
            elif 11 <= month <= 12:
                period = "Q4"
            if month == 1:
                period = "Q4"
        return period

    def get_entity(self, request):
        entity = request.GET.get('entity')
        if entity is None:
            entity = "all"
        return entity

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerManagementESListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                               BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        entity = self.request.GET.get('entity')

        sql = f"SELECT func_managementES_list_json({year},'{period}', '{entity}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                result_data = json.loads(result_json)

                # Calula el numero de modelos pendientes dentro de la cadena
                models_to_count = ['model_111', 'model_115', 'model_130', 'model_303', 'model_309', 'model_349',
                                   'model_369']
                pending_model_count = 0
                revision_model_count = 0
                disagreed_model_count = 0
                agreed_model_count = 0
                presented_model_count = 0
                for item in result_data:
                    for model in models_to_count:
                        if model in item:
                            if item[model] == 'required':
                                pending_model_count += 1
                            elif item[model] == 'pending':
                                revision_model_count += 1
                            elif item[model] == 'disagreed':
                                disagreed_model_count += 1
                            elif item[model] == 'agreed':
                                agreed_model_count += 1
                            elif item[model] == 'presented':
                                presented_model_count += 1

                total_invoices = 0
                total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                    'pending_model_count': pending_model_count,
                    'total_invoices': total_invoices,
                    'pending_model_count': pending_model_count,
                    'revision_model_count': revision_model_count,
                    'disagreed_model_count': disagreed_model_count,
                    'agreed_model_count': agreed_model_count,
                    'presented_model_count': presented_model_count

                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerManagementESListDTYearly(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                                     BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        entity = self.request.GET.get('entity')

        sql = f"SELECT func_gestoria_es_yearly({year},'{period}','{entity}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                result_data = json.loads(result_json)

                models_to_count = ['model_180', 'model_190', 'model_347', 'model_390']
                pending_model_count = 0
                revision_model_count = 0
                disagreed_model_count = 0
                agreed_model_count = 0
                presented_model_count = 0
                for item in result_data:
                    for model in models_to_count:
                        if model in item:
                            if item[model] == 'required':
                                pending_model_count += 1
                            elif item[model] == 'pending':
                                revision_model_count += 1
                            elif item[model] == 'disagreed':
                                disagreed_model_count += 1
                            elif item[model] == 'agreed':
                                agreed_model_count += 1
                            elif item[model] == 'presented':
                                presented_model_count += 1

                total_invoices = 0
                total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                    'pending_model_count': pending_model_count,
                    'total_invoices': total_invoices,
                    'pending_model_count': pending_model_count,
                    'revision_model_count': revision_model_count,
                    'disagreed_model_count': disagreed_model_count,
                    'agreed_model_count': agreed_model_count,
                    'presented_model_count': presented_model_count
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerManagementUSListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                               BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        first_date, last_date = get_first_and_last_date(year, period)

        sql = f"SELECT func_gestoria_usa('{first_date}','{last_date}', '{period}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)

                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                models_to_count = ['model_us', 'model_7004', 'model_b15']
                required_model_count = 0
                required_completed_model_count = 0
                required_notcompleted_model_count = 0
                required_notstarted_model_count = 0
                pending_model_count = 0
                disagreed_model_count = 0
                agreed_model_count = 0
                presented_model_count = 0
                fax_send_model_count = 0
                for item in result_data:
                    for model in models_to_count:
                        if model in item:
                            if item[model] == 'processed' or item[model] == 'required':
                                required_completed_model_count += 1
                                required_model_count += 1
                            elif item[model] == 'not-processed':
                                required_notcompleted_model_count += 1
                                required_model_count += 1
                            elif item[model] == 'not-started':
                                required_notstarted_model_count += 1
                                required_model_count += 1
                            elif item[model] == 'pending':
                                pending_model_count += 1
                            elif item[model] == 'disagreed':
                                disagreed_model_count += 1
                            elif item[model] == 'agreed':
                                agreed_model_count += 1
                            elif item[model] == 'presented':
                                presented_model_count += 1
                            elif item[model] == 'fax_send':
                                fax_send_model_count += 1

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,

                    'required_model_count': required_model_count,
                    'required_completed_model_count': required_completed_model_count,
                    'required_notcompleted_model_count': required_notcompleted_model_count,
                    'required_notstarted_model_count': required_notstarted_model_count,
                    'pending_model_count': pending_model_count,
                    'disagreed_model_count': disagreed_model_count,
                    'agreed_model_count': agreed_model_count,
                    'presented_model_count': presented_model_count,
                    'fax_send_model_count': fax_send_model_count
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerManagementDEListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                               BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')

        sql = f"SELECT func_managementDE_list_json({year},'{period}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                total_invoices = 0
                total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                    'total_invoices': total_invoices,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerManagementGBListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                               BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')

        sql = f"SELECT func_managementUK_list_json({year},'{period}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                total_invoices = 0
                total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                    'total_invoices': total_invoices,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerManagementESListDTNotModelTxt(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                                          BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        entity = self.request.GET.get('entity')

        sql = f"SELECT func_managementES_list_json_not_model_txt({year},'{period}','{entity}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerVatCountryView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller

    def get_template_names(self):
        country = self.kwargs["country"].upper()

        if country == 'ES':
            if self.get_period(self.request) == '0A':
                return ["sellers/seller_vat_es_yearly.html"]
            else:
                return ["sellers/seller_vat-spain.html"]
        elif country == 'DE':
            return ['sellers/seller_vat-germany.html']
        elif country == 'IT':
            if self.get_period(self.request) == '0A':
                return ["sellers/seller_vat-italy_yearly.html"]
            else:
                return ['sellers/seller_vat-italy.html']
        elif country == 'FR':
            return ['sellers/seller_vat-france.html']
        elif country == 'GB':
            return ['sellers/seller_vat-gb.html']
        elif country == 'PL':
            return ['sellers/seller_vat-poland.html']
        elif country == 'CZ':
            return ['sellers/seller_vat-czech.html']
        else:
            # Manejo por defecto o lanzar un error si es necesario
            return ['sellers/seller_vat-default.html'] # O la plantilla por defecto adecuada

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_year(self.request)
        period = self.get_period(self.request)
        country_code = self.kwargs["country"].upper()
        country = Country.objects.get(iso_code=country_code)
        sellervat_manager = SellerVat.objects.filter(vat_country__iso_code =country_code, manager_assigned__isnull= False)
        manager_ids = sellervat_manager.values_list('manager_assigned', flat=True)
        managers = User.objects.filter(role='manager', id__in = manager_ids)
        total_pending_invoices = Invoice.objects.filter(
            seller__vat_seller__vat_country__iso_code=country.iso_code,
            seller__vat_seller__is_contracted=True).filter(status="pending").count()

        context['period'] = period
        context['year'] = year
        context['month'] = self.get_month_period(self.request)
        context['country'] = country
        context['total_pending_invoices'] = total_pending_invoices
        context['managers'] = managers
        context['current_user_id'] = self.request.user.pk

        return context

    def get_year(self, request):
        year = None
        year = request.GET.get('year')
        month = datetime.now().month
        if (year is None):
            year = datetime.now().year
            if month == 1:
                year = year - 1
        return year

    def get_period(self, request):
        period = None
        period = request.GET.get('period')
        if (period is None):
            month = datetime.now().month
            if month >= 2 and month <= 4:
                period = "Q1"
            elif month >= 5 and month <= 7:
                period = "Q2"
            elif month >= 8 and month <= 10:
                period = "Q3"
            elif month >= 11 and month <= 12:
                period = "Q4"
            if month == 1:
                period = "Q4"
        return period

    def get_month_period(self, request) -> str:
        month = None
        month = request.GET.get('period')
        if (month is None):
            month = datetime.now().month - 1
            if month == 0:
                month = 12
            return f'M{month}'
        return month

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVatCountryDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    def get(self, request, *args, **kwargs):
        country = self.kwargs["country"].upper()
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        periodo = '' if period == '0' else period
        first_date, last_date = get_first_and_last_date(year, periodo)

        if period == '0A':
            if country == 'ES':

                sql = f"SELECT func_seller_vat_ES_yearly({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                        result_data = json.loads(result_json)

                        # Calula el numero de modelos pendientes dentro de la cadena
                        models_to_count = ['model_303_q4', 'model_347', 'model_390', 'model_184']
                        pending_model_count = 0
                        revision_model_count = 0
                        disagreed_model_count = 0
                        agreed_model_count = 0
                        presented_model_count = 0

                        for item in result_data:
                            for model in models_to_count:
                                if model in item:
                                    if item[model] == 'required':
                                        pending_model_count += 1
                                    elif item[model] == 'pending':
                                        revision_model_count += 1
                                    elif item[model] == 'disagreed':
                                        disagreed_model_count += 1
                                    elif item[model] == 'agreed':
                                        agreed_model_count += 1
                                    elif item[model] == 'presented':
                                        presented_model_count += 1

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                            'revision_model_count': revision_model_count,
                            'pending_model_count': pending_model_count,
                            'disagreed_model_count': disagreed_model_count,
                            'agreed_model_count': agreed_model_count,
                            'presented_model_count': presented_model_count
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

            if country == 'IT':
                sql = f"SELECT func_seller_vat_IT_yearly({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]
                        result_data = json.loads(result_json)

                        # Calula el numero de modelos pendientes dentro de la cadena
                        models_to_count = ['anualle']
                        pending_model_count = 0
                        revision_model_count = 0
                        disagreed_model_count = 0
                        agreed_model_count = 0
                        presented_model_count = 0

                        for item in result_data:
                            for model in models_to_count:
                                if model in item:
                                    if item[model] == 'required':
                                        pending_model_count += 1
                                    elif item[model] == 'pending':
                                        revision_model_count += 1
                                    elif item[model] == 'disagreed':
                                        disagreed_model_count += 1
                                    elif item[model] == 'agreed':
                                        agreed_model_count += 1
                                    elif item[model] == 'presented':
                                        presented_model_count += 1

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                            'revision_model_count': revision_model_count,
                            'pending_model_count': pending_model_count,
                            'disagreed_model_count': disagreed_model_count,
                            'agreed_model_count': agreed_model_count,
                            'presented_model_count': presented_model_count
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

        if period != '0A' or period == '0':
            if country == 'ES':

                sql = f"SELECT func_sellervat_countryES_list_json({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                        result_data = json.loads(result_json)

                        # Calula el numero de modelos pendientes dentro de la cadena
                        models_to_count = ['model_303', 'model_349', 'model_369']
                        pending_model_count = 0
                        revision_model_count = 0
                        disagreed_model_count = 0
                        agreed_model_count = 0
                        presented_model_count = 0

                        for item in result_data:
                            for model in models_to_count:
                                if model in item:
                                    if item[model] == 'required':
                                        pending_model_count += 1
                                    elif item[model] == 'pending':
                                        revision_model_count += 1
                                    elif item[model] == 'disagreed':
                                        disagreed_model_count += 1
                                    elif item[model] == 'agreed':
                                        agreed_model_count += 1
                                    elif item[model] == 'presented':
                                        presented_model_count += 1

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)
                        # required_count = sum(1 for item in result_data if 'required' in (item['model_303'], item['model_349']))

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                            'revision_model_count': revision_model_count,
                            'pending_model_count': pending_model_count,
                            'disagreed_model_count': disagreed_model_count,
                            'agreed_model_count': agreed_model_count,
                            'presented_model_count': presented_model_count
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

            if country == 'DE':
                sql = f"SELECT func_sellervat_countryDE_list_json({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]
                        result_data = json.loads(result_json)

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

            if country == 'IT':
                sql = f"SELECT func_sellervat_countryIT_list_json({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]
                        result_data = json.loads(result_json)

                        # Calula el numero de modelos pendientes dentro de la cadena
                        if period == 'Q4':
                            models_to_count = ['model_lipe', 'acconto']
                        else:
                            models_to_count = ['model_lipe']
                        pending_model_count = 0
                        revision_model_count = 0
                        disagreed_model_count = 0
                        agreed_model_count = 0
                        presented_model_count = 0

                        for item in result_data:
                            for model in models_to_count:
                                if model in item:
                                    if item[model] == 'required':
                                        pending_model_count += 1
                                    elif item[model] == 'pending':
                                        revision_model_count += 1
                                    elif item[model] == 'disagreed':
                                        disagreed_model_count += 1
                                    elif item[model] == 'agreed':
                                        agreed_model_count += 1
                                    elif item[model] == 'presented':
                                        presented_model_count += 1

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                            'revision_model_count': revision_model_count,
                            'pending_model_count': pending_model_count,
                            'disagreed_model_count': disagreed_model_count,
                            'agreed_model_count': agreed_model_count,
                            'presented_model_count': presented_model_count
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

            if country == 'FR':
                sql = f"SELECT func_sellervat_countryFR_list_json('{first_date}','{last_date}');"
                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                        result_data = json.loads(result_json)

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

            if country == 'GB':
                sql = f"SELECT func_sellervat_countryGB_list_json({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]
                        result_data = json.loads(result_json)

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                        }
                        return JsonResponse(response)

                except Exception as e:
                    print("Error:", e)

            if country == 'PL':
                # TODO: Crear la función SQL func_sellervat_countryPL_list_json
                sql = f"SELECT func_sellervat_countrypl_list_json({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]
                        result_data = json.loads(result_json)

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                        }
                        return JsonResponse(response)

                except Exception as e:
                    # TODO: Manejar mejor la excepción si la función SQL no existe
                    print(f"Error al ejecutar func_sellervat_countrypl_list_json: {e}")
                    # Devolver una respuesta vacía o con error para DataTables
                    return JsonResponse({'draw': int(request.GET.get('draw', 1)), 'data': [], 'error': str(e)})


            if country == 'CZ':
                # TODO: Crear la función SQL func_sellervat_countryCZ_list_json
                sql = f"SELECT func_sellervat_countrycz_list_json({year},'{period}');"

                try:
                    with connection.cursor() as cursor:
                        cursor.execute(sql)
                        result_json = cursor.fetchone()[0]
                        result_data = json.loads(result_json)

                        total_invoices = 0
                        total_invoices = sum(item.get("num_pending_invoices", 0) for item in result_data)

                        response = {
                            'draw': int(request.GET.get('draw', 1)),
                            'data': result_data,
                            'total_invoices': total_invoices,
                        }
                        return JsonResponse(response)

                except Exception as e:
                     # TODO: Manejar mejor la excepción si la función SQL no existe
                    print(f"Error al ejecutar func_sellervat_countrycz_list_json: {e}")
                    # Devolver una respuesta vacía o con error para DataTables
                    return JsonResponse({'draw': int(request.GET.get('draw', 1)), 'data': [], 'error': str(e)})


        # si no es ninguno de los países anteriores devuelve datos vacíos
        response = {
            'draw': int(request.GET.get('draw', 1)),
            'data': [],
        }
        return JsonResponse(response)


class SellerListView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller
    template_name = "sellers/seller_list.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_year(self.request)
        period = self.get_period(self.request)
        # total_pending_invoices = Invoice.objects.filter(Q(status="pending") | Q(status="revision-pending")).exclude(
        #     invoice_category__pk__icontains='_copy').count()

        context['period'] = period
        context['year'] = year
        # context['total_pending_invoices'] = total_pending_invoices
        return context

    def get_year(self, request):
        year = None
        year = request.GET.get('year')
        month = datetime.now().month
        if year is None:
            year = datetime.now().year
        return year

    def get_period(self, request):
        period = None
        period = request.GET.get('period')
        if (period is None):
            month = datetime.now().month
            if month >= 2 and month <= 4:
                period = "Q1"
            elif month >= 5 and month <= 7:
                period = "Q2"
            elif month >= 8 and month <= 10:
                period = "Q3"
            elif month >= 11 and month <= 12 or month == 1:
                period = "Q4"
        return period

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerListDT(LoginRequiredMixin, IsManagerRolePermission, BaseDatatableView):

    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')

        sql = f"SELECT func_seller_list_json({year})"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerListDTNotTxt(LoginRequiredMixin, IsManagerRolePermission, BaseDatatableView):

    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')

        sql = f"SELECT func_seller_list_json_pending_invoices({year})"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)


class SellerListDataView(LoginRequiredMixin, IsManagerRolePermission, ListView):
    model = Seller
    template_name = "sellers/seller_list_data.html"

    def get_context_data(self, **kwargs):
        type_list = self.kwargs["type_list"]
        print(type_list)
        error_message = self.request.GET.get('error')
        context = super().get_context_data(**kwargs)
        if error_message:
            context['error'] = error_message
            print(error_message)
        seller_vats = SellerVat.objects.all().values('vat_country').distinct()
        # Obtener los nombres de los países relacionados
        context['vat_countries'] = Country.objects.filter(iso_code__in=seller_vats).values_list('name', flat=True)
        context['vat_vat_status'] = SellerVatStatus.objects.all().values_list('description', flat=True)
        context['vat_status_process'] = SellerVatStatusProcess.objects.all().values_list('description', flat=True)
        context['vat_status_activation'] = SellerVatEsStatusActivation.objects.all().values_list('description',
                                                                                                 flat=True)
        context['vat_status_iae'] = SellerVatEsStatusAltaIae.objects.all().values_list('description', flat=True)
        context['vat_status_cert'] = SellerVatEsStatusCDigital.objects.all().values_list('description', flat=True)
        context['vat_status_vies'] = SellerVatEsStatusVies.objects.all().values_list('description', flat=True)
        context['vat_status_eori'] = SellerVatEsStatusEori.objects.all().values_list('description', flat=True)
        context['vat_status_type'] = SellerVatType.objects.all().values_list('description', flat=True)
        context['type_list'] = type_list

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerListDataDT(LoginRequiredMixin, IsManagerRolePermission, BaseDatatableView):
    def get(self, request, *args, **kwargs):

        type_list = self.kwargs["type_list"]

        sql = f"SELECT func_sellervat_list_json('{type_list}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)



