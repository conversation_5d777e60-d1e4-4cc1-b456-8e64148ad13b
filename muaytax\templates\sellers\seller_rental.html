{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Alquileres de Vendedor{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
                <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                Alquileres: Alquileres del vendedor
            </h5>
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
           <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:rental_list' seller.shortname %}">Alquileres</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos Alquilers</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
<style>
    .select-status {
            padding-top: 9px; 
            padding-bottom: 9px; 
        }
</style>


<div class="card">
    <div class="card-body">
        <div class="row">
        <div class="d-flex align-items-center justify-content-between">
            <h3 id="title_process">Datos del Alquiler:</h3>
            
        </div>
        <hr>
        </div><br>
        <form class="form-horizontal" method="post" id="form" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="row">
            <div class="col mb-3">
                <label for="provider" class="form-label">
                    <b>Apellidos y Nombre o denominación del perceptor:*</b>
                </label>
                <select id="provider" name="provider" class="select-status select form-select " required >
                    <option value="">----------</option>
                    {% for prov in provider %}
                        {% if prov.pk == object.provider.pk %}
                            <option value="{{ prov.pk }}" selected>{{ prov.name }}</option>
                        {% else %}
                            {% if prov.country != None %}
                                <option value="{{ prov.pk }}">{{ prov.name }} ({{prov.country}})</option>
                            {% else %}
                                <option value="{{ prov.pk }}">{{ prov.name }}</option>
                            {% endif %}
                        {% endif %}   
                    {% endfor%}
                </select>            
            </div>
            <div class="col-2 mb-3">
                <label for="first_char_province" class="form-label">
                    <b>Provincia (Código):*</b>
                </label>
                <select id="first_char_province" name="first_char_province" class="select-status select form-select" required>
                    <option value="">----------</option>
                    {% for provin in province %}
                        {% if provin.code == object.first_char_province.code %}
                            <option value="{{ provin.code }}" selected>({{provin.code}}) {{provin.description}}</option>
                        {% else %}
                            <option value="{{ provin.code }}">({{provin.code}}) {{provin.description}}</option>
                        {% endif %}                  
                    {% endfor%}
                </select>
            </div>
            <div class="col mb-3">
                <label for="modality" class="form-label">
                    <b>Modalidad:*</b>
                </label>
                <select id="modality" name="modality" class="select-status select form-select" required>
                    <option value="">----------</option>
                    {% if object.modality == '1' %}
                        <option value="1" selected>Renta o rendimiento satisfecho es de tipo dinerario</option>
                        <option value="2">Renta o rendimiento satisfecho es en especie</option>
                    {% elif object.modality == '2' %}
                        <option value="1">Renta o rendimiento satisfecho es de tipo dinerario</option>
                        <option value="2" selected>Renta o rendimiento satisfecho es en especie</option>
                    {% else %}
                        <option value="1">Renta o rendimiento satisfecho es de tipo dinerario</option>
                        <option value="2">Renta o rendimiento satisfecho es en especie</option>
                    {% endif %}
                </select>            
            </div>
            
        </div>
        <div class="row mt-3">
            <div class="col mb-3">
                <label for="situation" class="form-label">
                    <b>Situación:*</b>
                </label>
                <select id="situation" name="situation" class="select-status select form-select" required>
                    <option value="">----------</option>
                    {% for sit in situation %}
                        {% if sit.code == object.situation.code %}
                            <option value="{{ sit.code }}" selected>{{sit.description}}</option>
                        {% else %}
                            <option value="{{ sit.code }}">{{sit.description}}</option>
                        {% endif %}                  
                    {% endfor%}
                </select>            
            </div>
            <div class="col-3 mb-3">
                <label for="catastral_reference" class="form-label">
                    <b>Referencia Catastral:*</b>
                </label>
                <input 
                    type="text" 
                    maxlength="100"
                    id="catastral_reference" 
                    name="catastral_reference" 
                    class="textinput textInput form-control" 
                    value="{{object.catastral_reference| default:''}}"
                    required
                />         
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-1 mb-3">
                <label for="type_road" class="form-label">
                    <b>Tipo de vía:*</b>
                </label>
                <select id="type_road" name="type_road" class="select-status select form-select" required>
                    <option value="">----------</option>
                    {% for road in type_road %}
                        {% if road.code == object.type_road.code %}
                            <option value="{{ road.code }}"selected>{{road.name}}</option>
                        {% else %}
                            <option value="{{ road.code }}">{{road.name}}</option>
                        {% endif %}  
                    {% endfor%}
                </select>            
            </div>
            <div class="col-4 mb-3">
                <label for="name_road" class="form-label" >
                    <b>Nombre de la vía pública:*</b>
                </label>
                <input 
                    type="text" 
                    maxlength="150"
                    id="name_road" 
                    name="name_road" 
                    class="textinput textInput form-control"
                    value="{{object.name_road| default:''}}"
                    required
                />         
            </div>
            <div class="col mb-3">
                <label for="type_number" class="form-label">
                    Tipo Num: 
                </label>
                <select id="type_number" name="type_number" class="select-status select form-select" >
                    <option value="">----------</option>
                    {% if object.type_number == "NUM" %}
                        <option value="NUM"selected>NÚM</option>
                        <option value="KM">KM</option>
                        <option value="S/N">S/N</option>
                    {% elif object.type_number == "KM" %}
                        <option value="KM">KM</option>
                        <option value="NUM">NÚM</option>
                        <option value="S/N">S/N</option>
                    {% elif object.type_number == "S/N" %}
                        <option value="S/N">S/N</option>
                        <option value="NUM">NÚM</option>
                        <option value="KM">KM</option>
                    {% else %}
                        <option value="NUM">NÚM</option>
                        <option value="KM">KM</option>
                        <option value="S/N">S/N</option>
                    {% endif %}
                </select>            
            </div>
            <div class="col mb-3">
                    <label for="num_house" class="form-label">
                        Núm.Casa:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="num_house" 
                        name="num_house" 
                        class="textinput textInput form-control"
                        value="{{object.num_house| default:''}}" 
                    />         
            </div>
            <div class="col mb-3">
                    <label for="calif_nu" class="form-label">
                        Calif.Nu:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="calif_nu" 
                        name="calif_nu" 
                        class="textinput textInput form-control"
                        value="{{object.calif_nu| default:''}}" 
                    />         
            </div>
            <div class="col mb-3">
                    <label for="bloq" class="form-label">
                        Bloque:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="bloq" 
                        name="bloq" 
                        class="textinput textInput form-control"
                        value="{{object.bloq| default:''}}"
                    />         
            </div>
            <div class="col mb-3">
                    <label for="portal" class="form-label">
                        Núm.Portal:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="portal" 
                        name="portal" 
                        class="textinput textInput form-control"
                        value="{{object.portal| default:''}}"
                    />         
            </div>
            <div class="col mb-3">
                    <label for="stair" class="form-label">
                        Escalera:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="stair" 
                        name="stair" 
                        class="textinput textInput form-control"
                        value="{{object.stair| default:''}}" 
                    />         
            </div>
            <div class="col mb-3">
                    <label for="floor" class="form-label">
                        Planta:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="floor" 
                        name="floor" 
                        class="textinput textInput form-control"
                        value="{{object.floor| default:''}}" 
                    />         
            </div>
            <div class="col mb-3">
                    <label for="door" class="form-label">
                        Puerta:
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="door" 
                        name="door" 
                        class="textinput textInput form-control"
                        value="{{object.door| default:''}}"
                    />         
            </div>
        </div>
        <div class="row mt-3">
            <div class="col mb-3">
                    <label for="domiciliary_complement" class="form-label">
                        Complemento domicilio (ej: Urbanización.., Polígono Industrial.., C. Comercial..,):
                    </label>
                    <input 
                        type="text" 
                        maxlength="150"
                        id="domiciliary_complement" 
                        name="domiciliary_complement" 
                        class="textinput textInput form-control"
                        value="{{object.domiciliary_complement| default:''}}"
                    />         
            </div>
            <div class="col mb-3">
                    <label for="location" class="form-label">
                        Localidad / Población (si es distinta de Municipio):
                    </label>
                    <input 
                        type="text" 
                        maxlength="150"
                        id="location" 
                        name="location" 
                        class="textinput textInput form-control"
                        value="{{object.location| default:''}}" 
                    />         
            </div>
        </div>
        <div class="row mt-3">
        <div class="col-3 mb-3">
                    <label for="province" class="form-label">
                        <b>Provincia:*</b>
                    </label>
                    <select id="province" name="province" class="select-status select form-select" onchange="selectProvince()" required>
                        <option value="">----------</option>
                        {% for provin in province %}
                            {% if provin.code == object.province.code %}
                                <option value="{{ provin.code }}" selected>{{provin.description}}</option>
                            {% else %}
                                <option value="{{ provin.code }}">{{provin.description}}</option>
                            {% endif %}                  
                        {% endfor%}
                    </select>
                    {% comment %} <input 
                        type="text" 
                        maxlength="50"
                        id="province" 
                        name="province" 
                        class="textinput textInput form-control" 
                        value="{{object.province| default:''}}"
                        required
                    />          {% endcomment %}

            </div>
            <div class="col-2 mb-3">
                    <label for="province_code" class="form-label">
                        <b>Cód.Provincia:*</b>
                    </label>
                    <input 
                        type="text" 
                        maxlength="50"
                        id="province_code" 
                        name="province_code" 
                        class="textinput textInput form-control" 
                        value="{{object.province.code| default:''}}"
                        required readonly
                    />
                    {% comment %} <select id="province_code" name="province_code" class="select-status select form-select" required>
                        <option value="">----------</option>
                        {% for provin in province %}
                            {% if provin.code == object.province_code.code %}
                                <option value="{{ provin.code }}" selected>({{provin.code}}) {{provin.description}}</option>
                            {% else %}
                                <option value="{{ provin.code }}">({{provin.code}}) {{provin.description}}</option>
                            {% endif %}                  
                        {% endfor%}
                    </select> {% endcomment %}
                    
            </div>
            <div class="col-3 mb-3">
                    <label for="name_municipality" class="form-label">
                        <b>Nombre del municipio:*</b>
                    </label>
                    <select id="name_municipality" name="name_municipality" class="select-status select form-select" onchange= "selectMunicipie()" required>
                    <option value="">----------</option>
                        {% for mun in municipe %}
                            {% if mun.code == object.name_municipality.code %}
                                <option value="{{ mun.code }}"selected>{{mun.description}}</option>
                            {% else %}
                                <option value="{{ mun.code }}">{{mun.description}}</option>
                            {% endif %}  
                        {% endfor%}
                    </select>   
            </div>
            <div class="col-2 mb-3">
                    <label for="municipality_code" class="form-label">
                        <b>Código del municipio:*</b>
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="municipality_code_show" 
                        name="municipality_code_show" 
                        class="textinput textInput form-control"
                        value="({{object.name_municipality.cod_mun | default:''}}) {{object.name_municipality | default:''}}"
                        readonly
                        required 
                    />
                    
            </div>
            <div class="col-2 mb-3">
                    <label for="postal_code" class="form-label">
                        <b>Código Postal:*</b>
                    </label>
                    <input 
                        type="text" 
                        maxlength="10"
                        id="postal_code" 
                        name="postal_code" 
                        class="textinput textInput form-control" 
                        value="{{object.postal_code| default:''}}"
                        required
                    />         
            </div>
            <div class="col-12 mb-3 mt-3"> </div>
            <div class="col-6 mb-3">
                <label for="postal_code" class="form-label">
                    <b>Fecha de Inicio del Alquiler:*</b>
                </label>
                <input 
                    type="date" 
                    maxlength="10"
                    id="start_date" 
                    name="start_date" 
                    class="dateinput form-control" 
                    value="{{object.start_date | date:'Y-m-d' | default:''}}"
                    required
                />         
            </div>
            <div class="col-6 mb-3">
                <label for="postal_code" class="form-label">
                    Fecha de Fin del Alquiler:
                </label>
                <input 
                    type="date"
                    maxlength="10"
                    id="end_date" 
                    name="end_date" 
                    class="dateinput form-control" 
                    value="{{object.end_date | date:'Y-m-d' | default:''}}"
                />        
            </div>
        </div>
        <div class="mt-3 d-flex justify-content-center align-items-center">
            <button type="submit" class="btn btn-primary">Guardar</button>
        </div>
        </form>
        
        
    </div>
</div>

{% endblock content %}

{% block javascripts %}

<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
<script>

    const selectProvince = () =>{
        let municipies = '{{municipies_json|safe}}';
        let province = document.getElementById("province").value;

        //Establecer el codigo de provincia en el input de solo lectura
        document.getElementById("province_code").value = province;

        //Filtrar los municipios por provincia
        let municipies_prov = JSON.parse(municipies).filter(mun => mun.cod_prov == province);

        let selectElement = document.getElementById("name_municipality");
        selectElement.innerHTML = "";

        let defaultOption = document.createElement("option");
        defaultOption.value = "";
        defaultOption.textContent = "----------";
        selectElement.appendChild(defaultOption);

        municipies_prov.forEach(mun => {
            let option = document.createElement("option");
            option.value = mun.code;
            option.textContent = mun.description;
            selectElement.appendChild(option);
        });
    }

    const selectMunicipie = () =>{
        let municipies = '{{municipies_json|safe}}';
        let municipality = document.getElementById("name_municipality").value;
        let province = document.getElementById("province").value;

        //Filtrar los municipios por provincia
        let municipies_prov = JSON.parse(municipies).filter(mun => mun.cod_prov == province);

        //Mostrar el codigo de municipio en el input de solo lectura
        let selected = JSON.parse(municipies).filter(mun => mun.code == municipality);
        document.getElementById("municipality_code_show").value = "("+selected[0].cod_mun+") "+selected[0].description;
        
    }

    window.onload = function() {
        let municipies = '{{municipies_json|safe}}';
        let province = document.getElementById("province").value;
        if (province != ''){
            //Filtrar los municipios por provincia
            let municipies_prov = JSON.parse(municipies).filter(mun => mun.cod_prov == province);

            let selectElement = document.getElementById("name_municipality");
            selectElement.innerHTML = "";

            municipies_prov.forEach(mun => {
                let option = document.createElement("option");
                option.value = mun.code;
                option.textContent = mun.description;
                selectElement.appendChild(option);
                if (mun.code == '{{object.name_municipality.code}}'){
                    selectElement.value = mun.code;
                }
            });
        }
        };

</script>
{% endblock javascripts %}