from django.db import models

class DocumentType(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=150,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Tipo de Documento"
        verbose_name_plural = "Tipos de Documentos"
    
    def __str__(self):
        return self.description
    
# @admin.register(DocumentType)
# class DocumentTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
