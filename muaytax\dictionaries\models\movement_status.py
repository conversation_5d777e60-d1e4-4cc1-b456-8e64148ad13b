from django.db import models

class MovementStatus(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=100,
        verbose_name="<PERSON>ó<PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Estado del Moviemiento Bancario"
        verbose_name_plural = "Estados de los Moviemientos Bancarios"
    
    def __str__(self):
        return self.description
    
# @admin.register(MovementStatus)
# class MovementStatusAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]