# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-22 11:39+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: muaytax/app_bookings/models/absence.py:62
msgid "Si la ausencia no es todo el día, debe indicar hora de inicio y fin."
msgstr ""

#: muaytax/app_bookings/models/absence.py:64
msgid "La hora final debe ser mayor que la hora inicial."
msgstr ""

#: muaytax/app_bookings/models/absence.py:70
#: muaytax/app_bookings/models/absence.py:91
msgid "Ya existe una ausencia para este gestor en esta fecha."
msgstr ""

#: muaytax/app_bookings/models/absence.py:75
msgid "La hora inicial de la nueva ausencia se solapa con otra ausencia"
msgstr ""

#: muaytax/app_bookings/models/absence.py:83
msgid ""
"La hora final de la nueva ausencia se solapa con el inicio de otra ausencia"
msgstr ""

#: muaytax/app_bookings/models/manage_model.py:21
#: muaytax/app_sellers/models/seller.py:168
msgid "Autónomo"
msgstr ""

#: muaytax/app_bookings/models/manage_model.py:22
#: muaytax/app_sellers/models/seller.py:169
msgid "SL"
msgstr ""

#: muaytax/app_bookings/models/manage_model.py:23
#: muaytax/app_sellers/models/seller.py:170
msgid "LLC"
msgstr ""

#: muaytax/app_bookings/models/manage_model.py:24
#: muaytax/app_sellers/models/seller.py:171
msgid "Otro"
msgstr ""

#: muaytax/app_customers/views.py:211
#: muaytax/app_documents/views/document.py:124
#: muaytax/app_documents/views/presented_model.py:268
#: muaytax/app_partners/views.py:113
#: muaytax/app_products/views/product_amz.py:137
#: muaytax/app_providers/views.py:216 muaytax/app_sellers/views/partner.py:36
#: muaytax/app_sellers/views/seller.py:7740
#: muaytax/app_sellers/views/seller_admin.py:14
#: muaytax/app_sellers/views/seller_vat.py:838 muaytax/users/views.py:1129
msgid "Information successfully updated"
msgstr ""

#: muaytax/app_documents/constants.py:4
msgid "Solicitar devolución"
msgstr ""

#: muaytax/app_documents/constants.py:5
msgid "Compensar"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "Alabama"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "Alaska"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "Arizona"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "Arkansas"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "California"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "Colorado"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:18
msgid "Connecticut"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "Delaware"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "District of Columbia"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "Florida"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "Georgia"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "Hawaii"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "Idaho"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:19
msgid "Illinois"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Indiana"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Iowa"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Kansas"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Kentucky"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Louisiana"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Maine"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Maryland"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:20
msgid "Massachusetts"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Michigan"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Minnesota"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Mississippi"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Missouri"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Montana"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Nebraska"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:21
msgid "Nevada"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:22
msgid "New Hampshire"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:22
msgid "New Jersey"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:22
msgid "New Mexico"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:22
msgid "New York"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:22
msgid "North Carolina"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:22
msgid "North Dakota"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "Ohio"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "Oklahoma"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "Oregon"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "Pennsylvania"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "Rhode Island"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "South Carolina"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:23
msgid "South Dakota"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "Tennessee"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "Texas"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "Utah"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "Vermont"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "Virginia"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "Washington"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:24
msgid "West Virginia"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:25
msgid "Wisconsin"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:25
msgid "Wyoming"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:33
msgid "Dirección de la EMPRESA"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:37
msgid "Ciudad"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:42
#: muaytax/app_documents/forms/M54721120.py:169
msgid "Estado"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:47
#: muaytax/app_documents/forms/M54721120.py:170
msgid "Código postal"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:64
msgid "Nombre de la EMPRESA"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:65
msgid "Teléfono de contacto"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:66
msgid "Número EIN de la EMPRESA"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:67
msgid "País de incorporación de la empresa"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:70
msgid "Indicar tal y como aparece en los Articles of Organization."
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:71
msgid "Número de teléfono de contacto del administrador de la empresa"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:72
msgid ""
"Se compone de 9 números con el formato: 00-0000000. (Escribir sin el guión)"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:120
msgid "El número EIN debe tener 9 dígitos (sin guiones)"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:144
msgid ""
"El correo electrónico no puede ser modificado. Si deseas cambiarlo, "
"dirígitete a la sección de configuración de tu perfil"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:157
msgid "Correo electrónico"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:158
msgid "Nombre del MIEMBRO"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:159
msgid "Apellido del MIEMBRO"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:162
msgid ""
"Dirección de correo electrónico que se utiliza para iniciar sesion en el "
"sistema"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:167
msgid "Dirección de residencia del miembro"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:168
msgid "Ciudad de residencia del miembro"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:171
msgid "País"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:183
#: muaytax/app_documents/forms/M54721120.py:184
#: muaytax/app_documents/forms/M54721120.py:185
#: muaytax/app_documents/forms/M54721120.py:186
msgid "Yes"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:183
#: muaytax/app_documents/forms/M54721120.py:184
#: muaytax/app_documents/forms/M54721120.py:185
#: muaytax/app_documents/forms/M54721120.py:186
#: muaytax/app_workers/models/worker.py:101
msgid "No"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:190
msgid ""
"Si la respuesta es \"Sí\", debes agregar registros contables en la pestaña "
"de \"Transacciones reportables\""
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:191
msgid ""
"Activos totales de la empresa a 31 de diciembre de 2023. La cantidad debe "
"ser expresada en dólares americanos. Si tienes activos en otra moneda, usa "
"la tasa de cambio del 31 de diciembre de 2023"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:192
msgid ""
"Esta información la puedes encontrar en la segunda página de los Articles of "
"Organization, donde indica \"Purpose: -\""
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:193
msgid "El formato de la fecha es: día, mes, año"
msgstr ""

#: muaytax/app_documents/forms/M54721120.py:196
msgid "País de actividad del MIEMBRO"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:14
msgid "Venta de productos físicos o digitales"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:15
msgid "Venta de servicios digitales."
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:16
msgid "Programación y servicios informáticos."
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:19
msgid "Venta de productos"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:20
msgid "Compra de productos"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:21
msgid "Venta de servicios"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:22
msgid "Compra de servicios"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:23
msgid "Contribución"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:24
msgid "Constitución"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:25
msgid "Disolución"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:26
msgid "Adquisición"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:27
msgid "Distribución"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:34
msgid "Empresa"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:37
msgid "El modelo está asociado a la empresa que representa el administrador"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:41
msgid "Address of the member"
msgstr "Dirección del miembro"

#: muaytax/app_documents/models/model_5472_1120.py:49
msgid "País de nacionalidad del miembro"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:58
msgid "País de residencia fiscal del miembro"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:67
msgid "País de actividad del miembro"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:75
msgid "Número de pasaporte"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:81
msgid "Número de identificación de USA"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:87
msgid "Casilla 4b(3)"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:90
msgid ""
"Número de la casilla 4b(3) del formulario 5472 presentado el año anterior"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:96
msgid "Casilla 1F (todos los años anteriores)"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:100
msgid ""
"Suma de las casillas 1F del formulario 5472 presentado en años anteriores"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:106
msgid "Descripción de la actividad principal"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:109
msgid "Escribe máximo 50 caracteres"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:114
msgid "Tipo de Actividad"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:121
msgid "¿Tiene la empresa actividad física en EEUU?"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:125
#, fuzzy
#| msgid ""
#| "A LLC is classified as a foreign owned disregarded entity when it is a "
#| "single member LLC and this member is a non-resident foreigner in the USA "
#| "and also the LLC has no presence in the USA."
msgid ""
"A LLC is classified as a foreign-owned disregarded entity when it is a "
"single member LLC and this member is a non-resident foreigner in the USA and "
"also the LLC has no presence in the USA."
msgstr ""
"Una LLC se clasifica como foreign owned disregarded entity cuando es una LLC "
"de un solo miembro y este miembro es un extranjero no-residente en los USA y "
"además la LLC no tiene presencia en USA."

#: muaytax/app_documents/models/model_5472_1120.py:133
msgid "¿Es el primer año que la empresa presenta un formulario 5472-1120?"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:139
msgid "¿Es el último año que la empresa presenta este formulario?"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:143
msgid ""
"Indique si es el último año que la empresa presenta el formulario 5472-1120 "
"y se va a disolver."
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:149
msgid "Principales países donde la empresa desarrolla sus actividades"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:157
msgid "Activos totales de la empresa"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:164
msgid "¿Eres freelance o miembro de otra empresa?"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:172
msgid "Año de presentación del modelo 5472-1120"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:176
msgid "EL modelo 5472-1120 ha sido procesado?"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:187
msgid "Traducciones"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:193
#: muaytax/app_documents/models/model_5472_1120.py:203
#, python-brace-format
msgid "Contracted model {model}"
msgstr "Modelo {model} contratado"

#: muaytax/app_documents/models/model_5472_1120.py:194
#, python-brace-format
msgid "Contracted models {model}"
msgstr "Modelos {model} contratados"

#: muaytax/app_documents/models/model_5472_1120.py:197
#, python-brace-format
msgid "Model {model} from {seller} ({year})"
msgstr "Modelo {model} de {seller} ({year})"

#: muaytax/app_documents/models/model_5472_1120.py:219
msgid "Date"
msgstr "Fecha"

#: muaytax/app_documents/models/model_5472_1120.py:225
msgid "Tipo de transacción"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:231
msgid "Descripción"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:238
msgid "Monto"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:245
msgid "Moneda"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:254
msgid "Total en dólares"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:264
#, python-brace-format
msgid "ID: {id} SELLER: {seller} YEAR: {year} DESC:{desc}"
msgstr "ID: {id} SELLER: {seller} AÑO: {year} DESC:{desc}"

#: muaytax/app_documents/models/model_5472_1120.py:270
#, python-brace-format
msgid "ID: {id} DESC: {desc}"
msgstr "ID: {id} DESC: {desc}"

#: muaytax/app_documents/models/model_5472_1120.py:273
msgid "Dato contable"
msgstr ""

#: muaytax/app_documents/models/model_5472_1120.py:274
#: muaytax/templates/sellers/seller_request5472_1120.html:256
msgid "Datos contables"
msgstr ""

#: muaytax/app_documents/models/presented_model.py:179
msgid "Modelo 5472-1120 firmado"
msgstr ""

#: muaytax/app_documents/models/presented_model.py:190
msgid "ID de destino de fax"
msgstr ""

#: muaytax/app_documents/models/presented_model.py:193
msgid "Informe de envío de fax"
msgstr ""

#: muaytax/app_fax/views.py:64
msgid ""
"El fax se ha enviado correctamente. Espere la respuesta en su correo "
"electrónico."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:915
msgid "El archivo excel debe tener exactamente 2 hojas"
msgstr ""

#: muaytax/app_sellers/forms/seller.py:919
msgid ""
"El archivo excel debe tener dos hojas llamadas \"Reportables 1\" y "
"\"Reportables 2\"."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:933
#, python-brace-format
msgid "La hoja {sheet} no tiene los encabezados correctos."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:946
#, python-brace-format
msgid "La fecha en la fila {exc_index} de la hoja {sheet} está vacía."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:948
#, python-brace-format
msgid "La fecha en la fila {exc_index} de la hoja {sheet} no es válida."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:951
#, python-brace-format
msgid ""
"La fecha en la fila {exc_index} de la hoja {sheet} no corresponde al año de "
"la presentación de este formulario: {current_presentation_year}"
msgstr ""

#: muaytax/app_sellers/forms/seller.py:959
#, python-brace-format
msgid ""
"El tipo de transacción en la fila {exc_index} de la hoja {sheet} está vacío."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:962
#, python-brace-format
msgid ""
"El tipo de transacción en la fila {exc_index} de la hoja {sheet} no es "
"válido."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:972
#, python-brace-format
msgid "La descripción en la fila {exc_index} de la hoja {sheet} está vacía."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:978
#, python-brace-format
msgid "El total en la fila {exc_index} de la hoja {sheet} está vacío."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:980
#, python-brace-format
msgid "El total en la fila {exc_index} de la hoja {sheet} no es válido."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:986
#, python-brace-format
msgid "La moneda en la fila {exc_index} de la hoja {sheet} está vacía."
msgstr ""

#: muaytax/app_sellers/forms/seller.py:989
#, python-brace-format
msgid "La moneda en la fila {exc_index} de la hoja {sheet} no es válida."
msgstr ""

#: muaytax/app_sellers/models/seller.py:552
#: muaytax/app_sellers/models/seller.py:558
#, python-format
msgid "Model %(model)s contracted?"
msgstr "¿Modelo %(model)s contratado?"

#: muaytax/app_sellers/models/seller.py:563
msgid "Model 5472-1120 WITHOUT ACTIVITY contracted?"
msgstr "¿Modelo 5472-1120 SIN ACTIVIDAD contratado?"

#: muaytax/app_workers/constants.py:4
msgid "Empleado"
msgstr ""

#: muaytax/app_workers/constants.py:5
msgid "Administrador con situación laboral"
msgstr ""

#: muaytax/app_workers/constants.py:6
msgid "Administrador sin situación laboral"
msgstr ""

#: muaytax/app_workers/constants.py:18
msgid ""
"Si el perceptor es soltero, viudo, divorciado o separado legalmente con "
"hijos menores de 18 años o mayores incapacitados sujetos a patria potestad "
"prorrogada o rehabilitada, que conviven exclusivamente con él, siempre que "
"tenga, al menos, un hijo o descendiente con derecho a la aplicación del "
"mínimo por descendientes a que se refiere el artículo 58 de la Ley del "
"Impuesto."
msgstr ""

#: muaytax/app_workers/constants.py:19
msgid ""
"Si el perceptor está casado y no separado legalmente y su cónyuge no tiene "
"rentas anuales superiores a la cuantía a la que se refiere la situación 2ª "
"de las contempladas en el artículo 81.1 del Reglamento del Impuesto."
msgstr ""

#: muaytax/app_workers/constants.py:20
msgid ""
"Si la situación familiar del perceptor es distinta de las anteriores o no "
"deseó manifestarla ante la persona o entidad retenedora."
msgstr ""

#: muaytax/app_workers/constants.py:24
msgid ""
"Si el perceptor no padece ninguna discapacidad o si, padeciéndola, el grado "
"de minusvalía es inferior al 33 por 100"
msgstr ""

#: muaytax/app_workers/constants.py:25
msgid ""
"Si el grado de minusvalía del perceptor es igual o superior al 33 por 100 e "
"inferior al 65 por 100."
msgstr ""

#: muaytax/app_workers/constants.py:26
msgid ""
"Si el grado de minusvalía del perceptor es igual o superior al 33 por 100 e "
"inferior al 65 por 100, siempre que, además, acredite necesitar ayuda de "
"terceras personas o movilidad reducida."
msgstr ""

#: muaytax/app_workers/constants.py:27
msgid ""
"Si el grado de minusvalía del perceptor es igual o superior al 65 por 100"
msgstr ""

#: muaytax/app_workers/constants.py:31
msgid "Si el perceptor es el titular de la unidad de convivencia."
msgstr ""

#: muaytax/app_workers/constants.py:32
msgid ""
"Si el perceptor no es el titular de la unidad de convivencia. En este caso, "
"deberá cumplimentarse obligatoriamente el campo “NIF del titular de la "
"unidad de convivencia"
msgstr ""

#: muaytax/app_workers/models/worker.py:100
msgid "Sí"
msgstr ""

#: muaytax/app_workers/models/worker.py:164
msgid "Edad"
msgstr ""

#: muaytax/app_workers/models/worker.py:182
msgid ""
"Si el trabajador no es titular de la unidad de convivencia, debe indicar el "
"NIF/NIE del titular"
msgstr ""

#: muaytax/app_workers/models/worker.py:186
msgid "Si el trabajador está casado, debe indicar el NIF/NIE del cónyuge"
msgstr ""

#: muaytax/app_workers/models/worker.py:189
msgid "La edad mínima debe ser de 18 años"
msgstr ""

#: muaytax/templates/account/account_inactive.html:5
#: muaytax/templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr ""

#: muaytax/templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr ""

#: muaytax/templates/account/email.html:7
msgid "Account"
msgstr ""

#: muaytax/templates/account/email.html:10
msgid "E-mail Addresses"
msgstr ""

#: muaytax/templates/account/email.html:13
msgid "The following e-mail addresses are associated with your account:"
msgstr ""

#: muaytax/templates/account/email.html:27
msgid "Verified"
msgstr ""

#: muaytax/templates/account/email.html:29
msgid "Unverified"
msgstr ""

#: muaytax/templates/account/email.html:31
msgid "Primary"
msgstr ""

#: muaytax/templates/account/email.html:37
msgid "Make Primary"
msgstr ""

#: muaytax/templates/account/email.html:38
msgid "Re-send Verification"
msgstr ""

#: muaytax/templates/account/email.html:39
msgid "Remove"
msgstr ""

#: muaytax/templates/account/email.html:46
msgid "Warning:"
msgstr ""

#: muaytax/templates/account/email.html:46
msgid ""
"You currently do not have any e-mail address set up. You should really add "
"an e-mail address so you can receive notifications, reset your password, etc."
msgstr ""

#: muaytax/templates/account/email.html:50
msgid "Add E-mail Address"
msgstr ""

#: muaytax/templates/account/email.html:55
msgid "Add E-mail"
msgstr ""

#: muaytax/templates/account/email.html:63
msgid "Do you really want to remove the selected e-mail address?"
msgstr ""

#: muaytax/templates/account/email_confirm.html:6
#: muaytax/templates/account/email_confirm.html:9
msgid "Confirm E-mail Address"
msgstr ""

#: muaytax/templates/account/email_confirm.html:15
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-mail "
"address for user %(user_display)s."
msgstr ""

#: muaytax/templates/account/email_confirm.html:19
msgid "Confirm"
msgstr ""

#: muaytax/templates/account/email_confirm.html:26
#, python-format
msgid ""
"This e-mail confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgstr ""

#: muaytax/templates/account/login.html:106
#: muaytax/templates/account/loginOLD.html:103
msgid "Please correct the error below."
msgstr ""

#: muaytax/templates/account/login.html:108
#: muaytax/templates/account/loginOLD.html:105
msgid "Please correct the errors below."
msgstr ""

#: muaytax/templates/account/loginOLD.html:71
msgid "Forgot Password?"
msgstr ""

#: muaytax/templates/account/logout.html:5
#: muaytax/templates/account/logout.html:19
#: muaytax/templates/account/logout.html:28
msgid "Sign Out"
msgstr ""

#: muaytax/templates/account/logout.html:21
msgid "Are you sure you want to sign out?"
msgstr ""

#: muaytax/templates/account/password_change.html:6
#: muaytax/templates/account/password_change.html:19
#: muaytax/templates/account/password_change.html:24
#: muaytax/templates/account/password_reset_from_key.html:5
#: muaytax/templates/account/password_reset_from_key.html:29
#: muaytax/templates/account/password_reset_from_key_done.html:4
#: muaytax/templates/account/password_reset_from_key_done.html:17
msgid "Change Password"
msgstr ""

#: muaytax/templates/account/password_reset.html:7
#: muaytax/templates/account/password_reset.html:20
#: muaytax/templates/account/password_reset_done.html:6
#: muaytax/templates/account/password_reset_done.html:19
msgid "Password Reset"
msgstr ""

#: muaytax/templates/account/password_reset.html:25
msgid ""
"Ingrese su dirección de correo electrónico a continuación y le enviaremos un "
"correo electrónico que le permitirá restablecerla."
msgstr ""

#: muaytax/templates/account/password_reset.html:30
msgid "SOLICITAR"
msgstr ""

#: muaytax/templates/account/password_reset.html:33
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""

#: muaytax/templates/account/password_reset_done.html:25
msgid ""
"Le hemos enviado un correo electrónico. Póngase en contacto con nosotros si "
"no lo recibe en unos minutos."
msgstr ""

#: muaytax/templates/account/password_reset_from_key.html:29
msgid "Bad Token"
msgstr ""

#: muaytax/templates/account/password_reset_from_key.html:32
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""

#: muaytax/templates/account/password_reset_from_key.html:38
#: muaytax/templates/admin/base.html:55
msgid "Change password"
msgstr ""

#: muaytax/templates/account/password_reset_from_key.html:41
#: muaytax/templates/account/password_reset_from_key_done.html:18
msgid "Your password is now changed."
msgstr ""

#: muaytax/templates/account/password_set.html:6
#: muaytax/templates/account/password_set.html:9
#: muaytax/templates/account/password_set.html:14
msgid "Set Password"
msgstr ""

#: muaytax/templates/account/signup.html:6
msgid "Signup"
msgstr ""

#: muaytax/templates/account/signup.html:9
#: muaytax/templates/account/signup.html:19
msgid "Sign Up"
msgstr ""

#: muaytax/templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""

#: muaytax/templates/account/signup_closed.html:5
#: muaytax/templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr ""

#: muaytax/templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr ""

#: muaytax/templates/account/verification_sent.html:5
#: muaytax/templates/account/verification_sent.html:8
#: muaytax/templates/account/verified_email_required.html:5
#: muaytax/templates/account/verified_email_required.html:8
msgid "Verify Your E-mail Address"
msgstr ""

#: muaytax/templates/account/verification_sent.html:10
msgid ""
"We have sent an e-mail to you for verification. Follow the link provided to "
"finalize the signup process. Please contact us if you do not receive it "
"within a few minutes."
msgstr ""

#: muaytax/templates/account/verified_email_required.html:12
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your e-mail address. "
msgstr ""

#: muaytax/templates/account/verified_email_required.html:16
msgid ""
"We have sent an e-mail to you for\n"
"verification. Please click on the link inside this e-mail. Please\n"
"contact us if you do not receive it within a few minutes."
msgstr ""

#: muaytax/templates/account/verified_email_required.html:20
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your e-"
"mail address</a>."
msgstr ""

#: muaytax/templates/admin/base.html:41
msgid "Welcome,"
msgstr ""

#: muaytax/templates/admin/base.html:46
msgid "View site"
msgstr ""

#: muaytax/templates/admin/base.html:51
msgid "Documentation"
msgstr ""

#: muaytax/templates/admin/base.html:62
msgid "Log out"
msgstr ""

#: muaytax/templates/admin/base.html:73
msgid "Breadcrumbs"
msgstr ""

#: muaytax/templates/admin/base.html:76
msgid "Home"
msgstr ""

#: muaytax/templates/hijack/notification.html:8
#, python-format
msgid "Actualmente estás trabajando a nombre de <em>%(user)s</em>."
msgstr ""

#: muaytax/templates/hijack/notification.html:16
msgid "hide"
msgstr ""

#: muaytax/templates/hijack/notification.html:19
msgid "release"
msgstr ""

#: muaytax/templates/includes/sidebar-customer.html:207
#: muaytax/templates/sellers/seller_request5472_1120.html:4
#: muaytax/templates/sellers/seller_request5472_1120.html:56
#: muaytax/templates/sellers/seller_request5472_1120.html:67
#, python-format
msgid "Model %(model_n)s"
msgstr "Modelo %(model_n)s"

#: muaytax/templates/includes/sidebar-customer.html:210
#, fuzzy, python-format
#| msgid "Model %(model_n)s"
msgid "Form %(model_n)s"
msgstr "Modelo %(model_n)s"

#: muaytax/templates/sellers/include/m54721120/seller_m54721120_account_record_section.html:6
#, python-format
msgid "Accounting data information %(value)s"
msgstr ""

#: muaytax/templates/sellers/include/m54721120/seller_m54721120_finish.html:4
msgid "Resumen general"
msgstr ""

#: muaytax/templates/sellers/include/m54721120/seller_m54721120_finish.html:13
#: muaytax/templates/sellers/seller_request5472_1120.html:99
msgid "Información personal"
msgstr ""

#: muaytax/templates/sellers/include/m54721120/seller_m54721120_finish.html:105
msgid "Información de la empresa"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:64
msgid "Administración"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:108
msgid "Información de la Empresa"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:117
msgid "Información contable"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:127
#: muaytax/templates/sellers/seller_request5472_1120.html:356
msgid "Finish"
msgstr "Finalizar"

#: muaytax/templates/sellers/seller_request5472_1120.html:145
msgid "Datos generales del miembro"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:157
msgid "Datos de dirección del miembro"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:170
msgid "Datos de país referente al miembro"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:181
#: muaytax/templates/sellers/seller_request5472_1120.html:237
#: muaytax/templates/sellers/seller_request5472_1120.html:326
#: muaytax/templates/sellers/seller_request5472_1120.html:353
msgid "Guardar y seguir editando"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:184
#: muaytax/templates/sellers/seller_request5472_1120.html:240
#: muaytax/templates/sellers/seller_request5472_1120.html:328
msgid "Siguiente"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:199
msgid "Datos de la Empresa"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:211
msgid "Dirección de la Empresa"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:223
msgid "Datos de país referente a la Empresa"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:232
#: muaytax/templates/sellers/seller_request5472_1120.html:321
#: muaytax/templates/sellers/seller_request5472_1120.html:349
msgid "Anterior"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:280
msgid "Ventas / Compras al miembro"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:287
msgid "Transacciones reportables"
msgstr ""

#: muaytax/templates/sellers/seller_request5472_1120.html:377
#, python-format
msgid ""
"\n"
"                    Do you want to submit the information for the model "
"%(model_n)s?\n"
"                  "
msgstr ""
"\n"
"                    ¿Deseas enviar la información del modelo %(model_n)s?\n"
"                  "

#: muaytax/templates/sellers/seller_request5472_1120.html:381
msgid "Before proceeding, make sure all the data is correct."
msgstr ""
"Antes de proceder, asegúrate de revisar que todos los datos estén correctos."

#: muaytax/templates/sellers/seller_request5472_1120.html:392
msgid "No, back"
msgstr "No, atrás"

#: muaytax/templates/sellers/seller_request5472_1120.html:394
msgid "Yes, submit"
msgstr "Sí, enviar"

#: muaytax/templates/sellers/seller_request5472_1120.html:437
msgid "The information is sending. Do not close or refresh the page"
msgstr "La información se está enviando. No cierres ni actualices la página"

#: muaytax/templates/sellers/seller_request5472_1120.html:439
msgid "Please wait..."
msgstr "Por favor espera..."

#: muaytax/users/admin.py:19
msgid "Personal info"
msgstr ""

#: muaytax/users/admin.py:21
msgid "Permissions"
msgstr ""

#: muaytax/users/admin.py:32
msgid "Important dates"
msgstr ""

#: muaytax/users/apps.py:7
msgid "Users"
msgstr ""

#: muaytax/users/forms.py:34 muaytax/users/tests/test_forms.py:36
msgid "This username has already been taken."
msgstr ""

#: muaytax/users/models.py:39
msgid "role"
msgstr ""

#: muaytax/users/models.py:42
msgid "seller"
msgstr ""

#: muaytax/users/models.py:43
msgid "Manager"
msgstr ""

#~ msgid "Country of activity of the member"
#~ msgstr "País de actividad del miembro"

#~ msgid "Country of tax residence of the company member"
#~ msgstr "País de residencia fiscal del miembro"

#~ msgid "Seller"
#~ msgstr "Vendedor"

#~ msgid "Passport number"
#~ msgstr "Número de pasaporte"

#~ msgid "Description of main activity"
#~ msgstr "Descripción de la actividad principal"

#~ msgid "NAICS Activity Code"
#~ msgstr "Código de actividad NAICS"

#~ msgid "Country of incorporation of the company"
#~ msgstr "País de incorporación de la empresa"

#~ msgid "Is the LLC a single member?"
#~ msgstr "¿La Empresa es de un único miembro?"

#~ msgid "Is the LLC a foreign owned disregarded entity?"
#~ msgstr "¿La Empresa es una foreign owned disregarded entity?"

#~ msgid "Is it the first year the LLC presents a Form 5472-1120?"
#~ msgstr "¿Es el primer año que la LLC presenta un formulario 5472-1120?"

#~ msgid "Main countries where the LLC develops its activities"
#~ msgstr "Principales países donde la LLC desarrolla sus actividades"

#~ msgid "Total assets of the company"
#~ msgstr "Activos totales de la empresa"

#~ msgid "Does the LLC import goods that are owned by the member?"
#~ msgstr "¿La LLC importa bienes que son propiedad del miembro?"

#~ msgid "Year of the Declaration"
#~ msgstr "Año de la declaración"

#~ msgid "Has the model been processed?"
#~ msgstr "¿El modelo ha sido procesado?"

#~ msgid "Buy"
#~ msgstr "Compra"

#~ msgid "Sell"
#~ msgstr "Venta"

#~ msgid "Amount"
#~ msgstr "Monto"

#~ msgid "Currency"
#~ msgstr "Moneda"

#~ msgid "Accounting record"
#~ msgstr "Dato contable"

#~ msgid "Accounting records"
#~ msgstr "Datos contables"

#~ msgid "Personal information"
#~ msgstr "Información personal"

#~ msgid "Company information"
#~ msgstr "Información de la empresa"

#~ msgid "General data"
#~ msgstr "Datos generales"

#~ msgid "Next"
#~ msgstr "Siguiente"

#~ msgid "Company data"
#~ msgstr "Datos de la empresa"

#~ msgid "Previous"
#~ msgstr "Anterior"

#~ msgid "Assets"
#~ msgstr "Activos"

#~ msgid "Accounting data"
#~ msgstr "Dato contable"

#~ msgid "Other related questions"
#~ msgstr "Otras preguntas relacionadas"
