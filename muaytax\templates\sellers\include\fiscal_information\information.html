{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="fas fa-user text-c-blue wid-20"></i>
            <span class="p-l-5">Datos fiscales</span>
        </h5>
        <button
            id="toggleButtonInfo"
            onclick="toggleButtonInfo(this)"
            type="button" class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="collapse" data-bs-target=".pro-det-edit"
            aria-expanded="false" aria-controls="pro-det-edit-1 pro-det-edit-2">
            <i class="feather icon-edit"></i>
        </button>
    </div>
    {% include 'sellers/include/fiscal_information/profile.html' %}
</div>

{% if seller.legal_entity == 'sl' or seller.legal_entity == 'self-employed' or sv_es is not None %}
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="fas fa-user text-c-blue wid-20"></i>
            <i class="fas fa-plus text-c-blue wid-20"></i>
            <span class="p-l-5">Informacion Fiscal Adicional | España </span>
            <img class="wid-25" src="/static/assets/images/flags/es.svg" alt="España">
        </h5>
        <button
            id="toggleButtonInfo"
            onclick="toggleButtonInfo(this)"
            type="button" class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="collapse" data-bs-target=".pro-det2-edit"
            aria-expanded="false" aria-controls="pro-det2-edit-1 pro-det2-edit-2">
            <i class="feather icon-edit"></i>
        </button>
    </div>
    {% include 'sellers/include/fiscal_information/aditional_information_es.html' %}

</div>
{% endif %}

{% if seller.legal_entity == 'self-employed' %}
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="fas fa-calendar-alt text-c-blue wid-20"></i>
            <span class="p-l-5">Fecha de Inscripción a Tarifa Plana</span>
        </h5>
        <button
            id="toggleButtonInfo"
            onclick="toggleButtonInfo(this)"
            type="button"
            class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="collapse"
            data-bs-target=".flat-rate-edit"
            aria-expanded="false"
            aria-controls="flat-rate-edit-view flat-rate-edit-form">
            <i class="feather icon-edit"></i>
        </button>
    </div>

    {% if seller.flat_rate_inss_status == 'critical' %}
        <div class="alert alert-danger m-3" role="alert">
            🚨 ¡Atención! La tarifa plana expirará en {{ seller.flat_rate_inss_warning_days }} días.
        </div>
    {% elif seller.flat_rate_inss_status == 'expired' %}
        <div class="alert alert-secondary m-3 fw-bold" role="alert">
            La tarifa plana ya ha expirado.
        </div>
    {% endif %}
    {% include 'sellers/include/flat_rate_inss.html' %}
</div>


{% endif %}

{% if seller.legal_entity == 'sl' %}
    {% include 'sellers/include/fiscal_information/flat_rate_inss/flat_rate_inss_sl_card.html' %}
{% endif %}

{% if sv_es is not None and seller.legal_entity == 'sl' or seller.legal_entity == 'self-employed' %}
    <div class="card">
        <div class="card-header d-flex align-items-center justify-content-between">
            <h5>
                <i class="fas fa-calculator text-c-blue wid-20"></i>
                <span class="p-l-5">Rendimientos Netos</span>
            </h5>
            <button
                type="button"
                class="btn btn-primary btn-sm rounded m-0 float-end"
                data-bs-toggle="tooltip" data-bs-placement="top"
                title="Añadir rendimiento"
                id = "addNetYieldButton"
                onclick="addEditNetYield(this, 'add');"
                >
                <i class="fas fa-plus"></i>
            </button>
        </div>
        <div class="card-body border-top" >
            {% include 'sellers/include/fiscal_information/net_yields/net_yields_table.html' %}
            {% include 'sellers/include/fiscal_information/net_yields/net_yields_modal.html' %}
        </div>
    </div>
{% endif %}


<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="far fa-map text-c-blue wid-20"></i>
            <span class="p-l-5">Dirección de la Empresa o Freelance</span>
        </h5>
        <button
            id="toggleButtonInfo"
            onclick="toggleButtonInfo(this)"
            type="button" class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="collapse" data-bs-target=".pro-adr-edit"
            aria-expanded="false" aria-controls="pro-adr-edit-1 pro-adr-edit-2">
            <i class="feather icon-edit"></i>
        </button>
    </div>
    {% include 'sellers/include/fiscal_information/address.html' %}
</div>
