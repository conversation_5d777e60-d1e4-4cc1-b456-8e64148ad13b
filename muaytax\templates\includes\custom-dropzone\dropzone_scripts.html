{% load static %}
<!-- Custome Dropzone scripts START -->
<script src="{% static 'assets/js/plugins/notifier.js' %}"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
<script>
    // DOM ELEMENTS
    const dropzoneContainer = document.getElementById("dropzoneContainer");
    const customDropzone = document.getElementById("customDropzone");
    const fileUploadInput = document.getElementById("fileInput");
    const fileListContainer = document.getElementById("fileListContainer");
    const fileListRows = document.getElementById("fileList");
    const fileCount = document.getElementById("fileCount");
    const submitDropzoneBtn = document.getElementById("submitDropzoneBtn");
    const dropzoneForm = document.getElementById("uploadDropzoneForm");
    const limitInvoiceAlert = document.getElementById("limitInvoiceAlertMessage");

    const globalUserRoleInvoiceUploader = "{{ user_role }}";

    // Constants
    const ALLOWED_EXTENSIONS = ["application/pdf", "image/jpeg", "image/png"];
    // declare sweetalert2
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3500,
        timerProgressBar: true,
        customClass: {
            title: "mb-0",
        },
        didOpen: (toast) => {
            toast.addEventListener("mouseenter", Swal.stopTimer);
            toast.addEventListener("mouseleave", Swal.resumeTimer);
        },
    });

    let files = [];

    // Event Listeners
    customDropzone.addEventListener("click", handleDropzoneClick);
    customDropzone.addEventListener("dragover", handleDragOver);
    customDropzone.addEventListener("dragleave", handleDragLeave);
    customDropzone.addEventListener("drop", handleFileDrop);
    fileUploadInput.addEventListener("change", handleFileInputChange);
    dropzoneForm.addEventListener("submit", handleFormSubmit);

    function handleDropzoneClick(e) {
        // and customDropzone doesnt have .disabled class
        if (e.target !== fileUploadInput && !customDropzone.classList.contains("disabled")) {
            fileUploadInput.click();
        }
    }

    function handleDragOver(e) {
        e.preventDefault();
        if (customDropzone.classList.contains("disabled")) {
            return;
        }
        customDropzone.classList.add("custom-dropzone-dragover");
        const imgContainer = customDropzone.querySelector(".dropzone-img-container");
        imgContainer.classList.add("enlarge-img");
    }

    function handleDragLeave() {
        // e.preventDefault();
        if (customDropzone.classList.contains("disabled")) {
            return;
        }
        customDropzone.classList.remove("custom-dropzone-dragover");
        const imgContainer = customDropzone.querySelector(".dropzone-img-container");
        imgContainer.classList.remove("enlarge-img");
    }

    async function handleFileDrop(e) {
        e.preventDefault();
        if (customDropzone.classList.contains("disabled")) {
            return;
        }
        customDropzone.classList.remove("custom-dropzone-dragover");
        const droppedFiles = await filterAllowedFiles(Array.from(e.dataTransfer.files));
        handleFilesUploaded(droppedFiles);
        const imgContainer = customDropzone.querySelector(".dropzone-img-container");
        imgContainer.classList.remove("enlarge-img");
    }

    async function handleFileInputChange(e) {
        const selectedFiles = await filterAllowedFiles(Array.from(e.target.files));
        handleFilesUploaded(selectedFiles); 
        
    }

    async function filterAllowedFiles(files) {
        const validFiles = [];

        for (const file of files) {
            const isValidExtension = ALLOWED_EXTENSIONS.includes(file.type);
            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
            const {isValidPages, isEncrypted} = await _isValidPDF(file);

            if (!isValidExtension) {
                notifier.show("Formato no permitido!", `${file.name} no es un formato permitido`, "danger", "", 4000);
                continue;
            }

            if (!isValidSize) {
                notifier.show("Tamaño excedido!", `${file.name} excede el tamaño máximo de 10MB`, "danger", "", 4000);
                continue;
            }

            if (isEncrypted) {
                notifier.show("PDF encriptado!", `${file.name} está encriptado y no puede ser procesado`, "danger", "", 4000);
                continue;
            }

            if (!isValidPages) {
                notifier.show("Páginas excedidas!", `${file.name} excede el número máximo de páginas permitidas`, "danger", "", 4000);
                continue;
            }

            validFiles.push(file);
        }

        return validFiles;
    }

    function handleFilesUploaded(newFiles) {
        const newFilesToAdd = newFiles.filter((file) => !_isDuplicate(file));

        if (newFilesToAdd.length > 0) {
            files = [...files, ...newFilesToAdd];
        }
        _updateDropzoneContainer();
        _updateFileInput();
        _updateSubmitButton();
        _checkSellerLimitInvoiceUploader();
        setTimeout(() => {
            _updateFileList();
            _updateFileCount();
        }, 310);
    }

    function removeFile(index) {
        files.splice(index, 1);
        _updateDropzoneContainer();
        _updateFileInput();
        _updateSubmitButton();
        _updateFileCount();
        _updateFileList();
        _checkSellerLimitInvoiceUploader();
    }

    async function _isValidPDF(file) {
        if (file.type === "application/pdf") {

            try{
                const arrayBuffer = await file.arrayBuffer();
                const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                const pageCount = pdfDoc.getPageCount();
                return { isValidPages: pageCount <= 10, isEncrypted: false };

            } catch (error) {
                if (error.message.includes("encrypted")) {
                    return { isValidPages: false, isEncrypted: true };
                }
                
            }

        }
        return { isValidPages: true, isEncrypted: false };
    }

    function _isDuplicate(file) {
        const _isDuplicateFile = files.some(
            (f) => f.name === file.name && f.size === file.size && f.type === file.type
        );
        if (_isDuplicateFile) {
            notifier.show("Duplicado!", `${file.name} ya ha sido cargado`, "warning", "", 4000);
        }
        return _isDuplicateFile;
    }

    function _updateFileInput() {
        const dataTransfer = new DataTransfer();

        files.forEach((file) => {
            dataTransfer.items.add(file);
        });

        fileUploadInput.files = dataTransfer.files;
    }

    function _updateFileList() {
        fileListRows.innerHTML = files
            .map(
                (file, index) => {
                    const fileSizeKB = (file.size / 1024).toFixed(2);
                    return `
                        <div class="file-list-row align-items-center p-2 mb-2 rounded border">
                            <div class="d-flex f align-items-center">
                                <div class="file-icon text-primary">
                                    <i class="bi ${_getFileIcon(file.type)}"></i>
                                </div>
                                <div class=" file-info flex-grow-1 ms-2">
                                    <div class="text-truncate text-dark fw-bold">${file.name}</div>
                                    <div class="text-muted small">${fileSizeKB} kb</div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-icon btn-dlt-file btn-outline-danger" onclick="removeFile(${index})" aria-label="Eliminar archivo">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    `;
                }
            )
            .join("");
    }

    function _updateFileCount() {
        if (files.length > 0) {
            fileCount.classList.remove("d-none");
            fileCount.querySelector("span").textContent = `${files.length} ${files.length === 1
                    ? "archivo cargado y listo para procesar"
                    : "archivos cargados y listos para procesar"
                }`;
        } 
        else {
            fileCount.classList.add("d-none");
        }
    }

    function _updateSubmitButton() {
        if (files.length === 0) {
            submitDropzoneBtn.disabled = true;
        } else {
            if (globalUserRoleInvoiceUploader == "seller") {
                submitDropzoneBtn.disabled = files.length + total > limit && limit > 0;
                return;
            } 
            submitDropzoneBtn.disabled = false;
        }
    }

    function _updateDropzoneContainer() {
        if (files.length > 0) {
            dropzoneContainer.classList.add("col-12", "col-lg-6");
            fileListContainer.classList.add("col-12", "col-lg-6", "files-added");
            fileListContainer.scrollIntoView({ behavior: "smooth" });
        } else {
            dropzoneContainer.classList.remove("col-12", "col-lg-6");
            fileListContainer.classList.remove("col-12", "col-lg-6", "files-added");
        }
    }

    function _getFileIcon(fileType) {
        switch (fileType) {
            case "application/pdf":
                return "bi-filetype-pdf text-danger";
            case "image/jpeg":
                return "bi-filetype-jpg text-success";
            case "image/png":
                return "bi-filetype-png text-warning";
            default:
                return "bi-file-earmark";
        }
    }

    function _startUploadAnimation() {
        const dltFileBtns = document.querySelectorAll(".btn-dlt-file");
        dltFileBtns.forEach((btn) => {
            btn.disabled = true;
        });
        submitDropzoneBtn.disabled = true;
        customDropzone.classList.add("disabled");
        fileUploadInput.disabled = true;

        submitDropzoneBtn.classList.add("uploading");
        customDropzone.classList.add("uploading");
    }

    function _stopUploadAnimation() {
        const dltFileBtns = document.querySelectorAll(".btn-dlt-file");
        dltFileBtns.forEach((btn) => {
            btn.disabled = false;
        });

        submitDropzoneBtn.classList.remove("uploading");
        customDropzone.classList.remove("uploading");
        customDropzone.classList.remove("disabled");
        fileUploadInput.disabled = false;
    }

    function _checkSellerLimitInvoiceUploader() {
        if (globalUserRoleInvoiceUploader == "seller") {
            if (files.length + total > limit && limit > 0) {
                limitInvoiceAlert.classList.remove("d-none");
            } else {
                limitInvoiceAlert.classList.add("d-none");
            }
        }
    }

    async function handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;
        _startUploadAnimation();

        const formData = new FormData(form);
        files.forEach((file) => {
            formData.append("files", file);
        });

        try {
            await new Promise(resolve => setTimeout(resolve, 500)); // Pause for 0.5 seconds
            const response = await fetch(e.target.action, {
                method: "POST",
                body: formData,
                headers: {
                    "X-CSRFToken": '{{ csrf_token }}',
                },
            });

            if (response.ok) {
                const {data, code, message} = await response.json();
                
                _stopUploadAnimation();
                files = [];
                _updateFileInput();
                _updateDropzoneContainer();
                _updateFileCount();
                _updateFileList();

                if (code == 'partial_success') {
                    Toast.fire({
                        icon: "warning",
                        title: "Carga parcialmente exitosa!",
                        html: `
                            <p class="mb-0">
                                ${message}
                                <a href="#" id="show-errors-link" class="text-decoration-none">Ver detalles</a>
                            </p>
                        `,
                        didOpen: (toast) => {
                            toast.addEventListener("mouseenter", Swal.stopTimer);
                            toast.addEventListener("mouseleave", Swal.resumeTimer);
                            const showErrorsLink = document.getElementById("show-errors-link");
                            showErrorsLink.addEventListener("click", () => {
                                showErrorsModal(data.invoices.failed);
                                Toast.close();
                            });
                        },
                    });
                }
                else {
                    Toast.fire({
                        icon: "success",
                        title: "Muy bien!",
                        html: `
                            <p class="mb-0">
                                ${message}
                            </p>
                        `,
                    });
                }


                if (globalUserRoleInvoiceUploader == "seller") {
                    total += data.invoices_created;
                    countInvoices(total, limit);
                }
            }
            else {
                _stopUploadAnimation();
                _updateSubmitButton();

                if (response.status == 413){
                    Swal.fire({
                        icon: "warning",
                        title: "Límite de facturas excedido",
                        text: `Has excedido el límite de facturas permitidas. Puedes cargar hasta ${limit} facturas.`,
                        confirmButtonText: "Entendido",
                        confirmButtonColor: "#0d6efd",
                    });
                    return;
                }
                const {errors} = await response.json();
                
                Toast.fire({
                    icon: "error",
                    title: "Oops...",
                    html: `
                        <p class="mb-0">
                            Ocurrió un error al procesar las facturas. <a href="#" id="show-errors-link" class="text-decoration-none">Ver detalles</a>
                        </p>
                    `,
                    didOpen: (toast) => {
                        toast.addEventListener("mouseenter", Swal.stopTimer);
                        toast.addEventListener("mouseleave", Swal.resumeTimer);
                        const showErrorsLink = document.getElementById("show-errors-link");
                        showErrorsLink.addEventListener("click", () => {
                            showErrorsModal(errors);
                            Toast.close();
                        });
                    },
                });
            }

        } catch (error) {
            console.error("Error:", error);
            Toast.fire({
                icon: "error",
                title: "Oops...",
                text: "Ocurrió un error al procesar las facturas. Consulta con el departamento de IT.",
            });
            
        }
        _stopUploadAnimation();
        _updateSubmitButton();

    }

    var perfectScroll = new PerfectScrollbar(".file-list.scroll-div", {
        wheelSpeed: 0.5,
        swipeEasing: 0,
        suppressScrollX: !0,
        wheelPropagation: 1,
        minScrollbarLength: 40,
    });

    function showErrorsModal(errors) {
        const failedFilesList = document.getElementById("failedFilesList");
        const failedInvoicesLabel = document.getElementById("failedFilesModalLabel");

        failedInvoicesLabel.innerHTML = `
            <i class="bi bi-exclamation-circle-fill me-2"></i>
            Facturas con errores (${errors.length})
        `;

        const errorListHTML = errors.map(error => `
            <div class="alert alert-danger rounded padding-overflow">
                <h6 class="card-title d-flex">
                    <i class="feather icon-file-text text-danger me-2"></i>
                    ${error.file}</h6>
                <p class="card-text text-danger"><small>${error.error}</small></p>
            </div>
        `).join("");

        failedFilesList.innerHTML = errorListHTML;

        $('#modalErrors').modal('show');
    }

</script>
<!-- Custome Dropzone scripts END -->