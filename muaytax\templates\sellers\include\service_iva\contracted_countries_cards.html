{% load crispy_forms_filters crispy_forms_field %}
{% load custom_filters %}

<div class="container px-3 mt-4">
    <div class="row gy-4">
        <!-- Columna: Solo Mantenimiento -->
        <div class="col-md-6">
            <div class="rounded-container-box">
                <h5 class="mb-3 section-title">Mantenimiento</h5>
                <div class="table-responsive">
                    <table class="custom-country-table w-100">
                        <thead>
                            <tr>
                                <th class="text-start">País</th>
                                <th class="text-center">Fecha de compra</th>
                                <th class="text-center">Estado</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for iso_code, form in seller_vat_form.items %}
                                {% if form.instance.maintenance_type.code == "maintenance" %}
                                    {% with submitted=submitted_countries|contains:iso_code %}
                                        <tr class="align-middle">
                                            <td class="text-start">
                                                <img src="{{ STATIC_URL }}assets/images/flags/{{ iso_code|lower }}.svg"
                                                     alt="{{ country_names|get_item:iso_code }}"
                                                     class="flag-icon me-2" />
                                                {{ country_names|get_item:iso_code }}
                                            </td>
                                            <td class="text-center">
                                                {{ form.contracting_date.value|date:"Y-m-d" }}
                                            </td>
                                            <td class="text-center">
                                                {% if submitted or is_form_processed %}
                                                    <span class="pill-btn bg-secondary">Enviado</span>
                                                {% else %}
                                                    {% with country_errors=countries_errors_info|get_item:iso_code %}
                                                        {% if country_errors %}
                                                            <span 
                                                                class="pill-btn bg-danger"
                                                                data-bs-toggle="tooltip"
                                                                data-bs-placement="top"
                                                                data-bs-html="true"
                                                                title="{{ country_errors.messages|join:'<br>' }}">
                                                                Errores
                                                            </span>
                                                        {% else %}
                                                            <span class="pill-btn bg-success">Editable</span>
                                                        {% endif %}
                                                    {% endwith %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endwith %}
                                {% endif %}
                            {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center text-muted">No hay países.</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Columna: Alta + Mantenimiento -->
        <div class="col-md-6">
            <div class="rounded-container-box">
                <h5 class="mb-3 section-title">Alta nueva con mantenimiento</h5>
                <div class="table-responsive">
                    <table class="custom-country-table w-100">
                        <thead>
                            <tr>
                                <th class="text-start">País</th>
                                <th class="text-center">Fecha de compra</th>
                                <th class="text-center">Estado</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for iso_code, form in seller_vat_form.items %}
                                {% if form.instance.maintenance_type.code == "maintenance_subcription" %}
                                    {% with submitted=submitted_countries|contains:iso_code %}
                                        <tr class="align-middle">
                                            <td class="text-start">
                                                <img src="{{ STATIC_URL }}assets/images/flags/{{ iso_code|lower }}.svg"
                                                     alt="{{ country_names|get_item:iso_code }}"
                                                     class="flag-icon me-2" />
                                                {{ country_names|get_item:iso_code }}
                                            </td>
                                            <td class="text-center">
                                                {{ form.contracting_date.value|date:"Y-m-d" }}
                                            </td>
                                            <td class="text-center">
                                                {% if submitted or is_form_processed %}
                                                    <span class="pill-btn bg-secondary">Enviado</span>
                                                {% else %}
                                                    {% with country_errors=countries_errors_info|get_item:iso_code %}
                                                        {% if country_errors %}
                                                            <span 
                                                                class="pill-btn bg-danger"
                                                                data-bs-toggle="tooltip"
                                                                data-bs-placement="top"
                                                                data-bs-html="true"
                                                                title="{{ country_errors.messages|join:'<br>' }}">
                                                                Errores
                                                            </span>
                                                        {% else %}
                                                            {% if form.instance.form_submitted %}
                                                                <span class="pill-btn bg-warning text-dark">Procesando</span>
                                                            {% else %}
                                                                <span class="pill-btn bg-success">Editable</span>
                                                            {% endif %}
                                                        {% endif %}
                                                    {% endwith %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endwith %}
                                {% endif %}
                            {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center text-muted">No hay países.</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function (tooltipTriggerEl) {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>

