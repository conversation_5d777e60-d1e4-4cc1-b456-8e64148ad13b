from collections import defaultdict
from datetime import datetime
import json

from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.forms.models import BaseModelForm
from django.http import HttpResponse, HttpResponseBadRequest, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, UpdateView, CreateView, DeleteView, View
from django.core.exceptions import ValidationError
from django.conf import settings

from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from muaytax.utils.mixins import JsonableResponseMixin

from muaytax.app_address.models.address import Address
from muaytax.app_documents.models.document import Document
from muaytax.app_sellers.forms.seller_vat import (
    sellerVatChangeForm, 
    SellerVatDeleteForm, 
    SellerVatNewSellerForm,
    SellerVatNotContractedForm, 
    SellerVatNotContractedChangeForm,
)
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_vat_history import SellerVatHistory
from muaytax.app_representatives.models.representative import Representative
from muaytax.users.models import User
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.sellervat_es_status_activation import SellerVatEsStatusActivation
from muaytax.dictionaries.models.sellervat_es_status_alta_iae import SellerVatEsStatusAltaIae
from muaytax.dictionaries.models.sellervat_es_status_c_digital import SellerVatEsStatusCDigital
from muaytax.dictionaries.models.sellervat_es_status_eori import SellerVatEsStatusEori
from muaytax.dictionaries.models.sellervat_es_status_vies import SellerVatEsStatusVies
from muaytax.dictionaries.models.sellervat_period import SellerVatPeriod
from muaytax.dictionaries.models.sellervat_quarter import SellerVatQuarter
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_status_process_color import SellerVatStatusProcessColor
from muaytax.dictionaries.models.sellervat_type import SellerVatType
from muaytax.dictionaries.models.payment_methods import PaymentMethod
from muaytax.app_documents.models.document import Document


import logging

logger = logging.getLogger(__name__)

debug = settings.DEBUG # Variable de depuración global

class SellerVATListView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = SellerVat
    template_name_suffix = "_list"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def setTemplateByRole(self):
        self.template_name_suffix = "_list_seller"

        user_role = self.request.user.role
        if user_role == "manager":
            self.template_name_suffix = "_list_manager"

        return self.template_name

    def get(self, request, *args, **kwargs):
        self.setTemplateByRole()
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        vats = SellerVat.objects.filter(seller_id=seller.id)
        for vat in vats:
            vat.document = False
            document=Document.objects.filter(sellerVat_id=vat.id)
            if document:
                vat.document = True
  
       
        return vats

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


def update_historial(self):
    sellervat_pk = self.object.pk
    sellervat = SellerVat.objects.get(pk=sellervat_pk)

    fields = self.request.POST.dict()

    if (fields.get('is_contracted') == 'on'):
        is_contracted = True
    else:
        is_contracted = False

    if (fields.get('vat_vies') == 'on'):
        vat_vies = True
    else:
        vat_vies = False

    if (fields.get('is_max_priority') == 'on'):
        is_max_priority = True
    else:
        is_max_priority = False

    if (fields.get('is_local') == 'on'):
        is_local = True
    else:
        is_local = False

    user = self.request.user

    # for campo, valor in fields.items():
    #     debug and print(f"{campo}: {valor}")
    # debug and print("---------------")

    if sellervat.contracting_date != None and sellervat.contracting_date != '':
        sellervat_contracting_date = sellervat.contracting_date.strftime('%Y-%m-%d')
    else:
        sellervat_contracting_date = None

    if sellervat.end_contracting_date != None and sellervat.end_contracting_date != '':
        sellervat_end_contracting_date = sellervat.end_contracting_date.strftime('%Y-%m-%d')
    else:
        sellervat_end_contracting_date = None

    if sellervat.contracting_discontinue != None and sellervat.contracting_discontinue != '':
        sellervat_contracting_discontinue = sellervat.contracting_discontinue.strftime('%Y-%m-%d')
    else:
        sellervat_contracting_discontinue = None

    if sellervat.activation_date != None and sellervat.activation_date != '':
        sellervat_activation_date = sellervat.activation_date.strftime('%Y-%m-%d')
    else:
        sellervat_activation_date = None

    if sellervat.deactivation_date != None and sellervat.deactivation_date != '':
        sellervat_deactivation_date = sellervat.deactivation_date.strftime('%Y-%m-%d')
    else:
        sellervat_deactivation_date = None

    if sellervat.start_contracting_date != None and sellervat.start_contracting_date != '':
        sellervat_start_contracting_date = sellervat.start_contracting_date.strftime('%Y-%m-%d')
    else:
        sellervat_start_contracting_date = None

    if sellervat.collection_date != None and sellervat.collection_date != '':
        sellervat_collection_date = sellervat.collection_date.strftime('%Y-%m-%d')
    else:
        sellervat_collection_date = None
    

    if (sellervat.vat_country.iso_code != fields.get('vat_country')):
        vat_country = Country.objects.get(iso_code=fields.get('vat_country'))
    else:
        vat_country = None

    if (sellervat.vat_number != fields.get('vat_number')):
        vat_number = fields.get('vat_number')
    else:
        vat_number = None

    if (sellervat.siret != fields.get('siret')):
        siret = fields.get('siret')
    else:
        siret = None

    if (sellervat.steuernummer != fields.get('steuernummer')):
        steuernummer = fields.get('steuernummer')
    else:
        steuernummer = None

    if (sellervat.vat_vies != vat_vies):
        vat_vies = vat_vies
    else:
        vat_vies = None

    if (sellervat.is_contracted != is_contracted):
        is_contracted = is_contracted
    else:
        is_contracted = None

    if (sellervat_contracting_date != fields.get('contracting_date') and fields.get(
        'contracting_date') != None and fields.get('contracting_date') != ''):
        contracting_date = fields.get('contracting_date')
    else:
        contracting_date = None

    if (sellervat_start_contracting_date != fields.get('start_contracting_date') and fields.get(
        'start_contracting_date') != None and fields.get('contracting_date') != ''):
        start_contracting_date = fields.get('start_contracting_date')
    else:
        start_contracting_date = None

    if (sellervat_collection_date != fields.get('collection_date') and fields.get(
        'collection_date') != None and fields.get('contracting_date') != ''):
        collection_date = fields.get('collection_date')
    else:
        collection_date = None

    

    if (sellervat_end_contracting_date != fields.get('end_contracting_date') and fields.get(
        'end_contracting_date') != None and fields.get('end_contracting_date') != ''):
        end_contracting_date = fields.get('end_contracting_date')
    else:
        end_contracting_date = None

    if (sellervat_contracting_discontinue != fields.get('contracting_discontinue') and fields.get(
        'contracting_discontinue') != None and fields.get('contracting_discontinue') != ''):
        contracting_discontinue = fields.get('contracting_discontinue')
    else:
        contracting_discontinue = None

    if (sellervat.es_status_activation is not None and sellervat.es_status_activation.code != fields.get(
        'es_status_activation') and sellervat.vat_country.iso_code == 'ES'):
        es_status_activation = SellerVatEsStatusActivation.objects.get(code=fields.get('es_status_activation'))
    else:
        es_status_activation = None

    if (sellervat.es_status_altaiae is not None and sellervat.es_status_altaiae.code != fields.get(
        'es_status_altaiae') and sellervat.vat_country.iso_code == 'ES'):
        es_status_altaiae = SellerVatEsStatusAltaIae.objects.get(code=fields.get('es_status_altaiae'))
    else:
        es_status_altaiae = None

    if (sellervat.es_status_cdigital is not None and sellervat.es_status_cdigital.code != fields.get(
        'es_status_cdigital') and sellervat.vat_country.iso_code == 'ES'):
        es_status_cdigital = SellerVatEsStatusCDigital.objects.get(code=fields.get('es_status_cdigital'))
    else:
        es_status_cdigital = None

    if (sellervat.es_status_vies is not None and sellervat.es_status_vies.code != fields.get(
        'es_status_vies') and sellervat.vat_country.iso_code == 'ES'):
        es_status_vies = SellerVatEsStatusVies.objects.get(code=fields.get('es_status_vies'))
    else:
        es_status_vies = None

    if (sellervat.es_status_eori is not None and sellervat.es_status_eori.code != fields.get(
        'es_status_eori') and sellervat.vat_country.iso_code == 'ES'):
        es_status_eori = SellerVatEsStatusEori.objects.get(code=fields.get('es_status_eori'))
    else:
        es_status_eori = None

    if (sellervat.status_process is not None and sellervat.status_process.code != fields.get('status_process')):
        status_process = SellerVatStatusProcess.objects.get(code=fields.get('status_process'))
    else:
        status_process = None

    if (sellervat.is_max_priority != is_max_priority):
        is_max_priority = is_max_priority
    else:
        is_max_priority = None

    if (sellervat.type is not None and sellervat.type.code != fields.get('type') and (fields.get('type') != '')):
        type = SellerVatType.objects.get(code=fields.get('type'))
    else:
        type = None

    if (sellervat.vat_status is not None and sellervat.vat_status.code != fields.get('vat_status')):
        vat_status = SellerVatStatus.objects.get(code=fields.get('vat_status'))
    else:
        vat_status = None

    if (sellervat_activation_date != fields.get('activation_date') and fields.get(
        'activation_date') != None and fields.get('activation_date') != ''):
        activation_date = fields.get('activation_date')
    else:
        activation_date = None

    if (sellervat_deactivation_date != fields.get('deactivation_date') and fields.get(
        'deactivation_date') != None and fields.get('deactivation_date') != ''):
        deactivation_date = fields.get('deactivation_date')
    else:
        deactivation_date = None

    if (sellervat.period is not None and sellervat.period.code != fields.get('period') and (
        fields.get('period') != '')):
        period = SellerVatPeriod.objects.get(code=fields.get('period'))
    else:
        period = None

    if (sellervat.quarter is not None and sellervat.quarter.code != fields.get('quarter') and (
        fields.get('quarter') != '')):
        quarter = SellerVatQuarter.objects.get(code=fields.get('quarter'))
    else:
        quarter = None

    if (sellervat.payment_method_hmrc is not None and sellervat.payment_method_hmrc.code != fields.get('payment_method_hmrc') and (
        fields.get('payment_method_hmrc') != '')):
        payment_method_hmrc = SellerVatQuarter.objects.get(code=fields.get('payment_method_hmrc'))
    else:
        payment_method_hmrc = None

    if (sellervat.comment != fields.get('comment')):
        comment = fields.get('comment')
    else:
        comment = None

    if ((sellervat.vat_address is not None and sellervat.vat_address.address_name != fields.get('address_name')) or (
        sellervat.vat_address is None)):
        address_name = fields.get('address_name')
    else:
        address_name = None

    if ((sellervat.vat_address is not None and sellervat.vat_address.address != fields.get('address')) or (
        sellervat.vat_address is None)):
        address = fields.get('address')
    else:
        address = None

    if ((
        sellervat.vat_address is not None and sellervat.vat_address.address_number != fields.get('address_number')) or (
        sellervat.vat_address is None)):
        address_number = fields.get('address_number')
    else:
        address_number = None

    if ((sellervat.vat_address is not None and sellervat.vat_address.address_continue != fields.get(
        'address_continue')) or (sellervat.vat_address is None)):
        address_continue = fields.get('address_continue')
    else:
        address_continue = None

    if ((sellervat.vat_address is not None and sellervat.vat_address.address_zip != fields.get('address_zip')) or (
        sellervat.vat_address is None)):
        address_zip = fields.get('address_zip')
    else:
        address_zip = None

    if ((sellervat.vat_address is not None and sellervat.vat_address.address_city != fields.get('address_city')) or (
        sellervat.vat_address is None)):
        address_city = fields.get('address_city')
    else:
        address_city = None

    if (
        sellervat.vat_address is not None and sellervat.vat_address.address_country != '' and sellervat.vat_address.address_country != None and sellervat.vat_address.address_country.iso_code != fields.get(
        'address_country')):
        address_country = Country.objects.get(iso_code=fields.get('address_country'))
    else:
        address_country = None

    if ((sellervat.vat_address is not None and sellervat.vat_address.address_catastral != fields.get(
        'address_catastral')) or (sellervat.vat_address is None)):
        address_catastral = fields.get('address_catastral')
    else:
        address_catastral = None

    new_sellervat_history = SellerVatHistory.objects.create(
        user=user,
        sellervat=sellervat,
        vat_country=vat_country,
        vat_number=vat_number,
        siret=siret,
        steuernummer=steuernummer,
        vat_vies=vat_vies,
        is_contracted=is_contracted,
        contracting_date=contracting_date,
        start_contracting_date=start_contracting_date,
        collection_date=collection_date,
        end_contracting_date=end_contracting_date,
        contracting_discontinue=contracting_discontinue,
        es_status_activation=es_status_activation,
        es_status_altaiae=es_status_altaiae,
        es_status_cdigital=es_status_cdigital,
        es_status_vies=es_status_vies,
        es_status_eori=es_status_eori,
        status_process=status_process,
        is_max_priority=is_max_priority,
        is_local=is_local,
        type=type,
        vat_status=vat_status,
        activation_date=activation_date,
        deactivation_date=deactivation_date,
        period=period,
        quarter=quarter,
        payment_method_hmrc=payment_method_hmrc,
        comment=comment,
        address_name=address_name,
        address=address,
        address_number=address_number,
        address_continue=address_continue,
        address_zip=address_zip,
        address_city=address_city,
        address_country=address_country,
        address_catastral=address_catastral
    )


def create_historial(self, instance):
    user = self.request.user
    sellervat = SellerVat.objects.get(pk=instance.pk)

    # debug and print(instance)

    if (instance.vat_country != None):
        vat_country = Country.objects.get(iso_code=instance.vat_country.iso_code)
    else:
        vat_country = None

    if (instance.vat_number != None):
        vat_number = instance.vat_number
    else:
        vat_number = None

    if (instance.siret != None):
        siret = instance.siret
    else:
        siret = None

    if (instance.steuernummer != None):
        steuernummer = instance.steuernummer
    else:
        steuernummer = None

    if (instance.vat_vies != None):
        vat_vies = instance.vat_vies
    else:
        vat_vies = None

    if (instance.is_contracted != None):
        is_contracted = instance.is_contracted
    else:
        is_contracted = None

    if (instance.is_local != None):
        is_local = instance.is_local
    else:
        is_local = None

    if (instance.contracting_date != None):
        contracting_date = instance.contracting_date
    else:
        contracting_date = None

    if (instance.start_contracting_date != None):
        start_contracting_date = instance.start_contracting_date
    else:
        start_contracting_date = None

    if (instance.collection_date != None):
        collection_date = instance.collection_date
    else:
        collection_date = None

    if (instance.end_contracting_date != None):
        end_contracting_date = instance.end_contracting_date
    else:
        end_contracting_date = None

    if (instance.contracting_discontinue != None):
        contracting_discontinue = instance.contracting_discontinue
    else:
        contracting_discontinue = None

    if (instance.es_status_activation != None and instance.vat_country.iso_code == 'ES'):
        es_status_activation = SellerVatEsStatusActivation.objects.get(code=instance.es_status_activation.code)
    else:
        es_status_activation = None

    if (instance.es_status_altaiae != None and instance.vat_country.iso_code == 'ES'):
        es_status_altaiae = SellerVatEsStatusAltaIae.objects.get(code=instance.es_status_altaiae.code)
    else:
        es_status_altaiae = None

    if (instance.es_status_cdigital != None and instance.vat_country.iso_code == 'ES'):
        es_status_cdigital = SellerVatEsStatusCDigital.objects.get(code=instance.es_status_cdigital.code)
    else:
        es_status_cdigital = None

    if (instance.es_status_vies != None and instance.vat_country.iso_code == 'ES'):
        es_status_vies = SellerVatEsStatusVies.objects.get(code=instance.es_status_vies.code)
    else:
        es_status_vies = None

    if (instance.es_status_eori != None and instance.vat_country.iso_code == 'ES'):
        es_status_eori = SellerVatEsStatusEori.objects.get(code=instance.es_status_eori.code)
    else:
        es_status_eori = None

    if (instance.status_process != None):
        status_process = SellerVatStatusProcess.objects.get(code=instance.status_process.code)
    else:
        status_process = None

    if (instance.is_max_priority != None):
        is_max_priority = instance.is_max_priority
    else:
        is_max_priority = None

    if (instance.type != None):
        type = SellerVatType.objects.get(code=instance.type.code)
    else:
        type = None

    if (instance.vat_status != None):
        vat_status = SellerVatStatus.objects.get(code=instance.vat_status.code)
    else:
        vat_status = None

    if (instance.activation_date != None):
        activation_date = instance.activation_date
    else:
        activation_date = None

    if (instance.deactivation_date != None):
        deactivation_date = instance.deactivation_date
    else:
        deactivation_date = None

    if (instance.period != None):
        period = SellerVatPeriod.objects.get(code=instance.period.code)
    else:
        period = None

    if (instance.quarter != None):
        quarter = SellerVatQuarter.objects.get(code=instance.quarter.code)
    else:
        quarter = None

    if (instance.payment_method_hmrc != None):
        payment_method_hmrc = PaymentMethod.objects.get(code=instance.payment_method_hmrc.code)
    else:
        payment_method_hmrc = None   

    if (instance.comment != None):
        comment = instance.comment
    else:
        comment = None

    if (instance.vat_address != None and instance.vat_address.address_name != None):
        address_name = instance.vat_address.address_name
    else:
        address_name = None

    if (instance.vat_address != None and instance.vat_address.address != None):
        address = instance.vat_address.address
    else:
        address = None

    if (instance.vat_address != None and instance.vat_address.address_number != None):
        address_number = instance.vat_address.address_number
    else:
        address_number = None

    if (instance.vat_address != None and instance.vat_address.address_continue != None):
        address_continue = instance.vat_address.address_continue
    else:
        address_continue = None

    if (instance.vat_address != None and instance.vat_address.address_zip != None):
        address_zip = instance.vat_address.address_zip
    else:
        address_zip = None

    if (instance.vat_address != None and instance.vat_address.address_city != None):
        address_city = instance.vat_address.address_city
    else:
        address_city = None

    if (instance.vat_address != None and instance.vat_address.address_country != None):
        address_country = Country.objects.get(iso_code=instance.vat_address.address_country.iso_code)
    else:
        address_country = None

    if (instance.vat_address != None and instance.vat_address.address_catastral != None):
        address_catastral = instance.vat_address.address_catastral
    else:
        address_catastral = None

    new_sellervat_history = SellerVatHistory.objects.create(
        user=user,
        sellervat=sellervat,
        vat_country=vat_country,
        vat_number=vat_number,
        siret=siret,
        steuernummer=steuernummer,
        vat_vies=vat_vies,
        is_contracted=is_contracted,
        contracting_date=contracting_date,
        start_contracting_date=start_contracting_date,
        collection_date=collection_date,
        end_contracting_date=end_contracting_date,
        contracting_discontinue=contracting_discontinue,
        es_status_activation=es_status_activation,
        es_status_altaiae=es_status_altaiae,
        es_status_cdigital=es_status_cdigital,
        es_status_vies=es_status_vies,
        es_status_eori=es_status_eori,
        status_process=status_process,
        is_max_priority=is_max_priority,
        is_local=is_local,
        type=type,
        vat_status=vat_status,
        activation_date=activation_date,
        deactivation_date=deactivation_date,
        period=period,
        quarter=quarter,
        payment_method_hmrc=payment_method_hmrc,
        comment=comment,
        address_name=address_name,
        address=address,
        address_number=address_number,
        address_continue=address_continue,
        address_zip=address_zip,
        address_city=address_city,
        address_country=address_country,
        address_catastral=address_catastral

    )


class SellerVATDetailView(JsonableResponseMixin, LoginRequiredMixin,
                          (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = SellerVat
    form_class = sellerVatChangeForm
    template_name_suffix = "_detail"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def post(self, request, *args, **kwargs):
        request.POST = request.POST.copy()
        request.POST['seller'] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        sellervat = get_object_or_404(SellerVat, seller_id=seller.id, pk=self.kwargs["pk"])
        countries = Country.objects.all().order_by("name")
        sellervat_address = Address.objects.filter(vat_address__id=sellervat.pk)
        representative = Representative.objects.filter(seller_id = seller.pk, type_representation = 'legal_representative')
        manager_assigned = User.objects.filter(role = 'manager')
        sellervat_status = SellerVatStatus.objects.all().order_by("order")
        sellervat_period = SellerVatPeriod.objects.all().order_by("order")
        sellervat_quarter = SellerVatQuarter.objects.all().order_by("order")
        sellervat_status_process = SellerVatStatusProcess.objects.all().order_by("order")
        sellervat_status_list = []
        for status in sellervat_status:
            sellervat_status_list.append({
                'code': status.code,
                'description': status.description,
                'contracted': status.contracted
            })
        sellervat_status_json = json.dumps(sellervat_status_list)
        status_activation = SellerVatEsStatusActivation.objects.all().order_by("order")
        status_alta_iae = SellerVatEsStatusAltaIae.objects.all().order_by("order")
        status_c_digital = SellerVatEsStatusCDigital.objects.all().order_by("order")
        status_vies = SellerVatEsStatusVies.objects.all().order_by("order")
        status_eori = SellerVatEsStatusEori.objects.all().order_by("order")
        sellervat_type = SellerVatType.objects.all().order_by("order")
        sellervat_status_process_color = SellerVatStatusProcessColor.objects.all().order_by("order")
        sellervat_history = SellerVatHistory.objects.filter(sellervat=sellervat).order_by("-created_at")
        history_first = SellerVatHistory.objects.filter(sellervat=sellervat).first()

        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["sellervat"] = sellervat
        context["sellervat_status_process"] = sellervat_status_process
        context["sellervat_status"] = sellervat_status
        context["status_process_color"] = sellervat_status_process_color
        context["sellervat_status_json"] = sellervat_status_json
        context["sellervat_period"] = sellervat_period
        context["sellervat_quarter"] = sellervat_quarter
        context["countries"] = countries

        context["status_activation"] = status_activation
        context["status_alta_iae"] = status_alta_iae
        context["status_c_digital"] = status_c_digital
        context["status_vies"] = status_vies
        context["status_eori"] = status_eori
        context["sellervat_type"] = sellervat_type

        context["sellervat_address"] = sellervat_address
        context["sellervat_history"] = sellervat_history
        context["history_first"] = history_first
        context["representative"] = representative
        context["manager_assigned"] = manager_assigned

        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:vat_list",
            args=[self.object.seller.shortname],
        )

    def form_valid(self, form):
        update_historial(self)

        fields = self.request.POST.dict()
        sellervat_pk = self.object.pk
        sellervat = SellerVat.objects.get(pk=sellervat_pk)

        if fields.get('address_name') is not None and fields.get('address_name') != '':
            address_country = Country.objects.get(iso_code=fields.get('address_country'))

            if sellervat.vat_address:
                address_pk = sellervat.vat_address_id

                address = Address.objects.get(pk=address_pk)
                address.address_name = fields.get('address_name')
                address.address = fields.get('address')
                address.address_number = fields.get('address_number')
                address.address_continue = fields.get('address_continue')
                address.address_zip = fields.get('address_zip')
                address.address_city = fields.get('address_city')
                address.address_country = address_country
                address.address_catastral = fields.get('address_catastral')

                address.save()

            else:
                new_address = Address.objects.create(
                    address_name=fields.get('address_name'),
                    address=fields.get('address'),
                    address_number=fields.get('address_number'),
                    address_continue=fields.get('address_continue'),
                    address_zip=fields.get('address_zip'),
                    address_city=fields.get('address_city'),
                    address_country=address_country,
                    address_catastral=fields.get('address_catastral')
                )

                self.object.vat_address = new_address
                self.object.save()

        response = super().form_valid(form)
        self.update_values()
        return response

    def dict_data(self, form):
        return {'valid': True}

    def form_invalid(self, form: BaseModelForm) -> HttpResponse:
        debug and print(form.errors)
        return super().form_invalid(form)

    def update_values(self):
        # actualizamos prioridad del sellerVat
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        seller_vat = get_object_or_404(SellerVat, seller_id=seller.id, pk=self.kwargs["pk"])


    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVATNewView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    model = SellerVat
    form_class = sellerVatChangeForm
    template_name_suffix = "_detail"

    def post(self, request, *args, **kwargs):
        request.POST = request.POST.copy()
        request.POST['seller'] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        instance = form.save()
        fields = self.request.POST.dict()

        if fields.get('address_name') is not None and fields.get('address_name') != '':
            new_address = Address.objects.create(
                address_name=fields.get('address_name'),
                address=fields.get('address'),
                address_number=fields.get('address_number'),
                address_continue=fields.get('address_continue'),
                address_zip=fields.get('address_zip'),
                address_city=fields.get('address_city'),
                address_catastral=fields.get('address_catastral'),
                address_country=Country.objects.get(iso_code=fields.get('address_country'))
            )
            instance.vat_address = new_address
            instance.save()

        create_historial(self, instance)

        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        # sellervat = get_object_or_404(SellerVat, seller_id=seller.id, pk=self.kwargs["pk"])
        countries = Country.objects.all().order_by("name")
        sellervat_status = SellerVatStatus.objects.all().order_by("order")
        sellervat_period = SellerVatPeriod.objects.all().order_by("order")
        sellervat_quarter = SellerVatQuarter.objects.all().order_by("order")
        sellervat_status_process = SellerVatStatusProcess.objects.all().order_by("order")
        representative = Representative.objects.filter(seller_id = seller.pk)

        sellervat_status_list = []
        for status in sellervat_status:
            sellervat_status_list.append({
                'code': status.code,
                'description': status.description,
                'contracted': status.contracted
            })
        sellervat_status_json = json.dumps(sellervat_status_list)
        status_activation = SellerVatEsStatusActivation.objects.all().order_by("order")
        status_alta_iae = SellerVatEsStatusAltaIae.objects.all().order_by("order")
        status_c_digital = SellerVatEsStatusCDigital.objects.all().order_by("order")
        status_vies = SellerVatEsStatusVies.objects.all().order_by("order")
        status_eori = SellerVatEsStatusEori.objects.all().order_by("order")
        sellervat_type = SellerVatType.objects.all().order_by("order")
        sellervat_status_process_color = SellerVatStatusProcessColor.objects.all().order_by("order")

        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        # context["sellervat"] = sellervat
        context["status_process_color"] = sellervat_status_process_color
        context["sellervat_status_process"] = sellervat_status_process
        context["sellervat_status"] = sellervat_status
        context["sellervat_status_json"] = sellervat_status_json
        context["sellervat_period"] = sellervat_period
        context["sellervat_quarter"] = sellervat_quarter
        context["countries"] = countries
        context["status_activation"] = status_activation
        context["status_alta_iae"] = status_alta_iae
        context["status_c_digital"] = status_c_digital
        context["status_vies"] = status_vies
        context["status_eori"] = status_eori
        context["sellervat_type"] = sellervat_type
        context["representative"] = representative

        context["is_new"] = True
        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:vat_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVATDeleteView(LoginRequiredMixin, (IsSellerShortnamePermission or IsManagerRolePermission),
                          SuccessMessageMixin, DeleteView):
    model = SellerVat
    form_class = SellerVatDeleteForm
    # template_name_suffix = "_delete"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def setTemplateByRole(self):
        user_role = self.request.user.role
        if user_role == "manager":
            self.template_name = "sellers/sellervat_delete.html"
        else:
            self.template_name = "sellers/sellervat_deleteseller.html"
        return self.template_name

    def get(self, request, *args, **kwargs):
        self.setTemplateByRole()
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        vat = get_object_or_404(SellerVat, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:vat_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVATUpdateView(LoginRequiredMixin, IsSellerShortnamePermission, SuccessMessageMixin, UpdateView):
    model = SellerVat
    form_class = sellerVatChangeForm
    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVATNewSeller(LoginRequiredMixin, IsSellerShortnamePermission, SuccessMessageMixin, CreateView):
    model = SellerVat
    form_class = SellerVatNewSellerForm
    template_name_suffix = "_detail_seller"

    def post(self, request, *args, **kwargs):
        debug and print("post")
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        request.POST = request.POST.copy()
        request.POST['seller'] = seller
        request.POST['is_contracted'] = False
        form = self.get_form()
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

    def form_invalid(self, form):
        debug and print("form_invalid: " + str(form.errors))
        return HttpResponseBadRequest(form.errors)

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        debug and print("form_valid")
        form.instance.seller = seller
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        vat_country = SellerVat.objects.filter(seller_id=seller.id).values_list('vat_country__name',
                                                                                flat=True).distinct()
        countries = Country.objects.exclude(name__in=vat_country).order_by("name")

        context = super().get_context_data(**kwargs)
        context["countries"] = countries
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return reverse(
            "app_sellers:vat_list",
            args=[seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVatNotContracted(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    model = SellerVat
    form_class = SellerVatNotContractedForm
    template_name_suffix = "_not_contracted"

    def post(self, request, *args, **kwargs):
        request.POST = request.POST.copy()
        request.POST['seller'] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        request.POST['is_contracted'] = False

        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        representative = Representative.objects.filter(seller_id = seller.pk)

        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["countries"] = Country.objects.all().order_by("name")
        context["representative"] = representative        
        return context

    def get_success_url(self):
        return reverse("app_sellers:vat_list", args=[self.object.seller.shortname])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class SellerVatNotContractedUpdate(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                                   UpdateView):
    model = SellerVat
    form_class = SellerVatNotContractedChangeForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = '_not_contracted'

    def post(self, request, *args, **kwargs):
        request.POST = request.POST.copy()
        request.POST['seller'] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        representative = Representative.objects.filter(seller_id = seller.pk, type_representation = 'legal_representative')

        debug and print(f"Repres: {representative}")

        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["countries"] = Country.objects.all().order_by("name")
        context["representative"] = representative
        return context

    def get_success_url(self):
        return reverse("app_sellers:vat_list", args=[self.object.seller.shortname])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home")) 

class UpdateVatActivationDates(LoginRequiredMixin, View):
    template_name = 'sellers/include/fiscal_information/partials/vat-country-card.html'

    def get(self, request, *args, **kwargs):
        seller_vat_pk = request.GET.get("seller_vat_pk")
        debug and print(f"GET seller_vat_pk => {seller_vat_pk}")
        sellervat = get_object_or_404(SellerVat, pk=seller_vat_pk)

        context = {'pais_iva': sellervat}
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        debug and print("POST data =>", request.POST)

        try:
            seller_vat_pk = request.POST.get("seller_vat_pk")
            sellervat = get_object_or_404(SellerVat, pk=seller_vat_pk)

            # Parsear fechas
            contracting_date = self.parse_date(request.POST.get("contracting_date"))
            contracting_discontinue = self.parse_date(request.POST.get("contracting_discontinue"))
            activation_date = self.parse_date(request.POST.get("activation_date"))
            deactivation_date = self.parse_date(request.POST.get("deactivation_date"))
            start_contracting_date = self.parse_date(request.POST.get("start_contracting_date"))
            end_contracting_date = self.parse_date(request.POST.get("end_contracting_date"))
            collection_date = self.parse_date(request.POST.get("collection_date"))

            # Diccionario para almacenar todos los errores
            validation_errors = defaultdict(list)

            # Lista simplificada de fechas por tipo de servicio
            dates_fields = [
                ("contracting_date", "contracting_discontinue", "Contratación"),
                ("activation_date", "deactivation_date", "Hacienda"),
                ("start_contracting_date", "end_contracting_date", "Servicio"),
            ]

            # Validar que la fecha de baja no sea anterior a la de alta y que no haya fecha de baja sin fecha de alta
            for start_field, end_field, service_type in dates_fields:
                start_date = locals().get(start_field) # Obtener los valores del campo fecha alta
                end_date = locals().get(end_field) # Obtener los valores del campo fecha baja

                if start_date and end_date and end_date < start_date:
                    validation_errors[end_field].append(
                        f"La fecha de baja de {service_type} no puede ser anterior a la de alta."
                    )
                if not start_date and end_date:
                    validation_errors[start_field].append(
                        f"Debe haber una fecha de alta de {service_type} si se especifica una fecha de baja."
                    )

            # Validar la fecha de cobro respecto al inicio del servicio
            if start_contracting_date and collection_date and collection_date < start_contracting_date:
                validation_errors["collection_date"].append(
                    "La fecha de cobro no puede ser anterior a la fecha de inicio del servicio."
                )

            # Asignar valores al objeto
            sellervat.contracting_date = contracting_date
            sellervat.contracting_discontinue = contracting_discontinue
            sellervat.activation_date = activation_date
            sellervat.deactivation_date = deactivation_date
            sellervat.start_contracting_date = start_contracting_date
            sellervat.end_contracting_date = end_contracting_date
            sellervat.collection_date = collection_date

            # Asignar valores del VAT
            vat_number = request.POST.get("vat_number", "").strip() or None
            steuernummer = request.POST.get("steuernummer", "").strip() or None
            sellervat.vat_number = vat_number
            sellervat.steuernummer = steuernummer

            # Intentar ejecutar validaciones del modelo
            try:
                sellervat.clean()
            except ValidationError as e:
                for field, errors in e.message_dict.items():
                    validation_errors[field].extend(errors)

            # Si hay errores, devolverlos todos en un solo JSON
            if validation_errors:
                response_data = {"errors": dict(validation_errors)}
                debug and print("JSON response (errores) =>", response_data)
                return JsonResponse(response_data, status=400)

            # Guardar solo si no hay errores
            try:
                sellervat.save()
            except Exception as e:
                debug and print(f"Error guardando sellervat: {e}")
                return JsonResponse({'error': f'Error al guardar: {str(e)}'}, status=500)


        except ValueError as e:
            response_data = {'error': f'Error en los datos ingresados: {str(e)}'}
            debug and print("JSON response (ValueError) =>", response_data)
            return JsonResponse(response_data, status=400)

        except Exception as e:
            response_data = {'error': f'Error inesperado: {str(e)}'}
            debug and print("JSON response (Exception) =>", response_data)
            return JsonResponse(response_data, status=500)

        # Renderizar la tarjeta si todo fue exitoso
        debug and print("Datos guardados correctamente en BD")
        return render(request, self.template_name, {
            'pais_iva': sellervat,
            'message': 'VAT details updated successfully.'
        })

    def parse_date(self, date_string):
        """Convierte una fecha en string a objeto datetime.date o devuelve None si está vacía."""
        if not date_string:
            return None
        try:
            parsed_date = datetime.strptime(date_string, '%Y-%m-%d').date()
            debug and print(f"parse_date({date_string}) => {parsed_date}")
            return parsed_date
        except ValueError:
            debug and print(f"parse_date() ERROR: Formato incorrecto ({date_string})")
            return None

    def add_date_error(self, errors, field, message):
        """Añade un error al diccionario de fechas y lo muestra en debug."""
        errors[field].append(message)
        debug and print(f"Error agregado: {field} -> {message}")
