from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models.signals import post_save, post_delete
from django.utils.translation import gettext_lazy as _
from django.dispatch import receiver
from django.urls import reverse
from muaytax.signals import disable_for_load_data

from django.db.models import Q
from datetime import timedelta
from django.utils import timezone


class Partner(models.Model):
    # id -> AutoGen

    name = models.CharField(
        max_length=100,
        verbose_name="Nombre",
    )

    last_name = models.CharField(
        max_length=100,
        verbose_name="Apellidos",
    )

    partner_type = models.ForeignKey(
        "dictionaries.PartnerType",
        on_delete=models.PROTECT,
        verbose_name="Tipo de Socio",
        default="natural",
    )

    # nif de representante legal
    legal_representative = models.CharField(
        blank=True,
        null=True,
        max_length=50,
        verbose_name="NIF de Representante",
    )

    id_type = models.ForeignKey(
        "dictionaries.IdentifierType",
        on_delete=models.PROTECT,
        verbose_name="Tipo de Identificación",
        help_text="Tipo de Identificación",
        default="dni",
    )

    id_number = models.CharField(
        max_length=50,
        verbose_name="ID Fiscal / DNI / Pasaporte",
        help_text="ID Fiscal / DNI / Pasaporte",
        unique=True,
    )

    id_passport = models.CharField(
        max_length=50,
        blank=True,
        default="",
        verbose_name="Pasaporte",
        help_text="Número de Pasaporte si aplica",
        # unique=True
    )

    id_expedition_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de Expedición",
        help_text="Fecha de Expedición",
    )

    id_expiration_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de Expiración/Caducidad",
    )

    shares_number = models.FloatField(
        default=0,
        blank=True,
        null=True,
        verbose_name="Número de Participaciones",
    )

    shares_percentage = models.FloatField(
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name="Porcentaje Participación",
        help_text='Porcentaje de participación que tiene el miembro en la empresa',
        blank=True,
        null=True,
    )

    shares_percentage_m184 = models.FloatField(
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        blank=True,
        null=True,
        verbose_name="Porcentaje Participación en el modelo 184",
    )

    activity_sector = models.CharField(
        blank=True,
        null=True,
        max_length=100,
        verbose_name="Sector de Actividad (Persona Jurídica)",
        help_text="Solo aplica a Persona Jurídica",
    )

    usual_activity = models.CharField(
        blank=True,
        null=True,
        max_length=100,
        verbose_name="Profesión habitual (Persona Física)",
        help_text="Solo aplica a Persona Física",
    )

    birthday = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha nacimiento (Persona Física)",
        help_text="Solo aplica a Persona Física",
    )

    birth_country = models.ForeignKey(
        "dictionaries.Country",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        related_name="partner_birth_country",
        verbose_name="País de Nacimiento",
    )

    fiscal_province = models.ForeignKey(
        "dictionaries.ProvinceCode",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        related_name="partner_fiscal_province",
        verbose_name="Provincia de residencia fiscal",
    )

    address = models.ForeignKey(
        "address.Address",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="partner_address",
        verbose_name="Dirección Socio",
    )

    days_member = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(365)],
        default=0,
        blank=True,
        null=True,
        verbose_name="Días que ha sido socio",
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="partner_seller",
        verbose_name="Vendedor",
    )

    start_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Fecha de Alta"),
    )

    end_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Fecha de Baja"),
    )

    sl_flat_rate_inss_start_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de alta Tarifa Plana INSS (SL)",
        help_text="Fecha de inicio de la tarifa plana aplicable al socio de la SL."
    )

    sl_flat_rate_inss_end_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de fin Tarifa Plana INSS (SL)",
        help_text="Fecha de finalización de la tarifa plana aplicable al socio de la SL."
    )

    sl_flat_rate_inss_next_expiration_send_email = models.BooleanField(
        default=False,
        verbose_name="Correo de expiración Tarifa Plana enviado",
        help_text="Indica si ya se ha enviado el correo de proxima expiración de tarifa plana al socio."
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    @property
    def get_full_name(self):
        return f"{self.name} {self.last_name}"

    @property
    def sl_flat_rate_inss_warning_days(self):
        if self.sl_flat_rate_inss_end_date:
            return (self.sl_flat_rate_inss_end_date - timezone.now().date()).days
        return None

    @property
    def sl_flat_rate_inss_status(self):
        days_remaining = self.sl_flat_rate_inss_warning_days
        if days_remaining is None:
            return "no_info"
        elif days_remaining <= 0:
            return "expired"
        elif days_remaining <= 30:
            return "critical"
        return "ok"

    def save(self, *args, **kwargs):
        """📌 Reglas para almacenar DNI o Pasaporte correctamente"""
        if self.id_type.code == "passport":
            # Si es pasaporte, copiarlo a ambos campos
            if not self.id_passport and self.id_number:
                self.id_passport = self.id_number

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Socio"
        verbose_name_plural = "Socios"
        constraints = [
            models.UniqueConstraint(
                fields=["id_passport"],
                name="unique_id_passport_not_empty",
                condition=~Q(id_passport="")  # 🔹 Solo aplica si id_passport tiene un valor
            )
        ]
        
    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse("app_providers:detail", kwargs={"pk": self.pk})
    
    def is_included_in_m184(self, year):
        if self.start_date and self.start_date.year <= int(year) and (self.end_date is None or self.end_date.year >= int(year)):
            return True
        return False

# @receiver(post_save, sender=Partner)
# @disable_for_load_data
# def after_sellervat_save(sender, instance, created, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)

# @receiver(post_delete, sender=Partner)
# @disable_for_load_data
# def after_sellervat_delete(sender, instance, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)