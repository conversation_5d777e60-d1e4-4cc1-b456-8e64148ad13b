import json
import os
import random
import string
import io
from datetime import datetime, date
from urllib.parse import urlencode
from datetime import timedelta
import uuid


from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.core.mail import EmailMultiAlternatives
from django.core.serializers import serialize
from django.db import IntegrityError, transaction
from django.db.models import Q, Case, When, Value, CharField
from django.http import JsonResponse, HttpResponse, HttpResponseRedirect, HttpResponseBadRequest
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, UpdateView, <PERSON>reate<PERSON>iew, DeleteView, View
from pypdf import Pdf<PERSON>eader, PdfWriter
from django_datatables_view.base_datatable_view import BaseDatatableView

from muaytax.app_bookings.models.bookings import Bookings
from muaytax.app_bookings.models.manage_model import ManageModel
from muaytax.app_documents.forms.presentd_models_forced import PresentedModelForcedCreateForm
from muaytax.app_documents.forms.presented_models import (
    PresentedModelChangeForm,
    PresentedModelChangeFormSeller,
    PresentedModelDeleteForm,
    PresentedModelCreateForm
)
from muaytax.app_documents.models.presented_model import PresentedModel
from muaytax.app_documents.models.presented_model_forced import PresentedModelForced
from muaytax.app_documents.utils.utils import (
    generate_external_excel_111, 
    still_in_limit_date_direct_debit,
    present_to_AEAT,
    draft_valitation,
    create_periodic_task_instance,
    set_execution_time
)
from muaytax.app_documents.tasks import process_presentedmodel_with_ocr

from muaytax.app_fax.utils import retry_send_fax
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.calendar_api.bookings import add_to_CAL, update_CAL
from muaytax.dictionaries.models.booking_subjects import BookingSubject
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.model import Model
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.period import Period
from muaytax.dictionaries.models.presented_model_results import PresentedModelResults
from muaytax.email_notifications.booking_mails import (
    send_mail_disagree_not_booking_appointment,
    send_mail_appointment_disagreed_model_manager,
    send_mail_appointment_disagreed_model_seller
) 
from muaytax.email_notifications.model_notifications import send_email_model_111_to_external_manager
from muaytax.email_notifications.utils import (
    get_mail_signature, 
    get_mail_signature_usa, 
    get_mail_signature_amzvat
)
from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission

from muaytax.email_notifications.document_notifications import send_email_hmrc_model

from muaytax.utils.txt_models import *
from muaytax.utils.env_resources import logo_url_head_muaytax

from django_celery_beat.models import PeriodicTask, ClockedSchedule

User = get_user_model()


class PresentedModelListView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = PresentedModel
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def setTemplateByRole(self):
        self.template_name_suffix = "_list_seller"

        user_role = self.request.user.role
        if user_role == "manager":
            self.template_name_suffix = "_list_manager"

        return self.template_name

    def get(self, request, *args, **kwargs):
        self.setTemplateByRole()
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        models = PresentedModel.objects.filter(seller_id=seller.id)
        return models

    def get_context_data(self, **kwargs):
        show = "all"
        url = self.request.path

        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        sellervat = SellerVat.objects.filter(seller_id=seller.id).annotate(
            order_priority=Case(
                When(vat_country__iso_code='ES', then=Value(1)),
                default=Value(2),
                output_field=CharField(),
            )
        ).order_by("order_priority", "vat_country__name")

        # check if theres a vat object with country GB in the sellervat list
        vrn = None
        gb_vat = sellervat.filter(vat_country__iso_code='GB').first()
        if gb_vat and gb_vat.vat_number:
            vrn = gb_vat.vat_number.replace(" ", "").removeprefix("GB") 

        models = PresentedModel.objects.filter(seller_id=seller.id)
        countries = Country.objects.all().order_by("name")
        models_status = ModelStatus.objects.all()
        period = Period.objects.all().order_by("order")
        modelscode = Model.objects.filter(code__startswith='ES')

        url_to_show_map = {
            reverse("app_documents:presented_model_list", args=[seller.shortname]): "all",
            reverse("app_documents:presented_model_vat_list", args=[seller.shortname]): "vat",
            reverse("app_documents:presented_model_pending_list", args=[seller.shortname]): "pending",
            reverse("app_documents:presented_model_enterprise_list", args=[seller.shortname]): "enterprise",
        }
        show = url_to_show_map.get(url, "all")

        if (show == "vat"):
            q1 = Q(model__code__startswith="ES-3")
            q2 = ~Q(model__code__startswith="ES-")
            models = models.filter(q1 | q2).exclude(model__code__startswith="US-")
        elif (show == "enterprise"):
            q1 = Q(model__code__startswith="ES-3")
            q2 = ~Q(model__code__startswith="ES-") & ~Q(model__code__startswith="US-")
            models = models.exclude(q1 | q2)
        elif (show == "pending"):
            # pendiente, aprobadas y no presentadas o rechazadas.
            q1 = Q(status__code="pending")
            q2 = Q(status__code="agreed")
            q3 = Q(status__code="disagreed")

            models = models.filter(q1 | q2 | q3)

        context = super().get_context_data(**kwargs)
        for obj in context['object_list']:
            obj.file.name = obj.file.name.replace("uploads/", "").replace("presented_models/", "")
            # print("obj.file.url: " + obj.get_file_url() )
            # print("obj.file.name: " + obj.file.name)

        for obj in models:
            obj.file.name = obj.file.name.replace("uploads/", "").replace("presented_models/", "")
            # obj.json_pdf = json.loads(obj.json_pdf)
            # print("obj.file.url: " + obj.get_file_url() )
            # print("obj.file.name: " + obj.file.name)

        # checks if the appointment has been created when discarding a model
        booking_created = self.request.session.pop('booking_created', False)
        updated_booking = self.request.session.pop('updated_booking', False)
        if booking_created:
            context["has_created"] = True
            context["booking_created"] = Bookings.objects.filter(seller_id=seller.id).last()
        if updated_booking:
            context["has_updated"] = True
            context["updated_booking"] = Bookings.objects.filter(pk=updated_booking).last()

        if self.request.META.get('HTTP_REFERER'):
            http_referer = "/".join([""] + self.request.META.get('HTTP_REFERER').split('/')[3:])
            model_upload = reverse("app_documents:presented_model_upload", args=[seller.shortname])

            if http_referer == model_upload or http_referer.split("/")[-2].isnumeric():
                # print("vino de model_upload o de model_view")
                context["referer"] = True

        exclude_5472_2024 = PresentedModel.objects.filter(seller_id=seller.id, model="US-5472", year="24").exists()

        context.update({
            "seller": seller,
            "sellervat": sellervat,
            "models": models,
            "show": show,
            "countries": countries,
            "models_status": models_status,
            "period": period,
            "modelscode": modelscode,
            'fax_models': ["US-BE15", "US-7004"],
            'url_model': urlManualToAEAT(),
            'vrn': vrn,
            'exclude_5472_2024': exclude_5472_2024,
        })

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class PresentedModelDetailView(LoginRequiredMixin, IsManagerRolePermission, UpdateView):
    model = PresentedModel
    form_class = PresentedModelChangeForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = "_detail_manager"

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        form.instance.seller = seller
        form.instance.comment = self.get_object().comment

        try:
            with transaction.atomic():
                response = super().form_valid(form)
        except IntegrityError:
            #  form.add_error('asin', 'Este código ASIN ya existe')
            response = self.form_invalid(form)
        return response

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        model = get_object_or_404(PresentedModel, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)
        context["seller"] = seller

        json_pdf = {}
        if (model.json_pdf):
            # if (model.json_pdf.find("'") != -1):
            #     model.json_pdf = model.json_pdf.replace("'", '"')
            json_pdf = json.loads(model.json_pdf)
        # print(json_pdf)
        context["json_pdf"] = json_pdf

        context["historial_json"] = True if model.historial_change_json else False

        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_documents:presented_model_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class JsonPdfPresentedModelDataTableView(LoginRequiredMixin, IsSellerShortnamePermission, BaseDatatableView):
    model = PresentedModel
    columns = ["key", "value", "has_change"]

    def get_data(self):
        presented_model = PresentedModel.objects.filter(id=self.kwargs["pk"]).first()

        if not presented_model:
            return []

        json_pdf = json.loads(presented_model.json_pdf) or {}
        json_pdf_backup = presented_model.json_pdf_backup or {}

        results = []
        for key, value in json_pdf.items():
            has_change = json_pdf_backup.get(key) is not None and json_pdf_backup[key] != value
            results.append({
                "key": key,
                "value": value,
                "has_change": bool(has_change)
            })
        
        return results

    def get(self, request, *args, **kwargs):
        data = self.get_data()
        return JsonResponse({
            'data': data
        })
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class UpdateJsonPdfFieldView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    """Actualiza un campo del json_pdf de un modelo presentado."""
    
    def post(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        model = get_object_or_404(PresentedModel, seller_id=seller.id, pk=self.kwargs["pk"])

        if not model.json_pdf:
            return JsonResponse({"status": "error", "message": "No existe json_pdf en el modelo."}, status=400)
        
        try:
            data = json.loads(request.body)
            key = data.get("key")
            value = data.get("value")

            if not key or value is None:
                return JsonResponse({"status": "error", "message": "Clave y valor son requeridos."}, status=400)

            json_pdf = json.loads(model.json_pdf)
            backup_json_pdf = model.json_pdf_backup or {}

            if key not in json_pdf:
                return JsonResponse({"status": "error", "message": f"La clave '{key}' no existe en el json_pdf."}, status=400)
            
            # Normalizar el valor con , como separador decimal
            if value.replace(',', '', 1).replace('.', '', 1).isdigit():
                normalized_value = value.replace('.', ',', 1)
            else:
                normalized_value = value

            with transaction.atomic():
                # Hacer backup del campo antes de actualizarlo
                if key not in backup_json_pdf and json_pdf[key] != normalized_value:
                    backup_json_pdf[key] = json_pdf[key]

                if key in backup_json_pdf and backup_json_pdf[key] == normalized_value:
                    del backup_json_pdf[key]
                
                json_pdf[key] = normalized_value

                model.json_pdf = json.dumps(json_pdf)
                model.json_pdf_backup = backup_json_pdf
                model._skip_pm_signal = True
                model.save()

            return JsonResponse({"status": "success", "message": "Campo actualizado correctamente", "data": {"key": key, "value": normalized_value}})
        
        except json.JSONDecodeError:
            return JsonResponse({"status": "error", "message": "El cuerpo de la solicitud no es un JSON válido."}, status=400)

        except Exception as e:
            return JsonResponse({"status": "error", "message": str(e)}, status=500)

class ResetJsonPdfFieldView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    """Reestablece un campo del json_pdf de un modelo presentado."""
    def post(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        model = get_object_or_404(PresentedModel, seller_id=seller.id, pk=self.kwargs["pk"])

        if not model.json_pdf:
            return JsonResponse({"status": "error", "message": "No existe json_pdf en el modelo."}, status=400)
        
        try:
            data = json.loads(request.body)
            key = data.get("key")

            if not key:
                return JsonResponse({"status": "error", "message": "Clave es requerida."}, status=400)

            json_pdf = json.loads(model.json_pdf)
            backup_json_pdf = model.json_pdf_backup or {}

            if key not in json_pdf:
                return JsonResponse({"status": "error", "message": f"La clave '{key}' no existe en el json_pdf."}, status=400)
            
            with transaction.atomic():
                if key in backup_json_pdf:
                    json_pdf[key] = backup_json_pdf[key]
                    del backup_json_pdf[key]

                    model.json_pdf = json.dumps(json_pdf)
                    model.json_pdf_backup = backup_json_pdf
                    model._skip_pm_signal = True
                    model.save()

            return JsonResponse({"status": "success", "message": "Campo reestablecido correctamente", "data": {"key": key, "value": json_pdf[key]}})
        
        except json.JSONDecodeError:
            return JsonResponse({"status": "error", "message": "El cuerpo de la solicitud no es un JSON válido."}, status=400)

        except Exception as e:
            return JsonResponse({"status": "error", "message": str(e)}, status=500)

class PresentedModelDeleteView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission),
                               SuccessMessageMixin, DeleteView):
    model = PresentedModel
    form_class = PresentedModelDeleteForm
    template_name_suffix = "_delete"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        model = get_object_or_404(PresentedModel, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        modelObj = self.get_object()
        action = self.request.POST.get('action')
        print(f"action: {action}")
        r = None
        if action == "regenerate":
            model = self.request.POST.get('model')
            for prefix in ['ES-', 'IT-', 'US-', 'GB-', 'NL-']:
                model = model.replace(prefix, "").replace(" ", "")
            # period = self.request.POST.get('period') if self.request.POST.get('period') else 'all'
            # year = self.request.POST.get('year') if self.request.POST.get('year') else 'all'
            period = modelObj.period.code
            year = modelObj.year
            query_params = urlencode({"period": period, "year": year})
            r = reverse(
                "app_sellers:model_update",
                args=[self.object.seller.shortname, model],
            )
            r += f"?{query_params}"
        else:
            r = reverse(
                "app_documents:presented_model_list",
                args=[self.object.seller.shortname],
            )
        return r

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class HistorialPresentedModelDataTableView(LoginRequiredMixin, IsSellerShortnamePermission, BaseDatatableView):

    def get (self, request, *args, **kwargs):
        model = get_object_or_404(PresentedModel, pk=self.kwargs["pk"])
        data = {}
        if model.historial_change_json:
            data = model.historial_change_json
        
        # Obtener verbose_name de cada campo en PresentedModel
        field_verbose_map = {field.name: str(field.verbose_name) for field in model._meta.fields}

        def replace_keys_with_verbose(obj):
            """ Función recursiva para reemplazar claves con verbose_name """
            if isinstance(obj, dict):
                return {field_verbose_map.get(k, k): replace_keys_with_verbose(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [replace_keys_with_verbose(item) for item in obj]
            return obj

        data = replace_keys_with_verbose(data)

        draw = int(request.GET.get('draw', 1))
        response = {
            "draw": draw,
            "data": data
        }
    
        return JsonResponse(response, safe=False)

class PresentedModelUpdateView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):
    model = PresentedModel
    form_class = PresentedModelChangeForm
    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class PresentedModelUploadView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = "_upload"

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        models = PresentedModel.objects.filter(seller_id=seller.id)
        return models

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)
        for obj in context['object_list']:
            obj.file.name = obj.file.name.replace("uploads/", "").replace("presented_models/", "")
        context["seller"] = seller
        context["seller_vat"] = SellerVat.objects.all().filter(seller_id=seller.id)
        context["countries"] = Country.objects.all().order_by("name")
        context["model"] = Model.objects.all().order_by("description")
        context["period"] = Period.objects.all().order_by("order")
        context["status"] = ModelStatus.objects.all().order_by("order")
        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "seller_vat": serialize("json", context["seller_vat"]),
            "countries": serialize("json", context["countries"]),
            "model": serialize("json", context["model"]),
            "period": serialize("json", context["period"]),
            "status": serialize("json", context["status"]),
        }
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class PresentedModelCreateView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    form_class = PresentedModelCreateForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    allowed_file_extensions = [".pdf", ".jpg", ".jpeg", ".png", ".xml", ".zip", ".rar"]
    error_messages = {
        "invalid_file_extension": f"La extensión de los archivos permitidos son: ${allowed_file_extensions}",
        "invalid_presentation_pdf": "Solo se permiten archivos PDF para presentaciones sustitutivas, complementarias o generales.",
        "file_attached": "Documento adjunto cargado correctamente",
        "form_error": "Error in PresentedModelCreateView",
    }

    def get_seller(self) -> Seller:
        if not hasattr(self, "_seller"):
            self._seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return self._seller

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        kwargs["data"]["seller"] = self.get_seller().id
        return kwargs

    def validate_file(self, file, presentation_type):
        """Validate the uploaded file."""
        if not file:
            return False, self.error_messages["invalid_file_extension"]

        if presentation_type in ['substitutive-presentation', 'complementary-presentation', 'general-presentation']:
            if not file.name.lower().endswith('.pdf'):
                return False, self.error_messages["invalid_presentation_pdf"]

        if not any(file.name.lower().endswith(ext) for ext in self.allowed_file_extensions):
            return False, self.error_messages["invalid_file_extension"]

        return True, None
    
    def get_existing_pm(self, form_data):
        """Fetch the existing PresentedModel."""
        return PresentedModel.objects.filter(
            Q(status__code__in=["pending", "agreed", "presented"]),
            seller=self.get_seller(),
            model_id=form_data.get("model"),
            period_id=form_data.get("period"),
            year=form_data.get("year"),
            country=form_data.get("country")
        ).first()

    def form_invalid(self, form):
        if (form and form.errors):
            print("form_invalid: {}".format(form.errors))
        else:
            print("form_invalid: {}".format(form))
            form.errors = "Error in PresentedModelCreateView"
        return HttpResponseBadRequest(form.errors["file"])
    
    def form_valid(self, form):
        presentation_type = form.cleaned_data.get("presentation_type")
        process_with_ocr = form.cleaned_data.get("process_with_ocr")
        file = form.cleaned_data.get("file")

        is_valid, error_message = self.validate_file(file, presentation_type)
        if not is_valid:
            return JsonResponse({"status": "error", "message": error_message}, status=400)
        
        existing_pm = self.get_existing_pm(form.cleaned_data)
        if existing_pm:
            self.update_model(form.cleaned_data, existing_pm, presentation_type, process_with_ocr)
            return JsonResponse({"status": "success", "message": "Modelo presentado correctamente"})
        else:
            form.instance._skip_pm_signal = True # este campo se usa para que la señal se pare de ejecutar
            r = super().form_valid(form)
            new_instance = self.create_model(presentation_type, process_with_ocr)
            if (new_instance and new_instance['has_error'] == True):
                print(f"\r\033[91mError en el método de crear nuevo presented model\033\r[0m")
                return HttpResponseBadRequest(new_instance['error_message'])
            
            return r

    def get_success_url(self) -> str:
        return reverse(
            "app_documents:presented_model_pending_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseBadRequest("No Permission")

    def update_model(self, cleaned_form, existing_pm: PresentedModel, presentation_type: str, process_with_ocr: bool):
        """Uploads a Presentatios by updating existing model with new file and status."""
        today = timezone.now()
        is_contracted = existing_pm.seller.contracted_accounting
        legal_entity = existing_pm.seller.legal_entity
        cleaned_file = cleaned_form.get("file")
        
        if existing_pm.model.code in ['IT-LIPE', 'IT-VATANNUALE']:
            existing_pdf_path  = f"muaytax{existing_pm.get_file_url()}"
            existing_pdf = PdfReader(existing_pdf_path)
            json_pdf = json.loads(existing_pm.json_pdf)

            new_pdf = PdfReader(io.BytesIO(cleaned_form.get("file").read()))

            merged_pdf_writer = PdfWriter()

            for page in new_pdf.pages:
                merged_pdf_writer.add_page(page)

            for page in existing_pdf.pages:
                merged_pdf_writer.add_page(page)
                merged_pdf_writer.update_page_form_field_values(merged_pdf_writer.pages[-1], json_pdf)

            new_pdf_path = existing_pdf_path
            with open(new_pdf_path, "wb") as output_stream:
                merged_pdf_writer.write(output_stream)
            existing_pm.file.name = f'uploads/presented_models/{existing_pm.file.name.split("/")[-1]}' # OJO: esto se modifica porque hay errores en los nombres y las rutas de los pdf en presented_models            
            print(f"existing_pm.file.name 11111: {existing_pm.file.name}")
        else:
            print("Actualizando un modelo que no es italiano")
            random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=7))
            file_name = "{}_{}_{}_{}_{}_{}.pdf".format(
                existing_pm.seller.shortname,
                existing_pm.country.name,
                existing_pm.model.code,
                existing_pm.period.code,
                existing_pm.year,
                random_string
            )
            if presentation_type == "general-presentation":
                existing_pm.file = cleaned_file
                existing_pm.file.name = file_name
            elif presentation_type == "substitutive-presentation":
                existing_pm.substitute_presentation = cleaned_file
                existing_pm.substitute_presentation.name = file_name
            elif presentation_type == "complementary-presentation":
                existing_pm.complementary_presentation = cleaned_file
                existing_pm.complementary_presentation.name = file_name
            elif presentation_type == "resolution-presentation":
                existing_pm.resolution_presentation = cleaned_file
                existing_pm.resolution_presentation.name = file_name
            elif presentation_type == "attach_doc":
                existing_pm.attach_files = cleaned_file
                existing_pm.save()
                return JsonResponse({"status": "success", "message": "Documento adjunto cargado correctamente"})
            elif presentation_type == "receipt_file":
                existing_pm.receipt_file = cleaned_file
                existing_pm.save()
                return JsonResponse({"status": "success", "message": "Documento adjunto cargado correctamente"})

        existing_pm.status = cleaned_form.get("status")
        existing_pm.presentation_date = today

        if existing_pm.country.iso_code == 'ES' and existing_pm.status.code == 'presented':
            if existing_pm.model.code != 'ES-303':
                existing_pm.is_paid = True
                self._send_email_model_presented(existing_pm.seller, existing_pm)
            elif existing_pm.model.code == 'ES-303' and is_contracted == True:
                existing_pm.is_paid = True
                self._send_email_model_presented(existing_pm.seller, existing_pm)
            elif existing_pm.model.code == 'ES-303' and is_contracted != True and existing_pm.result.code == 'deposit' and existing_pm.amount != None and existing_pm.amount != 0:
                if (legal_entity is not None and (legal_entity == 'sl' or legal_entity == 'self-employed')):
                    existing_pm.is_paid = True
                    self._send_email_model_presented(existing_pm.seller, existing_pm)
                else:
                    self._send_email_presentedModel303(existing_pm.seller, existing_pm)

        existing_pm.save()

        # enviar modelo a ser procesado con OCR si es que el usuario lo solicita
        if process_with_ocr:
            print(f"\033[93mSe ha enviado el modelo {existing_pm.pk} al OCR para procesarse\033[0m")
            process_presentedmodel_with_ocr.delay(existing_pm.pk)
    
    def create_model(self, presentation_type: str, process_with_ocr: bool = False):
        print(f'Creando un nuevo modelo con nuevo flujo. Tipo de presentación: {presentation_type}')
        today = timezone.now()
        seller = self.object.seller
        errors = {"has_error": False, "error_message": ""}
        model = PresentedModel.objects.get(pk=self.object.pk)
        is_contracted = seller.contracted_accounting

        invalid_presentation_types = {"substitutive-presentation", "complementary-presentation", "resolution-presentation"}

        if presentation_type in invalid_presentation_types:
            model.delete()
            return {"has_error": True, "error_message": "invalid_presentation_subs_comp_reso"}
        
        try:
            random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=7))
            file_name = f"{seller.shortname}_{model.country.name}_{model.model.code}_{model.period.code}_{model.year}_{random_string}.pdf"

            old_path = model.file.path
            new_path = os.path.join(os.path.dirname(old_path), file_name)
            os.rename(old_path, new_path)
            model.file.name = f"uploads/presented_models/{file_name}"

            if model.status.code == 'presented':
                model.presentation_date = today

            json_fields = {}
            try:
                filepath = f'/app/muaytax{model.get_file_url()}' # OJO: esto se modifica porque hay errores en los nombres y las rutas de los pdf en presented_models
                reader = PdfReader(filepath)
                # Accedemos a los metadatos si existen
                if "/JsonValues" in reader.metadata:
                    json_metadata = reader.metadata["/JsonValues"]
                    json_fields = json.loads(json_metadata)
                else:
                    for page in reader.pages:
                        if "/Annots" not in page:
                            continue
                        for annot in page["/Annots"]:
                            obj = reader.get_object(annot)
                            key = obj.get("/T", None)
                            value = obj.get("/V", "")
                            if (key != None and value != None):
                                json_fields[key] = value

                model.json_pdf = json.dumps(json_fields)

                if json_fields:
                    model_code = model.model.code
                    if model_code == 'ES-111' and 'casilla_30' in json_fields:
                        amount_pdf = float(json_fields['casilla_30'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'ES-115' and 'page1_field15' in json_fields:
                        amount_pdf = float(json_fields['page1_field15'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'ES-130' and 'campo_19' in json_fields:
                        amount_pdf = float(json_fields['campo_19'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf
                    
                    elif model_code == 'ES-202':
                        amount_pdf = float(json_fields['pag1_total'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'ES-309' and 'casilla24' in json_fields:
                        amount_pdf = float(json_fields['casilla24'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'ES-303' and 'campo_71' in json_fields:
                        amount_pdf = float(json_fields['campo_71'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'ES-369' and 'doc_6_resultado_total' in json_fields:
                        amount_pdf = float(json_fields['doc_6_resultado_total'].replace(",", "."))
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf

                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1

                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'IT-LIPE' and ('VP14-1' in json_fields and 'VP14-2' in json_fields):
                        amount_pdf_1 = json_fields['VP14-1'].replace(",", ".")
                        amount_pdf_2 = json_fields['VP14-2'].replace(",", ".")
                        if model.period.code == 'Q4':
                            amount_pdf_1 = json_fields['VP14_1_hide'].replace(",", ".")
                            amount_pdf_2 = json_fields['VP14_2_hide'].replace(",", ".")

                        if amount_pdf_1 == '':
                            amount_pdf_1 = None
                        else:
                            amount_pdf_1 = float(amount_pdf_1)

                        if amount_pdf_2 == '':
                            amount_pdf_2 = None
                        else:
                            amount_pdf_2 = float(amount_pdf_2)

                        if amount_pdf_1 is not None and amount_pdf_1 != 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf_1
                        elif amount_pdf_2 is not None and amount_pdf_2 != 0:
                            model.result = PresentedModelResults.objects.get(code='credit')
                            model.amount = amount_pdf_2
                        elif amount_pdf_1 is not None and amount_pdf_1 == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf_1

                    elif model_code == 'IT-ACCONTO' and 'total' in json_fields:
                        amount_pdf = float(json_fields['total'].replace(",", "."))                        
                        model.status = ModelStatus.objects.get(code='pending')
                        if amount_pdf > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf
                        elif amount_pdf < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf * -1
                        elif amount_pdf == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf

                    elif model_code == 'IT-VATANNUALE':
                        amount_pdf_1 = float(json_fields.get('page12_VX1', ''))
                        amount_pdf_2 = float(json_fields.get('page12_VX2_1', ''))
                        if amount_pdf_1 != '' and amount_pdf_1 != 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_pdf_1
                        elif amount_pdf_2 != '' and amount_pdf_2 != 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_pdf_2

                        elif (amount_pdf_1 == '' and amount_pdf_2 == '') or (amount_pdf_1 == 0 and amount_pdf_2 == 0):
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_pdf_1
                            model.status = ModelStatus.objects.get(code='agreed')

                    elif model_code == 'US-7004':
                        model.result = PresentedModelResults.objects.get(code='informative')
                        model.status = ModelStatus.objects.get(code='agreed')
                        path = f'/app/muaytax{model.get_file_url()}'
                        from_number = settings.FAXPLUS_SENDING_NUMBER
                        to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER_US_7004]
                        email = seller.user.email
                        response = retry_send_fax(path, from_number, to_number_list, email)
                        if 'error' not in response:
                            fax_id = list(response['ids'].values())[0]
                            model.fax_destination_id = fax_id
                    
                    elif model_code == 'US-BE15':
                        model.result = PresentedModelResults.objects.get(code='informative')
                        model.status = ModelStatus.objects.get(code='agreed')
                        path = f'/app/muaytax{model.get_file_url()}'
                        from_number = settings.FAXPLUS_SENDING_NUMBER
                        to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER_US_BE15]
                        email = seller.user.email
                        response = retry_send_fax(path, from_number, to_number_list, email)
                        if 'error' not in response:
                            fax_id = list(response['ids'].values())[0]
                            model.fax_destination_id = fax_id
                    
                    elif model.model.code == 'US-5472':
                        print("enviando fax")
                        model.m5472_signed  = model.file
                        model.status = ModelStatus.objects.get(code='agreed')
                        path = f'/app/muaytax{model.get_file_url()}'
                        from_number = settings.FAXPLUS_SENDING_NUMBER
                        to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER_US_5472]
                        email = seller.user.email
                        # TODO: DESCOMENTAR PARA PROBAR EL ENVÍO DE FAX
                        # response = retry_send_fax(path, from_number, to_number_list, email)
                        # if 'error' not in response:
                        #     fax_id = list(response['ids'].values())[0]
                        #     model.fax_destination_id = fax_id

                    elif model_code == 'GB-VAT-PROOF' and 'BOX_5' in json_fields and json_fields.get('BOX_5', '') != '':
                        amount_box_5 = float(json_fields.get('BOX_5', '').replace(",", "."))
                        if amount_box_5 > 0:
                            model.result = PresentedModelResults.objects.get(code='deposit')
                            model.amount = amount_box_5
                        elif amount_box_5 < 0:
                            model.result = PresentedModelResults.objects.get(code='compensate')
                            model.amount = amount_box_5 * -1
                        elif amount_box_5 == 0:
                            model.result = PresentedModelResults.objects.get(code='result0')
                            model.amount = amount_box_5

                        # ENVÍO DIRECTO DEL CORREO HMRC
                        try:
                            seller_vat_gb = SellerVat.objects.filter(seller=seller, vat_country__iso_code="GB").first()
                            print(f"Buscando SellerVat para seller={seller.shortname} (ISO GB)")

                            if seller_vat_gb:
                                period_code = self.request.POST.get("period")
                                year = self.request.POST.get("year")
                                period_label = self.request.POST.get("period_label")

                                if not seller_vat_gb.payment_method_hmrc:
                                    print("No se puede enviar el modelo: el método de pago HMRC no está definido.")
                                    return JsonResponse({
                                        "status": "error",
                                        "message": "No se ha definido un método de pago HMRC. Por favor, selecciona uno antes de continuar."
                                    }, status=400)

                                print(f"SellerVat encontrado: vat_number={seller_vat_gb.vat_number}, payment_method={seller_vat_gb.payment_method_hmrc}")
                                print(f"Enviando correo con:")
                                print(f"   ➤ box_5_value = {model.amount}")
                                print(f"   ➤ period_label = {period_label}")
                                print(f"   ➤ referencia = {model.pk} - {model.seller.shortname} - {model.model.code} - {model.period.code} - {model.year}")

                                send_email_hmrc_model(
                                    seller_vat=seller_vat_gb,
                                    box_5_value=model.amount,
                                    period_label=period_label,
                                    model=model
                                )
                                print("Correo HMRC enviado con éxito")
                            else:
                                print("No se encontró SellerVat para el país GB")

                        except Exception as e:
                            print(f"Error al enviar email HMRC para modelo {model.pk} - {e}")

                    # set informtive and agreed status
                    elif model_code in ['ES-180', 'ES-190', 'ES-390', 'ES-349', 'ES-347', 'ES-184']:
                        model.result = PresentedModelResults.objects.get(code='informative')
                        model.status = ModelStatus.objects.get(code='agreed')

            except Exception as e:
                print(f"Error al leer los campos editables del PDF: {e}")

            notEmail = self.request.POST.get('modelNotEmail')
            if notEmail != 'notEmail':
                if model.status.code == "pending":
                    if model.model.code == 'ES-303' and model.period.code == "Q4" and model.result.code != 'result0':
                        self._send_email_303Q4_pending(seller, model)
                    elif model.model.code == 'IT-VATANNUALE':
                        if model.result.code == 'compensate' or model.result.code == 'deposit':
                            self._send_email_vatanualle(seller, model)
                    elif model.model.code == 'GB-VAT-PROOF':
                        clocked_schedule, _ = ClockedSchedule.objects.get_or_create(
                            clocked_time=timezone.now() + timedelta(days=1),
                        )
                        PeriodicTask.objects.create(
                            name=f"Auto agree modelo {model.pk} - {model.seller.shortname} - {model.model.code} - {model.period.code} - {model.year} ",
                            task='muaytax.app_documents.tasks.presentedmodels.auto_agree_model',
                            clocked=clocked_schedule,
                            one_off=True,
                            args=[model.pk],
                        )
                    else:
                        self._send_email(seller, model)
                
                # Si se envía el correo | fecha actual
                model.email_sent_at = timezone.now()
            else:
                # Si no se envía el correo |  null
                model.email_sent_at = None

            #? aquí pueden llegar modelos generados por la app (con json_pdf) o modelos subidos por el cargador, los cuales no tienen json_pdf con lo cual ciertos datos estarán vacíos
            if model.country.iso_code == 'ES' and model.status.code == 'presented':
                if model.model.code != 'ES-303':
                    model.is_paid = True
                    self._send_email_model_presented(seller, model)
                elif model.model.code == 'ES-303' and is_contracted == True:
                    model.is_paid = True
                    self._send_email_model_presented(seller, model)
                elif model.model.code == 'ES-303' and is_contracted != True and model.result and model.result.code == 'deposit' and model.amount != None and model.amount != 0:
                    self._send_email_presentedModel303(seller, model)

            # Guarda el modelo solo una vez 
            model.save()

            if process_with_ocr:
                print(f"\033[93mSe ha enviado el modelo {model.pk} al OCR para procesarse\033[0m")
                process_presentedmodel_with_ocr.delay(model.pk)
                
        except Exception as err:
            print("\rError in create_model: {}\nNo se ha creado instancia".format(repr(err)))
            model.delete()
            errors = {"has_error": True, "error_message": repr(err)}

        return errors

    def _send_email(self, seller, model):

        message= ''
        user = seller.user
        if model.model.country == 'ES':
            signature_info = get_mail_signature(seller)
            model_type = model.model.code.replace('ES-', '')
            message = render_to_string("emails/model_pending_revision.html", {
                'user': user,
                'model': model,
                'seller': seller,
                'signature_info': signature_info,
                'logo_head_muaytax': logo_url_head_muaytax()
            })

        elif model.model.country == 'IT':
            signature_info = get_mail_signature_amzvat()
            model_type = model.model.code.replace('IT-', '')
            
            if model_type == 'ACCONTO':
                json_dict = json.loads(model.json_pdf)
                vp14 = json_dict['vp14']
                total = json_dict['total']
                message = render_to_string("emails/acconto_it.html", {
                    'user': user,
                    'model': model,
                    'seller': seller,
                    'vp14': vp14,
                    'total': total,
                    'signature_info': signature_info,
                    'logo_head_muaytax': logo_url_head_muaytax()
                })

            elif model.model.code == 'IT-LIPE':
                if model.period.code == "Q1":
                    first_month = 1
                    latest_month = 3
                elif model.period.code == "Q2":
                    first_month = 4
                    latest_month = 6
                elif model.period.code == "Q3":
                    first_month = 7
                    latest_month = 9
                elif model.period.code == "Q4":
                    first_month = 10
                    latest_month = 12

                invoice = Invoice.objects.filter(
                    seller=seller,
                    accounting_date__year=model.year,
                    accounting_date__month__gte=first_month,
                    accounting_date__month__lte=latest_month,
                    tax_country='IT',
                    status='revised',
                ).exclude(
                    Q(transaction_type='oss') | Q(transaction_type='oss-refund')
                ).count()

                message = render_to_string("emails/model_IT_pending_revision.html", {
                    'user': user,
                    'model': model,
                    'seller': seller,
                    'invoice': invoice,
                    'signature_info': signature_info,
                    'logo_head_muaytax': logo_url_head_muaytax()
                })

        elif model.model.country == 'US':
            signature_info = get_mail_signature_usa()
            model_type = model.model.code.replace('US-', '')
            message = render_to_string("emails/modelos/sent_to_revision/model_5472_1120.html", {
                'user': user,
                'model': model,
                'seller': seller,
                'signature_info': signature_info,
                'logo_head_muaytax': logo_url_head_muaytax()
            })

        elif model.model.country == 'GB':
            signature_info = get_mail_signature_amzvat()
            model_type = model.model.code.replace('GB-', '')
            message = render_to_string("emails/model_pending_revision.html", {
                'user': user,
                'model': model,
                'seller': seller,
                'signature_info': signature_info,
                'logo_head_muaytax': logo_url_head_muaytax()
            })

        if model_type == 'ACCONTO':
            subject = f'MUAYTAX - Acconto IVA Italia ha sido cargado en la APP'
            text_content = f'Acconto IVA Italia ha sido cargado en la APP. Se requiere revisión'
        else:
            subject = f'MUAYTAX - Modelo {model_type} pendiente de aprobar'
            text_content = f'Tiene un modelo {model_type} pendiente de revisión'

        from_email = [signature_info['email']]
        to_email = [user.email]
        reply_to = [signature_info['email']]

        html_content = message

        model_email_notification = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to)

        model_email_notification.attach_alternative(html_content, "text/html")

        model_email_notification.send()

    def _send_email_vatanualle(self, seller, model):
        model_type = model.model.code.replace('IT-', '')
        user = seller.user

        if model_type == 'VATANNUALE':
            message = render_to_string("emails/anualle_it.html", {
                'user': user,
                'model': model,
                'seller': seller,
                'logo_head_muaytax': logo_url_head_muaytax()
            })

        subject = f'MUAYTAX - Modelo annuale Italia pendiente de aprobar'

        from_email = '<EMAIL>'
        to_email = [user.email]
        reply_to = [user.email]

        html_content = message

        text_content = f'El borrador del Modelo Annuale (Italia) ha sido cargado y está pendiente de aprobar'


        email_anualle = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to)

        email_anualle.attach_alternative(html_content, "text/html")

        email_anualle.send()

    def _send_email_presentedModel303(self, seller, model):
        print('Método send email model 303')
        user = seller.user
        vat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()

        if vat is not None:
            seller_vat = str(vat.vat_number).replace('ES', '')
        else:
            seller_vat = str(seller.nif_registration).replace('ES', '')

        message = render_to_string("emails/payment_model303.html", {
            'user': user,
            'model': model,
            'seller': seller,
            'seller_vat': seller_vat,
            'logo_head_muaytax': logo_url_head_muaytax()
        })
        subject = 'MUAYTAX - ACCIÓN REQUERIDA - Pago Modelo 303'
        from_email = '<EMAIL>'
        to_email = [user.email]
        reply_to = [user.email]

        html_content = message

        text_content = 'ACCIÓN REQUERIDA - Pago Modelo 303'

        email_discard = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to)

        email_discard.attach_alternative(html_content, "text/html")

        email_discard.send()

    def _send_email_model_presented(self, seller, model):
        print('Método send email model presented')
        user = seller.user
        message = render_to_string("emails/model_presented.html", {
            'user': user,
            'model': model,
            'seller': seller,
            'logo_head_muaytax': logo_url_head_muaytax()
        })
        subject = 'MUAYTAX - Modelo presentado y cargado en la APP'
        from_email = '<EMAIL>'
        to_email = [user.email]
        reply_to = [user.email]

        html_content = message

        text_content = 'Tiene un modelo presentado y cargado en la APP'

        email_discard = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to)

        email_discard.attach_alternative(html_content, "text/html")

        email_discard.send()

    def _send_email_303Q4_pending(self, seller, model):
        print('_send_email 3034Q')
        model_type = model.model.code.replace('ES-', '')
        user = seller.user
        message = render_to_string("emails/modelos/model_303Q4_pending_revision.html", {
            'user': user,
            'model': model,
            'seller': seller,
            'logo_head_muaytax': logo_url_head_muaytax()
        })
        subject = f'MUAYTAX - Modelo {model_type} pendiente de aprobar'
        from_email = '<EMAIL>'
        to_email = [user.email]
        reply_to = [user.email]

        html_content = message
        text_content = f'Tiene un modelo {model_type} pendiente de revisión'

        email_discard = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to)

        email_discard.attach_alternative(html_content, "text/html")

        email_discard.send()

class PresentedModelForcedCreateView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    form_class = PresentedModelForcedCreateForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_form_kwargs(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        kwargs["data"]["seller"] = seller.id
        return kwargs

    def form_invalid(self, form):
        if (form and form.errors):
            print("form_invalid: {}".format(form.errors))
        else:
            print("form_invalid: {}".format(form))
            form.errors = "Error in PresentedModelForcedCreateView"
        return HttpResponseBadRequest(form.errors)

    def form_valid(self, form):
        r = super().form_valid(form)

        try:
            pmfs = PresentedModelForced.objects.filter(seller = form.instance.seller, model = form.instance.model, period = form.instance.period, year = form.instance.year)
            if pmfs:
                for pmf in pmfs:
                    if (pmf.pk != form.instance.pk):
                        pmf.delete()
        except Exception as e:
            print('Error al eliminar el modelo forzado: ' + str(e))

        return r

    def get_success_url(self) -> str:
        return reverse(
            "app_sellers:summary",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseBadRequest("No Permission")

class PresentedModelDetailSellerView(LoginRequiredMixin, IsSellerShortnamePermission, UpdateView):
    model = PresentedModel
    form_class = PresentedModelChangeFormSeller
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = "_detail_seller"

    def book_appointment(self, form, seller):
        selected_date = form.cleaned_data['booking_date']
        selected_time = form.cleaned_data['booking_time']
        manager_instance = User.objects.get(username=form.cleaned_data['manager'])
        subject_instance = BookingSubject.objects.get(code='model-disagree')
        combined_datetime = datetime.strptime(f'{selected_date} {selected_time}', '%Y-%m-%d %H:%M')
        event_id = str(uuid.uuid4().hex)  # Generate a unique event ID
        modelo = form.instance.model.code
        comentario = modelo + ": " + form.cleaned_data['comment']

        # Create and save a new appointment
        new_booking = Bookings(
            seller=seller,
            manager=manager_instance,
            subject=subject_instance,
            topics=modelo,
            comments=comentario,
            date=combined_datetime,
            idcal_event=event_id,
        )
        new_booking.save()
        send_mail_appointment_disagreed_model_seller(seller, modelo, new_booking)
        send_mail_appointment_disagreed_model_manager(seller, modelo, new_booking)
        form.instance.disagree_appointment = new_booking

        add_to_CAL(new_booking)

    def update_appointment(self, form, previous_appointment):
        pm_appointment = previous_appointment
        old_appoinment = pm_appointment.disagree_appointment
        modelo = form.instance.model.code

        # update old appointment topics
        old_appoinment.topics += ", " + modelo
        old_appoinment.comments += "\n" + modelo + ": " + form.cleaned_data['comment']
        old_appoinment.save()

        form.instance.disagree_appointment = old_appoinment
        update_CAL(old_appoinment)

    def choice_seller303Q4(self, model):
        json_pdf = model.json_pdf
        dict_json = json.loads(json_pdf)

        ca78 = float(dict_json.get('campo_110', '0,0').replace(",",".") if dict_json.get('campo_110', '0,0') != '' else '0.0')
        ca110 = float(dict_json.get('campo_110', '0,0').replace(",",".") if dict_json.get('campo_110', '0,0') != '' else '0.0')
        ca66 = float(dict_json.get('campo_66', '0,0').replace(",",".") if dict_json.get('campo_66', '0,0') != '' else '0.0')
        ca70 = float(dict_json.get('campo_70', '0,0').replace(",", ".") if dict_json.get('campo_70', '0,0') != '' else '0.0')

        if ca66 < ca110:
            ca78 = ca66
            if ca66 <= 0:
                ca78 = 0.0
        else:
            ca78 = ca110

        if ca110 - ca78 > 0:
            ca87 = str(ca110 - ca78).replace(".", ",")
        else:
            ca87 = str(0)

        ca69 = ca66 - ca78
        ca71 = str(ca69 - ca70).replace(".", ",")

        dict_json.update(
            {
                "campo_78": str(ca78).replace(".", ","),
                "campo_69":  str(ca69).replace(".",","),
                "campo_87": ca87,
                "campo_71": ca71
            }
        )
        model.json_pdf = json.dumps(dict_json)
        model.save()

    def acconto_lipe(self, model, form):
        if (model.model.code == 'IT-LIPE' or model.model.code == 'IT-ACCONTO') and form.cleaned_data[
                    'status'].code == 'agreed':
                    model.is_paid = True
                    if model.model.code == 'IT-ACCONTO':
                        model.status = ModelStatus.objects.get(code='presented')
                    model.save()
        return model

    def concept_it_lipe(self, seller, model):
        if model.country.iso_code == 'IT':
            vat_number = SellerVat.objects.filter(seller=seller, vat_country='IT').first().vat_number
            tax_code = ''
            if model.model.code == 'IT-ACCONTO':
                tax_code = '6035'
            elif model.model.code == 'IT-VATANNUALE':
                tax_code = '6099'
            elif model.period.code == "Q1":
                tax_code = '6031'
            elif model.period.code == "Q2":
                tax_code = '6032'
            elif model.period.code == "Q3":
                tax_code = '6033'
            elif model.period.code == "Q4":
                tax_code = '6034'

            concept_code = f"{vat_number.replace('IT', '')}.{tax_code}.{model.year}"
            return concept_code

    def get_vp14_from_lipe(self, model):
        vp14_total = ''
        if model.model.code == 'IT-LIPE' and model.period.code == 'Q4':
            json_pdf = json.loads(model.json_pdf)
            vp14_1 = json_pdf.get('VP14_1_hide', '')
            vp14_2 = json_pdf.get('VP14_2_hide', '')
            if vp14_1 == '':
                vp14_total = vp14_2
            elif vp14_2 == '':
                vp14_total = vp14_1
            if vp14_1 == '' and vp14_2 == '':
                vp14_total = '0,0'

            return vp14_total

    def seller_inv_for_lipe(self, seller, model):
        if model.model.code == 'IT-LIPE':
            if model.period.code == "Q1":
                first_month = 1
                latest_month = 3
            elif model.period.code == "Q2":
                first_month = 4
                latest_month = 6
            elif model.period.code == "Q3":
                first_month = 7
                latest_month = 9
            elif model.period.code == "Q4":
                first_month = 10
                latest_month = 12

            invoice = Invoice.objects.filter(
                seller=seller,
                accounting_date__year=model.year,
                accounting_date__month__gte=first_month,
                accounting_date__month__lte=latest_month,
                tax_country='IT',
                status='revised',
            ).exclude(
                Q(transaction_type='oss') | Q(transaction_type='oss-refund')
            ).count()

            print ("invoice ",invoice)
            return invoice

    def create_task_auto_AEAT(self, model):
        avalible_model = ['ES-111', 'ES-115', 'ES-130', 'ES-303', 'ES-309', 'ES-390', 'ES-202']

        if model.model.code in avalible_model:
            # execution_time = datetime.now() + timedelta(minutes=2) for testing 
            execution_time = set_execution_time()
            name = f"Auto envío {model.model.code}_{model.period.code}_{model.year} - {model.seller.name} || {execution_time.strftime('%d/%m/%Y %H:%M:%S')}"
            create_periodic_task_instance(name, "muaytax.app_documents.tasks.presentedmodels.auto_model_toAEAT", execution_time, True, model.pk)

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        today  = timezone.localtime(timezone.now())
        form.instance.seller = seller
        pk = self.kwargs.get('pk')
        model = PresentedModel.objects.filter(seller=seller, pk=pk).first()
        modelo = form.instance
        previous_appointment = self.check_for_previous_appointment(seller, model)

        # Enviar correo en caso de disconformidad sin booking
        if form.cleaned_data['is_booking'] == 'notBooking':
            comment = form.cleaned_data['comment']
            country_code=model.country.iso_code
            send_mail_disagree_not_booking_appointment(comment, country_code, seller, model)
            
        id_status = form.cleaned_data.get('status', None)
        if id_status.code == 'agreed':
            form.instance.confirmation_date = today
            # Crea la instancia de la tarea periódica para el autoenvío a la AEAT
            if settings.IS_PRODUCTION: 
                self.create_task_auto_AEAT(model)

            # Aquí se envía el correo al manager externo con el excel que se debe generar
            if model.send_to_external_manager:
                # Generar excel del modelo 111 para el manager externo
                external_excel_path = generate_external_excel_111(seller, model)
                send_email_model_111_to_external_manager(seller, model, external_excel_path)

        is_signing = self.request.POST.get('is_signing', False)
        if is_signing:
            print('is_signing', is_signing)
            model.sign_required = True
            model.save()
            return HttpResponseRedirect(reverse('app_documents:sign_documents', args=[seller.shortname, model.pk]))

        try:
            with transaction.atomic():
                # seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
                # form.instance.seller = seller
                if form.cleaned_data['is_booking'] == 'booking':
                    if previous_appointment:
                        self.update_appointment(form, previous_appointment)
                        self.request.session['updated_booking'] = previous_appointment.disagree_appointment.id
                    else:
                        self.book_appointment(form, seller)
                        self.request.session['booking_created'] = True

                # Model 303 4Q change json when devolution
                if form.cleaned_data['choice_seller'] == 'compensate':
                    self.choice_seller303Q4(modelo)

                # Logic for IT acconto and LIPE
                if modelo.country.iso_code == 'IT':
                    self.acconto_lipe(modelo, form)

                response = super().form_valid(form)
        except IntegrityError:
            #  form.add_error('asin', 'Este código ASIN ya existe')
            response = self.form_invalid(form)

        return response

    def form_invalid(self, form):
        if form and form.errors:
            print("Errores iniciales del formulario: {}".format(form.errors))

            if 'nrc_file' in form.errors:
                nrc_file = form.files.get('nrc_file')
                if nrc_file:
                    max_filename_length = 100
                    if len(nrc_file.name) > max_filename_length:
                        # Obtener el objeto `PresentedModel` relacionado
                        pk = self.kwargs.get('pk')
                        presented_model = PresentedModel.objects.filter(pk=pk).first()
                        if not presented_model:
                            print("No se encontró el modelo presentado.")
                            return super().form_invalid(form)

                        # Extraer datos para el renombramiento
                        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
                        model = presented_model.model.pk
                        name = seller.shortname
                        period = presented_model.period.code
                        year = presented_model.year
                        extension = nrc_file.name.split('.')[-1]
                        unique_code = uuid.uuid4().hex[:8]

                        # Formato del nuevo nombre
                        new_filename = f"recibo_{name}_{model}_{period}_{year}_{unique_code}.{extension}"   
                        nrc_file.name = new_filename
                        print(f"Archivo renombrado a: {nrc_file.name}")

                        # Reasignar el archivo al formulario
                        form.files['nrc_file'] = nrc_file

                        # Limpiar el error del campo `nrc_file`
                        form._errors['nrc_file'] = None

                        # Intentar revalidar el formulario
                        form.full_clean()
                        if form.is_valid():
                            print("Formulario válido después del renombramiento")
                            return self.form_valid(form)  # Si ahora es válido, pasa a form_valid
                        else:
                            print("Errores restantes tras el renombramiento: {}".format(form.errors))
        
        else:
            print("form_invalid: {}".format(form))

        return super().form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        model_object = self.get_object()
        
        still_in_limit = still_in_limit_date_direct_debit(model_object, 'app')

        context['direct_debit_models'] = ['ES-111', 'ES-115', 'ES-130', 'ES-303', 'ES-202']
        context['fractionated_models'] = ['ES-130', 'ES-303', 'ES-309', 'ES-202']
        if model_object.result:
            context['direct_debit_conditions'] = (
                model_object.model.code in context['direct_debit_models']
                and seller.is_contracted_accounting_today
                and seller.legal_entity not in ('self-employed-outside', 'other')
                and model_object.result.code == 'deposit'
                and still_in_limit
            )
            context['fractioned_payment_conditions'] = (
                model_object.model.code in context['fractionated_models']
                and seller.is_contracted_accounting_today
                and seller.legal_entity not in ('self-employed-outside', 'other')
                and model_object.result.code == 'deposit'
                and model_object.amount < 50000
            )

        # data for model IT-LIPE and ACCONTO
        context['invoice'] = self.seller_inv_for_lipe(seller, model_object)
        context['concept'] = self.concept_it_lipe(seller, model_object)
        context["vp14"] = self.get_vp14_from_lipe(model_object)

        # get appointments that start after now
        context["appointments_disagree_model"] = self.check_for_previous_appointment(seller, model_object)

        # today = date.today()
        # presentation_date = date(2024, 4, 21)
        # context['can_book_disagreement'] = today < presentation_date
        context['support_email'] = '<EMAIL>' if seller.legal_entity in ['self-employed', 'sl'] else '<EMAIL>'

        # Enable Book Disagreement ###################################################################
        ahorita = datetime.now().date()
        year = ahorita.year
        if (
            ( ahorita > datetime.strptime(f'{year}-04-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-04-22', '%Y-%m-%d').date() ) or 
            ( ahorita > datetime.strptime(f'{year}-07-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-07-22', '%Y-%m-%d').date() ) or 
            ( ahorita > datetime.strptime(f'{year}-10-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-10-22', '%Y-%m-%d').date() ) or
            ( ahorita > datetime.strptime(f'{year}-01-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-01-22', '%Y-%m-%d').date() )
        ):
            context['can_book_disagreement'] = True
        else:
            context['can_book_disagreement'] = False
        ##############################################################################################

        return context

    def check_for_previous_appointment(self, seller, model):
        today  = timezone.localtime(timezone.now())
        """
        Buscas entre los presented models, si hay alguno que tenga un appointment asociado con ese manager del respectivo modelo
        y que la fecha sea posterior a hoy.
        "model" es igual al "presented model" que se está editando
        """
        manager_of_model = ''
        if model.country.iso_code == 'ES':
            manager_of_model = ManageModel.objects.filter(model=model.model, legal_entity=seller.legal_entity).first()
        else:
            manager_of_model = ManageModel.objects.filter(model=model.model).first()

        if manager_of_model is None:
            return None

        return PresentedModel.objects.filter(
            seller=seller,
            disagree_appointment__manager=manager_of_model.manager,
            disagree_appointment__date__gt=today,
            disagree_appointment__status='pending'
        ).order_by("disagree_appointment__date").first()

    def get_success_url(self) -> str:
        return reverse("app_documents:presented_model_list", args=[self.object.seller.shortname])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class PresentedModelGenerateTXT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def get_form_kwargs(self):
        print("get_form_kwargs")
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        return kwargs

    def get(self, request, *args, **kwargs):

        model_id = self.kwargs['model_id']
        presented_model = PresentedModel.objects.filter(id=model_id).first()
        seller = Seller.objects.filter(id=presented_model.seller.pk).first()
        model = presented_model.model.code.replace('ES-', '').replace('IT-', '')
        year = presented_model.year
        period = presented_model.period.code.replace('Q', 'T')

        print("model: ", presented_model)

        error_messages = {}

        if presented_model and presented_model.model.code == 'IT-VATANNUALE':
            txt = generate_TXT_MODEL_ANNUALE_IT(presented_model)
            if isinstance(txt, list):
                error_messages = txt
            else:
                filePath = f"muaytax/media/generated_txt/TXT_{seller.shortname}_IT_{year}_{period}_{model}.iva"
                with open(filePath, "w", encoding="cp1252", newline="\r\n") as archivo:
                    archivo.write(txt)
        elif presented_model and presented_model.model.country == 'ES':
            txt = txtToAEAT(presented_model, seller)
            if isinstance(txt, list):
                error_messages = txt
            else:
                filePath = f"muaytax/media/generated_txt/TXT_{seller.shortname}_ES_{year}_{period}_{model}.txt"
                with open(filePath, "w", encoding="cp1252") as archivo:
                    archivo.write(txt)
        else:
            print("ningun modelo")
            error_messages = {"error": "No se ha encontrado el modelo"}

        if error_messages:
            return JsonResponse(error_messages, status=400, safe=False)
        print("filePath: ", filePath)
        return JsonResponse(filePath, safe=False)

class PresentedModelDirectAEATView(LoginRequiredMixin, IsManagerRolePermission, View):
    
    def get(self, request, *args, **kwargs):
        model_id = self.kwargs['model_id']
        action = self.kwargs['action']
        error_txt = {}
        json_response = {}

        presented_model = PresentedModel.objects.filter(id=model_id).first()
        seller = Seller.objects.filter(id=presented_model.seller.pk).first()

        headers = {'Content-Type': 'application/json'}
        cert_password = settings.PASSWORD_CERTIFICATE
        cert_path = settings.PATH_CERTIFICATE
        if os.path.exists(cert_path) is False:
            error_txt.update({"error": "No se ha encontrado el certificado digital"})

        model_txt = txtToAEAT(presented_model, seller)
        if isinstance(model_txt, list):
            error_txt = model_txt

        if error_txt:
            if action == 'send':
                presented_model.aeat_json_response = json.dumps(error_txt)
                presented_model.model_sent_by = 'manager'
                presented_model.save()
            return JsonResponse(error_txt, status=400, safe=False)

        if action == 'send':
            json_response = present_to_AEAT(presented_model, model_txt, cert_path, cert_password, headers, sent_by='manager')
        elif action == 'draft':
            json_response = draft_valitation(presented_model, model_txt, cert_path, cert_password, headers)

        print("json_response: ", json_response)
        return JsonResponse(json_response, safe=False)

class PresentedModelGenerateXML(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def get(self, request, *args, **kwargs):
        seller_id = self.kwargs['pk']
        model = self.kwargs['model']
        period = self.kwargs['period']
        year = self.kwargs['year']

        presented_model = PresentedModel.objects.filter(
            year=year,
            seller_id=seller_id,
            period_id=period,
            model_id__code__icontains=model
            ).first()

        if presented_model:
            xml_file, file_name = generate_XML(presented_model)

            response = HttpResponse(xml_file, content_type='application/xml')
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'

            return response
        else:
            return JsonResponse({"error": "No se puede generar XML, aún no se ha mandado a revisión el modelo."}, status=204)
            # return HttpResponse(content=message, status=204, reason=message)

    def handle_no_permission(self):
        return HttpResponseBadRequest("No Permission")

def generate_XML(presented_model):
    from django.template import loader

    dict_json = json.loads(presented_model.json_pdf)
    modified_dict_json = {key.replace('-', '_'): value for key, value in dict_json.items()}

    period_number = presented_model.period.order
    module_number = str(period_number) + str(presented_model.year)

    modified_dict_json['fecha_impegno'] = datetime.now().strftime('%d%m%Y')
    modified_dict_json['numero_modulo'] = module_number

    numeric_fields = ['VP2', 'VP3', 'VP4', 'VP5', 'VP6_1', 'VP6_2', 'VP7', 'VP8', 'VP9', 'VP10', 'VP11', 'VP12',  'VP13_2', 'VP14_1', 'VP14_2']
    for field in numeric_fields:
        if field in modified_dict_json and modified_dict_json[field]:
            value = float(modified_dict_json[field].replace(',', '.'))
            if value == 0:
                modified_dict_json[field] = ""
            else:
                modified_dict_json[field] = "{:.2f}".format(value).replace('.', ',')

    xml_content = loader.render_to_string('documents/xml/lipe.xml', {'data': modified_dict_json})
    file_name = f"IT{modified_dict_json['codice_fiscale_pag1']}_LI_{module_number}"

    return xml_content, file_name
