{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters crispy_forms_field %}

{% block title %}
    Crear Nueva Tarea
{% endblock title %}

{% block stylesheets %}
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/choices/choices.min.css">
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/flatpickr/flatpickr.min.css">
    <style>
        .is-focused .choices__inner,
        .is-open .choices__inner {
            border-color: #86b7fe !important; 
        }
        .choices__inner {
            color: #495057 !important;
            font-size: 14px!important;
        }
        .choices__list--dropdown {
            color: #212529 !important;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .bg-booking-custom {
            background-color: #f4f4f5!important;
        }
        .rounded-10 {
            border-radius: 10px!important;
        }
        .nav-link {
            color: #888;
            text-decoration: none;
        }
        .nav-link:hover {
            color: #888;
        }
        .nav-link:focus {
            color: #03ad65;
        }
        .nav-pills .nav-link.active, .nav-pills .show>.nav-link {
            color: #03ad65;
            font-weight: 600;
            background: #fff;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }
        .form-control.flatpickr-input {
            background-color: #fff; /* Fondo blanco */
            cursor: pointer; /* Cursor de puntero para indicar que es seleccionable */
        }
        
        .form-control.flatpickr-input:disabled {
            cursor: not-allowed; /* Cursor no permitido cuando está deshabilitado */
        }
        .choices__inner {
            background-color: #fff; /* Fondo blanco para el campo de selección */
            cursor: pointer; /* Cursor de puntero */
        }
        
        .choices__inner.is-disabled {
            cursor: not-allowed; /* Cursor no permitido cuando está deshabilitado */
        }
        .form-text.warning {
            color: #e76c25; /* Color amarillo para advertencia */
        }
    </style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                        Crear Nueva Tarea
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="feather icon-home"></i></a></li>
                    <li class="breadcrumb-item"><a href="{% url 'app_tasks:list_task_pending_manager' request.user.username %}">Tareas Pendientes</a></li>
                    <li class="breadcrumb-item"><a href="#">Crear Nueva Tarea</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
    <div class="col-xl-6 col-lg-8 col-md-10 col-sm-12 offset-xl-3 offset-lg-2 offset-md-1 offset-sm-0">
        <div class="card">
            <div class="card-header">
                <h5 class="text-center">Crear Nueva Tarea</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="container booking-container">
                        <div class="row">
                            <!-- Seller (user) -->
                            <div class="col-12 mb-2">
                                <label for="{{ form.user.id_for_label }}" class="col-form-label{% if form.user.field.required %} requiredField{% endif %}">
                                    {{ form.user.label }}{% if form.user.field.required %}<span class="asteriskField">*</span>{% endif %}
                                </label>
                                {% if form.user.errors %}
                                    {% crispy_field form.user 'class' 'form-control is-invalid' %}
                                    <span class="text-danger">{{ form.user.errors }}</span>
                                {% else %}
                                    {% crispy_field form.user 'class' 'form-control form-select' %}
                                {% endif %}
                            </div>

                            <!-- Tipo de tarea y Fecha límite -->
                            <div class="col-md-6 mb-2">
                                <label for="{{ form.task_type.id_for_label }}" class="col-form-label{% if form.task_type.field.required %} requiredField{% endif %}">
                                    {{ form.task_type.label }}{% if form.task_type.field.required %}<span class="asteriskField">*</span>{% endif %}
                                </label>
                                {% if form.task_type.errors %}
                                    {% crispy_field form.task_type 'class' 'form-control is-invalid' %}
                                    <span class="text-danger">{{ form.task_type.errors }}</span>
                                {% else %}
                                    {% crispy_field form.task_type 'class' 'form-control form-select' %}
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="{{ form.due_date.id_for_label }}" class="col-form-label{% if form.due_date.field.required %} requiredField{% endif %}">
                                    {{ form.due_date.label }}{% if form.due_date.field.required %}<span class="asteriskField">*</span>{% endif %}
                                </label>
                                <div class="input-group">
                                    {% if form.due_date.errors %}
                                        {% crispy_field form.due_date 'class' 'form-control is-invalid' autocomplete='off' %}
                                        <span class="text-danger">{{ form.due_date.errors }}</span>
                                    {% else %}
                                        {% crispy_field form.due_date 'class' 'form-control cursor-pointer' autocomplete='off' %}
                                    {% endif %}
                                    <span class="input-group-text bg-transparent cursor-pointer" id="calendar-icon"><i class="feather icon-calendar"></i></span>
                                </div>
                            </div>

                            <!-- Notificación y Días antes de la fecha límite para la notificación -->
                            <div class="col-md-6 mb-2">
                                <label for="{{ form.notification_type.id_for_label }}" class="col-form-label{% if form.notification_type.field.required %} requiredField{% endif %}">
                                    {{ form.notification_type.label }}{% if form.notification_type.field.required %}<span class="asteriskField">*</span>{% endif %}
                                </label>
                                {% if form.notification_type.errors %}
                                    {% crispy_field form.notification_type 'class' 'form-control is-invalid' %}
                                    <span class="text-danger">{{ form.notification_type.errors }}</span>
                                {% else %}
                                    {% crispy_field form.notification_type 'class' 'form-control form-select' %}
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="{{ form.notification_days_before_deadline.id_for_label }}" class="col-form-label{% if form.notification_days_before_deadline.field.required %} requiredField{% endif %}">
                                    {{ form.notification_days_before_deadline.label }}{% if form.notification_days_before_deadline.field.required %}<span class="asteriskField">*</span>{% endif %}
                                </label>
                                {% if form.notification_days_before_deadline.errors %}
                                    {% crispy_field form.notification_days_before_deadline 'class' 'form-control is-invalid' %}
                                    <span class="text-danger">{{ form.notification_days_before_deadline.errors }}</span>
                                {% else %}
                                    {% crispy_field form.notification_days_before_deadline 'class' 'form-control' %}
                                {% endif %}
                                <small id="notification-help" class="form-text warning">Deshabilitado cuando no tiene notificaciones.</small>
                            </div>

                            <!-- Descripción -->
                            <div class="col-12 mb-2">
                                <label for="{{ form.description.id_for_label }}" class="col-form-label{% if form.description.field.required %} requiredField{% endif %}">
                                    {{ form.description.label }}{% if form.description.field.required %}<span class="asteriskField">*</span>{% endif %}
                                </label>
                                {% if form.description.errors %}
                                    {% crispy_field form.description 'class' 'form-control is-invalid' %}
                                    <span class="text-danger">{{ form.description.errors }}</span>
                                {% else %}
                                    {% crispy_field form.description 'class' 'form-control' %}
                                {% endif %}
                            </div>

                            <!-- Botones -->
                            <div class="col-12 d-flex justify-content-between mt-3">
                                <a href="{% url 'app_tasks:list_task_pending_manager' request.user.username %}" class="btn btn-secondary w-48">Cancelar</a>
                                <button type="submit" class="btn btn-primary w-48">Crear Tarea</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block javascripts %}
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/choices/choices.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sellerField = document.getElementById('id_user');
        new Choices(sellerField, {
            searchPlaceholderValue: 'Buscar vendedor',
            noResultsText: 'No se encontraron resultados',
            itemSelectText: 'Presiona para seleccionar',
            shouldSort: false,
            position: 'bottom',
            allowHTML: true,
        });
    
        const dueDateInput = document.getElementById('id_due_date');
        const calendarIcon = document.getElementById('calendar-icon');
    
        const datepicker = flatpickr(dueDateInput, {
            dateFormat: 'Y-m-d',
            disableMobile: true,
            allowInput: false, // Deshabilitar la entrada manual
        });
    
        calendarIcon.addEventListener('click', function() {
            dueDateInput._flatpickr.open();
        });
    
        const notificationTypeField = document.getElementById('id_notification_type');
        const notificationDaysField = document.getElementById('id_notification_days_before_deadline');
        const notificationHelp = document.getElementById('notification-help');
    
        function toggleNotificationDaysBeforeDeadline(value) {
            if (value === 'none') {
                notificationDaysField.setAttribute('disabled', 'disabled');
                notificationDaysField.removeAttribute('required');
                notificationDaysField.value = '';
                notificationHelp.style.display = 'block'; // Mostrar el mensaje de ayuda
            } else {
                notificationDaysField.removeAttribute('disabled');
                notificationDaysField.setAttribute('required', 'required');
                notificationHelp.style.display = 'none'; // Ocultar el mensaje de ayuda
            }
        }
    
        notificationTypeField.addEventListener('change', function() {
            toggleNotificationDaysBeforeDeadline(this.value);
        });
    
        // Inicializar el estado del campo de notificación
        toggleNotificationDaysBeforeDeadline(notificationTypeField.value);
    });
</script>
{% endblock javascripts %}
