{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
{{ country.name | title }}
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/all/all.min-v5.15.4.css"> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    fieldset.form-borders {
      border: 1px groove #838383 !important;
      padding: 0 1.4em 1.4em 1.4em !important;
      margin: 0 0 1.5em 0 !important;
      -webkit-box-shadow:  0px 0px 0px 0px #000;
      box-shadow:  0px 0px 0px 0px #000;
    }
    legend.form-borders {
      text-align: left !important;
      width:inherit; /* Or auto */
      padding:0 10px; /* To give a bit of padding on the left and right */
      border-bottom: none;
      float: unset !important;
    }


    #list-table td span {     
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }


     .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }
      
      .dataTables_filter {
      display: none;
    }

    .icon-text-size{
      font-size:15px;
    }

    .modal-size{
      max-width: 80%;
    }
    .list-causes-warning{
      list-style-type: circle;
    }
    .card{
      max-height: 73%;
    }
    .row-cards{
      margin-bottom: -25px;
    }
    .error-title{
      color: #d11507;
      font-weight: bold;
    }
  
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">IVA {{ country.name | title }}</h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
          </li>
          <li class="breadcrumb-item">
            <a href=".">IVA {{ country.name | title }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
        <div class="dt-responsive">        
          <fieldset class="form-borders">
            <legend class="form-borders">Filtros <i class="fa-solid fa-filter"></i></legend>



            <div class="row d-flex mt-3">
              <!-- Search + Pagination -->
              <div class="col d-flex justify-content-center align-items-start">
                <div class="input-group">
                  <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()" />
                </div>
              </div>
              <!-- Search + Pagination -->
              <div class="col-3">
                <select class="form-select form-control" name="period-input" id="period" onchange="onChangePeriodYear()">
                  <option value="0A">Anual</option>
                  <option value="Q1">Trimestre 1</option>
                  <option value="Q2">Trimestre 2</option>
                  <option value="Q3">Trimestre 3</option>
                  <option value="Q4">Trimestre 4</option>
                </select>
              </div>
              <div class="col-3">
                <select class="form-select form-control" name="period-input" id="year" onchange="onChangePeriodYear()">
                  <option value="2022">2022</option>
                  <option value="2023">2023</option>
                  <option value="2024">2024</option>
                </select>
              </div>
              <div class="col-auto d-flex  align-items-center">
                <p class="mx-1">
                  <button id="showcolumns1" class="btn btn-secondary m-0" onclick="onClickButtonTxT()" >
                    <i class="fa-regular fa-eye"></i>
                    <b>TXT</b>
                  </button>
                </p>
                <p class="mx-1">
                  <button id="showcolumns" class="btn btn-info m-0" id="infoModal" data-bs-toggle="modal" data-bs-target="#modal" >
                    <i class="fa-regular fa-circle-question fa-lg"></i>
                    <b>Iconos </b>
                  </button>
                </p>
              </div>
            </div>
          </fieldset>
          <!-- Total pending invoices -->
          <div class="row row-cards">
            <div class="col-2 ">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL FACTURAS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b id="total_invoices_count">&nbsp</b></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2 ">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS REQUERIDOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="pending-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-file-contract " style="color: #FF0000;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="revision-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-regular fa-clock fa-sm" style="color: #7c7d7e;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS RECHAZADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="disagreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-xmark" style="color: #FE8330;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS ACEPTADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="agreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-check" style="color: #ffd700;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS PRESENTADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="presented-model-count">&nbsp</b>&nbsp&nbsp<i class="fas fa-check-double" style="color: #02c018;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <table id="seller-list-table2" class="table nowrap">
            <thead class="table-head">
              <tr>
                <th>Nombre</th>
                <th>Pendientes</th>
                {% if country.iso_code == "ES" %}
                  <th  style="display: none">Model Priority</th>
                  <th  style="display: none">Priority avg</th>
                  <th class="model" style="display:">M-303</th>
                  <th class="model" style="display:">M-349</th>
                  <th class="model" style="display:">M-369</th>
                {% endif %}
                <th>Enero</th>
                <th>Febrero</th>
                <th>Marzo</th>
                <th>Abril</th>
                <th>Mayo</th>
                <th>Junio</th>
                <th>Julio</th>
                <th>Agosto</th>
                <th>Septiembre</th>
                <th>Octubre</th>
                <th>Noviembre</th>
                <th>Diciembre</th>
                <th>Último acceso</th>
                <th style="width:5%;">Acciones</th>
              </tr>
            </thead>
            <tbody>
             
            </tbody>
          </table>
          

          <!-- Modal Info-->
                <div class="modal fade " id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
                  <div class="modal-dialog modal-dialog-centered modal-lg modal-size" role="document">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title" id="modalLabel">Información sobre los iconos del listado</h5>
                      </div>
                        <div class="modal-body">
                          {% comment %} <div class="col form-group form-check p-3">
                            <p> <b> <h4 style= "text-align: center;">¿Está seguro que desea eliminar las facturas seleccionadas?</b></h4></p>
                          </div> {% endcomment %}
                          <div class="row d-flex align-items-center">
                            <div class= "col">
                              <ul>
                                <li><p><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i> <b class="icon-text-size">&nbsp;Modelo requerido</b></li></p>
                                <li><p><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i> <b class="icon-text-size">&nbsp;Modelo pendiente de confirmar</b></li></p>  
                                <li><p><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i> <b class="icon-text-size">&nbsp;Modelo rechazado por el cliente</b></p></li> 
                                <li><p><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i> <b class="icon-text-size">&nbsp;Modelo aceptado por el cliente</b></p></li>
                              </ul>
                            </div>
                            <div class= "col">
                              <ul>
                                <li><p><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i> <b class="icon-text-size">&nbsp;Modelo presentado</b></p></li> 
                                <li><p><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Modelo NO requerido</b></p></li>
                              </ul>
                            </div>
                            
                          </div>
                          <br>
                          <p style="font-size:20px; color: #FF0000;"><b>Códigos de errores XX:</b></p>
                          <div class= "col " style="margin-left: 20px;" >
                            <b>MODELO 303:</b>
                            <ul class="list-causes-warning">
                              <li> <span class="error-title">01:</span> País IVA España NO contratado (ficha país IVA) y además tiene facturas contabilizadas en este período.</li>
                              <li> <span class="error-title">02:</span> País IVA España no se encuentra registrado entre sus países IVA y además tiene facturas contabilizadas con país de tasas en España en este período.</li>
                              <li> <span class="error-title">03:</span> País IVA: España contratado (ficha país IVA), "¿Quiere activación?" &rarr; no o "Alta IAE" &rarr; Stand-by y además tiene facturas contabilizadas en este período.</li>
                              <li> <span class="error-title">04:</span> Facturas revisadas con fecha de contabilización vacía. </li>
                            </ul>
                          </div>
                          <div class= "col" style="margin-left: 20px;">
                            <b>MODELO 349:</b>
                            <ul class="list-causes-warning">
                              <li> <span class="error-title">05:</span> País IVA España NO contratado (ficha país IVA)  y además tiene facturas contabilizadas en este período con tipo de transacción "intra comunitario".</li>
                              <li> <span class="error-title">06:</span> País IVA España no se encuentra registrado entre sus países IVA y además tiene facturas contabilizadas en este período con tipo de transacción "intra comunitario".</li>
                              <li> <span class="error-title">07:</span> País IVA España contratado (ficha país IVA), "¿Quiere activación?" &rarr; no o "Alta IAE" &rarr; Stand-by y además tiene facturas contabilizadas en este período con tipo de transacción "intra comunitario".</li>
                              <li> <span class="error-title">08:</span> Facturas revisadas con fecha de contabilización vacía con tipo de transacción "intra comunitario".</li>
                            </ul>
                          </div>
                          <div class= "col " style="margin-left: 20px;" >
                            <b>MODELO 369:</b>
                            <ul class="list-causes-warning">
                              <li> <span class="error-title">09:</span> OSS &rarr; NO/Desconocido y tiene facturas de tipo OSS</li>
                              <li> <span class="error-title">10:</span> País IVA España no se encuentra registrado entre sus países IVA y además tiene facturas contabilizadas con país de tasas en España en este período.</li>
                              <li> <span class="error-title">11:</span> País IVA: España contratado (ficha país IVA), "¿Quiere activación?" &rarr; no o "Alta IAE" &rarr; Stand-by y además tiene facturas contabilizadas en este período.</li>
                              <li> <span class="error-title">12:</span> Facturas revisadas con fecha de contabilización vacía. </li>
                              <li> <span class="error-title">14:</span> País IVA España NO contratado (ficha país IVA) y además tiene facturas contabilizadas en este período.</li>
                              <li> <span class="error-title">15:</span> OSS Contratada y País IVA España NO contratado/Existente.</li>
                            </ul>
                          </div>
                          
                        </div>
                      <div class="modal-footer d-flex justify-content-center">
                          <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">Cerrar</button>
                      </div>
                    </div>
                  </div>
                </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}

<!-- JQUERY DATATABLES -->
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
<script>
    let showTXT = false;

    let dataTable = null;

    const debug = true;
      const ajaxData=(d)=>{
        if(debug) console.log('ajax ', d);
          let tParams = "";
          let year = document.getElementById("year").value;
          let period = document.getElementById("period").value;
          if(year){
            if (debug) console.log('filterDT | year: ', year);
            d.year = year
            tParams += "&year=" + year;
          }
          if(period){
            if (debug) console.log('filterDT | period: ', period);
            d.period = period
            tParams += "&period=" + period;
          }

        getTotals(tParams);
        return d
      }

    const getTotals = (params) => {
      let p = params;

      if (!p || p == undefined || p == null || p == "") {
        p = "";
      } else if (p.charAt(0) == "&") {
        p[0] = "?";
      }
    }


    $(document).ready(function() {
        let cookie;
        let pending_model_count = 0;
        dataTable = $("#seller-list-table2").DataTable({
            "serverSide":false,
              "ajax":{
                  "dataSrc":"data",
                  "url":"{% url 'app_sellers:seller_vatcountry_list_dt' country.iso_code %}",
                  "data":function( d ){
                    ajaxData(d);
                    
                    }
              },
              "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
                    "lengthMenu": "_MENU_",
                    "zeroRecords": "No se han encontrado vendedores.",
                    "info": "_START_ a _END_ de un total de _TOTAL_",
                    "search": "Buscar:",
                    "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                    "infoFiltered": ""
                  },
              "createdRow": function(row, data, dataIndex) {

                const shortname = data.shortname; 
                const link = '/sellers/' + shortname + '/';
                $(row).attr('style', 'cursor: pointer;');
                $(row).attr('onclick', "window.location.href = '" + link + "';");
              },
              "columns":[
                  {"data": "user_name",
                  "className":"head_name",
                  "render": function(data, type, row) {
                    let html = '';
                    html += '<td class="align-middle">';
                  html += '<div class="d-inline-block">';
                  html += '<h6 class="m-b-0"><b>';
                  
                  let name = row.seller_name;  
                  if (typeof name === 'string') {  
                      const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l','sl.']; 
                        const words = row.seller_name.split(' ').map(function(word) {
                            const lowerWord = word.toLowerCase();
                            if (lowerCaseSuffixes.includes(lowerWord)) {
                                return word.toUpperCase();
                            } else {
                                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                            }
                      });
                    html += words.join(' ');
                  }
                  html += '</b>';
                  if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                      html += ' - ' + row.user_name.split(' ').map(function(word) {
                          return word.charAt(0).toUpperCase() + word.slice(1);
                      }).join(' ');
                  }  
                  
                  html += '</h6>';
                  html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                  html += '</div>';
                  html += '</td>';
                  
                  return html;}
                  },
                  {"data": "num_pending_invoices",
                    "className":"pendings",
                    "render": function(data, type, row) {
                      if (data && (type === 'display' || type === 'filter')) {
                        let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                        html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                        html += '<div class="progress" style="height: 15px;">';
                        html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                        html += '</div>';
                        html += '</td>';
                        return html;
                      }
                      return data;
                    }
                },
                {"data": null,
                  "visible":false,
                  "render": function(data, type, row) {
                    let html='';
                    html += '<div>'+ row.model_min +'</div>';
                    return html;
                  }
                },
                {"data": null,
                  "visible":false,
                  "render": function(data, type, row) {
                    let html='';
                    html += '<div>' + row.model_avg +'</div>';
                    return html;
                  }
                },
                {"data": "model_303",
                  "className":"model",
                  "render": function(data, type, row) {
                        let html = ' ';
                        let m303 = row.model_303;
                        if(m303 == "presented"){
                            html += '<span class="d-none">'+ 5 + '</span>';
                            html += '<a href="/sellers/'+ row.shortname +'/docs/presented_models/"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';  
                        }
                        else if(m303 == "pending"){
                            html += '<span class="d-none">'+ 5 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i>';
                        }
                        else if(m303 == 'disagreed'){
                            html += '<span class="d-none">'+ 2 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i>';
                        }
                        else if(m303 == 'agreed'){
                            html += '<span class="d-none">'+ 3 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                        }
                        else if(m303 == 'not-required'){
                            html += '<span class="d-none">'+ 6 +'</span>';
                            html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                        }
                        else if(m303 == 'required'){
                            html += '<span class="d-none">'+ 0 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i>';
                        }
                        else if(m303 == 'warning'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i>';
                        }
                        else if(m303 == 'warning01'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-xl " style="color: #FF0000;">01</i>';
                        }
                        else if(m303 == 'warning02'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-xl " style="color: #FF0000;">02</i>';
                        }
                        else if(m303 == 'warning03'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-xl " style="color: #FF0000;">03</i>';
                        }
                        else if(m303 == 'warning04'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-xl " style="color: #FF0000;">04</i>';
                        }
                        return html;

                  }
                },
                {"data": "model_349",
                  "className":"model",
                  "render": function(data, type, row) {
                        let html = ' ';
                        let m349 = row.model_349;
                        if(m349 == "presented"){
                            html += '<span class="d-none">'+ 5 + '</span>';
                            html += '<a href="/sellers/'+ row.shortname +'/docs/presented_models/"><i class="fas fa-check-double fa-xl " style="color: #02c018;"></i></a>';  
                        }
                        else if(m349 == "pending"){
                            html += '<span class="d-none">'+ 5 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i>';
                        }
                        else if(m349 == 'disagreed'){
                            html += '<span class="d-none">'+ 2 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i>';
                        }
                        else if(m349 == 'agreed'){
                            html += '<span class="d-none">'+ 3 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                        }
                        else if(m349 == 'not-required'){
                            html += '<span class="d-none">'+ 6 +'</span>';
                            html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                        }
                        else if(m349 == 'required'){
                            html += '<span class="d-none">'+ 0 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-file-contract fa-xl" style="color:	#FF0000;"></i>';
                        }
                        else if(m349 == 'warning05'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-xl " style="color: #FF0000;">05</i>';
                        }
                        else if(m349 == 'warning06'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-xl " style="color: #FF0000;">06</i>';
                        }
                        else if(m349 == 'warning07'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-xl " style="color: #FF0000;">07</i>';
                        }
                        else if(m349 == 'warning08'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-xl " style="color: #FF0000;">08</i>';
                        }
                        return html;
                  }
                },
                {"data": "model_369",
                  "className":"model",
                  "render": function(data, type, row) {
                        let html = ' ';
                        let m369 = row.model_369;
                        if(m369 == "presented"){
                            html += '<span class="d-none">'+ 5 + '</span>';
                            html += '<a href="/sellers/'+ row.shortname +'/docs/presented_models/"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';  
                        }
                        else if(m369 == "pending"){
                            html += '<span class="d-none">'+ 5 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i>';
                        }
                        else if(m369 == 'disagreed'){
                            html += '<span class="d-none">'+ 2 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i>';
                        }
                        else if(m369 == 'agreed'){
                            html += '<span class="d-none">'+ 3 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                        }
                        else if(m369 == 'not-required'){
                            html += '<span class="d-none">'+ 6 +'</span>';
                            html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                        }
                        else if(m369 == 'required'){
                            html += '<span class="d-none">'+ 0 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i>';
                        }
                        else if(m369 == 'warning'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i>';
                        }
                        else if(m369 == 'warning09'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">09</i>';
                        }
                        else if(m369 == 'warning10'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">10</i>';
                        }
                        else if(m369 == 'warning11'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">11</i>';
                        }
                        else if(m369 == 'warning12'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">12</i>';
                        }
                        else if(m369 == 'warning14'){
                            html += '<span class="d-none">'+ 1 +'</span>';
                            html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">14</i>';
                        }
                        else if(m369 == 'warning15'){
                          html += '<span class="d-none">'+ 1 +'</span>';
                          html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">15</i>';
                        }
                        return html;

                  }
                },
                {"data": "month1",
                    "className": "txt Q1",
                  "render": function(data, type, row) {
                        let html = '';
                        const month1 = row.month1; 
                      if (month1 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    },            
                },
                {"data": "month2",
                  "className": "txt Q1",
                  "render": function(data, type, row) {
                        let html = '';
                        const month2 = row.month2; 
                      if (month2 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }       

                },
                {"data": "month3",
                  "className": "txt Q1",
                  "render": function(data, type, row) {
                        let html = '';
                        const month3 = row.month3; 
                      if (month3 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }
                },
                {"data": "month4",
                  "className": "txt Q2",
                  "render": function(data, type, row) {
                        let html = '';
                        const month4 = row.month4; 
                      if (month4 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }      
                },
                {"data": "month5",
                  "className": "txt Q2",
                    "render": function(data, type, row) {
                          let html = '';
                          const month5 = row.month5; 
                        if (month5 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },      
                  },
                {"data": "month6",
                  "className": "txt Q2",
                  "render": function(data, type, row) {
                        let html = '';
                        const month6 = row.month6; 
                      if (month6 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    } 
                },
                {"data": "month7",
                  "className": "txt Q3",
                  "render": function(data, type, row) {
                        let html = '';
                        const month7 = row.month7; 
                      if (month7 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }     
                },
                {"data": "month8",
                "className": "txt Q3",
                  "render": function(data, type, row) {
                        let html = '';
                        const month8 = row.month8; 
                      if (month8 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    },      
                },
                {"data": "month9",
                  "className": "txt Q3",
                  "render": function(data, type, row) {
                        let html = '';
                        const month9 = row.month9; 
                      if (month9 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    },      
                },
                {"data": "month10",
                  "className": "txt Q4",
                  "render": function(data, type, row) {
                        let html = '';
                        const month10 = row.month10; 
                      if (month10 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }     
                },
                {"data": "month11",
                  "className": "txt Q4",
                  "render": function(data, type, row) {
                        let html = '';
                        const month11 = row.month11; 
                      if (month11 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }     
                },
                {"data": "month12",
                  "className": "txt Q4",
                  "render": function(data, type, row) {
                        let html = '';
                        const month12 = row.month12; 
                      if (month12 === true) {
                          html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                          html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                          html += '</a>';
                      } else {
                          html += '&nbsp';
                      }
                          
                          return html;
                    }     
                },
                {"data": "last_login",
                  "className":"login",
                  "render": function(data, type, row) {
                    if (data && (type === 'display' || type === 'filter')) {
                      const date = new Date(data);

                      const day = date.getDate().toString().padStart(2, '0');
                      const month = date.toLocaleString('default', { month: 'short' });
                      const year = date.getFullYear();
                      const hours = date.getHours().toString().padStart(2, '0');
                      const minutes = date.getMinutes().toString().padStart(2, '0');

                      const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;
                      
                      return formattedDate;
                  }
                  return data; // Para otros tipos, como 'sort'
                  }
                },
                {"data": null,
                  "className":"actions",
                  "orderable": false,
                  "render":function(data, type, row) {
                    let html = '<td class="align-middle text-center">';
                    html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                    html += '<i class="feather icon-edit"></i>';
                    html += '</a>';
                    html += '</td>';
                    html = '<div style="text-align: center;">' + html + '</div>';    
                    return html;
                  },
                
                }
              ],
              "order": [[2, "asc"],[3, "asc"],[0, "asc"]],
              "paging": true,
              "searching":true,
              "lengthChange":false,
              "lengthMenu":[[100,150,200,-1], [100,150,200, 'Todos']],
              "initComplete": function(settings, json) {
                 dataTable.settings()[0].nTBody.style.width = "100%";
                 dataTable.settings()[0].nTable.style.width = "100%";
                 dataTable.settings()[0].nTHead.style.width = "100%";
                 
                 pending_model_count = json.pending_model_count; // Obtiene el valor calculado cuando se hace la consulta en la base de datos
                 document.getElementById("pending-model-count").textContent = pending_model_count; // Actualiza el valor en el HTML
                 revision_model_count = json.revision_model_count;
                 document.getElementById("revision-model-count").textContent = revision_model_count;
                 disagreed_model_count = json.disagreed_model_count;
                 document.getElementById("disagreed-model-count").textContent = disagreed_model_count;
                 agreed_model_count = json.agreed_model_count;
                 document.getElementById("agreed-model-count").textContent = agreed_model_count;
                 total_invoices_count =json.total_invoices;
                 presented_model_count = json.presented_model_count;
                 document.getElementById("presented-model-count").textContent = presented_model_count;
                 document.getElementById("total_invoices_count").textContent = total_invoices_count;
                 
                 
                 console.log("Tabla:", settings)
                  cookie = getCookie("allsellersvat_show_txt");
                  const period = "{{period}}";
                  if(cookie == true || cookie == "true"){
                    $('th.txt, td.txt').hide();
                    $(`th.txt.${period}, td.txt.${period}`).show();

                  }else{
                      $('th.txt, td.txt').hide();
                  }
                 
       
              },
              "drawCallback": function(settings) {
                  console.log("Tabla redibujada:", settings);
                  dataTable.settings()[0].nTable.style.width = "100%";
                  cookie = getCookie("allsellersvat_show_txt");
                  const period = "{{period}}";
                  if(cookie == true || cookie == "true"){
                    $('th.txt, td.txt').hide();
                    $(`th.txt.${period}, td.txt.${period}`).show();

                  }else{
                      $('th.txt, td.txt').hide();
                  }
                  }
        
        });

    });

    function search() {
      var tipo = $("#search").val();
      dataTable.column(0).search(tipo).draw();
    }

    function filtrar() {
      var tipo = $("#tipo").val();
      dataTable.column(1).search(tipo).draw();
    }

    const onChangePeriodYear =()=>{
      const period = document.getElementById('period');
      const year = document.getElementById('year');
      const urlembed= "{% url 'app_sellers:vat' country.iso_code %}";
      const newUrl= urlembed + '?period=' + period.value + '&year=' + year.value;
      window.location.href = newUrl;
      console.log(newUrl);
    }

    const onload =()=>{
      const period = document.getElementById('period');
      const year = document.getElementById('year');
      period.value = '{{period}}';
      year.value = '{{year}}';

    }

    const createCookie = (name, value) => {
      document.cookie = name + "=" + value + "; path=/";
    }

    const getCookie = (name) => {
      const cookieValue = document.cookie.match("(^|;)\\s*" + name + "\\s*=\\s*([^;]+)");
      return cookieValue ? cookieValue.pop() : "";
    }

    
     const onClickButtonTxT = () => {
        const period = "{{period}}";
        if (showTXT == false) {
          $('th.txt, td.txt, div.txt').hide();
          $(`th.txt.${period}, td.txt.${period}, div.txt.${period}`).show();
          showTXT = true;
        } else {
          $('th.txt, td.txt, div.txt').hide();
          showTXT = false;
        }
        
        createCookie("allsellersvat_show_txt", showTXT);
    }

    const cookie = getCookie("allsellersvat_show_txt");
        if(cookie == true || cookie == "true"){
          onClickButtonTxT();
        }  

    onload();
    
    

</script>
<!-- JQUERY DATATABLES -->



{% endblock javascripts %}