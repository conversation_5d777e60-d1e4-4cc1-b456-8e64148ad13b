from django.db import models
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model

from datetime import datetime

User = get_user_model()

class Schedule(models.Model):
    manager = models.OneToOneField(
        User, 
        on_delete=models.CASCADE,
        related_name="schedule_manager",
        verbose_name="<PERSON>estor",
        limit_choices_to={'role': 'manager'},
    )
    start_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="Hora de inicio",
        default="09:00",
        help_text="Hora de inicio de la jornada laboral"
    )
    end_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="Hora de fin",
        default="18:00",
        help_text="Hora de fin de la jornada laboral"
    )
    start_break_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="Hora de inicio de descanso",
    )
    end_break_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="Hora de fin de descanso",
    )
    buffer_time = models.CharField(
        choices=(
            ("1", "15 minutos"),
            ("2", "30 minutos"),
            ("3", "45 minutos"),
            ("4", "1 hora"),
        ),
        default="1",
        max_length=20,
        verbose_name="Tiempo entre llamadas",
        help_text="Este es el tiempo que tienes para prepararte para la siguiente llamada"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)
    class Meta:
        verbose_name = "Horario para llamadas"
        verbose_name_plural = "Horarios para llamadas"

    def __str__(self):
        return self.manager.username
    
    # get working hours in number of hours
    @property
    def get_working_hours(self):
        if self.start_time and self.end_time:
            start_t = datetime.combine(datetime.today(), self.start_time)
            end_t = datetime.combine(datetime.today(), self.end_time)
            working_hours_delta = end_t - start_t
            return working_hours_delta.total_seconds() / 3600
        return 0
    
    @property
    def get_break_time(self):
        if self.start_break_time and self.end_break_time:
            start_t = datetime.combine(datetime.today(), self.start_break_time)
            end_t = datetime.combine(datetime.today(), self.end_break_time)
            break_time_delta = end_t - start_t
            return break_time_delta.total_seconds() / 3600
        return 0

    
    # @admin.display(description="Horas de asistencia telefónica")
    def get_total_working_hours(self):
        working_hours = self.get_working_hours
        break_time = self.get_break_time
        if working_hours:
            return working_hours - break_time if break_time else working_hours
        return 0
    
    def clean(self):
        if self.start_time and self.end_time:
            if self.start_time >= self.end_time:
                raise ValidationError("La hora final debe ser mayor a la hora inicial")

        if self.start_break_time and self.end_break_time:
            if self.start_break_time >= self.end_break_time:
                raise ValidationError("La hora de inicio de descanso no puede ser mayor o igual que la hora de fin de descanso")
            
            # Calculate the total minutes of break time
            start_break_datetime = datetime.combine(datetime.today(), self.start_break_time)
            end_break_datetime = datetime.combine(datetime.today(), self.end_break_time)
            break_time_minutes = (end_break_datetime - start_break_datetime).total_seconds() / 60
            
            if break_time_minutes > 60:
                raise ValidationError("El tiempo de descanso no puede ser superior a 1 hora")
            
            # check that the break time is not outside the start and end time
            if self.start_time and self.end_time:
                if self.start_break_time < self.start_time or self.end_break_time > self.end_time:
                    raise ValidationError("El tiempo de descanso no puede estar fuera del intervalo del horario de trabajo")
                
        if self.start_break_time and not self.end_break_time:
            raise ValidationError("Si ingresaste la hora de inicio de comida, debes ingresar la hora de fin")
        
        if not self.start_break_time and self.end_break_time:
            raise ValidationError("Si ingresaste la hora de fin de comida, debes ingresar la hora de inicio")
        
        if not self.start_time and self.end_time:
            raise ValidationError("Si ingresaste la hora de fin, debes ingresar la hora de inicio")
        
        if self.start_time and not self.end_time:
            raise ValidationError("Si ingresaste la hora de inicio, debes ingresar la hora de fin")
    
