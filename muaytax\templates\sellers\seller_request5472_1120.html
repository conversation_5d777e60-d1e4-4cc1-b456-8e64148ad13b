{% extends "layouts/base.html" %}
{% load static i18n crispy_forms_field crispy_forms_filters crispy_forms_tags %}
{% block title %}
  {% blocktranslate with model_n='5472-1120' %}Model {{ model_n }}{% endblocktranslate %}
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <link href="{% static 'assets/css/select2/select2.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/m5472-1120/form.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/m5472-1120/newForm.css' %}" rel="stylesheet">
  <!-- FUENTE LETRA -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
  <!-- FUENTE LETRA -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/htmx/htmx.min-v1.6.0.js"></script>
  <link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">
  
  <!-- DATATABLES IMPORTATIONS -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables-v2.0.8.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v2.0.8.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.10.25.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css" type="text/css"/> 
  <!-- DATATABLES IMPORTATIONS -->
  <style>
    .tooltip-inner {
      max-width: 220px;
    }

    .choices_is_invalid {
      border-color: #dc3545 !important;
    }

    .choices_is_invalid:focus {
      border-color: #dc3545 !important;
      box-shadow: 0 0 0 .25rem rgba(220, 53, 69, .25) !important;
    }

    .choices_is_invalid::after {
      position: absolute;
      content: "";
      width: 18px;
      height: 18px;
      background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 12 12\' width=\'12\' height=\'12\' fill=\'none\' stroke=\'%23dc3545\'%3e%3ccircle cx=\'6\' cy=\'6\' r=\'4.5\'/%3e%3cpath stroke-linejoin=\'round\' d=\'M5.8 3.6h.4L6 6.5z\'/%3e%3ccircle cx=\'6\' cy=\'8.2\' r=\'.6\' fill=\'%23dc3545\' stroke=\'none\'/%3e%3c/svg%3e');
      background-repeat: no-repeat;
      background-size: contain;
      right: calc(1.5em + 0.75rem);
      top: 50%;
      transform: translateY(-50%);
    }

    .is-focused .choices_is_invalid,
    .is-open .choices_is_invalid {
      border-color: #dc3545 !important;
      box-shadow: 0 0 0 .25rem rgba(220, 53, 69, .25) !important;
    }
  </style>

{% endblock %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col mu-breadcrumb">
          <div class="page-header-title">
            <h5 class="m-b-10">
              {% blocktranslate with model_n='5472-1120' %}Model {{ model_n }}{% endblocktranslate %}
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="mu mu-home fw-mu-700"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="#">{% translate 'Declaraciones' %}</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">{% blocktranslate with model_n='5472-1120' %}Model {{ model_n }}{% endblocktranslate %}</a>
            </li>
          </ul>
        </div>

        <div class="col">
          <a class="btn_green dropdown-toggle" id="openVideoTutorial" style="float:right; font-size: 14px;"
             type="button">
            <svg width="22" height="22" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path id="play_icon" d="M9.699 6.64799L22.887 14.262C23.865 14.826 23.865 16.2375 22.887 16.8015L9.699 24.4155C8.721 24.9795 7.5 24.2745 7.5 23.145V7.91849C7.5 6.79049 8.7225 6.08399 9.699 6.64799Z" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10"/>
            </svg>
            Video tutorial
          </a>

        </div>
      </div>
      <iframe 
        id="tutorial" width="800" height="460"
        style="display: none; position: fixed; right:0; z-index: 9;"
        src="https://www.youtube.com/embed/B-VGYJQFxNE?rel=0&start=35"
        title="Video tutorial 5472-1120"
        frameborder="0"
        allow="autoplay; encrypted-media; picture-in-picture;"
        referrerpolicy="strict-origin-when-cross-origin"
        allowfullscreen
      ></iframe>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="card-body text_font">
    <div class="col-sm-12">
      <form class="" id="form5472-1120" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <!-- tabs -->
        <ul class="nav nav-tabs nav-tabs-custom-general" id="myTab" role="tablist">
          <li class="nav-item">
            <a class="nav-link active" id="general-tab" data-bs-toggle="tab" href="#general" role="tab"
               aria-controls="general" aria-selected="true" style="display: flex;">
              <i class="align_icon">
                <svg width="30" height="30" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M26.0098 18.25C28.9093 18.25 31.2598 15.8995 31.2598 13C31.2598 10.1005 28.9093 7.75 26.0098 7.75C23.1103 7.75 20.7598 10.1005 20.7598 13C20.7598 15.8995 23.1103 18.25 26.0098 18.25Z" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M30.7752 33.5L29.7652 44.2325C29.7377 44.5275 29.3902 44.7575 28.9677 44.7575H23.0477C22.6277 44.7575 22.2802 44.5275 22.2502 44.2325L21.2402 33.5" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M30.9001 33.5H32.0701C32.5226 33.5 32.8976 33.1525 32.9301 32.7025L33.5726 23.975C33.6226 23.3125 33.0351 22.75 32.2926 22.75H19.7251C18.9851 22.75 18.3976 23.3125 18.4451 23.975L19.0876 32.7025C19.1201 33.1525 19.4951 33.5 19.9476 33.5H20.9826" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                </svg>              
              </i>
              <span class="d-none d-md-inline nav-tab-font" style="align-content: center;">&nbsp;{% translate 'Información personal' %}</span>
              <i class="feather icon-alert-circle hidden"
                 style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="company-tab" data-bs-toggle="tab" href="#company" role="tab" aria-controls="company"
               aria-selected="false" style="display: flex;">
              <i class="align_icon">
                <svg width="30" height="30" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M40.7544 10.15H30.2964C29.3183 10.15 28.5254 10.9429 28.5254 11.921V22.379C28.5254 23.3571 29.3183 24.15 30.2964 24.15H40.7544C41.7325 24.15 42.5254 23.3571 42.5254 22.379V11.921C42.5254 10.9429 41.7325 10.15 40.7544 10.15Z" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M19.5786 44.1H9.12061C8.14251 44.1 7.34961 44.8929 7.34961 45.871V56.329C7.34961 57.3071 8.14251 58.1 9.12061 58.1H19.5786C20.5567 58.1 21.3496 57.3071 21.3496 56.329V45.871C21.3496 44.8929 20.5567 44.1 19.5786 44.1Z" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M40.7534 44.1H30.2954C29.3173 44.1 28.5244 44.8929 28.5244 45.871V56.329C28.5244 57.3071 29.3173 58.1 30.2954 58.1H40.7534C41.7315 58.1 42.5244 57.3071 42.5244 56.329V45.871C42.5244 44.8929 41.7315 44.1 40.7534 44.1Z" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M61.9282 44.1H51.4702C50.4921 44.1 49.6992 44.8929 49.6992 45.871V56.329C49.6992 57.3071 50.4921 58.1 51.4702 58.1H61.9282C62.9063 58.1 63.6992 57.3071 63.6992 56.329V45.871C63.6992 44.8929 62.9063 44.1 61.9282 44.1Z" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M14.3496 44.1V36.113C14.3496 35.112 15.1616 34.3 16.1626 34.3H54.8866C55.8876 34.3 56.6996 35.112 56.6996 36.113V44.1" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M35.5254 24.15V44.1" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                </svg>

              </i>
              <span class="d-none d-md-inline nav-tab-font" style="align-content: center;">&nbsp;{% translate 'Información de la Empresa' %}</span>
              <i class="feather icon-alert-circle hidden"
                 style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="reportable-tab" data-bs-toggle="tab" href="#reportable" role="tab"
               aria-controls="reportable" aria-selected="false" style="display: flex;">
              <i class="align_icon">
                <svg width="30" height="30" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.5918 56.9695H10.6783C7.84678 56.9695 5.55078 54.6735 5.55078 51.842V16.5935C5.55078 13.762 7.84678 11.4695 10.6783 11.4695H56.7733C59.6048 11.4695 61.9008 13.762 61.9008 16.5935V41.2125" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M5.55078 24.3075H61.9008" stroke="#031549" stroke-width="4" stroke-miterlimit="10"/>
                <path d="M12.5507 20.2195C13.7105 20.2195 14.6507 19.2793 14.6507 18.1195C14.6507 16.9597 13.7105 16.0195 12.5507 16.0195C11.3909 16.0195 10.4507 16.9597 10.4507 18.1195C10.4507 19.2793 11.3909 20.2195 12.5507 20.2195Z" fill="#031549"/>
                <path d="M19.2006 20.2195C20.3604 20.2195 21.3006 19.2793 21.3006 18.1195C21.3006 16.9597 20.3604 16.0195 19.2006 16.0195C18.0408 16.0195 17.1006 16.9597 17.1006 18.1195C17.1006 19.2793 18.0408 20.2195 19.2006 20.2195Z" fill="#031549"/>
                <path d="M25.851 20.2195C27.0108 20.2195 27.951 19.2793 27.951 18.1195C27.951 16.9597 27.0108 16.0195 25.851 16.0195C24.6912 16.0195 23.751 16.9597 23.751 18.1195C23.751 19.2793 24.6912 20.2195 25.851 20.2195Z" fill="#031549"/>
                <path d="M16.2715 48.16V37.66" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="square"/>
                <path d="M23.2715 48.16V32.06" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="square"/>
                <path d="M30.2715 48.16V41.16" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="square"/>
                <path d="M13.4717 48.51H33.7717" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M40.376 32.41H54.0715" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M66.276 49.9695C66.276 56.154 61.2605 61.1695 55.076 61.1695C50.512 61.1695 46.585 58.4395 44.842 54.5195C44.219 53.13 43.876 51.59 43.876 49.9695C43.876 43.7815 48.8915 38.7695 55.076 38.7695C56.763 38.7695 58.366 39.144 59.801 39.8125C63.623 41.594 66.276 45.4685 66.276 49.9695Z" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M55.272 54.243V55.594" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M55.272 44.3695V45.661" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M55.2717 50.015C56.4407 50.015 57.3892 50.9635 57.3892 52.1325C57.3892 53.3015 56.4407 54.25 55.2717 54.25C54.6487 54.25 54.0852 53.9805 53.7002 53.55" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M55.2718 50.015C54.1028 50.015 53.1543 49.0665 53.1543 47.8975C53.1543 46.7285 54.1028 45.78 55.2718 45.78C55.8878 45.78 56.4408 46.0425 56.8258 46.459" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                <path d="M40.376 39.06H47.6" stroke="#031549" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round"/>
                </svg>
              </i>
              <span class="d-none d-md-inline nav-tab-font" style="align-content: center;">&nbsp;{% translate 'Información contable' %}</span>
              <i class="feather icon-alert-circle hidden"
                 style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
            </a>
          </li>
          {% if not is_processed %}
            <li class="nav-item">
              <a class="nav-link" id="finish-tab" data-bs-toggle="tab" href="#finish" role="tab" aria-controls="finish"
                 aria-selected="false" style="display: flex;">
                <i class="align_icon">
                  <svg width="30" height="30" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M55.2897 28.1435C56.2032 30.6075 56.7002 33.271 56.7002 36.05C56.7002 48.615 46.5152 58.8 33.9502 58.8C21.3852 58.8 11.2002 48.615 11.2002 36.05C11.2002 23.485 21.3852 13.3 33.9502 13.3C39.7077 13.3 44.9647 15.4385 48.9722 18.963" stroke="#031549" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M64.4073 11.452L34.0238 41.8355L22.1553 29.967" stroke="#031549" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </i>
                <span class="d-none d-md-inline nav-tab-font" style="align-content: center;">&nbsp;{% translate 'Finish' %}</span>
                <i class="feather icon-alert-circle hidden"
                   style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
              </a>
            </li>
          {% endif %}
        </ul>
        <div class="tab-content" id="myTabContent">
          <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="25"
                 aria-valuemin="0" aria-valuemax="100" style="width: 25%"></div>
          </div>
          <!-- Info tab -->
          <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
            <div class="row">
              <div class='col'>
              </div>
              <div class="col-11" style="display: flex; justify-content: center;">
                <div class="card" style="box-shadow: none; width: 100%;">
                  <div class="card-header">
                    <h5>{% translate 'Datos generales del miembro' %}</h5>
                    <p style="margin-bottom:0; margin-top:10px;">{% translate 'Recuerda que aquí debes ingrear los datos correspondientes al Miembro' %}</p>
                  </div>
                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_general_info.html" %}
                  </div>
                  <div class="card-header">
                    <h5>{% translate 'Datos de dirección del miembro' %}</h5>
                  </div>

                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_general_address.html" %}
                  </div>
                  <div class="card-header">
                    <h5>{% translate 'Datos fiscales' %}</h5>
                  </div>
                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_general_fiscal_data.html" %}
                  </div>
                  <div class="card-footer d-flex justify-content-center">
                    <div class="row">
                      <div class="div_button_gap">
                        {% if not is_processed %}
                          <button type="button" class="btn_grey"
                                  name="save-submit"
                                  onclick="saveForm()">{% translate 'Guardar y seguir editando' %}</button>
                        {% endif %}
                        <button type="button" class="btn_dark_blue" onclick="changeTab('company-tab')">
                          {% translate 'Siguiente' %}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class='col'>
              </div>
            </div>
          </div>
          <!-- company tab -->
          <div class="tab-pane fade" id="company" role="tabpanel" aria-labelledby="company-tab">
            <div class="row">
              <!--CSS-->
              <div class='col'>
              </div>
              <div class="col-11" style="display: flex; justify-content: center;">
                <!--CSS-->
                <div class="card" style="box-shadow: none; width: 100%;">
                  <div class="card-header">
                    <h5>{% translate 'Datos de la Empresa' %}</h5>
                    <p style="margin-bottom:0; margin-top:10px;">{% translate 'Recuerda que aquí debes ingrear todos los datos correspondientes a la Empresa' %}</p>
                  </div>
                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_company_info.html" %}
                  </div>
                  <div class="card-header">
                    <h5>{% translate 'Dirección de la Empresa' %}</h5>
                  </div>
                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_company_address.html" %}
                  </div>
                  <div class="card-header">
                    <h5>{% translate 'Datos de país referente a la Empresa' %}</h5>
                  </div>
                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_company_country.html" %}
                  </div>
                  <div class="card-footer d-flex justify-content-center">
                    <div class="row">
                      <div class="div_button_gap">
                        <button type="button" class="back_btn" onclick="changeTab('general-tab')">
                          {% translate 'Anterior' %}
                        </button>
                        {% if not is_processed %}
                          <button type="button" class="btn_grey"
                                  name="save-submit"
                                  onclick="saveForm()">{% translate 'Guardar y seguir editando' %}</button>
                        {% endif %}
                        <button type="button" class="btn_dark_blue" onclick="changeTab('reportable-tab')">
                          {% translate 'Siguiente' %}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class='col'>
              </div>
            </div>
          </div>
          <!-- reportable tab -->
          <div class="tab-pane fade" id="reportable" role="tabpanel" aria-labelledby="reportable-tab">
            <div class="row">
              <!--CSS-->
              <div class='col'>
              </div>
              <div class="col-11" style="display: flex; justify-content: center;">
                <!--CSS--> 
                <div class="card" style="box-shadow: none; width: 100%;">
                  <div class="card-header">
                    <h5>{% translate 'Datos contables' %}</h5>
                  </div>
                  <div class="card-body section-card-body">
                    {% include "sellers/include/m54721120/seller_m54721120_reportable_info_assets.html" %}
                  </div>
                  <div class="card-body d-none" id="reportBody">
                    {% if not is_processed %}
                      <div class="d-flex" style="gap: 15px; margin-bottom: 15px;">
                        <button type="button" class="btn_dark_blue" id="import_accounting" style="display: flex;">
                          <i class="align_icon">
                            <svg width="23" height="23" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.1402 30.75H9.26771C7.86021 30.75 6.72021 29.5 6.72021 27.96V10.54C6.72021 9 7.86021 7.75 9.26771 7.75H40.9227C42.3302 7.75 43.4702 9 43.4702 10.54V27.96C43.4702 29.5 42.3302 30.75 40.9227 30.75H31.0127" stroke="#FFF" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                            <path d="M25.0322 16.1274C25.0922 22.7599 25.1022 36.4724 25.0622 43.0899" stroke="#FFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M25.1899 16.1274C27.5099 18.4024 29.7624 20.7399 32.0399 23.0549" stroke="#FFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M25.0175 16.1274C22.6925 18.4074 20.4325 20.7524 18.1475 23.0749" stroke="#FFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </i>
                          &nbsp;Importar excel
                        </button>
                        <a href="{% url 'app_sellers:download_excel' seller.shortname %}" class="back_btn" style="display: flex; width: auto;"
                          id="download_excel">
                          <i class="align_icon">
                            <svg width="23" height="23" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.1402 20.09H9.26771C7.86021 20.09 6.72021 21.34 6.72021 22.88V40.3C6.72021 41.84 7.86021 43.09 9.26771 43.09H40.9227C42.3302 43.09 43.4702 41.84 43.4702 40.3V22.88C43.4702 21.34 42.3302 20.09 40.9227 20.09H31.0127" stroke="#031549" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"/>
                            <path d="M25.0322 34.7125C25.0922 28.08 25.1022 14.3675 25.0622 7.75" stroke="#031549" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M25.1899 34.7125C27.5099 32.4375 29.7624 30.1 32.0399 27.785" stroke="#031549" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M25.0175 34.7125C22.6925 32.4325 20.4325 30.0875 18.1475 27.765" stroke="#031549" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </i>
                          &nbsp;Descargar plantilla excel
                        </a>
                      </div>
                    {% endif %}
                    <ul class="nav nav-tabs nav-tabs-custom" id="secondTab" role="tablist">
                      <li class="nav-item me-1 d-none">
                        <a class="nav-link" id="report1-tab" data-bs-toggle="tab" href="#report1" role="tab"
                           aria-controls="report1" aria-selected="true">
                          <span class="d-md-inline nav-tab-font">{% translate 'Ventas / Compras al miembro' %}</span>
                        </a>
                      </li>
                      <li class="nav-item d-none">
                        <a class="nav-link" id="report2-tab" data-bs-toggle="tab" href="#report2" role="tab"
                           aria-controls="report2" aria-selected="false">
                          <span class="d-md-inline nav-tab-font">{% translate 'Transacciones reportables' %}</span>
                          <i class="feather icon-alert-circle hidden"
                             style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
                        </a>
                      </li>
                    </ul>
                    <div class="separator-line"><!--linea de separacion --></div>
                    <div class="tab-content custom-tab-content" id="secondTabContent">
                      <!-- Report1 tab -->
                      <div class="tab-pane fade" id="report1" role="tabpanel" aria-labelledby="report1-tab">
                        <div class="row">
                          <div class="col-sm-12" style="padding: 0;">
                            {% include "sellers/include/m54721120/seller_m5472_reportable_info1.html" %}
                          </div>
                        </div>
                      </div>
                      <!-- Report2 tab -->
                      <div class="tab-pane fade" id="report2" role="tabpanel" aria-labelledby="report2-tab">
                        <div class="row">
                          <div class="col-sm-12" style="padding: 0;">
                            {% include "sellers/include/m54721120/seller_m5472_reportable_info2.html" %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-body section-card-body" id="noReporBody">
                    <div class="alert alert-warning-light-green text-center" role="alert">
                      <h5 class="alert-heading label-weight mb-0">No necesitas registrar transacciones</h5>
                    </div>
                  </div>
                  <div class="card-footer d-flex justify-content-center">
                    <div class="row">
                      <div class="div_button_gap">
                        <button type="button" class="back_btn" onclick="changeTab('company-tab')">
                          {% translate 'Anterior' %}
                        </button>
                        {% if not is_processed %}
                          <button type="button" class="btn_grey"
                                  name="save-submit"
                                  onclick="saveForm()">{% translate 'Guardar y seguir editando' %}</button>
                          <button type="button" class="btn_dark_blue" onclick="changeTab('finish-tab')">
                            {% translate 'Siguiente' %}
                          </button>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class='col'>
              </div>
            </div>
          </div>
          <!-- finish tab -->
          {% if not is_processed %}
            <div class="tab-pane fade" id="finish" role="tabpanel" aria-labelledby="finish-tab">
              <div class="row">
                <!--CSS-->
                <div class='col'>
                </div>
                <div class="col-11" style="display: flex; justify-content: center;">
                  <!--CSS-->
                  <div class="card" style="box-shadow: none; width: 100%">
                    {% include "sellers/include/m54721120/seller_m54721120_finish.html" %}
                    <div class="card-footer d-flex justify-content-center">
                      <div class="row">
                        <div class="div_button_gap">
                          <button type="button" class="back_btn" onclick="changeTab('reportable-tab')">
                            {% translate 'Anterior' %}
                          </button>
                          <button type="button" class="btn_grey"
                                  name="save-submit"
                                  onclick="saveForm()">{% translate 'Guardar y seguir editando' %}</button>
                          <button type="button" id="finalizarButton" class="btn_green"
                                  onclick="submitModel()">
                            {% translate 'Finish' %}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class='col'>
                </div>
              </div>
            </div>
          {% endif %}
        </div>
        <!-- modal confirmación -->
        <div class="modal fade" id="confirmForm" tabindex="-1" aria-labelledby="animateModalLabel" aria-modal="true"
             role="dialog">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-body text-center" style="padding: 40px 50px 40px 50px;">
                <div class="swal2-icon swal2-question swal2-icon-show mb-3" style="display: flex; margin-top: 0;">
                  <div class="swal2-icon-content" style="color: #ffca33 !important;">?</div>
                </div>
                <h5 class="finish-modal-font">
                  {% blocktranslate with model_n='5472-1120' %}
                    Do you want to submit the information for the model {{ model_n }}?
                  {% endblocktranslate %}
                </h5>
                <p class = "mb-0">{% translate "Before proceeding, make sure all the data is correct." %}</p>
                <div class="alert alert-warning-light-green d-none mb-0 mt-4" role="alert" id="reportAlert">
                  <h5 class="alert-heading nav-tab-font d-flex justify-content-center align-items-center" >
                    <i class="align_icon">
                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.9999 26.7C21.1303 26.7 26.0999 21.7304 26.0999 15.6C26.0999 9.46964 21.1303 4.5 14.9999 4.5C8.86954 4.5 3.8999 9.46964 3.8999 15.6C3.8999 21.7304 8.86954 26.7 14.9999 26.7Z" stroke="#031549" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15.1499 18.9V9.59998" stroke="#031549" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15.15 22.2C15.6471 22.2 16.05 21.7971 16.05 21.3C16.05 20.803 15.6471 20.4 15.15 20.4C14.6529 20.4 14.25 20.803 14.25 21.3C14.25 21.7971 14.6529 22.2 15.15 22.2Z" fill="#031549"/>
                        </svg>
                    </i>
                    &nbsp;Importante</h5>
                  <p>No has registrado transacciones en algún reporte. Si esto es correcto continúa con el envío, de lo
                    contrario dirigete a la pestaña de información contable</p>
                </div>

              </div>
              <div class="modal-footer justify-content-center" style="padding-bottom: 40px;">
                <button type="button" class="btn_dark_blue"
                        data-bs-dismiss="modal">{% translate 'No, back' %}</button>
                <button type="button" id="sign-m5472-1120" name="proccess-sign"
                        class="btn_green" data-bs-toggle="modal" data-bs-target="#signModal">Si, firmar</button>
              </div>
            </div>
          </div>
        </div>
        <!-- modal sign -->
        {% include "sellers/include/m54721120/seller_m54721120_modal_sign.html" %}
      </form>
    </div>
  </div>
  {% include "sellers/include/m54721120/modal_add_account_record1.html" %}
  {% include "sellers/include/m54721120/modal_add_account_record2.html" %}
  {% if not is_processed %}
    <form id="upload_form"
          hx-trigger="submit"
          hx-post="{% url 'app_sellers:process_excel' seller.shortname %}"
          hx-target="#temporal"
          hx-swap="innerHTML"
          hx-encoding="multipart/form-data"
          hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
          style="display: none"
    >
    </form>
    <div id="temporal" style="display: none"></div>
  {% endif %}
  <div class="modal fade modal-animate anim-blur " id="LoadingModal" tabindex="-1" role="dialog"
       aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
        <div class="modal-body d-flex flex-column justify-content-center align-items-center">
          <div class="modal-body">
            <div class="d-flex justify-content-center align-items-center text-center">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <p>&nbsp;</p>
              <img style="width:110px;" src="{% static 'assets/images/logo.png' %}"/>
            </div>
            <p class="text-white text-center mb-0">
              <b>{% translate 'The information is sending. Do not close or refresh the page' %}</b>
            </p>
            <p class="text-white text-center mb-0"><b>{% translate 'Please wait...' %}</b></p>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <!--GLOBAL VARS-->
  <script>
    let model5472PresentationYear = parseInt("{{model5472PresentationYear}}");
    let m54721120_instance = "{{pm5472_1120_instance}}";
    let previous_pm5472 = "{{previous_pm5472}}"; 
    let url_zip = "{% url 'app_documents:request_zip_us' seller.shortname %}"
    const limitRecords = "{{ limit_records }}" === "True";
  </script>

  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-ui.min-v1.13.1.js"></script>
  <script src="{% static 'assets/js/select2/select2.min.js' %}"></script>
  <script src="{% static 'assets/js/m5472-1120/form.js' %}"></script>
  <script src="{% static 'assets/js/plugins/choices.min.js' %}"></script>
  <script src="{% static 'assets/js/plugins/imask.min.js' %}"></script>
  <script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>

  <!-- DATATABLES IMPORTATIONS -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <!-- DATATABLES IMPORTATIONS -->
  <script>
    let is_processed = "{{ is_processed }}";
    
    //#### TABLES DELCRARATIONS ####
    let tableRecords1 = null;
    let arrayRowData1 = [];
    let totalTable1 = 0;

    let tableRecords2 = null;
    let arrayRowData2 = [];
    let totalTable2 = 0;

    document.getElementById('openVideoTutorial').addEventListener('click', function () {
      const tutorial = document.getElementById('tutorial');
    
      if (tutorial.style.visibility === 'hidden' || tutorial.style.visibility === '') {
        tutorial.style.visibility = 'visible';
        tutorial.style.display = 'block';
    
        document.getElementById('openVideoTutorial').classList.remove('dropdown-toggle');
        document.getElementById('openVideoTutorial').innerHTML = '<i class="feather icon-x"></i> Cerrar video tutorial';
      } else {
        tutorial.style.visibility = 'hidden';
        tutorial.style.display = 'none';
    
        // Detener el video al cerrar
        tutorial.src = tutorial.src;
    
        document.getElementById('openVideoTutorial').classList.add('dropdown-toggle');
        document.getElementById('openVideoTutorial').innerHTML = `<svg width="22" height="22" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path id="play_icon" d="M9.699 6.64799L22.887 14.262C23.865 14.826 23.865 16.2375 22.887 16.8015L9.699 24.4155C8.721 24.9795 7.5 24.2745 7.5 23.145V7.91849C7.5 6.79049 8.7225 6.08399 9.699 6.64799Z" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10"/>
                                                                  </svg> Video tutorial`;
      }
    });
    
    
    

    (function () {
      const datepickerElements = document.querySelectorAll('#id_date');
      datepickerElements.forEach(element => {
        const d_week = new Datepicker(element, {
          buttonClass: 'btn',
          format: 'yyyy-mm-dd',
          closeOnSelect: true,
          orientation: 'bottom',
          showOnFocus: true,
          maxDate: new Date(model5472PresentationYear, 11, 31),
          minDate: new Date(model5472PresentationYear, 0, 1),
          autohide: true,

        });
      });
    })();

    (function () {
      const datepickerElements = document.querySelectorAll('#id_incorporation_date');
      datepickerElements.forEach(element => {
        const d_week = new Datepicker(element, {
          buttonClass: 'btn',
          format: 'yyyy-mm-dd',
          closeOnSelect: true,
          orientation: 'bottom',
          showOnFocus: true,
          maxDate: new Date(model5472PresentationYear, 11, 31),
          autohide: true,

        });
        element.addEventListener('changeDate', function (e) {
          if (element.checkValidity()) {
            $('#id_incorporation_date').trigger('change');
            element.classList.remove('is-invalid');
            const errorElement = element.parentElement.querySelector('.invalid-feedback');
            if (errorElement) {
              errorElement.remove();
            }
            addHiddenInTab();
          }
        });

      });
    })();

    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer);
        toast.addEventListener('mouseleave', Swal.resumeTimer);
      }
    });

    $(document).ready(function () {
      const saveAndEdit = "{{ save_edit|default:'False' }}";
      const submitForm = "{{ submit5472|default:'False' }}";

      if (saveAndEdit === 'True') {
        Toast.fire({
          icon: 'success',
          title: 'Formulario guardado correctamente'
        });
      }
      if (submitForm === 'True') {
        Swal.fire({
          title: 'Form 5472 1120 procesado correctamente.',
          icon: 'success',
          showCancelButton: false,
          confirmButtonText: 'Aceptar',
          customClass: {
            confirmButton: 'btn_green',
          },
        });
      }
      const countryRegistration = document.getElementById('id_country_registration');
      if (countryRegistration) {
        countryRegistration.disabled = true;
      }

      const einInput = document.getElementById('id_ein');
      if (einInput) {
        var einInputMask = new IMask(einInput, {
          mask: '00-0000000'
        });
      }

      const infoTransBtn = document.getElementById('infoTransaccionBtn1');
      const infoTransBtn2 = document.getElementById('infoTransaccionBtn2');

      const infoDiv = document.getElementById('infoTransaccion1');
      const infoDiv2 = document.getElementById('infoTransaccion2');

      if (infoTransBtn) {
        infoTransBtn.addEventListener('mouseenter', function () {
          infoDiv.classList.remove('d-none');
        });
        infoTransBtn.addEventListener('mouseleave', function () {
          infoDiv.classList.add('d-none');
        });
      }

      if (infoTransBtn2) {
        infoTransBtn2.addEventListener('mouseenter', function () {
          infoDiv2.classList.remove('d-none');
        });
        infoTransBtn2.addEventListener('mouseleave', function () {
          infoDiv2.classList.add('d-none');
        });
      }

      const uploadForm = document.getElementById('upload_form');
      const importExcelBtn = document.getElementById('import_accounting');
      if (importExcelBtn) {
        importExcelBtn.addEventListener('click', () => {
          {#mostrar la primera vez que de clic un modal que alerte al usuario de que no suba #}
          {#dos veces el mismo excel para evitar duplicados#}
          Swal.fire({
            html: `
                    <div class="swal2-icon-content" style="padding-bottom: 10px;">
                      <svg width="70" height="70" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14.9999 26.4C21.1303 26.4 26.0999 21.4303 26.0999 15.3C26.0999 9.16959 21.1303 4.19995 14.9999 4.19995C8.86954 4.19995 3.8999 9.16959 3.8999 15.3C3.8999 21.4303 8.86954 26.4 14.9999 26.4Z" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M15.2998 11.4V20.7" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M13.0498 21.15H17.3998" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M13.3496 11.4H15.2996" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M14.9996 8.89998C15.4967 8.89998 15.8996 8.49703 15.8996 7.99998C15.8996 7.50292 15.4967 7.09998 14.9996 7.09998C14.5026 7.09998 14.0996 7.50292 14.0996 7.99998C14.0996 8.49703 14.5026 8.89998 14.9996 8.89998Z" fill="#0083E3"/>
                      </svg>
                    </div>
                  <div class="swal2-title-text ">Asegúrate de no importar dos veces el mismo excel para evitar el ingreso de datos duplicados.</div>
                  `,
            showCancelButton: true,
            confirmButtonText: 'Seleccionar archivo',
            cancelButtonText: 'Cancelar',
            customClass: {
            confirmButton: 'btn-excel-confirm',
            cancelButton: 'btn-swal-cancel',
          },
          }).then((result) => {
            if (result.isConfirmed) {
              const fileInput = document.createElement('input');
              fileInput.type = 'file';
              fileInput.name = 'excel';
              fileInput.accept = '.xlsx, .xls';
              fileInput.style.display = 'none';
              fileInput.onchange = (e) => {
                // Si no se selecciona ningún archivo, no se hace nada
                if (e.target.files.length === 0) {
                  return;
                }

                if (uploadForm.checkValidity()) { // Verifica la validez del formulario
                  // añade informacion adicional al formulario a enviar

                  const presentation_year = model5472PresentationYear;
                  const presentationYearInput = document.createElement('input');
                  presentationYearInput.type = 'hidden';
                  presentationYearInput.name = 'presentation_year';
                  presentationYearInput.value = presentation_year;

                  const elem1 = document.createElement('input');
                  elem1.type = 'hidden';
                  elem1.name = 'tax_residence_country';
                  elem1.value = document.getElementById('id_tax_residence_country').value;

                  const elem2 = document.createElement('input');
                  elem2.type = 'hidden';
                  elem2.name = 'is_sl_self_employed';
                  elem2.value = document.getElementById('id_is_sl_self_employed_0').checked ? 'True' : 'False';

                  upload_form.appendChild(presentationYearInput);
                  uploadForm.appendChild(elem1);
                  uploadForm.appendChild(elem2);
                  uploadForm.appendChild(fileInput);

                  uploadForm.requestSubmit(); // Envia el formulario si es válido

                  // elimina la información adicional del formulario
                  uploadForm.removeChild(uploadForm.lastChild);
                  uploadForm.removeChild(uploadForm.lastChild);
                  uploadForm.removeChild(uploadForm.lastChild);
                  Swal.fire({
                  //title: 'Enviando excel, por favor espere...',
                  //icon: 'info',
                  html: `
                    <div class="swal2-icon-content" style="padding-bottom: 10px;">
                      <svg width="70" height="70" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14.9999 26.4C21.1303 26.4 26.0999 21.4303 26.0999 15.3C26.0999 9.16959 21.1303 4.19995 14.9999 4.19995C8.86954 4.19995 3.8999 9.16959 3.8999 15.3C3.8999 21.4303 8.86954 26.4 14.9999 26.4Z" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M15.2998 11.4V20.7" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M13.0498 21.15H17.3998" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M13.3496 11.4H15.2996" stroke="#0083E3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M14.9996 8.89998C15.4967 8.89998 15.8996 8.49703 15.8996 7.99998C15.8996 7.50292 15.4967 7.09998 14.9996 7.09998C14.5026 7.09998 14.0996 7.50292 14.0996 7.99998C14.0996 8.49703 14.5026 8.89998 14.9996 8.89998Z" fill="#0083E3"/>
                      </svg>
                    </div>
                  <div class="swal2-title-text ">Enviando excel, por favor espere...</div>
                  `,
                  allowOutsideClick: false,  
                  didOpen: () => {
                      Swal.showLoading();  
                  }
                });
                } else {
                  uploadForm.reportValidity(); // Opcional: muestra los mensajes de error de validación
                }
              };
              fileInput.click();
            }
          });
        });
      }

      //Exoprtar excel
      document.body.addEventListener('htmx:afterRequest', (e) => {
        if (e.detail.elt.id === 'upload_form') {
          const xhr = e.detail.xhr;
          if (xhr && xhr.status === 200) {
            let response = JSON.parse(xhr.response);
            if (m54721120_instance == null || m54721120_instance == 'None') {
              m54721120_instance = response.pm5472_1120_instance;
            }
            tableRecords1.ajax.reload();
            tableRecords2.ajax.reload();

            Toast.fire({
              //time: 5000,
              timer:0,
              icon: 'success',
              title: 'Excel importado correctamente',
            });
          } else {
            const resp = JSON.parse(xhr.response);
            let message = '';
            for (let elem in resp.excel) {
              message += '<p>' + resp.excel[elem] + '</p>';
            }
            Toast.fire({
              time: 5000,
              icon: 'error',
              title: 'Error al importar excel',
              html: message,
              customClass:{
                popup: 'swal2-excel-error'
              }
            });
          }
        }
      });

      const checkEvents = (id) => {
        const checkAll = document.getElementById('checkAll_' + id);
        const checkboxes = document.querySelectorAll('input[data-id="' + id + '"]');

        if (checkAll) {
          checkAll.onclick = (e) => {
            checkboxes.forEach((c) => c.checked = e.target.checked);
            const checked = Array.from(checkboxes).filter((checkbox) => checkbox.checked);
            const delete_accounting = document.getElementById('delete_accounting_' + id);
            delete_accounting.style.display = checked.length === 0 ? 'none' : 'inline-block';
          }
          checkboxes.forEach((c) => c.onclick = (e) => {
            const checkAll = document.getElementById('checkAll_' + id);
            const checkboxes = document.querySelectorAll('input[data-id="' + id + '"]');
            const delete_accounting = document.getElementById('delete_accounting_' + id);

            const checked = Array.from(checkboxes).filter((checkbox) => checkbox.checked);
            checkAll.checked = checkboxes.length > 0 ? checked.length === checkboxes.length : false;
            checkAll.indeterminate = checked.length > 0 && checked.length < checkboxes.length;
            delete_accounting.style.display = checked.length === 0 ? 'none' : 'inline-block';
          });
        }
      };
      checkEvents('1');
      checkEvents('2');
    });

    document.getElementById('form5472-1120').addEventListener("keydown", function (e) {
      if (e.key === "Enter") {
        if (e.target.tagName === "TEXTAREA") {
          return;
        }
        e.preventDefault();
        var inputs = Array.prototype.slice.call(document.querySelectorAll('input, select, textarea'));
        var index = inputs.indexOf(document.activeElement);
        if (index !== -1 && index < inputs.length - 1) {
          inputs[index + 1].focus();
        }
      }
    });


//################## ACCOUNTING RECORDS ##################

    //Function confirmation delete accounting record
    const confirmDeleteAccountingRecord = (element, data) =>{
      let arrayRowData = [];

      if (data == 'data1'){
        arrayRowData = arrayRowData1;
      } else if (data == 'data2'){
        arrayRowData = arrayRowData2;
      }

      Swal.fire({
              html: arrayRowData.length > 1 ? 
                `
                  <div class="swal2-icon-content" style="padding-bottom: 10px;">
                    <svg width="71" height="70" viewBox="0 0 71 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M35.5016 62.3C49.8057 62.3 61.4016 50.7042 61.4016 36.4C61.4016 22.0958 49.8057 10.5 35.5016 10.5C21.1974 10.5 9.60156 22.0958 9.60156 36.4C9.60156 50.7042 21.1974 62.3 35.5016 62.3Z" stroke="#E0364C" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M35.8516 44.0999V22.3999" stroke="#E0364C" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M35.85 51.8001C37.0098 51.8001 37.95 50.8599 37.95 49.7001C37.95 48.5403 37.0098 47.6001 35.85 47.6001C34.6902 47.6001 33.75 48.5403 33.75 49.7001C33.75 50.8599 34.6902 51.8001 35.85 51.8001Z" fill="#E0364C"/>
                    </svg>
                  </div>
                  <div class="swal2-title-text ">¿Estás seguro de eliminar los registros seleccionados?</div>
                  `
                  :
                  `
                  <div class="swal2-icon-content" style="padding-bottom: 10px;">
                    <svg width="71" height="70" viewBox="0 0 71 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M35.5016 62.3C49.8057 62.3 61.4016 50.7042 61.4016 36.4C61.4016 22.0958 49.8057 10.5 35.5016 10.5C21.1974 10.5 9.60156 22.0958 9.60156 36.4C9.60156 50.7042 21.1974 62.3 35.5016 62.3Z" stroke="#E0364C" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M35.8516 44.0999V22.3999" stroke="#E0364C" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M35.85 51.8001C37.0098 51.8001 37.95 50.8599 37.95 49.7001C37.95 48.5403 37.0098 47.6001 35.85 47.6001C34.6902 47.6001 33.75 48.5403 33.75 49.7001C33.75 50.8599 34.6902 51.8001 35.85 51.8001Z" fill="#E0364C"/>
                    </svg>
                  </div>
                  <div class="swal2-title-text ">¿Estás seguro de eliminar el resgistro?</div>
                  `
                  ,
              showCancelButton: true,
              confirmButtonText: 'Sí, eliminar',
              cancelButtonText: 'Cancelar',
              customClass: {
              confirmButton: 'btn-delete-report-confirm',
              cancelButton: 'btn-swal-cancel',
            },
          }).then((result) => {
              if (result.isConfirmed) {
                  updateAccountingData(element, arrayRowData, data);
              }
      });
    }
  
    //Functioan async delete accounting record
    const updateAccountingData = async (element, arrayRowData, data) =>{
      Swal.fire({
        title: 'Eliminando, por favor espere...',
        icon: 'info',
        allowOutsideClick: false,  
        didOpen: () => {
            Swal.showLoading();  
        }
      });
      try{
          let response = await fetch("{% url 'app_documents:delete_report_data' seller.shortname %}", {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                'data': element != null ? [element] : arrayRowData, //Envía los PK que se desea borrar, puede ser un elemento o un array
            })
          });
          if (response.ok){
            if (data == 'data1'){
              tableRecords1.ajax.reload();
              $('#select_all_table1').prop("checked", false)
            } else if (data == 'data2'){
              tableRecords2.ajax.reload();
              $('#select_all_table2').prop("checked", false)
            }
            Toast.fire({
              time: 5000,
              icon: 'success',
              title: 'Eliminado correctamente',
        });
          }
      }catch (error) {
        Toast.fire({
              time: 5000,
              icon: 'error',
              title: 'Error contacta con soporte',
        });
      }
    }

    //Function that verifies the sum of the records in the tables and hides buttons if the total limit is 10
    const verifySumRecords = () =>{
      if(totalTable1 + totalTable2 >= 10){
        $('#add_accounting_1').hasClass('d-none') ? null : $('#add_accounting_1').addClass('d-none');
        $('#add_accounting_2').hasClass('d-none') ? null : $('#add_accounting_2').addClass('d-none');
      }else{
        $('#add_accounting_1').hasClass('d-none') ? $('#add_accounting_1').removeClass('d-none') : null;
        $('#add_accounting_2').hasClass('d-none') ? $('#add_accounting_2').removeClass('d-none') : null;
      }
    }

    // Formating the date
    const formatDate = (dateString) =>{
        if (!dateString) {
            return '--';
        }
        let [year, month, day] = dateString.split('-');
        const months = [
            'ene', 'feb', 'mar', 'abr', 'may', 'jun',
            'jul', 'ago', 'sep', 'oct', 'nov', 'dic'
        ];
        month = months[parseInt(month, 10) - 1];

        return `${day}/${month}/${year}`;
    }

  //########## DATATABLE1 "Ventas / Compras al miembro" ##########
  $(document).ready(function () {
    let timer = null;
    let filtro1 = '';

    $("#search1").on("input", function () {
      filtro1 = $(this).val();
      tableRecords1.search(filtro1).draw();
    });

    const ajaxData = (d) =>{

      let input = filtro1;
      if(input){
        d.search = input;
      }

      d.m54721120_instance = m54721120_instance;

      d.transaction_type = [1, 2, 3, 4];

      return d;
    }

    tableRecords1 = $('#recordsTable1').DataTable({
      "processing": false,
      //descomentar si se quiere hacer paginación asíncrona
      //"serverSide": true, 
      "ajax": {
        "url": "{% url 'app_documents:report_data_list_DT' seller.shortname %}",
        "type": "GET",
          "dataSrc": 'data',
            data: function (d) {
              return ajaxData(d);
            }
      },
      "select": {"style": 'multi', "selector": 'custom-checkbox'},
      "columns": [
        {
          "data": null,
          "className": 'select-checkbox miId',
          "orderable": false,
          "render": function (data, type, full, meta) {
            if (type === 'display') {
              return '<input type="checkbox" class="custom-checkbox" data-select="true" id="miId">';
            }
            return data;
          }
        },
        {
          data: 'date',
          render: function(data, type, row) {
              return formatDate(data);
          }
        },
        {"data": "transaction_type"},
        {"data": "description"},
        {"data": "amount"},
        {"data": "currency"},
        {
          data: null,
          render: function(data, type, row) {
              return `
                    <a 
                      onclick="confirmDeleteAccountingRecord(${row.pk}, 'data1');"
                      data-record-pk="${row.pk}"
                      class="text-danger f-14 me-2" 
                      data-bs-toggle="tooltip" 
                      data-bs-placement="top" 
                      title="Eliminar" 
                      role="button">
                      <svg width="30" height="31" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_437_3515)">
                        <rect y="0.284302" width="30" height="30" rx="5" fill="#F7FAFF"/>
                        <path d="M19.016 24.1078H10.9842C10.6618 24.1078 10.3948 23.8561 10.3724 23.5325L9.51714 11.2419C9.49243 10.8843 9.77361 10.5796 10.1289 10.5796H19.8713C20.2277 10.5796 20.5089 10.8843 20.483 11.2419L19.6277 23.5325C19.6054 23.8572 19.3383 24.1078 19.016 24.1078Z" stroke="#E0364C" stroke-width="1.7" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M9 8.9314H21" stroke="#E0364C" stroke-width="1.7" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M12.6484 7.2843H17.3543" stroke="#E0364C" stroke-width="1.7" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M17.7054 13.402L17.1172 21.6373" stroke="#E0364C" stroke-width="1.7" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M12.293 13.402L12.8812 21.6373" stroke="#E0364C" stroke-width="1.7" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M15 13.2843V21.6372" stroke="#E0364C" stroke-width="1.7" stroke-miterlimit="10" stroke-linecap="round"/>
                        </g>
                        <rect x="1" y="1.2843" width="28" height="28" rx="4" stroke="#E0364C" stroke-width="2"/>
                        <defs>
                        <clipPath id="clip0_437_3515">
                        <rect y="0.284302" width="30" height="30" rx="5" fill="white"/>
                        </clipPath>
                        </defs>
                      </svg>
                  </a>
                      `;
          },
          orderable: false, 
          searchable: false,
        },
      ],
      "order": [] ,
      "lengthMenu": [[50, 100, 200, -1], [50, 100, 200, 'Todos']],
      "paging": true,
      "searching": true,
      "dom": 'Bfrtip',
      "language": {
          "lengthMenu": "_MENU_",
          "zeroRecords": "No se han encontrado datos contables.",
          "info": "_TOTAL_ resultados. ",
          "search": "Buscar:",
          "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
          "infoFiltered": "",
          "paginate": {
              "first":      "Primero",
              "last":       "Último",
              "next":       "Siguiente",
              "previous":   "Anterior"
          },
          "select": {
              "rows": {
                  _: "Has seleccionado %d registros", 
                  0: "", 
                  1: "Has seleccionado 1 registro" 
              }
          },
      },
      "columnDefs": [
        {
          "targets": [0, -1], // Ocultar columnas de acciones cuando el form está procesado
          "visible": "{{ is_processed }}" == "True" ? false : true,
          "searchable": false
        }
      ],
      drawCallback: function (settings) {
          $('[data-bs-toggle="tooltip"]').tooltip();
          
          $('#select_all').prop("checked", false);
          arrayRowData1 = [];

          if (arrayRowData1.length == 0) {
            $('#delete_accounting_several1').hasClass('d-none') ? null : $('#delete_accounting_several1').addClass('d-none');
          }

          // Sumar los valores para obtener el total
          data = settings.json;
          let total = 0.0;
          if(data){
            data = data.data;
            data.forEach(element => {
              total += element.total_currency !==null ? parseFloat(element.total_currency) : parseFloat(element.amount);
            });
          }
          $('.companyAssets').val(total.toFixed(2));

        },
      
    });

    //Select row by click
    $('#recordsTable1').on('click', 'input.custom-checkbox[data-select="true"]', function (e) {
      e.stopPropagation(); // Evita que el clic se propague
      let row = $(this).closest('tr');
      let dataTable = $('#recordsTable1').DataTable();

      if (dataTable.row(row).nodes().to$().hasClass('selected')) {
        dataTable.row(row).deselect();
      } else {
        dataTable.row(row).select();
      }
    });
    
    //Select all rows
    $('#select_all_table1').on('change', function () {
      let isChecked = $(this).prop('checked');
      let rows = tableRecords1.rows({page: 'current'}).nodes();
      // Selecciona o deselecciona todas las filas según el estado de la casilla
      $(rows).find('input[type="checkbox"]').prop('checked', isChecked);

      // Dispara el evento 'select.dt' después de seleccionar todas las filas
      if (isChecked) {
        tableRecords1.rows({page: 'current'}).select();
      } else {
        tableRecords1.rows({page: 'current'}).deselect();
      }tableRecords1
    });

    // Select rows and add to arrayRowData1
    $('#recordsTable1').on('select.dt', function (e, dt, type, indexes) {
      let rowData = dt.rows(indexes).data().toArray();

      rowData.forEach(row => {
        if (!arrayRowData1.includes(row.pk)) {
            arrayRowData1.push(row.pk);
        }
      });

      if (arrayRowData1.length > 0) {
        $('#delete_accounting_several1').removeClass("d-none");
      }
      
    });

    // Deselect rows when detele from arrayRowData1
    $('#recordsTable1').on('deselect.dt', function (e, dt, type, indexes) {
      let rowDataDelete = dt.rows(indexes).data().toArray();

      arrayRowData1 = arrayRowData1.filter(row => !rowDataDelete.some(r => r.pk === row));
      if (arrayRowData1.length == 0) {
        $('#delete_accounting_several1').addClass("d-none");
      }
    });

    // deselect all rows whene change page
    $('#recordsTable1').on('page.dt', function() {
      $('#select_all_table1').prop("checked", false); //Uncheck select all

      let rows = tableRecords1.rows().nodes();  //Uncheck all checkboxes
      $(rows).find('input[type="checkbox"]').prop('checked', false);
      tableRecords1.rows().deselect();

      arrayRowData1 = []
    });


  });

  //######### DATATABLE2 "Transacciones reportables" ##########
  $(document).ready(function (){

    let filtro2 = '';

    $("#search2").on("input", function () {
        filtro2 = $(this).val();
        tableRecords2.search(filtro2).draw();
    });

    const ajaxData = (d) =>{

      let input = filtro2;
      if(input){
        d.search = input;
      }

      d.transaction_type = [5, 6, 7, 8, 9];

      d.m54721120_instance = m54721120_instance;

      return d;
    }

    tableRecords2 = $('#recordsTable2').DataTable({
      processing: false,
      //descomentar si se quiere hacer paginación asíncrona
      //serverSide: true,
      "ajax": {
        "url": "{% url 'app_documents:report_data_list_DT' seller.shortname %}",
        "type": "GET",
          "dataSrc": 'data',
            data: function (d) {
              return ajaxData(d);
            }
      },
      "select": {"style": 'multi', "selector": 'custom-checkbox'},
      "columns": [
        {
          "data": null,
          "className": 'select-checkbox miId',
          "orderable": false,
          "render": function (data, type, full, meta) {
            if (type === 'display') {
              return '<input type="checkbox" class="custom-checkbox" data-select="true" id="miId">';
            }
            return data;
          }
        },
        {
          data: 'date',
          render: function(data, type, row) {
              return formatDate(data);
          }
        },
        {"data": "transaction_type"},
        {"data": "description"},
        {"data": "amount"},
        {"data": "currency"},
        {
          data: null,
          render: function(data, type, row) {
              return `
                    <a 
                      onclick="confirmDeleteAccountingRecord(${row.pk}, 'data2');"
                      data-record-pk="${row.pk}"
                      class="text-danger f-14 me-2" 
                      data-bs-toggle="tooltip" 
                      data-bs-placement="top" 
                      title="Eliminar" 
                      role="button">
                      <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" style="background-color: white;
                        border: solid 2px #E0364C;
                        border-radius: 5px;
                        padding-bottom: 2px;">
                        <path d="M19.9708 26.7H9.73034C9.31934 26.7 8.97884 26.379 8.95034 25.9665L7.85984 10.296C7.82834 9.83998 8.18684 9.45148 8.63984 9.45148H21.0613C21.5158 9.45148 21.8743 9.83998 21.8413 10.296L20.7508 25.9665C20.7223 26.3805 20.3818 26.7 19.9708 26.7Z" stroke="#E0364C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M7.2002 7.34998H22.5002" stroke="#E0364C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M11.8501 5.25H17.8501" stroke="#E0364C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M18.2998 13.05L17.5498 23.55" stroke="#E0364C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M11.3999 13.05L12.1499 23.55" stroke="#E0364C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                        <path d="M14.8501 12.9V23.55" stroke="#E0364C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                      </svg>
                    </a>
                      `;
          },
          orderable: false, 
          searchable: false,
        },
      ],
      "order": [] ,
      "lengthMenu": [[50, 100, 200, -1], [50, 100, 200, 'Todos']],
      "paging": true,
      "searching": true,
      "dom": 'Bfrtip',
      "language": {
          "lengthMenu": "_MENU_",
          "zeroRecords": "No se han encontrado datos contables.",
          "info": "_TOTAL_ resultados. ",
          "search": "Buscar:",
          "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
          "infoFiltered": "",
          "paginate": {
              "first":      "Primero",
              "last":       "Último",
              "next":       "Siguiente",
              "previous":   "Anterior"
          },
          "select": {
              "rows": {
                  _: "Has seleccionado %d registros", 
                  0: "", 
                  1: "Has seleccionado 1 registro" 
              }
          },
      },
      "columnDefs": [
        {
          "targets": [0, -1], // Ocultar columnas de acciones cuando el form está procesado
          "visible": "{{ is_processed }}" == "True" ? false : true,
          "searchable": false
        }
      ],
      drawCallback: function (settings) {
          $('[data-bs-toggle="tooltip"]').tooltip();
          
          $('#select_all').prop("checked", false);
          arrayRowData2 = [];
          if (arrayRowData2.length == 0) {
            $('#delete_accounting_several2').hasClass('d-none') ? null : $('#delete_accounting_several2').addClass('d-none');
          }


          // Sumar los valores para obtener el total
          data = settings.json;
          let total = 0.0;
          if(data){
            data = data.data;
            data.forEach(element => {
              total += element.total_currency !==null ? parseFloat(element.total_currency) : parseFloat(element.amount);
            });
          }
          $('.reportRecords').val(total.toFixed(2));

        
        },
    });

    //Select row by click
    $('#recordsTable2').on('click', 'input.custom-checkbox[data-select="true"]', function (e) {
      e.stopPropagation(); // Evita que el clic se propague
      let row = $(this).closest('tr');
      let dataTable = $('#recordsTable2').DataTable();

      if (dataTable.row(row).nodes().to$().hasClass('selected')) {
        dataTable.row(row).deselect();
      } else {
        dataTable.row(row).select();
      }
    });

    //Select all rows
    $('#select_all_table2').on('change', function (){
      let isChecked = $(this).prop('checked');
      let rows = tableRecords2.rows({page: 'current'}).nodes();
      // Selecciona o deselecciona todas las filas según el estado de la casilla
      $(rows).find('input[type="checkbox"]').prop('checked', isChecked);

      // Dispara el evento 'select.dt' después de seleccionar todas las filas
      if (isChecked) {
        tableRecords2.rows({page: 'current'}).select();
      } else {
        tableRecords2.rows({page: 'current'}).deselect();
      }
    });

    //Select rows and add to arrayRowData2
    $('#recordsTable2').on('select.dt', function (e, dt, type, indexes) {
      let rowData = dt.rows(indexes).data().toArray();

      rowData.forEach(row => {
          if (!arrayRowData2.includes(row.pk)) {
              arrayRowData2.push(row.pk);
          }
      });

      if (arrayRowData2.length > 0){
        $('#delete_accounting_several2').removeClass("d-none");
      }
      
    });

    //Deselect rows when delete from arrayRowData2
    $('#recordsTable2').on('deselect.dt', function (e, dt, type, indexes) {
      let rowDataDelete = dt.rows(indexes).data().toArray();

      arrayRowData2 = arrayRowData2.filter(row => !rowDataDelete.some(r => r.pk === row));
      if (arrayRowData2.length == 0){
        $('#delete_accounting_several2').addClass("d-none");
      }
    });

    //Deselect all rows when change page
    $('#recordsTable2').on('page.dt', function() {
      $('#select_all_table2').prop("checked", false); //Uncheck select all

      let rows = tableRecords2.rows().nodes();  //Uncheck all checkboxes
      $(rows).find('input[type="checkbox"]').prop('checked', false);
      tableRecords2.rows().deselect();

      arrayRowData2 = []
    });

  })

//################## ACCOUNTING RECORDS ##################



  </script>
{% endblock %}
