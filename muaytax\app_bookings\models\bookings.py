from django.db import models
from django.db.models import Q

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta

User = get_user_model()

class Bookings(models.Model):
    seller = models.ForeignKey(
        "sellers.Seller",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="booking_seller",
        verbose_name="Vendedor",
    )
    
    guest_user = models.ForeignKey(
        "bookings.GuestUser",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="booking_guest_seller",
        verbose_name="Vendedor invitado"
    )

    manager = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="booking_manager",
        verbose_name="Gestor",
        limit_choices_to={'role': 'manager'}
    )
    
    subject = models.ForeignKey(
        "dictionaries.BookingSubject",
        null=True,
        on_delete=models.SET_NULL,
        related_name="booking_subject",
        verbose_name="<PERSON>unt<PERSON> de la llamada",
    )
    
    topics = models.TextField(
        null=True,
        blank=True,
        verbose_name="Temas a tratar"
    )
    
    comments = models.TextField(
        null=True,
        blank=True,
        verbose_name="Comentarios"
    )
    
    date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Fecha de la llamada"
    )
    
    duration = models.CharField(
        choices=(
            ("1", "15 minutos"),
            ("2", "30 minutos"),
            ("3", "45 minutos"),
            ("4", "1 hora"),
        ),
        default="1",
        max_length=20,
        verbose_name="Duración de la llamada",
        help_text="Duración estimada de la llamada en minutos"
    )
    
    idcal_event = models.CharField(
        null=True,
        blank=True,
        max_length=250,
        verbose_name="Id del evento en Google Calendar"
    )
    
    daily_report = models.ForeignKey(
        "bookings.DailyReports",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="booking_daily_report",
        verbose_name="Reporte diario"
    )
    
    created_by = models.CharField(
        choices=(
            ("seller", "Vendedor"),
            ("manager", "Gestor"),
            ("public", "Público"),
        ),
        blank=True,
        null=True,
        max_length=20,
        verbose_name="Llamada creada por:"
    )
    
    is_guest_user = models.BooleanField(
        default=False,
        verbose_name="¿Es un usuario invitado?"
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Fecha de solicitud")
    
    modified_at = models.DateTimeField(auto_now=True, verbose_name="Fecha de actualización")

    status = models.CharField(
        choices=(
            ("pending", "Pendiente"),
            ("done", "Finalizada"),
            ("cancelled", "Cancelada"),
        ),
        default="pending",
        max_length=20,
        verbose_name="Estado de la llamada",
    )

    # @admin.display(description="Duración de la llamada")
    def get_duration_text(self):
        return dict(self._meta.get_field('duration').choices)[self.duration]
    
    def get_end_time(self):
        if self.date and self.duration:
            duration_minutes = (int(self.duration) + 1) * 15
            end_time = self.date + timedelta(minutes=duration_minutes)
            return end_time.time()
        return None

    # @admin.display(description="Hora de la llamada")
    def get_time(self):
        time = ""
        if self.date:
            time = self.date.astimezone().strftime("%H:%M")
        return time
    
    # @admin.display(description="Fecha de la llamada")
    def get_date(self):
        date = ""
        if self.date:
            date = self.date.date()
        return date
    
    # @property
    def get_user(self):
        if self.seller:
            return self.seller.user.name or self.seller.name
        if self.guest_user:
            return self.guest_user.get_full_name
        return ""
    

    class Meta:
        verbose_name = "Cita telefónica"
        verbose_name_plural = "Citas telefónicas"

    def __str__(self):
        if self.date:
            return 'Cita ' + self.date.strftime("%d/%m/%Y") + ' con ' + self.get_user()
        return 'Cita ' + self.get_user()
    
    def clean(self):
        if self.seller and self.guest_user:
            raise ValidationError("No puedes asignar un vendedor y un vendedor invitado a la vez")

        if self.date and self.status != "cancelled":
            # Verificamos si el usuario actual (sea seller o guest_user) ya tiene una cita ese día
            base_conditions = Q(
                date__date=self.date.date(),
                status="pending"
            )

            if self.pk:
                base_conditions &= ~Q(pk=self.pk)

            # Si es un seller, buscamos otras citas del mismo seller
            if self.seller:
                # Primero, verificamos que el seller no tenga citas con el mismo gestor en el mismo día
                if self.manager:
                    same_manager_conditions = base_conditions & Q(
                        seller=self.seller,
                        manager=self.manager
                    )

                    manager_bookings = Bookings.objects.filter(same_manager_conditions)

                    if manager_bookings.exists():
                        raise ValidationError("Solo puedes agendar una llamada por día con el mismo gestor. Ya tienes una llamada programada con este gestor para este día.")

                # Verificamos si el seller ya tiene una cita para el mismo subject en la misma fecha
                if self.subject:
                    same_subject_conditions = base_conditions & Q(
                        seller=self.seller,
                        subject=self.subject
                    )

                    subject_bookings = Bookings.objects.filter(same_subject_conditions)

                    if subject_bookings.exists():
                        raise ValidationError("Solo puedes agendar una llamada por día para el mismo departamento. Ya tienes una llamada programada para este tema en este día.")

            # Si es un guest_user, buscamos otras citas del mismo guest_user
            elif self.guest_user:
                user_bookings = Bookings.objects.filter(
                    base_conditions,
                    guest_user=self.guest_user
                )
                if user_bookings.exists():
                    raise ValidationError("Solo puedes agendar una llamada por día. Ya tienes una llamada programada para este día.")

            # Validamos también que el gestor no tenga otra cita a la misma HORA exacta
            if self.manager and self.date:
                same_time_conditions = Q(
                    manager=self.manager,
                    date=self.date,  # Misma fecha y hora exacta
                    status="pending"
                )

                if self.pk:
                    same_time_conditions &= ~Q(pk=self.pk)

                manager_bookings = Bookings.objects.filter(same_time_conditions)

                if manager_bookings.exists():
                    raise ValidationError("Ya existe una cita para este gestor en esta fecha y hora exacta.")
