from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.urls import reverse
from muaytax.signals import disable_for_load_data

class Representative(models.Model):

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="representative_seller",
        verbose_name="Vendedor",
    )

    first_name = models.Char<PERSON>ield(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="Nombre del Representante"
    )

    last_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Apellidos del Representante"
    )

    representative_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="NIF del representante"
    )

    codice_fiscale = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Codice fiscale del representante"
    )

    birthdate= models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de nacimiento del Representante",
    )
    
    is_notification_representative= models.BooleanField(
        blank=True,
        null=True,
        default=False,
        verbose_name="Representante a efectos de notificaciones",
    )   

    address = models.ForeignKey(
        "address.Address",
        blank="true",
        null="true",
        related_name="representative_address",
        on_delete=models.CASCADE,
        verbose_name="Dirección del representante",
    )


    gender = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        choices = [
            ('M', 'Masculino'),
            ('F', 'Femenino'),
        ],
        verbose_name="Sexo del Representante"
    )

    birth_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True, null=True,              
        related_name="representative_birth_country",
        verbose_name="País de Nacimiento del Representante",
    )

    type_representation = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        choices = [
            ('director', 'Director'),
            ('legal_representative', 'Representante Legal'),
        ]
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Representante"
        verbose_name_plural = "Representantes"
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    def get_absolute_url(self):
        return reverse("app_representatives:representative_update", kwargs={"pk": self.pk})

# @receiver(post_save, sender=Representative)
# @disable_for_load_data
# def after_sellervat_save(sender, instance, created, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)

# @receiver(post_delete, sender=Representative)
# @disable_for_load_data
# def after_sellervat_delete(sender, instance, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)