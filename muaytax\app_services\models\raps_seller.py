from django.db import models
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.rap_category import RapCategory


class RapSeller(models.Model):

  seller = models.ForeignKey(
    "sellers.Seller",
    on_delete=models.CASCADE,
    related_name="raps_seller",
    verbose_name="Vendedor",
  )

  category = models.ForeignKey(
    "dictionaries.RapCategory",
    on_delete=models.CASCADE,
    related_name="raps_category",
    verbose_name="Categoría",
  )

  created_at = models.DateTimeField(auto_now_add=True)

  modified_at = models.DateTimeField(auto_now=True)

  class Meta:
      verbose_name = "Rap del vendedor"
      verbose_name_plural = "Raps del vendedor"

  def __str__(self):
    return self.category.description
