from django.urls import path
from muaytax.app_lists import views

app_name = "app_lists"

urlpatterns = [
    # NEW ###########################################################################################################
    path(
        "sellers/management/<country>/",
        view=views.SellerManagementCachedView.as_view(),
        name="new-management"
    ),
    path(
        "sellers/management/us/fixer/",
        view=views.SellerManagementUsFixer.as_view(),
        name="management_us_fixer_5472"
    ),
    path(
        "sellers/new-managementESdatatables/",
        view=views.SellerManagementESCachedListDT.as_view(),
        name="new_seller_management_ES_list",
    ),
    # path(
    #     "sellers/update-seller-management-ES-List/",
    #     view=views.UpdateSellerManagementESList.as_view(),
    #     name="update_seller_management_ES_list",
    # ),
    path(
        "sellers/update-all-seller-List/",
        view=views.UpdateAllSellerList.as_view(),
        name="update_all_seller_list",
    ),
    ###############################################################################################################
]

##################################### VAT URLS ##########################################
urlpatterns += [
    path(
        "sellers/vat/<country>/",
        view=views.SellerVatManagementListView.as_view(),
        name="vat_management"
    ),
    path(
        "sellers/vat/<country>/dt-list/",
        view=views.SellerVatManagementListDT.as_view(),
        name="vat_management_dt_list"
    ),
]

##################################### MANAGEMENT USA URLS ##########################################
urlpatterns += [
    path(
        "sellers/management/us/dt-list/",
        view=views.SellerManagementUSCachedListDT.as_view(),
        name="seller_management_us_dt_list"
    ),
    path(
        "sellers/management/us/dt-list/fixer",
        view=views.SellerManagementUsDTFixer.as_view(),
        name="seller_management_us_dt_list_fixer"
    ),
]

##################################### VERIFACTU URLS ##########################################
urlpatterns += [
    path(
        "sellers/management-es/verifactu/",
        view=views.SellerManagementESVerifactuList.as_view(),
        name="verifactu_management"
    ),
    path(
        "sellers/management-es/verifactuDT/",
        view=views.SellerManagementESVerifactuListDT.as_view(),
        name="verifactu_managementDT"
    )
]

