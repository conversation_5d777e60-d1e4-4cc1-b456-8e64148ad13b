{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Representantes del Vendedor{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
                <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                Representantes del vendedor
            </h5>
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
           <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_representatives:representative_list' seller.shortname %}">Representantes</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Eliminar</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
<style>
  
</style>

    <div class="card">
        <div class="card-body">
            <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
            {% csrf_token %}
                <div class="text-center"> 
                    <h1>Eliminar Representante</h1>
                    <p>¿Está seguro de que desea eliminar al representante?</p>
                    <p><b>Nombre:</b> {{ object.first_name }}</p>
                    <p><b>Apellidos:</b> {{ object.last_name }}</p>
                    <p><b>NIF:</b> {{ object.representative_id }}</p>
                    <div class="controls">
                        <button type="submit" class="btn btn-danger">Eliminar</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
{% endblock content %}

{% block javascripts %}

<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
  <script>
  </script>
{% endblock javascripts %}