from django.db import models

class SellerVatEsStatusActivation(models.Model):

    code = models.CharField(
        blank=True,
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ódigo"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )


    class Meta:
        verbose_name = "Estado del proceso de activación para España"
        verbose_name_plural = "Estado de los procesos de activación para España"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatEsStatusActivation)
# class SellerVatEsStatusActivationAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
