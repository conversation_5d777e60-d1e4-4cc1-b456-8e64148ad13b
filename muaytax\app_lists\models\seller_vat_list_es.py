from django.core import validators
from django.db import models
from django.contrib import admin


class SellerVatListES(models.Model):

    seller = models.ForeignKey(
        "sellers.seller", 
        on_delete=models.CASCADE, 
        verbose_name="empresa",
        related_name="seller_list_vat_es"
    )

    show = models.BooleanField(
        default=True,
        verbose_name="Mostrar",
    )

    year = models.PositiveIntegerField(
        verbose_name="Año",
        validators=[
            validators.MinValueValidator(2000),
            validators.MaxValueValidator(2050),
        ],
    )

    period = models.ForeignKey(
        "dictionaries.Period",
        on_delete=models.PROTECT,
        related_name="period_seller_list_vat_es",
        verbose_name="Periodo",
        validators=[
            validators.RegexValidator(
                regex=r"Q[1-4]|0A",
                message="Periodo no válido",
                code="invalid_period",
            )
        ],
    )

    model_184 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 184",
        validators=[
            validators.RegexValidator(
                regex=r"not-started|not-processed|processed|required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_184",
            )
        ],
    )

    model_303 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 303",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_303",
            )
        ],
    )

    model_347 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 347",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_347",
            )
        ],
    )

    model_349 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 349",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_349",
            )
        ],
    )

    model_369 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 369",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_369",
            )
        ],
    )

    model_390 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 390",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_390",
            )
        ],
    )

    ############ TXT Mensuales #################################
    txt_01 = models.BooleanField(
        default=False,
        verbose_name="TXT Enero",
    )

    txt_02 = models.BooleanField(
        default=False,
        verbose_name="TXT Febrero",
    )

    txt_03 = models.BooleanField(
        default=False,
        verbose_name="TXT Marzo",
    )

    txt_04 = models.BooleanField(
        default=False,
        verbose_name="TXT Abril",
    )

    txt_05 = models.BooleanField(
        default=False,
        verbose_name="TXT Mayo",
    )

    txt_06 = models.BooleanField(
        default=False,
        verbose_name="TXT Junio",
    )

    txt_07 = models.BooleanField(
        default=False,
        verbose_name="TXT Julio",
    )

    txt_08 = models.BooleanField(
        default=False,
        verbose_name="TXT Agosto",
    )

    txt_09 = models.BooleanField(
        default=False,
        verbose_name="TXT Septiembre",
    )

    txt_10 = models.BooleanField(
        default=False,
        verbose_name="TXT Octubre",
    )

    txt_11 = models.BooleanField(
        default=False,
        verbose_name="TXT Noviembre",
    )

    txt_12 = models.BooleanField(
        default=False,
        verbose_name="TXT Diciembre",
    )

    model_json_result = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON Resultados de Modelos",
    )

    monthly_muaytax_json_invoices = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON Facturas Muaytax Mensuales",
    )

    class Meta:
        verbose_name = "Listado IVA España"
        verbose_name_plural = "Listado IVA España"
        constraints = [
            models.UniqueConstraint(fields=['seller', 'year', 'period'], name='seller_vat_list_es_unique_seller_year_period'),
        ]

    def __str__(self):
        return f"IVA ES - {self.seller} - {self.year} - {self.period}"
    

@admin.register(SellerVatListES)
class SellerListVatESAdmin(admin.ModelAdmin):
    list_display = ["id", "seller", "year", "period", "show"]
    list_filter =  ["year", "period"]
    search_fields = ["id", "seller__name", "year", "period__description", "show"]

