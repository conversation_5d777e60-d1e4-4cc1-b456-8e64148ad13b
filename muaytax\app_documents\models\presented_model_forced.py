from django.core.validators import FileExtensionValidator, MinValueValidator, MaxValueValidator
from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from muaytax.signals import disable_for_load_data
from muaytax.app_documents.constants import *


class PresentedModelForced(models.Model):
    # id -> AutoGen
    year = models.PositiveIntegerField(
        verbose_name="Año de la Declaración",
    )
    period = models.ForeignKey(
        "dictionaries.Period",
        on_delete=models.PROTECT,
        verbose_name="Periodo de la Declaración",
        related_name="presented_model_forced_period",
    )
    country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        verbose_name="País de la Declaración",
        related_name="presented_model_forced_country",
    )
    model = models.ForeignKey(
        "dictionaries.Model",
        on_delete=models.PROTECT,
        verbose_name="Modelo de la Declaración",
        related_name="presented_model_forced_model",
    )
    status = models.ForeignKey(
        "dictionaries.ModelStatus",
        on_delete=models.PROTECT,
        default="not-required",
        verbose_name="Estado del Modelo",
        related_name="presented_model_forced_status",
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        verbose_name="Vendedor",
        related_name="presented_model_forced_seller",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Modelo Forzado"
        verbose_name_plural = "Modelos Forzados"

    def __str__(self):
        return "Presented Model ID:{}".format(self.pk)


@receiver(post_save, sender=PresentedModelForced)
@disable_for_load_data
def after_sellervat_save(sender, instance, created, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk, year=instance.year)

@receiver(post_delete, sender=PresentedModelForced)
@disable_for_load_data
def after_sellervat_delete(sender, instance, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk)