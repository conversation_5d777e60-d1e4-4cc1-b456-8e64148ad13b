from django.db import models

class AccountingAccount(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=10,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    country = models.Char<PERSON>ield(
        max_length=2,
        verbose_name="País",
    )

    class Meta:
        verbose_name = "Cuenta Contable"
        verbose_name_plural = "Cuentas Contables"
    
    def __str__(self):
        return self.description
    
# @admin.register(AccountingAccount)    
# class AccountingAccountAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
