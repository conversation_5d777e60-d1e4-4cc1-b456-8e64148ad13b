{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Socios{% endblock title %}

{% block stylesheets %}
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/buttons/buttons.dataTables.min-v2.0.1.css"/>
{% endblock stylesheets %}


{% block content %}
<div class="card text-dark bg-light">
  <div class="card-header text-white bg-light">
    <h2>FIX Presented Models</h2>
  </div>
  <div class="card-body">

    <h4>Cantidad PM Pendientes (Q2 2023): {{presented_models.count}}</h4>

    <form method="post" enctype="multipart/form-data" action="">
      {% csrf_token %}
      <div class="row my-3">
        <div class="col-2">
          <label for="qty-pending">Cantidad PM pendientes de areglar:</label>
          <input type="text" class="form-control" id="id_qty-pending" name="qty-pending" value="{{presented_models.count}}" disabled>
        </div>
        <div class="col-2">
          <label for="qty">Cantidad a procesar:</label>
          <input type="number" class="form-control" id="id_qty" name="qty" value="10" min="1" max="1000">
        </div>
        <div class="col-2">
          <label for="wait">Tiempo de Espera (S) entre procesos:</label>
          <input type="number" class="form-control" id="id_wait" name="wait" value="2" min="0" max="10">
        </div>
        <div class="col-2">
          <label for="period">Periodo:</label>
          <input type="text" class="form-control" id="id_period" name="period" value="Q2" disabled>
        </div>
        <div class="col-2">
          <label for="year">Año:</label>
          <input type="text" class="form-control" id="id_year" name="year" value="2023" disabled>
        </div>
        <div class="col-2 d-flex justify-content-center align-items-end">
          {% if presented_models.count > 0 %}
            <button 
              type="submit" 
              class="btn btn-primary w-100" 
              id="submitbutton" 
              onclick="onClickButtonSubmit()"
            > Procesar </button>
          {% else %}
            <button 
              type="button" 
              class="btn btn-primary w-100" 
              id="submitbutton" 
              disabled
            > Procesar </button>
          {% endif %}
        </div>

        <div class="col m-3 alert alert-info rounded" id="models" role="alert">
          <div class="row d-flex justify-content-center text-center">
            <div class="col-2">
              <button type="button" class="btn btn-secondary w-100" id="button-pending" onclick="onClickButtonModel(event)"> 
                Modelos Pendientes 
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-111" onclick="onClickButtonModel(event)"> 
                Modelo 111 
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-115" onclick="onClickButtonModel(event)"> 
                Modelo 115 
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-130" onclick="onClickButtonModel(event)"> 
                Modelo 130 
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-303" onclick="onClickButtonModel(event)"> 
                Modelo 303 
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-309" onclick="onClickButtonModel(event)"> 
                Modelo 309
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-349" onclick="onClickButtonModel(event)"> 
                Modelo 349 
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-369" onclick="onClickButtonModel(event)"> 
                Modelo 369
              </button>
            </div>
            <div class="col-1">
              <button type="button" class="btn btn-secondary w-100" id="button-lipe" onclick="onClickButtonModel(event)"> 
                LIPE
              </button>
            </div>
          </div>
        </div>        

        <div class="col-12 mt-3" style="display:none;" id="loading">
          <div class="alert alert-warning rounded text-center" role="alert">
            <div class="spinner-border text-success" role="status"></div> <br>
            <span class="text-success"> <b>Procesando...</b> </span>
          </div>
        </div>
      </div>      
    </form>

    <div class="table-responsive" id="pm-pending" style="display:none;">
      <table class="table table-bordered">
        <tr>
          <th class="text-center"> Modelos Pendientes </th>
        </tr>
      </table>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th scope="col">Seller</th>
            <th scope="col">Modelo</th>
            <th scope="col">Estado</th>
            <th scope="col">Periodo</th>
            <th scope="col">Año</th>
          </tr>
        </thead>
        <tbody>
          {% for pm in presented_models %}
          <tr>
            <td> {{ pm.seller }} </td>
            <th> {{ pm.model }} </th>
            <td> {{ pm.status }} </td>
            <td> {{ pm.period }} </td>
            <td> {{ pm.year }} </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <div class="table-responsive" id="pm-111" style="display:none;">
      <table id="table-111" class="table table-striped table-bordered dt-responsive nowrap" style="width:99%">
        <thead>
          <tr>
            <th>ejercicio</th>
            <th>periodo</th>
            <th>email</th>
            <th>nif</th>
            <th>nombre</th>
            <th>negativa</th>
            <th style="display:none;">ingreso</th>
            <th style="display:none;">pago</th>
            <th style="display:none;">iban</th>
            <th>complementaria</th>
            <th style="display:none;">justificante</th>
            <th style="display:none;">casilla_01</th>
            <th style="display:none;">casilla_02</th>
            <th style="display:none;">casilla_03</th>
            <th style="display:none;">casilla_04</th>
            <th style="display:none;">casilla_05</th>
            <th style="display:none;">casilla_06</th>
            <th>casilla_07</th>
            <th>casilla_08</th>
            <th>casilla_09</th>
            <th style="display:none;">casilla_10</th>
            <th style="display:none;">casilla_11</th>
            <th style="display:none;">casilla_12</th>
            <th style="display:none;">casilla_13</th>
            <th style="display:none;">casilla_14</th>
            <th style="display:none;">casilla_15</th>
            <th style="display:none;">casilla_16</th>
            <th style="display:none;">casilla_17</th>
            <th style="display:none;">casilla_18</th>
            <th style="display:none;">casilla_19</th>
            <th style="display:none;">casilla_20</th>
            <th style="display:none;">casilla_21</th>
            <th style="display:none;">casilla_22</th>
            <th style="display:none;">casilla_23</th>
            <th style="display:none;">casilla_24</th>
            <th style="display:none;">casilla_25</th>
            <th style="display:none;">casilla_26</th>
            <th style="display:none;">casilla_27</th>
            <th>casilla_28</th>
            <th style="display:none;">casilla_29</th>
            <th>casilla_30</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="table-responsive" id="pm-115" style="display:none;">
      <table id="table-115" class="table table-striped table-bordered dt-responsive nowrap" style="width:99%">
        <thead>
          <tr>
            <th>page1_field2</th>
            <th>page1_field3</th>
            <th>email</th>
            <th>page1_field6</th>
            <th>page1_field7</th>
            <th>page1_field10</th>
            <th>page1_field11</th>
            <th>page1_field12</th>
            <th>page1_field13</th>
            <th>page1_field15</th>
            <th>page1_field20</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="table-responsive" id="pm-130" style="display:none;">
      <form method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="alert alert-warning rounded mb-5" align="center">
          <label for="formFile" class="form-label"><b>Seleccione el archivo CSV para actualizar el 130</b></label> <br>
          <input class="form-control w-50" type="file" id="file" name="file">
          <input type="hidden" name="model" value="130">
          <input type="submit" class="btn btn-primary mt-3" value="Actualizar">
        </div>
      </form>
      <table id="table-130" class="table table-striped table-bordered dt-responsive nowrap" style="width:99%">
        <thead>
          <tr>
            <th>ejercicio</th>
            <th>periodo</th>
            <th>email</th>
            <th>nif</th>
            <th>nombre</th>
            <th>apellidos</th>
            <th>Importe_ingreso</th>

            <th style="display:none;">resultado_deducir</th>
            <th style="display:none;">forma_pago</th>
            <th style="display:none;">iban</th>
            <th style="display:none;">complementaria</th>
            <th style="display:none;">negativa</th>
            <th style="display:none;">numero_justificante</th>

            <th>campo_01</th>
            <th>campo_02</th>
            <th>campo_03</th>
            <th>campo_04</th>
            <th>campo_05</th>
            <th>campo_06</th>
            <th>campo_07</th>
            <th style="display:none;">campo_08</th>
            <th style="display:none;">campo_09</th>
            <th style="display:none;">campo_10</th>
            <th style="display:none;">campo_11</th>
            <th>campo_12</th>
            <th style="display:none;">campo_13</th>
            <th>campo_14</th>
            <th>campo_15</th>
            <th style="display:none;">campo_16</th>
            <th>campo_17</th>
            <th style="display:none;">campo_18</th>
            <th>campo_19</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="table-responsive" id="pm-303" style="display:none;">
      <form method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="alert alert-warning rounded mb-5" align="center">
          <label for="formFile" class="form-label"><b>Seleccione el archivo CSV para actualizar el 303</b></label> <br>
          <input class="form-control w-50" type="file" id="file" name="file">
          <input type="hidden" name="model" value="303">
          <input type="submit" class="btn btn-primary mt-3" value="Actualizar">
        </div>
      </form>
      <table id="table-303" class="table table-striped table-bordered dt-responsive nowrap" style="width:99%">
        <thead>
          <tr>
            <th>ejercicio</th>
            <th>periodo</th>
            <th>email</th>
            <th>nif</th>
            <th>nombre</th>
            <th style="display:none;">nif_2</th>
            <th style="display:none;">nombre_2</th>
            <th style="display:none;">nif_3</th>
            <th style="display:none;">nombre_3</th>
            <th style="display:none;">foral</th>
            <th style="display:none;">art_30</th>
            <th style="display:none;">sii</th>
            <th style="display:none;">regimen_simplificado</th>
            <th style="display:none;">exonerado_anual</th>
            <th style="display:none;">conjunta</th>
            <th style="display:none;">volumen_anual</th>
            <th style="display:none;">criterio_caja</th>
            <th style="display:none;">destinatario_caja</th>
            <th style="display:none;">prorrata</th>
            <th style="display:none;">revocacion_prorrata</th>
            <th style="display:none;">concurso_acreedores</th>
            <th style="display:none;">fecha_concurso</th>
            <th style="display:none;">preconcursal</th>
            <th style="display:none;">postconsursal</th>
            <th style="display:none;">sin_actividad</th>
            <th style="display:none;">autoliquidacion</th>
            <th style="display:none;">justificante</th>
            <th style="display:none;">ingreso</th>
            <th style="display:none;">IBAN</th>
            <th style="display:none;">devolucion_importe</th>
            <th style="display:none;">IBAN_devolucion</th>
            <th style="display:none;">IBAN_transferencia_SEPA</th>
            <th style="display:none;">IBAN_transferencia_SWIFT</th>
            <th style="display:none;">resto_SWIFT</th>
            <th style="display:none;">resto_cuenta</th>
            <th style="display:none;">resto_banco</th>
            <th style="display:none;">resto_direccion</th>
            <th style="display:none;">resto_ciudad</th>
            <th style="display:none;">resto_pais</th>
            <th style="display:none;">resto_pais_code</th>
            <th style="display:none;">campo_150</th>
            <th style="display:none;">campo_152</th>
            <th>campo_1</th>
            <th>campo_3</th>
            <th style="display:none;">campo_153</th>
            <th style="display:none;">campo_155</th>
            <th>campo_4</th>
            <th>campo_6</th>
            <th>campo_7</th>
            <th>campo_9</th>
            <th>campo_10</th>
            <th>campo_11</th>
            <th>campo_12</th>
            <th>campo_13</th>
            <th>campo_14</th>
            <th>campo_15</th>
            <th style="display:none;">campo_156</th>
            <th style="display:none;">campo_158</th>
            <th>campo_16</th>
            <th>campo_18</th>
            <th>campo_19</th>
            <th>campo_21</th> 
            <th>campo_22</th>
            <th>campo_24</th>
            <th>campo_25</th>
            <th>campo_26</th>
            <th>campo_27</th>
            <th>campo_28</th>
            <th>campo_29</th>
            <th style="display:none;">campo_30</th>
            <th style="display:none;">campo_31</th>
            <th>campo_32</th>
            <th>campo_33</th>
            <th style="display:none;">campo_34</th>
            <th style="display:none;">campo_35</th>
            <th>campo_36</th>
            <th>campo_37</th>
            <th style="display:none;">campo_38</th>
            <th style="display:none;">campo_39</th>
            <th>campo_40</th>
            <th>campo_41</th>
            <th style="display:none;">campo_42</th>
            <th style="display:none;">campo_43</th>
            <th style="display:none;">campo_44</th>
            <th>campo_45</th>
            <th>campo_46</th>
            <th>campo_59</th>
            <th>campo_60</th>
            <th>campo_120</th>
            <th style="display:none;">campo_122</th>
            <th>campo_123</th>
            <th>campo_124</th>
            <th style="display:none;">campo_62</th>
            <th style="display:none;">campo_63</th>
            <th style="display:none;">campo_74</th>
            <th style="display:none;">campo_76</th>
            <th>campo_64</th>
            <th>campo_66</th>
            <th>campo_77</th>
            <th style="display:none;">campo_110</th>
            <th>campo_78</th>
            <th>campo_87</th>
            <th>campo_69</th>
            <th style="display:none;">campo_70</th>
            <th style="display:none;">campo_68</th>
            <th style="display:none;">campo_109</th>
            <th>campo_71</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="table-responsive" id="pm-309" style="display:none;">
      <table id="table-309" class="table table-striped table-bordered dt-responsive nowrap" style="width:99%">
        <thead>
          <tr>
            <th>ejercicio</th>
            <th>periodo</th>
            <th>email</th>
            <th>nif_1</th>
            <th>nombre_1</th>
            <th>apellidos_1</th>
            <th style="display:none;">nif_3</th>
            <th style="display:none;">nif_iva_3</th>
            <th style="display:none;">pais_3</th>
            <th style="display:none;">nombre_3</th>
            <th style="display:none;">apellidos_3</th>
            <th style="display:none;">nif_4</th>
            <th style="display:none;">nombre_4</th>
            <th style="display:none;">apellidos_4</th>
            <th style="display:none;">seccion5_punto1</th>
            <th style="display:none;">seccion5_punto4</th>
            <th style="display:none;">seccion5_punto2</th>
            <th style="display:none;">seccion5_punto5</th>
            <th style="display:none;">seccion5_punto3</th>
            <th style="display:none;">seccion5_punto6</th>
            <th style="display:none;">seccion6_punto1</th>
            <th style="display:none;">seccion6_punto4</th>
            <th style="display:none;">seccion6_punto2</th>
            <th style="display:none;">seccion6_punto5</th>
            <th style="display:none;">seccion6_punto3</th>
            <th style="display:none;">seccion6_punto6</th>
            <th style="display:none;">complementaria</th>
            <th style="display:none;">importe_ingreso</th>
            <th style="display:none;">numero_justificante</th>
            <th style="display:none;">forma_pago</th>
            <th style="display:none;">iban</th>
            <th style="display:none;">marca</th>
            <th style="display:none;">tipo</th>
            <th style="display:none;">modelo</th>
            <th style="display:none;">bastidos</th>
            <th style="display:none;">clasificacion</th>
            <th style="display:none;">fabricante</th>
            <th style="display:none;">tipo_modelo</th>
            <th style="display:none;">contruccion</th>
            <th style="display:none;">eslora</th>
            <th style="display:none;">aeronave_fabricante</th>
            <th style="display:none;">aeronave_marca</th>
            <th style="display:none;">aeronave_serie</th>
            <th style="display:none;">aeronave_ao</th>
            <th style="display:none;">aeronave_kg</th>
            <th style="display:none;">casilla01</th>
            <th style="display:none;">casilla02</th>
            <th style="display:none;">casilla03</th>
            <th style="display:none;">casilla04</th>
            <th style="display:none;">casilla05</th>
            <th>casilla06</th>
            <th>casilla07</th>
            <th>casilla08</th>
            <th>casilla09</th>
            <th>casilla10</th>
            <th style="display:none;">casilla11</th>
            <th style="display:none;">casilla12</th>
            <th style="display:none;">casilla13</th>
            <th style="display:none;">casilla14</th>
            <th style="display:none;">casilla15</th>
            <th style="display:none;">casilla16</th>
            <th style="display:none;">casilla17</th>
            <th style="display:none;">casilla18</th>
            <th style="display:none;">casilla19</th>
            <th style="display:none;">casilla20</th>
            <th style="display:none;">casilla21</th>
            <th style="display:none;">casilla22</th>
            <th style="display:none;">casilla23</th>
            <th style="display:none;">casilla24</th>
            <th style="display:none;">casilla25</th>
            <th style="display:none;">casilla26</th>
            <th style="display:none;">casilla27</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="table-responsive" id="pm-349" style="display:none;">
      <b>349 JSON:</b> <br>
      {{model_349}}
    </div>

    <div class="table-responsive" id="pm-369" style="display:none;">
      <form method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="alert alert-warning rounded mb-5" align="center">
          <label for="formFile" class="form-label"><b>Seleccione el archivo CSV para actualizar el 369</b></label> <br>
          <input class="form-control w-50" type="file" id="file" name="file">
          <input type="hidden" name="model" value="369">
          <input type="hidden" name="period" value="Q2">
          <input type="submit" class="btn btn-primary mt-3" value="Actualizar">
        </div>
      </form>
      <b>369 JSON:</b> <br>
      {{model_369}}
    </div>

    <div class="table-responsive" id="pm-lipe" style="display:none;">
      <form method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="alert alert-warning rounded mb-5" align="center">
          <label for="formFile" class="form-label"><b>Seleccione el archivo CSV para crear el LIPE</b></label> <br>
          <input class="form-control w-50" type="file" id="file" name="file">
          <input type="hidden" name="model" value="lipe">
          <input type="submit" class="btn btn-primary mt-3" value="Actualizar">
        </div>
      </form>
      <table id="table-lipe" class="table table-striped table-bordered dt-responsive nowrap" style="width:99%">
        <thead>
          <tr>
            <th>Empresa</th>
            <th>EMAIL de la APP</th>
            <th>Anno di imposta</th>
            <th>VP1 (1)</th>
            <th>VP1 (2)</th>
            <th>VP1 (3)</th>
            <th>VP1 (4)</th>
            <th>VP1 (5)</th>
            <th>VP2</th>
            <th>VP3</th>
            <th>VP4</th>
            <th>VP5</th>
            <th>VP6 (1)</th>
            <th>VP6 (2)</th>
            <th>VP7</th>
            <th>VP8</th>
            <th>VP9</th>
            <th>VP10</th>
            <th>VP11</th>
            <th>VP12</th>
            <th>VP13 (1)</th>
            <th>VP13 (2)</th>
            <th>VP14 (1)</th>
            <th>VP14 (2)</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

  </div>
</div>
{% endblock content %}

{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.buttons.min-v2.0.1.js"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/buttons/buttons.html5.min-v2.0.1.js"></script>
  <script>
    const onClickButtonSubmit = () => {
      document.getElementById("submitbutton").style.display = "none";
      document.getElementById("models").style.display = "none";
      document.getElementById("loading").style.display = "block";
    }

    const onClickButtonModel = (e) => {
      console.log(e);
      let button = e.target || e.srcElement;
      console.log(button.id);
      let pm_id = "";

      if ( button.id == "button-pending" ) {
        pm_id = "pm-pending";
      } else if (button.id == "button-111") {
        pm_id = "pm-111";
      } else if (button.id == "button-115") {
        pm_id = "pm-115";
      } else if (button.id == "button-130") {
        pm_id = "pm-130";
      } else if (button.id == "button-303") {
        pm_id = "pm-303";
      } else if (button.id == "button-309") {
        pm_id = "pm-309";
      } else if (button.id == "button-349") {
        pm_id = "pm-349";
      } else if (button.id == "button-369") {
        pm_id = "pm-369";
      } else if (button.id == "button-lipe") {
        pm_id = "pm-lipe";
      }

      hideAll();
      if (pm_id != "") {
        if(document.getElementById(pm_id).style.display == "none") {
          document.getElementById(pm_id).style.display = "block";
        } else {
          document.getElementById(pm_id).style.display = "none";
        }
      }
    }

    const hideAll = () => {
      const pm = ['pm-pending', 'pm-111', 'pm-115', 'pm-130', 'pm-303', 'pm-309', 'pm-349', 'pm-369', 'pm-lipe'];
      pm.forEach(element => {
        document.getElementById(element).style.display = "none";
      });
    }

    const createDataTable111 = () => {
      let data = [];
      try {
        data = {{model_111|safe}};
      } catch (error) {
        console.error(error);
      }
      console.log(data);
      if (data && data.length > 0) {  
        const table = $("#table-111"); 
        table.DataTable ({
            "dom" : "Bti",
            "data" : data,
            "language": {
              "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },     
            {% comment %} "searching": false, {% endcomment %}
            "order": [[2, 'asc'], [0, 'asc'],  [1, 'asc'],],
            "buttons": [
                "copy", "csv", "excel"
            ],
            "columns" : [
                { "data" : "ejercicio" },
                { "data" : "periodo" },
                { "data" : "email" },
                { "data" : "nif" },
                { "data" : "nombre" },
                { "data" : "negativa" },
                { "data" : "ingreso", "visible": false },
                { "data" : "pago", "visible": false },
                { "data" : "iban", "visible": false },
                { "data" : "complementaria" },
                { "data" : "justificante", "visible": false },
                { "data" : "casilla_01", "visible": false },
                { "data" : "casilla_02", "visible": false },
                { "data" : "casilla_03", "visible": false },
                { "data" : "casilla_04", "visible": false },
                { "data" : "casilla_05", "visible": false },
                { "data" : "casilla_06", "visible": false },
                { "data" : "casilla_07" },
                { "data" : "casilla_08" },
                { "data" : "casilla_09", "visible": false },
                { "data" : "casilla_10", "visible": false },
                { "data" : "casilla_11", "visible": false },
                { "data" : "casilla_12", "visible": false },
                { "data" : "casilla_13", "visible": false },
                { "data" : "casilla_14", "visible": false },
                { "data" : "casilla_15", "visible": false },
                { "data" : "casilla_16", "visible": false },
                { "data" : "casilla_17", "visible": false },
                { "data" : "casilla_18", "visible": false },
                { "data" : "casilla_19", "visible": false },
                { "data" : "casilla_20", "visible": false },
                { "data" : "casilla_21", "visible": false },
                { "data" : "casilla_22", "visible": false },
                { "data" : "casilla_23", "visible": false },
                { "data" : "casilla_24", "visible": false },
                { "data" : "casilla_25", "visible": false },
                { "data" : "casilla_26", "visible": false },
                { "data" : "casilla_27", "visible": false },
                { "data" : "casilla_28" },
                { "data" : "casilla_29", "visible": false },
                { "data" : "casilla_30" }
            ]
        });
      }
    }

    const createDataTable115 = () => {
      let data = [];
      try {
        data = {{model_115|safe}};
      } catch (error) {
        console.error(error);
      }
      console.log(data);
      if (data && data.length > 0) {  
        const table = $("#table-115"); 
        table.DataTable ({
            "dom" : "Bti",
            "data" : data,
            "language": {
              "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },     
            {% comment %} "searching": false, {% endcomment %}
            "order": [[0, 'asc']],
            "buttons": [
                "copy", "csv", "excel"
            ],
            "columns" : [
                { "data" : "page1_field2" },
                { "data" : "page1_field3" },
                { "data" : "email" },
                { "data" : "page1_field6" },
                { "data" : "page1_field7" },
                { "data" : "page1_field10" },
                { "data" : "page1_field11" },
                { "data" : "page1_field12" },
                { "data" : "page1_field13" },
                { "data" : "page1_field15" },
                { "data" : "page1_field20" }
            ]
        });
      }
    }

    const createDataTable130 = () => {
      let data = [];
      try {
        data = {{model_130|safe}};
      } catch (error) {
        console.error(error);
      }
      console.log(data);
      if (data && data.length > 0) {  
        const table = $("#table-130"); 
        table.DataTable ({
            "dom" : "Btip",
            "data" : data,
            "language": {
              "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },     
            {% comment %} "searching": false, {% endcomment %}
            "order": [[2, 'asc'], [0, 'asc'],  [1, 'asc'],],
            "buttons": [
                "copy", "csv", "excel"
            ],
            "columns" : [
                { "data" : "ejercicio" },
                { "data" : "periodo" },
                { "data" : "email" },
                { "data" : "nif" },
                { "data" : "nombre" },
                { "data" : "apellidos" },
                { "data" : "Importe_ingreso" },
                { "data" : "resultado_deducir", "visible": false },
                { "data" : "forma_pago", "visible": false },
                { "data" : "iban", "visible": false },
                { "data" : "complementaria", "visible": false },
                { "data" : "negativa", "visible": false },
                { "data" : "numero_justificante", "visible": false },
                { "data" : "campo_01" },
                { "data" : "campo_02" },
                { "data" : "campo_03" },
                { "data" : "campo_04" },
                { "data" : "campo_05" },
                { "data" : "campo_06" },
                { "data" : "campo_07" },
                { "data" : "campo_08", "visible": false  },
                { "data" : "campo_09", "visible": false },
                { "data" : "campo_10", "visible": false },
                { "data" : "campo_11", "visible": false },
                { "data" : "campo_12" },
                { "data" : "campo_13", "visible": false },
                { "data" : "campo_14" },
                { "data" : "campo_15" },
                { "data" : "campo_16", "visible": false },
                { "data" : "campo_17" },
                { "data" : "campo_18", "visible": false },
                { "data" : "campo_19" },
            ]
        });
      }
    }

    const createDataTable303 = () => {
      let data = [];
      try {
        data = {{model_303|safe}};
      } catch (error) {
        console.error(error);
      }
      console.log(data);
      if (data && data.length > 0) {  
        const table = $("#table-303"); 
        table.DataTable ({
            "dom" : "Bti",
            "data" : data,
            "language": {
              "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },     
            {% comment %} "searching": false, {% endcomment %}
            "order": [[2, 'asc'], [0, 'asc'],  [1, 'asc'],],
            "buttons": [
                "copy", "csv", "excel"
            ],
            "columns" : [
                { "data" : "ejercicio" },
                { "data" : "periodo" },
                { "data" : "email" },
                { "data" : "nif" },
                { "data" : "nombre" },
                { "data" : "nif_2", "visible": false },
                { "data" : "nombre_2", "visible": false },
                { "data" : "nif_3", "visible": false },
                { "data" : "nombre_3", "visible": false },
                { "data" : "foral", "visible": false },
                { "data" : "art_30", "visible": false },
                { "data" : "sii", "visible": false },
                { "data" : "regimen_simplificado", "visible": false },
                { "data" : "exonerado_anual", "visible": false },
                { "data" : "conjunta", "visible": false },
                { "data" : "volumen_anual", "visible": false },
                { "data" : "criterio_caja", "visible": false },
                { "data" : "destinatario_caja", "visible": false },
                { "data" : "prorrata", "visible": false },
                { "data" : "revocacion_prorrata", "visible": false },
                { "data" : "concurso_acreedores", "visible": false },
                { "data" : "fecha_concurso", "visible": false },
                { "data" : "preconcursal", "visible": false },
                { "data" : "postconsursal", "visible": false },
                { "data" : "sin_actividad", "visible": false },
                { "data" : "autoliquidacion", "visible": false },
                { "data" : "justificante", "visible": false },
                { "data" : "ingreso", "visible": false },
                { "data" : "IBAN", "visible": false },
                { "data" : "devolucion_importe", "visible": false },
                { "data" : "IBAN_devolucion", "visible": false },
                { "data" : "IBAN_transferencia_SEPA", "visible": false },
                { "data" : "IBAN_transferencia_SWIFT", "visible": false },
                { "data" : "resto_SWIFT", "visible": false },
                { "data" : "resto_cuenta", "visible": false },
                { "data" : "resto_banco", "visible": false },
                { "data" : "resto_direccion", "visible": false },
                { "data" : "resto_ciudad", "visible": false },
                { "data" : "resto_pais", "visible": false },
                { "data" : "resto_pais_code", "visible": false },
                { "data" : "campo_150", "visible": false },
                { "data" : "campo_152", "visible": false },
                { "data" : "campo_1" },
                { "data" : "campo_3" },
                { "data" : "campo_153", "visible": false },
                { "data" : "campo_155", "visible": false },
                { "data" : "campo_4" },
                { "data" : "campo_6" },
                { "data" : "campo_7" },
                { "data" : "campo_9" },
                { "data" : "campo_10" },
                { "data" : "campo_11" },
                { "data" : "campo_12" },
                { "data" : "campo_13" },
                { "data" : "campo_14" },
                { "data" : "campo_15" },
                { "data" : "campo_156", "visible": false },
                { "data" : "campo_158", "visible": false },
                { "data" : "campo_16" },
                { "data" : "campo_18" },
                { "data" : "campo_19" },
                { "data" : "campo_21" },
                { "data" : "campo_22" },
                { "data" : "campo_24" },
                { "data" : "campo_25" },
                { "data" : "campo_26" },
                { "data" : "campo_27" },
                { "data" : "campo_28" },
                { "data" : "campo_29" },
                { "data" : "campo_30", "visible": false },
                { "data" : "campo_31", "visible": false },
                { "data" : "campo_32" },
                { "data" : "campo_33" },
                { "data" : "campo_34", "visible": false },
                { "data" : "campo_35", "visible": false },
                { "data" : "campo_36" },
                { "data" : "campo_37" },
                { "data" : "campo_38", "visible": false },
                { "data" : "campo_39", "visible": false },
                { "data" : "campo_40" },
                { "data" : "campo_41" },
                { "data" : "campo_42", "visible": false },
                { "data" : "campo_43", "visible": false },
                { "data" : "campo_44", "visible": false },
                { "data" : "campo_45" },
                { "data" : "campo_46" },
                { "data" : "campo_59" },
                { "data" : "campo_60" },
                { "data" : "campo_120" },
                { "data" : "campo_122", "visible": false },
                { "data" : "campo_123" },
                { "data" : "campo_124" },
                { "data" : "campo_62", "visible": false },
                { "data" : "campo_63", "visible": false },
                { "data" : "campo_74", "visible": false },
                { "data" : "campo_76", "visible": false },
                { "data" : "campo_64" },
                { "data" : "campo_66" },
                { "data" : "campo_77" },
                { "data" : "campo_110", "visible": false },
                { "data" : "campo_78" },
                { "data" : "campo_87" },
                { "data" : "campo_69" },
                { "data" : "campo_70", "visible": false  },
                { "data" : "campo_68", "visible": false },
                { "data" : "campo_109", "visible": false },
                { "data" : "campo_71" },
            ]
        });
      }
    }

    const createDataTable309 = () => {
      let data = [];
      try {
        data = {{model_309|safe}};
      } catch (error) {
        console.error(error);
      }
      console.log(data);
      if (data && data.length > 0) {  
        const table = $("#table-309"); 
        table.DataTable ({
            "dom" : "Bti",
            "data" : data,
            "language": {
              "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },     
            {% comment %} "searching": false, {% endcomment %}
            "order": [[2, 'asc'], [0, 'asc'],  [1, 'asc'],],
            "buttons": [
                "copy", "csv", "excel"
            ],
            "columns" : [
                { "data" : "ejercicio" },
                { "data" : "periodo" },
                { "data" : "email" },
                { "data" : "nif_1" },
                { "data" : "nombre_1" },
                { "data" : "apellidos_1" },
                { "data" : "nif_3", "visible": false },
                { "data" : "nif_iva_3", "visible": false },
                { "data" : "pais_3", "visible": false },
                { "data" : "nombre_3", "visible": false },
                { "data" : "apellidos_3", "visible": false },
                { "data" : "nif_4", "visible": false },
                { "data" : "nombre_4", "visible": false },
                { "data" : "apellidos_4", "visible": false },
                { "data" : "seccion5_punto1", "visible": false },
                { "data" : "seccion5_punto4", "visible": false },
                { "data" : "seccion5_punto2", "visible": false },
                { "data" : "seccion5_punto5", "visible": false },
                { "data" : "seccion5_punto3", "visible": false },
                { "data" : "seccion5_punto6", "visible": false },
                { "data" : "seccion6_punto1", "visible": false },
                { "data" : "seccion6_punto4", "visible": false },
                { "data" : "seccion6_punto2", "visible": false },
                { "data" : "seccion6_punto5", "visible": false },
                { "data" : "seccion6_punto3", "visible": false },
                { "data" : "seccion6_punto6", "visible": false },
                { "data" : "complementaria", "visible": false },
                { "data" : "importe_ingreso", "visible": false },
                { "data" : "numero_justificante", "visible": false },
                { "data" : "forma_pago", "visible": false },
                { "data" : "iban", "visible": false },
                { "data" : "marca", "visible": false },
                { "data" : "tipo", "visible": false },
                { "data" : "modelo", "visible": false },
                { "data" : "bastidos", "visible": false },
                { "data" : "clasificacion", "visible": false },
                { "data" : "fabricante", "visible": false },
                { "data" : "tipo_modelo", "visible": false },
                { "data" : "contruccion", "visible": false },
                { "data" : "eslora", "visible": false },
                { "data" : "aeronave_fabricante", "visible": false },
                { "data" : "aeronave_marca", "visible": false },
                { "data" : "aeronave_serie", "visible": false },
                { "data" : "aeronave_ao", "visible": false },
                { "data" : "aeronave_kg", "visible": false },
                { "data" : "casilla01", "visible": false },
                { "data" : "casilla02", "visible": false },
                { "data" : "casilla03", "visible": false },
                { "data" : "casilla04", "visible": false },
                { "data" : "casilla05", "visible": false },
                { "data" : "casilla06" },
                { "data" : "casilla07" },
                { "data" : "casilla08" },
                { "data" : "casilla09" },
                { "data" : "casilla10" },
                { "data" : "casilla11", "visible": false },
                { "data" : "casilla12", "visible": false },
                { "data" : "casilla13", "visible": false },
                { "data" : "casilla14", "visible": false },
                { "data" : "casilla15", "visible": false },
                { "data" : "casilla16", "visible": false },
                { "data" : "casilla17", "visible": false },
                { "data" : "casilla18", "visible": false },
                { "data" : "casilla19", "visible": false },
                { "data" : "casilla20", "visible": false },
                { "data" : "casilla21", "visible": false },
                { "data" : "casilla22", "visible": false },
                { "data" : "casilla23", "visible": false },
                { "data" : "casilla24", "visible": false },
                { "data" : "casilla25", "visible": false },
                { "data" : "casilla26", "visible": false },
                { "data" : "casilla27", "visible": false },
            ]
        });
      }
    }

    const createDataTableLIPE = () => {
      let data = [];
      try {
        {% if model_lipe %}
          data = {{model_lipe|safe}};
        {% else %}
          data = [];
        {% endif %}
      } catch (error) {
        console.error(error);
      }
      console.log(data);
      if (data && data.length > 0) {  
        const table = $("#table-lipe"); 
        table.DataTable ({
            "dom" : "Btip",
            "data" : data,
            "language": {
              "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },     
            {% comment %} "searching": false, {% endcomment %}
            "order": [[2, 'asc'], [0, 'asc'],  [1, 'asc'],],
            "buttons": [
                "copy", "csv", "excel"
            ],
            "columns" : [
              { "data" : "Empresa" },
              { "data" : "EMAIL de la APP" },
              { "data" : "Anno di imposta" },
              { "data" : "VP1 (1)" },
              { "data" : "VP1 (2)" },
              { "data" : "VP1 (3)" },
              { "data" : "VP1 (4)" },
              { "data" : "VP1 (5)" },
              { "data" : "VP2" },
              { "data" : "VP3" },
              { "data" : "VP4" },
              { "data" : "VP5" },
              { "data" : "VP6 (1)" },
              { "data" : "VP6 (2)" },
              { "data" : "VP7" },
              { "data" : "VP8" },
              { "data" : "VP9" },
              { "data" : "VP10" },
              { "data" : "VP11" },
              { "data" : "VP12" },
              { "data" : "VP13 (1)" },
              { "data" : "VP13 (2)" },
              { "data" : "VP14 (1)" },
              { "data" : "VP14 (2)" },
            ]
        });
      }
    }

    const onClickButtonDownload = () => {
      const btn_csv = document.getElementsByClassName("buttons-csv")[0];
      btn_csv.click();
    }

    createDataTable111();
    createDataTable115();
    createDataTable130();
    createDataTable303();
    createDataTable309();
    createDataTableLIPE();
  </script>
{% endblock javascripts %}

