{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Notificaciones MuayTax
{% endblock title %}

{% block stylesheets %}
<link rel="stylesheet" crossorigin href="https://use.fontawesome.com/releases/v6.2.1/css/all.css" type="text/css" />

<link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/swal/custom-swal-buttons.css' %}" />
<link rel="stylesheet" href="{% static 'assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css' %}" />

<style>
    @keyframes swing {
        0% { transform: rotate(0deg); }
        20% { transform: rotate(15deg); }
        40% { transform: rotate(-10deg); }
        60% { transform: rotate(7deg); }
        80% { transform: rotate(-5deg); }
        100% { transform: rotate(0deg); }
    }
    .swing {
        display: inline-block;
        /* animation: swing 1.5s ease-in-out infinite; */
        animation: swing 1.5s cubic-bezier(0.5, 0.05, 0.1, 0.3) infinite;
        transform-origin: top center; /* Swing from the top */
    }
    .time-unit {
        background-color: #e9f7ef;
        border-radius: 0.5rem;
        padding: 0.5rem;
        width: 5rem;
        text-align: center;
    }
    .time-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #0d6efd;
    }
    .time-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .status-badge-font-size {
        font-size: 1rem;
    }
    .progress-bar-start,
    .progress-bar-end {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: absolute;
        top: -90%;
        transform: translateY(-50%);
    }
    .progress-bar-start {
        left: 0;
        transform: translateX(-61%);
    }
    .progress-bar-end {
        right: 0;
        transform: translateX(61%);
    }
    .progress-side-ball {
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        z-index: 1;
    }
    .progress-left-side-ball{
        background-color: #198754;
    }
    .progress-right-side-ball{
        background-color: #eef1f4;
    }
    .progress-text{
        font-size: .875rem;
    }

</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                        Notificaciones
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'app_notifications:notifications_dashboard' %}">Centro de notificaciones</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="">{{object.title}}</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}

<div id="main-container" class="container py-5">
    <div class="card">
        <div id="content-body" class="card-body px-5">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <h2 id="notification-title" class="card-title text-success mb-4 fw-bold">{{ object.title|capfirst }}</h2>
                    
                    <div class="mb-4">
                        <h2 class="h5 mb-2">
                            <i class="bi bi-heart-pulse-fill text-dark"></i>                      
                            Estado
                        </h2>
                        {% if object.status == 'pending' %}
                        <span class="badge bg-warning px-3 py-2 status-badge-font-size">Pendiente</span>
                        {% elif object.status == 'success' %}
                        <span class="badge bg-success px-3 py-2 status-badge-font-size">Enviado</span>
                        {% elif object.status == 'failed' %}
                        <span class="badge bg-danger px-3 py-2 status-badge-font-size">Fallido</span>
                        {% elif object.status == 'canceled' %}
                        <span class="badge bg-secondary px-3 py-2 status-badge-font-size">Cancelado</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-4">
                        <h2 class="h5 mb-2">
                            <i class="bi bi-person-fill text-dark"></i>
                            Remitente
                        </h2>
                        <p>{{ object.sender }} </p>
                    </div>
                    
                    <div class="mb-4">
                        <h2 class="h5 mb-2">
                            <i class="bi bi-calendar-event-fill text-dark"></i>
                            Fecha y Hora de Notificación
                        </h2>
                        <p>{{ object.scheduled_datetime }}</p>
                    </div>
                    
                    <div>
                        <h2 class="h5 mb-2">
                            <i class="fas fa-paper-plane text-dark"></i>
                            Vía de Notificación
                        </h2>
                        <span class="badge bg-secondary">
                            {% if object.notification_method == 'email' %}
                            <i class="bi bi-envelope-fill me-1"></i>
                            {% elif object.notification_method == 'sms' %}
                            <i class="fas fa-comment-dots me-1"></i>
                            {% elif object.notification_method == 'call' %}
                            <i class="bi bi-telephone-fill me-1"></i>
                            {% elif object.notification_method == 'wapp' %}
                            <i class="bi bi-whatsapp me-1"></i>
                            {% endif %}
                            {{ object.get_notification_method_display }}
                        </span>
                    </div>
                </div>
                <div class="col-md-6 d-flex flex-column align-items-center justify-content-center">
                    <div class="row text-center">
                        <i id="animated-bell" class="bi bi-bell text-success mb-3 fa-4x"></i>
                        <div class="d-flex justify-content-center mb-3">
                            <div class="time-unit mx-1">
                                <div id="dias" class="time-value text-dark">00</div>
                                <div class="time-label">Días</div>
                            </div>
                            <div class="time-unit mx-1">
                                <div id="horas" class="time-value text-dark">00</div>
                                <div class="time-label">Horas</div>
                            </div>
                            <div class="time-unit mx-1">
                                <div id="minutos" class="time-value text-dark">00</div>
                                <div class="time-label">Minutos</div>
                            </div>
                            <div class="time-unit mx-1">
                                <div id="segundos" class="time-value text-dark">00</div>
                                <div class="time-label">Segundos</div>
                            </div>
                        </div>
                        <h2 class="h4 text-dark fw-bolder">Tiempo para ejecución</h2>
                    </div>

                    {% if object.status == 'pending' %}
                    <div class="d-flex flex-column w-100 gap-3 m-1 px-5 py-3">
                        <div class="progress position-relative">
                            <div id="progress-bar" class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            <div class="progress-bar-sides">
                                <div class="progress-bar-start">
                                    <div class="progress-side-ball progress-left-side-ball"></div>
                                    <span class="progress-text">Creación</span>
                                </div>
                                <div class="progress-bar-end">
                                    <div class="progress-side-ball progress-right-side-ball"></div>
                                    <span class="progress-text">Ejecución</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% elif object.status != 'canceled' %}
                    <a href="" class="btn btn-link text-dark m-1">
                        <i class="bi bi-file-earmark-text-fill me-1"></i>
                        Ver reporte de envío
                    </a>
                    {% endif %}

                </div>
            </div>
            <div class="mt-4">
                <h2 class="h5 mb-2">Descripción</h2>
                <p class="bg-light p-3 rounded">{{ object.description }}</p>
            </div>
        </div>
    </div>
</div>



{% endblock content %}

{% block javascripts %}

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

<script>
    const progressBar = document.getElementById("progress-bar");
    const daysEl = document.getElementById("dias");
    const hoursEl = document.getElementById("horas");
    const minutesEl = document.getElementById("minutos");
    const secondsEl = document.getElementById("segundos");
    const bellIcon = document.getElementById("animated-bell");
    const container = document.getElementById('main-container');
    const contentBody = document.getElementById('content-body');
    const notificationTitle = document.getElementById('notification-title');

    const notificationStatus = "{{ object.status }}";
    const creationTime = new Date("{{ object.created_at|date:'c' }}").getTime();
    const scheduledTime = new Date("{{ object.scheduled_datetime|date:'c' }}").getTime();
    const totalTime = scheduledTime - creationTime;

    // Regex for mobile detection (placed outside function for efficiency)
    const mobileRegex = /mobi|android/i;

    let countdown;

    function updateCounter() {
        const now = Date.now();
        const timeRemaining = scheduledTime - now;
        const elapsedTime = now - creationTime;

        const percentageElapsed = Math.max(0, Math.min(100, (elapsedTime / totalTime) * 100));
        progressBar.style.width = `${percentageElapsed}%`;

        const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

        daysEl.textContent = days.toString().padStart(2, '0');
        hoursEl.textContent = hours.toString().padStart(2, '0');
        minutesEl.textContent = minutes.toString().padStart(2, '0');
        secondsEl.textContent = seconds.toString().padStart(2, '0');

        if (timeRemaining < 0) {
            clearInterval(countdown);
            resetCountdownDisplay();
            progressBar.style.width = "100%";
            showNotificationSuccess();
        }
    }

    function resetCountdownDisplay() {
        daysEl.textContent = "00";
        hoursEl.textContent = "00";
        minutesEl.textContent = "00";
        secondsEl.textContent = "00";
    }

    function showNotificationSuccess() {
        Swal.fire({
            title: 'Notificación enviada!',
            text: 'La notificación ha sido enviada a su/s destinatario/s.',
            icon: 'success',
            confirmButtonText: 'Cerrar',
            allowOutsideClick: false,
            allowEscapeKey: false,
            customClass: {
                confirmButton: 'w-100 btn-dark',
                actions: 'swal2-action-div-full-width'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.reload();
            }
        });
    }

    function isMobileDevice() {
        return mobileRegex.test(navigator.userAgent);
    }

    function adjustForMobile() {
        container.classList.remove("py-5");
        contentBody.classList.remove("px-5");
        notificationTitle.style.fontSize = "1.75rem";
        bellIcon.classList.remove("fa-4x");
        bellIcon.classList.add("fa-3x");

        document.querySelectorAll('.time-unit').forEach(function (el) {
            el.style.width = "4.5rem";
            el.querySelector('.time-value').style.fontSize = "1.2rem";
            el.querySelector('.time-label').style.fontSize = "0.7rem";
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
        if (notificationStatus === 'pending') {
            updateCounter();
            countdown = setInterval(updateCounter, 1000);
            bellIcon.classList.add("swing");
        }

        if (isMobileDevice()) {
            adjustForMobile();
        }
    });

</script>


{% endblock javascripts %}