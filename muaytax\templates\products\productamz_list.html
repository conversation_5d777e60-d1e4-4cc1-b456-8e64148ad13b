{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Productos Amazon
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {     
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    } 

    .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Productos Amazon</h5>
          </div>
          <ul class="breadcrumb">
            {% if user.role == 'manager' %}
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name|title}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Productos Amazon</a>
            </li>
            {% else %}
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Productos Amazon</a>
            </li>
            {% endif %}
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;" >
          <a href="./new/" class="btn btn-primary"> 
            Crear Nuevo
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."  />
              </div>
              <div class="col-2 d-flex justify-content-end">
                  <a href="#" class="btn btn-dark" data-bs-toggle="modal" data-bs-target="#changeProductModal"> 
                    Asignar coste adquisición
                  </a>
              </div>
            </div>
          </div>
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th>Nombre</th>
                  <th>ASIN</th>
                  <th>Coste de adquisición</th>
                  <th style="width:5%;">Acciones</th>
                </tr>
              </thead>
              <tbody>
                {% for object in object_list %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.name }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.asin }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.price }}</span>
                  </td>
                  <td class="align-middle">
                    <div>
                      <a data-bs-toggle="tooltip" data-bs-placement="top" title="Editar" class="btn btn-icon btn-success" href="{% url 'app_products:amz_detail' object.seller.shortname object.pk %}">
                        <i class="feather icon-edit"></i>
                      </a>
                      <a data-bs-toggle="tooltip" data-bs-placement="top" title="Eliminar" class="btn btn-icon btn-danger" href="{% url 'app_products:amz_delete' object.seller.shortname object.pk %}">
                        <i class="feather icon-trash-2"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% include 'products/include/modal_product.html' %}
  </div>
{% endblock content %}

{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css"> 
  <script>
       $(document).ready(function(){
        const dataTableOptions = {
          paging: false,
          searching: true, 
          ordering: true,
          truncation: true,
          info: true,
          footer: true,
          columnDefs: [
            { targets: 3, orderable: false}
          ],
          {% comment %}
          columnDefs: [
            { targets: 3, orderData: [3, 'desc'] }
          ],
          {% endcomment %}
          language: {
            lengthMenu: "_MENU_",
            zeroRecords: "No se han encontrado productos.",
            info: "_TOTAL_ resultados. ",
            search: "Buscar:",
            infoEmpty: "No hay resultados que coincidan con su búsqueda.",
            infoFiltered: ""
          },
          dom: 'lrtip',
          fixedHeader: true,
        };
        
    
        const dataTable =$("#list-table").DataTable(dataTableOptions);

        $("#search").on("input", function(){
            const filtro =$(this).val();
            console.log(filtro)
            dataTable.search(filtro).draw();
        });

      });
       
  </script>
{% endblock javascripts %}
