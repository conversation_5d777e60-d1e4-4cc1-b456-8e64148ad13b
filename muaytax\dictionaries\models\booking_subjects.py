from django.db import models

class BookingSubject(models.Model):
    code = models.CharField(
        primary_key=True,
        max_length=50,
        verbose_name="<PERSON><PERSON><PERSON>",
        help_text="Codigo del asunto de la llamada"
    )
    description = models.CharField(
        max_length=50,
        verbose_name="<PERSON>unt<PERSON>",
    )

    class Meta:
        verbose_name = "Asunto de llamada"
        verbose_name_plural = "Asuntos de llamada"

    def __str__(self):
        return self.description
    
# @admin.register(BookingSubject)
# class BookingSubjectAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]