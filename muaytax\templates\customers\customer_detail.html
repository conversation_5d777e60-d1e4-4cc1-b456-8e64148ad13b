{% extends "layouts/base.html" %}
{% load crispy_forms_tags crispy_forms_field crispy_forms_filters %}
{% block title %}Cliente{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_customers:list' seller.shortname %}">Clientes</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">{{ object.name }}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" id="form" method="post" enctype="multipart/form-data" action="">
        {% crispy form %}
      </form>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      let asteriskSpan = document.querySelector('label[for="id_address_zip"] .asteriskField');
      if (!asteriskSpan) {
        // create the asterisk span element
        asteriskSpan = document.createElement('span');
        asteriskSpan.classList.add('asteriskField');
        asteriskSpan.innerHTML = '*';
        // add the asterisk span element
        const label = document.querySelector('label[for="id_address_zip"]');
        label.appendChild(asteriskSpan);
      }
      // set the id of asteriskSpan
      asteriskSpan.id = "span_id_address_zip";

      const country = document.getElementById('id_country');
      country.addEventListener('change', () => onChangeCustomerCountry());

      const onChangeCustomerCountry = (force_update = true) => {
        const customer_country = document.getElementById('id_country');
        const address_country = document.getElementById('id_address_country');
        const address_zip = document.getElementById('id_address_zip');
        const span_address_zip = document.getElementById('span_id_address_zip');

        // Si Cambias el pais del proveedor, cambia el pais de la direccion
        if (customer_country && address_country) {
          if (address_country.value == "" || force_update == true) {
            address_country.value = customer_country.value;
          }
        }

        // Si el pais es España, el codigo postal es obligatorio, en caso contrario es opcional
        if (customer_country.value == "ES") {
          span_address_zip.innerHTML = '*';
          address_zip.setAttribute('required', 'required');
        } else {
          span_address_zip.innerHTML = '';
          address_zip.setAttribute('required', 'required');
          address_zip.removeAttribute('required');
        }
      };
      onChangeCustomerCountry(false);
    });
  </script>
{% endblock javascripts %}
