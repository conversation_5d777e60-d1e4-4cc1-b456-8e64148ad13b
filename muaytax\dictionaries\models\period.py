from django.db import models

class Period(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ódigo",
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción",
    )

    order = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name="Orden",
    )

    class Meta:
        verbose_name = "Periodo Fiscal"
        verbose_name_plural = "Periodos Fiscales"
    
    def __str__(self):
        return self.description
    
# @admin.register(Period)
# class PeriodAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
