{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Notificaciones MuayTax
{% endblock title %}

{% block stylesheets %}
<link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
<link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.bootstrap5.min.css' %}" type="text/css"/>
<link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css' %}" type="text/css"/>

<link rel="stylesheet" crossorigin href="https://use.fontawesome.com/releases/v6.2.1/css/all.css" type="text/css" />
<style>
    .table{
        vertical-align: middle;
    }
    .custom-table-head {
        background-color: #f2f2f2!important;
    }
</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                        Notificaciones
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="">Centro de notificaciones</a>
                    </li>
                </ul>
            </div>
            <!-- <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
                <a href="" class="btn btn-dark">
                    <span class="d-none d-lg-none d-xl-block">
                        <i class="fas fa-bell fa-lg"></i>&nbsp;
                        Notificación
                    </span>
                    <span class="d-none d-md-block d-xl-none">
                        <i class="fas fa-bell fa-lg"></i>
                    </span>
                    <span class="d-sm-block d-md-none d-xl-none">
                        <i class="fas fa-bell fa-lg"></i>
                    </span>
                </a>
            </div> -->
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}

<div class="col-12">
    <div class="row d-flex flex-wrap">
        <div class="col-12 col-sm-6 col-md-6 col-lg-3 col-xl-3 d-flex">
            <div class="card rounded-3 border flex-fill">
                <div class="card-block">
                    <div class="d-flex gap-3">
                        <div class="">
                            <i class="fas fa-envelope-open-text fa-4x text-success"></i>
                        </div>
                        <div class="flex-fill">
                            <h3 class="fw-bolder">{{ success_mails }}</h3>
                            <h6 class="f-w-300 text-muted mb-0">
                                <b id="total_notifications_count">Email enviados</b>
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6 col-lg-3 col-xl-3 d-flex">
            <div class="card rounded-3 border flex-fill">
                <div class="card-block">
                    <div class="d-flex gap-3">
                        <div class="">
                            <i class="fas fa-sms fa-4x text-success"></i>
                        </div>
                        <div class="flex-fill">
                            <h3 class="fw-bolder">{{ success_sms }}</h3>
                            <h6 class="f-w-300 text-muted mb-0">
                                <b id="total_notifications_count">Mensajes enviados</b>
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6 col-lg-3 col-xl-3 d-flex">
            <div class="card rounded-3 border flex-fill">
                <div class="card-block">
                    <div class="d-flex gap-3">
                        <div class="">
                            <i class="fa-brands fa-whatsapp fa-4x text-success"></i>
                        </div>
                        <div class="flex-fill">
                            <h3 class="fw-bolder">{{ success_wapps }}</h3>
                            <h6 class="f-w-300 text-muted mb-0">
                                <b id="total_notifications_count">Whatsapp enviados</b>
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6 col-lg-3 col-xl-3 d-flex">
            <div class="card rounded-3 border flex-fill">
                <div class="card-block">
                    <div class="d-flex gap-3">
                        <div class="">
                            <i class="fas fa-phone fa-4x text-success"></i>
                        </div>
                        <div class="flex-fill">
                            <h3 class="fw-bolder">{{ success_calls }}</h3>
                            <h6 class="f-w-300 text-muted mb-0">
                                <b id="total_notifications_count">Llamadas realizadas</b>
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section starts -->
<div class="col-12">
    <div class="row flex-column-reverse flex-lg-row">
        <div class="gap-3 col-sm-12 col-lg-6 d-flex flex-column flex-lg-row justify-content-center justify-content-lg-start filters-row mb-2">
            <!-- status -->
            <div class="btn-group btn-group-toggle" data-toggle="buttons">
                <label class="btn btn-light border align-content-center active" title="">
                    <input type="radio" name="status" class="status-filter" autocomplete="off" value="" checked>
                    <span class="d-none d-lg-none d-xl-block">Todo</span>
                    <span class="d-none d-md-block d-xl-none">Todo</span>
                    <span class="d-sm-block d-md-none d-xl-none">Todo</span>
                </label>
                <label class="btn btn-light border align-content-center" title="Pendiente">
                    <input type="radio" name="status" class="status-filter" autocomplete="off" value="pending">
                    <span class="d-none d-lg-none d-xl-block">Pendiente</span>
                    <span class="d-none d-md-block d-xl-none">
                        <i class="far fa-clock fa-lg"></i>
                    </span>
                    <span class="d-sm-block d-md-none d-xl-none">
                        <i class="far fa-clock fa-lg"></i>
                    </span>
                </label>
                <label class="btn btn-light  border align-content-center" title="Enviado">
                    <input type="radio" name="status" class="status-filter" autocomplete="off" value="success">
                    <span class="d-none d-lg-none d-xl-block">Enviado</span>
                    <span class="d-none d-md-block d-xl-none">
                        <i class="far fa-check-circle fa-lg"></i>
                    </span>
                    <span class="d-sm-block d-md-none d-xl-none">
                        <i class="far fa-check-circle fa-lg"></i>
                    </span>
                </label>
                <label class="btn btn-light border align-content-center" title="Fallido">
                    <input type="radio" name="status" class="status-filter" autocomplete="off" value="failed">
                    <span class="d-none d-lg-none d-xl-block">Fallido</span>
                    <span class="d-none d-md-block d-xl-none">
                        <i class="far fa-times-circle fa-lg"></i>
                    </span>
                    <span class="d-sm-block d-md-none d-xl-none">
                        <i class="far fa-times-circle fa-lg"></i>
                    </span>
                </label>
                <label class="btn btn-light border align-content-center" title="Cancelado">
                    <input type="radio" name="status" class="status-filter" autocomplete="off" value="canceled">
                    <span class="d-none d-lg-none d-xl-block">Cancelado</span>
                    <span class="d-none d-md-block d-xl-none">
                        <i class="fas fa-ban fa-lg"></i>
                    </span>
                    <span class="d-sm-block d-md-none d-xl-none">
                        <i class="far fa-times-circle fa-lg"></i>
                    </span>
                </label>
            </div>
            <!-- notification type selector box filter -->
            <div class="btn-group btn-group-toggle" data-toggle="buttons">
                <label class="btn btn-light  border align-content-center active">
                    <input type="radio" name="notification-type" class="notification-type-filter" autocomplete="off" value="" checked>
                    <span class="">Todo</span>
                </label>
                <label class="btn btn-light  border align-content-center" title="email">
                    <input type="radio" name="notification-type" class="notification-type-filter" autocomplete="off" value="email">
                    <span class="">
                        <i class="fas fa-envelope-open-text fa-lg"></i>
                    </span>
                </label>
                <label class="btn btn-light  border align-content-center" title="sms">
                    <input type="radio" name="notification-type" class="notification-type-filter" autocomplete="off" value="sms">
                    <span class="">
                        <i class="fas fa-sms fa-lg"></i>
                    </span>
                </label>
                <label class="btn btn-light  border align-content-center" title="whatsapp">
                    <input type="radio" name="notification-type" class="notification-type-filter" autocomplete="off" value="wapp">
                    <span class="">
                        <i class="fa-brands fa-whatsapp fa-lg"></i>
                    </span>
                </label>
                <label class="btn btn-light  border align-content-center" title="llamada">
                    <input type="radio" name="notification-type" class="notification-type-filter" autocomplete="off" value="call">
                    <span class="">
                        <i class="fas fa-phone fa-lg"></i>
                    </span>
                </label>
                
            </div>
        </div>
    </div>
</div>
<!-- Filter Section ends -->
<!-- Table section -->
<div class="col-12">
    <div class="row">
        <div class="table-responsive">
            <table id="notification-report-table" class="table table-hover stripe nowrap" style="width: 100%; background-color: rgb(255, 255, 255);">
                <thead class="custom-table-head">
                    <tr>
                        <th style="width: 15%;">Remitente</th>
                        <th style="width: 20%;">Título</th>
                        <th style="width: 30%;">Descripción</th>
                        <th style="width: 5%;">Vía</th>
                        <th style="width: 15%;">Programado</th>
                        <th style="width: 10%;">Estado</th>
                        <!-- <th style="width: 10%;">Acciones</th> -->
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

{% endblock content %}

{% block javascripts %}

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="{% static 'assets/datatables/datatable/2.0.7/js/dataTables.js' %}"></script>
<script>

    let table;
    const $statusFilter = document.querySelectorAll('[name="status"]');
    const $notificationTypeFilter = document.querySelectorAll('[name="notification-type"]');

    window.onload = () => {
        createDT();

        $statusFilter.forEach(filter => {
            filter.addEventListener('change', () => {
                const status = filter.value;
                $statusFilter.forEach(f => {
                    f.checked = false;
                    f.parentElement.classList.remove('active');
                });

                filter.checked = true;
                filter.parentElement.classList.add('active');

                table.column(5).search(status).draw();
            });
        });

        $notificationTypeFilter.forEach(filter => {
            filter.addEventListener('change', () => {
                const notificationType = filter.value;
                $notificationTypeFilter.forEach(f => {
                    f.checked = false;
                    f.parentElement.classList.remove('active');
                });

                filter.checked = true;
                filter.parentElement.classList.add('active');

                table.column(3).search(notificationType).draw();
            });
        });

    }

    const createDT = () => {
        table = $('#notification-report-table').DataTable({
            "serverSide": false,
            "scrollY": "calc(100vh - 250px)",
            "scrollCollapse": true,
            "ajax": {
                "dataSrc": "data",
                "url": "{% url 'app_notifications:notification_report_table' %}",
                "type": "GET",
            },
            "language": {
                "url": "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
                "lengthMenu": "_MENU_",
                "zeroRecords": "No se han encontrado notificaciones.",
                "info": "_START_ a _END_ de un total de _TOTAL_",
                "search": "Buscar:",
                "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                "infoFiltered": ""
            },
            "columns":[
                {
                    "data": "sender",
                    "render": function(data, type, row){
                        let imageUrl = '';
                        let html = '';

                        if (row.sender.code === 'amzvat-department') {
                            imageUrl = '{% static "assets/images/logos/svg/departamentos/isotipo-amzvat.svg" %}';
                        } else if (row.sender.code === 'accounting-es-department') {
                            imageUrl = '{% static "assets/images/logos/svg/departamentos/bandera-españa.svg" %}';
                        } else if (row.sender.code === 'support-usa-department') {
                            imageUrl = '{% static "assets/images/logos/svg/departamentos/bandera-eeuu.svg" %}';
                        }
                        html += '<span class="d-none">' + row.sender.name + '</span>';
                        html += `
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2">
                                    <img src="${imageUrl}" alt="avatar" class="avatar-img">
                                </div>                                            
                                <div class="d-flex flex-column">
                                    <span class="fw-bolder">${row.sender.name}</span>
                                    <span class="text-muted">${row.sender.email}</span>
                                </div>
                            </div>
                        `;
                        return html;
                    }
                },
                {
                    "data": "title",
                    "render": function(data, type, row){
                        return `
                            <span class="">${row.title}</span>
                        `;
                    }
                },
                {
                    "data": "description",
                    "render": function(data, type, row){
                        return `
                            <span class="">${row.description}</span>
                        `;
                    }
                },
                {
                    "data": "notification_method",
                    "render": function(data, type, row) {
                        let icon = '';
                        let html = '';

                        if (row.notification_method === 'email') {
                            icon = '<i class="fas fa-envelope-open-text fa-lg"></i>';
                        } else if (row.notification_method === 'sms') {
                            icon = '<i class="fas fa-sms fa-lg"></i>';
                        } else if (row.notification_method === 'wapp') {
                            icon = '<i class="fa-brands fa-whatsapp fa-lg"></i>';
                        } else if (row.notification_method === 'call') {
                            icon = '<i class="fas fa-phone fa-lg"></i>';
                        }
                        html += '<span class="d-none">' + row.notification_method + '</span>';
                        html += icon;
                        return html;
                    }
                },
                {
                    "data": "scheduled_datetime",
                    "render": function(data, type, row){
                        if (data && (type === 'display' || type === 'filter')) {
                        const date = new Date(data);

                        const day = date.getDate().toString().padStart(2, '0');
                        const month = date.toLocaleString('default', { month: 'short' });
                        const year = date.getFullYear();
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');

                        const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

                        return formattedDate;
                        }
                        return data;
                    }
                },
                {
                    "data": "status",
                    "render": function(data, type, row){
                        let badge = '';
                        let html = '';

                        if (row.status === 'pending') {
                            badge = `
                                <span class="badge badge-warning-lighten">
                                    <i class="fas fa-circle fa-xs me-1 blinking-dot"></i>
                                    Pendiente
                                </span>
                            `;
                        } else if (row.status === 'success') {
                            badge = `
                                <span class="badge badge-success-lighten">
                                    <i class="fas fa-check-circle fa-xs me-1"></i>
                                    Enviado
                                </span>
                            `;
                        } else if (row.status === 'failed') {
                            badge = `
                                <span class="badge badge-danger-lighten">
                                    <i class="fas fa-times-circle fa-xs me-1"></i>
                                    Fallido
                                </span>
                            `;
                        } else if (row.status === 'canceled') {
                            badge = `
                                <span class="badge badge-danger-lighten">
                                    <i class="fas fa-times-circle fa-xs me-1"></i>
                                    Cancelado
                                </span>
                            `;
                        }
                        html += '<span class="d-none">' + row.status + '</span>';
                        html += badge;
                        return html;
                    }
                },
                // {
                //     "data": null,
                //     "orderable": false,
                //     "render": function(data, type, row){
                //         return `
                //             <a href="" class="btn btn-primary btn-sm">Editar</a>
                //         `;
                //     }
                // }

            ],
            "paging": true,
            "searching": true,
            "ordering": true,
            "lengthChange": false,
            "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
            "layout": {
                topEnd: null,
                topCenter: null,
                topStart: null,
            },
            "createdRow": function(row, data, dataIndex){
                const link = "{% url 'app_notifications:notification_detail' 0 %}".replace('0', data.id);
                $(row).addClass('cursor-pointer');
                $(row).attr('data-href', link);
                $(row).on('click', function(){
                    window.location.href = $(this).data('href');
                });
                
            }
        });
    }

</script>
{% endblock javascripts %}