{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Soporte MuayTax
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}" type="text/css" />
  <link rel="stylesheet" href="{% static 'assets/css/plugins/introjs.min.css' %}">
  <link rel="stylesheet" crossorigin href="{% static 'assets/css/loading.css' %}"/>
  <style>
    .swal2-action-div-full-width{
      margin: 1em 1.6em!important;

    }
    .swal2-styled.swal2-confirm{
      background-color: rgba(33, 37, 41, 1)!important;
    }
    .swal2-styled.swal2-cancel{
      background-color: transparent!important;
      color: rgba(33, 37, 41, 1)!important;
    }
    .swal2-actions:not(.swal2-loading) .swal2-styled:hover,
    .swal2-actions:not(.swal2-loading) .swal2-styled:active {
      background-image: none!important;
    }
    .swal2-styled.swal2-cancel:hover{
      background-color: transparent!important;
      text-decoration: underline!important;
    }
    .swal2-btn-full-width{
      width: 100%!important;
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              Soporte
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_bookings:list_bookings_seller' user.seller.shortname %}">Mis llamadas</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_bookings:new_booking' user.seller.shortname %}">Solicitar una llamada</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}

<div class="col-12" id="uploadinfo" v-show="inputUploadType==''">
  <div class="card info">
    <div class="card-header row">
      <div class="col-11 d-flex justify-content-start align-items-center text-left">
        <h5>¿Qué tipo de ayuda necesitas?</h5>
      </div>
    </div>
  </div>
</div>
<div class="row">

  <div class="col-12">
    <div class="event-grid">
      {% if seller.legal_entity != "llc" %}
      {% if bookings_accounting_today or seller.can_access_register_support %}
        <div class="event-card">
          <div class="event-card__content">
            {% if completed_information %}
              <button class="event-card__button" data-url="{% url 'app_bookings:create_new_booking' user.seller.shortname 'contracted-accounting' %}" onclick="window.location.href=this.getAttribute('data-url')">
              </button>
            {% else %}
              <button class="event-card__button" data-url="#" onclick="requestInfoAlert()">
              </button>
            {% endif %}
            <div class="event-card__top-line color-3"></div>
            <div class="event-card__body">
              <h5 class="card-title text-center">Mi Gestor</h5>
              <div class="event-card__image">
                <img src="{% static 'assets/images/booking/contracted-accounting.svg' %}" />
              </div>
            </div>
            <div class="event-card__body event-hover-data text-white">
              <p class="text-center mb-2 f-w-700">
                Pulsa aquí si:
              </p>
              <small>Quieres hablar con tu gestor de temas relacionados con la SL/Autónomo. Gestión de tu contabilidad española.</small>
            </div>
          </div>
        </div>
        {% endif %}
      {% endif %}
      {% if support_m5472 %}
      <div class="event-card">
        <div class="event-card__content">
          {% if completed_information %}
            <button class="event-card__button" data-url="{% url 'app_bookings:create_new_booking' user.seller.shortname 'form-5472-1120' %}" onclick="window.location.href=this.getAttribute('data-url')">
            </button>
          {% else %}
            <button class="event-card__button" data-url="#" onclick="requestInfoAlert()">
            </button>
          {% endif %}
          <div class="event-card__top-line color-5"></div>
          <div class="event-card__body">
            <h5 class="card-title text-center">5472_1120</h5>
            <div class="event-card__image">
              <img src="{% static 'assets/images/booking/form-5472.svg' %}" />
            </div>
          </div>
          <div class="event-card__body event-hover-data text-white">
            <p class="text-center mb-2 f-w-700">
              Pulsa aquí si:
            </p>
            <small>Tienes dudas sobre el formulario 5472-1120</small>
          </div>
        </div>
      </div>
      {% endif %}
      {% if seller.is_boir_contracted %}
      <div class="event-card">
        <div class="event-card__content">
          {% if completed_information %}
            <button class="event-card__button" data-url="{% url 'app_bookings:create_new_booking' user.seller.shortname 'boir-support' %}" onclick="window.location.href=this.getAttribute('data-url')">
            </button>
          {% else %}
            <button class="event-card__button" data-url="#" onclick="requestInfoAlert()">
            </button>
          {% endif %}
          <div class="event-card__top-line color-4"></div>
          <div class="event-card__body">
            <h5 class="card-title text-center">BOIR</h5>
            <div class="event-card__image">
              <img src="{% static 'assets/images/booking/form-5472.svg' %}" />
            </div>
          </div>
          <div class="event-card__body event-hover-data text-white">
            <p class="text-center mb-2 f-w-700">
              Pulsa aquí si:
            </p>
            <small>Tienes dudas sobre el formulario BOIR.</small>
          </div>
        </div>
      </div>
      {% endif %}
      {% if seller.is_b15_contracted or seller.legal_entity == "llc" %}
        {% if bookings_maintenance_llc_today or seller.is_contracted_accounting_usa_today or seller.is_contracted_accounting_usa_basic_today %}
        <div class="event-card">
          <div class="event-card__content">
            {% if completed_information %}
              <button class="event-card__button" data-url="{% url 'app_bookings:create_new_booking' user.seller.shortname 'llc-support' %}" onclick="window.location.href=this.getAttribute('data-url')">
              </button>
            {% else %}
              <button class="event-card__button" data-url="#" onclick="requestInfoAlert()">
              </button>
            {% endif %}
            <div class="event-card__top-line color-3"></div>
            <div class="event-card__body">
              <h5 class="card-title text-center">Soporte de tu LLC</h5>
              <div class="event-card__image">
                <img src="{% static 'assets/images/booking/contracted-accounting.svg' %}" />
              </div>
            </div>
            <div class="event-card__body event-hover-data text-white">
              <p class="text-center mb-2 f-w-700">
                Pulsa aquí si:
              </p>
              <small>Tienes dudas sobre tu LLC.</small>
            </div>
          </div>
        </div>
        {% endif %}
      {% endif %}
      {% if booking_iva_today or booking_iva_contracted_today or raps_contracted %}
      <div class="event-card">
        <div class="event-card__content">
          {% if completed_information %}
            <button class="event-card__button" data-url="{% url 'app_bookings:create_new_booking' user.seller.shortname 'vat-country' %}" onclick="window.location.href=this.getAttribute('data-url')">
            </button>
          {% else %}
            <button class="event-card__button" data-url="#" onclick="requestInfoAlert()">
            </button>
          {% endif %}
          <div class="event-card__top-line color-2"></div>
          <div class="event-card__body">
            <h5 class="card-title text-center">Países IVA</h5>
            <div class="event-card__image">
              <img src="{% static 'assets/images/booking/vat-country.svg' %}" />
            </div>
          </div>
          <div class="event-card__body event-hover-data text-white">
            <p class="text-center mb-2 f-w-700">
              Pulsa aquí si:
            </p>
            <small>Tienes dudas sobre tus IVAS Europeos.</small>
          </div>
        </div>
      </div>
      {% endif %}
      <div class="event-card">
        <div class="event-card__content">
          {% if completed_information %}
            <button class="event-card__button" data-bs-toggle="modal" data-bs-target="#confirm-contract-service-modal">
            </button>
          {% else %}
            <button class="event-card__button" data-url="#" onclick="requestInfoAlert()">
            </button>
          {% endif %}
          <div class="event-card__top-line color-1"></div>
          <div class="event-card__body">
            <h5 class="card-title text-center">Contratar nuevos Servicios</h5>
            <div class="event-card__image">
              <img src="{% static 'assets/images/booking/contract-service.svg' %}" />
            </div>
          </div>
          <div class="event-card__body event-hover-data text-white">
            <p class="text-center mb-2 f-w-700">
              Pulsa aquí si:
            </p>
            <small>Quieres información para contratar un nuevo producto y/o servicio.</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="row">
  <div class="col-12 mt-3">
    <div class="row d-flex justify-content-center alert alert-warning rounded step3">
      <h6>
        <b>
          IMPORTANTE: Ahora la duración de la llamada es variable y puedes elegir entre 15 o 30 minutos según la disponibilidad. Por lo que sólo se tratará el tema seleccionado en la solicitud de llamada.
        </b>
      </h6>
    </div>
  </div>        
</div>

<div class="modal fade modal-animate anim-blur " id="loadingModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" >
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
      <div class="modal-body d-flex flex-column justify-content-center align-items-center">

        <div class="modal-body">
          <div class="d-flex justify-content-center align-items-center text-center">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <p>&nbsp;</p>
              <img style="width:110px;" src="{% static 'assets/images/logo.png' %}"/> 
          </div>
          <p class="text-white text-center mb-0"><b>Estás siendo redirigido para que completes tus datos.</b></p>
          <p class="text-white text-center mb-0"><b>Por favor espera...</b></p>
          
        </div>

      </div>
    </div>
  </div>
</div>

<!-- modal are you sure you want to get an appointment with contracted accouinting -->
<div class="modal fade" id="confirm-contract-service-modal" tabindex="-1" role="dialog" aria-labelledby="contractedAccountingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="contractedAccountingModalLabel">Contratar Servicio de Contabilidad</h5>
        <button type="button" id="close-presentation-hmrc" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>¿Estás seguro que quieres información acerca de contratar un nuevo producto y/o servicio?</p>
        <p>Para cualquier otra duda siempre puedes agendar llamada en la opción correspondiente</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-primary" data-url="{% url 'app_bookings:create_new_booking' user.seller.shortname 'contract-service' %}" onclick="window.location.href=this.getAttribute('data-url')">Sí, solicitar</button>
      </div>
    </div>
  </div>
</div>

{% endblock content %}

{% block javascripts %}

<!-- tour Js -->
<script src="{% static 'assets/js/plugins/intro.min.js' %}"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
{% comment %}
<script src="{% static 'assets/js/tour-bookings.js' %}"></script>
{% endcomment %}
<script>
  const shortname = "{{ user.seller.shortname }}";

  function requestInfoAlert() {
    let nextUrl = "{% url 'app_bookings:new_booking' user.seller.shortname %}"
    const url = "{% url 'users:profile' user.username %}?next=" + encodeURIComponent(nextUrl) + "#user-set-seller";
    const html = `
      <h5>Para solicitar este servicio, por favor completa la información de tu perfil:</h5>
      <div class="mt-3 text-danger small">
        <li>Teléfono de contacto</li>
      </div>
    `;
    Swal.fire({
      title: 'Información incompleta',
      text: 'Para solicitar este servicio, por favor completa la información de tu perfil.',
      html: html,
      icon: 'info',
      showCancelButton: true,
      confirmButtonText: 'Completar información >>',
      cancelButtonText: 'Cancelar',
      customClass: {
        confirmButton: 'swal2-btn-full-width',
        actions: 'swal2-action-div-full-width',
      },
    }).then((result) => {
      if (result.isConfirmed) {
        const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
          keyboard: false,
          backdrop: 'static'
        });
        modalLoading.show();
        window.location.href = url;
      }
    });
  }
</script>

{% endblock javascripts %}