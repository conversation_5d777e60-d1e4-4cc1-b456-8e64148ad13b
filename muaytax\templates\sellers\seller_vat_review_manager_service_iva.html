{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}
{% load gravatar %}
{% load custom_filters %}

{% block title %}Validación del Formulario IVA{% endblock title %}

{% block stylesheets %}
  <link href="{% static 'assets/css/select2/select2.min.css' %}" rel="stylesheet">
  <!-- Estilos personalizados CSS -->
  <link href="{% static 'assets/css/formIVA/sellerVat-formIVA.base.css' %}" rel="stylesheet">
  <!-- CDNs externos -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css" />
{% endblock %}
{% block breadcrumb %}
  <div class="page-header text_font">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <!-- T<PERSON><PERSON>lo principal con ícono de guía -->
          <div class="page-header-title d-flex align-items-center">
            <h5 class="m-b-10 me-2">
              Validación del gestor para el formulario IVA - Cliente {{ seller.shortname }}
            </h5>
            <!-- Enlace a la guía con ícono y palabra clave -->
            <button class="btn p-0 bg-transparent border-0 d-flex align-items-center" type="button" data-bs-toggle="modal" data-bs-target="#managerGuideModal" title="Ver guía del gestor">
              <span class="text-info fw-semibold">[Guía &nbsp;<i class="custom-icon fa-solid fa-circle-info text-info"></i>]</span>
            </button>
          </div>
          <!-- Breadcrumb -->
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}">
                <i class="feather icon-home"></i>
              </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}">
                {% if seller.name %}
                  {{ seller.name.capitalize }}
                {% else %}
                  Resumen
                {% endif %}
              </a>
            </li>
            <li class="breadcrumb-item active text-dark">
              Validación del gestor
            </li>
          </ul>
        </div>

        <div class="col">
          <!-- Botón para leyenda visual -->
          <button class="btn_green" style="float:right;" type="button" data-bs-toggle="modal" data-bs-target="#legendModal">leyenda</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Includes de los modales -->
  {% include "sellers/include/service_iva/manager/guide/manager_validation_guide_modal.html" %}
  {% include "sellers/include/service_iva/manager/guide/manager_validation_legend_modal.html" %} 
{% endblock %}

{% block content %}
<div class="row">
  <!-- TARJETA 1: Info general y socios -->
  <div class="card-body text_font col-12 col-md-6">
    <div id="generalTabsCard">
      {% include "sellers/include/service_iva/manager/review/manager_general_tabs_card.html" %}
    </div>
  </div>

  <!-- TARJETA 2: Documentación y migración por país -->
  <div class="card-body text_font col-12 col-md-6">
    <div id="countryTabsCard">
      {% include "sellers/include/service_iva/manager/review/country_tabs_card.html" %}
    </div>
  </div>
  <div class="col-12 text-center mt-4">
    <button class="btn btn_green" onclick="confirmFinalValidation()">Validar Formulario IVA</button>
  </div>
</div>

{% include "sellers/include/service_iva/shared/loading_modal.html" %}
{% include "sellers/include/service_iva/manager/review/validation_modal_by_fields.html" %}
{% include "sellers/include/service_iva/manager/review/manager_validation_summary_modal.html" %}

{% endblock %}

{% block javascripts %}
<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{% static 'assets/js/select2/select2.min.js' %}"></script>

<!-- DATATABLES IMPORTATIONS -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
<!-- DATATABLES IMPORTATIONS -->

<!-- sweet alert Js -->
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script>
  let currentFieldId = null;
  const totalCountries = {{ contracted_countries|length }};

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== "") {
      const cookies = document.cookie.split(";");
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === name + "=") {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  async function showCustomAlert(type, title, message) {
    return await Swal.fire({
      icon: type,
      title: title,
      html: message,
      allowOutsideClick: false,
      allowEscapeKey: false,
      confirmButtonText: "OK",
      buttonsStyling: false,
      customClass: {
        confirmButton: "btn btn_green",
      },
    });
  }

  function openValidationModal(id, fieldName, fieldValue, fileUrl) {
    currentFieldId = id;
  
    // Limpiar estado previo
    document.getElementById("validationComment").value = '';
    document.getElementById("commentSection").classList.add("d-none");
    document.getElementById("validRadio").checked = true;
  
    // Mostrar nombre del campo
    document.getElementById("modalFieldName").innerText = fieldName;
  
    // Mostrar valor del campo o archivo
    if (fileUrl && fileUrl.endsWith(".pdf")) {
      document.getElementById("modalFieldValue").innerHTML = `<iframe src="${fileUrl}" width="100%" height="500px"></iframe>`;
    } else if (fileUrl && fileUrl.match(/\.(jpg|jpeg|png|gif)$/)) {
      document.getElementById("modalFieldValue").innerHTML = `<img src="${fileUrl}" class="img-fluid"/>`;
    } else {
      document.getElementById("modalFieldValue").innerText = fieldValue || "No proporcionado";
    }
  
    document.getElementById("modalFileLink").innerHTML = fileUrl
      ? `<a href="${fileUrl}" target="_blank">Ver documento</a>` : "";
  
    // 🔍 Buscar ícono y estado anterior
    const iconEl = document.querySelector(`#icon-${id} i`);
    if (iconEl && (iconEl.classList.contains("fa-circle-xmark") || iconEl.classList.contains("fa-circle-exclamation"))) {
      document.getElementById("invalidRadio").checked = true;
      document.getElementById("commentSection").classList.remove("d-none");
  
      let commentText = "";
  
      if (id.includes("-full")) {
        // Es una validación grupal (socio completo)
        const groupBlock = document.querySelector(`#icon-${id}`).closest(".border.rounded");
        const commentBlock = groupBlock.querySelector(".manager-validation-comment");
        if (commentBlock) {
          commentText = commentBlock.innerText.replace(/^\s*⚠️\s*/, "").trim();
        }
      } else {
        // Es un campo individual
        const commentBlock = document.querySelector(`#icon-${id}`).closest(".d-flex")?.querySelector(".manager-validation-comment");
        if (commentBlock) {
          commentText = commentBlock.innerText.replace(/^\s*⚠️\s*/, "").trim();
        }
      }
  
      document.getElementById("validationComment").value = commentText;
    }
  
    const modal = new bootstrap.Modal(document.getElementById("validationModal"), {
      backdrop: "static",
      keyboard: false
    });
    modal.show();
  }
  
  async function updateContractedCountriesTable() {
    console.log("🔄 Ejecutando updateContractedCountriesTable...");
    try {
      const res = await fetch("{% url 'app_sellers:vat_manager_review' shortname=seller.shortname %}?action=get_countries_table");
      const data = await res.json();
      console.log("✅ Respuesta de updateContractedCountriesTable:", data);
      document.getElementById("contractedCountriesWrapper").innerHTML = data.html;
    } catch (err) {
      console.error("❌ Error en updateContractedCountriesTable:", err);
    }
  }
  
  async function updateCompanyInfoBlock() {
    console.log("🔄 Ejecutando updateCompanyInfoBlock...");
    try {
      const res = await fetch("{% url 'app_sellers:vat_manager_review' shortname=seller.shortname %}?action=get_company_info_block");
      const data = await res.json();
      console.log("✅ Respuesta de updateCompanyInfoBlock:", data);
      document.getElementById("companyInfoBlock").innerHTML = data.html;
    } catch (err) {
      console.error("❌ Error en updateCompanyInfoBlock:", err);
    }
  }

  async function updatePartnersBlock() {
    console.log("🔄 Ejecutando updatePartnersBlock...");
    try {
      const res = await fetch("{% url 'app_sellers:vat_manager_review' shortname=seller.shortname %}?action=get_partners_block");
      const data = await res.json();
      console.log("✅ Respuesta de updatePartnersBlock:", data);
      document.getElementById("partnersBlock").innerHTML = data.html;
    } catch (err) {
      console.error("❌ Error en updatePartnersBlock:", err);
    }
  }

  async function reloadGeneralTabsCard() {
    try {
      const response = await fetch(window.location.href + "?refresh_general_tabs=1");
      const data = await response.json();
      if (data.html) {
        document.getElementById("generalTabsCard").innerHTML = data.html;
      }
    } catch (error) {
      console.error("Error al recargar los tabs generales:", error);
    }
  }

  async function reloadCountryTabsCard() {
    try {
      const response = await fetch(window.location.href + "?refresh_country_tabs=1");
      const data = await response.json();
      if (data.html) {
        document.getElementById("countryTabsCard").innerHTML = data.html;
      }
    } catch (error) {
      console.error("Error al recargar los tabs de país:", error);
    }
  }

  document.getElementById("invalidRadio").addEventListener("change", () => {
    document.getElementById("commentSection").classList.remove("d-none");
  });
  document.getElementById("validRadio").addEventListener("change", () => {
    document.getElementById("commentSection").classList.add("d-none");
  });

  async function initBootstrapTooltips() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltipTriggerList.forEach(el => new bootstrap.Tooltip(el));
  }

  async function submitValidation() {
    const isValid = document.getElementById("validRadio").checked;
    const comment = document.getElementById("validationComment").value.trim();
    const iconContainer = document.getElementById("icon-" + currentFieldId);
    const validationUrl = "{% url 'app_sellers:manager_field_validation' shortname=seller.shortname %}";
  
    const validationData = {
      field_id: currentFieldId,
      validation_status: isValid ? "correcto" : "incorrecto",
      comment: isValid ? "" : comment,
    };
  
    // Validación: si es incorrecto, debe tener comentario
    if (!isValid && !comment) {
      const modal = bootstrap.Modal.getInstance(document.getElementById("validationModal"));
      modal.hide();
  
      Swal.fire({
        icon: "warning",
        title: "Comentario requerido",
        text: "Debes añadir un comentario si el campo es incorrecto.",
        confirmButtonText: "Volver",
      }).then(() => {
        setTimeout(() => {
          modal.show();
          document.getElementById("invalidRadio").checked = true;
          document.getElementById("commentSection").classList.remove("d-none");
        }, 300);
      });
      return;
    }
  
    // Enviar al backend
    fetch(validationUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCookie("csrftoken"),
      },
      body: JSON.stringify(validationData),
    })
      .then((response) => response.json())
      .then(async (data) => {
        console.log("[VALIDACIÓN] Respuesta del servidor:", data);
  
        // 1. Construcción del ícono
        let iconHTML = "";
        if (isValid) {
          iconHTML = `
            <button type="button" class="btn p-0 bg-transparent"
                    onclick="openValidationModal('${currentFieldId}', '', '', '')">
              <i class="fa-solid fa-circle-check text-success"
                 data-bs-toggle="tooltip"
                 data-bs-placement="top"
                 title="Validado correctamente"></i>
            </button>`;
        } else if (!isValid && comment) {
          iconHTML = `
            <button type="button" class="btn p-0 bg-transparent"
                    onclick="openValidationModal('${currentFieldId}', '', '', '')">
              <i class="fa-solid fa-circle-xmark text-danger"
                 data-bs-toggle="tooltip"
                 data-bs-placement="top"
                 title="Error pendiente"></i>
            </button>`;
        }
  
        // 2. Actualizar icono y tooltip
        if (iconContainer) {
          // 🧹 Destruir tooltip antiguo si existe
          const prevIcon = iconContainer.querySelector('i');
          if (prevIcon) {
            bootstrap.Tooltip.getInstance(prevIcon)?.dispose();
          }
  
          // 💡 Reemplazar HTML e inicializar nuevo tooltip
          iconContainer.innerHTML = iconHTML;
          await initBootstrapTooltips();
        }
  
        // 3. Actualizar comentario en DOM
        const isGroupValidation = currentFieldId.includes("-full");
        let fieldBlock = isGroupValidation
          ? iconContainer.closest(".border.rounded")
          : iconContainer.closest(".d-flex");
  
        let contentArea = isGroupValidation
          ? fieldBlock
          : fieldBlock?.querySelector(".flex-grow-1");
  
        if (fieldBlock && contentArea) {
          // Eliminar comentarios anteriores
          const existingComments = fieldBlock.querySelectorAll(".manager-validation-comment");
          existingComments.forEach(el => el.remove());
  
          // Agregar nuevo comentario si aplica
          if (!isValid && comment) {
            const commentBox = document.createElement("div");
            commentBox.className = "mt-2 p-2 border border-danger rounded bg-light text-danger small manager-validation-comment";
            commentBox.innerHTML = `<i class="fa-solid fa-triangle-exclamation me-1"></i> ${comment}`;
  
            if (isGroupValidation) {
              const header = fieldBlock.querySelector(".d-flex.justify-content-between");
              header?.insertAdjacentElement("afterend", commentBox);
            } else {
              contentArea.appendChild(commentBox);
            }
          }
        }
  
        // 4. Cerrar el modal
        bootstrap.Modal.getInstance(document.getElementById("validationModal")).hide();
      })
      .catch((error) => {
        console.error("[VALIDACIÓN] Error al guardar:", error);
        showCustomAlert("error", "Error", "No se pudo guardar la validación.");
      });
  }
  
  function changeTab(tabId) {
    let progressElement = null;
    let progressValue = 0;
  
    // GRUPO 1: Info General / Socios
    if (tabId === "manager-general-tab") {
      progressValue = 50;
      progressElement = document.getElementById("progressBarGeneral");
    } else if (tabId === "manager-members-tab") {
      progressValue = 100;
      progressElement = document.getElementById("progressBarGeneral");
    }
  
    // GRUPO 2: Países
    else if (tabId.startsWith("manager-general-tab-country-")) {
      const tabs = Array.from(document.querySelectorAll('#managerTabCountry .nav-link'));
      const clickedIndex = tabs.findIndex(tab => tab.id === tabId);
  
      if (clickedIndex >= 0 && totalCountries > 0) {
        const step = 100 / totalCountries;
        progressValue = Math.round(step * (clickedIndex + 1));
        progressElement = document.getElementById("progressBarCountries");
      }
    }
  
    // Aplicar valor a la barra
    if (progressElement) {
      progressElement.style.width = progressValue + "%";
      progressElement.setAttribute("aria-valuenow", progressValue);
    }
  
    console.log(`Tab seleccionado: ${tabId} → Progreso: ${progressValue}%`);
  }

  function updateProgress(group) {
    // funcion para actualizar la barra de progreso
    console.log(`Actualizando Barra de progreso de ${group}`)
  }

  function submitForceFinalValidation() {
    submitFinalValidation(true);
  }

  async function submitFinalValidation(force = false) {
    const loadingModalElement = document.getElementById("LoadingModal");
    const loadingModal = new bootstrap.Modal(loadingModalElement, {
      backdrop: "static",
      keyboard: false
    });
  
    // Cerrar modal de resumen y mostrar loading
    bootstrap.Modal.getInstance(document.getElementById("summaryModal"))?.hide();
    loadingModal.show();
  
    try {
      const response = await fetch("{% url 'app_sellers:vat_manager_review' shortname=seller.shortname %}", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCookie("csrftoken"),
        },
        body: JSON.stringify({ action: "validate_full_form", force: force }),
      });
  
      const result = await response.json();
  
      if (result.status === "ok") {
        setTimeout(async () => {
          loadingModal.hide();
        
          console.log("🔁 Actualizando bloques tras validación...");
        
          await showCustomAlert("success", "Formulario validado", result.message);
          await updateContractedCountriesTable();
          await updateCompanyInfoBlock();
          await updatePartnersBlock();
          await reloadGeneralTabsCard();
          await reloadCountryTabsCard();
          await initBootstrapTooltips();
        }, 1000);
        
      } else {
        loadingModal.hide();
        showCustomAlert("error", "Error", result.message || "Error al validar el formulario");
      }
  
    } catch (error) {
      console.error("❌ Error al enviar validación:", error);
      loadingModal.hide();
      showCustomAlert("error", "Error", "No se pudo validar el formulario. Intenta nuevamente.");
    }
  }

  function confirmFinalValidation() {
    fetch("{% url 'app_sellers:vat_manager_review' shortname=seller.shortname %}?action=get_invalid_fields")
      .then(res => res.json())
      .then(data => {
        let htmlMessage = "";
  
        if (data.invalid_fields && Object.keys(data.invalid_fields).length > 0) {
          for (const [block, fields] of Object.entries(data.invalid_fields)) {
            const blockTitle = block
              .replace("company_info_form", "Datos de la empresa")
              .replace("partner", "Información de socios")
              .replace("documents_by_country_", "Documentos - ")
              .replace("migration_info_", "Migración - ")
              .toUpperCase();
          
            htmlMessage += `
              <div class="card mb-3">
                <div class="card-body">
                  <h6 class="fw-bold mb-2">${blockTitle}</h6>
            `;
          
            if (Array.isArray(fields)) {
              // Caso normal: lista simple
              htmlMessage += `
                <ul class="mb-0 ps-3">
                  ${fields.map(f => `<li>${f}</li>`).join("")}
                </ul>
              `;
            } else if (typeof fields === "object" && fields !== null) {
              // Caso socios: objeto con nombres de socios
              for (const [partnerName, partnerFields] of Object.entries(fields)) {
                htmlMessage += `<div class="mb-2"><strong>${partnerName}</strong><ul class="ps-3">`;
                partnerFields.forEach(f => {
                  htmlMessage += `<li>${f}</li>`;
                });
                htmlMessage += `</ul></div>`;
              }
            }
          
            htmlMessage += `</div></div>`;
          }
          
        } else {
          htmlMessage = `
            <div class="alert alert-success mb-0">
              Todos los campos están correctos.<br>
              <em>Los campos no validados se asumirán como correctos.</em>
            </div>
          `;
        }
  
        // Inyectar contenido en el modal
        document.getElementById("summaryModalBody").innerHTML = htmlMessage;
  
        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById("summaryModal"));
        modal.show();
      })
      .catch(error => {
        console.error("Error obteniendo campos inválidos:", error);
        Swal.fire("Error", "No se pudo obtener la información de validación", "error");
      });
  }
  
  document.addEventListener("DOMContentLoaded", () => {
    // Inicializar barra de progreso para Información General
    changeTab('manager-general-tab');
  
    // Buscar el primer tab de países y activarlo
    const firstCountryTab = document.querySelector('#managerTabCountry .nav-link');
    if (firstCountryTab) {
      changeTab(firstCountryTab.id);
    }

    // Inicializar tooltips de Bootstrap
    initBootstrapTooltips();
    
  });
</script>
{% endblock %}
