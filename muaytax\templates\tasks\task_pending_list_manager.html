{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}

{% block title %}
    Tareas Pendientes
{% endblock title %}

{% block stylesheets %}
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/materialdesignicons/materialdesignicons.min-v5.4.55.css">
    <style>
        #list-table td,
        #list-table th {
            text-align: center;
            vertical-align: middle;
        }
        .table-head {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            z-index: 1;
            text-align: left;
        }
        .card-text {
            text-align: center;
        }
        .card-title {
            margin-bottom: 0;
            text-align: center;
            font-size: 18px;
            color: black;
        }
        .task-count {
            display: inline-block;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background-color: red;
            color: white;
            text-align: center;
            line-height: 25px;
            font-size: 14px;
            font-weight: bold;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .dropdown-form {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: white;
            z-index: 1000;
            min-width: 300px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .dropdown-form.show {
            display: block;
        }
        .toast-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1050;
        }
        
        .modal.show .swal2-container {
            z-index: 1060 !important;
        }
    
        .fa-bell-slash {
            color: gray;
        }
        .fa-bell {
            color: blue;
        }
        .fa-exclamation-triangle {
            color: orange;
        }
        .fa-ban {
            color: red;
        }
    
        .toggle-switch {
            appearance: none !important;
            -webkit-appearance: none !important;
            width: 50px !important;
            height: 26px !important;
            background-color: #dc3545 !important;
            border-radius: 50px !important;
            position: relative !important;
            outline: none !important;
            cursor: pointer !important;
            transition: background-color 0.3s !important;
        }
    
        .toggle-switch:checked {
            background-color: #28a745 !important;
        }
    
        .toggle-switch::before {
            content: '' !important;
            position: absolute !important;
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            background: white !important;
            top: 50% !important;
            left: 3px !important;
            transform: translateY(-50%) !important;
            transition: left 0.3s !important;
        }
    
        .toggle-switch:checked::before {
            left: 27px !important;
        }
    
        .icon-large {
            font-size: 1.25rem !important; /* Ajusta el tamaño según tus necesidades */
        }
    
        .modal-title {
            font-weight: bold !important;
            font-size: 1.5rem !important;
        }
    
        .list-group-item {
            border: none !important;
            border-bottom: 1px solid #e9ecef !important;
        }

        .form-check-input:checked {
            background-image: none !important;
        }
    </style>
{% endblock stylesheets %}

{% block breadcrumb %}
    <div class="page-header">
        <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
            <div class="page-header-title">
                <h5 class="m-b-10">
                <a href="{% url 'home' %}"><i class="feather icon-arrow-left"></i></a> &nbsp; 
                Tareas Pendientes
                </h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                <a href="#">Tareas Pendientes</a>
                </li>
            </ul>
            </div>
        </div>
        </div>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div id="app" class="mt-0">
        <div class="row justify-content-center mt-0">
            <div class="col-3">
                <div class="card rounded m-2">
                    <div class="card-body">
                        <div class="card-title"><b>Tareas Completadas <br> &nbsp; </b></div>
                        <div class="card-text" style='display:none;' v-show="true">
                            <h3 class="f-w-300 align-items-center mt-3 mb-2 mx-2 text-muted"><b>
                                <i class="fa-solid fa-check-double text-success"> &nbsp; [[ completedTasksAll ]]</i> 
                            </b></h3>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card rounded m-2">
                    <div class="card-body">
                        <div class="card-title"><b>Tareas Actuales <br> Pendientes</b></div>
                        <div class="card-text" style='display:none;' v-show="true">
                            <h3 class="f-w-300 align-items-center mt-3 mb-2 mx-2 text-muted"><b>
                                <i class="fa-solid fa-clock text-warning"> &nbsp; [[ pendingTasksAll ]]</i> 
                            </b></h3>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card rounded m-2">
                    <div class="card-body">
                        <div class="card-title"><b>Tareas Actuales <br> Vistas  No Completadas</b></div>
                        <div class="card-text" style='display:none;' v-show="true">
                            <h3 class="f-w-300 align-items-center mt-3 mb-2 mx-2 text-muted"><b>
                                <i class="fa-solid fa-clock text-warning"> &nbsp; [[ seenTasksAll ]]</i>
                            </b></h3>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card rounded m-2">
                    <div class="card-body">
                        <div class="card-title"><b>Tareas Pasadas <br>  Sin Completar</b></div>
                        <div class="card-text" style='display:none;' v-show="true">
                            <h3 class="f-w-300 align-items-center mt-3 mb-2 mx-2 text-muted"><b>
                                <i class="fa-solid fa-clock text-danger"> &nbsp; [[ overdueTasksAll ]]</i>
                            </b></h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card user-profile-list">
                    <div class="card-body">
                        <div class="col-xl-12 col-sm-12 d-flex justify-content-end mb-2">
                            <div class="col-auto list-action-btn-block position-relative">
                                <button
                                    id="dropdownButton"
                                    data-bs-toggle="tooltip" 
                                    class="btn btn-dark"
                                    type="button" 
                                    title="Filtros"
                                    @click="toggleFilters"
                                >
                                    <i class="mdi mdi-filter-outline fa-xl me-0"></i>
                                    <span class="badge top-right-badge rounded-pill bg-danger d-none" id="id-filter-notification">2</span>
                                </button>
                                <div 
                                    id="dropdownFiltersForm" 
                                    class="dropdown-form shadow p-3" 
                                    :class="{ show: showFilters }"
                                >
                                    <!-- submenú de filtros -->
                                    <form id="filtersFormID" class="w-100">
                                        <div class="col-12">
                                            <label for="filterType" class="form-label">Tipos de Tareas</label>
                                            <select id="filterType" class="form-control form-select" v-model="filterType" @change="filterTasks">
                                                <option value="">Todos los Tipos</option>
                                                <option v-for="type in taskTypes" :key="type" :value="type">[[ type ]]</option>
                                            </select>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <label class="form-label">Fecha Límite</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <select id="filterYear" class="form-control form-select" v-model="filterYear" @change="filterTasks">
                                                        <option value="">Años</option>
                                                        <option v-for="year in taskYears" :key="year" :value="year">[[ year ]]</option>
                                                    </select>
                                                </div>
                                                <div class="col-6">
                                                    <select id="filterMonth" class="form-control form-select" v-model="filterMonth" :disabled="!filterYear" @change="filterTasks">
                                                        <option value="">Meses</option>
                                                        <option v-for="(month, index) in months" :key="index" :value="index+1">[[ month ]]</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <label for="filterPeriod" class="form-label">Tiempo</label>
                                            <select id="filterPeriod" class="form-control form-select" v-model="filterPeriod" @change="filterTasks">
                                                <option value="all">Ver Todas</option>
                                                <option value="future">Ver Actuales</option>
                                                <option value="past">Ver Pasadas (Caducadas)</option>
                                            </select>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <div class="row">
                                                <div class="col-6 mt-2">
                                                    <label for="filterSeen" class="form-label">Vistas</label>
                                                    <select id="filterSeen" class="form-control form-select" v-model="filterSeen" @change="filterTasks">
                                                        <option value="all">Todas</option>
                                                        <option value="yes">Vistas</option>
                                                        <option value="no">No Vistas</option>
                                                    </select>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <label for="filterCompleted" class="form-label">Completadas</label>
                                                    <select id="filterCompleted" class="form-control form-select" v-model="filterCompleted" @change="filterTasks">
                                                        <option value="all">Todas</option>
                                                        <option value="completed">Completadas</option>
                                                        <option value="not_completed">No Completadas</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-3">
                                            <button type="button" class="btn btn-secondary w-100" @click="clearFiltersAndClose">[[ clearButtonText ]]</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="col-auto">
                                <a href="{% url 'app_tasks:create_task_pending' %}" class="btn btn-dark" data-bs-toggle="tooltip" title="Crear Nueva Tarea">
                                    <i class="mdi mdi-loupe fa-xl me-0" style="font-size: 1.5rem;"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="dt-responsive table-responsive">
                        <table id="list-table" class="table nowrap">
                            <thead class="table-head">
                                <tr>
                                    <th>Descripcion Corta</th>
                                    <th>Tipo</th>
                                    <th>Alerta</th>                            
                                    <th>Vista</th>
                                    <th>Completada</th>
                                    <th>Tiempo</th>
                                    <th>Fecha Límite</th>
                                    <th>Modificado</th>
                                    <th>Creado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody id="task-table-body">
                                <tr v-for="task in filteredTasks" :key="task.id">
                                    <td style="width:50%;">
                                        <span v-if="task.description.length > 50" data-bs-toggle="tooltip" title="[[ task.description ]]">[[ task.description.substring(0, 50) + '...' ]]</span>
                                        <span v-else>[[ task.description ]]</span>
                                    </td>
                                    <td>[[ task.task_type_name ]]</td>  
                                    <td>
                                        <i 
                                            v-if="task.notification_type === 'none'" 
                                            class="fa fa-bell-slash text-secondary icon-large" 
                                            title="No Notificación"
                                        ></i>
                                        <i 
                                            v-else-if="task.notification_type === 'minimal'" 
                                            class="fa fa-bell text-primary icon-large" 
                                            title="Notificación Mínima"
                                        ></i>
                                        <i 
                                            v-else-if="task.notification_type === 'invasive'" 
                                            class="fa fa-exclamation-triangle text-warning icon-large" 
                                            title="Notificación Invasiva"
                                        ></i>
                                        <i 
                                            v-else-if="task.notification_type === 'blocking'" 
                                            class="fa fa-shield text-danger icon-large" 
                                            title="Notificación Bloqueante"
                                        ></i>
                                    </td>                                 
                                    <td><i :class="['fa', task.seen ? 'fa-check text-success' : 'fa-times text-danger', 'icon-large']"></i></td>
                                    <td><i :class="['fa', task.completed ? 'fa-check text-success' : 'fa-times text-danger', 'icon-large']"></i></td>
                                    <td>
                                        <i v-if="task.days_before_due_date >= 0" 
                                            class="fa fa-clock text-success icon-large" 
                                            title="Tarea Actual" 
                                            data-bs-toggle="tooltip">
                                        </i>
                                        <i v-else 
                                            class="fa fa-clock text-danger icon-large" 
                                            title="Tarea Caducada" 
                                            data-bs-toggle="tooltip">
                                        </i>
                                    </td>
                                    <td>[[ formatDate(task.due_date) ]]</td>
                                    <td>[[ formatDate(task.modified_at, true) ]]</td>
                                    <td>[[ formatDate(task.created_at, true) ]]</td>
                                    <td>
                                        <button @click="showTaskDetails(task)" class="btn btn-icon btn-success" title="Ver detalles" data-bs-toggle="tooltip"><i class="feather icon-eye"></i></button>
                                        <button @click="completeTask(task.id)" class="btn btn-icon btn-primary" title="Completar" data-bs-toggle="tooltip"><i class="feather icon-check"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal | Invasivas o bloqueantes -->
        <div 
            class="modal fade" 
            id="notificationModal" 
            tabindex="-1" 
            aria-labelledby="notificationModalLabel" 
            aria-hidden="true" 
            :class="{ show: notificationModalVisible }"
            @hidden.bs.modal="onNotificationModalHidden"
            @shown.bs.modal="onNotificationModalShown"
            data-bs-backdrop="static" 
            data-bs-keyboard="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="notificationModalLabel">Tareas pendientes de ver invasivas o bloqueantes</h5>
                    </div>
                    <div class="modal-body">
                        <ul class="list-group">
                            <li class="list-group-item d-flex align-items-center justify-content-between" v-for="(task, index) in invasiveTasks" :key="task.id">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <span class="badge rounded-pill me-2 bg-dark">[[ index + 1 ]]</span>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">[[ task.description ]]</h6>
                                        <small class="text-muted">completar abtes del: [[ formatDate(task.due_date) ]]</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-secondary me-3" :class="{ 'bg-warning': task.notification_type === 'invasive', 'bg-danger': task.notification_type === 'blocking' }">[[ task.notification_type === 'invasive' ? 'Invasiva' : 'Bloqueante' ]]</span>
                                    <input class="form-check-input toggle-switch" type="checkbox" v-model="task.seen" @change="updateSeenStatusInvasive(task)" id="toggleSwitch" title="vista" data-bs-toggle="tooltip">
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @click="closeNotificationModal" :disabled="!allTasksSeen">Close</button>
                    </div>
                </div>
            </div>
        </div>

        {% include 'tasks/includes/task_pending_detail.html' %}

        <!-- Mensajes | crada tarea a seller -->
        {% if django_message %}
        <div id="django-message" data-message-type="success">{{ django_message }}</div>
        {% endif %}
    </div>
{% endblock content %}

{% block javascripts %}
    <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>
    <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>

    <script>
        const { ref, reactive, toRefs, onMounted, watch, nextTick, computed } = Vue;

        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer);
                toast.addEventListener('mouseleave', Swal.resumeTimer);
            }
        });
        
        const app = Vue.createApp({
            delimiters: ['[[', ']]'],
            setup() {
                // ###### Variables Reeactivas ######
                const tasks = ref([]);
                const filteredTasks = ref([]);
                const filterType = ref('');
                const filterYear = ref('');
                const filterMonth = ref('');
                const filterPeriod = ref('all');
                const filterSeen = ref('all');
                const filterCompleted = ref('all');
                const taskTypes = ref([]);
                const taskYears = ref([]);
                const taskId = ref(null);
                const taskDescription = ref('');
                const taskDueDate = ref('');
                const taskNotificationType = ref('');
                const taskSeen = ref(false);
                const taskCompleted = ref(false);
                const pendingTasksCount = ref({{ pending_tasks_count }});
                const seenTasksCount = ref({{ seen_tasks_count }});
                const unseenTasksCount = ref({{ unseen_tasks_count }});
                const completedTasksCount = ref({{ completed_tasks_count }});
                const uncompletedTasksCount = ref({{ uncompleted_tasks_count }});
                const numberMinimalNotification = ref({{ min_notification_tasks|yesno:"true,false" }});
                const isModalOpen = ref(false);
                const months = ref(['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre']);
                const showFilters = ref(false);
                const invasiveTasks = ref([]);
                const notificationModalVisible = ref(false);
                const toastQueue = ref([]);
                let dataTable = null;
        
                // Función para formatear fechas
                function formatDate(dateString, includeTime = false) {
                    const options = includeTime
                        ? { day: '2-digit', month: 'short', year: 'numeric', hour: '2-digit', minute: '2-digit' }
                        : { day: '2-digit', month: 'short', year: 'numeric' };
                    return new Date(dateString).toLocaleDateString('es-ES', options);
                }
        
                // Obtener CSRF token
                function getCookie(name) {
                    let cookieValue = null;
                    if (document.cookie && document.cookie !== '') {
                        const cookies = document.cookie.split(';');
                        for (let i = 0; cookies.length > i; i++) {
                            const cookie = cookies[i].trim();
                            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                break;
                            }
                        }
                    }
                    return cookieValue;
                }
        
                const csrfToken = getCookie('csrftoken');
                axios.defaults.headers.common['X-CSRFToken'] = csrfToken;
        
                // ###### Funciones | Metodos ######
        
                // Mostrar un toast de notificación
                const showToast = (message, type, modalToast = false, timer = 1000, position = 'top-end') => {
                    const toastOptions = {
                        icon: type,
                        title: message,
                        showConfirmButton: false,
                        timer: timer,
                        position: position,
                        timerProgressBar: true,
                        didOpen: (toast) => {
                            toast.addEventListener('mouseenter', Swal.stopTimer);
                            toast.addEventListener('mouseleave', Swal.resumeTimer);
                        },
                        willClose: async () => {
                            if (toastQueue.value.length > 0) {
                                const nextToast = toastQueue.value.shift();
                                await new Promise(resolve => setTimeout(resolve, 500)); // Esperar 500 ms antes de mostrar el siguiente toast
                                nextToast();
                            }
                        }
                    };
        
                    if (modalToast) {
                        toastOptions.position = 'center';
                        toastOptions.toast = false;
                        Swal.fire(toastOptions);
                    } else {
                        if (notificationModalVisible.value) {
                            toastQueue.value.push(() => Toast.fire(toastOptions));
                        } else {
                            Toast.fire(toastOptions);
                        }
                    }
                };
        
                // Actualizar el estado de una tarea
                const updateTaskStatus = async (taskId, action) => {
                    try {
                        const response = await axios.post("{% url 'app_tasks:update_task' 0 %}".replace('0', taskId), new URLSearchParams({
                            action: action
                        }));
                        return response.data;
                    } catch (error) {
                        console.error(`Error al actualizar el estado de la tarea: ${action}`);
                        if (debug) throw error;
                    }
                };
                
                // Actualizar el estado de visto de una tarea
                const updateSeenStatus = async (taskId) => {
                    const task = tasks.value.find(t => t.id === taskId);
                    if (!task || task.completed) {
                        return;
                    }
        
                    try {
                        const response = await updateTaskStatus(taskId, 'seen');
                        if (response.status === 'success' && !taskSeen.value) {
                            seenTasksCount.value++;
                            unseenTasksCount.value--;
                            taskSeen.value = 'Sí';
                            loadTasks();
                            showToast('Estado de visto actualizado', 'success');
                        } else {
                            console.error('Error en la respuesta del servidor:', response);
                            showToast('Error al actualizar estado de visto', 'error');
                        }
                    } catch (error) {
                        showToast('Error al actualizar estado de visto', 'error');
                    }
                };
        
                // Actualizar el estado de visto de una tarea invasiva
                const updateSeenStatusInvasive = async (task) => {
                    try {
                        const response = await updateTaskStatus(task.id, 'seen');
                        if (response.status === 'success') {
                            task.seen = true;
                        } else {
                            console.error('Error en la respuesta del servidor:', response);
                            showToast('Error al actualizar estado de visto', 'error', true);
                        }
                    } catch (error) {
                        showToast('Error al actualizar estado de visto', 'error', true);
                    }
                };
        
                // Completar una tarea
                const completeTask = async (taskId, fromModal = false) => {
                    const task = tasks.value.find(t => t.id === taskId);
                    if (!task) {
                        showToast('Tarea no encontrada', 'error');
                        return;
                    }
        
                    if (task.completed) {
                        showToast('Esta tarea ya está completada', 'warning');
                        return;
                    }
        
                    if (!task.seen) {
                        try {
                            const response = await updateTaskStatus(taskId, 'seen');
                            if (response.status === 'success') {
                                task.seen = true;
                            } else {
                                console.error('Error en la respuesta del servidor:', response);
                                showToast('Error al actualizar estado de visto', 'error');
                                return;
                            }
                        } catch (error) {
                            showToast('Error al actualizar estado de visto', 'error');
                            return;
                        }
                    }
        
                    try {
                        const response = await updateTaskStatus(taskId, 'complete');
                        if (response.status === 'success') {
                            task.completed = true;
        
                            if (fromModal) {
                                const modalElement = document.getElementById('detailModal');
                                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                                modalInstance.hide();
        
                                modalElement.addEventListener('hidden.bs.modal', () => {
                                    showToast('Tarea completada exitosamente', 'success');
                                    window.location.href = window.location.href; // Recargar la página actual
                                }, { once: true });
                            } else {
                                showToast('Tarea completada exitosamente', 'success');
                                loadTasks();
                            }
                        } else {
                            console.error('Error en la respuesta del servidor:', response);
                            showToast('Error al completar la tarea', 'error');
                        }
                    } catch (error) {
                        showToast('Error al completar la tarea', 'error');
                    }
                };
        
                // Completar una tarea invasiva
                const completeInvasiveTask = async (task) => {
                    if (task.completed) {
                        showToast('Esta tarea ya está completada', 'warning');
                        return;
                    }
        
                    if (!task.seen) {
                        try {
                            const response = await updateTaskStatus(task.id, 'seen');
                            if (response.status === 'success') {
                                task.seen = true;
                            } else {
                                console.error('Error en la respuesta del servidor:', response);
                                showToast('Error al actualizar estado de visto', 'error', true);
                                return;
                            }
                        } catch (error) {
                            showToast('Error al actualizar estado de visto', 'error', true);
                            return;
                        }
                    }
        
                    try {
                        const response = await updateTaskStatus(task.id, 'complete');
                        if (response.status === 'success') {
                            task.completed = true;
                            const modalElement = document.getElementById('notificationModal');
                            const modalInstance = bootstrap.Modal.getInstance(modalElement);
                            modalInstance.hide();
        
                            modalElement.addEventListener('hidden.bs.modal', () => {
                                showToast('Tarea completada exitosamente', 'success');
                                window.location.href = window.location.href; // Recargar la página actual
                            }, { once: true });
                        } else {
                            console.error('Error en la respuesta del servidor:', response);
                            showToast('Error al completar la tarea', 'error');
                        }
                    } catch (error) {
                        showToast('Error al completar la tarea', 'error');
                    }
                };
        
                // Mostrar los detalles de una tarea
                const showTaskDetails = (task) => {
                    taskId.value = task.id;
                    taskDescription.value = task.description;
                    taskDueDate.value = formatDate(task.due_date);
                    taskNotificationType.value = task.notification_type;
                    taskSeen.value = task.seen;
                    taskCompleted.value = task.completed;
        
                    const modalElement = document.getElementById('detailModal');
                    const modalInstance = new bootstrap.Modal(modalElement);
                    modalInstance.show();
                };
        
                // Alternar la visibilidad de los filtros
                const toggleFilters = () => {
                    showFilters.value = !showFilters.value;
                };
        
                // Filtrar las tareas
                const filterTasks = () => {
                    filteredTasks.value = tasks.value.filter(task => {
                        const taskDueDate = new Date(task.due_date).setHours(0, 0, 0, 0);
                        const now = new Date().setHours(0, 0, 0, 0);
                        let periodMatch = true;
                        if (filterPeriod.value === 'past') {
                            periodMatch = taskDueDate < now;
                        } else if (filterPeriod.value === 'future') {
                            periodMatch = taskDueDate >= now;
                        }
        
                        const seenMatch = filterSeen.value === 'all' ||
                            (filterSeen.value === 'yes' && task.seen) ||
                            (filterSeen.value === 'no' && !task.seen);
                        const completedMatch = filterCompleted.value === 'all' ||
                            (filterCompleted.value === 'completed' && task.completed) ||
                            (filterCompleted.value === 'not_completed' && !task.completed);
        
                        return (filterType.value === '' || task.task_type_name === filterType.value) &&
                            (filterYear.value === '' || taskDueDate.getFullYear().toString() === filterYear.value) &&
                            (filterMonth.value === '' || taskDueDate.getMonth() + 1 === parseInt(filterMonth.value)) &&
                            periodMatch && seenMatch && completedMatch;
                    });
                };
        
                /// ###### Funciones | Computed ######
        
                // Computed property | Verificar si todas las tareas bloqueantes han sido vistas
                const allTasksSeen = computed(() => {
                    return invasiveTasks.value.every(task => task.seen);
                });
        
                // Computed property | Contar las tareas no completadas actuales
                const completedTasksAll = computed(() => {
                    return tasks.value.filter(task => task.completed ).length;
                });
        
                // Computed property | Contar las tareas no completadas actuales
                const pendingTasksAll = computed(() => {
                    return tasks.value.filter(task => !task.completed && new Date(task.due_date).setHours(0, 0, 0, 0) >= new Date().setHours(0, 0, 0, 0)).length;
                });
        
                // Computed property | Contar las tareas vistas no completadas actuales
                const seenTasksAll = computed(() => {
                    return tasks.value.filter(task => task.seen && !task.completed && new Date(task.due_date).setHours(0, 0, 0, 0) >= new Date().setHours(0, 0, 0, 0)).length;
                });
        
                // Computed property | Contar las tareas no completadas pasadas
                const overdueTasksAll = computed(() => {
                    return tasks.value.filter(task => !task.completed && new Date(task.due_date).setHours(0, 0, 0, 0) < new Date().setHours(0, 0, 0, 0)).length;
                });
        
                // Computed property | Texto del botón de limpiar filtros
                const clearButtonText = computed(() => {
                    return filterType.value === '' && filterYear.value === '' && filterMonth.value === '' && filterPeriod.value === 'all' && filterSeen.value === 'all' && filterCompleted.value === 'all' ? 'Cerrar' : 'Limpiar Filtros';
                });
        
                // Cargar las tareas
                const loadTasks = async () => {
                    const response = await fetch("{% url 'app_tasks:json_task_pending_manager' user.username %}");
                    const data = await response.json();
                    // Asegurar que la respuesta sea una lista
                    tasks.value = Array.isArray(data) ? data : [];
                    filteredTasks.value = tasks.value;
                
                    // Clasificar tareas por tipo de notificación
                    const blockingTasks = data.filter(task => task.notification_type === 'blocking' && !task.seen);
                    const invasiveTasksArray = data.filter(task => task.notification_type === 'invasive' && !task.seen);
                
                    // Actualizar la lista de tareas invasivas y bloqueantes
                    invasiveTasks.value = [...blockingTasks, ...invasiveTasksArray];
                
                    // Extraer tipos de tareas y años únicos
                    const types = new Set();
                    const years = new Set();
                    data.forEach(task => {
                        types.add(task.task_type_name);
                        years.add(new Date(task.due_date).getFullYear().toString());
                    });
                    taskTypes.value = Array.from(types);
                    taskYears.value = Array.from(years).sort();
                
                    // Mostrar el modal si hay tareas invasivas o bloqueantes
                    if (blockingTasks.length > 0 || invasiveTasksArray.length > 0) {
                        notificationModalVisible.value = true;
                        const modalElement = document.getElementById('notificationModal');
                        const modalInstance = new bootstrap.Modal(modalElement);
                        modalInstance.show();
                    }
                
                    await nextTick();
                    if (dataTable) {
                        dataTable.destroy();
                    }
                    dataTable = $("#list-table").DataTable({
                        paging: false,
                        searching: true,
                        ordering: true,
                        truncation: true,
                        info: true,
                        footer: true,
                        language: {
                            lengthMenu: "_MENU_",
                            zeroRecords: "No se han encontrado tareas.",
                            info: "_TOTAL_ resultados. ",
                            search: "Buscar:",
                            infoEmpty: "No hay resultados que coincidan con su búsqueda.",
                            infoFiltered: ""
                        },
                        dom: 'lrtip',
                        fixedHeader: true,
                        columnDefs: [
                            { orderable: false, targets: [0, 4, 5, 7] },
                            { targets: '_all', className: 'dt-center' }
                        ],
                        order: [6, 'asc'], // Ordenar por la columna de días restantes por defecto
                        drawCallback: function(settings) {
                            $('[data-bs-toggle="tooltip"]').tooltip({
                                container: 'body'
                            });
                        }
                    });
                };
        
                // Función para limpiar los filtros y cerrar el submenú
                const clearFiltersAndClose = () => {
                    clearFilters();
                    showFilters.value = false;
                };
        
                // Limpiar los filtros
                const clearFilters = () => {
                    filterType.value = '';
                    filterYear.value = '';
                    filterMonth.value = '';
                    filterPeriod.value = 'all';
                    filterSeen.value = 'all';
                    filterCompleted.value = 'all';
                    filterTasks();
                };
        
                // Cerrar el modal de notificaciones
                const closeNotificationModal = async () => {
                    // Actualizar a vista las invasivas de la lista al cerrar modal
                    const updatePromises = invasiveTasks.value.map(task => {
                        task.seen = true;
                        return updateSeenStatusInvasive(task);
                    });
        
                    notificationModalVisible.value = false; // Actualiza que modal está cerrado
                    // Cerrar el modal
                    const modalElement = document.getElementById('notificationModal');
                    const modalInstance = bootstrap.Modal.getInstance(modalElement);
                    modalInstance.hide();
        
                    await waitForModalToClose(); // Espera a que el modal se cierre completamente
                    await Promise.all(updatePromises); // Espera a que todas las promesas de actualización se completen
        
                    showToast('Todas las tareas han sido marcadas como vistas.', 'success', true, 2000, 'center'); // Notificación de 3s en el centro
        
                    // Esperar a que se cierre el toast de confirmación antes de verificar las tareas mínimas
                    setTimeout(async () => {
                        await minimalShowToast(); // Llama a minimalShowToast después de cerrar el modal y mostrar el mensaje de éxito
                    }, 2000); // Esperar 2 segundos antes de mostrar el toast de notificación mínima
                };
        
                // Mostrar el toast de las minimal
                const minimalShowToast = async () => {
                    // Filtrar tareas mínimas no vistas
                    const minimalTasksNotSeen = tasks.value.filter(task => task.notification_type === 'minimal' && !task.seen);
                    if (minimalTasksNotSeen.length > 0) {
                        await waitForModalToClose();
                        showToast('Tienes tareas pendientes con notificación mínima.', 'info');
                    }
                };
        
                // Promesa de cierre de Modal
                const waitForModalToClose = () => {
                    return new Promise((resolve) => {
                        if (!notificationModalVisible.value) {
                            resolve();
                        } else {
                            const observer = new MutationObserver((mutations) => {
                                mutations.forEach((mutation) => {
                                    if (mutation.attributeName === 'style' && getComputedStyle(document.getElementById('notificationModal')).display === 'none') {
                                        observer.disconnect();
                                        resolve();
                                    }
                                });
                            });
        
                            observer.observe(document.getElementById('notificationModal'), { attributes: true });
                        }
                    });
                };
        
                // Manejar el estado del modal de detalles
                const onModalHidden = async () => {
                    if (!taskSeen.value) {
                        await updateSeenStatus(taskId.value); // Esperar a que se actualice el estado
                    }
                    isModalOpen.value = false;
                };
        
                const onModalShown = () => {
                    isModalOpen.value = true;
                };
        
                const showDjangoMessage = () => {
                    const messageElement = document.getElementById('django-message');
                    if (messageElement) {
                        const messageType = messageElement.getAttribute('data-message-type') || 'info';
                        showToast(messageElement.textContent, messageType, true, 4000, 'center');
                    }
                };
            
                document.addEventListener('DOMContentLoaded', () => {
                    showDjangoMessage();
                });
        
                // ###### Watches ######
        
                watch([filterType, filterYear, filterMonth, filterPeriod, filterSeen, filterCompleted], () => {
                    filterTasks();
                });
        
                watch(isModalOpen, (newVal, oldVal) => {
                    if (!newVal && oldVal) {
                        if (taskId.value && !taskSeen.value) {
                            updateSeenStatus(taskId.value);
                        }
                    }
                });
        
                watch(filterYear, (newVal) => {
                    if (newVal === '') {
                        filterMonth.value = '';
                    }
                    filterTasks();
                });
        
                // Función inicial
                onMounted(async () => {
                    await loadTasks();
        
                    // Verificar si hay tareas invasivas o bloqueantes
                    if (invasiveTasks.value.length === 0) {
                        await minimalShowToast();
                    }
        
                    window.addEventListener('popstate', function (event) {
                        location.reload();
                    });
        
                    // Event listeners for the modal
                    document.getElementById('detailModal').addEventListener('hidden.bs.modal', onModalHidden);
                    document.getElementById('detailModal').addEventListener('shown.bs.modal', onModalShown);
        
                    // Esconder tooltips cuando se haga clic en ellos
                    document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(element => {
                        element.addEventListener('click', function () {
                            const tooltip = bootstrap.Tooltip.getInstance(element);
                            if (tooltip) {
                                tooltip.hide();
                            }
                        });
                    });
        
                });
        
                return {
                    ...toRefs(reactive({
                        tasks,
                        filteredTasks,
                        filterType,
                        filterYear,
                        filterMonth,
                        filterPeriod,
                        filterSeen,
                        filterCompleted,
                        taskTypes,
                        taskYears,
                        taskId,
                        taskDescription,
                        taskDueDate,
                        taskNotificationType,
                        taskSeen,
                        taskCompleted,
                        pendingTasksCount,
                        seenTasksCount,
                        unseenTasksCount,
                        completedTasksCount,
                        uncompletedTasksCount,
                        months,
                        showFilters,
                        notificationModalVisible,
                        invasiveTasks,
                        toastQueue,
                        isModalOpen,
                        completedTasksAll,
                        pendingTasksAll,
                        seenTasksAll,
                        overdueTasksAll 
                    })),
                    updateSeenStatus,
                    loadTasks,
                    showToast,
                    completeTask,
                    completeInvasiveTask,
                    showTaskDetails,
                    filterTasks,
                    formatDate,
                    toggleFilters,
                    clearFilters,
                    closeNotificationModal,
                    allTasksSeen,
                    updateSeenStatusInvasive,
                    onModalHidden,
                    onModalShown,
                    clearFiltersAndClose,
                    clearButtonText
                };
            },
        }).mount('#app');
    </script>
{% endblock javascripts %}
