from django.db import models


class InvoiceType(models.Model):
    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.Char<PERSON>ield(
        max_length=50,
        verbose_name="Descripción"
    )

    category = models.Char<PERSON>ield(
        max_length=50,
        verbose_name="Categoria"
    )

    class Meta:
        verbose_name = "Tipo de factura"
        verbose_name_plural = "Tipo de facturas"

    def __str__(self):
        return self.description


# @admin.register(InvoiceType)
# class InvoiceTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "category"]
#     search_fields = ["code", "description", "category"]
