"""
Base settings to build other settings files upon.
"""
import os
from pathlib import Path

import environ
from faxplus import ApiClient, Configuration

ROOT_DIR = Path(__file__).resolve(strict=True).parent.parent.parent
# muaytax/
APPS_DIR = ROOT_DIR / "muaytax"
VUE_DIR = ROOT_DIR / "vue"
env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=False)
if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(ROOT_DIR / ".env"))

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", False)
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
# TIME_ZONE = "UTC"
TIME_ZONE = "Europe/Madrid"

# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "es-ES"
# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-l10n
USE_L10N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#locale-paths
LOCALE_PATHS = [str(ROOT_DIR / "locale")]

# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases
DATABASES = {"default": env.db("DATABASE_URL")}
DATABASES["default"]["ATOMIC_REQUESTS"] = True
# https://docs.djangoproject.com/en/stable/ref/settings/#std:setting-DEFAULT_AUTO_FIELD
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "config.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "config.wsgi.application"

# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # "django.contrib.humanize", # Handy template tags
    "django.contrib.admin",
    "django.forms",
    "django_extensions",
]
THIRD_PARTY_APPS = [
    "crispy_forms",
    "crispy_bootstrap5",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "rest_framework",
    "rest_framework.authtoken",
    "rest_framework_simplejwt",
    "corsheaders",
    "drf_spectacular",
    "hijack",
    # "hijack.contrib.admin",
    "django_gravatar",
    "pycountry",
    "pypdf",
    "geopy",
    "pandas",
    "openpyxl",
    "xlsxwriter",
    'import_export',
    'django_celery_beat',
    'dal',
    'dal_select2',
    # "celery",
    "phonenumber_field",
    "api_rest",
]

LOCAL_APPS = [
    "muaytax.app_address",
    "muaytax.app_banks",
    "muaytax.app_importers",
    "muaytax.app_marketplaces",
    "muaytax.app_documents",
    "muaytax.app_products",
    "muaytax.app_workers",
    "muaytax.app_providers",
    "muaytax.app_partners",
    "muaytax.app_sellers",
    "muaytax.app_customers",
    "muaytax.app_representatives",
    "muaytax.app_invoices",
    "muaytax.app_vat",
    "muaytax.dictionaries",
    "muaytax.users",
    "muaytax.app_bookings",
    "muaytax.app_esign",
    "muaytax.app_lists",
    "muaytax.app_fax",
    "muaytax.app_notifications",
    "muaytax.app_services",
    "muaytax.app_annotations",
    "muaytax.app_tasks",
    "muaytax.app_hmrc",
    "muaytax.app_landing",
    "muaytax.app_ocr",
    # "muaytax.compliances",
    "django_cleanup.apps.CleanupConfig",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = [
    "config.custom-auth-backend.CustomBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-user-model
AUTH_USER_MODEL = "users.User"
# https://docs.djangoproject.com/en/dev/ref/settings/#login-redirect-url
LOGIN_REDIRECT_URL = "users:redirect"
# https://docs.djangoproject.com/en/dev/ref/settings/#login-url
LOGIN_URL = "account_login"
# https://docs.djangoproject.com/en/dev/ref/settings/#logout-redirect-url
LOGOUT_REDIRECT_URL = "users:redirect"

from urllib.parse import quote
from kombu import Queue, Exchange  # type: ignore

REDIS_PASSWORD = quote(env('REDIS_PASSWORD'), safe='')
# celery settings
CELERY_BROKER_URL = f'redis://:{REDIS_PASSWORD}@redis:6379/0'  # Esta URL se refiere a la base de datos 0 en Redis, que se utiliza para almacenar las colas de tareas de Celery.
CELERY_RESULT_BACKEND = f'redis://:{REDIS_PASSWORD}@redis:6379/1'  # Esta URL se refiere a la base de datos 1 en Redis, que se utiliza para almacenar los resultados de las tareas de Celery.
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
CELERY_TASK_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Europe/Madrid'
CELERY_ENABLE_UTC = True
CELERY_RESULT_EXPIRES = 60 * 60 * 24  # 1 day
CELERY_TASK_TIME_LIMIT = 3600  # 1h

CELERY_TASK_DEFAULT_QUEUE = 'default'
CELERY_TASK_DEFAULT_EXCHANGE = 'default'
CELERY_TASK_DEFAULT_EXCHANGE_TYPE = 'direct'
CELERY_TASK_DEFAULT_ROUTING_KEY = 'default'
CELERY_TASK_QUEUES = (
    Queue('default', Exchange('default'), routing_key='default'),
    Queue('cache_updates', Exchange('cache_updates'), routing_key='cache_updates'),
    Queue('invoice_processing', Exchange('invoice_processing'), routing_key='invoice_processing'),
)

CELERY_TASK_ROUTES = {}


# GOOGLE LOGIN
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE' : [
            'profile',
            'email'
        ],
        'APP': {
            'client_id': env('GOOGLE_CLIENT_ID'), 
            'secret': env('GOOGLE_CLIENT_SECRET'), 
        },
        'AUTH_PARAMS': {
            'access_type':'online',
        }
    }
}

# CELERY_TASK_ROUTES = {
#     'muaytax.app_lists.tasks.cachedlists.update_cached_seller_signal_task': {'queue': 'cache_updates'},
#     # Aquí puedes agregar más rutas específicas según necesites
# }

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
    {"NAME": "config.password_validators.custom_password_validation.UppercasePasswordValidator"},
    {"NAME": "config.password_validators.custom_password_validation.LowercasePasswordValidator"},
    {"NAME": "config.password_validators.custom_password_validation.ExistNumberValidator"},
    {"NAME": "config.password_validators.custom_password_validation.ExistSpecialCharValidator"},

]

# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.common.BrokenLinkEmailsMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "hijack.middleware.HijackUserMiddleware",
    "muaytax.users.middleware.LastActivityMiddleware",
]

# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = str(ROOT_DIR / "staticfiles")

# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/static/"

ASSETS_ROOT = "/static/assets/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [
    str(APPS_DIR / "static"),
    str(VUE_DIR / "register-wizard" / "dist-django/static"),
]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

# ASSETS
# ------------------------------------------------------------------------------
ASSETS_ROOT = os.getenv("ASSETS_ROOT", "/static/assets")
ASSETS_URL = os.getenv("ASSETS_ROOT", "/static/assets")

# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(APPS_DIR / "media")
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#dirs
        "DIRS": [
            str(APPS_DIR / "templates"),
            str(VUE_DIR / "register-wizard" / "dist-django"),  # REGISTER WIZARD
        ],
        # https://docs.djangoproject.com/en/dev/ref/settings/#app-dirs
        "APP_DIRS": True,
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
                "muaytax.users.context_processors.allauth_settings",
            ],
            "libraries": {},
        },
    }
]

# IMPORT-EXPORT-CONFIG
# https://django-import-export.readthedocs.io/en/latest/installation.html#import-export-skip-admin-log

IMPORT_EXPORT_SKIP_ADMIN_LOG = True

# GRAVATAR CONFIG
# https://github.com/twaddington/django-gravatar

GRAVATAR_DEFAULT_IMAGE = 'https://app.muaytax.com/static/assets/images/logo.png'

# https://docs.djangoproject.com/en/dev/ref/settings/#form-renderer
FORM_RENDERER = "django.forms.renderers.TemplatesSetting"

# http://django-crispy-forms.readthedocs.io/en/latest/install.html#template-packs
CRISPY_TEMPLATE_PACK = "bootstrap5"
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"

# FIXTURES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#fixture-dirs
FIXTURE_DIRS = (str(APPS_DIR / "fixtures"),)

# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-httponly
SESSION_COOKIE_NAME = 'muaytax_session'
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_AGE = 43200  # 12H
SECURE_CROSS_ORIGIN_OPENER_POLICY= "same-origin-allow-popups"

# SESSION_COOKIE_AGE = 31536000  # 1 year
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = False
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-browser-xss-filter
SECURE_BROWSER_XSS_FILTER = True
# https://docs.djangoproject.com/en/dev/ref/settings/#x-frame-options
X_FRAME_OPTIONS = "SAMEORIGIN"

# EMAIL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
EMAIL_BACKEND = env(
    "DJANGO_EMAIL_BACKEND",
    default="django.core.mail.backends.smtp.EmailBackend",
)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-timeout
EMAIL_TIMEOUT = 5

# ADMIN
# ------------------------------------------------------------------------------
# Django Admin URL.
ADMIN_URL = "admin/"
# https://docs.djangoproject.com/en/dev/ref/settings/#admins
ADMINS = [('it', '<EMAIL>')]
# https://docs.djangoproject.com/en/dev/ref/settings/#managers
MANAGERS = ADMINS

# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(module)s "
                      "%(process)d %(thread)d %(message)s"
        }
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        }
    },
    "root": {"level": "INFO", "handlers": ["console"]},
}

# django-allauth
# ------------------------------------------------------------------------------
ACCOUNT_ALLOW_REGISTRATION = env.bool("DJANGO_ACCOUNT_ALLOW_REGISTRATION", False)
# https://django-allauth.readthedocs.io/en/latest/configuration.html
ACCOUNT_AUTHENTICATED_LOGIN_REDIRECTS = False
# ACCOUNT_AUTHENTICATION_METHOD = "username"
ACCOUNT_AUTHENTICATION_METHOD = "username_email"
# ACCOUNT_EMAIL_REQUIRED = False
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USERNAME_REQUIRED = False
# ACCOUNT_USERNAME_REQUIRED = True
ACCOUNT_SESSION_REMEMBER = True
ACCOUNT_EMAIL_SUBJECT_PREFIX = 'MUAYTAX - '

ACCOUNT_LOGIN_ATTEMPTS_LIMIT = 5

# templates de mails en inglés muaytax/templates/account/email/email_confirmation_message.html
ACCOUNT_EMAIL_VERIFICATION = "none"
ACCOUNT_CONFIRM_EMAIL_ON_GET = True

ACCOUNT_ADAPTER = "muaytax.users.adapters.AccountAdapter"
# https://django-allauth.readthedocs.io/en/latest/forms.html
ACCOUNT_FORMS = {
    "login": "muaytax.users.forms.CustomLoginForm",
    "signup": "muaytax.users.forms.UserSignupForm",
}

# SOCIALACCOUNT_ADAPTER = "muaytax.users.adapters.SocialAccountAdapter"
# https://django-allauth.readthedocs.io/en/latest/forms.html
# SOCIALACCOUNT_FORMS = {"signup": "muaytax.users.forms.UserSocialSignupForm"}

# django-rest-framework
# -------------------------------------------------------------------------------
# django-rest-framework - https://www.django-rest-framework.org/api-guide/settings/
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': False,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': env('MUAYTAX_JWT_SECRET_KEY'),
    'ISSUER': env('MUAYTAX_JWT_ISSUER'),

    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",
}

# django-cors-headers - https://github.com/adamchainz/django-cors-headers#setup
CORS_URLS_REGEX = r"^/api/.*$"

# By Default swagger ui is available only to admin user(s). You can change permission classes to change that
# See more configuration options at https://drf-spectacular.readthedocs.io/en/latest/settings.html#settings
SPECTACULAR_SETTINGS = {
    "TITLE": "muaytax API",
    "DESCRIPTION": "Documentation of API endpoints of muaytax",
    "VERSION": "1.0.0",
    "SERVE_PERMISSIONS": ["rest_framework.permissions.IsAdminUser"],
    "SERVERS": [
        {"url": "http://127.0.0.1:8000", "description": "Local Development server"},
        {"url": "https://app.muaytax.com", "description": "Production server"},
    ],
}

# ZOHO SIGN API
# ------------------------------------------------------------------------------
ZOHO_SIGN_CLIENT_ID = env('ZOHO_SIGN_CLIENT_ID')
ZOHO_SIGN_CLIENT_SECRET = env('ZOHO_SIGN_CLIENT_SECRET')
ZOHO_SIGN_TEMP_REFRESH_TOKEN = env('ZOHO_SIGN_TEMP_REFRESH_TOKEN')
ZOHO_SIGN_BASE_URL = 'https://sign.zoho.eu/api/v1/requests'

DEEPL_API_KEY = env('DEEPL_API_KEY')

# FAXPLUS
FAXPLUS_API_KEY = env('FAXPLUS_API_KEY')
FAXPLUS_SENDING_NUMBER = env('FAXPLUS_SENDING_NUMBER')
FAXPLUS_RECEIVING_NUMBER = env('FAXPLUS_RECEIVING_NUMBER')
FAXPLUS_RECEIVING_NUMBER_US_5472 = env('FAXPLUS_RECEIVING_NUMBER_US_5472')
FAXPLUS_RECEIVING_NUMBER_US_7004 = env('FAXPLUS_RECEIVING_NUMBER_US_7004')
FAXPLUS_RECEIVING_NUMBER_US_BE15 = env('FAXPLUS_RECEIVING_NUMBER_US_BE15')

# CONFIGURACIÓN DE API CLIENT DE FAXPLUS
conf = Configuration()
conf.access_token = FAXPLUS_API_KEY
FAXPLUS_API_CLIENT = ApiClient(configuration=conf)

# AEAT
URL_AEAT = env('URL_AEAT')
URL_AEAT_VALIDATION = env('URL_AEAT_VALIDATION')
PATH_CERTIFICATE = env('PATH_CERTIFICATE')
PASSWORD_CERTIFICATE = env('PASSWORD_CERTIFICATE')
PATH_VERIFY_CA = env('PATH_VERIFY_CA')

# RECAPTCHA
RECAPTCHA_PUBLIC_KEY = env('RECAPTCHA_SITE_KEY')
RECAPTCHA_PRIVATE_KEY = env('RECAPTCHA_SECRET_KEY')

# CURRENCY_CONVERTER
CURRENCY_API_KEY = env('CURRENCY_API_KEY')

# ENCRYPTATION KEY CA
ENCRYPTION_KEY = env('ENCRYPTION_KEY')

# MARKETPLACE KEY DETAIL
SHOP_AUTH_MARETPLACE = env('SHOP_AUTH_MARETPLACE')

# INVOICE_CREATE_API
INVOICES_MINDEE_CLIENT_KEY= env('INVOICES_MINDEE_CLIENT_KEY')
INVOICES_GELOCATOR_GOOGLEV3_KEY= env('INVOICES_GELOCATOR_GOOGLEV3_KEY')

# GOOGLE AUTH
GOOGLE_CLIENT_ID = env('GOOGLE_CLIENT_ID')
GOOGLE_CLIENT_SECRET= env('GOOGLE_CLIENT_SECRET')

# VERIFACTU
VERIFACTU_SOAP_URL = env('VERIFACTU_SOAP_URL')
VERIFACTU_QR_VALIDATOR_URL = env('VERIFACTU_QR_VALIDATOR_URL')