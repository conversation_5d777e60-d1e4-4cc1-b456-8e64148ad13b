from django.db import models


class EconomicActivity(models.Model):
    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=150,
        verbose_name="<PERSON>ódigo"
    )

    description = models.Char<PERSON>ield(
        max_length=150,
        verbose_name="Descripción"
    )
    cnae = models.Char<PERSON><PERSON>(
        max_length=150,
        verbose_name="cnae"
    )

    activity_code = models.CharField(
        max_length=150,
        verbose_name="Código de actividad"
    )
    
    class Meta:
        verbose_name = "Actividad Economica"
        verbose_name_plural = "Actividades Economicas"

    def __str__(self):
        return self.description


# @admin.register(EconomicActivity)
# class EconomicActivityAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "cnae"]
#     search_fields = ["code", "description", "cnae"]
