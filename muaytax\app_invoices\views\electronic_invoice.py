import tempfile
import json
import random
import string



from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import get_object_or_404
from django.views.generic import View, ListView
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.urls import reverse
from django.conf import settings
from django.db.models import OuterRef, Subquery, Q, Value
from django.template.loader import get_template
from django.views.generic import View, ListView
from django_datatables_view.base_datatable_view import BaseDatatableView
from rest_framework.response import Response

from cryptography.hazmat.primitives.serialization.pkcs12 import load_key_and_certificates
from cryptography.hazmat.primitives.serialization import pkcs12
from cryptography.x509.oid import NameOID

from muaytax.app_invoices.electronic_invoice.facturae_utils import Facturae
from muaytax.app_invoices.electronic_invoice.veriFactu import *
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_invoices.constants import *
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.invoice_status import InvoiceStatus
from muaytax.app_documents.forms.document import DocumentCreateForm
from muaytax.utils.env_resources import logo_url_blue_letter_muaytax
from muaytax.utils.general import get_first_and_last_date

import pdfkit

class CreateXMLInvoiceView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def get(self, request, *args, **kwargs):
        invoice = get_object_or_404(Invoice, pk=self.kwargs['pk'])
        user = request.user.role
        error_messages = []
        
        facturae = Facturae()
        data = facturae.data_invoice_xml(self.kwargs["shortname"], invoice) # Json datos de la factura
        facturae.check_invoice_data(data, error_messages, user, invoice) # Comprobar datos de la factura

        if error_messages:
            return JsonResponse(error_messages, status=500, safe=False)

        if invoice:            
            file_name = f"factura.xml"
            signed_xml_string_utf8 = None

            # Generamos el XML en un directorio temporal
            with tempfile.TemporaryDirectory() as temp_dir:

                xml_content = facturae.generate_xml(data)
                file_path = f"{temp_dir}/archivo_temporal.xml"

                # Escribir el XML en el archivo
                with open(file_path, "wb") as xml_file:
                    xml_file.write(xml_content)
                
                with open(file_path, "r", encoding="utf-8") as file:
                    request = file.read()

                try:
                    signed_xml_string_utf8=facturae._sign_file( request, invoice.seller)

                except Exception as e:
                    print('Error al firmar el XML: ', e)
                
                signed_file_path = file_path.replace(".xml", "_signed.xml")

                with open(signed_file_path, "w", encoding="utf-8") as file:
                    file.write(signed_xml_string_utf8.decode("utf-8"))

                with open(signed_file_path, "rb") as xml_file:
                    updated_xml_content = xml_file.read()

                response = HttpResponse(updated_xml_content, content_type='application/xml')
                
                response['Content-Disposition'] = f'attachment; filename="{file_name}"'
                
                return response
        else:
            return JsonResponse({"error": "Error al generar el xml"}, status=500)

class CreateXMLVeriFactuView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def get(self, request, *args, **kwargs):
        invoice = get_object_or_404(Invoice, pk=self.kwargs['pk'])
        error_messages = []

        actionInvoice = request.GET.get('action') if request.GET.get('action') != '' else None
        args = {"isCancel": True, "invoiceReference": invoice.reference} if actionInvoice == 'cancel_record_aeat' else {}
        
        veriFactu = VeriFactu()
        data = veriFactu.data_invoice_xml(invoice.seller, invoice, args)

        if invoice:
            file_name = f"{invoice.reference}_{invoice.seller.name}.xml"

            # Generamos el XML en un directorio temporal
            with tempfile.TemporaryDirectory() as temp_dir:
                if actionInvoice != 'xml_from_aeat':
                    if actionInvoice == 'check_record_aeat':
                        xml_content = veriFactu.generate_xml_check_record_aeat(data)
                    else:
                        xml_content = veriFactu.generate_xml(data)
                else:
                    xml_content = get_xml_from_aeat(invoice)
                file_path = f"{temp_dir}/archivo_temporal.xml"

                # Escribir el XML en el archivo
                with open(file_path, "wb") as xml_file:
                    xml_file.write(xml_content)
                

                with open(file_path, "r", encoding="utf-8") as file:
                    request = file.read()

                response = HttpResponse(request, content_type='application/xml')

                response['Content-Disposition'] = f'attachment; filename="{file_name}"'

                return response
        else:
            return JsonResponse({"error": "Error al generar el xml"}, status=500)

class SendInvoiceVeriFactuToAEATView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def post(self, request, *args, **kwargs):
        invoice = get_object_or_404(Invoice, pk=self.kwargs['pk'])

        data = json.loads(request.body)
        actionInvoice = data.get('action') if data.get('action') != '' else None
        args = {"isCancel": True, "invoiceReference": invoice.reference} if actionInvoice == 'cancel_record_aeat' else {"isCancel": False, "invoiceReference": invoice.reference}
        
        # Inicializamos la clase VeriFactu
        veriFactu = VeriFactu()
        data = veriFactu.data_invoice_xml(invoice.seller, invoice, args)
        response_dict = {}
        error_message = None

        if invoice:
            file_name = f"factura.xml"

            # Generamos el XML en un directorio temporal
            with tempfile.TemporaryDirectory() as temp_dir:
                if actionInvoice == 'check_record_aeat':
                    xml_content = veriFactu.generate_xml_check_record_aeat(data)
                else:
                    xml_content = veriFactu.generate_xml(data)
                file_path = f"{temp_dir}/archivo_temporal.xml"

                # Escribir el XML en el archivo
                with open(file_path, "wb") as xml_file:
                    xml_file.write(xml_content)
                
                with open(file_path, "r", encoding="utf-8") as file:
                    request = file.read()

                response = HttpResponse(request, content_type='application/xml')

                response['Content-Disposition'] = f'attachment; filename="{file_name}"'

                # Verificar el certificado digital
                verify_cert, cert_path, cert_password, cert = verify_cert_digital(invoice.seller)
                
                if not verify_cert:
                    error_message = "El certificado digital no es válido o no se ha encontrado."
                else:
                    is_representative, same_nif, is_expirated = validations_issuer_info_cert(cert, data.get('nif'), invoice.seller)
                    error_message = "El certificado digital está caducado." if is_expirated else "El certificado de firma digital no coincide con el NIF del obligado tributario." if not same_nif and not is_representative else None
                
                if error_message:
                    return JsonResponse({"error": error_message}, status=500)
                

                response = send_xml_VeriFactu_to_AEAT(response, cert_path, cert_password)

                if response.ok:
                    fault = None

                    try:
                        # Parsear el XML de la respuesta
                        root = etree.fromstring(response.content)

                        # Convertir el XML en un diccionario
                        response_dict = xml_to_dict(root)

                        # Comprobar si hay un error en la formación del XML
                        fault = response_dict.get('Body', {}).get('Fault',{})
                        if fault:
                            error_message = fault.get('detail').get('callstack').split("\n")[0]
                            response_dict = {"ERROR": error_message}
                        else:
                            response_dict = response_dict.get('Body')

                    except etree.XMLSyntaxError as e:
                        response_dict = {"error": f"Error al analizar el XML {str(e)}"}

                    if actionInvoice != 'check_record_aeat' and fault == {}:
                        response_dict = response_from_VeriFactu(data, response_dict, invoice, veriFactu, response, args)

                else:
                    response_dict = {
                        "error": f"Error al enviar el XML a la AEAT {getattr(response, 'text', 'respuesta no válida')}"
                    }
                return JsonResponse(response_dict, safe=False)
        else:
            return JsonResponse({"error": "No se encontró la factura"}, status=500)

class CheckDigitalCertificateView(LoginRequiredMixin, IsSellerShortnamePermission, View):

    form_class = DocumentCreateForm

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs['shortname'])

        doc_url = f"<a href='/sellers/{self.kwargs['shortname']}/docs/documents' target='_blank' class='btn btn-outline-dark' style='padding: 6px;'>Ir a documentos</a>"

        certificate = Document.objects.filter(documentType='ES-SIGNATURE', seller=seller).first()
        cert_path = certificate.file.path if certificate else None
        cert_password = certificate.get_password() if certificate else None


        error_messages = []

        ### VERIFICACIÓN DE CERTIFICADO DE FIRMA DIGITAL (SI EXISTE Y CONTRASEÑA) ###
        if not certificate:
            error_messages.append({"error": f"No se ha encontrado un certificado de firma digital. Por favor comprueba tu certificado {doc_url}"})
        else:
            if certificate.get_password() == "" or certificate.get_password() is None:
                error_messages.append({"error": f"El certificado de firma no tiene contraseña. Por favor comprueba tu certificado {doc_url}"})
        

        ### VERIFICACIÓN DE CERTIFICADO DE FIRMA DIGITAL (FECHA EXPIRACIÓN) ###
        if cert_password:
            with open(cert_path, "rb") as cert_file:
                cert_data = cert_file.read()
            
            p12 = load_key_and_certificates(cert_data, cert_password.encode())
            cert = p12[1]

            expiration_date = cert.not_valid_after_utc
            if expiration_date.replace(tzinfo=None) < datetime.now():
                error_messages.append({"error": f"El certificado de firma digital ha expirado. Por favor comprueba tu certificado {doc_url}"})

        if error_messages:
            return JsonResponse(error_messages, safe=False)
        return JsonResponse({}, safe=False)
    
    def post(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs['shortname'])
        sellerVatES = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
        sellerNif = sellerVatES.vat_number.replace("ES", '') if sellerVatES and sellerVatES.vat_number is not None and sellerVatES.vat_number != ''  else seller.nif_registration 
        file_cert = request.FILES.get('file')
        password = request.POST.get('password')

        if file_cert and password:
            try:
                cert_data = file_cert.read()
                private_key, cert, additional_certs = pkcs12.load_key_and_certificates(cert_data, password.encode())
                if cert:
                    is_representative, same_nif, is_expirated = validations_issuer_info_cert(cert, sellerNif, seller)
                    if is_expirated:
                        return JsonResponse({"error": "El certificado de firma digital ha expirado."}, status=500)
                    elif not is_representative and not same_nif:
                        return JsonResponse({"error": "El certificado de firma digital no coincide con el NIF del obligado tributario."}, status=500)
                    else:
                        post_data = {
                            'password': password,
                            'documentType': DocumentType.objects.get(code='ES-SIGNATURE'),
                            'seller': seller,
                            'expiration_date': cert.not_valid_after_utc.replace(tzinfo=None),
                            'privacy': 'public'
                        }
                        if sellerVatES:
                            post_data['sellerVat'] = sellerVatES.pk

                        form = self.form_class(post_data, request.FILES)
                        if form.is_valid():
                            form.save()
                            return JsonResponse({"success": "Certificado cargado correctamente."}, status=200)
                        else:
                            print("Error al cargar el certificado:", form.errors)
                            return JsonResponse({"error": "Error al cargar el certificado, contacta con soporte"}, status=500)
            except Exception as e:
                print("Error al cargar el certificado:", e)
                return JsonResponse({"error": f"Error al cargar el certificado: contraseña inválida"}, status=500)
        else:
            return JsonResponse({"error": "No se ha proporcionado un certificado o contraseña."}, status=400)

class DownloadResponsibleDeclarationVeriFactu(LoginRequiredMixin, View):
     def get(self, request, *args, **kwargs):
        filename = "DeclaracionResponsableVeriFactu.pdf"

        data = {
            "logo_muaytax": logo_url_blue_letter_muaytax()
        }

        template = get_template("invoices/include/responsible_declaration_veriFactu.html")
        html = template.render(data)

        pdf = pdfkit.from_string(html, False)  

        if not pdf:
            return HttpResponse("Error generando el PDF", status=500)

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

class VerifactuInvoiceList(LoginRequiredMixin, IsManagerRolePermission, ListView):
    model = Invoice
    template_name= "invoices/invoice_list_verifactu.html"

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        invoices_for_verifactu = Invoice.objects.filter(is_generated=True, tax_country__iso_code= 'ES', created_at__gte='2025-05-08').exclude(status='discard').exclude(is_generated_amz=True)
        invoice_years = invoices_for_verifactu.filter(seller_id=seller.id).dates('expedition_date', 'year')


        context = super().get_context_data(**kwargs)
        context["invoice_statuses"] = InvoiceStatus.objects.all().order_by("description")
        context['seller'] = seller
        context['invoice_years'] = [date.year for date in invoice_years]

        print("invoice_years", context['invoice_years'])
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class VerifactuInvoiceListDT(LoginRequiredMixin, IsManagerRolePermission, BaseDatatableView):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    model = Invoice
    columns = ['id','file', 'reference', 'status', 'expedition_date', 'accounting_date', 'is_manager_cancel_sending_to_verifactu']

    def get_initial_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        latest_verifactu_subquery = VerifactuInv.objects.filter(
            seller=seller,
            invoice=OuterRef('pk')
        ).order_by('-created_at')

        # Anotamos la tabla Invoice con un campo de la última VerifactuInv (ejemplo: status_in_verifactu)
        invoice = Invoice.objects.filter(seller=seller, is_generated=True, tax_country__iso_code='ES', created_at__gte='2025-05-08').exclude(invoice_category__code__icontains='_copy').exclude(is_generated_amz=True).order_by('-expedition_date')
        
        qs = invoice.annotate(
            verifactu_status=Coalesce(
                Subquery(latest_verifactu_subquery.values('status_in_verifactu')[:1]),
                Value('not_sent'),
            ),
            verifactu_operation_type=Coalesce(
                Subquery(latest_verifactu_subquery.values('operation_type')[:1]),
                Value('no_info'),
            ),
            verifactu_op_date=Subquery(latest_verifactu_subquery.values('created_at')[:1]),
        )

        self.not_sent_count = qs.filter(verifactu_status='not_sent').count()
        self.total_sent_count = qs.filter(verifactu_status='Correcto').count()
        self.total_sent_with_errors_count = qs.filter(verifactu_status='AceptadoConErrores').count()
        self.total_invoices_count = qs.count()
        
        return qs
    
    def filter_queryset(self, qs):

        status = self.request.GET.get('status')
        if status:
            qs = qs.filter(status=status)

        status_in_verifactu = self.request.GET.get('status_in_verifactu')
        if status_in_verifactu:
            qs = qs.filter(verifactu_status=status_in_verifactu)
        
        operation_type = self.request.GET.get('operation_type')
        if operation_type:
            qs = qs.filter(verifactu_operation_type=operation_type)
        
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')

        if year and period:
            first_date, last_date = get_first_and_last_date(year, period)
            qs = qs.filter(expedition_date__range=(first_date, last_date))
        
        if year and not period:
            qs = qs.filter(expedition_date__year=year)
        
        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(
                Q(reference__icontains=search) |
                Q(pk__icontains=search) 
            )

        return qs
    
    def render_column(self, row, column):

        VERIFACTU_CHOICES_TO_RENDER ={
            'no_info': 'Sin información',
            'not_sent': 'No enviada',
            'Correcto': 'Correcto',
            'AceptadoConErrores': 'Aceptada con errores',
            'Alta': 'Alta',
            'Anulacion': 'Anulación',
        }

        raw_value = getattr(row, column, '')

        if column.endswith('_date'):
            return raw_value.strftime("%d/%m/%Y") if raw_value else '----'
        
        elif column.startswith('verifactu_'):
            return VERIFACTU_CHOICES_TO_RENDER.get(raw_value, raw_value)
        
        return super().render_column(row, column)
    
    def render_to_response(self, context, **response_kwargs):
        response = super().render_to_response(context, **response_kwargs)

        data = json.loads(response.content)
        data['not_sent_count'] = getattr(self, 'not_sent_count', 0)
        data['total_sent_count'] = getattr(self, 'total_sent_count', 0)
        data['total_sent_with_errors_count'] = getattr(self, 'total_sent_with_errors_count', 0)
        data['total_invoices_count'] = getattr(self, 'total_invoices_count', 0)

        return JsonResponse(data, safe=False)