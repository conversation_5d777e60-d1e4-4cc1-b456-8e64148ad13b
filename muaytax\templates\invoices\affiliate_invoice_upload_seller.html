{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css"/>
  <style>
    .uploadzone {
      width: 100%;
      min-height: 50vh !important;
      border: 2px solid rgba(0, 0, 0, .3);
      box-sizing: border-box;
      padding: 20px 20px;
    }

    .dropzone {
      width: 100%;
      min-height: 50vh !important;
      background-image: url("https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png");
      background-size: 85px;
      background-repeat: no-repeat;
      background-position: center 40%;
    }

    .dropzone .dz-message {
      display: block;
      position: absolute;
      top: 53%;
      width: 95%;
      text-align: center;
    }

    .dropzone .dz-preview.dz-error .dz-error-message {
      display: none !important;
    }

    .tooltip-inner a {
      color: white;
      text-decoration: none;
    }

    .hidden {
      display: none;
    }

    .tooltip-inner {
      background-color: black !important;
      color: white !important;
      border-radius: 50px !important;
      padding: 5px 10px !important;
      font-size: 12px;
    }
    .tooltip.bs-tooltip-top .arrow::before {
      border-top-color: black !important;
    }
  </style>
{% endblock stylesheets %}
{% block title %}
  Cargar Factura de Afiliado
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas: Cargar Factura de Afiliado
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' user.seller.shortname %}">Facturas</a>
                </li>
                <li class="breadcrumb-item active">
                  <a href="#">Cargar Factura de Afiliado</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="card-body">
    <div class="col-sm-12">
      <!-- Barra de Progreso -->
      <div class="progress mb-4">
        <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
      </div>

      <!-- Información general -->
      <div class="card" id="step1">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5>Información General: Estos datos son los que deben contener la factura</h5>
          <button type="button" class="btn btn-dark ml-2" onclick="showStep('charger')">
            Continuar para cargar la factura
            <i class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i>
          </button>
        </div>
        <div class="card-body ">
          <div class="row justify-content-center">
            <div class="col-md-4">
              <div class="d-flex align-items-center justify-content-start mb-3">
                <h4 class="mb-0">
                  <i class="fas fa-info-circle fa-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Información de Muay Tax"></i> Si tu cuenta bancaria se encuentra en USA
                </h4>
              </div>
              <p>
                Muay Tax LLC<br>
                EIN Number 32-0615479<br>
                99 Wall Street #173<br>
                New York, NY<br>
                EEUU<br>
                10005
              </p>
            </div>
            <div class="col-md-4">
              <div class="d-flex align-items-center justify-content-start mb-3">
                <h4 class="mb-0">
                  <i class="fas fa-info-circle fa-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Información de Muay Tax"></i> Si tu cuenta bancaria se encuentra en Europa
                </h4>
              </div>
              <p>
                Muay Tax Advisors SL<br>
                CIF B67659607<br>
                c/ Colón 10 Piso 6, Puerta 13<br>
                Valencia<br>
                España<br>
                46004
              </p>
            </div>
            <div class="col-md-2">
              <div class="d-flex align-items-center justify-content-start mb-3">
                <h4 class="mb-0">
                  <i class="fas fa-info-circle fa-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Información de su Empresa"></i> Datos de su empresa:
                </h4>
              </div>
              <p>
                Nombre de la empresa/organización<br>
                Código Swift/BIC<br>
                IBAN/Número de cuenta<br>
                Dirección del destinatario<br>
                Ciudad<br>
                País<br>
                Código postal
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Cargador -->
      <div class="card hidden" id="step2">
        <div class="row">
          <!-- Toast | START  -->
          <div id="toast" class="toast text-white bg-success w-100 fade border" data-bs-animation="true" data-bs-autohide="true" data-bs-delay="5000" role="alert">
            <div class="d-flex">
              <div id="toast-body" class="toast-body">
                <span id="toast-text"></span>
              </div>
              <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
          </div>
          <!-- Toast | END  -->
      
          <div class="vue" id="uploads">
            <!-- Errores | START  -->
            {% if error %}
              <div class="col-12 alert alert-danger text-center mx-auto">
                {{ error | safe }}
              </div>
            {% endif %}
            <!-- Errores | END  -->
      
            <div class="col-12" id="uploadinfo">
              <div class="card info">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Cargador de Factura</h5>
                    <button type="button" class="btn btn-dark ml-2" onclick="showStep('info')">
                      Volver a información
                      <i class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i>
                    </button>
                  </div>
                </div>
                <div class="card-body">
                  <form 
                    method="post" 
                    enctype="multipart/form-data" 
                    action="{% url 'app_invoices:seller_invoices_upload_affiliate' seller.shortname %}" 
                    id="myDropzoneAffiliateInvoice" 
                    class="dropzone"
                  > 
                    {% csrf_token %}
                    <div class="d-none d-print-none">
                      <input type="hidden" id="id" name="seller" value="{{ seller.pk }}">
                    </div>
                    <div class="fallback">
                      <input type="file" id="file" name="file" multiple>
                    </div>
                  </form>
                  <span id="myListContainerAffiliateInvoice"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-3">
            <div class="card">
              <div class="card-block">
                <div class="row d-flex align-items-center">
                  <div class="col">
                    <h4><b>FACTURAS SUBIDAS</b></h4>
                    <h4 class="f-w-300 d-flex align-items-center mt-4 mb-0 text-muted">
                      <b id="invoice-info"></b>
                    </h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dropzone/dropzone.min-v5.js"></script>
  <script>
    // Inicializar tooltips de Bootstrap 5
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    function showStep(p) {
      if (p === 'charger') {
        document.getElementById('step1').classList.add('hidden');
        document.getElementById('step2').classList.remove('hidden');
        document.getElementById('progress-bar').style.width = '100%';
        document.getElementById('progress-bar').setAttribute('aria-valuenow', '100');

      } else if (p === 'info') {
        document.getElementById('step2').classList.add('hidden');
        document.getElementById('step1').classList.remove('hidden');
        document.getElementById('progress-bar').style.width = '50%';
        document.getElementById('progress-bar').setAttribute('aria-valuenow', '50');
      }
    }

    // Variables del backend
    let total = {{ total_invoices }};
    const limit = {{ limit_invoices }};

    // Variables para contador de facturas
    let numFilesUploaded = 0;
    let numFilesErrored = 0;
    let numFilesToUpload = 0;
    let uploadedInvoices = []; // Lista de facturas procesadas correctamente

    // Función para manejar el contador y sus límites
    function countInvoices(total, limit) {
      let displayText = "";
      if (limit === -1) {
        displayText = `<b>Subidas: </b> ${total} <br><b>Límite: </b> Sin Límite`;
      } else {
        displayText = `${total} / ${limit}`;
      }
      document.getElementById("invoice-info").innerHTML = displayText;
    }
    
    // Cargar el contador y sus limites al iniciar 
    countInvoices(total, limit);
    
    // Funcion comun para inicializar Dropzone
    function createDropzone(selector, containerId) {
      Dropzone.options[selector] = {
        init: function () {
          var myList = document.createElement("ul");
          myList.classList.add("list-group");
          document.getElementById(containerId).appendChild(myList);
  
          // Evento que se dispara al agregar un archivo al Dropzone
          this.on("addedfile", (file) => {
            // Incrementar el número de archivos a cargar cuando se agrega un archivo
            numFilesToUpload++;
          });
  
          // Evento que se dispara justo antes de enviar los archivos
          this.on("sending", (file, xhr, formData) => {
            formData.append("total", total);
            formData.append("numFilesToUpload", numFilesToUpload);
            formData.append("is_last_file", false); // Por defecto, no es el último archivo
            formData.append("csrfmiddlewaretoken", document.querySelector("[name=csrfmiddlewaretoken]").value);
          });
          
          // Evento que se dispara cuando un archivo se carga con éxito
          this.on("success", (file, response) => {
            // Incrementar el número de archivos cargados con éxito
            numFilesUploaded++;
            total++;
            uploadedInvoices.push(response.invoice_id); // Agregar el ID de la factura procesada al backend
            // Actualizar la variable reactiva totalInvoices en Vue.js
            data_export.totalInvoices.value++;
            $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
            // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
            var listItem = document.createElement("li");
            listItem.innerText = file.name + " - Subido";
            listItem.classList.add("list-group-item", "list-group-item-success");
            myList.insertBefore(listItem, myList.firstChild);
          });
  
          // Evento que se dispara cuando ocurre un error durante la carga de un archivo
          this.on("error", (file, errorMessage) => {
            // Incrementar el número de archivos que fallaron en la carga
            numFilesErrored++;
            $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
            // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
            var listItem = document.createElement("li");
            listItem.innerText = file.name + " - Error. " + errorMessage;
            listItem.classList.add("list-group-item", "list-group-item-danger");
            myList.insertBefore(listItem, myList.firstChild);

            // Crear botón de eliminar
            var removeButton = document.createElement("button");
            removeButton.classList.add("btn", "btn-danger", "btn-sm", "float-end");
            removeButton.innerText = "Eliminar";

            // Evento al hacer clic en el botón de eliminar
            removeButton.onclick = () => {
              myList.removeChild(listItem);
              numFilesErrored--;
              numFilesToUpload--;
              this.removeFile(file);
              countInvoices(total, limit);
            };

            listItem.appendChild(removeButton);
            myList.insertBefore(listItem, myList.firstChild);
          });

          // Evento clave: Cuando toda la cola de archivos ha sido procesada
          this.on("queuecomplete", () => {
            console.log("Cola completa. Enviando lista de facturas...");
            const csrfToken = document.querySelector("[name=csrfmiddlewaretoken]").value;
            const uploadUrl = document.getElementById(selector).getAttribute("action");
    
            // Enviar toda la lista al backend
            fetch(uploadUrl, {
              method: "POST",
              headers: { "Content-Type": "application/json", "X-CSRFToken": csrfToken },
              body: JSON.stringify({
                is_last_file: true,
                invoice_ids: uploadedInvoices, // Enviar toda la lista acumulada
              }),
            })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                console.log("Correo enviado con éxito con facturas:", uploadedInvoices);
              } else {
                console.error("Error al enviar correo:", data.error);
              }
            })
            .catch((error) => console.error("Error en la solicitud:", error))
            .finally(() => {
              // Reiniciar la lista después del envío
              uploadedInvoices = [];
            });
          });
  
          // Evento que se dispara cuando se ha completado la carga
          this.on("complete", (file) => {
            console.log(`numFilesUploaded: ${numFilesUploaded}`)
            console.log(`numFilesErrored: ${numFilesErrored}`)
            console.log(`numFilesToUpload: ${numFilesToUpload}`)

            if (numFilesToUpload === numFilesUploaded + numFilesErrored) {
              if (numFilesToUpload === numFilesErrored) {
                // ALL ERRORS
              } else if (numFilesToUpload === numFilesUploaded) {
                // ALL OK
              } else {
                // MIXED
              }
              numFilesToUpload = 0;
              num_send = 0;
            }
            countInvoices(total, limit);
          });
          numFilesUploaded = 0;
          numFilesErrored = 0;
          numFilesToUpload = 0;
        },
        parallelUploads: 4,
        maxFiles: 1000,
        maxFilesize: 10, // 10 MB
        acceptedFiles: 'application/pdf',
        dictDefaultMessage: "Arrastre los archivos aquí",
        dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
        dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
        dictInvalidFileType: "No puede subir archivos de este tipo.",
        dictResponseError: "El servidor respondió con el código {{statusCode}}",
        dictCancelUpload: "Cancelar Subida.",
        dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
        dictRemoveFile: "Eliminar archivo.",
        dictMaxFilesExceeded: "No puede subir más archivos."
      };
    }
    
    // Creando las instancias de Dropzone
    createDropzone('myDropzoneAffiliateInvoice', 'myListContainerAffiliateInvoice');

  </script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    const { ref } = Vue;

    const inputUploadType = ref("{{uploadType}}");
    const totalInvoices = ref({{ total_invoices }});  // Declarar la variable totalInvoices aquí

    const clickInvoiceType = (type) => {
      inputUploadType.value = type;
    };

    const data_export = {
      inputUploadType,
      clickInvoiceType,
      totalInvoices,  // Incluir la variable totalInvoices en data_export
    };

    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const { createApp } = VUE3;

      const app = createApp({
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return { ...data_export };
        },
        watch: {
          totalInvoices(newValue) {
            countInvoices(newValue, limit);  // Llamar a countInvoices cuando totalInvoices cambie
          }
        }
      });

      app.mount(target);
    };

    createVue3('#uploads', data_export);
  </script>
{% endblock javascripts %}
