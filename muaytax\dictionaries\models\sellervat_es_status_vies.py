from django.db import models

class SellerVatEsStatusVies(models.Model):

    code = models.CharField(
        blank=True,
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )


    class Meta:
        verbose_name = "Estado del VIES para España"
        verbose_name_plural = "Estados de los VIES para España"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatEsStatusVies)
# class SellerVatEsStatusViesAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
