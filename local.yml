version: '3'

volumes:
  muaytax_local_postgres_data: {}
  muaytax_local_postgres_data_backups: {}
  celerybeat_data: {} # Volume for celerybeat to store schedule information
  redis_data: {}

services:

  django:
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: muaytax_local_django
    restart: on-failure:5
    container_name: muaytax_local_django
    platform: linux/x86_64
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app:z
      - ./muaytax/media:/app/muaytax/media:z
    env_file:
      - ./.envs/.local/.celery
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    ports:
      - "8000:8000"
    command: /start

  redis:
    build:
      context: .
      dockerfile: ./compose/local/redis/Dockerfile
    restart: on-failure:5
    container_name: redis_local
    volumes:
      - .:/app:z
      - ./muaytax/media:/app/muaytax/media:z
      - redis_data:/data
    env_file:
      - ./.envs/.local/.celery
    command: ["/bin/sh", "/start-redis"]

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: muaytax_production_postgres
    shm_size: '1g'
    restart: on-failure:5
    container_name: muaytax_local_postgres
    volumes:
      - muaytax_local_postgres_data:/var/lib/postgresql/data:Z
      - muaytax_local_postgres_data_backups:/backups:z
    env_file:
      - ./.envs/.local/.postgres
    ports:
      - "5432:5432"

  celeryworker:
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: muaytax_local_django
    restart: on-failure:5
    container_name: celeryworker_muaytax_local
    command: celery -A muaytax worker -l info -Q default,cache_updates,invoice_processing
    volumes:
      - .:/app:z
      - ./muaytax/media:/app/muaytax/media:z
    env_file:
      - ./.envs/.local/.celery
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    depends_on:
      - django
      - postgres
      - redis
      - celerybeat

  # celeryworker_cache_updates:
  #   build:
  #     context: .
  #     dockerfile: ./compose/local/django/Dockerfile
  #   image: muaytax_local_django
  #   restart: on-failure:5
  #   container_name: celeryworker_muaytax_local_cache_updates
  #   command: celery -A muaytax worker -l info -Q cache_updates
  #   volumes:
  #     - .:/app:z
  #     - ./muaytax/media:/app/muaytax/media:z
  #   env_file:
  #     - ./.envs/.local/.celery
  #     - ./.envs/.local/.django
  #     - ./.envs/.local/.postgres
  #   depends_on:
  #     - django
  #     - postgres
  #     - redis
  #     - celerybeat

  # celeryworker_invoice_processing:
  #   build:
  #     context: .
  #     dockerfile: ./compose/local/django/Dockerfile
  #   image: muaytax_local_django
  #   restart: on-failure:5
  #   container_name: celeryworker_muaytax_local_invoice_processing
  #   command: celery -A muaytax worker -l info -Q invoice_processing
  #   volumes:
  #     - .:/app:z
  #     - ./muaytax/media:/app/muaytax/media:z
  #   env_file:
  #     - ./.envs/.local/.celery
  #     - ./.envs/.local/.django
  #     - ./.envs/.local/.postgres
  #   depends_on:
  #     - django
  #     - postgres
  #     - redis
  #     - celerybeat

  celerybeat:
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: muaytax_local_django
    restart: on-failure:5
    container_name: celerybeat_muaytax_local
    command: celery -A muaytax beat -l info
    volumes:
      - .:/app:z
      - ./muaytax/media:/app/muaytax/media:z
      - celerybeat_data:/var/lib/celery/beat # Persistent volume for beat schedule
    env_file:
      - ./.envs/.local/.celery
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    depends_on:
      - django
      - postgres
      - redis

  flower:
    image: muaytax_local_django
    container_name: flower_muaytax_local
    restart: on-failure:5
    volumes:
      - .:/app:z
      - ./compose/local/flower/start-flower:/start-flower:z
    env_file:
      - ./.envs/.local/.celery
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    depends_on:
      - redis
      - celeryworker
    ports:
      - "5556:5555"
    # command: celery -A muaytax flower --port=5555 --basic_auth=webmaster:${FLOWER_ADMIN_PASSWORD} --url_prefix=flower
    command: ["/bin/sh", "/start-flower"]
