from django.db import models

class SellerVatStatusProcess(models.Model):

    code = models.CharField(
        blank=True,
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )


    class Meta:
        verbose_name = "Estado del proceso del Pais IVA"
        verbose_name_plural = "Estado de los procesos de los Paises IVA"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatStatusProcess)
# class SellerVatStatusProcessAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
