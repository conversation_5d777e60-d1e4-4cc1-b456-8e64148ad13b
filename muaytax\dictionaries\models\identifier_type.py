from django.db import models

class IdentifierType(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>",
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Tipo de Identificación"
        verbose_name_plural = "Tipos de Identificación"
    
    def __str__(self):
        return self.description
    
# @admin.register(IdentifierType)
# class IdentifierTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
