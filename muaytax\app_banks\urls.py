from django.urls import path
from muaytax.app_banks import views
from muaytax.app_importers import views as views_importers

app_name = "app_banks"
urlpatterns = [
    path(
        "sellers/<shortname>/banks/",
        view=views.BankListView.as_view(),
        name="bank_list",
    ),
    path(
        "sellers/<shortname>/banks/new/<banktype>/",
        view=views.BankNewView.as_view(),
        name="bank_new",
    ),
    path(
        "sellers/<shortname>/banks/<pk>/edit/",
        view=views.BankEditView.as_view(),
        name="bank_edit",
    ),
    # path(
    #     "sellers/<shortname>/banks/<pk>/movements/files/",
    #     view=views_importers.BankMovementsImporterListView.as_view(),
    #     name="bank_movements_files",
    # ),
    path(
        "sellers/<shortname>/banks/<pk>/movements/upload/",
        view=views.MovementsUploadView.as_view(),
        name="bank_movements_upload",
    ),

    path(
        "sellers/<shortname>/banks/<pk>/movements/create/",
        view=views.MovementsCreateView.as_view(),
        name="bank_movements_create",
    ),
    path(
        "sellers/<shortname>/banks/<pk>/movements/manual-create/",
        view=views.MovementsManualCreateView.as_view(),
        name="bank_movements_manual_create",
    ),

    path(
        "sellers/<shortname>/reconciliation/get/movement/",
        view=views.GetReconciliationByMovement.as_view(),
        name="bank_reconciliation_get_by_movement",
    ),

    path(
        "sellers/<shortname>/reconciliation/get/movement/DT/",
        view=views.GetMovementsDT.as_view(),
        name="bank_reconciliation_get_all_dt",
    ),

    path(
        "sellers/<shortname>/banks/<bank_id>/total-amount/",
        view=views.GetTotalAmount.as_view(),
        name="bank_total_amount",
    ),

    path(
        "sellers/<shortname>/reconciliation/get/movement/<movementid>/",
        view=views.GetReconciliationByMovement.as_view(),
        name="bank_reconciliation_get_by_movementid",
    ),

    path(
        "sellers/<shortname>/reconciliation/new/",
        view=views.NewReconcilation.as_view(),
        name="bank_reconciliation_new",
    ),

    path(
        "sellers/<shortname>/reconciliation/remove/",
        view=views.RemoveReconcilation.as_view(),
        name="bank_reconciliation_remove",
    ),

    path(
        'sellers/<shortname>/movement/remove/',
        view=views.RemoveMovement.as_view(),
        name='remove_movement'
    ),

    # path(
    #     "sellers/<shortname>/reconciliation/<pk>/edit/",
    #     view=views.EditReconciliation.as_view(),
    #     name="bank_reconciliation_edit",
    # ),

    path(
        "sellers/<shortname>/reconciliation/get/invoices/",
        view=views.GetInvoicesForReconcilation.as_view(),
        name="bank_reconciliation_get_invoices",
    ),
    path(
        "sellers/<shortname>/reconciliation/get/movements/",
        view=views.GetMovementsForReconcilation.as_view(),
        name="bank_reconciliation_get_movements",
    ),


    path(
        "sellers/<shortname>/unreconciled-invoices/",
        view=views.UnreconciledInvoices.as_view(),
        name="bank_unreconciled_invoices_list",
    ),
    path(
        "sellers/<shortname>/unreconciled-invoices/new/",
        view=views.NewReconcilationForUnreconlicedInvoice.as_view(),
        name="bank_unreconciled_invoice_new",
    ),
    path(
        "sellers/<shortname>/unreconciled-amz-invoices/new/",
        view=views.NewReconciliationsForUnreconciledAmzInvoices.as_view(),
        name="bank_unreconciled_amz_invoices_new",
    ),
    path(
        "sellers/<shortname>/reconciliation-transfer-amz/new/",
        view=views.NewReconciliatioTransferForAmazon.as_view(),
        name="bank_reconciliation_transfer_amz_new",
    ),

    path(
        "sellers/<shortname>/entry/",
        view=views.EntryListView.as_view(),
        name="entry_list",
    ),
    path(
        "sellers/<shortname>/entry/filezip_list/",
        view=views.EntryFileList.as_view(),
        name="entry_filezip_list",
    ),
    path(
        "sellers/<shortname>/entry/filezip_list/delete/<pk>/",
        view=views.EntryFileDeleteView.as_view(),
        name="entry_filezip_list_delete",
    ),
    path(
        "sellers/<shortname>/entry/new-all/<yeargen>/<int:limit>/",
        view=views.EntryNewAllView.as_view(),
        name="entry_new_all",
    ),
    path(
        "sellers/<shortname>/entry/new-reconciliations-amz/",
        view=views.EntryNewReconciliationAmzView.as_view(),
        name="entry_new_reconciliation_amz",
    ),
    path(
        "sellers/<shortname>/entry/new-reconciliations/",
        view=views.EntryNewReconciliationView.as_view(),
        name="entry_new_reconciliation",
    ),
    path(
        "sellers/<shortname>/entry/new-invoices/",
        view=views.EntryNewInvoicesView.as_view(),
        name="entry_new_invoices",
    ),
    path(
        "sellers/<shortname>/entry/delete-all/",
        view=views.EntryDeleteAllView.as_view(),
        name="entry_delete_all",
    ),
    path(
        "sellers/<shortname>/entry/export/",
        view=views.EntryExportView.as_view(),
        name="generate_export",
    ),
    path(
        "sellers/<shortname>/entry/<pk>/delete/",
        view=views.EntryDeleteView.as_view(),
        name="entry_delete",
    ),
    path(
        "sellers/<shortname>/get/entry/DT/",
        view=views.GetEntrysDT.as_view(),
        name="entry_get_all_dt",
    ),
    path(
        "banks/get/iban/<iban>/",
        view=views.GetIbanDataView.as_view(),
        name="bank_get_iban_data",
    ),
    
    # Rutas del Libro Mayor
    path(
        "sellers/<shortname>/ledger/",
        view=views.LedgerListView.as_view(),
        name="ledger_list",
    ),
    path(
        "sellers/<shortname>/ledger/data/",
        view=views.LedgerDataView.as_view(),
        name="ledger_data",
    ),
    path(
        "sellers/<shortname>/ledger/export/",
        view=views.LedgerExportView.as_view(),
        name="ledger_export",
    ),
]

# bank rules urls
urlpatterns += [
    path(
        "sellers/<shortname>/banks/rules/save/",
        view=views.SaveBankRule.as_view(),
        name="bank_rule_save",
    ),
    path(
        "sellers/<shortname>/banks/rules/get/",
        view=views.GetBankRules.as_view(),
        name="bank_rule_get",
    ),
    path(
        "sellers/<shortname>/banks/rules/delete/",
        view=views.DeleteBankRule.as_view(),
        name="bank_rule_delete",
    ),
    path(
        "sellers/<shortname>/banks/rules/update-priorities/",
        view=views.UpdateBankRulePriorities.as_view(),
        name="bank_rule_update_priorities",
    ),
    path(
        "sellers/<shortname>/banks/rules/apply/",
        view=views.ApplyBankRules.as_view(),
        name="bank_rule_apply",
    ),
]

# refactored urls
urlpatterns += [
    path(
        "sellers/<shortname>/entry/generate/<year>/",
        view=views.GenerateEntryView.as_view(),
        name="generate_entries",
    ),
]