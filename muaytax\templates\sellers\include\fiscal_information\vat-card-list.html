{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="row">
    {% for pais_iva in seller_vats %}
    <div id="vat-country-card-{{pais_iva.pk}}" class="col-sm-12 col-md-6 col-lg-6 col-xl-4">
        <div class="card" {% if pais_iva.is_local %} style="border: 1px solid red;" {% endif %}>
            <div class="position-absolute top-0 end-0 m-2">
                <!-- Botón para abrir el modal de edición -->
                <a
                    id="updateActivityButton"
                    class="text-warning f-14 me-2"
                    onclick="openAddUpdateVatActivationDateModal(this)"
                    data-vat-pk="{{ pais_iva.pk }}"
                    data-vat-name="{{ pais_iva.vat_country.name }}"
                    data-vat-country-code="{{ pais_iva.vat_country.iso_code }}"
                    data-is-local="{{ pais_iva.is_local }}"
                    data-is-contracted="{{ pais_iva.is_contracted }}"
                    data-contracting-date="{{ pais_iva.contracting_date|date:'Y-m-d'|lower }}"
                    data-contracting-end-date="{{ pais_iva.contracting_discontinue|date:'Y-m-d'|lower }}"
                    
                    data-act-date="{% if pais_iva.activation_date %}{{ pais_iva.activation_date|date:'Y-m-d'|lower }}{% else %}es nulo{% endif %}"
                    data-deact-date="{% if pais_iva.deactivation_date %}{{ pais_iva.deactivation_date|date:'Y-m-d'|lower }}{% else %}es nulo{% endif %}"

                    data-start-contracting-date="{{ pais_iva.start_contracting_date|date:'Y-m-d'|lower }}"
                    data-end-contracting-date="{{ pais_iva.end_contracting_date|date:'Y-m-d'|lower }}"
                    data-collection-date="{{ pais_iva.collection_date|date:'Y-m-d'|lower }}"
                    data-vat-number="{{ pais_iva.vat_number }}"
                    data-steuernummer="{{ pais_iva.steuernummer|default_if_none:'' }}"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="Editar"
                    role="button">
                    <i class="fas fa-edit"></i>
                </a>
            </div>
            <div class="card-block text-center">
                <div class="d-flex flex-row justify-content-center gap-1">
                    <h5 class="mb-3 text-uppercase">{{ pais_iva.vat_country.name }}</h5>
                    {% if pais_iva.is_local %}
                    <i 
                        role="button"
                        class="icon feather icon-star-on text-warning f-20"
                        data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Es país local">
                    </i>
                    {% endif %}
                </div>
                <div class="position-relative d-inline-block">
                    <img class="rounded-circle img-thumbnail wid-70" src="{% static 'assets/images/flags/' %}{{ pais_iva.vat_country.iso_code|lower }}.svg" alt="">
                    <div class="certificated-badge">
                        <i role="button" class="fas f-20{% if pais_iva.is_contracted %} fa-check-circle text-c-green{% else %} fa-times-circle text-c-red{% endif %}"
                        data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="{% if pais_iva.is_contracted %}Contratado{% else %} No contratado {% endif %}"></i>
                    </div>
                </div>
                <div class="d-flex justify-content-center gap-1 align-items-center">
                    <h4 class="f-w-300 mt-3" id="vat_number_{{pais_iva.pk}}">
                        {{ pais_iva.vat_number|default:"S/N" }}
                    </h4>
                    {% if pais_iva.vat_number %}
                    <i style="vertical-align: middle;"
                        class="f-16 mdi mdi-content-copy text-primary"
                        role="button"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        data-original-title="Copiar" data-bs-title="Copiar"
                        data-clipboard="true" data-clipboard-target="#vat_number_{{pais_iva.pk}}"
                        data-copied-tip="Copiado!" data-original-tip="Copiar">
                    </i>
                    {% endif %}
                </div>
                <!-- Información de fechas y estado del país -->
                <div class="d-flex flex-wrap justify-content-between mt-3 vat-country-card-text-group">
                    <span class="text-muted f-w-300">Inicio de Contratación</span>
                    <span>{{ pais_iva.contracting_date|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                </div>
                <div class="d-flex flex-wrap justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">Fin de Contratación</span>
                    <span>{{ pais_iva.contracting_discontinue|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                </div>
                <div class="d-flex flex-wrap justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">Alta Hacienda</span>
                    <div class="d-flex flex-wrap align-items-center gap-1 vat-country-card-text-group">
                        <span>{{ pais_iva.activation_date|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                    </div>
                </div>
                <div class="d-flex flex-wrap justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">Baja Hacienda</span>
                    <div class="d-flex align-items-center gap-1 vat-country-card-text-group">
                        <span>{{ pais_iva.deactivation_date|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                    </div>
                </div>
                <div class="d-flex flex-wrap justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">Inicio de Servicio</span>
                    <div class="d-flex flex-wrap align-items-center gap-1 vat-country-card-text-group">
                        <span>{{ pais_iva.start_contracting_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </div>
                </div>
                <div class="d-flex flex-wrap justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">Fin de Servicio</span>
                    <div class="d-flex flex-wrap align-items-center gap-1 vat-country-card-text-group">
                        <span>{{ pais_iva.end_contracting_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </div>
                </div>
                <div class="d-flex flex-wrap justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">Inicio de Cobro</span>
                    <div class="d-flex flex-wrap align-items-center gap-1 vat-country-card-text-group">
                        <span>{{ pais_iva.collection_date|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                    </div>
                </div>
                <div class="d-flex justify-content-between vat-country-card-text-group">
                    <span class="text-muted f-w-300">VIES</span>
                    <span class="{% if pais_iva.vat_vies %} text-c-green {% else %} text-c-red {% endif %}">
                        {% if pais_iva.vat_vies %}Activo{% else %}Inactivo{% endif %}
                    </span>
                </div>
            </div>
            <div class="card-footer">
                <div class="row">
                    <div class="col text-center">
                        STEUERNUMMER o SIRET:
                        <br>
                        {% firstof pais_iva.steuernummer pais_iva.siret "No tiene" %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Modales -->
<div class="modal fade" 
    id="addUpdateVatActivationDateModal" tabindex="-1" 
    aria-labelledby="animateModalLabel" aria-modal="true" 
    role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form
                id="addUpdateVatActivationDateForm"
                hx-post="{% url 'app_sellers:update_vat_activation_dates' shortname=object.shortname %}"
                hx-trigger="submit"
                hx-target="#row-id-{{ pais_iva.pk }}, #vat-row-{{ iva_country.pk }}"
                hx-swap="outerHTML"
                hx-on::after-request="this.reset(); document.getElementById('vatDateSubmitBtn').disabled = false;">
                {% csrf_token %}
                <div class="modal-header bg-dark">
                    <h5 class="modal-title text-white">Editar detalles del VAT</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Indicador de carga -->
                    <div id="loading-indicator-vat" class="d-none text-center">
                        <div class="custom-spinner"></div>
                        <p>Validando...</p>
                    </div>
                    <div id="modal-content-vat">
                        <div class="text-center">
                            <div class="d-flex flex-row justify-content-center gap-1">
                                <h5 id="vatCountryTitleModal" class="mb-3 text-uppercase"></h5>
                                <i 
                                    id="vatCountryIsLocalModal"
                                    role="button"
                                    class="icon feather icon-star-on text-warning f-20"
                                    data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Es país local"
                                ></i>
                            </div>
                            <div class="position-relative d-inline-block text-center">
                                <img
                                    id="vatCountryFLagModal" class="rounded-circle img-thumbnail wid-70"
                                    src="{% static 'assets/images/flags/es.svg' %}" data-src="{% static 'assets/images/flags/' %}"  alt="">
                                <div class="certificated-badge">
                                    <i 
                                        id="vatCountryIsContractedModal"
                                        role="button" class="fas f-20 fa-check-circle text-c-green"
                                        data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Contratado"
                                    ></i>
                                    <i 
                                        id="vatCountryIsNotContractedModal"
                                        role="button" class="fas f-20 fa-times-circle text-c-red d-none"
                                        data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Contratado"
                                    ></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Campos de formulario para las fechas y números -->
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracting_date" class="col-form-label pb-0">Fecha de Contratación</label>
                                <input type="date" class="form-control" id="id_contracting_date" name="contracting_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracting_discontinue" class="col-form-label pb-0">Fecha fin de Contratación</label>
                                <input type="date" class="form-control" id="id_contracting_discontinue" name="contracting_discontinue">
                            </div>
                        </div>
                        
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_activation_date" class="col-form-label pb-0">Fecha de Alta en hacienda</label>
                                <input type="date" class="form-control" id="id_activation_date" name="activation_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_deactivation_date" class="col-form-label pb-0">Fecha de baja en hacienda</label>
                                <input type="date" class="form-control" id="id_deactivation_date" name="deactivation_date">
                            </div>
                        </div>
                        <div class="row mb-2 ">
                            <div class="col-12">
                                <label for="id_start_contracting_date" class="col-form-label pb-0">Fecha de Inicio de Servicio</label>
                                <input type="date" class="form-control" id="id_start_contracting_date" name="start_contracting_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_end_contracting_date" class="col-form-label pb-0">Fecha de Fin de Servicio</label>
                                <input type="date" class="form-control" id="id_end_contracting_date" name="end_contracting_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_collection_date" class="col-form-label pb-0">Fecha de Inicio de Cobro</label>
                                <input type="date" class="form-control" id="id_collection_date" name="collection_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_vat_number" class="col-form-label pb-0">VAT Number</label>
                                <input type="text" class="form-control" id="id_vat_number" name="vat_number">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_steuernummer" class="col-form-label pb-0">STEUERNUMMER o SIRET</label>
                                <input type="text" class="form-control" id="id_steuernummer" name="steuernummer">
                            </div>
                        </div>
                        <input type="hidden" name="seller_vat_pk" id="id_seller_vat_pk">
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="vatDateSubmitBtn" type="submit" class="btn btn-primary">Actualizar</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block javascripts %}

<script>
    const vatUpdateform = document.getElementById('addUpdateVatActivationDateForm');

    const HTMXCardCountryHandler = {
        // Función para actualizar la tarjeta de un país tras la edición
        async updateCardCountry(pk) {
            debugLog(`[HTMXCardCountryHandler] Ejecutando -> updateCardCountry`);
    
            const url = "{% url 'app_sellers:update_vat_activation_dates' shortname=object.shortname %}";
            const cardCountryId = `#vat-country-card-${pk}`;
            console.log(`cardCountryId es ---> ${cardCountryId}`);
    
            try {
                const response = await fetch(`${url}?seller_vat_pk=${pk}`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                });
    
                const html = await response.text();
                console.log("Respuesta del servidor:", html); // Imprime la respuesta en la consola
    
                const cardContainer = document.querySelector(cardCountryId);
                if (cardContainer) {
                    cardContainer.outerHTML = html;
                    console.log(`Tarjeta del país con PK ${pk} actualizada.`);
                } else {
                    console.error(`No se encontró la tarjeta con el PK ${pk}`);
                }
            } catch (error) {
                console.error(`Error al actualizar la tarjeta del país con PK ${pk}: `, error);
            }
        }
    };

    // Función para abrir el modal de edición del VAT
    function openAddUpdateVatActivationDateModal(element) {
        const modalElement = document.getElementById('addUpdateVatActivationDateModal');
        if (!modalElement) return;

        const modal = new bootstrap.Modal(modalElement);
        const title = document.getElementById('vatCountryTitleModal');
        const isLocalIcon = document.getElementById('vatCountryIsLocalModal');
        const flagImg = document.getElementById('vatCountryFLagModal');
        const countryFlagsURL = flagImg ? flagImg.dataset.src : '';
        const isContractedIcon = document.getElementById('vatCountryIsContractedModal');
        const isNotContractedIcon = document.getElementById('vatCountryIsNotContractedModal');
        const contractingDateInput = document.getElementById('id_contracting_date');
        const contractingEndDateInput = document.getElementById('id_contracting_discontinue');
        const activationDateInput = document.getElementById('id_activation_date');
        const deactivationDateInput = document.getElementById('id_deactivation_date');
        const startContractingDateInput = document.getElementById('id_start_contracting_date');
        const endContractingDateInput = document.getElementById('id_end_contracting_date');  
        const collectionDateInput = document.getElementById('id_collection_date');  
        const vatNumberInput = document.getElementById('id_vat_number');
        const steuernummerInput = document.getElementById('id_steuernummer');
        const vatPkInput = document.getElementById('id_seller_vat_pk');

        // vatUpdateform.reset();
        vatUpdateform.querySelector('#vatDateSubmitBtn').disabled = false;

        // Limpiar errores anteriores
        vatNumberInput.classList.remove('is-invalid');
        const feedback = vatNumberInput.nextElementSibling;
        if (feedback) feedback.textContent = '';

        const vatData = {
            pk: element.getAttribute('data-vat-pk'),
            name: element.getAttribute('data-vat-name'),
            countryCode: element.getAttribute('data-vat-country-code'),
            isLocal: element.getAttribute('data-is-local') === 'True',
            isContracted: element.getAttribute('data-is-contracted') === 'True',
            contractingDate: element.getAttribute('data-contracting-date'),
            ContractingEndDate: element.getAttribute('data-contracting-end-date'),
            activationDate: element.getAttribute('data-act-date'),
            deactivationDate: element.getAttribute('data-deact-date'),
            startContractingDate: element.getAttribute('data-start-contracting-date'),
            endContractingDate: element.getAttribute('data-end-contracting-date'),    
            collectionDate: element.getAttribute('data-collection-date'),    
            vatNumber: element.getAttribute('data-vat-number'),
            steuernummer: element.getAttribute('data-steuernummer')
        };

        // Obtener el PK y la URL del formulario
        vatPkInput.value = vatData.pk;
        vatUpdateform.setAttribute('hx-target', `#vat-country-card-${vatData.pk}`);
        vatUpdateform.setAttribute('hx-swap', 'outerHTML');

        // Actualizar el contenido del modal
        if (flagImg) flagImg.src = `${countryFlagsURL}${vatData.countryCode.toLowerCase()}.svg`;
        if (title) title.textContent = vatData.name;
        if (isLocalIcon) isLocalIcon.classList.toggle('d-none', !vatData.isLocal);
        if (isContractedIcon) isContractedIcon.classList.toggle('d-none', !vatData.isContracted);
        if (isNotContractedIcon) isNotContractedIcon.classList.toggle('d-none', vatData.isContracted);

        // Establecer los valores de los inputs de fecha
        if (contractingDateInput) contractingDateInput.value = vatData.contractingDate;
        if (contractingEndDateInput) contractingEndDateInput.value = vatData.ContractingEndDate;

        console.log("activationDate:", vatData.activationDate);
        console.log("deactivationDate:", vatData.deactivationDate);
        if (activationDateInput) activationDateInput.value = vatData.activationDate;
        if (deactivationDateInput) deactivationDateInput.value = vatData.deactivationDate;
        console.log("Valor asignado en activationDateInput:", activationDateInput.value);
        console.log("Valor asignado en deactivationDateInput:", deactivationDateInput.value);

        if (startContractingDateInput) startContractingDateInput.value = vatData.startContractingDate;
        if (collectionDateInput) collectionDateInput.value = vatData.collectionDate;
        if (endContractingDateInput) endContractingDateInput.value = vatData.endContractingDate;
        if (vatNumberInput) vatNumberInput.value = vatData.vatNumber;
        if (steuernummerInput) steuernummerInput.value = vatData.steuernummer;

        // Resetear el feedback de validación
        resetValidationFeedback()

        // Actualizar los datepickers
        initializeDatePicker();

        modal.show();
    }
    
</script>
{% endblock %}
