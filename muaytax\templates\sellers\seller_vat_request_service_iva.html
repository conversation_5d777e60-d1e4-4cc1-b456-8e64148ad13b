{% extends "layouts/base.html" %}
{% load static crispy_forms_field crispy_forms_filters crispy_forms_tags %}
{% load static %}
{% block title %}Formulario IVA{% endblock title %}

{% block stylesheets %}
    <!-- Estilos de Select2 -->
    <link href="{% static 'assets/css/select2/select2.min.css' %}" rel="stylesheet">
    <!-- DATATABLES IMPORTACIONES -->
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables-v2.0.8.css" />
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.10.25.css" type="text/css"/>
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css" type="text/css"/> 
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v2.0.8.css" type="text/css"/>
    <!-- Estilos personalizados CSS form_mod184 -->
    <link href="{% static 'assets/css/m184/form.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/m184/newForm.css' %}" rel="stylesheet">
    <!-- CDNs externos -->
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <!-- Estilos personalizados CSS -->
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/formIVA/sellerVat-formIVA.base.css">
{% endblock stylesheets %}

{% block breadcrumb %}
    {% include "sellers/include/service_iva/shared/navBreadcrumb.html" %}
{% endblock breadcrumb %}

{% block content %}
    {% if is_form_processed %}
        <div class="alert alert-warning">
            <strong>El Formulario ya ha sido enviado para todos los países:</strong>
            <span style="font-style: italic;">(Todos los campos están bloqueados).</span>
        </div>
    {% elif is_partial_saved %}
        <div class="alert alert-warning">
            <strong>Este formulario fue parcialmente reavierto:</strong>
            <span style="font-style: italic;">(Hay paises por completar o campos que corregir).</span>
        </div>
    {% endif %}

    <div class="card-body text_font">
        <div class="col-sm-12">
            <form id="iva_form" method="post" enctype="multipart/form-data" action="{% url 'app_sellers:vat_request_service' seller.shortname %}">
                {% csrf_token %}

                <!-- INICIO TABS DE NAVEGACIÓN -->
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    {% include "sellers/include/service_iva/navTabs/navTabs_general.html" %}
                    {% include "sellers/include/service_iva/navTabs/navTabs_members.html" %}
                    {% include "sellers/include/service_iva/navTabs/navTabs_documents.html" %}
                    {% include "sellers/include/service_iva/navTabs/navTabs_summary.html" %}
                </ul>
                <!-- FIN TABS DE NAVEGACIÓN -->

                <!-- INICIO DE PESTAÑAS -->
                <div class="tab-content" id="myTabContent">
                    
                    <!-- Barra de progreso -->
                    {% include "sellers/include/service_iva/shared/progress_bar.html" %}

                    <!-- 1) Pestaña: Información General -->
                    <div class="tab-pane active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card" style="box-shadow: none;">
                                    
                                    <!-- Lista de países contratados -->
                                    <div class="card-header">
                                        <h5>Países Contratados</h5>
                                    </div>
                                    <div class="card-body">
                                        <h5>Resumen de los países donde se ha contratado el servicio de IVA.</h5>
                                        {% include "sellers/include/service_iva/contracted_countries_cards.html" %}
                                    </div>

                                    <div class="card-header">
                                        <h5>Datos de Empresa</h5>
                                    </div>
                                    <div class="card-body">
                                        <h5>Facilite la información solicitada sobre los datos de su empresa necesarios para la contratación.</h5>
                                        {% include "sellers/include/service_iva/company_info_form.html" with legal_entity=legal_entity %}
                                    </div>

                                    <div class="card-header">
                                        <h5>Datos de Migración</h5>
                                    </div>
                                    <div class="card-body">
                                        {% include "sellers/include/service_iva/migration_info_form.html" %}
                                    </div>

                                    <div class="card-footer d-flex justify-content-center">
                                        <div class="row">
                                            <div class="col-sm-12 btn_20gap">
                                                <button type="button" class="btn_green partial_saving" onclick="saveForm()">
                                                    Guardar y seguir editando
                                                </button>
                                                <button type="button" class="btn_dark_blue" 
                                                        onclick="changeTab('members-tab')">
                                                    Siguiente
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FIN: Pestaña Información General -->

                    <!-- 2) Pestaña: Miembros -->
                    <div class="tab-pane" id="members" role="tabpanel" aria-labelledby="members-tab">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card" style="box-shadow: none;">
                                    
                                    <div class="card-header card-header-grey">
                                        <h5>información General de Socios</h5>
                                    </div>
                                    <div class="card-body" id="partners-info-container">
                                        {% include "sellers/include/service_iva/members_info.html" %}
                                    </div>

                                    <div class="card-footer d-flex justify-content-center">
                                        <div class="row">
                                            <div class="col-sm-12 btn_20gap">
                                                <button type="button" class="back_btn" onclick="changeTab('general-tab')">
                                                    Atrás
                                                </button>
                                                <button type="button" class="btn_green partial_saving" onclick="saveForm()">
                                                    Guardar y seguir editando
                                                </button>
                                                <button type="button" class="btn_dark_blue" 
                                                        onclick="changeTab('documents-tab')">
                                                    Siguiente
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FIN: Pestaña Miembros -->

                    <!-- 3) Pestaña: Documentos por País -->
                    <div class="tab-pane" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card" style="box-shadow: none;">
                                    
                                    <div class="card-header card-header-grey">
                                        <h5>Documentos por País</h5>
                                    </div>
                                    <div class="card-body">
                                        {% include "sellers/include/service_iva/country_documents.html" %}
                                    </div>

                                    <div class="card-footer d-flex justify-content-center">
                                        <div class="row">
                                            <div class="col-sm-12 btn_20gap">
                                                <button type="button" class="back_btn" onclick="changeTab('members-tab')">
                                                    Atrás
                                                </button>
                                                <button type="button" class="btn_green partial_saving" onclick="saveForm()">
                                                    Guardar y seguir editando
                                                </button>
                                                <button type="button" class="btn_dark_blue"
                                                        onclick="changeTab('summary-tab')">
                                                    Siguiente
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FIN: Pestaña Documentos por País -->

                    <!-- 4) Pestaña: Resumen y Finalizar -->
                    <div class="tab-pane" id="summary" role="tabpanel" aria-labelledby="summary-tab">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card" style="box-shadow: none;">
                                    
                                    <div class="card-header card-header-grey">
                                        <h5>Resumen de la Información</h5>
                                    </div>
                                    <div class="card-body">
                                        {% include "sellers/include/service_iva/final_summary.html" %}
                                    </div>

                                    <div class="card-footer d-flex justify-content-center">
                                        <div class="row">
                                            <div class="col-sm-12 btn_20gap">
                                                <button type="button" class="back_btn" onclick="changeTab('documents-tab')">
                                                    Atrás
                                                </button>
                                                <button type="button" id="finalizarButton" class="btn_green" 
                                                        onclick="openConfirmationModal()">
                                                    Finalizar
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FIN: Pestaña Resumen y Finalizar -->

                </div>
                <!-- FIN DE PESTAÑAS -->


                <!-- INICIO MODAL DE CONFIRMACIÓN -->
                {% include "sellers/include/service_iva/modals/confirmation_modal.html" %}
                <!-- FIN MODAL DE CONFIRMACIÓN -->
            </form>
        </div>
    </div>

    <!-- start - MODAL AGREGAR SOCIO (si lo necesitas) -->
    <div class="col-sm-12 mt-3 text_font">
        <div class="card">
            <div class="card-body">
                {% include "sellers/include/service_iva/seller_vat_partners.html" %}
            </div>
        </div>
    </div>
    <!-- end - MODAL AGREGAR SOCIO -->
    
    <!-- JSON de validación del gestor como atributo para acceso desde JS -->
    <div id="managerValidationData"
        data-json="{{ manager_validation_data|safe|escapejs }}"
        class="d-none">
    </div>

    <!-- INICIO MODAL DE CARGA -->
    {% include "sellers/include/service_iva/shared/loading_modal.html" %}
    <!-- FIN MODAL DE CARGA -->

{% endblock content %}


{% block javascripts %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
    <script src="{% static 'assets/js/select2/select2.min.js' %}"></script>

    <!-- DATATABLES IMPORTATIONS -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
    <!-- DATATABLES IMPORTATIONS -->

    <!-- sweet alert Js -->
    <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script type="text/javascript">
        window.debug = {{ debug|yesno:"true,false" }};
    
        // Función global para logs de depuración
        window.debugLog = function (...args) {
            if (window.debug) {
                console.log(...args);
            }
        };
    </script>
    
    <script>
        window.submittedCountries = {{ submitted_countries|safe }};
        const isFormProcessed = {{ is_form_processed|yesno:"true,false" }};
        const submittedCountries = {{ submitted_countries|safe }};
        const partialSaved = {{ is_partial_saved|yesno:"true,false" }};
        const isNewForm = {{ is_new_form|yesno:"true,false" }};

        function changeTab(tabId) {
            // Ocultar todas las pestañas
            document.querySelectorAll('.tab-pane').forEach(tab => tab.style.display = "none");
            document.querySelectorAll('.tab-pane').forEach(tab => tab.classList.remove('active'));
        
            // Mostrar solo la pestaña seleccionada
            document.getElementById(tabId.replace('-tab', '')).style.display = "block";
            document.getElementById(tabId.replace('-tab','')).classList.add('active');
        
            // Remover la clase 'active' de todos los botones de navegación
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
        
            // Añadir la clase 'active' al tab actual
            document.getElementById(tabId).classList.add('active');
        
            // Actualizar la barra de progreso
            updateProgressBar(tabId);
        }

        function updateProgressBar(tabId) {
            let progressBar = document.querySelector('.progress-bar');
            let progressValue = 25;

            switch(tabId) {
                case 'general-tab': progressValue = 25; break;
                case 'members-tab': progressValue = 50; break;
                case 'documents-tab': progressValue = 75; break;
                case 'summary-tab': progressValue = 100; break;
            }

            progressBar.style.width = progressValue + '%';
            progressBar.setAttribute('aria-valuenow', progressValue);
        }

        function openConfirmationModal() {
            const modal = new bootstrap.Modal(document.getElementById('confirmForm'));
            modal.show();
        }

        function buildHtmlBlock(title, items) {
            if (!items.length) return "";
            const lines = items.map(item => `<p style="margin: 0 0 6px 0;">${item}</p>`).join("");
            return `
                <h6 style="margin-top: 1rem; margin-bottom: .4rem; font-weight: 600;">${title}</h6>
                <div>${lines}</div>
            `;
        }

        function lockReadonlyFields(isFormProcessed, submittedCountries = [], partialSaved = false) {
            if (isNewForm) return;
            // Si el formulario está completamente procesado, bloquear todo
            if (isFormProcessed) {
                window.debugLog("Formulario completo bloqueado (is_form_processed = true)");
                lockCompanyInfoFields();
                lockPartners();
                lockAllMigrationForms();
                lockCountryDocuments();
                disableSaveButtons();
                return;
            }
        
            // Si NO está completamente procesado, solo bloquear los países en submittedCountries
            const allIsoButtons = [...document.querySelectorAll("[data-country-iso]")].map(btn =>
                btn.dataset.countryIso
            );
        
            allIsoButtons.forEach(iso => {
                if (submittedCountries.includes(iso)) {
                    window.debugLog(`Bloqueando país ya enviado: ${iso}`);
                    lockMigrationFormByIso(iso);
                    lockCountryDocumentsByIso(iso);
                }
            });
        }

        function lockFileInputField(field, iso = "", suffix = "", baseField = "") {
            if (!field || field.type !== "file") return;
        
            // Ocultar el input
            field.classList.add("d-none");
        
            // Ocultar el label autogenerado por crispy
            const colDiv = field.closest(".col-lg-4");
            if (colDiv) {
                const autoLabel = colDiv.querySelector("label");
                if (autoLabel) autoLabel.classList.add("d-none");
        
                // Quitar mb-3 del contenedor si existe
                const fieldWrapper = field.closest(".mb-3");
                if (fieldWrapper) fieldWrapper.classList.remove("mb-3");
            }
        
            // Mostrar el <p> con nombre del archivo
            const fileInfoId = `${baseField || field.name}_fileinfo_${iso}${suffix}`;
            const fileInfoP = document.getElementById(fileInfoId);
            if (fileInfoP) {
                fileInfoP.classList.remove("d-none");
                fileInfoP.classList.add("readonly-file-display", "form-control", "col-lg-4");
        
                // Insertar el label izquierdo si no existía
                const parentRow = fileInfoP.closest(".form-group.row");
                if (parentRow) {
                    const existingManualLabel = parentRow.querySelector(".col-lg-6.col-form-label");
                    if (!existingManualLabel) {
                        const labelDiv = document.createElement("div");
                        labelDiv.className = "col-lg-6 col-form-label text-lg-end";
                        parentRow.insertBefore(labelDiv, fileInfoP.parentElement);
                    }
                }
            }
        }
        
        function lockCompanyInfoFields(allowedFields = []) {
            document.querySelectorAll('#company_info_form input, #company_info_form select, #company_info_form textarea').forEach(field => {
                const name = field.name || field.id?.replace("id_", "");
                const type = field.type;
        
                const isAllowed = allowedFields.includes(name);
        
                if (type === "file") {
                    // Si no está permitido editar, ocultar input y mostrar info
                    if (!isAllowed) {
                        field.classList.add("d-none");
        
                        const colDiv = field.closest(".col-lg-4");
                        if (colDiv) {
                            const autoLabel = colDiv.querySelector("label");
                            if (autoLabel) {
                                autoLabel.classList.add("d-none");
                            }
                        }
        
                        const crispyDiv = document.getElementById(`div_id_${name}`);
                        if (crispyDiv && crispyDiv.classList.contains("mb-3")) {
                            crispyDiv.classList.remove("mb-3");
                        }
        
                        const fileInfoP = document.getElementById(`${name}_fileinfo`);
                        if (fileInfoP) {
                            fileInfoP.classList.remove("d-none");
                            fileInfoP.classList.add("readonly-file-display", "form-control", "col-lg-4");
                        }
                    }
        
                } else {
                    if (!isAllowed) {
                        field.setAttribute("readonly", "true");
                        field.setAttribute("disabled", "true");
                    }
                }
            });
        }
        
        function lockPartners(validationData = {}) {
            window.debugLog("Ejecutando lockPartners...");
        
            // Ocultar botón de agregar nuevo socio
            const addNewBtn = document.querySelector(".new-partner-btn");
            if (addNewBtn) {
                addNewBtn.classList.add("d-none");
            }
        
            // Ocultar todos los botones editar inicialmente
            document.querySelectorAll(".edit-partner-btn").forEach(btn => {
                const partnerId = btn.dataset.partnerId;
        
                // 🔍 Buscar si hay ALGÚN campo de ese socio con error pendiente
                const partnerValidation = Object.entries(validationData.partner || {});
                const hasPendingError = partnerValidation.some(([key, value]) => {
                    return key.startsWith(`${partnerId}-`) && value.status === "incorrecto" && value.pending === true;
                });
        
                if (!hasPendingError) {
                    btn.classList.add("d-none");
                } else {
                    btn.classList.remove("d-none");
                    window.debugLog(`Dejar visible botón de socio con error: ${partnerId}`);
                }
            });
        }
        
        function lockMigrationFormByIso(iso, allowedFields = []) {
            const migrationForm = document.querySelector(`#migration-form-${iso}`);
            if (!migrationForm) return;
        
            migrationForm.querySelectorAll("input, select, textarea").forEach(field => {
                const name = field.name;
        
                const suffixMatch = name.match(/_(\d+)$/);
                const suffix = suffixMatch ? `_${suffixMatch[1]}` : "";
                const baseField = suffix ? name.replace(/_\d+$/, "") : name;
        
                const isAllowed = allowedFields.includes(name);
        
                if (field.type === "file") {
                    if (!isAllowed) {
                        lockFileInputField(field, iso, suffix, baseField);
                    }
                } else {
                    if (!isAllowed) {
                        field.setAttribute("readonly", "true");
                        field.setAttribute("disabled", "true");
                    }
                }
            });
        
            // Botón
            const migBtn = document.querySelector(`.country-migration-btn[data-country="${iso}"]`);
            if (migBtn) migBtn.classList.add("readonly-country-btn");
        
            // Check
            const icon = document.getElementById(`check-migration-icon-${iso}`);
            if (icon) {
                icon.classList.remove("text-success");
                icon.classList.add("text-secondary");
            }
        
            window.debugLog(`Migración bloqueada para ${iso}, excepto:`, allowedFields);
        }

        function lockCountryDocumentsByIso(iso, allowedFields = []) {
            const docForm = document.querySelector(`#documents-form-${iso}`);
            if (!docForm) return;
        
            docForm.querySelectorAll("input, select, textarea").forEach(field => {
                const type = field.type;
                const name = field.name;
        
                const suffixMatch = name.match(/_(\d+)$/);
                const suffix = suffixMatch ? `_${suffixMatch[1]}` : "";
                const baseField = suffix ? name.replace(/_\d+$/, "") : name;
        
                const isAllowed = allowedFields.includes(name);
        
                if (type === "file") {
                    if (!isAllowed) {
                        lockFileInputField(field, iso, suffix, baseField);
                    }
                } else {
                    if (!isAllowed) {
                        field.setAttribute("readonly", "true");
                        field.setAttribute("disabled", "true");
                    }
                }
            });
        
            // Estilizar botón como deshabilitado
            const docBtn = document.querySelector(`.country-documents-btn[data-country="${iso}"]`);
            if (docBtn) docBtn.classList.add("readonly-country-btn");
        
            const icon = document.getElementById(`check-documents-icon-${iso}`);
            if (icon) {
                icon.classList.remove("text-success");
                icon.classList.add("text-secondary");
            }
        
            window.debugLog(`Documentos bloqueados para ${iso}, excepto:`, allowedFields);
        }
        
        function lockAllMigrationForms() {
            const allMigrationForms = document.querySelectorAll("[id^='migration-form-']");
            allMigrationForms.forEach(formDiv => {
                const iso = formDiv.dataset.countryIso;
                lockMigrationFormByIso(iso);
            }); 
        }

        function lockCountryDocuments() {
            const allDocForms = document.querySelectorAll("[id^='documents-form-']");
            allDocForms.forEach(formDiv => {
                const iso = formDiv.dataset.countryIso;
                lockCountryDocumentsByIso(iso);
            });
        }
        
        function disableSaveButtons() {
            // Oculta y desactiva todos los botones de guardado parcial
            document.querySelectorAll(".partial_saving").forEach(btn => {
                btn.setAttribute("disabled", "true");
                btn.classList.add("d-none");
            });
        
            // Oculta y desactiva el botón de finalizar
            const finalizarBtn = document.getElementById("finalizarButton");
            if (finalizarBtn) {
                finalizarBtn.setAttribute("disabled", "true");
                finalizarBtn.classList.add("d-none");
            }
        
            window.debugLog("Botones de guardado parcial y final ocultos/desactivados");
        }

        function getLabelForInput(input) {
            if (!input) return "(sin nombre)";
        
            // Intenta encontrar el <label> asociado por 'for'
            let label = document.querySelector(`label[for="${input.id}"]`);
            if (label) return label.innerText.trim();
        
            // Si no, busca el padre con clase .form-group.row y su hijo con col-form-label
            let row = input.closest('.form-group.row');
            if (row) {
                let labelDiv = row.querySelector('.col-form-label');
                if (labelDiv) return labelDiv.innerText.trim();
            }
        
            // Si no hay label, usar el name del campo
            return input.name || "(sin label)";
        }
        
        // Obtener token CSRF
        function getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]').value;
        }

        function generateFormJson() {
            const ivaFormJson = {};
            const formData = new FormData();
        
            const forms = document.querySelectorAll('[id^="company_info_form"], [id^="migration-form-"], [id^="documents-form-"]');
        
            forms.forEach(form => {
                const formId = form.id;
                const inputs = form.querySelectorAll('input, select, textarea');
                const data = {};
        
                inputs.forEach(input => {
                    const name = input.name;
                    if (!name) return;

                    const type = input.type;
                    let value = "";
        
                    if (input.type === 'file') {
                        const file = input.files[0];
                        const countryIso = form.dataset.countryIso || "GLOBAL";
                        const fileInfo = document.getElementById(`${name}_fileinfo_${countryIso}`) || document.getElementById(`${name}_fileinfo`);
                        const existingFile = fileInfo?.innerText?.trim().replace("Documento cargado:", "").trim();

                        data[name] = {
                            value: file ? file.name : existingFile || "",
                            file: ""  // se completa en backend
                        };
        
                        if (file) {
                            formData.append(name, file);
                            window.debugLog(`Archivo recogido: ${name} → ${file.name}`);
                        } else {
                            window.debugLog(`Archivo previo mantenido (p): ${name} → ${existingFile || "(vacío)"}`);
                        }

                    } else if (input.type === 'checkbox') {
                        value = input.checked;
                        data[name] = value;
                    } else if (input.type === 'radio') {
                        if (input.checked) {
                            value = input.value;
                            data[name] = value;
                        } else if (!(name in data)) {
                            data[name] = "";
                        }
                    } else {
                        value = input.value?.trim() || "";
                        data[name] = value;
                    }

                    if (type !== "file") {
                        window.debugLog(`Campo recogido: ${name} = "${data[name]}" (${type})`);
                    }
                });
                // Asignar clave por tipo de formulario
                let blockKey = formId;
                if (blockKey.startsWith('migration-form-')) {
                    const iso = blockKey.replace('migration-form-', '');
                    blockKey = `migration_info_${iso}`;
                } else if (blockKey.startsWith('documents-form-')) {
                    const iso = blockKey.replace('documents-form-', '');
                    blockKey = `documents_by_country_${iso}`;
                }
        
                ivaFormJson[blockKey] = data;
            });
        
            // Agregar info de socios pendientes
            const rows = document.querySelectorAll('#partners_table tbody tr');
            window.debugLog(`Total de filas encontradas en #partners_table: ${rows.length}`);
            let hasPending = false;
        
            rows.forEach((row, index) => {
                const validateBtn = row.querySelector('button.add-partner-btn');
                if (validateBtn) {
                    hasPending = true;
                }
            });
        
            window.debugLog(`Resultado de hasPending → ${hasPending}`);
            ivaFormJson["partners_pending_validation"] = hasPending;
        
            const finalJson = {
                iva_form: ivaFormJson
            };
        
            // Añadir a FormData (si quieres usarlo luego)
            formData.append("iva_form", JSON.stringify(finalJson.iva_form));
            formData.append("csrfmiddlewaretoken", getCsrfToken());
        
            return {
                json: finalJson,
                formData: formData
            };
        }

        async function reloadPartnersBlock() {
            const container = document.getElementById("partners-info-container");
        
            try {
                const response = await fetch(`${window.location.pathname}?action=get_partners_block`);
                const data = await response.json();
        
                if (data.html && container) {
                    container.innerHTML = data.html;
        
                    // Reasignar eventos, tooltips, etc.
                    attachEventListenersToButtons();
                    initTooltips();
                }
            } catch (error) {
                console.error("Error recargando bloque de socios:", error);
            }
        }
        
        async function saveForm() {
            
            const { json: finalJson, formData } = generateFormJson(); // Construir el JSON
            
            // Mostrar JSON en consola legible
            window.debugLog("JSON generado por saveForm():\n", JSON.stringify(finalJson, null, 4));
            try {
                const response = await fetch(window.location.href, {
                    method: "POST",
                    body: formData
                });
        
                const data = await response.json();
                window.debugLog("Enviado correctamente:", data);
        
                Swal.fire({
                    icon: 'success',
                    title: 'Guardado',
                    text: 'El formulario se ha guardado correctamente.'
                });
        
            } catch (err) {
                console.error("Error al enviar:", err);
        
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Hubo un problema al guardar el formulario.'
                });
            }
        }
        
        async function processedFormularioIVA() {
            event.preventDefault(); // Evita la acción por defecto
            const submittedCountries = window.submittedCountries || [];  // Asegúrate de definir esta variable global en el template

            console.clear();
            window.debugLog("Iniciando validación final del formulario IVA...");
            const { json: finalJson, formData } = generateFormJson(); // Construir el JSON

            window.debugLog("JSON listo para envío final:\n", JSON.stringify(finalJson, null, 4));
        
            const form = document.getElementById("iva_form");
            const allInputs = form.querySelectorAll("input, select, textarea");
            let isValid = true;
            let camposVacios = [];
        
            allInputs.forEach(input => {
                const type = input.type;
                const value = input.value?.trim();
                const inputName = input.name || "(sin name)";
                const inputId = input.id || "(sin id)";
                const iso = input.closest("[data-country-iso]")?.dataset.countryIso || "GLOBAL";
                if (submittedCountries.includes(iso)) {
                    window.debugLog(`Campo ignorado por estar en país enviado (${iso})`);
                    return;  // Saltamos validación si es de un país ya enviado
                }
                
                window.debugLog(`Revisando [${iso}] ➤ ID: ${inputId} | NAME: ${inputName} | TIPO: ${type} | VALOR: "${value}"`);
            
                if (type === "hidden" || (!input.name && !input.id)) return;
            
                const isRequired = input.required || input.classList.contains("required");
            
                // Detectar si el input pertenece a migración o documentos
                const wrapper = input.closest(".country-form");
                const section = wrapper?.id?.includes("documents-form")
                    ? "documents"
                    : wrapper?.id?.includes("migration-form")
                        ? "migration"
                        : null;
            
                const relatedBtn = section
                    ? document.querySelector(`.country-${section}-btn[data-country="${iso}"]`)
                    : null;
            
                if (type === "file") {
                    // Usar sufijo si tiene data-country-iso (para migración y docs), si no, usar directo (Detectar sufijo (ej. _1, _2, ...))
                    const suffixMatch = input.name.match(/_(\d+)$/);
                    const suffix = suffixMatch ? `_${suffixMatch[1]}` : "";
                    const baseField = suffix ? input.name.replace(/_\d+$/, "") : input.name;

                    const fileInfoId = iso !== "GLOBAL"
                        ? `${baseField}_fileinfo_${iso}${suffix}`
                        : `${baseField}_fileinfo`;

                    const fileInfo = document.getElementById(fileInfoId);
                    const existingFile = fileInfo?.innerText?.trim() || "";
            
                    const hasUploadedFile = input.files.length > 0 || existingFile;
            
                    if (!hasUploadedFile && isRequired) {
                        input.classList.add("border", "border-danger");
                        relatedBtn?.classList.add("border", "border-danger");
                        const fieldLabel = getLabelForInput(input);
                        camposVacios.push(`${fieldLabel} (${iso})`);

                        isValid = false;
                    } else {
                        input.classList.remove("border", "border-danger");
                    }
            
                } else if (type !== "button" && type !== "submit" && isRequired) {
                    if (!value) {
                        input.classList.add("border", "border-danger");
                        relatedBtn?.classList.add("border", "border-danger");
                        const fieldLabel = getLabelForInput(input);
                        camposVacios.push(`${fieldLabel} (${iso})`);

                        isValid = false;
                    } else {
                        input.classList.remove("border", "border-danger");
                    }
                }
            
                // Limpieza visual del botón si ya no hay errores
                if (relatedBtn && isValid) {
                    relatedBtn.classList.remove("border", "border-danger");
                }
            });
            
        
            window.debugLog("Campos recorridos:", allInputs.length);
            window.debugLog("Campos vacíos detectados:", camposVacios);
        
            // Validar socios
            const rows = document.querySelectorAll("#partners_table tbody tr");
            let pendingPartners = [];
            let totalConfirmedShares = 0;
        
            rows.forEach(row => {
                const name = row.querySelector(".col-name")?.innerText.trim() || "Socio sin nombre";
                const validateBtn = row.querySelector(".add-partner-btn");
                const isConfirmed = row.querySelector(".col-icons svg path[d='M10.5 15L14 19L20 11']") !== null;
                const isBaja = row.classList.contains("baja-row");
        
                if (validateBtn) {
                    pendingPartners.push(name);
                }
        
                if (isConfirmed && !isBaja) {
                    const btn = row.querySelector(".col-buttons button");
                    const percent = parseFloat(btn?.dataset.partnerSharesPercentage || "0");
                    totalConfirmedShares += percent;
                }
            });
        
            window.debugLog("Socios pendientes:", pendingPartners);
            window.debugLog(`Porcentaje total confirmado: ${totalConfirmedShares.toFixed(2)}%`);
        
            // Mostrar errores si aplica
            if (!isValid || pendingPartners.length > 0 || totalConfirmedShares !== 100) {
                bootstrap.Modal.getInstance(document.getElementById("confirmForm"))?.hide();
        
                let errorMsg = "";
                if (camposVacios.length) {
                    errorMsg += `Hay campos obligatorios vacíos:\n• ${camposVacios.join("\n• ")}\n\n`;
                }
                if (pendingPartners.length > 0) {
                    errorMsg += `Socios pendientes de validar:\n• ${pendingPartners.join("\n• ")}\n\n`;
                }
                if (totalConfirmedShares !== 100) {
                    errorMsg += `El porcentaje total debe ser 100%. Actualmente: ${totalConfirmedShares.toFixed(2)}%`;
                }
        
                const htmlContent = `
                    ${buildHtmlBlock("Campos obligatorios vacíos", camposVacios)}
                    ${buildHtmlBlock("Socios pendientes de validar", pendingPartners)}
                    ${totalConfirmedShares !== 100
                        ? `<h6 style="margin-top: 1rem;">Error de porcentaje</h6><p style="margin-bottom: 0;">El porcentaje total debe ser 100%. Actualmente: <strong>${totalConfirmedShares.toFixed(2)}%</strong></p>`
                        : ""}
                `;

                Swal.fire({
                    icon: "error",
                    title: "Errores en el formulario",
                    html: htmlContent,
                    customClass: {
                        popup: 'swal-custom-popup',
                        title: 'swal-custom-title',
                        htmlContainer: 'swal-custom-html'
                    },
                    width: '42rem',
                    padding: '1.5rem',
                });
        
                return;
            }
        
            // Enviar si es válido
            window.debugLog("Formulario válido. Creando y Enviando dataJSON...");

            //const { json: finalJson, formData } = generateFormJson(); // Construir el JSON

            //window.debugLog("JSON listo para envío final:\n", JSON.stringify(finalJson, null, 4));

            // Mostrar modal de carga inmediatamente
            const loadingModalElement = document.getElementById("LoadingModal");
            const loadingModal = new bootstrap.Modal(loadingModalElement, {
                backdrop: 'static',
                keyboard: false
            });

            // Oculta el modal de confirmación en cuanto empieza la carga
            bootstrap.Modal.getInstance(document.getElementById("confirmForm"))?.hide();
            loadingModal.show();
        
            try {
                const response = await fetch("{% url 'app_sellers:vat_submit_final' seller.shortname %}", {
                    method: "POST",
                    body: formData
                });
        
                const result = await response.json();

                if (result.success) {
                    setTimeout(() => {
                        loadingModal.hide();
                    }, 1000);
                    
                    {% comment %} bootstrap.Modal.getInstance(document.getElementById("confirmForm"))?.hide();
                    Swal.fire({
                        icon: "success",
                        title: "Formulario enviado correctamente",
                        timer: 2000,
                        showConfirmButton: false
                    }); {% endcomment %}
                    const shortname = "{{ seller.shortname }}";
                    window.debugLog(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!${shortname}`)
                    window.location.href = `/sellers/${shortname}/requestiva/success/`;
                    
                } else {
                    throw new Error(result.error || "Error desconocido.");
                }
            } catch (error) {
                setTimeout(() => {
                    loadingModal.hide();
                    Swal.fire({
                        icon: "error",
                        title: "Error al enviar",
                        text: error.message || "Algo falló al enviar el formulario."
                    });
                }, 1000);
            }
        }
        
        // Cuando cargue la página, inicia la barra en 25%
        document.addEventListener('DOMContentLoaded', function() {
            
            window.debugLog(`isFormProcessed es: -> ${isFormProcessed}`)
            window.debugLog(`partialSaved es: -> ${partialSaved}`)
            window.debugLog(`submittedCountries es: -> ${submittedCountries}`)
            window.debugLog(`isNewForm es: -> ${isNewForm}`)
            lockReadonlyFields(isFormProcessed, submittedCountries, partialSaved);
            updateProgressBar("general-tab");

            const renderedFields = [];
            
            document.querySelectorAll("form input, form select, form textarea").forEach(field => {
                if (field.name || field.id) {
                    renderedFields.push({
                        name: field.name || null,
                        id: field.id || null,
                        type: field.tagName.toLowerCase(),
                        formId: field.closest("form")?.getAttribute("id") || "no-id"
                    });
                }
            });
        });
    </script>
{% endblock javascripts %}