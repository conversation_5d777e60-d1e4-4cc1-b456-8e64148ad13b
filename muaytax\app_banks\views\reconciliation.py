import json

from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse, HttpResponseRedirect
from django.views.generic import View
from django.forms.models import model_to_dict
from django.db.models import F, Q, Sum, Value, CharField, OuterRef, Subquery, Case, When, DecimalField, ExpressionWrapper, BooleanField
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django_datatables_view.base_datatable_view import BaseDatatableView

from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission, IsSellerRolePermission
from muaytax.app_banks.models.bank import Bank
from muaytax.app_banks.models.reconciliation import Reconciliation
from muaytax.app_banks.models.movement import Movement as BankMovement  # Alias para evitar confusión
from muaytax.app_banks.models.bank_rule import BankRule
from muaytax.app_invoices.models.concept import Concept
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType
from muaytax.dictionaries.models.accounting_account import AccountingAccount
# from muaytax.app_accounting.models.entry import Entry

def update_movement_status(seller, movement_id):
    movement = BankMovement.objects.filter(pk=movement_id, bank__bank_seller = seller).first()

    if movement is not None:
        reconciliations = Reconciliation.objects.filter(movement=movement_id)
        reconciliations_amount = reconciliations.aggregate(total=Sum(F('amount')))['total'] or 0

        movement_pending = movement.amount_euros
        if (movement.amount_euros is not None and reconciliations_amount is not None):
            if (movement.amount_euros < 0):
                if (reconciliations_amount < 0):
                    movement_pending = float(movement.amount_euros) - float(reconciliations_amount)
                else:
                    movement_pending = float(movement.amount_euros) + float(reconciliations_amount)
            else:
                if (reconciliations_amount < 0):
                    movement_pending = float(movement.amount_euros) + float(reconciliations_amount)
                else:
                    movement_pending = float(movement.amount_euros) - float(reconciliations_amount)

        movement_pending = round(movement_pending, 2)

        if reconciliations.count() == 0:
            movement.status_id = 'pending'
        elif movement.amount_euros != 0 and movement_pending == 0:
            movement.status_id = 'conciliated'
        elif movement.amount_euros == 0 and movement_pending == 0:
            movement.status_id = 'conciliated'
        elif movement.amount_euros >= 0 and movement_pending > 0:
            movement.status_id = 'partially-conciliated'
        elif movement.amount_euros >= 0 and movement_pending < 0:
            movement.status_id = 'over-conciliated'
        elif movement.amount_euros < 0 and movement_pending > 0:
            movement.status_id = 'over-conciliated'
        elif movement.amount_euros < 0 and movement_pending < 0:
            movement.status_id = 'partially-conciliated'
        else:
            movement.status_id = 'partially-conciliated'

        movement.save()
        return movement
    else:
        print(f"Movimiento {movement_id} no encontrado. No se puede actualizar el estado.")
        return None

class GetReconciliationByMovement(LoginRequiredMixin, IsSellerShortnamePermission, View):

    def get(self, request, *args, **kwargs):
        try:
            movement_id = self.kwargs["movementid"]
        except:
            movement_id = None

        if movement_id is None:
            movement_id = request.GET.get('movement_id')

        bank_id = request.GET.get('bank_id')
        year = request.GET.get('year')
        return self._get_response(movement_id, bank_id, year)

    def post(self, request, *args, **kwargs):
        movement_id = self.kwargs["movementid"]
        if movement_id is None:
            movement_id = request.POST.get('movement_id')

        bank_id = request.POST.get('bank_id')
        year = request.POST.get('year')
        return self._get_response(movement_id, bank_id, year)

    def _get_response(self, movement_id, bank_id, year):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        if movement_id:
            if movement_id == 'all':
                movements = BankMovement.objects.filter(bank__bank_seller = seller.pk)

                if bank_id is not None and bank_id.isdigit():
                    movements = movements.filter(bank = bank_id)

                if year is not None and year.isdigit():
                    movements = movements.filter(movement_date__year=year)

                if movements is not None:
                    for movement in movements:
                        reconciliations = Reconciliation.objects.filter(movement = movement.pk)
                        reconciliations = reconciliations.annotate(
                            invoice__reference=F('invoice__reference'),
                            invoice__invoice_category=F('invoice__invoice_category'),
                            invoice__is_rectifying=F('invoice__is_rectifying') or False,
                        ).distinct()

                        movement_dict = model_to_dict(movement)
                        movement_dict['used_in_entry'] = movement.used_in_entry or False

                        data.append({
                            "movement": movement_dict,
                            "reconciliations_amount": reconciliations.aggregate(total=Sum(F('amount')))['total'] or 0,
                            "reconciliations_quantity": reconciliations.count(),
                            "reconciliations":  list(reconciliations.values()),
                        })
            elif movement_id.isdigit():
                movement = BankMovement.objects.filter(pk=movement_id, bank__bank_seller = seller.pk).first()
                if movement is not None:
                    reconciliations = Reconciliation.objects.filter(movement = movement.pk)

                    reconciliations = reconciliations.annotate(
                        # invoice__total_euros=Sum(F('invoice__concept__total_euros')),
                        invoice__total_euros=Sum(
                            Case(
                                When(
                                    Q(movement__amount_euros__lt=0) &
                                    Q(invoice__invoice_category__pk='sales'),
                                    then=F('invoice__concept__total_euros') * -1
                                ),
                                When(
                                    Q(movement__amount_euros__lt=0) &
                                    Q(invoice__invoice_category__pk='expenses'),
                                    then=F('invoice__concept__total_euros') * 1
                                ),
                                When(
                                    Q(movement__amount_euros__gte=0) &
                                    Q(invoice__invoice_category__pk='sales') &
                                    Q(invoice__is_rectifying=True),
                                    then=F('invoice__concept__total_euros') * 1
                                ),
                                When(
                                    Q(movement__amount_euros__gte=0) &
                                    Q(invoice__invoice_category__pk='sales') &
                                    ~Q(invoice__is_rectifying=True),
                                    then=F('invoice__concept__total_euros') * -1
                                ),
                                When(
                                    Q(movement__amount_euros__gte=0) &
                                    Q(invoice__invoice_category__pk='expenses') &
                                    Q(invoice__is_rectifying=True),
                                    then=F('invoice__concept__total_euros') * 1
                                ),
                                When(
                                    Q(movement__amount_euros__gte=0) &
                                    Q(invoice__invoice_category__pk='expenses') &
                                    ~Q(invoice__is_rectifying=True),
                                    then=F('invoice__concept__total_euros') * -1
                                ),
                                default=F('invoice__concept__total_euros'),
                                output_field=DecimalField(decimal_places=2),  # Ajusta el tipo de campo según tus necesidades
                            )
                        ),
                        invoice__concept = Subquery(
                            Concept.objects.filter(invoice=OuterRef('invoice')).values('concept')[:1]
                        ),
                        invoice__reference=F('invoice__reference'),
                        invoice__accounting_date=F('invoice__accounting_date'),
                        invoice__invoice_category=F('invoice__invoice_category'),
                        invoice__is_rectifying=F('invoice__is_rectifying') or False,
                        transfer__total_euros = Sum(F('movement_transfer__amount_euros')),
                        transfer__concept = F('movement_transfer__concept'),
                        transfer__observation = F('movement_transfer__observation'),
                        transfer__date = F('movement_transfer__movement_date'),
                        account__id = F('accounting_account__pk'),
                        account__description = F('accounting_account__description'),
                    ).distinct()

                    if reconciliations is not None:
                        movement_dict = model_to_dict(movement)
                        movement_dict['used_in_entry'] = movement.used_in_entry or False
                        # Preparar datos para enviar como respuesta
                        data.append({
                            'movement': movement_dict,
                            "reconciliations_amount": reconciliations.aggregate(total=Sum(F('amount')))['total'] or 0,
                            "reconciliations_quantity": reconciliations.count(),
                            "reconciliations":  list(reconciliations.values()),
                        })
                    # else:
                    #     error = True
                    #     error_message = "No se ha encontrado ninguna conciliación para el movimiento"
                else:
                    error = True
                    error_message = "No se ha encontrado el movimiento"
            else:
                error = True
                error_message = "El ID del movimiento no es válido"
        else:
            error = True
            error_message = "No se ha recibido el movimiento"


        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "draw":1,
                "recordsFiltered":0,
                "recordsTotal":0,
                "result": "error",
                # "status": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "draw":1,
                "recordsFiltered":data.__len__(),
                "recordsTotal":data.__len__(),
                "result": "ok",
                # "status": "ok",
                "data": data,
            }

        # Devolver una respuesta en formato JSON
        return JsonResponse(data)

class NewReconcilation(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):

    def get(self, request, *args, **kwargs):
        movement_id = request.GET.get('movement_id')
        invoice_id = request.GET.get('invoice_id')
        account_id = request.GET.get('account_id')
        bank_id = request.GET.get('bank_id')
        movement_transfer_id = request.GET.get('movement_transfer_id')
        amount = request.GET.get('amount')
        type = request.GET.get('type')
        return self._get_response(movement_id, invoice_id, account_id, bank_id, movement_transfer_id, amount, type)

    def post(self, request, *args, **kwargs):
        movement_id = request.POST.get('movement_id')
        invoice_id = request.POST.get('invoice_id')
        account_id = request.POST.get('account_id')
        bank_id = request.POST.get('bank_id')
        movement_transfer_id = request.POST.get('movement_transfer_id')
        amount = request.POST.get('amount')
        type = request.POST.get('type')
        return self._get_response(movement_id, invoice_id, account_id, bank_id, movement_transfer_id, amount, type)

    def _get_response(self, movement_id, invoice_id, account_id, bank_id, movement_transfer_id, amount, type):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        if type is None:
            if invoice_id is not None:
                type = 'invoice'
            elif account_id is not None:
                type = 'account'
            elif bank_id is not None:
                type = 'bank'
            elif movement_transfer_id is not None:
                type = 'transfer'
            else:
                type = None

        if movement_id is not None and type is not None:
            movement = BankMovement.objects.filter(pk=movement_id, bank__bank_seller = seller).first()
            if type == 'invoice':
                rec_type = ReconciliationType.objects.filter(pk=type).first()
                if invoice_id is not None and invoice_id.isdigit():
                    invoice = Invoice.objects.filter(pk=invoice_id, status='revised', seller=seller).first()
                    if invoice is not None:
                        # Check if this is a partial reconciliation
                        is_partial = amount is not None and amount != "" and amount != "auto"

                        # Only check for existing reconciliations if this is not a partial reconciliation
                        if is_partial:
                            # For partial reconciliations, we allow multiple reconciliations for the same invoice
                            pass
                        else:
                            # For full reconciliations, we check if there's already a reconciliation for this invoice
                            otherRecs = Reconciliation.objects.filter(invoice=invoice_id, movement_id=movement_id).count()
                            if otherRecs > 0:
                                error = True
                                error_message = "Ya existe una conciliación para el movimiento y la factura"
                                return JsonResponse({
                                    "result": "error",
                                    "message": error_message,
                                    "data": []
                                })

                        # If we get here, we can create a new reconciliation
                        mvs = 1
                        if (movement is not None and movement.amount_euros is not None):
                            if (float(movement.amount_euros) < 0):
                                if ( invoice.invoice_category.pk == 'sales' and invoice.is_rectifying == True ):
                                    mvs = -1
                                if ( invoice.invoice_category.pk == 'sales' and (invoice.is_rectifying == False or invoice.is_rectifying == None) ):
                                    mvs = -1
                                if ( invoice.invoice_category.pk == 'expenses' and invoice.is_rectifying == True):
                                    mvs = 1
                                if ( invoice.invoice_category.pk == 'expenses' and (invoice.is_rectifying == False or invoice.is_rectifying == None)):
                                    mvs = 1
                            else:
                                if ( invoice.invoice_category.pk == 'sales' and invoice.is_rectifying == True ):
                                    mvs = 1
                                if ( invoice.invoice_category.pk == 'sales' and (invoice.is_rectifying == False or invoice.is_rectifying == None)):
                                    mvs = -1
                                if ( invoice.invoice_category.pk == 'expenses' and invoice.is_rectifying == True):
                                    mvs = 1
                                if ( invoice.invoice_category.pk == 'expenses' and (invoice.is_rectifying == False or invoice.is_rectifying == None)):
                                    mvs = -1

                            # Set Amount if is None or Auto
                            if (amount is None or amount == "" or amount == "auto"):
                                amount_inv = Concept.objects.filter(invoice=invoice).aggregate(total=Sum(F('total_euros')))['total'] or 0
                                amount_inv = amount_inv * mvs
                                amount_used = Reconciliation.objects.filter(invoice=invoice_id).aggregate(total=Sum(F('amount')))['total'] or 0
                                # amount_used = amount_used * mvs
                                amount = float(amount_inv) - float(amount_used)

                            # Make New Reconciliation
                            new_rec = Reconciliation()
                            new_rec.movement_id = movement_id
                            new_rec.invoice_id = invoice_id
                            new_rec.account_id = None
                            new_rec.bank_id = None
                            new_rec.amount = amount
                            new_rec.type = rec_type
                            # Eliminar la línea que asigna seller, ya que no existe en el modelo
                            new_rec.save()
                            data.append(model_to_dict(new_rec))
                    else:
                        error = True
                        error_message = "No se ha encontrado la factura"
                else:
                    error = True
                    error_message = "El ID de la factura no es válido"
            elif type == 'account':
                rec_type = ReconciliationType.objects.filter(pk=type).first()
                if account_id is not None and account_id.isdigit():
                    account = AccountingAccount.objects.filter(pk=account_id).first()
                    # print(f'ACCOUNT: {account} - {account_id}')
                    if account is not None or account_id is not None:
                        otherRecs = Reconciliation.objects.filter(accounting_account=account_id, movement_id=movement_id).count()
                        if otherRecs == 0:
                            # Make New Reconciliation
                            new_rec = Reconciliation()
                            new_rec.movement_id = movement_id
                            new_rec.invoice_id = None
                            new_rec.accounting_account = account
                            new_rec.accounting_account_detail = account_id
                            new_rec.bank_id = None
                            new_rec.amount = amount
                            new_rec.type = rec_type
                            # Eliminar la línea que asigna seller, ya que no existe en el modelo
                            new_rec.save()
                            data.append(model_to_dict(new_rec))
                        else:
                            error = True
                            error_message = "Ya existe una conciliación para el movimiento y la cuenta contable"
                    # else:
                    #     error = True
                    #     error_message = "No se ha encontrado la cuenta contable"
                else:
                    error = True
                    error_message = "El ID de la cuenta contable no es válido"
            elif type == 'bank':
                rec_type = ReconciliationType.objects.filter(pk=type).first()
                if bank_id is not None and bank_id.isdigit():
                    bank = Bank.objects.filter(pk=bank_id, bank_seller=seller).first()
                    if bank is not None:
                        otherRecs = Reconciliation.objects.filter(bank_id=bank_id, movement_id=movement_id).count()
                        if otherRecs == 0:
                            # Make New Reconciliation
                            new_rec = Reconciliation()
                            new_rec.movement_id = movement_id
                            new_rec.invoice_id = None
                            new_rec.account_id = None
                            new_rec.bank_id = bank_id
                            new_rec.amount = amount
                            new_rec.type = rec_type
                            # Eliminar la línea que asigna seller, ya que no existe en el modelo
                            new_rec.save()
                            data.append(model_to_dict(new_rec))
                        else:
                            error = True
                            error_message = "Ya existe una conciliación para el movimiento y banco"
                    else:
                        error = True
                        error_message = "No se ha encontrado el Banco"
                else:
                    error = True
                    error_message = "El ID de la cuenta bancaria no es válido"
            elif type == 'transfer':
                rec_type = ReconciliationType.objects.filter(pk=type).first()
                if movement_transfer_id is not None and movement_transfer_id.isdigit():
                    movement_transfer = BankMovement.objects.filter(pk=movement_transfer_id, bank__bank_seller=seller).first()
                    if movement_transfer is not None:
                        otherRecs = Reconciliation.objects.filter(movement_transfer_id=movement_transfer_id, movement_id=movement_id).count()
                        if otherRecs == 0:

                            # Set Amount if is None or Auto
                            if (amount is None or amount == "" or amount == "auto"):
                                amount = BankMovement.objects.filter(pk=movement_transfer_id, bank__bank_seller=seller).aggregate(total=Sum(F('amount_euros')))['total'] or 0

                            # Make New Reconciliation => Movement_Transfer to Movement
                            new_rec = Reconciliation()
                            new_rec.movement_id = movement_transfer_id
                            new_rec.invoice_id = None
                            new_rec.account_id = None
                            new_rec.bank_id = None
                            new_rec.movement_transfer_id = movement_id
                            new_rec.amount = amount
                            new_rec.type = rec_type
                            # Eliminar la línea que asigna seller, ya que no existe en el modelo
                            new_rec.save()

                            # Make New Reconciliation => Movement to Movement_Transfer
                            new_rec = Reconciliation()
                            new_rec.movement_id = movement_id
                            new_rec.invoice_id = None
                            new_rec.account_id = None
                            new_rec.bank_id = None
                            new_rec.movement_transfer_id = movement_transfer_id
                            new_rec.amount = amount
                            new_rec.type = rec_type
                            # Eliminar la línea que asigna seller, ya que no existe en el modelo
                            new_rec.save()
                            data.append(model_to_dict(new_rec))
                        else:
                            error = True
                            error_message = "Ya existe una conciliación para la transferencia entre estos movimientos"
                    else:
                        error = True
                        error_message = "No se ha encontrado el Movimiento destino."
                else:
                    error = True
                    error_message = "El ID del movimiento destino no es válido"
            else:
                error = True
                error_message = "El tipo de conciliación no es válido"
        else:
            error = True
            error_message = "No se ha recibido el movimiento destino y origien"


        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "result": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "result": "ok",
                "message": "Conciliación creada correctamente",
                "data": data,
            }
            update_movement_status(seller, movement_id)
            if movement_transfer_id is not None:
                update_movement_status(seller, movement_transfer_id)

        # Obtener bank_id del movimiento origen
        bank_id = movement.bank_id if movement and movement.bank else None

        data["bank_id"] = bank_id
        return JsonResponse(data)

class RemoveReconcilation(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):

    def get(self, request, *args, **kwargs):
        movement_id = request.GET.get('movement_id')
        reconcilation_id = request.GET.get('reconcilation_id')
        invoice_id = request.GET.get('invoice_id')
        transfer_id = request.GET.get('movement_transfer_id')
        return self._get_response(movement_id, reconcilation_id, invoice_id, transfer_id)

    def post(self, request, *args, **kwargs):
        movement_id = request.POST.get('movement_id')
        reconcilation_id = request.POST.get('reconcilation_id')
        invoice_id = request.POST.get('invoice_id')
        transfer_id = request.POST.get('movement_transfer_id')
        return self._get_response(movement_id, reconcilation_id, invoice_id, transfer_id)

    # def _get_response(self, movement_id, reconcilation_id, invoice_id, movement_transfer_id):
    #     error = False
    #     error_message = None
    #     data = []
    #     seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

    #     if (reconcilation_id is not None and movement_id is None):
    #         move = BankMovement.objects.filter(reconciliation_movement = reconcilation_id).first()
    #         if (move is not None):
    #             movement_id = str(move.pk)

    #     if (invoice_id is not None and movement_id is not None and reconcilation_id is None):
    #         rec = Reconciliation.objects.filter(invoice_id = invoice_id).first()
    #         if (rec is not None):
    #             reconcilation_id = str(rec.pk)
    #             actual_movement_id = rec.movement_id

    #     if (movement_transfer_id is not None and movement_id is not None and reconcilation_id is None):
    #         rec = Reconciliation.objects.filter(movement_transfer_id = movement_transfer_id, movement_id = movement_id).first()
    #         if (rec is not None):
    #             reconcilation_id = str(rec.pk)

    #     if movement_id is not None and reconcilation_id is not None:
    #         if reconcilation_id.isdigit() and movement_id.isdigit():
    #             rec = Reconciliation.objects.filter(pk=reconcilation_id, movement_id=movement_id, movement__bank__bank_seller = seller).first()
    #             if rec is not None:

    #                 if (rec.type is not None and rec.type.pk == 'transfer'):
    #                     rec2 = Reconciliation.objects.filter(movement_transfer_id=rec.movement_id, movement_id=rec.movement_transfer_id).first()
    #                     if rec2 is not None:
    #                         rec2.delete()
    #                         data.append(model_to_dict(rec2))

    #                 rec.delete()
    #                 data.append(model_to_dict(rec))
    #             else:
    #                 error = True
    #                 error_message = "No se ha encontrado la conciliación"
    #         else:
    #             error = True
    #             error_message = "El ID de la conciliación o del movimiento no es válido"
    #     elif reconcilation_id is None and movement_id is not None:
    #         if movement_id.isdigit():
    #             mov = BankMovement.objects.filter(pk=movement_id, bank__bank_seller = seller).first()
    #             rec = Reconciliation.objects.filter(movement_id=movement_id, movement__bank__bank_seller = seller)
    #             if rec is not None:
    #                 for r in rec:
    #                     movement_transfer_id = None
    #                     if (r.type is not None and r.type.pk == 'transfer'):
    #                         r2 = Reconciliation.objects.filter(movement_transfer_id=r.movement_id, movement_id=r.movement_transfer_id).first()
    #                         if r2 is not None:
    #                             r2.delete()
    #                             update_movement_status(seller, r.movement_transfer_id)
    #                             data.append(model_to_dict(r2))
    #                             movement_transfer_id = r.movement_transfer_id
    #                     r.delete()
    #                     # update_movement_status(seller, r.movement_id)
    #                     data.append( model_to_dict(r) )

    #                     # Try to delete Amz Movement on remove transfer reconciliation
    #                     try:
    #                         if movement_transfer_id is not None:
    #                             m_trans = BankMovement.objects.filter( pk = movement_transfer_id ).first()
    #                             b_trans = m_trans.bank
    #                             if b_trans is not None and str(b_trans.bank_name).lower() == 'amazon':
    #                                 m_trans.delete()
    #                     except Exception as e:
    #                         print(f"Error on remove amz movement: {e}")
    #                         pass
    #             else:
    #                 error = True
    #                 error_message = "No se ha encontrado la conciliación"

    #             if mov is not None:
    #                 if mov.bank is not None:
    #                     bnk_name = str(mov.bank.bank_name).lower()
    #                     if bnk_name == 'amazon' or bnk_name == 'accounts':
    #                         mov.delete()
    #         else:
    #             error = True
    #             error_message = "El ID del movimiento no es válido"
    #     else:
    #         error = True
    #         error_message = "No se ha recibido el movimiento o la conciliación"

    #     # Si Existe un error => Devolver el Error
    #     if error and error_message:
    #         data = {
    #             "result": "error",
    #             "message": error_message,
    #             "data": []
    #         }
    #     else:
    #         data = {
    #             "result": "ok",
    #             "message": "Conciliaciónes eliminadas correctamente",
    #             "data": data,
    #         }
    #         update_movement_status(seller, movement_id)
    #         if movement_transfer_id is not None:
    #             update_movement_status(seller, movement_transfer_id)

    #     # Obtener bank_id del movimiento principal (si existe)
    #     bank_id = None
    #     if movement_id and str(movement_id).isdigit():
    #         mov = BankMovement.objects.filter(pk=movement_id).first()
    #         if mov and mov.bank:
    #             bank_id = mov.bank.pk

    #     # Devolver una respuesta en formato JSON
    #     data["bank_id"] = bank_id
    #     return JsonResponse(data)

    def _get_response(self, movement_id, reconcilation_id, invoice_id, movement_transfer_id):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        if (reconcilation_id is not None and movement_id is None):
            move = BankMovement.objects.filter(reconciliation_movement = reconcilation_id).first()
            if (move is not None):
                movement_id = str(move.pk)

        # Initialize variables to store actual movement IDs
        actual_movement_id = None
        affected_movement_ids = []

        if (invoice_id is not None and movement_id is not None and reconcilation_id is None):
            # Modificado: Ordenar por ID en orden descendente para obtener la más reciente primero
            # También usar .last() en lugar de .first() para obtener la última reconciliación
            rec = Reconciliation.objects.filter(
                invoice_id=invoice_id,
                movement_id=movement_id
            ).order_by('-id').first()

            # Si no se encuentra, buscar cualquier reconciliación con esta factura, ordenada por ID descendente
            if rec is None:
                rec = Reconciliation.objects.filter(
                    invoice_id=invoice_id
                ).order_by('-id').first()

            if (rec is not None):
                reconcilation_id = str(rec.pk)
                # Store the actual movement_id from the reconciliation
                actual_movement_id = rec.movement_id
                affected_movement_ids.append(actual_movement_id)

        if (movement_transfer_id is not None and movement_id is not None and reconcilation_id is None):
            # También ordenar por ID descendente aquí
            rec = Reconciliation.objects.filter(
                movement_transfer_id=movement_transfer_id,
                movement_id=movement_id
            ).order_by('-id').first()

            if (rec is not None):
                reconcilation_id = str(rec.pk)
                affected_movement_ids.append(movement_id)

        if movement_id is not None and reconcilation_id is not None:
            if reconcilation_id.isdigit() and movement_id.isdigit():
                # If we have an actual_movement_id, use that to find the reconciliation
                lookup_movement_id = actual_movement_id if actual_movement_id else movement_id

                rec = Reconciliation.objects.filter(
                    pk=reconcilation_id,
                    movement_id=lookup_movement_id,
                    movement__bank__bank_seller=seller
                ).first()

                if rec is not None:
                    affected_movement_ids.append(rec.movement_id)

                    if (rec.type is not None and rec.type.pk == 'transfer'):
                        # También ordenar por ID descendente aquí
                        rec2 = Reconciliation.objects.filter(
                            movement_transfer_id=rec.movement_id,
                            movement_id=rec.movement_transfer_id
                        ).order_by('-id').first()

                        if rec2 is not None:
                            affected_movement_ids.append(rec2.movement_id)
                            rec2.delete()
                            data.append(model_to_dict(rec2))

                    rec.delete()
                    data.append(model_to_dict(rec))
                else:
                    error = True
                    error_message = "No se ha encontrado la conciliación"
            else:
                error = True
                error_message = "El ID de la conciliación o del movimiento no es válido"
        elif reconcilation_id is None and movement_id is not None:
            if movement_id.isdigit():
                mov = BankMovement.objects.filter(pk=movement_id, bank__bank_seller=seller).first()
                affected_movement_ids.append(int(movement_id))

                # If we're removing by invoice_id, we need to find the specific reconciliation
                if invoice_id is not None:
                    # Ordenar por ID descendente para obtener las más recientes primero
                    recs = Reconciliation.objects.filter(
                        invoice_id=invoice_id,
                        movement__bank__bank_seller=seller
                    ).order_by('-id')
                else:
                    # Otherwise, get all reconciliations for this movement, ordered by newest first
                    recs = Reconciliation.objects.filter(
                        movement_id=movement_id,
                        movement__bank__bank_seller=seller
                    ).order_by('-id')

                if recs.exists():
                    for r in recs:
                        movement_transfer_id = None
                        affected_movement_ids.append(r.movement_id)

                        if (r.type is not None and r.type.pk == 'transfer'):
                            # Ordenar por ID descendente aquí también
                            r2 = Reconciliation.objects.filter(
                                movement_transfer_id=r.movement_id,
                                movement_id=r.movement_transfer_id
                            ).order_by('-id').first()

                            if r2 is not None:
                                affected_movement_ids.append(r2.movement_id)
                                r2.delete()
                                update_movement_status(seller, r.movement_transfer_id)
                                data.append(model_to_dict(r2))
                                movement_transfer_id = r.movement_transfer_id

                        r.delete()
                        data.append(model_to_dict(r))

                        # Try to delete Amz Movement on remove transfer reconciliation
                        try:
                            if movement_transfer_id is not None:
                                m_trans = BankMovement.objects.filter(pk=movement_transfer_id).first()
                                b_trans = m_trans.bank
                                if b_trans is not None and str(b_trans.bank_name).lower() == 'amazon':
                                    m_trans.delete()
                        except Exception as e:
                            print(f"Error on remove amz movement: {e}")
                            pass
                else:
                    error = True
                    error_message = "No se ha encontrado la conciliación"

                if mov is not None:
                    if mov.bank is not None:
                        bnk_name = str(mov.bank.bank_name).lower()
                        if bnk_name == 'amazon' or bnk_name == 'accounts':
                            mov.delete()
            else:
                error = True
                error_message = "El ID del movimiento no es válido"
        else:
            error = True
            error_message = "No se ha recibido el movimiento o la conciliación"

        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "result": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "result": "ok",
                "message": "Conciliaciónes eliminadas correctamente",
                "data": data,
            }

            # Update all affected movements
            affected_movement_ids = list(set(affected_movement_ids))  # Remove duplicates
            for move_id in affected_movement_ids:
                update_movement_status(seller, move_id)

            if movement_transfer_id is not None:
                update_movement_status(seller, movement_transfer_id)

        # Obtener bank_id del movimiento principal (si existe)
        bank_id = None
        if movement_id and str(movement_id).isdigit():
            mov = BankMovement.objects.filter(pk=movement_id).first()
            if mov and mov.bank:
                bank_id = mov.bank.pk

        # If we removed a reconciliation from a different movement, include both movement IDs
        if actual_movement_id and actual_movement_id != int(movement_id):
            data["original_movement_id"] = actual_movement_id

            # Also get the bank_id for this movement
            orig_mov = BankMovement.objects.filter(pk=actual_movement_id).first()
            if orig_mov and orig_mov.bank:
                data["original_bank_id"] = orig_mov.bank.pk

        # Devolver una respuesta en formato JSON
        data["bank_id"] = bank_id
        return JsonResponse(data)

class RemoveMovement(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):

    def post(self, request, *args, **kwargs):
        try:
            # Cargar datos del cuerpo de la solicitud
            body = json.loads(request.body.decode('utf-8'))
            movement_id = body.get('movement_id', None)

            # print(f"Datos recibidos en el backend: {body}")  # Log para depuración
            # print(f"Movement ID: {movement_id}")  # Confirmar que movement_id llega correctamente

            # Validar si se proporcionó el ID del movimiento
            if not movement_id:
                return JsonResponse({
                    "result": "error",
                    "message": "No se proporcionó el ID del movimiento.",
                })

            # Obtener el vendedor asociado al shortname
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

            # Buscar el movimiento asociado
            movement = BankMovement.objects.filter(pk=movement_id, bank__bank_seller=seller).first()
            if not movement:
                return JsonResponse({
                    "result": "error",
                    "message": "Movimiento no encontrado.",
                })

            # Verificar si el movimiento pertenece a los bancos prohibidos
            bank_name = movement.bank.bank_name.lower() if movement.bank and movement.bank.bank_name else ""
            if bank_name in ['amazon', 'accounts']:
                return JsonResponse({
                    "result": "error",
                    "message": "No se permite eliminar movimientos de los bancos 'Amazon' o 'Accounts'.",
                })

            # Eliminar todas las conciliaciones asociadas al movimiento
            reconciliations = Reconciliation.objects.filter(movement_id=movement_id)
            reconciliations_data = []

            if reconciliations.exists():
                for reconciliation in reconciliations:
                    # Si es una conciliación tipo "transfer", eliminar también la transferencia asociada
                    if reconciliation.type and reconciliation.type.pk == 'transfer':
                        linked_reconciliation = Reconciliation.objects.filter(
                            movement_transfer_id=reconciliation.movement_id,
                            movement_id=reconciliation.movement_transfer_id
                        ).first()
                        if linked_reconciliation:
                            linked_reconciliation.delete()
                            reconciliations_data.append({
                                "id": linked_reconciliation.pk,
                                "movement": linked_reconciliation.movement.pk if linked_reconciliation.movement else None,
                                "type": linked_reconciliation.type.pk if linked_reconciliation.type else None,
                            })

                    reconciliations_data.append({
                        "id": reconciliation.pk,
                        "movement": reconciliation.movement.pk if reconciliation.movement else None,
                        "type": reconciliation.type.pk if reconciliation.type else None,
                    })

                    reconciliation.delete()

            # ACTUALIZAR ESTADOS ANTES DE ELIMINAR EL MOVIMIENTO
            update_movement_status(seller, movement_id)
            if reconciliations.exists():
                for reconciliation in reconciliations:
                    if reconciliation.movement_transfer_id:
                        update_movement_status(seller, reconciliation.movement_transfer_id)

            # Obtener el ID del banco antes de eliminar el movimiento
            bank_id = movement.bank_id

            # Eliminar el movimiento de la base de datos
            try:
                # Usar delete() para eliminar realmente el registro de la base de datos
                movement.delete()

                # Verificar que el movimiento se haya eliminado correctamente
                if BankMovement.objects.filter(pk=movement_id).exists():
                    return JsonResponse({
                        "result": "error",
                        "message": "No se pudo eliminar el movimiento. Sigue existiendo en la base de datos.",
                    })

                # Respuesta exitosa
                return JsonResponse({
                    "result": "ok",
                    "message": "Movimiento y conciliaciones asociadas eliminados correctamente.",
                    "bank_id": bank_id,
                    "reconciliations": reconciliations_data,
                })
            except Exception as e:
                print(f"Error al eliminar el movimiento: {e}")
                return JsonResponse({
                    "result": "error",
                    "message": f"Error al eliminar el movimiento: {str(e)}",
                })

        except json.JSONDecodeError:
            return JsonResponse({
                "result": "error",
                "message": "El cuerpo de la solicitud no es un JSON válido.",
            })
        except Exception as e:
            print(f"Error inesperado al eliminar movimiento: {e}")
            return JsonResponse({
                "result": "error",
                "message": f"Error inesperado: {str(e)}",
            })

class GetInvoicesForReconcilation(LoginRequiredMixin, IsSellerShortnamePermission, View):

    def get(self, request, *args, **kwargs):
        movement_id = request.GET.get('movement_id')
        invoice_id  = request.GET.get('invoice_id')
        return self._get_response(movement_id, invoice_id)

    def post(self, request, *args, **kwargs):
        movement_id = request.POST.get('movement_id')
        invoice_id  = request.POST.get('invoice_id')
        return self._get_response(movement_id, invoice_id)

    def _get_response(self, movement_id, invoice_id,):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        if movement_id is not None:
            movement = BankMovement.objects.filter(pk=movement_id, bank__bank_seller = seller).first()
            reconcilations = Reconciliation.objects.filter(movement_id=movement_id, type_id="invoice")
            reconcilations_amount = reconcilations.aggregate(total=Sum(F('amount')))['total'] or 0
            invoices_conciliated = reconcilations.values('invoice_id').distinct().values_list('invoice_id', flat=True)
            invoices = Invoice.objects.filter(seller=seller, status='revised', amz_txt_eur = None).exclude(is_txt_amz=True).exclude(invoice_category__code__endswith='_copy')
            if (movement is not None and movement.amount_euros is not None):
                if (movement.amount_euros < 0):
                    invoices = invoices.exclude(Q(invoice_category__code='expenses') & Q(is_rectifying=True) )
                    invoices = invoices.exclude(Q(invoice_category__code='sales')    & (Q(is_rectifying=False) | Q(is_rectifying=None)) )
                else:
                    invoices = invoices.exclude(Q(invoice_category__code='sales') & Q(is_rectifying=True) )
                    invoices = invoices.exclude(Q(invoice_category__code='expenses')    & (Q(is_rectifying=False) | Q(is_rectifying=None)) )

            if invoices is not None:
                if (invoice_id is not None and invoice_id.isdigit()):
                    invoices = invoices.filter(pk=invoice_id)

                invoices = invoices.annotate(
                    provider__name=F('provider__name'),
                    provider__cif=F('provider__nif_cif_iva'),
                    customer__name=F('customer__name'),
                    customer__cif=F('customer__nif_cif_iva'),
                )

                for invoice in invoices:
                    # Check if invoice has any reconciliations with this movement
                    conciliated = invoice.pk in invoices_conciliated

                    movement_amount_pending = movement.amount_euros
                    if (movement is not None and reconcilations_amount is not None):
                        if (movement.amount_euros < 0):
                            if (reconcilations_amount < 0):
                                movement_amount_pending = float(movement.amount_euros) - float(reconcilations_amount)
                            else:
                                movement_amount_pending = float(movement.amount_euros) + float(reconcilations_amount)
                        else:
                            if (reconcilations_amount < 0):
                                movement_amount_pending = float(movement.amount_euros) + float(reconcilations_amount)
                            else:
                                movement_amount_pending = float(movement.amount_euros) - float(reconcilations_amount)

                    mvs = 0
                    if (movement is not None and movement.amount_euros is not None and invoice.invoice_category is not None ):
                        if (float(movement.amount_euros) < 0):
                            if ( invoice.invoice_category.pk == 'sales' and invoice.is_rectifying == True ):
                                mvs = -1
                            if ( invoice.invoice_category.pk == 'sales' and (invoice.is_rectifying == False or invoice.is_rectifying == None) ):
                                mvs = -1
                            if ( invoice.invoice_category.pk == 'expenses' and invoice.is_rectifying == True):
                                mvs = 1
                            if ( invoice.invoice_category.pk == 'expenses' and (invoice.is_rectifying == False or invoice.is_rectifying == None)):
                                mvs = 1
                        else:
                            if ( invoice.invoice_category.pk == 'sales' and invoice.is_rectifying == True ):
                                mvs = 1
                            if ( invoice.invoice_category.pk == 'sales' and (invoice.is_rectifying == False or invoice.is_rectifying == None)):
                                mvs = -1
                            if ( invoice.invoice_category.pk == 'expenses' and invoice.is_rectifying == True):
                                mvs = 1
                            if ( invoice.invoice_category.pk == 'expenses' and (invoice.is_rectifying == False or invoice.is_rectifying == None)):
                                mvs = -1

                    # Calculate total invoice amount
                    total_euros = invoice.concept_set.aggregate(total=Sum(F('total_euros')))['total'] or 0
                    total_euros = round(total_euros, 2) * mvs

                    # Initialize with default values
                    total_euros_used = 0
                    total_euros_pending = total_euros

                    # Calculate reconciled amounts
                    otherRecs = Reconciliation.objects.filter(invoice__pk=invoice.pk)
                    if otherRecs.count() > 0:
                        total_euros_used = otherRecs.aggregate(total=Sum(F('amount')))['total'] or 0
                        total_euros_pending = round(float(total_euros) - float(total_euros_used), 2)

                    # Determine reconciliation status
                    is_fully_reconciled = abs(total_euros_pending) < 0.01  # Using small threshold for floating point comparison
                    is_partially_reconciled = abs(total_euros_used) > 0.01 and not is_fully_reconciled  # Check absolute value for any reconciliations

                    # Calculate difference for sorting/filtering
                    dif_total_euros_pending = total_euros_pending
                    if conciliated and is_fully_reconciled:
                        dif_total_euros_pending = 0

                    if (movement_amount_pending is not None and total_euros_pending is not None and (not conciliated or is_partially_reconciled)):
                        if (float(movement_amount_pending) < 0):
                            if (float(total_euros_pending) < 0):
                                dif_total_euros_pending = float(movement_amount_pending) - float(total_euros_pending)
                            else:
                                dif_total_euros_pending = float(movement_amount_pending) + float(total_euros_pending)
                        else:
                            if (total_euros_pending < 0):
                                dif_total_euros_pending = float(movement_amount_pending) + float(total_euros_pending)
                            else:
                                dif_total_euros_pending = float(movement_amount_pending) - float(total_euros_pending)

                    dif_total_euros_pending = round(dif_total_euros_pending, 2)

                    if (dif_total_euros_pending is not None and dif_total_euros_pending < 0):
                        dif_total_euros_pending = dif_total_euros_pending * -1

                    dif_days = None
                    if (movement.movement_date is not None and invoice.invoice_date is not None):
                        dif_days = (movement.movement_date - invoice.invoice_date).days
                    elif (movement.movement_date is not None and invoice.accounting_date is not None):
                        dif_days = (movement.movement_date - invoice.accounting_date).days
                    else:
                        dif_days = None

                    if (dif_days is not None and dif_days < 0):
                        dif_days = dif_days * -1

                    # Only include invoices that have pending amount or are already reconciled
                    if total_euros_pending != 0 or conciliated:
                        # Create a sort field for reconciliation status
                        # 0 = fully reconciled, 1 = partially reconciled, 2 = not reconciled
                        reconciliation_sort = 2  # Default: not reconciled
                        if is_fully_reconciled:
                            reconciliation_sort = 0  # Fully reconciled
                        elif is_partially_reconciled:
                            reconciliation_sort = 1  # Partially reconciled

                        # Get a list of movement IDs this invoice is reconciled with
                        reconciliations_with_movements = []
                        if conciliated or is_partially_reconciled or is_fully_reconciled:
                            reconciliations_with_movements = list(Reconciliation.objects.filter(
                                invoice=invoice.pk
                            ).values_list('movement_id', flat=True).distinct())

                        data.append({
                            "id": invoice.pk,
                            "conciliated": conciliated,  # Keep this for backward compatibility
                            "fully_reconciled": is_fully_reconciled,  # New field
                            "partially_reconciled": is_partially_reconciled,  # New field
                            "reconciliation_sort": reconciliation_sort,  # New field for sorting
                            "reference": invoice.reference,
                            "concept": invoice.concept_set.first().concept,
                            "invoice_date": invoice.invoice_date or invoice.expedition_date,
                            "accounting_date": invoice.accounting_date,
                            "dif_days": dif_days,
                            "total_euros": total_euros,
                            "total_euros_pending": total_euros_pending,
                            "total_euros_reconciled": total_euros_used,  # Add amount already reconciled
                            "dif_total_euros_pending": dif_total_euros_pending,
                            "provider_name": invoice.provider__name,
                            "provider_cif": invoice.provider__cif,
                            "customer_name": invoice.customer__name,
                            "customer_cif": invoice.customer__cif,
                            "reconciliations_with_movements": reconciliations_with_movements,  # Add list of movement IDs
                        })

        else:
            error = True
            error_message = "No se ha recibido el movimiento"

        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "draw":1,
                "recordsFiltered":0,
                "recordsTotal":0,
                "result": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "draw":1,
                "recordsFiltered":data.__len__(),
                "recordsTotal":data.__len__(),
                "result": "ok",
                "data": data,
            }
        # Devolver una respuesta en formato JSON
        return JsonResponse(data)


class GetMovementsForReconcilation(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def get(self, request, *args, **kwargs):
        movement_id = request.GET.get('movement_id')
        bank_id = request.GET.get('bank_id')
        return self._get_response(movement_id, bank_id)

    def post(self, request, *args, **kwargs):
        movement_id = request.POST.get('movement_id')
        bank_id  = request.POST.get('bank_id')
        return self._get_response(movement_id, bank_id)

    def _get_response(self, movement_id, bank_id):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        if movement_id is not None:
            mv_from = BankMovement.objects.filter(pk=movement_id, bank__bank_seller = seller).first()
            if (mv_from is not None):
                reconcilations = Reconciliation.objects.filter(movement_id=movement_id, type_id="transfer")
                reconcilations_amount_euros = reconcilations.aggregate(total=Sum(F('amount')))['total'] or 0
                mv_conciliated = reconcilations.values('movement_transfer_id').distinct().values_list('movement_transfer_id', flat=True)
                if (reconcilations_amount_euros is not None):
                    mv_from_amount_pending = mv_from.amount_euros - reconcilations_amount_euros
                mv_from_amount_pending = mv_from.amount_euros


                movements = BankMovement.objects.filter(bank__bank_seller=seller).exclude(status='conciliated').exclude(bank=mv_from.bank)
                if (movements):
                    if (bank_id is not None and bank_id.isdigit()):
                        movements = movements.filter(bank_id = bank_id)

                    for mv in movements:
                        # Conciliated
                        conciliated = False
                        if (mv.pk in mv_conciliated):
                            conciliated = True

                        # Total Euros
                        total_euros = round(mv.amount_euros,2)

                        # Get reconciliations for this movement
                        movement_reconciliations = Reconciliation.objects.filter(movement=mv.pk)

                        # Calculate total reconciled amount
                        total_euros_reconciled = movement_reconciliations.aggregate(total=Sum(F('amount')))['total'] or 0

                        # Total Euros Pending
                        total_euros_pending = round(total_euros - total_euros_reconciled, 2)

                        # Determine reconciliation status
                        fully_reconciled = abs(total_euros_pending) < 0.01  # Using small threshold for floating point comparison
                        partially_reconciled = total_euros_reconciled > 0 and not fully_reconciled

                        # Check if there's a reconciliation specifically with the current movement
                        direct_reconciliation = Reconciliation.objects.filter(
                            movement=mv.pk,
                            movement_transfer=movement_id
                        ).exists()

                        # Diff Total Euros Pending
                        dif_total_euros_pending = total_euros_pending
                        if (conciliated):
                            dif_total_euros_pending = 0

                        if (mv_from_amount_pending is not None and total_euros_pending is not None and not conciliated):
                            if (float(mv_from_amount_pending) < 0):
                                if (float(total_euros_pending) < 0):
                                    dif_total_euros_pending = float(mv_from_amount_pending) - float(total_euros_pending)
                                else:
                                    dif_total_euros_pending = float(mv_from_amount_pending) + float(total_euros_pending)
                            else:
                                if (total_euros_pending < 0):
                                    dif_total_euros_pending = float(mv_from_amount_pending) + float(total_euros_pending)
                                else:
                                    dif_total_euros_pending = float(mv_from_amount_pending) - float(total_euros_pending)

                        dif_total_euros_pending = round(dif_total_euros_pending, 2)

                        if (dif_total_euros_pending is not None and dif_total_euros_pending < 0):
                            dif_total_euros_pending = dif_total_euros_pending * -1

                        # Diff Days
                        dif_days = None
                        if (mv_from.movement_date is not None and mv.movement_date is not None):
                            dif_days = (mv_from.movement_date - mv.movement_date).days

                        if (dif_days is not None and dif_days < 0):
                            dif_days = dif_days * -1


                        data.append({
                            "id": mv.pk,
                            "movement_number": mv.movement_number,
                            "conciliated": conciliated,
                            "fully_reconciled": fully_reconciled,
                            "partially_reconciled": partially_reconciled,
                            "total_euros_reconciled": total_euros_reconciled,
                            "direct_reconciliation": direct_reconciliation,
                            "concept": mv.concept,
                            "observation": mv.observation,
                            "movement_date": mv.movement_date,
                            "dif_days": dif_days,
                            "amount_euros": total_euros,
                            "amount_euros_pending": total_euros_pending,
                            "dif_total_euros_pending": dif_total_euros_pending,
                            "status": mv.status.pk,
                        })
                else:
                    error = True
                    error_message = "No se han encontrado movimientos destino para conciliar"
            else:
                error = True
                error_message = "No se ha encontrado el movimiento origen"
        else:
            error = True
            error_message = "No se ha recibido el movimiento origen"
        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "draw":1,
                "recordsFiltered":0,
                "recordsTotal":0,
                "result": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "draw":1,
                "recordsFiltered":data.__len__(),
                "recordsTotal":data.__len__(),
                "result": "ok",
                "data": data,
            }
        # Devolver una respuesta en formato JSON
        return JsonResponse(data)

class GetMovementsDT(LoginRequiredMixin, IsSellerShortnamePermission, BaseDatatableView):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    model = BankMovement

    columns = [
        "pk",
        "movement_number",
        "status.pk",
        "concept",
        "observation",
        "amount",
        "amount_euros",
        "currency.pk",
        "movement_date",
        "bank.pk",
        "used_in_entry",
        "reconciliations_amount",
        "reconciliations_quantity",
        "reconciliations",
        "created_at",
        "modified_at",
    ]

    order_columns = columns

    max_display_length = 9999999

    def filter_queryset(self, qs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        qs = qs.filter(bank__bank_seller=seller)

        bank_id = self.request.GET.get('bank_id')
        if bank_id:
            qs = qs.filter(bank_id=bank_id)
        else:
            qs = BankMovement.objects.none()

        year = self.request.GET.get('year')
        if year and year.isdigit():
            qs = qs.filter(movement_date__year=year)

        months_param = self.request.GET.get('months', '')
        if months_param:
            try:
                months = [int(month) for month in months_param.split(",") if month.isdigit()]
                qs = qs.filter(movement_date__month__in=months)
            except ValueError:
                qs = qs.none()

        search_value = self.request.GET.get('search[value]', '').strip()
        if search_value:
            qs = qs.filter(
                Q(movement_number__icontains=search_value) |
                Q(concept__icontains=search_value) |
                Q(observation__icontains=search_value) |
                Q(amount__icontains=search_value) |
                Q(amount_euros__icontains=search_value) |
                Q(movement_date__icontains=search_value)
            )

        return qs

    def render_column(self, row, column):
        if column == 'reconciliations':
            return row.reconciliations

        return super().render_column(row, column)

    def prepare_results(self, qs):
        data = []
        for row in qs:
            try:
                reconciliations = [
                    {
                        "id": rec.pk,
                        "type_id": rec.type.pk if rec.type else None,
                        "amount": rec.amount,
                        "invoice_id": rec.invoice.pk if rec.invoice else None,
                        "invoice__reference": rec.invoice.reference if rec.invoice else None,
                        "invoice__invoice_category": str(rec.invoice.invoice_category) if rec.invoice and rec.invoice.invoice_category else None,
                        "invoice__is_rectifying": rec.invoice.is_rectifying if rec.invoice else False,
                        "bank_id": rec.bank.pk if rec.bank else None,
                        "movement_transfer_id": rec.movement_transfer.pk if rec.movement_transfer else None,
                        "accounting_account_id": rec.accounting_account.pk if rec.accounting_account else None,
                        "accounting_account_detail": rec.accounting_account_detail if hasattr(rec, 'accounting_account_detail') and rec.accounting_account_detail else None,
                    }
                    for rec in row.reconciliation_movement.all()
                ]

                row_data = {
                    "id": str(row.pk) if row.pk else None,
                    "bank_id": row.bank.pk if row.bank else None,
                    "bank_name": row.bank.bank_name if row.bank else None,
                    "status_id": row.status.pk if row.status else None,
                    "movement_number": row.movement_number or "",
                    "concept": row.concept or "",
                    "observation": row.observation or "",
                    "amount": row.amount if row.amount is not None else 0.0,
                    "currency": row.currency.pk if row.currency else None,
                    "amount_euros": row.amount_euros if row.amount_euros is not None else 0.0,
                    "movement_date": row.movement_date.isoformat() if row.movement_date else None,
                    "reconciliations_amount": row.reconciliations_amount if row.reconciliations_amount is not None else 0.0,
                    "reconciliations_quantity": row.reconciliations_quantity if row.reconciliations_quantity is not None else 0,
                    "reconciliations": reconciliations,
                }

                data.append(row_data)
            except Exception as e:
                # logs para identificar problemas con filas específicas
                print(f"Error procesando fila con ID {getattr(row, 'pk', 'desconocido')}: {str(e)}")

        return data


    def handle_no_permission(self):
        response_data = {'error': 'No tienes permiso para realizar la peticion.'}
        return JsonResponse(response_data, status=403)

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)

        # Asegúrate de que la respuesta sea JSON serializable antes de imprimir
        try:
            content = response.content.decode('utf-8')  # Decodificar el contenido a string
            json_data = json.loads(content)  # Cargar el contenido como JSON
            # print("JSON enviado al DataTable:", json.dumps(json_data, indent=2))
        except Exception as e:
            print("Error al procesar la respuesta JSON:", str(e))

        return response

class GetTotalAmount(LoginRequiredMixin, IsSellerShortnamePermission, View):
    """
    Vista para calcular la suma total de la columna 'amount' para un banco específico.
    """

    def get(self, request, *args, **kwargs):
        # Extraer el shortname y bank_id desde kwargs
        shortname = self.kwargs.get("shortname")
        bank_id = self.kwargs.get("bank_id")

        # Logs para depuración
        print(f"Datos de la solicitud GET:\nshortname: {shortname}\nbank_id: {bank_id}")

        # Validar que bank_id fue proporcionado
        if not bank_id or not bank_id.isdigit():
            print("Error: El ID del banco no fue proporcionado o no es válido.")
            return JsonResponse({
                "result": "error",
                "message": "El ID del banco es requerido.",
                "total_amount": 0,
            })

        # Obtener el vendedor asociado al shortname
        seller = get_object_or_404(Seller, shortname=shortname)

        # Filtrar movimientos asociados al banco que estén en estado "conciliado"
        movements = BankMovement.objects.filter(
            bank_id=bank_id,
            bank__bank_seller=seller,
            status__pk="conciliated",
        )

        # Calcular la suma total de la columna 'amount'
        total_amount = movements.aggregate(total=Sum("amount_euros"))["total"] or 0

        # Logs para verificar los resultados
        print(f"Total calculado para bank_id={bank_id}: {total_amount}")

        # Respuesta JSON con el resultado
        return JsonResponse({
            "result": "ok",
            "message": "Cálculo exitoso.",
            "total_amount": total_amount,
        })

class SaveBankRule(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    """
    Vista para guardar una nueva regla bancaria o actualizar una existente.
    """

    def post(self, request, *args, **kwargs):
        try:
            # Cargar datos del cuerpo de la solicitud
            data = json.loads(request.body.decode('utf-8')) if request.body else request.POST

            bank_id = data.get('bank_id')
            rule_match_type = data.get('rule_match_type')
            pattern = data.get('rule_pattern')
            rule_id = data.get('rule_id')  # Para actualizaciones
            accounting_account_id = data.get('accounting_account_id')  # ID de la cuenta contable

            # Validar datos requeridos
            if not all([bank_id, rule_match_type, pattern]):
                return JsonResponse({
                    "result": "error",
                    "message": "Todos los campos son requeridos."
                })

            # Obtener el vendedor asociado al shortname
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

            # Obtener el banco
            bank = get_object_or_404(Bank, pk=bank_id, bank_seller=seller)

            # Si se proporciona rule_id, actualizar la regla existente
            if rule_id:
                try:
                    rule = get_object_or_404(BankRule, pk=rule_id, bank=bank, seller=seller)
                    rule.rule_match_type = rule_match_type
                    rule.pattern = pattern

                    # Actualizar la cuenta contable si se proporciona
                    if accounting_account_id:
                        try:
                            # Buscar la cuenta contable por ID
                            account = AccountingAccount.objects.get(pk=accounting_account_id)
                            rule.accounting_account = account
                        except AccountingAccount.DoesNotExist:
                            # Intentar asignación directa como respaldo
                            rule.accounting_account_id = accounting_account_id
                    else:
                        rule.accounting_account = None

                    rule.save()
                    message = "Regla actualizada correctamente."
                except Exception as e:
                    return JsonResponse({
                        "result": "error",
                        "message": f"Error al actualizar la regla: {str(e)}"
                    })
            else:
                # Crear una nueva regla
                try:
                    # Crear una nueva regla
                    rule = BankRule(
                        bank=bank,
                        rule_match_type=rule_match_type,
                        pattern=pattern,
                        seller=seller
                    )

                    # Asignar la cuenta contable si se proporciona
                    if accounting_account_id:
                        try:
                            # Buscar la cuenta contable por ID
                            account = AccountingAccount.objects.get(pk=accounting_account_id)
                            rule.accounting_account = account
                        except AccountingAccount.DoesNotExist:
                            # Intentar asignación directa como respaldo
                            rule.accounting_account_id = accounting_account_id

                    # Guardar la regla
                    rule.save()

                    message = "Regla creada correctamente."
                except Exception as e:
                    print(f"Error detallado al crear la regla: {str(e)}")
                    return JsonResponse({
                        "result": "error",
                        "message": f"Error al crear la regla: {str(e)}"
                    })

            # Devolver respuesta exitosa
            return JsonResponse({
                "result": "ok",
                "message": message,
                "rule_id": rule.pk
            })

        except json.JSONDecodeError:
            return JsonResponse({
                "result": "error",
                "message": "El cuerpo de la solicitud no es un JSON válido."
            })
        except Exception as e:
            print(f"Error inesperado en SaveBankRule: {str(e)}")
            return JsonResponse({
                "result": "error",
                "message": f"Error inesperado: {str(e)}"
            })

class GetBankRules(LoginRequiredMixin, IsSellerShortnamePermission, View):
    """
    Vista para obtener las reglas bancarias de un banco específico.
    """

    def get(self, request, *args, **kwargs):
        bank_id = request.GET.get('bank_id')

        if not bank_id:
            return JsonResponse({
                "result": "error",
                "message": "El ID del banco es requerido.",
                "rules": []
            })

        # Obtener el vendedor asociado al shortname
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        try:
            # Obtener el banco
            bank = get_object_or_404(Bank, pk=bank_id, bank_seller=seller)

            # Obtener las reglas del banco
            rules = BankRule.objects.filter(bank=bank, seller=seller, is_active=True)

            # Formatear las reglas para la respuesta
            rules_data = [{
                "id": rule.pk,
                "rule_match_type": rule.rule_match_type,
                "rule_match_type_display": rule.get_rule_match_type_display(),
                "pattern": rule.pattern,
                "priority": rule.priority,
                "accounting_account_id": rule.accounting_account_id,
                "accounting_account_description": rule.accounting_account.description if rule.accounting_account else None,
                "created_at": rule.created_at.isoformat() if rule.created_at else None,
                "modified_at": rule.modified_at.isoformat() if rule.modified_at else None,
            } for rule in rules]

            return JsonResponse({
                "result": "ok",
                "message": "Reglas obtenidas correctamente.",
                "rules": rules_data
            })

        except Exception as e:
            return JsonResponse({
                "result": "error",
                "message": f"Error al obtener las reglas: {str(e)}",
                "rules": []
            })

class DeleteBankRule(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    """
    Vista para eliminar una regla bancaria.
    """

    def post(self, request, *args, **kwargs):
        try:
            # Cargar datos del cuerpo de la solicitud
            data = json.loads(request.body.decode('utf-8')) if request.body else request.POST

            rule_id = data.get('rule_id')

            # Validar datos requeridos
            if not rule_id:
                return JsonResponse({
                    "result": "error",
                    "message": "El ID de la regla es requerido."
                })

            # Obtener el vendedor asociado al shortname
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

            # Obtener la regla
            try:
                rule = get_object_or_404(BankRule, pk=rule_id, seller=seller)

                # Eliminar la regla completamente de la base de datos
                rule.delete()

                # Verificar que la regla se haya eliminado correctamente
                if BankRule.objects.filter(pk=rule_id).exists():
                    return JsonResponse({
                        "result": "error",
                        "message": "No se pudo eliminar la regla. Sigue existiendo en la base de datos."
                    })

                return JsonResponse({
                    "result": "ok",
                    "message": "Regla eliminada correctamente."
                })
            except Exception as e:
                return JsonResponse({
                    "result": "error",
                    "message": f"Error al eliminar la regla: {str(e)}"
                })

        except json.JSONDecodeError:
            return JsonResponse({
                "result": "error",
                "message": "El cuerpo de la solicitud no es un JSON válido."
            })


class UpdateBankRulePriorities(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    """
    Vista para actualizar las prioridades de las reglas bancarias.
    """

    def post(self, request, *args, **kwargs):
        try:
            # Cargar datos del cuerpo de la solicitud
            data = json.loads(request.body.decode('utf-8')) if request.body else request.POST

            # Obtener parámetros
            bank_id = data.get('bank_id')
            priorities = data.get('priorities', [])

            if not bank_id or not priorities:
                return JsonResponse({
                    'result': 'error',
                    'message': 'Faltan parámetros requeridos'
                })

            # Obtener el vendedor y el banco
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            bank = get_object_or_404(Bank, pk=bank_id, bank_seller=seller)

            # Actualizar las prioridades de las reglas
            try:
                for priority_data in priorities:
                    rule_id = priority_data.get('id')
                    priority = priority_data.get('priority')

                    if rule_id and priority is not None:
                        rule = get_object_or_404(BankRule, pk=rule_id, bank=bank)
                        rule.priority = priority
                        rule.save()

                return JsonResponse({
                    'result': 'ok',
                    'message': 'Prioridades actualizadas correctamente'
                })
            except Exception as e:
                return JsonResponse({
                    'result': 'error',
                    'message': f'Error al actualizar las prioridades: {str(e)}'
                })

        except json.JSONDecodeError:
            return JsonResponse({
                'result': 'error',
                'message': 'El cuerpo de la solicitud no es un JSON válido.'
            })
        except Exception as e:
            return JsonResponse({
                'result': 'error',
                'message': f'Error en la solicitud: {str(e)}'
            })


class ApplyBankRules(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    """
    Vista para aplicar las reglas bancarias a los movimientos pendientes.
    """

    def post(self, request, *args, **kwargs):
        try:
            # Cargar datos del cuerpo de la solicitud
            data = json.loads(request.body.decode('utf-8')) if request.body else request.POST

            # Obtener parámetros
            bank_id = data.get('bank_id')

            if not bank_id:
                return JsonResponse({
                    'result': 'error',
                    'message': 'El ID del banco es requerido.'
                })

            # Obtener el vendedor y el banco
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            bank = get_object_or_404(Bank, pk=bank_id, bank_seller=seller)

            # Obtener las reglas activas del banco ordenadas por prioridad (mayor a menor)
            rules = BankRule.objects.filter(
                bank=bank,
                seller=seller,
                is_active=True
            ).order_by('-priority')

            # Obtener los movimientos pendientes y parcialmente conciliados del banco
            movements = BankMovement.objects.filter(
                bank=bank,
                status__in=['pending', 'partially-conciliated']
            )

            total_movements = movements.count()
            matched_movements = 0
            for movement in movements:
                for rule in rules:
                    # Verificar si la regla tiene una cuenta contable asignada
                    if not rule.accounting_account:
                        print(f"  Regla ID: {rule.pk}, Patrón: '{rule.pattern}' - Saltada (sin cuenta contable)")
                        continue

                    # Obtener el concepto del movimiento
                    concept = movement.concept or ""

                    # Convertir a minúsculas para hacer la comparación insensible a mayúsculas/minúsculas
                    concept_lower = concept.lower()
                    pattern_lower = rule.pattern.lower()

                    # Aplicar la regla según el tipo de coincidencia (insensible a mayúsculas/minúsculas)
                    match = False
                    match_type = rule.rule_match_type
                    if match_type == 'equals':
                        match = concept_lower == pattern_lower
                    elif match_type == 'starts_with':
                        match = concept_lower.startswith(pattern_lower)
                    elif match_type == 'ends_with':
                        match = concept_lower.endswith(pattern_lower)
                    elif match_type == 'contains':
                        match = pattern_lower in concept_lower
                    else:
                        print(f"  Regla ID: {rule.pk}, Tipo: '{match_type}' desconocido, Patrón: '{rule.pattern}', Cuenta: {rule.accounting_account.pk} - Coincidencia: {match}")

                    # Si hay coincidencia, crear una reconciliación para asignar la cuenta contable
                    if match:
                        # Crear una nueva reconciliación de tipo 'account'
                        try:
                            # Verificamos si el movimiento ya está completamente conciliado
                            if movement.status_id == 'conciliated':
                                print(f"  ⚠ El movimiento ID: {movement.pk} ya está completamente conciliado. Saltando.")
                                continue

                            # Obtener el tipo de reconciliación 'account'
                            rec_type = ReconciliationType.objects.filter(pk='account').first()

                            if rec_type:
                                # Determinar el monto a usar para la reconciliación
                                # Para movimientos parcialmente conciliados, usar el monto pendiente
                                if movement.status_id == 'partially-conciliated':
                                    # Calcular el monto pendiente
                                    reconciliations_amount = Reconciliation.objects.filter(movement=movement).aggregate(total=Sum(F('amount')))['total'] or 0

                                    # Verificar si ya existe una reconciliación para este movimiento y cuenta contable
                                    existing_reconciliation = Reconciliation.objects.filter(
                                        movement=movement,
                                        accounting_account=rule.accounting_account
                                    ).first()

                                    # Si existe una reconciliación con la misma cuenta contable, excluirla del cálculo
                                    if existing_reconciliation:
                                        reconciliations_amount -= existing_reconciliation.amount

                                    # Calcular el monto pendiente según la lógica de update_movement_status
                                    if movement.amount_euros < 0:
                                        if reconciliations_amount < 0:
                                            pending_amount = float(movement.amount_euros) - float(reconciliations_amount)
                                        else:
                                            pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                    else:
                                        if reconciliations_amount < 0:
                                            pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                        else:
                                            pending_amount = float(movement.amount_euros) - float(reconciliations_amount)

                                    # Redondear a 2 decimales
                                    pending_amount = round(pending_amount, 2)

                                    # Para la cuenta contable, necesitamos usar el valor con signo opuesto
                                    # para que la conciliación sea completa
                                    amount = -pending_amount
                                else:
                                    # Para movimientos pendientes, usar el monto completo con signo invertido
                                    # Para que la conciliación sea correcta, necesitamos invertir el signo:
                                    # - Si el movimiento es negativo (gasto, -2000), la conciliación debe ser positiva (2000)
                                    # - Si el movimiento es positivo (ingreso, 5000), la conciliación debe ser negativa (-5000)
                                    amount = -movement.amount_euros

                                # Crear la reconciliación con todos los campos requeridos
                                try:
                                    # Verificar si ya existe una reconciliación para este movimiento y cuenta contable
                                    existing_reconciliation = Reconciliation.objects.filter(
                                        movement=movement,
                                        accounting_account=rule.accounting_account
                                    ).first()

                                    # Si existe una reconciliación con la misma cuenta contable
                                    if existing_reconciliation:
                                        # Eliminar la reconciliación existente para crear una nueva con el monto total pendiente
                                        existing_reconciliation.delete()

                                        # Recalcular el monto pendiente después de eliminar la reconciliación
                                        if movement.status_id == 'partially-conciliated':
                                            # Recalcular el monto pendiente
                                            reconciliations_amount = Reconciliation.objects.filter(movement=movement).aggregate(total=Sum(F('amount')))['total'] or 0

                                            # Calcular el monto pendiente según la lógica de update_movement_status
                                            if movement.amount_euros < 0:
                                                if reconciliations_amount < 0:
                                                    pending_amount = float(movement.amount_euros) - float(reconciliations_amount)
                                                else:
                                                    pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                            else:
                                                if reconciliations_amount < 0:
                                                    pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                                else:
                                                    pending_amount = float(movement.amount_euros) - float(reconciliations_amount)

                                            # Redondear a 2 decimales
                                            pending_amount = round(pending_amount, 2)

                                            # Para la cuenta contable, necesitamos usar el valor con signo opuesto
                                            amount = -pending_amount

                                    # Crear la nueva reconciliación con el monto calculado
                                    reconciliation = Reconciliation(
                                        movement=movement,
                                        accounting_account=rule.accounting_account,
                                        accounting_account_detail=rule.accounting_account.code,  # Solo usar el código (max 9 caracteres)
                                        amount=amount,
                                        type=rec_type,
                                        invoice=None,
                                        bank=None,
                                        movement_transfer=None,
                                        used_in_entry=False
                                    )
                                    reconciliation.save()
                                    matched_movements += 1
                                except Exception as e:
                                    print(f"Error al crear reconciliación: {str(e)}")
                                    pass

                            # Actualizar el estado del movimiento
                            update_movement_status(seller, movement.pk)

                            # Verificar si el movimiento quedó completamente conciliado
                            movement.refresh_from_db()
                            if movement.status_id == 'conciliated':
                                print(f"  ✅ Movimiento ID: {movement.pk} completamente conciliado después de aplicar la regla.")
                            else:
                                print(f"  ⚠ Movimiento ID: {movement.pk} sigue parcialmente conciliado después de aplicar la regla. Estado: {movement.status_id}")

                        except Exception:
                            pass

                        break  # Pasar al siguiente movimiento

            return JsonResponse({
                'result': 'ok',
                'message': 'Reglas aplicadas correctamente',
                'total_movements': total_movements,
                'matched_movements': matched_movements
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'result': 'error',
                'message': 'El cuerpo de la solicitud no es un JSON válido.'
            })
        except Exception as e:
            return JsonResponse({
                'result': 'error',
                'message': f'Error al aplicar las reglas: {str(e)}'
            })
