from datetime import datetime
import time
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.conf import settings
from django.db.models import Q
from django.utils import timezone

from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.app_address.models.address import Address
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_process import SellerProcess
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_services.models import Service
from muaytax.app_documents.models import ProcessedForm
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_status_process_color import SellerVatStatusProcessColor
from muaytax.dictionaries.models.sellervat_type import SellerVatType
from muaytax.dictionaries.models.service_type import ServiceType
from muaytax.email_notifications.utils import (
    get_mail_signature_plataform,
    get_mail_signature_muaytax,
    get_mail_signature_info_muaytax,
    get_mail_signature_usa,
)
from muaytax.dictionaries.models.store_products import StoreProduct
from muaytax.utils.env_resources import (
    logo_url_head_muaytax,
    logo_url_head_amzvat
)
from muaytax.users.api.email_generic_contact_msg import get_contact_message_for_product

from muaytax.utils.txt_models import special_char_eliminate

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.validators import UniqueValidator
from django.contrib.auth.password_validation import validate_password
from django.core.mail import EmailMessage, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from django.views.generic import View
from django.shortcuts import render
import pycountry, re


User = get_user_model()

class RegisterSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True)

    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)

    # Campos adicionales
    username2 = serializers.CharField(required=False)
    empresa = serializers.CharField(required=False)
    direccion = serializers.CharField(required=False)
    direccion2 = serializers.CharField(required=False)
    codigo_postal = serializers.CharField(required=False)
    ciudad = serializers.CharField(required=False)
    pais = serializers.CharField(required=False)
    telefono = serializers.CharField(required=False)
    producto = serializers.ListField(child=serializers.CharField(), required=False)
    producto_name = serializers.ListField(child=serializers.CharField(), required=False)

    fecha_compra = serializers.DateField(required=False)
    plataforma = serializers.CharField(required=False)
    nif = serializers.CharField(required=False)
    tipo_empresa = serializers.CharField(required=False)
    is_renewal = serializers.BooleanField(required=False, default=False)
    order_subscription_id = serializers.CharField(required=False)
    fecha_alta = serializers.DateField(required=False)
    fecha_baja = serializers.DateField(required=False)

    class Meta:
        model = User

        fields = (
            'username2', 'password', 'password2', 'email', 'first_name', 'last_name', 'empresa', 'direccion', 'direccion2',
            'codigo_postal', 'ciudad', 'pais', 'telefono', 'producto', 'producto_name', 'fecha_compra', 'plataforma', 'nif',
            'tipo_empresa', 'is_renewal', 'order_subscription_id', 'fecha_alta', 'fecha_baja'
        )

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        # print("VALIDATED DATA: "+ str(validated_data))
        user_is_new = False
        welcome_email = False
        vat=None
        process=None
        empresa = validated_data.get('empresa')
        telefono = validated_data.get('telefono')
        fecha_compra = validated_data['fecha_compra']
        fecha_baja = validated_data.get("fecha_baja", None)
        plataforma = validated_data.get('plataforma')
        producto_name = validated_data.get('producto_name', [])
        productos = validated_data.get('producto', [])
        nif = validated_data.get('nif')
        tipo_empresa = validated_data.get('tipo_empresa')
        client_email = validated_data.get('email')
        is_renewal = validated_data.get('is_renewal')
        order_subscription_id = validated_data.get('order_subscription_id')
        fecha_contratacion_llc = None
        fecha_contratacion_contabilidad_usa = None
        limite_facturas = 150

        try:
            print("\n\n\n\n\n\n")
            if is_renewal is not None and is_renewal == True:
                print("##############################################################################################################")
                print("# RENOVACION !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
                print("##############################################################################################################")
            else:
                print("##############################################################################################################")
                print("# NUEVA COMPRAAAAAAAA !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
                print("##############################################################################################################")
            print(f"{validated_data}")
            print("##############################################################################################################")
            print("\n\n\n\n\n\n")
        except Exception as e:
            print(f"Error al imprimir datos de la compra: {e}")

        # Si es renovacion -> Se Acabo. No se hace nada.
        if is_renewal is not None and is_renewal == True:
            raise serializers.ValidationError({"error": "Renovación de servicio. No se hace nada."})

        # tratamos de recuperar el usuario
        user_query = User.objects.filter(email__iexact=validated_data['email'])

        # funcion incrementamos el +1 del email
        def incrementar_plus_uno(email):
            pattern = re.compile(r'(.*)\+(\d+)@(.*)')
            match = pattern.match(email)
            if match is not None:
                numero = int(match.group(2)) + 1
                email = match.group(1) + '+' + str(numero) + '@' + match.group(3)
            return email

        # tratamos de recuperar el usuario y el seller, si existe, incrementamos el +1 del email
        if user_query.exists():
            user = user_query.first()
            print('user existe')
            print('user: ' + str(user))
            seller = Seller.objects.get(user=user)
            if seller is not None:
                for idx, producto in enumerate(productos):
                    if plataforma == 'gestoria':


                        if producto in ('7348','15670','16224','16226','16226',    '7692', '15671','16225','16228','16229'):
                            # Autónomo ES Alta o Mantenimiento
                            # if seller.legal_entity != 'self-employed':
                            email = validated_data['email']
                            email = incrementar_plus_uno(email)
                            validated_data['email'] = email
                            if producto in ('16224','16225','16226','16228'):
                                # Ajustar el limite de facturas
                                self.ajustar_limite_facturas(seller, producto, plataforma)

                        if producto in ('7693', '15635', '16239', '16240', '16238',     '7378', '15636', '16236', '16237', '16232'):
                            # Sociedad Limitada ES Alta o Mantenimiento
                            # if seller.legal_entity != 'sl':
                            email = validated_data['email']
                            email = incrementar_plus_uno(email)
                            validated_data['email'] = email
                            if producto in ('16236','16237','16239','16240'):
                                # Ajustar el limite de facturas
                                self.ajustar_limite_facturas(seller, producto, plataforma)

                        if producto in ('12881','15713', '20737','20738'):
                            # Ajustar el limite de facturas
                            self.ajustar_limite_facturas(seller, producto, plataforma)

                            # Ajustar la endidad si no existe una
                            seller.legal_entity = 'llc' if seller.legal_entity in ['other', None] else seller.legal_entity

                            # Ajustar la fecha de contratacion
                            if fecha_compra is not None and fecha_compra != '':
                                seller.contracted_accounting_usa_date = fecha_compra
                            else:
                                seller.contracted_accounting_usa_date = datetime.now()

                            print(f'--> {seller.limit_invoice} ** --> {seller.legal_entity} ** --> {seller.contracted_accounting_usa_date}')
                    if plataforma == 'muaytax':

                        if (
                            # IDS anteriores
                            producto in ('1479', '1478', '12613', '12614', '3180', '3181') or
                            # IDS New Mexico Premium
                            producto in ('17737','17738','17741','17742','17743','17744','17745','17746','17747','17748','17749','17750','17751','17752',
                                         '26264','26265','26266','26267','26268','26269','26270','26271','26272','26273','26274','26275','26276','26277') or
                            # IDS Wyoming Premium
                            producto in ('17803','17804','17805','17807','17806','17808','17809','17811','17810','17812','17813','17815','17814','17816',
                                         '26208','26209','26210','26211','26212','26213','26214','26215','26216','26217','26218','26219','26220','26221') or
                            # IDS Florida Premium
                            producto in ('17833','17835','17837','17839','17838','17840','17841','17843','17842','17844','17846','17848','17847','17849',
                                         '26236','26237','26238','26239','26240','26241','26242','26243','26244','26245','26246','26247','26248','26249') or
                            # IDS New Mexico Standard
                            producto in ('17721','17724','17725','17726','17727','17729','17730','17731','17732','17733','17734','17735','17736',
                                         '26250','26251','26252','26254','26255','26256','26257','26258','26259','26260','26261','26262','26263') or
                            # IDS Wyoming Standard
                            producto in ('17753','17754','17757','17759','17758','17760','17761','17763','17762','17764','17765','17767','17766','17768',
                                         '26278','26279','26280','26281','26282','26283','26284','26285','26286','26287','26288','26289','26290','26291') or
                            # IDS Florida Standard
                            producto in ('17817','17818','17821','17823','17822','17824','17825','17827','17828','17826','17829','17831','17830','17832',
                                         '26222','26223','26224','26225','26226','26227','26228','26229','26230','26231','26232','26233','26234','26235')
                        ):
                            # LLC Alta o Mantenimiento
                            # if seller.legal_entity != 'llc':
                            email = validated_data['email']
                            email = incrementar_plus_uno(email)
                            validated_data['email'] = email
                            if (
                                # IDS anteriores
                                producto in ('12613', '12614') or
                                # IDS New Mexico Premium
                                producto in ('17737','17738','17741','17742','17743','17744','17745','17746','17747','17748','17749','17750','17751','17752',
                                             '26264','26265','26266','26267','26268','26269','26270','26271','26272','26273','26274','26275','26276','26277') or
                                # IDS Wyoming Premium
                                producto in ('17803','17804','17805','17807','17806','17808','17809','17811','17810','17812','17813','17815','17814','17816', 
                                             '26208','26209','26210','26211','26212','26213','26214','26215','26216','26217','26218','26219','26220','26221') or
                                # IDS Florida Premium
                                producto in ('17833','17835','17837','17839','17838','17840','17841','17843','17842','17844','17846','17848','17847','17849',
                                             '26236','26237','26238','26239','26240','26241','26242','26243','26244','26245','26246','26247','26248','26249')
                            ):
                                seller.is_llc_premium_direction = True

                        elif producto in ['13441', '4310', '13442', '4311', '13443', '4312', '13444', '4289', '4292', '4313', '4293', '4294', '4288']:
                            # Ajustar el limite de facturas
                            self.ajustar_limite_facturas(seller, producto, plataforma)

                            # Ajustar la entidad legal si no existe una
                            seller.legal_entity = 'LLC' if seller.legal_entity in ['other', None] else seller.legal_entity

                            # Ajustar la fecha de contratacion
                            if fecha_compra is not None and fecha_compra != '':
                                seller.contracted_accounting_usa_basic_date = fecha_compra
                            else:
                                seller.contracted_accounting_usa_basic_date = datetime.now()


                seller.save()

        # recuperamos el usuario con el nuevo mail
        user_query = User.objects.filter(email__iexact=validated_data['email'])
        username2 = validated_data['username2']
        if empresa is not None and len(empresa) > 0:
            shortname = special_char_eliminate(empresa, 'lower', 'remove_spaces', 'alphanumeric')
        elif first_name is not None:
            first_name = validated_data['first_name']
            last_name = validated_data['last_name']
            shortname = special_char_eliminate(first_name + ' ' + last_name, 'lower', 'remove_spaces', 'alphanumeric')
        else:
            shortname = special_char_eliminate(username2, 'lower', 'remove_spaces', 'alphanumeric')

        # recuperamos si existe el seller que tiene ese mail como secundario
        sellers_with_email = Seller.objects.filter(valid_emails__icontains=validated_data['email'])

        # si no existe el usuario y no existe el seller con ese mail como secundario
        if not user_query.exists() and not sellers_with_email.exists():
            print('no existe user con mail ' + str(validated_data['email']))
            user_queryusername = User.objects.filter(username=shortname)
            if user_queryusername.exists():
                user = user_queryusername.first()
                seller = Seller.objects.filter(user=user).first()
                print('user existe con username ' + str(shortname))
                # Agregamos el nuevo correo como una línea adicional
                print('seller mail añadido a valid_emails: ' + str(seller))
                if seller:
                    seller.valid_emails += '\n' + validated_data['email']
                    seller.save()
                    print('recuperado user con username ' + str(user.username))


            else:
                found = False
                print('no existe user con username ' + str(shortname))
                # comprobamos si existe el usuario con esos nombres y apellidos
                user_queryname = User.objects.filter(first_name=validated_data['first_name'], last_name=validated_data['last_name'])
                if user_queryname.exists():
                    user = user_queryname.first()
                    seller = Seller.objects.filter(user=user).first()
                    if seller is not None:
                        # Agregamos el nuevo correo como una línea adicional
                        seller.valid_emails += '\n' + validated_data['email']

                        print('seller mail añadido a valid_emails: ' + str(seller))
                        seller.save()
                        print('user existe con nombre y apellidos ' + str(validated_data['first_name']) + ' ' + str(validated_data['last_name']))
                        found = True

                if not found:
                    print('no existe user con nombre y apellidos ' + str(validated_data['first_name']) + ' ' + str(validated_data['last_name']))
                    print('creando nuevo user con username ' + str(shortname))

                    user = User.objects.create(
                        username=shortname,
                        email=validated_data['email'],
                        name=validated_data['first_name'] + ' ' + validated_data['last_name'],
                        first_name=validated_data['first_name'],
                        last_name=validated_data['last_name'],
                    )
                    user.set_password(validated_data['password'])
                    user.save()
                    user_is_new = True
            pais = validated_data.get('pais')
            telefono = validated_data.get('telefono')

            if pais is not None and len(pais) > 0:
                print('pais: ' + str(pais))
                address = Address()
                address.address_name = f"Direccion {user.name}"
                address.address = validated_data.get('direccion')
                address.address_continue = validated_data.get('direccion2')
                address.address_zip = validated_data.get('codigo_postal')
                address.address_city = validated_data.get('ciudad')
                all_countries = Country.objects.filter(iso_code=validated_data.get('pais'))
                if all_countries is not None and len(all_countries) > 0:
                    country = all_countries[0]
                    address.address_country = country
                    print('country: ' + str(country))

                address.save()
            empresa = validated_data.get('empresa')
            nameSeller = ""
            surnameSeller = ""

            if empresa is not None and len(empresa) > 0:
                nameEmpresa=empresa
            else:
                nameEmpresa=user.first_name + ' ' + user.last_name

            service_cancellation_purchase_date = None
            service_registration_purchase_date = None

            if plataforma == 'gestoria':

                for idx, producto in enumerate(productos):
                    #cierre SL y autónomo
                    if producto in ('8895', '7713') and seller:
                        service_cancellation_purchase_date = datetime.now()

                    if producto in ('7348','15670','16224','16226','16226',    '7692', '15671','16225','16228','16229'):
                        legalEntity='self-employed'
                        if user:
                            nameSeller = user.first_name
                            surnameSeller = user.last_name
                        service_registration_purchase_date = datetime.now()

                    elif producto in ('7693', '15635', '16239', '16240', '16238', '7378', '15636', '16236', '16237', '16232'):
                        service_registration_purchase_date  = datetime.now()
                        legalEntity='sl'

                    elif producto in ('12881','15713','20737','20738'):
                        # Ajustar el limite de facturas
                        limite_facturas = self.ajustar_limite_facturas(seller=None, producto=producto, plataforma=plataforma)
                        # Ajustar la entidad legal si no existe una
                        legalEntity = 'llc'
                        # Otros datos
                        if user:
                            nameSeller = user.first_name
                            surnameSeller = user.last_name
                        # Ajustar la fecha de contratacion
                        if fecha_compra is not None and fecha_compra != '':
                            fecha_contratacion_contabilidad_usa = fecha_compra
                        else:
                            fecha_contratacion_contabilidad_usa = datetime.now()
                    else:
                        legalEntity='other'

            
            elif plataforma=='muaytax':
                for idx, producto in enumerate(productos):

                    if (
                            # Other LLC Services
                            producto in ('203', '3179', '6500', '9283','3180', '3181', '10911', '10912', '10913', '10868', '10869', '10870', '10348', '10349', '10350', '11283', '11284', '11285') or
                            # IDS Antiguos Mantenimiento LLC
                            producto in ('6501', '6502', '9284', '9285') or
                            # IDS New Mexico Premium
                            producto in ('18050','18051','18052','18054','18053','18055','18056','18058','18057','18059','18060','18062','18061','18063') or
                            # IDS Wyoming Premium
                            producto in ('17992','17993','17994','17996','17995','17997','17998','18000','17999','18001','18002','18004','18003','18005') or
                            # IDS Florida Premium
                            producto in ('18020','18021','18022','18024','18023','18025','18026','18028','18027','18029','18030','18033','18031','18034') or
                            # IDS New Mexico Standard
                            producto in ('18083','18035','18036','18038','18037','18039','18040','18042','18041','18043','18044','18046','18045','18047') or
                            # IDS Wyoming Standard
                            producto in ('18064','18065','18066','18068','18067','18069','18070','18072','18071','18073','18074','18076','18075','18077') or
                            # IDS Florida Standard
                            producto in ('18006','18007','18008','18010','18009','18011','18012','18014','18013','18015','18016','18018','18017','18019') or
                            # IDS Antiguos Creaciony Mantenimiento LLC
                            producto in ('1479', '1478', '12613', '12614', '3180', '3181') or
                            # IDS New Mexico Premium
                            producto in ('17737','17738','17741','17742','17743','17744','17745','17746','17747','17748','17749','17750','17751','17752',
                                         '26264','26265','26266','26267','26268','26269','26270','26271','26272','26273','26274','26275','26276','26277') or
                            # IDS Wyoming Premium
                            producto in ('17803','17804','17805','17807','17806','17808','17809','17811','17810','17812','17813','17815','17814','17816',
                                        '26208','26209','26210','26211','26212','26213','26214','26215','26216','26217','26218','26219','26220','26221') or
                            # IDS Florida Premium
                            producto in ('17833','17835','17837','17839','17838','17840','17841','17843','17842','17844','17846','17848','17847','17849',
                                         '26236','26237','26238','26239','26240','26241','26242','26243','26244','26245','26246','26247','26248','26249') or
                            # IDS New Mexico Standard
                            producto in ('17721','17724','17725','17726','17727','17729','17730','17731','17732','17733','17734','17735','17736',
                                         '26250','26251','26252','26254','26255','26256','26257','26258','26259','26260','26261','26262','26263') or
                            # IDS Wyoming Standard
                            producto in ('17753','17754','17757','17759','17758','17760','17761','17763','17762','17764','17765','17767','17766','17768',
                                         '26278','26279','26280','26281','26282','26283','26284','26285','26286','26287','26288','26289','26289','26290','26291') or
                            # IDS Florida Standard
                            producto in ('17817','17818','17821','17823','17822','17824','17825','17827','17828','17826','17829','17831','17830','17832',
                                         '26222','26223','26224','26225','26226','26227','26228','26229','26230','26231','26232','26233','26234','26235')
                    ):
                        legalEntity = 'llc'
                        if fecha_compra is not None and fecha_compra != '':
                            fecha_contratacion_llc = fecha_compra
                        else:
                            fecha_contratacion_llc = datetime.now()

                    elif producto in ['4302', '13441', '4310', '13442', '4311', '13443', '4312', '13444']:
                        # Ajustar el limite de facturas
                        limite_facturas = self.ajustar_limite_facturas(seller=None, producto=producto, plataforma=plataforma)
                        # Ajustar la entidad legal si no existe una
                        legalEntity = 'llc'
                        # Otros datos
                        nameSeller = user.first_name
                        surnameSeller = user.last_name
                        # Ajustar la fecha de contratacion
                        if fecha_compra is not None and fecha_compra != '':
                            fecha_contratacion_contabilidad_usa = fecha_compra
                        else:
                            fecha_contratacion_contabilidad_usa = datetime.now()
                    else:
                        legalEntity = 'other'

            else:
                legalEntity='other'

            # busca sellers que tengan este user y obten el primero
            user_seller=Seller.objects.filter(user=user).first()
            if user_seller is None:
                print('---------------------------------------------creando seller')
                seller = Seller.objects.create(
                    user=user,
                    name=nameEmpresa,
                    first_name=nameSeller,
                    last_name=surnameSeller,
                    legal_entity=legalEntity,
                    shortname=shortname,
                    service_cancellation_purchase_date=service_cancellation_purchase_date,
                    service_registration_purchase_date=service_registration_purchase_date,
                )
            else:
                print('----------------------------------------------recuperando seller')
                seller = user_seller

            print(f'.................selle {user_seller} con user {user}.................................es seller {seller}')

            if nif is not None and len(nif) > 0:
                if not seller.nif_registration or seller.nif_registration is None:
                    seller.nif_registration = nif
                    seller.save()

            if tipo_empresa is not None and len(tipo_empresa) > 0:
                if not seller.legal_entity or seller.legal_entity is None:
                    seller.legal_entity = tipo_empresa
                    seller.save()

            if fecha_contratacion_contabilidad_usa is not None:
                seller.contracted_accounting_usa_date = fecha_contratacion_contabilidad_usa
                seller.save()

            if fecha_contratacion_llc is not None:
                seller.contracted_maintenance_llc_date = fecha_contratacion_llc
                seller.save()

            if pais and address is not None:
                print('pais: ' + str(pais) + ' address: ' + str(address))
                seller.seller_address = address

            # if telefono is not None and len(telefono) > 0:
            #     seller.phone = validated_data['telefono']

            if limite_facturas is not None:
                seller.limit_invoice = limite_facturas

            seller.save()
            print('>>>>>>>>>>>>>>>>>>>>>>>> seller: ' + str(seller))

        # Fecha de compra
        fecha_compra = validated_data['fecha_compra']
        print('fecha_compra: ' + str(fecha_compra))
        producto_name = validated_data.get('producto_name', [])
        print('producto_name: ' + str(producto_name))

        # PRODUCTOS COMPRADOS
        productos = validated_data.get('producto', [])
        print('productos: ' + str(productos))
        if productos is not None and len(productos) > 0:

            # plataformas de compra
            plataforma = validated_data.get('plataforma')
            vat = None
            if plataforma == 'gestoria':
                for idx, producto in enumerate(productos):

                    if producto in ('7131', '7033', '15712') or producto in ('8952', '8953'):
                        # Modelo 184 (2 cuotas) y pago único
                        seller.is_184_contracted=True
                        seller.save()
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)
                        year_model = 2023 if producto == '8952' else 2024 if producto == '8953' else datetime.now().year
                        # CHECK IF EXISTS SERVICE 184
                        exist_service = Service.objects.filter(seller=seller, service_name=ServiceType.objects.get(code='model_184'), year=year_model).first()
                        if not exist_service:
                            service = self.create_service(seller, "model_184", fecha_compra, quantity=1, year=year_model)

                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para el cliente
                        self.send_email(seller, vat, process, producto, plataforma, 'España', client_email, "is_contemplado")

                    elif producto in ('7348','15670','16224','16226','16226',    '7692', '15671','16225','16228','16229'):
                        # Autónomo ES Alta (spanish freelance)
                        # Autónomo ES Mantenimiento (spanish freelance)

                        seller.legal_entity = 'self-employed'
                        # seller.contracted_accounting = True
                        # seller.contracted_accounting_date = fecha_compra
                        # seller.contracted_accounting_txt = True
                        # seller.contracted_accounting_txt_date = fecha_compra
                        seller.save()

                        vat_es = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='ES'))
                        if not vat_es:
                            vat = self.create_vat(seller, 'ES', fecha_compra, is_local=True)
                            vat.save()
                            # SellerVatActivity.objects.create(
                            #     sellervat=vat,
                            #     sellervat_activity_iae=EconomicActivity.objects.filter(
                            #         code__startswith=vat.vat_country.iso_code,
                            #         code__endswith='665',
                            #     ).first()
                            # )
                            process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        self.send_email(seller, vat, process, producto, plataforma, 'España', client_email, "is_contemplado")
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto in ('7693', '15635', '16239', '16240', '16238',    '7378', '15636', '16236', '16237', '16232'):

                        # Sociedad Limitada ES Alta (spanish company)
                        # Sociedad Limitada ES Mantenimiento (spanish company)

                        seller.legal_entity = 'sl'
                        # seller.contracted_accounting = True
                        # seller.contracted_accounting_date = fecha_compra
                        # seller.contracted_accounting_txt = True
                        # seller.contracted_accounting_txt_date = fecha_compra
                        seller.save()

                        vat_es = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='ES'))
                        if not vat_es:
                            vat = self.create_vat(seller, 'ES', fecha_compra, is_local=True)
                            vat.save()
                            process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        self.send_email(seller, vat, process, producto, plataforma, 'España', client_email, "is_contemplado")
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto in ('7568','7573'):
                        # Gestión Informes de Ventas de Amazon (UE)
                        # seller.contracted_accounting_txt = True
                        # seller.contracted_accounting_txt_date = fecha_compra
                        seller.is_amz_report = True
                        seller.amazon_sell = True
                        seller.save()

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)


                    elif producto in ('7688', '7806', '7782', '7802', '8208', '8210', '7686', '7804', '8274', '8277', '8279', '8281','8283',
                                      '15904', '15905', '15906', '15907', '1590', '15908', '15909', '15910', '15911', '15912', '15913', '15914', '15915', '15916'
                    ):
                        # Mail Productos Baja
                        sellervat = None
                        if producto == '7688' or producto == '15904':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='ES'))
                        elif producto == '7782' or producto == '15905':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='DE'))
                        elif producto == '8210' or producto == '15906':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='GB'))
                        elif producto == '7802' or producto == '15907':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='FR'))
                        elif producto == '7804' or producto == '15908':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='IT'))
                        elif producto == '7686'or producto == '15909':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='CZ'))
                        elif producto == '8208' or producto == '15910':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='PL'))
                        elif producto == '7806' or producto == '15911':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='SE'))
                        elif producto == '8283' or producto == '15912':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='NL'))
                        elif producto == '8277' or producto == '15913':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='AT'))
                        elif producto == '8281' or producto == '15914':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='JP'))
                        elif producto == '8279' or producto == '15915':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='AE'))
                        elif producto == '8274' or producto == '15916':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='US'))

                        try:
                            sellervat = sellervat[0]
                            if sellervat is not None:
                                sellervat.status_process = SellerVatStatusProcess.objects.get(code='contracted')
                                sellervat.vat_status = SellerVatStatus.objects.get(code='processing')
                                sellervat.type = SellerVatType.objects.get(code='deactivation_vat')
                                sellervat.status_process_color = SellerVatStatusProcessColor.objects.get(code='red')
                                sellervat.status_priority = 4
                                sellervat.contracting_discontinue = fecha_compra
                                sellervat.save()
                        except Exception as e:
                            print('Error al tratar de modificar los estados del sellervat dado de baja: ' + str(e))

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto in ('7714','15721',  '7360','15719',  '15720','7334'):

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto in ('7601', '7618'):

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        self.send_email(seller, vat, process, producto, plataforma, 'España', client_email, "is_contemplado")

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto in ('12881','15713','20737','20738'):
                        seller = Seller.objects.get(user=user)

                        vat = None
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)
                        time.sleep(1)
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, str(producto)+'-alt')
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                    # RAPS IN GESTORIA
                    elif producto in ('10317', '10318', '10321', '10322', '10323', '10324', '10325', '10326', '10327', '10328', '11410', '12812', '14002', '15674', '15685', '15696', '15697', '15698', '15699', '15700', '15701', '15702', '15703', '15704', '15705', '15706', '19852', '19853', '19854', '19855', '19856', '19857', '19858', '19860', '19861', '19862', '19863', '19799', '19800'):
                        vat = None
                        code =''
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        france_raps = ['10327', '10321', '10322', '10323', '10324', '10325', '10326', '10317', '10318', '10328', '11410']
                        new_france_register_raps = ['15696', '15697', '15698', '15699', '15700', '15701', '15702', '15703', '15704', '15705', '15706']
                        new_france_declaration_raps = ['19852', '19853', '19854', '19855', '19856', '19857', '19858', '19860', '19861', '19862', '19863']

                        if producto in ('12812', '15674', '19799'): # RAPS España
                            code = 'rap_spain'
                            quantity_raps=1
                        elif producto in ('14002','15685', '19800'): # RAPS Alemania
                            code = 'rap_germany'
                            quantity_raps=1
                        elif producto in france_raps or producto in new_france_register_raps or producto in new_france_declaration_raps: # RAPS Francia
                            code = 'rap_france'
                            quantity_raps = 0
                            if producto in france_raps:
                                quantity_raps = france_raps.index(producto) + 1
                            if producto in new_france_register_raps:
                                quantity_raps = new_france_register_raps.index(producto) + 1
                            if producto in new_france_declaration_raps:
                                quantity_raps = new_france_declaration_raps.index(producto) + 1

                        exist_service = Service.objects.filter(seller=seller, service_name=ServiceType.objects.get(code = code)).first()
                        if exist_service:
                            exist_service.quantity = exist_service.quantity + quantity_raps
                            exist_service.save()
                            form = ProcessedForm.objects.filter(seller = seller, json_form__icontains = f'"servicePK": "{exist_service.pk}"' ).first()
                            if form:
                                if form.is_form_processed:
                                    form.is_form_processed = False
                                    form.save()
                        else:
                            service = self.create_service(seller, code, fecha_compra, quantity=quantity_raps)

                        # Email para el cliente
                        self.send_email(seller, vat, process, producto, plataforma, '', client_email, "is_contemplado")
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto in ('8895', '7713'):
                        # Solo se asigna legal_entity_expected si el producto es '8895' o '7713'
                        legal_entity_expected = 'sl' if producto == '8895' else 'self-employed'

                        if user_is_new:
                            status = 'not-exist-user'
                        elif not seller.contracted_accounting_date:
                            status = 'missing-data'

                        else:
                            # Llamar a la función para procesar las fechas
                            status = self.process_accounting_dates(seller, fecha_baja, fecha_compra, legal_entity_expected)

                            # Guardamos los cambios en el modelo Seller
                            seller.save()
                            if fecha_baja is not None:
                                self.send_email(seller, vat, process, producto, plataforma, '', client_email, "is_contemplado") # Email información al cliente solo si ambas fechas están presentes
                        # Email para la gestoría | En función del estado
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto, status, fecha_baja, legal_entity_expected=legal_entity_expected, legal_entity_actual=seller.legal_entity)
                    else:
                        # Productos no contemplados
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    time.sleep(1)

            elif plataforma == 'amzvat':
                for idx, producto in enumerate(productos):
                    vat = None
                    if producto == '10701' or producto in ('10744', '10746') or producto in ('10745', '10747'):
                        # España
                        pais = "España"
                        vat_es = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='ES'))
                        if not vat_es:
                            vat = self.create_vat(seller, 'ES', fecha_compra)
                            vat.save()
                            if producto in ('10744', '10746'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10745', '10747'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '14596' or producto in ('14597', '14598', '14599', '14603', '14604', '14605') or producto in ('14600', '14601', '14602', '14606', '14607', '14608'):
                        # USA
                        pais = "Estados Unidos"
                        vat_us = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='US'))

                        if vat_us.filter(is_contracted=False):
                            vat = vat_us.filter(is_contracted=False).first()
                            if vat:
                                vat.vat_country = Country.objects.get(iso_code='US')
                                vat.is_contracted = True
                                vat.status_process = SellerVatStatusProcess.objects.get(code='information_requested')
                                vat.vat_status = SellerVatStatus.objects.get(code='processing')
                                vat.status_process_color = SellerVatStatusProcessColor.objects.get(code='grey')
                                vat.status_priority = 1
                                vat.status_grey_last_change_date=fecha_compra
                                vat.contracting_date = fecha_compra
                                if producto in ('14597', '14598', '14599', '14603', '14604', '14605'):
                                    vat.type = SellerVatType.objects.get(code='registration')
                                elif producto in ('14600', '14601', '14602', '14606', '14607', '14608'):
                                    vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()

                        elif not vat_us:
                            vat = self.create_vat(seller, 'US', fecha_compra)
                            vat.save()
                            if producto in ('14597', '14598', '14599', '14603', '14604', '14605'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('14600', '14601', '14602', '14606', '14607', '14608'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '18662' or producto in ('18663', '18665') or producto in ('18664', '18666'):
                        # Austria
                        pais = "Austria"
                        vat_at = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='AT'))
                        if not vat_at:
                            vat = self.create_vat(seller, 'AT', fecha_compra)
                            vat.save()
                            if producto in ('18663', '18665'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('18664', '18666'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '14591' or producto in ('14592', '14594') or producto in ('14593', '14595'):
                        # Emiratos Arabes Unidos
                        pais = "Emiratos Árabes Unidos"
                        vat_ae = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='AE'))
                        if not vat_ae:
                            vat = self.create_vat(seller, 'AE', fecha_compra)
                            vat.save()
                            if producto in ('14592', '14594'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('14593', '14595'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '14586' or producto in ('14587', '14589') or producto in ('14588', '14590'):
                        # Japon
                        pais = "Japón"
                        vat_jp = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='JP'))
                        if not vat_jp:
                            vat = self.create_vat(seller, 'JP', fecha_compra)
                            vat.save()
                            if producto in ('14587', '14589'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('14588', '14590'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '14413' or producto in ('14416', '14418') or producto in ('14417', '14419'):
                        # Suecia
                        pais = "Suecia"
                        vat_se = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='SE'))
                        if not vat_se:
                            vat = self.create_vat(seller, 'SE', fecha_compra)
                            vat.save()
                            if producto in ('14416', '14418'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('14417', '14419'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10797' or producto in ('10798', '10800') or producto in ('10799', '10801'):
                        # Alemania
                        pais = "Alemania"
                        vat_de = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='DE'))
                        if not vat_de:
                            vat = self.create_vat(seller, 'DE', fecha_compra)
                            vat.save()
                            if producto in ('10798', '10800'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10799', '10801'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10792' or producto in ('10793', '10795') or producto in ('10794', '10796'):
                        # Francia
                        pais = "Francia"
                        vat_fr = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='FR'))
                        if not vat_fr:
                            vat = self.create_vat(seller, 'FR', fecha_compra)
                            vat.save()
                            if producto in ('10793', '10795'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10794', '10796'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10787' or producto in ('10788', '10790') or producto in ('10789', '10791'):
                        # Países Bajos
                        pais = "Países Bajos"
                        vat_nl = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='NL'))
                        if not vat_nl:
                            vat = self.create_vat(seller, 'NL', fecha_compra)
                            vat.save()
                            if producto in ('10788', '10790'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10789', '10791'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10782' or producto in ('10783', '10785') or producto in ('10784', '10786'):
                        # Polonia
                        pais = "Polonia"
                        vat_pl = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='PL'))
                        if not vat_pl:
                            vat = self.create_vat(seller, 'PL', fecha_compra)
                            vat.save()
                            if producto in ('10783', '10785'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10784', '10786'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10777' or producto in ('10778', '10780') or producto in ('10779', '10781'):
                        # Reino Unido
                        pais = "Reino Unido"
                        vat_gb = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='GB'))
                        if not vat_gb:
                            vat = self.create_vat(seller, 'GB', fecha_compra)
                            vat.save()
                            if producto in ('10778', '10780'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10779', '10781'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10772' or producto in ('10773', '10775') or producto in ('10774', '10776'):
                        # República Checa
                        pais = "República Checa"
                        vat_cz = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='CZ'))
                        if not vat_cz:
                            vat = self.create_vat(seller, 'CZ', fecha_compra)
                            vat.save()
                            if producto in ('10773', '10775'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10774', '10776'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto == '10700' or producto in ('10750', '10752') or producto in ('10751', '10753'):
                        # Italia
                        pais = "Italia"
                        vat_it = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='IT'))
                        if not vat_it:
                            vat = self.create_vat(seller, 'IT', fecha_compra)
                            vat.save()
                            if producto in ('10750', '10752'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('10751', '10753'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto in ('33080 ', '33082') or producto in ('33081', '33083'):
                        # Irlanda
                        pais = "Irlanda"
                        vat_ie = SellerVat.objects.filter(seller=seller, vat_country=Country.objects.get(iso_code='IE'))
                        if not vat_ie:
                            vat = self.create_vat(seller, 'IE', fecha_compra)
                            vat.save()
                            if producto in ('33080 ', '33082'):
                                vat.type = SellerVatType.objects.get(code='registration')
                                vat.save()
                            elif producto in ('33081', '33083'):
                                vat.type = SellerVatType.objects.get(code='transfer')
                                vat.save()
                    elif producto in ('29732', '29733', '29734', '29735', '29736', '29737', '29738', '29739', '29740', '29741', '29742', '29743', '29744'):
                        # Mail Productos Baja
                        sellervat = None
                        if producto == '29732':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='ES'))
                        elif producto == '29733':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='DE'))
                        elif producto == '29734':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='GB'))
                        elif producto == '29735':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='FR'))
                        elif producto == '29736':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='IT'))
                        elif producto == '29737':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='CZ'))
                        elif producto == '29738':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='PL'))
                        elif producto == '29739':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='SE'))
                        elif producto == '29740':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='NL'))
                        elif producto == '29741':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='AT'))
                        elif producto == '29742':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='JP'))
                        elif producto == '29743':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='AE'))
                        elif producto == '29744':
                            sellervat = SellerVat.objects.filter(seller=seller,vat_country=Country.objects.get(iso_code='US'))

                        try:
                            sellervat = sellervat[0]
                            if sellervat is not None:
                                sellervat.status_process = SellerVatStatusProcess.objects.get(code='contracted')
                                sellervat.vat_status = SellerVatStatus.objects.get(code='processing')
                                sellervat.type = SellerVatType.objects.get(code='deactivation_vat')
                                sellervat.status_process_color = SellerVatStatusProcessColor.objects.get(code='red')
                                sellervat.status_priority = 4
                                sellervat.contracting_discontinue = fecha_compra
                                sellervat.save()
                        except Exception as e:
                            print('Error al tratar de modificar los estados del sellervat dado de baja: ' + str(e))

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)
                                        # RAPS
                    elif producto in ('29774','29775','29776','29777','29778','29779','29780','29781','29782','29783','29784','29752','29753','29754','29755','29756','29757','29758','29759','29760','29761','29762', '29763','29764','29765','29766','29767','29768','29769','29770','29771','29772','29773'):
                            vat = None
                            code =''
                            process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                            france_raps = ['29774','29775','29776','29777','29778','29779','29780','29781','29782','29783','29784']
                            spain_raps  = ['29752','29753','29754','29755','29756','29757','29758','29759','29760','29761','29762']
                            germany_raps = ['29763','29764','29765','29766','29767','29768','29769','29770','29771','29772','29773']

                            if producto in spain_raps: # RAPS España
                                code = 'rap_spain'
                                quantity_raps=0
                                if producto in spain_raps:
                                    quantity_raps = spain_raps.index(producto) + 1

                            elif producto in germany_raps: # RAPS Alemania
                                code = 'rap_germany'
                                quantity_raps=0
                                if producto in germany_raps:
                                    quantity_raps = germany_raps.index(producto) + 1

                            elif producto in france_raps: # RAPS Francia
                                code = 'rap_france'
                                quantity_raps = 0
                                if producto in france_raps:
                                    quantity_raps = france_raps.index(producto) + 1

                            exist_service = Service.objects.filter(seller=seller, service_name=ServiceType.objects.get(code = code)).first()
                            if exist_service:
                                exist_service.quantity = exist_service.quantity + quantity_raps
                                exist_service.save()
                                form = ProcessedForm.objects.filter(seller = seller, json_form__icontains = f'"servicePK": "{exist_service.pk}"' ).first()
                                if form:
                                    if form.is_form_processed:
                                        form.is_form_processed = False
                                        form.save()
                            else:
                                service = self.create_service(seller, code, fecha_compra, quantity=quantity_raps)

                            # Email para el cliente
                            self.send_email(seller, vat, process, producto, plataforma, '', client_email, "is_contemplado")
                            if user_is_new and not welcome_email:
                                welcome_email = True
                                self.send_welcome_email(seller, user, process, plataforma)

                            # Email para la gestoría
                            self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)
                    #Lista completa de productos Gestoría IVA, para añadirles la fecha
                    elif producto in ('10744','10746','10745','10747','33080','33082','33081','33083','14592','14594','14593','14595','14587','14589','14588','14590','18663','18665','18664','18666','14416','14418','14417','14419','10798','10800','10799','10801','10793','10795','10794','10796','10788','10790','10789','10791','10783','10785','10784','10786','10778','10780','10779','10781','10773','10775','10774','10776','10750','10752','10751','10753','10701','33079','14591','14586','18662','14413','10797','10792','10787','10782','10777','10772','10700','33097','29751','29752','29753','29754','29755','29756','29757','29758','29759','29760','29761','29762','29763','29764','29765','29766','29767','29768','29769','29770','29771','29772','29773','29774','29775','29776','29777','29778','29779','29780','29781','29782','29783','29784','29745','29731','29747','29746','29748'):
                        seller.contracted_maintenance_llc_date = timezone.now()
                        seller.save()
                    else:
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                    # en la ultima pasada del bucle enviamos a una funcion nueva que envie el email con todos los productos de la compra
                    if vat is not None and idx == len(productos) - 1:
                        self.send_email_amz(seller, process, productos, client_email, "is_contemplado")

                    if user_is_new and not welcome_email:
                        welcome_email = True
                        self.send_welcome_email(seller, user, process, plataforma)

                    time.sleep(1)

            elif plataforma == 'muaytax':

                for idx, producto in enumerate(productos):

                    if (
                        # IDS anteriores NewMexico y Wyoming
                        producto in ('1479', '1478', '12613', '12614', '3180', '3181') or
                        # IDS New Mexico Premium
                        producto in ('17737','17738','17741','17742','17743','17744','17745','17746','17747','17748','17749','17750','17751','17752',
                                     '26264','26265','26266','26267','26268','26269','26270','26271','26272','26273','26274','26275','26276','26277') or
                        # IDS Wyoming Premium
                        producto in ('17803','17804','17805','17807','17806','17808','17809','17811','17810','17812','17813','17815','17814','17816',
                                     '26208','26209','26210','26211','26212','26213','26214','26215','26216','26217','26218','26219','26220','26221') or
                        # IDS Florida Premium
                        producto in ('17833','17835','17837','17839','17838','17840','17841','17843','17842','17844','17846','17848','17847','17849',
                                     '26236','26237','26238','26239','26240','26241','26242','26243','26244','26245','26246','26247','26248','26249') or
                        # IDS New Mexico Standard
                        producto in ('17721','17724','17725','17726','17727','17729','17730','17731','17732','17733','17734','17735','17736',
                                     '26250','26251','26252','26254','26255','26256','26257','26258','26259','26260','26261','26262','26263') or
                        # IDS Wyoming Standard
                        producto in ('17753','17754','17757','17759','17758','17760','17761','17763','17762','17764','17765','17767','17766','17768',
                                     '26278','26279','26280','26281','26282','26283','26284','26285','26286','26287','26288','26289','26289','26290','26291') or
                        # IDS Florida Standard
                        producto in ('17817','17818','17821','17823','17822','17824','17825','17827','17828','17826','17829','17831','17830','17832',
                                     '26222','26223','26224','26225','26226','26227','26228','26229','26230','26231','26232','26233','26234','26235')
                    ):
                        # Servicio LLC «all-inclusive» | Pro
                        # Servicio LLC «all-inclusive» – Multi-member

                        vat = None
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        self.send_email(seller, vat, process, producto, plataforma, 'Estados Unidos', client_email, "is_contemplado")
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                    elif (
                        # IDS Antiguos
                        producto in ('6501', '6502', '9284', '9285') or
                        # IDS New Mexico Premium
                        producto in ('18050','18051','18052','18054','18053','18055','18056','18058','18057','18059','18060','18062','18061','18063') or
                        # IDS Wyoming Premium
                        producto in ('17992','17993','17994','17996','17995','17997','17998','18000','17999','18001','18002','18004','18003','18005') or
                        # IDS Florida Premium
                        producto in ('18020','18021','18022','18024','18023','18025','18026','18028','18027','18029','18030','18033','18031','18034') or
                        # IDS New Mexico Standard
                        producto in ('18083','18035','18036','18038','18037','18039','18040','18042','18041','18043','18044','18046','18045','18047') or
                        # IDS Wyoming Standard
                        producto in ('18064','18065','18066','18068','18067','18069','18070','18072','18071','18073','18074','18076','18075','18077') or
                        # IDS Florida Standard
                        producto in ('18006','18007','18008','18010','18009','18011','18012','18014','18013','18015','18016','18018','18017','18019')
                    ):
                        # Servicio mantenimiento LLC «all-inclusive» | Pro
                        # Servicio mantenimiento LLC «all-inclusive» (multi-member) | Pro

                        seller.contracted_maintenance_llc= True
                        seller.contracted_maintenance_llc_date = fecha_compra
                        seller.save()

                        vat = None
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        self.send_email(seller, vat, process, producto, plataforma, 'Estados Unidos', client_email, "is_contemplado")

                    elif producto == '5986':
                        seller = Seller.objects.get(user=user)

                        vat = None
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        self.send_email(seller, vat, process, producto, plataforma, 'Alemania', client_email, "is_contemplado")
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                    elif producto in ('11062', '11066', '6631', '7453', '1498') or producto in ('17238', '17237', '17235', '17234', '17233', '17232','17231', '17230') or producto in ('13351', '13352', '5806', '17174', '17175', '17176', '17177') or producto in ('25757', '25758', '25759', '25760', '25761', '25762', '25763', '25764', '25765'):

                        #El "-1" indica que tiene transacciones ilimitadas
                        form5472_1120 = {
                            '11062': ['model_54721120', 2023], '6631': ['model_54721120', 2022], '17230': ['model_54721120', 2021],
                            '17232': ['model_54721120', 2022], '17234': ['model_54721120', 2023], '17238': ['model_54721120', 2024],
                            '11066': ['model_54721120_limited', 2023], '17237': ['model_54721120_limited', 2024],
                            '17235': ['model_54721120_limited', 2023], '17233': ['model_54721120_limited', 2022],
                            '17231': ['model_54721120_limited', 2021],
                            # IDs de cierre LLC - Añadir servicio 5472-1120 completo (no limitado)
                            '13351': ['model_54721120', 2024], # Cierre de LLC NM (2024)
                            '13352': ['model_54721120', 2024], # Cierre de LLC Wyoming (2024)
                            '5806': ['model_54721120', 2024],  # Cierre LLC 2024
                            # Servicio de Cierre de LLC
                            '17174': ['model_54721120', datetime.now().year],
                            '17175': ['model_54721120', datetime.now().year],
                            '17176': ['model_54721120', datetime.now().year],
                            '17177': ['model_54721120', datetime.now().year],
                            '25757': ['model_54721120', datetime.now().year],
                            '25758': ['model_54721120', datetime.now().year],
                            '25759': ['model_54721120', datetime.now().year],
                            '25760': ['model_54721120', datetime.now().year],
                            '25761': ['model_54721120', datetime.now().year],
                            '25762': ['model_54721120', datetime.now().year],
                            '25763': ['model_54721120', datetime.now().year],
                            '25764': ['model_54721120', datetime.now().year],
                            '25765': ['model_54721120', datetime.now().year],
                        }

                        if form5472_1120.get(producto):
                            self.create_service(
                                seller,
                                service_code=form5472_1120.get(producto)[0],
                                fecha_compra=fecha_compra,
                                year=form5472_1120.get(producto)[1]
                            )
                            seller.is_5472_1120_inactive_contracted = True if form5472_1120.get(producto)[0] == 'model_54721120_limited' else False
                            seller.save()

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        self.send_email(seller, vat, process, producto, plataforma, 'Estados Unidos', client_email, "is_contemplado")

                        # Productos no contemplados
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                        # Mail Productos '11062' y '11066'
                        # message = render_to_string("emails/api_email_product_11062_11066.html", {
                        #     'user': user,
                        #     'process': process,
                        #     'seller': seller,
                        #     'logo_head_muaytax': logo_url_head_muaytax()
                        # })
                        # subject = 'Notificación FORM 5472-1120'
                        # text_content = 'Notificación FORM 5472-1120'
                        # from_email = '<EMAIL>'
                        # mail = client_email
                        # to_email = [mail]
                        # bcc_email = ['<EMAIL>', '<EMAIL>']
                        # reply_to = ['<EMAIL>']
                        # html_content = message
                        # tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
                        # tracked_email.attach_alternative(html_content, "text/html")
                        # resp = tracked_email.send()
                        # print('Email Form 5472-1120 Contracted: ' + str(resp))

                    elif producto == '11034':
                        seller.is_boir_contracted = True
                        seller.save()

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    elif producto == '13089':
                        seller.is_b15_contracted = True
                        seller.save()

                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para usarios
                        self.send_email(seller, vat, process, producto, plataforma, 'Estados Unidos', client_email, "is_contemplado")

                    elif producto in ('6987', '7529') or producto in ('17166','17167'):

                        vat = None
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)
                        self.send_email(seller, vat, process, producto, plataforma, None, client_email, "is_contemplado")

                        # Email para el cliente
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                    # MANTENIMIENTO LLC - Full list
                    elif producto in (
                        '21107', '17991', '17992', '17993', '17994', '17995', '17996', '17997', '17998', '17999',
                        '18000', '18001', '18002', '18003', '18004', '18005', '18006', '18007', '18008', '18009',
                        '18010', '18011', '18012', '18013', '18014', '18015', '18016', '18017', '18018', '18019',
                        '18020', '18021', '18022', '18023', '18024', '18025', '18026', '18027', '18028', '18029',
                        '18030', '18031', '18033', '18034', '18083', '18035', '18036', '18037', '18038', '18039',
                        '18040', '18041', '18042', '18043', '18044', '18045', '18046', '18047', '18050', '18051',
                        '18052', '18053', '18054', '18055', '18056', '18057', '18058', '18059', '18060', '18061',
                        '18062', '18063', '18064', '18065', '18066', '18067', '18068', '18069', '18070', '18071',
                        '18072', '18073', '18074', '18075', '18076', '18077', '17719', '17803', '17804', '17805',
                        '17806', '17807', '17808', '17809', '17810', '17811', '17812', '17813', '17814', '17815',
                        '17816', '17817', '17818', '17821', '17822', '17823', '17824', '17825', '17826', '17827',
                        '17828', '17829', '17830', '17831', '17832', '17833', '17835', '17837', '17838', '17839',
                        '17840', '17841', '17842', '17843', '17844', '17846', '17847', '17848', '17849', '17721',
                        '17724', '17725', '17726', '17727', '17728', '17729', '17730', '17731', '17732', '17733',
                        '17734', '17735', '17736', '17737', '17738', '17741', '17742', '17743', '17744', '17745',
                        '17746', '17747', '17748', '17749', '17750', '17751', '17752', '17753', '17754', '17757',
                        '17758', '17759', '17760', '17761', '17762', '17763', '17764', '17765', '17766', '17767',
                        '17768', '11283', '11284', '11285', '10868', '10869', '10870', '10348', '10349', '10350',
                        '9283', '9284', '9285', '6500', '6501', '6502','26208', '26209', '26210', '26211', '26212',
                        '26218', '26219', '26220', '26221', '26222', '26223', '26224', '26225', '26226', '26227',
                        '26228', '26229', '26230', '26231', '26232', '26233', '26234', '26235', '26236', '26237',
                        '26238', '26239', '26240', '26241', '26242', '26243', '26244', '26245', '26246', '26247',
                        '26248', '26249', '26250', '26251', '26252', '26253', '26254', '26255', '26256', '26257',
                        '26258', '26259', '26260', '26261', '26262', '26263', '26264', '26265', '26266', '26267',
                        '26268', '26269', '26270', '26271', '26272', '26273', '26274', '26275', '26276', '26277',
                        '26278', '26279', '26280', '26281', '26282', '26283', '26284', '26285', '26286', '26287',
                        '26288', '26289', '26290', '26291', '26213', '26214', '26215', '26216', '26217', '26207',
                    ):
                        if seller is not None:
                            seller.service_llc_registration_purchase_date = timezone.now()
                            seller.save()

                    # CIERRE LLC - Full list
                    elif producto in (
                        '21112', '17174', '17175', '17176', '17177', '13352', '13351'
                    ):
                        if seller is not None:
                            seller.service_llc_cancellation_purchase_date = timezone.now()
                            seller.save()
                    else:
                        process = self.create_process(seller, fecha_compra, plataforma, producto_name[idx], producto, vat)

                        # Email para el cliente
                        if user_is_new and not welcome_email:
                            welcome_email = True
                            self.send_welcome_email(seller, user, process, plataforma)

                        # Email para la gestoría
                        # Productos no contemplados
                        self.send_email_to_management(user, str(producto_name[idx]), empresa, telefono, fecha_compra, plataforma, producto)

                    time.sleep(1)

        return user

    def send_welcome_email(self, seller, user, process, plataforma):
        signature = ''
        signature = get_mail_signature_plataform(plataforma)
        logo_url = logo_url_head_muaytax()

        message = render_to_string("emails/api_email_bienvenida.html", {
            'user': user,
            'seller': seller,
            'mail_signature': signature,
            'logo_head_muaytax': logo_url
        })
        subject = 'Notificación Alta Aplicación MUAY TAX'
        text_content = 'Notificación Alta Aplicación MUAY TAX'
        from_email = signature['email']
        mail = user.email
        to_email = [mail]

        bcc_email = ['<EMAIL>', '<EMAIL>']
        reply_to = [signature['email']]
        html_content = message
        tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
        tracked_email.attach_alternative(html_content, "text/html")
        resp = tracked_email.send()
        print('Email bienvenida : ' + str(resp))

        try:
            # Enviar email recordatorio de facturas si aplica
            self.send_invoice_reminder_email(user, logo_url)
        except Exception as e:
            print(f"Error enviando recordatorio de facturas a {user.email}: {e}")

    def send_invoice_reminder_email(self, user, logo_url):
        """
        Enviar email de recordatorio para subir facturas.
        """
        current_date = datetime.now()
        current_day = current_date.day
        current_month = current_date.month

        if not (1 <= current_day <= 10):
            return  # Enviar email si current_day esta entre el día 1 y 10 del mes

        username = f" {user.first_name}" if user.first_name else ""

        month = {
            1: 'ENERO', 2: 'FEBRERO', 3: 'MARZO',
            4: 'ABRIL', 5: 'MAYO', 6: 'JUNIO',
            7: 'JULIO', 8: 'AGOSTO', 9: 'SEPTIEMBRE',
            10: 'OCTUBRE', 11: 'NOVIEMBRE', 12: 'DICIEMBRE'
        }

        lower_month= month[current_month].lower()
        message = render_to_string("emails/scheduled_emails/monthly_general_sl_self-employed.html", {
            'username': username,
            'day': "10 de "+ lower_month,
            'logo_head_muaytax': logo_url
        })
        subject = 'Solicitud de Información Contable'
        text_content = 'Solicitud de Información Contable'
        from_email = '<EMAIL>'
        to_email = [user.email]
        reply_to = ["<EMAIL>"]

        bcc_email = ['<EMAIL>']
        html_content = message
        tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email,  reply_to=reply_to, bcc=bcc_email)
        tracked_email.attach_alternative(html_content, "text/html")
        resp = tracked_email.send()

    def send_email_to_management(self, user, producto_name, empresa, tlf, fecha_compra, plataforma, producto, status=None, fecha_baja=None, **kwargs):
        baja_iva = False
        baja_tax_agency = False
        legal_entity_expected = kwargs.get('legal_entity_expected')
        legal_entity_actual = kwargs.get('legal_entity_actual')

        if plataforma == 'gestoria':

            # Obtención del certificado digital
            if producto in ('7714', '15721',  '7360','15719',  '15720','7334'):
                subject = 'Nuevo pedido - Gestoría IVA'
                text_content = 'Nuevo pedido - Gestoría IVA - ' + producto_name
                to_email = '<EMAIL>'

            # Gestión para la baja de número IVA
            elif (  producto in ('7688', '7806', '7782', '7802', '8202', '8210', '7686', '7804', '8274', '8277', '8279', '8281','8283') or
                    producto in ('15904', '15905', '15906', '15907', '1590', '15908', '15909', '15910', '15911', '15912', '15913', '15914', '15915', '15916') or
                    producto in ('29733','29741','29732','29735','29736','29740','29738','29734','29737','29739','29744','29743','29742')
            ):
                subject = 'Nuevo pedido baja - Gestoría ES'
                text_content = 'Nuevo pedido - Gestoría ES - ' + producto_name
                to_email = '<EMAIL>'
                baja_iva = True

            # Mail Gestión Informes de Ventas de Amazon (UE)
            elif producto in ('7568', '7596', '10501'):
                subject = 'Nuevo pedido - Gestoría ES'
                text_content = 'Nuevo pedido - Gestoría ES - ' + producto_name
                to_email = '<EMAIL>'

            # Servicio Declaracion RAPS Francia
            elif producto in ('10327','10321','10322','10323','10324','10325','10326','10317','10318','10328') or producto in ('15696', '15697', '15698', '15699', '15700', '15701', '15702', '15703', '15704', '15705', '15706') or producto in ('19852', '19853', '19854', '19855', '19856', '19857', '19858', '19860', '19861', '19862', '19863'):
                subject = 'Nuevo pedido - Gestoría IVA'
                text_content = 'Nuevo pedido - Gestoría IVA - ' + producto_name
                to_email = '<EMAIL>'

            elif producto in('12881','12881-alt') or producto in ('15713','15713-alt') or producto in ('20737','20737-alt','20738','20738-alt'):
                subject = 'Nuevo pedido - Gestoría'
                text_content = 'Nuevo pedido - Gestoría - ' + producto_name
                to_email = '<EMAIL>'
                if producto in ('12881-alt', '15713-alt','20737-alt','20738-alt'):
                    to_email = '<EMAIL>'

            elif producto in ('7601', '7618'):
                subject = 'Nuevo pedido - Gestoría IVA'
                text_content = 'Nuevo pedido - Gestoría IVA - ' + producto_name
                to_email = '<EMAIL>'

            # Aquí es donde se manejan 'missing-data', 'success', y 'not-exist-seller'
            elif producto in ('8895', '7713'):
                if status == 'missing-data':
                    subject = 'Falta información para el proceso'
                    text_content = f"El proceso no puede continuar porque faltan el datos de alta para la contabilidad España del vendedor {empresa} con teléfono {tlf}. Fecha de compra: {fecha_compra}."
                    to_email = '<EMAIL>'

                elif status == 'missing-end-date':
                    subject = 'Falta información para el proceso'
                    text_content = f"El proceso no puede continuar porque falta el datos de baja de hacienda {empresa} con teléfono {tlf}. Fecha de compra: {fecha_compra}."
                    to_email = '<EMAIL>'

                elif status == 'update-success-without-inconsistency':
                    subject = 'Baja en Hacienda exitosa'
                    text_content = f"El proceso de baja en para la contabilidad Esapaña de la empresa {empresa} ha sido exitoso, pero se detectó que no se ha pasado una fecha de baja en hacienda. "
                    to_email = '<EMAIL>'

                elif status == 'update-success-with-inconsistency':
                    subject = 'Baja en Hacienda con inconsistencias'
                    text_content = (
                        f"El proceso de baja en Hacienda para la empresa {empresa} ha sido exitoso, pero se detectó una inconsistencia en la entidad legal. "
                        f"Se compró el producto asociado a una entidad '{'sociedad limitada' if legal_entity_expected == 'sl' else 'autónomo'}', "
                        f"pero la entidad registrada en el sistema es '{'sociedad limitada' if legal_entity_actual == 'sl' else 'autónomo'}'. "
                        f"Fecha de compra: {fecha_compra}."
                    )
                    to_email = '<EMAIL>'

                elif status == 'not-exist-user':
                    subject = 'Vendedor no encontrado'
                    text_content = f"No se ha encontrado el vendedor con la información proporcionada: {empresa}, {tlf}. Fecha de compra: {fecha_compra}."
                    to_email = '<EMAIL>'
                baja_tax_agency = True
            else:
                # Mail Productos no contemplados
                subject = 'Nuevo pedido - Gestoría ES'
                text_content = 'Nuevo pedido - Gestoría ES - ' + producto_name
                to_email = '<EMAIL>'

        elif plataforma == 'amzvat':
            subject = 'Nuevo pedido - Gestoría IVA'
            text_content = 'Nuevo pedido - Gestoría IVA - ' + producto_name
            to_email = '<EMAIL>'

        elif plataforma == 'muaytax':
            subject = 'Nuevo pedido - Gestoría EEUU'
            text_content = 'Nuevo pedido - Gestoría EEUU - ' + producto_name
            to_email = '<EMAIL>'

        # Añadir prefijo basado en las tres primeras letras de la tienda [27-02-25]
        prefixed_product_code = f"{plataforma[:3].upper()}-{producto}"
        store_product = StoreProduct.objects.filter(code=prefixed_product_code).first()

        if store_product:
            producto_name = store_product.description

        message = render_to_string("emails/api_email_to_management.html", {
            'producto_name': producto_name,
            'user': user,
            'empresa': empresa,
            'tlf': tlf,
            'fecha_compra': fecha_compra,
            'fecha_baja': fecha_baja,
            'plataforma': plataforma,
            'producto': producto,
            'baja_iva': baja_iva,
            'baja_tax_agency': baja_tax_agency,
            'status': status,
            'logo_head_muaytax': logo_url_head_muaytax()
        })

        subject = subject
        text_content = text_content
        to_email = ['<EMAIL>'] if not settings.IS_PRODUCTION else [to_email]
        bcc_email = ['<EMAIL>', '<EMAIL>']
        reply_to = [to_email]
        from_email = to_email
        html_content = message
        tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to,
                                            bcc=bcc_email)
        tracked_email.attach_alternative(html_content, "text/html")
        resp = tracked_email.send()
        print(f'Email to {to_email} -  {str(producto_name)}  : {str(resp)}')

    def send_email_amz(self, seller, process, productos, client_email, *args):

        if productos is not None:
            other = False
            country_product = False

            contemplated_products = [ "10799", "10801", "14593", "14595", "10779", "10781", "10751", "10753", "10794", "10796"]

            # Comprobar si hay algún producto comprado que no esté contemplado en la lista
            for producto in productos:
                if producto not in contemplated_products:
                    other = True
                if producto in contemplated_products:
                    country_product = True

            if country_product == True:
                other = False

            # Array de países que coinciden con los productos comprados
            array_countries = []
            for producto in productos:
                if producto in ("10799", "10801" ):
                    array_countries.append('Alemania')
                if producto in ("14593", "14595"):
                    array_countries.append('Emiratos Árabes Unidos')
                if producto in ("10779", "10781" ):
                    array_countries.append('Reino Unido')
                if producto in ("10751", "10753"):
                    array_countries.append('Italia')
                if producto in ("10794", "10796"):
                    array_countries.append('Francia')

            array_countries = list(set(array_countries))

            if array_countries != [] and len(array_countries) > 1:
                if array_countries[-1] == 'Italia':
                    array_countries.insert(-1, 'e')
                else:
                    array_countries.insert(-1, 'y')

            countries = f"{array_countries}".replace("[", "").replace("]", "").replace("'", "").replace(", y,", " y").replace(", e,", " e")

            user = seller.user
            mail = client_email
            to_email = [mail]

            # Verificar si se ha pasado "is_contemplado" en *args
            is_contemplado = "is_contemplado" in args
            # Verificar si el destinatario es el cliente
            if to_email == [client_email] and is_contemplado:
                # Llamar a la función para obtener el mensaje adecuado
                message_contact = get_contact_message_for_product(platform='amzvat', product_id=productos)
            else:
                message_contact = None  # Si no es el cliente o no es un ID contemplado, no es necesario

            bcc_email = ['<EMAIL>', '<EMAIL>']
            reply_to = ['<EMAIL>']
            message = render_to_string("emails/api_email_amzvat_header.html", {
                'user': user,
                'productos': productos,
                'other': other,
                'process': process,
                'seller': seller,
                'countries': countries,
                'message_contact': message_contact,
                'logo_head_amzvat': logo_url_head_amzvat()
            }) # EL logo es AMZVAT

            subject = 'AMZVAT - Documentación necesaria IVA '
            text_content = 'Documentación necesaria IVA '
            from_email = '<EMAIL>'
            html_content = message
            tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to,
                                                bcc=bcc_email)
            tracked_email.attach_alternative(html_content, "text/html")
            resp = tracked_email.send()
            print('Email Pais IVA : ' + str(resp) + ' producto: ' + str(productos))

    def send_email(self, seller, vat, process, productoid, plataforma, pais, client_email, *args):
        print(f'===============================================send_email_api_productID: {productoid}')

        # Function | Gestionar el envío del correo y posibles errores
        def send_email_message(subject, text_content, html_content, from_email, to_email, reply_to, bcc_email, excel_file_path=None):
            try:
                email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
                email.attach_alternative(html_content, "text/html")
                if excel_file_path:
                    email.attach_file(excel_file_path)
                response = email.send()
                print(f'Email enviado: {subject}, Respuesta: {response}')
            except Exception as e:
                print(f'Error al enviar el correo: {e}')
                # Enviar un correo con el <NAME_EMAIL>
                error_subject = f"Error en el envío de correo: {subject}"
                error_message = f"Se produjo un error al intentar enviar un correo.\n\nDetalles del error:\n{str(e)}"
                error_email = EmailMultiAlternatives(
                    error_subject,
                    error_message,
                    from_email,
                    ['<EMAIL>']
                )
                error_email.send()
                print('Error <NAME_EMAIL>')

        if productoid:
            # Configuración básica del correo
            user = seller.user
            subject = ''
            text_content = ''
            message = ''
            excel_file_path = None
            from_email = '<EMAIL>'
            to_email = ['<EMAIL>'] if not settings.IS_PRODUCTION else [client_email]
            bcc_email = ['<EMAIL>', '<EMAIL>']
            reply_to = ['<EMAIL>']

            # Firma General
            signature = get_mail_signature_plataform(plataforma)

            email_sent = False  # Bandera para evitar mensajes duplicados

            # Conjunto para rastrear los bloques de correos ya enviados
            sent_email_blocks = set()

            # Dic | Agrupación por IDs para Gestoría
            gestoria_ids = {
                'sl_is_high': ['7693', '15635', '16239', '16240', '16238'],
                'sl_is_maintenance': ['7378', '15636', '16236', '16237', '16232'],
                'employed_high': ['7348', '15670', '16224', '16226', '16226'],
                'employed_maintenance': ['7692', '15671', '16225', '16228', '16229'],
                'notificacion_model100': ['7601', '7618'],
                'lucid': ['10317', '10318', '10321', '10322', '10323', '10324', '10325', '10326', '10327', '10328', '11410', '12812', '14002', '15674', '15685', '15696', '15697', '15698', '15699', '15700', '15701', '15702', '15703', '15704', '15705', '15706', '19852', '19853', '19854', '19855', '19856', '19857', '19858', '19860', '19861', '19862', '19863', '19799', '19800'],
                'model_184': ['7131', '7033', '15712', '8952', '8953'],
            }

            # Dic | Agrupación por IDs para Muaytax
            muaytax_ids = {
                'llc_unique_high': ['1478', '1479', '12613', '12614','17737','17738','17721','177240','17803','17804','17753','17754','17833','17835','17817','17818','26208',
                                    '26209','26222','26223','26236','26237','26250','26264','26265','26278','26279'],
                'llc_unique_maintenance': ['6501','6502','10349','10350','18050','18051','18083','18035','17992','17993','18064','18065','18020','18021','18006','18007'],
                'llc_multi_high': [
                    '17737','17738','17741','17743','17742','17744','17745','17747','17746','17748','17749','17751','17750','17752','17721','17724','17725','17727',
                    '17730','17726','17729','17731','17730','17732','17733','17735','17734','17736','17803','17804','17805','17807','17806','17808','17809','17811',
                    '17810','17812','17813','17815','17814','17816','17753','17754','17757','17759','17758','17760','17761','17763','17762','17764','17765','17767',
                    '17766','17768','17833','17835','17837','17839','17838','17840','17841','17843','17842','17844','17846','17848','17847','17849','17817','17818',
                    '17821','17823','17822','17824','17825','17827','17828','17826','17829','17831','17830','17832','3180', '3181','26208','26209','26210','26211','26212',
                    '26213','26214','26215','26216','26217','26218','26219','26220','26221','26222','26223','26224','26225','26226','26227','26228','26229','26230','26231',
                    '26232','26233','26234','26235','26236','26237','26238','26239','26240','26241','26242','26243','26244','26245','26246','26247','26248','26249','26250',
                    '26251','26252','26254','26255','26256','26257','26258','26259','26260','26261','26262','26263','26264','26265','26266','26267','26268','26269','26270',
                    '26271','26272','26273','26274','26275','26276','26277','26278','26279','26280','26281','26282','26283','26284','26285','26286','26287','26288','26289',
                    '26290','26291'
                ],
                'llc_multi_maintenance': [
                    '9284', '9285',
                    '18052','18054','18053','18055','18056','18058','18057','18059','18060','18062','18061','18063',
                    '18036','18038','18037','18039','18040','18042','18041','18043','18044','18046','18045','18047',
                    '17994','17996','17995','17997','17998','18000','17999','18001','18002','18004','18003','18005',
                    '18066','18068','18067','18069','18070','18072','18071','18073','18074','18076','18075','18077',
                    '18022','18024','18023','18025','18026','18028','18027','18029','18030','18033','18031','18034',
                    '18008','18010','18009','18011','18012','18014','18013','18015','18016','18018','18017','18019'
                ],
                'be15': ['13089'],
                'tax_advice': ['6987', '7529', '17166', '17167'],
                'obsolete_product': ['5986'],
                '5472': ['17229', '17230' , '17231', '17232', '17233', '17234', '17235', '17238', '17237'],
            }

            # Verificar si se ha pasado "is_contemplado" en *args
            is_contemplado = "is_contemplado" in args
            # Verificar si el destinatario es el cliente
            if to_email == [client_email] and is_contemplado:
                # Llamar a la función para obtener el mensaje adecuado
                is_rap_or_lucid = productoid in gestoria_ids['lucid']
                message_contact = get_contact_message_for_product(platform=plataforma, product_id=productoid, is_rap_or_lucid=is_rap_or_lucid)

            else:
                message_contact = None  # Si no es el cliente o no es un ID contemplado, no es necesario

            if plataforma == 'gestoria':

                if productoid in gestoria_ids['sl_is_high'] and 'sl_is_high' not in sent_email_blocks:  # Sociedad Limitada ES Alta
                    sent_email_blocks.add('sl_is_high')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='registration')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Incorporación SL en España'
                    reply_to = ['<EMAIL>']
                    text_content = 'Documentación necesaria Incorporación SL en España'
                    signature = get_mail_signature_muaytax() # Firma particular
                    context = {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'mail_signature': signature,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    }

                    # Renderizar el mensaje con el contexto
                    message = render_to_string("emails/api_email_sl_es_alta.html", context)

                    excel_file_path = '/app/muaytax/static/assets/excel/DATOS+Incorporacion+SL.xlsx'
                    email_sent = True

                elif productoid in gestoria_ids['sl_is_maintenance'] and 'sl_is_maintenance' not in sent_email_blocks and not email_sent:  # Sociedad Limitada ES Mantenimiento
                    sent_email_blocks.add('sl_is_maintenance')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='transfer')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Migración SL en España'
                    reply_to = ['<EMAIL>']
                    text_content = 'Documentación necesaria Migración SL en España'
                    signature = get_mail_signature_muaytax() # Firma particular
                    context = {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'mail_signature': signature,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    }
                    # Renderizar el mensaje con el contexto
                    message = render_to_string("emails/api_email_sl_es_migracion.html", context)
                    excel_file_path = '/app/muaytax/static/assets/excel/DATOS+Incorporacion+SL.xlsx'
                    email_sent = True

                elif productoid in gestoria_ids['employed_high'] and 'employed_high' not in sent_email_blocks and not email_sent:  # Autónomo Alta
                    sent_email_blocks.add('employed_high')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='registration')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Alta Autónomo en España'
                    reply_to = ['<EMAIL>']
                    text_content = 'Documentación necesaria Alta Autónomo en España'
                    signature = get_mail_signature_muaytax() # Firma particular
                    context = {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'mail_signature': signature,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    }
                    # Renderizar el mensaje con el contexto
                    message = render_to_string("emails/api_email_autonomo_es_alta.html", context)
                    excel_file_path = '/app/muaytax/static/assets/excel/Alta+Autonomo.xlsx'
                    email_sent = True

                elif productoid in gestoria_ids['employed_maintenance'] and 'employed_maintenance' not in sent_email_blocks and not email_sent:  # Autónomo Mantenimiento
                    sent_email_blocks.add('employed_maintenance')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='transfer')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Migración Autónomo en España'
                    reply_to = ['<EMAIL>']
                    text_content = 'Documentación necesaria Migración Autónomo en España'
                    signature = get_mail_signature_muaytax() # Firma particular
                    context = {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'mail_signature': signature,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    }
                    # Renderizar el mensaje con el contexto
                    message = render_to_string("emails/api_email_autonomo_es_migracion.html", context)
                    email_sent = True

                elif productoid in gestoria_ids['notificacion_model100'] and 'notificacion_model100' not in sent_email_blocks and not email_sent:  # Notificación Modelo 100
                    sent_email_blocks.add('notificacion_model100')
                    signature = get_mail_signature_plataform(plataforma)
                    subject = 'MUAYTAX - Notificación Modelo 100'
                    text_content = 'MUAYTAX - Notificación Modelo 100'
                    message = render_to_string("emails/notifications/notification_MOD_100_2023.html", {
                        'seller': seller,
                        'mail_signature': signature,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    from_email = signature['email']
                    reply_to = [signature['email']]
                    email_sent = True

                elif productoid in gestoria_ids['lucid'] and 'lucid' not in sent_email_blocks and not email_sent:  # Envío de Plantilla lucid
                    sent_email_blocks.add('lucid')
                    france_raps = ['10327', '10321', '10322', '10323', '10324', '10325', '10326', '10317', '10318', '10328', '11410']
                    new_france_register_raps = ['15696', '15697', '15698', '15699', '15700', '15701', '15702', '15703', '15704', '15705', '15706']
                    new_france_declaration_raps = ['19852', '19853', '19854', '19855', '19856', '19857', '19858', '19860', '19861', '19862', '19863']
                    france = productoid in france_raps or productoid in new_france_register_raps or productoid in new_france_declaration_raps
                    subject = 'Muaytax - Registro LUCID'
                    text_content = 'Muaytax - Registro LUCID'
                    message = render_to_string("emails/api_email_lucid.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'producto': productoid,
                        'france': france,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    from_email = '<EMAIL>'
                    reply_to = ['<EMAIL>']
                    email_sent = True

                elif productoid in gestoria_ids['sl_and_employed_is_end_date_tax'] and 'sl_and_employed_is_end_date_tax' not in sent_email_blocks and not email_sent:  # SL o Autonomo baja hacienda
                    sent_email_blocks.add('sl_and_employed_is_end_date_tax')

                    subject = 'MUAYTAX - Notificacion'
                    text_content = 'Notificar la baja y las acciones faltantes requeridas'
                    message = render_to_string("emails/notifications/sl_and_employed_is_end_date_tax.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'contracted_accounting_end_date': seller.contracted_accounting_end_date,
                        'end_date_tax': seller.tax_agency_accounting_end_date,
                        'plataforma': plataforma,
                        'producto': productoid,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    from_email = '<EMAIL>' if not settings.IS_PRODUCTION else client_email
                    reply_to = ['<EMAIL>']
                    email_sent = True

                elif productoid in gestoria_ids['model_184'] and 'model_184' not in sent_email_blocks and not email_sent:
                    sent_email_blocks.add('model_184')
                    subject = 'Muaytax - Modelo 184'
                    text_content = 'Muaytax - Modelo 184'
                    message = render_to_string("emails/api_email_buy_m184.html", {
                        'user': user,
                        'seller': seller,
                        'company_info': get_mail_signature_muaytax(),
                        'logo_head_muaytax':logo_url_head_muaytax(),
                        'logo_head_amzvat':logo_url_head_amzvat()
                    })
                    from_email = '<EMAIL>'
                    reply_to = ['<EMAIL>']
                    email_sent = True


            elif plataforma == 'muaytax':

                if productoid in muaytax_ids['llc_unique_high'] and 'llc_unique_high' not in sent_email_blocks:  # Servicio LLC «all-inclusive» | Pro
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='registration')
                        vat.save()
                    vat.save() if vat else None
                    subject = 'MUAYTAX - Documentación necesaria Incorporación LLC'
                    text_content = 'Proceso Incorporación LLC (Acción necesaria)'
                    message = render_to_string("emails/api_email_llc_sole_alta.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    email_sent = True

                elif productoid in muaytax_ids['llc_unique_maintenance'] and 'llc_unique_maintenance' not in sent_email_blocks and not email_sent:  # Servicio mantenimiento LLC «all-inclusive» | Pro
                    sent_email_blocks.add('llc_unique_maintenance')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='transfer')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Mantenimiento LLC'
                    text_content = 'Proceso Mantenimiento LLC (Acción necesaria)'
                    message = render_to_string("emails/api_email_llc_sole_migracion.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    email_sent = True

                elif productoid in muaytax_ids['llc_multi_high'] and 'llc_multi_high' not in sent_email_blocks and not email_sent:  # Servicio LLC «all-inclusive» – Multi-member
                    sent_email_blocks.add('llc_multi_high')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='registration')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Incorporación LLC'
                    text_content = 'Proceso Incorporación LLC (Acción necesaria)'
                    message = render_to_string("emails/api_email_llc_multi_alta.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    email_sent = True

                elif productoid in muaytax_ids['llc_multi_maintenance'] and 'llc_multi_maintenance' not in sent_email_blocks and not email_sent:  # Servicio mantenimiento LLC «all-inclusive» (multi-member) | Pro
                    sent_email_blocks.add('llc_multi_maintenance')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='transfer')
                        vat.save()
                    subject = 'MUAYTAX - Documentación necesaria Mantenimiento LLC'
                    text_content = 'Proceso Mantenimiento LLC (Acción necesaria)'
                    message = render_to_string("emails/api_email_llc_multi_migracion.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    email_sent = True

                elif productoid in muaytax_ids['be15'] and 'be15' not in sent_email_blocks and not email_sent:  # Notificación Modelo be15
                    sent_email_blocks.add('be15')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='transfer')
                        vat.save()
                    subject = 'MUAYTAX - IMPORTANTE | IRS Presentación Form BE-15'
                    text_content = 'MUAYTAX - IMPORTANTE | IRS Presentación Form BE-15'
                    message = render_to_string("emails/api_email_be15.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    email_sent = True

                elif productoid in muaytax_ids['5472'] and '5472' not in sent_email_blocks and not email_sent:  # Notificación Modelo 5472
                    sent_email_blocks.add('5472')
                    if vat is not None:
                        vat.type = SellerVatType.objects.get(code='transfer')
                        vat.save()
                    subject = 'MUAYTAX - IMPORTANTE | IRS Presentación Form 5472-1120'
                    text_content = 'MUAYTAX - IMPORTANTE | IRS Presentación Form 5472-1120'
                    message = render_to_string("emails/api_email_buy_5472.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_cabecera_email': logo_url_head_muaytax(),
                        'company_info': get_mail_signature_usa(),
                    })
                    email_sent = False

                elif productoid in muaytax_ids['tax_advice'] and 'tax_advice' not in sent_email_blocks and not email_sent:  # Envío de plantilla bienvenida asesoramiento fiscal
                    sent_email_blocks.add('tax_advice')
                    subject = 'Muaytax - Asesoramiento Fiscal'
                    text_content = 'Muaytax - Asesoramiento Fiscal'
                    message = render_to_string("emails/api_email_welcome_tax_advice.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    from_email = '<EMAIL>'
                    reply_to = ['<EMAIL>']
                    email_sent = True

                elif productoid in muaytax_ids['obsolete_product'] and 'obsolete_product' not in sent_email_blocks and not email_sent:  # Producto en desuso, pero se mantiene la plantilla
                    sent_email_blocks.add('obsolete_product')
                    subject = 'Muaytax - Registro LUCID'
                    text_content = 'Muaytax - Registro LUCID'
                    message = render_to_string("emails/api_email_lucid.html", {
                        'user': user,
                        'process': process,
                        'seller': seller,
                        'logo_head_muaytax': logo_url_head_muaytax(),
                        'message_contact': message_contact
                    })
                    from_email = '<EMAIL>'
                    reply_to = ['<EMAIL>']
                    email_sent = True

            # Enviar correo | si está configurado correctamente
            if email_sent:
                html_content = message
                send_email_message(subject, text_content, html_content, from_email, to_email, reply_to, bcc_email, excel_file_path)

                # Añadir la fecha de envío a la lista de fechas de envío de email
                if process:
                    if not process.email_sent_dates:
                        process.email_sent_dates = []
                    process.email_sent_dates.append(datetime.now())

    # Actualiza las fechas de alta y baja en Hacienda y la consistencia de la entidad legal
    def process_accounting_dates(self, seller, baja_hacienda, fecha_compra, legal_entity_expected):

        try:
            seller.service_registration_purchase_date = fecha_compra
            if baja_hacienda:
                seller.service_cancellation_purchase_date = baja_hacienda
        except Exception as e:
            print(f"Error al actualizar fechas de servicio: {str(e)}")

        seller.contracted_accounting_end_date = fecha_compra

        # Validar si falta la fecha de baja
        if baja_hacienda is None:
            return 'missing-end-date'

        # Actualizar las fechas de alta y baja en Hacienda
        if seller.tax_agency_accounting_date:
            seller.tax_agency_accounting_end_date = baja_hacienda
        else:
            seller.tax_agency_accounting_date = seller.contracted_accounting_date
            seller.tax_agency_accounting_end_date = baja_hacienda

        # Verificar y/o actualizar legal_entity
        if not seller.legal_entity:
            seller.legal_entity = legal_entity_expected  # Asignamos el tipo de entidad esperado
            return 'update-success-without-inconsistency'  # Todo correcto y actualizado
        elif seller.legal_entity != legal_entity_expected:
            return 'update-success-with-inconsistency'  # Inconsistencia entre la entidad y el producto
        else:
            return 'update-success-without-inconsistency'  # Todo correcto y actualizado

    def ajustar_limite_facturas(self, seller, producto, plataforma):
        # Lista de tipos de limites para las facturas
        types = {
            'XXS': 5,
            'XS': 15,
            'S': 39,
            'M': 99,
            'L': 199,
            'XL': 499,
            'XXL': 999,
        }

        # Agrupación de productos por tipo para gestoria
        productos_gestoria = {
            'XXS': ['16224', '16225', '16226', '16228', '16236', '16237', '16239', '16240'],
            'S': ['12881', '15713', '20737','20738'],
        }

        # Agrupación de productos por tipo para muaytax
        productos_muaytax = {
            'S': ['13441', '4310', '4288'],
            'M': ['13442', '4311', '4289'],
            'L': ['13443', '4312', '4292'],
            'XL': ['13444', '4313', '4293'],
            'XXL': ['4294'],
        }

        # Construcción de límite gestoria a partir de grupos
        limites_facturas_gestoria = {
            producto: types[tipo]
            for tipo, productos in productos_gestoria.items()
            for producto in productos
        }

        # Construcción de límite muaytax a partir de grupos
        limites_facturas_muaytax = {
            producto: types[tipo]
            for tipo, productos in productos_muaytax.items()
            for producto in productos
        }

        if seller is None:
            if plataforma == 'gestoria' and producto in limites_facturas_gestoria:
                return limites_facturas_gestoria[producto]

            elif plataforma == 'muaytax' and producto in limites_facturas_muaytax:
                return limites_facturas_muaytax[producto]
        else:
            if plataforma == 'gestoria' and producto in limites_facturas_gestoria:
                seller.limit_invoice = limites_facturas_gestoria[producto]
                seller.save()
                return seller.limit_invoice

            elif plataforma == 'muaytax' and producto in limites_facturas_muaytax:
                seller.limit_invoice = limites_facturas_muaytax[producto]
                seller.save()
                return seller.limit_invoice

    def create_process(self, seller, fecha_compra, plataforma, product_name, product_id, vat=None):
        process_data = {
            'seller': seller,
            'contracting_date': fecha_compra,
            'platform': plataforma,
            'product_name': product_name,
            'product_id': product_id
        }

        if vat and vat is not None:
            process_data['seller_vat'] = vat

        process = SellerProcess.objects.create(**process_data)
        return process

    def create_vat(self, seller, iso_code, fecha_compra, is_local=None):
        vat_country = Country.objects.get(iso_code=iso_code)
        status_process = SellerVatStatusProcess.objects.get(code='information_requested')
        vat_status = SellerVatStatus.objects.get(code='processing')
        status_process_color = SellerVatStatusProcessColor.objects.get(code='grey')

        vat_data = {
            'seller': seller,
            'vat_country': vat_country,
            'is_contracted': True,
            'status_process': status_process,
            'vat_status': vat_status,
            'status_process_color': status_process_color,
            'status_priority': 1,
            'status_grey_last_change_date': fecha_compra,
            'contracting_date': fecha_compra,
        }

        if is_local is not None:
            vat_data['is_local'] = is_local

        vat = SellerVat.objects.create(**vat_data)
        return vat

    def create_service(self, seller, service_code, fecha_compra, quantity=1, year=None):
        service_type = ServiceType.objects.get(code=service_code)
        service = Service.objects.create(
            seller=seller,
            service_name=service_type,
            contracting_date=fecha_compra,
            quantity=quantity,
            year=year
        )
        return service

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):

    @classmethod
    def get_token(cls, user):
        token = super(MyTokenObtainPairSerializer, cls).get_token(user)

        # Add custom claims
        token['username'] = user.username
        return token

class TestMail(View):
    slug_url_kwarg = 'plataform'

    def get(self, request, *args, **kwargs):
        '''Vista para enviar correos de prueba'''


        print(self.kwargs["plataform"])
        print(self.kwargs["product"])
        platform = self.kwargs["plataform"]
        productos = self.kwargs["product"]
        seller = Seller.objects.get(shortname='')
        user = seller.user
        producto_name = 'Producto testing'
        empresa = 'Empresa testing'
        tlf = '123456789'
        fecha_compra = '2021-09-01'
        plataforma = platform
        producto = productos


        return HttpResponse('correos enviados')