from django.utils.translation import gettext_lazy as _

WORKER_TYPE = [
            ("1", _("Empleado")),
            ("2", _("Administrador con situación laboral")),
            ("3", _("Administrador sin situación laboral")),
        ]

CONTRACT_TYPE = [
            ("1", "Contrato o relación de carácter general, que comprenderá todas las situaciones no contempladas en los códigos numéricos siguientes."),
            ("2", "Contrato o relación de duración inferior al año, con excepción de los supuestos contemplados en el código 4."),
            ("3", "Contrato o relación laboral especial de carácter dependiente, con excepción de los rendimientos obtenidos por los penados en las instituciones penitenciarias y de las relaciones laborales de carácter especial que afecten a discapacitados, que se considerarán comprendidos en el código 1."),
            ("4", "Relación esporádica propia de los trabajadores manuales que perciben sus retribuciones por peonadas o jornales diarios, a que se refiere la regla 2.ª del artículo 83.2 del Reglamento del Impuesto."),
        ]
    

MARITAL_STATUS = [
            ("1", _("Si el perceptor es soltero, viudo, divorciado o separado legalmente con hijos menores de 18 años o mayores incapacitados sujetos a patria potestad prorrogada o rehabilitada, que conviven exclusivamente con él, siempre que tenga, al menos, un hijo o descendiente con derecho a la aplicación del mínimo por descendientes a que se refiere el artículo 58 de la Ley del Impuesto.")),
            ("2", _("Si el perceptor está casado y no separado legalmente y su cónyuge no tiene rentas anuales superiores a la cuantía a la que se refiere la situación 2ª de las contempladas en el artículo 81.1 del Reglamento del Impuesto.")),
            ("3", _("Si la situación familiar del perceptor es distinta de las anteriores o no deseó manifestarla ante la persona o entidad retenedora.")),
        ]

DISSABILITY = [
            ("0", _("Si el perceptor no padece ninguna discapacidad o si, padeciéndola, el grado de minusvalía es inferior al 33 por 100")),
            ("1", _("Si el grado de minusvalía del perceptor es igual o superior al 33 por 100 e inferior al 65 por 100.")),
            ("2", _("Si el grado de minusvalía del perceptor es igual o superior al 33 por 100 e inferior al 65 por 100, siempre que, además, acredite necesitar ayuda de terceras personas o movilidad reducida.")),
            ("3", _("Si el grado de minusvalía del perceptor es igual o superior al 65 por 100")),
        ]

IS_TITULAR = [
            ("1", _("Si el perceptor es el titular de la unidad de convivencia.")),
            ("2", _("Si el perceptor no es el titular de la unidad de convivencia. En este caso, deberá cumplimentarse obligatoriamente el campo “NIF del titular de la unidad de convivencia")),
        ]