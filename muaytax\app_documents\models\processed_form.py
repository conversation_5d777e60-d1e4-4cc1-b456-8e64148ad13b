import datetime
from django.db import models
from django.db.models.signals import pre_save
from django.contrib.postgres.fields import J<PERSON><PERSON>ield

from django.dispatch import receiver
from muaytax.signals import disable_for_load_data

class ProcessedForm(models.Model):

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="form_seller",
        verbose_name="Vendedor",
    )

    category_form = models.ForeignKey(
        "dictionaries.TypeForm",
        on_delete=models.CASCADE,
        related_name="category_form",
        verbose_name="Categoría del formulario",
    )

    json_form = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON con los campos del formulario",
    )
    
    is_form_processed = models.BooleanField(
        default=False,
        verbose_name="Formulario procesado",
    )

    is_partial_opening = models.BooleanField(
        default=False,
        verbose_name="Formulario IVA procesado Parcialmente",
    )

    manager_validation = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON con las validaciones del gestor",
    )

    re_open_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Fecha de reapertura del formulario", 
    )

    year = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Año",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Formulario procesado"
        verbose_name_plural = "Formularios procesados"

    def __str__(self):
        return f"{self.category_form} - {self.year}"

@receiver(pre_save, sender=ProcessedForm)
@disable_for_load_data
def pre_save_processed_form(sender, instance, **kwargs):
    if instance.pk:
        try:
            previous = ProcessedForm.objects.get(pk=instance.pk)
        except ProcessedForm.DoesNotExist:
            previous = None
        if previous:
            if previous.is_form_processed and not instance.is_form_processed:
                instance.re_open_date = datetime.datetime.now()