from django.db import models

class RapCategory(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=150,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=150,
        verbose_name="Descripción"
    )

    country = models.CharField(
        max_length=2,
        verbose_name="País del RAP",
    )


    class Meta:
        verbose_name = "Categoría de RAP"
        verbose_name_plural = "Categorías de RAP"

    def __str__(self):
        return self.description