from django.db import models

class OperationType(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    class Meta:
        verbose_name = "Tipo de Operación"
        verbose_name_plural = "Tipos de Operaciones"
    
    def __str__(self):
        return self.description
    
# @admin.register(OperationType)
# class OperationTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
