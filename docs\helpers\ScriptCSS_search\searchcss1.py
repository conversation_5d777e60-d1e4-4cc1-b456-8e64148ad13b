import os
import re

# === CONFIGURACIÓN ===
BASE_DIR = os.path.dirname(__file__)
CSS_FILE = os.path.join(BASE_DIR, "allstyle.css")
OUTPUT_USED = os.path.join(BASE_DIR, "style_formater.css")
OUTPUT_UNUSED = os.path.join(BASE_DIR, "style_restos.css")

TEMPLATE_PATHS = [
    "muaytax/templates/sellers/seller_vat_request_service_iva.html",
    "muaytax/templates/sellers/seller_vat_review_manager_service_iva.html"
]

INCLUDE_DIR = "muaytax/templates/sellers/include/service_iva"

# Agregar todos los templates de la carpeta INCLUDE_DIR
for file in os.listdir(INCLUDE_DIR):
    if file.endswith(".html"):
        TEMPLATE_PATHS.append(os.path.join(INCLUDE_DIR, file))

# === FUNCIONES ===

def load_templates(paths):
    content = ""
    for path in paths:
        with open(path, "r", encoding="utf-8") as f:
            content += f.read().lower()
    return content

def split_css_blocks(css_text):
    pattern = r"([^{]+)\{([^}]+)\}"
    return re.findall(pattern, css_text, re.MULTILINE)

def is_selector_used(selector, template_content):
    selector = selector.strip()
    selector = re.sub(r":\w+", "", selector)  # elimina pseudoclases como :hover, :after
    if selector.startswith("."):
        return selector[1:] in template_content
    elif selector.startswith("#"):
        return selector[1:] in template_content
    elif re.match(r"^[a-zA-Z0-9_-]+$", selector):  # elemento HTML
        return selector in template_content
    else:
        return False

def classify_blocks(blocks, template_content):
    used_blocks = []
    unused_blocks = []
    for selector, rules in blocks:
        selectors = [s.strip() for s in selector.split(",")]
        if any(is_selector_used(s, template_content) for s in selectors):
            used_blocks.append((selector, rules))
        else:
            unused_blocks.append((selector, rules))
    return used_blocks, unused_blocks

def write_css_file(path, blocks, title):
    with open(path, "w", encoding="utf-8") as f:
        f.write(f"/* {title} */\n\n")
        for selector, rules in blocks:
            f.write(f"{selector} {{\n{rules.strip()}\n}}\n\n")

# === EJECUCIÓN ===

with open(CSS_FILE, "r", encoding="utf-8") as f:
    css_content = f.read()

template_text = load_templates(TEMPLATE_PATHS)
css_blocks = split_css_blocks(css_content)

used, unused = classify_blocks(css_blocks, template_text)

write_css_file(OUTPUT_USED, used, "Bloques CSS utilizados o críticos")
write_css_file(OUTPUT_UNUSED, unused, "Bloques CSS no detectados en templates")

print(f"✔️  Resultado generado:")
print(f"   - {OUTPUT_USED}")
print(f"   - {OUTPUT_UNUSED}")
