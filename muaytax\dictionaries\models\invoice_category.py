from django.db import models

class InvoiceCategory(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    class Meta:
        verbose_name = "Categoria de la factura"
        verbose_name_plural = "Categoria de las facturas"
    
    def __str__(self):
        return self.description
    
# @admin.register(InvoiceCategory)
# class InvoiceCategoryAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
