{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}
Gestoría España
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/all/all.min-v5.15.4.css"> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    fieldset.form-borders {
      border: 1px groove #838383 !important;
      padding: 0 1.4em 1.4em 1.4em !important;
      margin: 0 0 1.5em 0 !important;
      -webkit-box-shadow:  0px 0px 0px 0px #000;
      box-shadow:  0px 0px 0px 0px #000;
    }
    legend.form-borders {
      text-align: left !important;
      width:inherit; /* Or auto */
      padding:0 10px; /* To give a bit of padding on the left and right */
      border-bottom: none;
      float: unset !important;
    }


    #list-table td span {
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }

    .dataTables_filter {
      display: none;
    }
    .head_name{
       width: 40%;
    }

    .pendings{
      width: 9%;
    }
    .login{
        width: 5%;
    }
    .actions{
        width: 5%;
    }
    #showcolumns{
      padding: 10px 10px;
    }
    .modal-size{
      max-width: 80%;
    }
    .list-causes-warning{
      list-style-type: circle;
    }
     .card{
      max-height: 73%;
    }
     .row-cards{
      margin-bottom: -25px;
    }
    .error-title{
      color: #d11507;
      font-weight: bold;
    }

  </style>
{% endblock stylesheets %}
{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">Gestoría España</h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
          </li>
          <li class="breadcrumb-item">
            <a href=".">Gestoría España</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}
{% block content %}
<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
        <div class="dt-responsive">
          <fieldset class="form-borders">
            <legend class="form-borders">Filtros <i class="fa-solid fa-filter"></i></legend>
            <div class="row d-flex mt-3">
              <!-- Search + Pagination -->
              <div class="col-4 d-flex justify-content-center align-items-start">
                <div class="input-group">
                  <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()" />
                </div>
              </div>
              <!-- Search + Pagination -->
              <div class="col-2">
                <select class="form-select form-control" name="period-input" id="period" onchange="onChangePeriodYear()">
                  <option value="0A">Anual</option>
                  <option value="Q1">Trimestre 1</option>
                  <option value="Q2">Trimestre 2</option>
                  <option value="Q3">Trimestre 3</option>
                  <option value="Q4">Trimestre 4</option>
                </select>
              </div>
              <div class="col-2">
                <select class="form-select form-control" name="year-input" id="year" onchange="onChangePeriodYear()">
                  <option value="2022">2022</option>
                  <option value="2023">2023</option>
                  <option value="2024">2024</option>
                  <option value="2025">2025</option>
                </select>
              </div>
              <div class="col-2">
                <select class="form-select form-control" name="entity-input" id="entity" onchange="onChangePeriodYear()">
                  <option value="all" selected>SL/Autonomos</option>
                  <option value="sl">SL</option>
                  <option value="self-employed">Autonomos</option>
                </select>
              </div>
              <div class="col-2 d-flex  align-items-center">
                <p class="mx-0">
                  <button id="showcolumns" class="btn btn-secondary m-0"  onclick="onClickButtonModelsTxT()" disabled>
                    <i class="fa-regular fa-eye"></i>
                    <b id="text_loading">Cargando...</b>
                  </button>
                </p>
                <p class="mx-1">
                  <button id="showcolumns1" class="btn btn-info m-0" id="infoModal" data-bs-toggle="modal" data-bs-target="#modal" >
                    <i class="fa-regular fa-circle-question fa-lg"></i>
                    <b>Iconos </b>
                  </button>
                </p>
              </div>
            </div>
          </fieldset>
          <!-- Total pending invoices -->
          <div class="row row-cards">
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL FACTURAS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b id="total_invoices_count">&nbsp</b></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS REQUERIDOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="pending-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-file-contract " style="color: #FF0000;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="revision-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-regular fa-clock fa-sm" style="color: #7c7d7e;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS RECHAZADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="disagreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-xmark" style="color: #FE8330;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS ACEPTADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="agreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-check" style="color: #ffd700;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS PRESENTADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="presented-model-count">&nbsp</b>&nbsp&nbsp<i class="fas fa-check-double" style="color: #02c018;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="id_table1" style="display: block">
            <table id="seller-list-table1" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th style="width: 80%;">Nombre</th>
                  <th>Pendientes</th>
                  <th>Último acceso</th>
                  <th>Acciones</th>
                </tr>
              </thead>
              <tbody>
              </tbody>
            </table>
          </div>
          <div id="id_table2" style="display: none">
            <table id="seller-list-table2" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th>Nombre</th>
                  <th>Pendientes</th>
                  <th style="display: none">Priority min</th>
                  <th style="display: none">Priority avg</th>
                  <th style="display:">M-111</th>
                  <th style="display:">M-115</th>
                  <th style="display:">M-130</th>
                  <th style="display:">M-303</th>
                  <th style="display:">M-309</th>
                  <th style="display:">M-349</th>
                  <th style="display:">M-369</th>
                  <th style="display:">Enero</th>
                  <th style="display:">Febrero</th>
                  <th style="display:">Marzo</th>
                  <th style="display:">Abril</th>
                  <th style="display:">Mayo</th>
                  <th style="display:">Junio</th>
                  <th style="display:">Julio</th>
                  <th style="display:">Agosto</th>
                  <th style="display:">Septiembre</th>
                  <th style="display:">Octubre</th>
                  <th style="display:">Noviembre</th>
                  <th style="display:">Diciembre</th>
                  <th >Último acceso</th>
                  <th style="width:5%;">Acciones</th>
                </tr>
              </thead>
              <tbody>
              </tbody>
            </table>
            <!-- Modal Info-->
            <div class="modal fade " id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
              <div class="modal-dialog modal-dialog-centered modal-lg modal-size" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">Información sobre los iconos del listado</h5>
                  </div>
                    <div class="modal-body">
                      {% comment %} <div class="col form-group form-check p-3">
                        <p> <b> <h4 style= "text-align: center;">¿Está seguro que desea eliminar las facturas seleccionadas?</b></h4></p>
                      </div> {% endcomment %}
                      <div class="row d-flex align-items-center">
                        <div class= "col">
                          <ul>
                            <li><p><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i> <b class="icon-text-size">&nbsp;Modelo requerido</b></li></p>
                            <li><p><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i> <b class="icon-text-size">&nbsp;Modelo pendiente de confirmar</b></li></p>
                            <li><p><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i> <b class="icon-text-size">&nbsp;Modelo rechazado por el cliente</b></p></li>
                            <li><p><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i> <b class="icon-text-size">&nbsp;Modelo aceptado por el cliente</b></p></li>
                          </ul>
                        </div>
                        <div class= "col">
                          <ul>
                            <li><p><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i> <b class="icon-text-size">&nbsp;Modelo presentado</b></p></li>
                            <li><p><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Modelo NO requerido</b></p></li>
                          </ul>
                        </div>
                      </div>
                      <br>
                      <p style="font-size:20px; color: #FF0000;"><b>Códigos de errores XX:</b></p>
                      <div class="col " style="margin-left: 20px;" >
                        <b>MODELO 369:</b>
                        <ul class="list-causes-warning">
                          <li> <span class="error-title">09:</span> OSS &rarr; NO/Desconocido y tiene facturas de tipo OSS</li>
                        </ul>
                      </div>
                      <div class="col " style="margin-left: 20px;" >
                        <b>MODELO 111:</b>
                        <ul class="list-causes-warning">
                          <li> <span class="error-title">13:</span> Nóminas societarias contratadas y sin contabilizar</li>
                        </ul>
                      </div>
                    </div>
                  <div class="modal-footer d-flex justify-content-center">
                      <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">Cerrar</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
{% block javascripts %}
<!-- JQUERY DATATABLES -->
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
<script>
let showModelTXT = false;

const debug = true;
const ajaxData = (d) => {
    let tParams = "";
    let year = document.getElementById("year").value;
    let period = document.getElementById("period").value;
    let entity = document.getElementById("entity").value;
    if (year) {
        d.year = year
        tParams += "&year=" + year;
    }
    if (period) {
        d.period = period
        tParams += "&period=" + period;
    }
    if (entity) {
        d.entity = entity
        tParams += "&entity=" + entity;
    }
    getTotals(tParams);
    return d
}

let dataTable1 = null;
let dataTable2 = null;

window.onload = function () {
    let periodSelect = document.getElementById("period");
    let yearSelect = document.getElementById("year");

    let periodTxt = periodSelect.value;
    let yearTxt = yearSelect.value;
    let cookie;

    function firstDataTable() {
      return new Promise((resolve, reject) => {
        // Tabla de management sin modelos ni txt
        dataTable1 = $("#seller-list-table1").DataTable({
          "serverSide": false,
          "ajax": {
              "dataSrc": "data",
              "url": "{% url 'app_sellers:seller_management_ES_list_not_model_txt' %}", "data": function (d) {
                  ajaxData(d);
              }
          },
          "language": {
              "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
              "lengthMenu": "_MENU_",
              "zeroRecords": "No se han encontrado vendedores.",
              "info": "_START_ a _END_ de un total de _TOTAL_",
              "search": "Buscar:",
              "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
              "infoFiltered": ""
          },
          "columns": [{
              "data": "user_name", "className": "head_name", "render": function (data, type, row) {
                  let html = '';
                  html += '<td class="align-middle">';
                  html += '<div class="d-inline-block">';
                  html += '<h6 class="m-b-0"><b>';

                  let name = row.seller_name;
                  if (typeof name === 'string') {
                      const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
                      const words = row.seller_name.split(' ').map(function (word) {
                          const lowerWord = word.toLowerCase();
                          if (lowerCaseSuffixes.includes(lowerWord)) {
                              return word.toUpperCase();
                          } else {
                              return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                          }
                      });
                      html += words.join(' ');
                  }
                  html += '</b>';
                  if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                      html += ' - ' + row.user_name.split(' ').map(function (word) {
                          return word.charAt(0).toUpperCase() + word.slice(1);
                      }).join(' ');
                  }

                  html += '</h6>';
                  html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                  html += '</div>';
                  html += '</td>';

                  return html;
              }
          }, {
              "data": "num_pending_invoices",
              "className": "pendings",
              "render": function (data, type, row) {
                  if (data && (type == 'display' || type == 'filter')) {
                    let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                    html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                    html += '<div class="progress" style="height: 15px;">';
                    html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                    html += '</div>';
                    html += '</td>';
                    return html;
                  }
                  return data;
              }
          }, {
              "data": "last_login", "className": "login", "render": function (data, type, row) {
                if (data && (type === 'display' || type === 'filter')) {
                    const date = new Date(data);

                    const day = date.getDate().toString().padStart(2, '0');
                    const month = date.toLocaleString('default', { month: 'short' });
                    const year = date.getFullYear();
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');

                    const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

                    return formattedDate;
                }
                  return data; // Para otros tipos, como 'sort'
              }
          }, {
              "data": null, "className": "actions", "orderable": false, "render": function (data, type, row) {
                  let html = '<td class="align-middle text-center">';
                  html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                  html += '<i class="feather icon-edit"></i>';
                  html += '</a>';
                  html += '</td>';
                  html = '<div style="text-align: center;">' + html + '</div>';
                  return html;
              },
          }],
          "paging": true,
          "searching": true,
          "lengthChange": false,
          "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
          "createdRow": function (row, data, dataIndex) {
              const shortname = data.shortname;
              const link = '/sellers/' + shortname + '/';
              $(row).attr('style', 'cursor: pointer;');
              $(row).attr('onclick', "window.location.href = '" + link + "';");
          },
          "initComplete": function (settings, json) {
              dataTable1.settings()[0].nTBody.style.width = "100%";
              dataTable1.settings()[0].nTable.style.width = "100%";
              dataTable1.settings()[0].nTHead.style.width = "100%";
          },
          "drawCallback": function (settings) {
              dataTable1.settings()[0].nTable.style.width = "100%";
              const period = "{{period}}";
              $('th.txt, td.txt').hide();
              $(`th.txt.${period}, td.txt.${period}`).show();
          }
        });
        resolve();
      });
    }

    function secondDataTable() {
      return new Promise((resolve, reject) => {
        // Tabla de management con modelo y txt
        dataTable2 = $("#seller-list-table2").DataTable({
          "serverSide": false,
          "ajax": {
              "dataSrc": "data", "url": "{% url 'app_sellers:seller_management_ES_list' %}", "data": function (d) {
                  ajaxData(d);
              }
          },
          "language": {
              "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
              "lengthMenu": "_MENU_",
              "zeroRecords": "No se han encontrado vendedores.",
              "info": "_START_ a _END_ de un total de _TOTAL_",
              "search": "Buscar:",
              "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
              "infoFiltered": ""
          },
          "columns": [{
              "data": "user_name", "className": "head_name", "render": function (data, type, row) {
                  let html = '';
                  html += '<td class="align-middle">';
                  html += '<div class="d-inline-block">';
                  html += '<h6 class="m-b-0"><b>';

                  let name = row.seller_name;
                  if (typeof name === 'string') {
                      const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
                      const words = row.seller_name.split(' ').map(function (word) {
                          const lowerWord = word.toLowerCase();
                          if (lowerCaseSuffixes.includes(lowerWord)) {
                              return word.toUpperCase();
                          } else {
                              return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                          }
                      });
                      html += words.join(' ');
                  }
                  html += '</b>';
                  if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                      html += ' - ' + row.user_name.split(' ').map(function (word) {
                          return word.charAt(0).toUpperCase() + word.slice(1);
                      }).join(' ');
                  }

                  html += '</h6>';
                  html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                  html += '</div>';
                  html += '</td>';

                  return html;
              }
          }, {
              "data": "num_pending_invoices",
              "className": "pendings",
              "render": function (data, type, row) {
                if (data && (type === 'display' || type === 'filter')) {
                    let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                    html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                    html += '<div class="progress" style="height: 15px;">';
                    html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                    html += '</div>';
                    html += '</td>';
                    return html;
                }
                return data;
              }
          }, {
              "data": null, "visible": false, "render": function (data, type, row) {
                  let html = '';
                  html += '<div>' + row.model_min + '</div>';
                  return html;
              }
          }, {
              "data": null, "visible": false, "render": function (data, type, row) {
                  let html = '';
                  html += '<div>' + row.model_avg + '</div>';
                  return html;
              }
          }, {
              "data": "model_111", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m111 = row.model_111;
                  let external_accounting = row.external_accounting;
                  if (m111 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/111/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m111 == "pending") {
                      if (external_accounting){
                        html += '<span class="d-none">' + 4 + '</span>';
                        html += '<a href="/sellers/' + row.shortname + '/model/111/"><i class="fa-solid fa-envelope fa-xl" style="color: #7c7d7e;"></i></a>';
                      } else {
                        html += '<span class="d-none">' + 4 + '</span>';
                        html += '<a href="/sellers/' + row.shortname + '/model/111/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                      }
                  } else if (m111 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                  } else if (m111 == 'agreed') {
                    if (external_accounting){
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-envelope-circle-check fa-xl" data-bs-toggle="tooltip" data-bs-placement="top" title="Conforme y enviado al gestor externo" style="color: #427ddb;"></i></a>';
                    } else {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                    }
                  } else if (m111 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m111 == 'required') {
                    if (external_accounting){
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/111/"><i class="fa-solid fa-envelope fa-xl" style="color:#FF0000;"  data-bs-toggle="tooltip" data-bs-placement="top" title="Modelo a presentar por gestor externo"></i></a>';
                    } else {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/111/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                    }
                  } else if (m111 == 'warning13') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/111/"><i class="fa-solid fa-xl " style="color: #FF0000;">13</i></a>';
                  }
                  return html;
              }
          }, {
              "data": "model_115", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m115 = row.model_115;
                  if (m115 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/115/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m115 == "pending") {
                      html += '<span class="d-none">' + 4 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/115/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                  } else if (m115 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                  } else if (m115 == 'agreed') {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                  } else if (m115 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m115 == 'required') {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/115/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                  } else if (m115 == 'warning') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/115/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                  }
                  return html;
              }
          }, {
              "data": "model_130", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m130 = row.model_130;
                  if (m130 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/130/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m130 == "pending") {
                      html += '<span class="d-none">' + 4 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/130/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                  } else if (m130 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                  } else if (m130 == 'agreed') {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                  } else if (m130 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m130 == 'required') {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/130/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                  } else if (m130 == 'warning') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/130/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                  }
                  return html;
              }
          }, {
              "data": "model_303", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m303 = row.model_303;
                  if (m303 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/303/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m303 == "pending") {
                      html += '<span class="d-none">' + 4 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                  } else if (m303 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                  } else if (m303 == 'agreed') {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                  } else if (m303 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m303 == 'required') {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                  } else if (m303 == 'warning') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                  }
                  return html;

              }
          }, {
              "data": "model_309", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m309 = row.model_309;
                  if (m309 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/309/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m309 == "pending") {
                      html += '<span class="d-none">' + 4 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/309/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                  } else if (m309 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                  } else if (m309 == 'agreed') {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                  } else if (m309 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m309 == 'required') {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/309/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                  } else if (m309 == 'warning') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/309/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                  }
                  return html;

              }
          }, {
              "data": "model_349", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m349 = row.model_349;
                  if (m349 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/349/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m349 == "pending") {
                      html += '<span class="d-none">' + 4 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                  } else if (m349 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                  } else if (m349 == 'agreed') {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                  } else if (m349 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m349 == 'required') {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                  } else if (m349 == 'warning') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/349/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                  }
                  return html;
              }
          }, {
              "data": "model_369", "className": "model", "render": function (data, type, row) {
                  let html = ' ';
                  let m369 = row.model_369;
                  if (m369 == "presented") {
                      html += '<span class="d-none">' + 5 + '</span>';
                      html += '<a href="/' + row.shortname + '/model/369/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                  } else if (m369 == "pending") {
                      html += '<span class="d-none">' + 4 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                  } else if (m369 == 'disagreed') {
                      html += '<span class="d-none">' + 2 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                  } else if (m369 == 'agreed') {
                      html += '<span class="d-none">' + 3 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                  } else if (m369 == 'not-required') {
                      html += '<span class="d-none">' + 6 + '</span>';
                      html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                  } else if (m369 == 'required') {
                      html += '<span class="d-none">' + 0 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                  } else if (m369 == 'warning09') {
                      html += '<span class="d-none">' + 1 + '</span>';
                      html += '<a href="/sellers/' + row.shortname + '/model/369/"><i class="fa-solid fa-xl " style="color: #FF0000;">09</i></a>';
                  }
                  return html;
              }
          }, {
              "data": "month1", "className": "txt Q1", "render": function (data, type, row) {
                  let html = '';
                  const month1 = row.month1;
                  if (month1 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              },
          }, {
              "data": "month2", "className": "txt Q1", "render": function (data, type, row) {
                  let html = '';
                  const month2 = row.month2;
                  if (month2 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month3", "className": "txt Q1", "render": function (data, type, row) {
                  let html = '';
                  const month3 = row.month3;
                  if (month3 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month4", "className": "txt Q2", "render": function (data, type, row) {
                  let html = '';
                  const month4 = row.month4;
                  if (month4 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month5", "className": "txt Q2", "render": function (data, type, row) {
                  let html = '';
                  const month5 = row.month5;
                  if (month5 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              },
          }, {
              "data": "month6", "className": "txt Q2", "render": function (data, type, row) {
                  let html = '';
                  const month6 = row.month6;
                  if (month6 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month7", "className": "txt Q3", "render": function (data, type, row) {
                  let html = '';
                  const month7 = row.month7;
                  if (month7 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month8", "className": "txt Q3", "render": function (data, type, row) {
                  let html = '';
                  const month8 = row.month8;
                  if (month8 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              },
          }, {
              "data": "month9", "className": "txt Q3", "render": function (data, type, row) {
                  let html = '';
                  const month9 = row.month9;
                  if (month9 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              },
          }, {
              "data": "month10", "className": "txt Q4", "render": function (data, type, row) {
                  let html = '';
                  const month10 = row.month10;
                  if (month10 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month11", "className": "txt Q4", "render": function (data, type, row) {
                  let html = '';
                  const month11 = row.month11;
                  if (month11 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "month12", "className": "txt Q4", "render": function (data, type, row) {
                  let html = '';
                  const month12 = row.month12;
                  if (month12 === true) {
                      html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                      html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                      html += '</a>';
                  } else {
                      html += '&nbsp';
                  }

                  return html;
              }
          }, {
              "data": "last_login",
              "className": "login",
              "render": function (data, type, row) {
                if (data && (type === 'display' || type === 'filter')) {
                    const date = new Date(data);

                    const day = date.getDate().toString().padStart(2, '0');
                    const month = date.toLocaleString('default', { month: 'short' });
                    const year = date.getFullYear();
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');

                    const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

                    return formattedDate;
                }
                return data; // For other types, like 'sort'
                }
          }, {
              "data": null, "className": "actions", "orderable": false, "render": function (data, type, row) {
                  let html = '<td class="align-middle text-center">';
                  html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                  html += '<i class="feather icon-edit"></i>';
                  html += '</a>';
                  html += '</td>';
                  html = '<div style="text-align: center;">' + html + '</div>';
                  return html;
              },
          }],
          "paging": true,
          "searching": true,
          "lengthChange": false,
          "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
          "createdRow": function (row, data, dataIndex) {
              const shortname = data.shortname;
              const link = '/sellers/' + shortname + '/';
              $(row).attr('style', 'cursor: pointer;');
              $(row).attr('onclick', "window.location.href = '" + link + "';");
          },
          "initComplete": function (settings, json) {
              dataTable2.settings()[0].nTBody.style.width = "100%";
              dataTable2.settings()[0].nTable.style.width = "100%";
              dataTable2.settings()[0].nTHead.style.width = "100%";
              $("#showcolumns").prop("disabled", false);
              onClickButtonModelsTxT();
              document.getElementById('text_loading').textContent='Modelos/TXT'
              document.getElementById("pending-model-count").textContent = json.pending_model_count; // Actualiza el valor en el HTML
              document.getElementById("total_invoices_count").textContent = json.total_invoices;
              document.getElementById("revision-model-count").textContent = json.revision_model_count;
              document.getElementById("disagreed-model-count").textContent = json.disagreed_model_count;
              document.getElementById("agreed-model-count").textContent = json.agreed_model_count;
              document.getElementById("presented-model-count").textContent = json.presented_model_count;
              cookie = getCookie("allsellersmanagement_show_model_txt");
              const period = "{{period}}";
              $('th.txt, td.txt').hide();
              $(`th.txt.${period}, td.txt.${period}`).show();
          },
          "drawCallback": function (settings) {
              dataTable2.settings()[0].nTable.style.width = "100%";
              cookie = getCookie("allsellersmanagement_show_model_txt");
              const period = "{{period}}";
              $('th.txt, td.txt').hide();
              $(`th.txt.${period}, td.txt.${period}`).show();
              $('[data-bs-toggle="tooltip"]').tooltip();
          }
        });
        resolve();
      });
    }

    // para hacer funciones en paralelo
    Promise.all([secondDataTable(), firstDataTable()]).then(() => {
      let syncingTables = false; // Para evitar ciclos infinitos

      //Cambiar pagina dataTable1 y reflejar en dataTable2
      dataTable1.on('page.dt', function() {
        const pageInfo = dataTable1.page.info();
        const currentPage = pageInfo.page;
        syncingTables = true;
        updatePageInTable(currentPage);
        syncingTables = false;
      });
      dataTable1.on('order.dt', function() {
        if(!syncingTables){
          const order = dataTable1.order();
          syncingTables = true;
          orderInTable(order);
          syncingTables = false;
        }
      });

      const updatePageInTable = (page) => {
        dataTable2.page(page).draw('page');
      }
      const orderInTable = (order) => {
        const total1 = dataTable1.columns().nodes().length;
        const total2 = dataTable2.columns().nodes().length;
        const position_order = order[0][0];
        const type_order = order[0][1];
        if (position_order === 0 || position_order === 1 ) { // si es la primera o segunda columna ordenacion
          dataTable2.order(order).draw();
        } else if (position_order === total1 - 2) { // si es la penultima columna ordenacion
          dataTable2.order([total2 - 2, type_order]).draw();
        }
      }

      //Cambiar pagina dataTable2 y reflejar en dataTable1
      dataTable2.on('page.dt', function() {
        const pageInfo = dataTable2.page.info();
        const currentPage = pageInfo.page;
        syncingTables = true;
        updatePageInTable2(currentPage);
        syncingTables = false;
      });
      dataTable2.on('order.dt', function() {
        if(!syncingTables){
          const order = dataTable2.order();
          syncingTables = true;
          orderInTable2(order);
          syncingTables = false;
        }
      });

      const updatePageInTable2 = (page) => {
          dataTable1.page(page).draw('page');
      }
      const orderInTable2 = (order) => {
        const total1 = dataTable1.columns().nodes().length;
        const total2 = dataTable2.columns().nodes().length;
        const position_order = order[0][0];
        const type_order = order[0][1];
        if (position_order === 0 || position_order === 1) { // si es la primera o segunda columna ordenacion
          dataTable1.order(order).draw();
        } else if (position_order === total2 - 2) { // si es la penultima columna ordenacion
          dataTable1.order([total1 - 2, type_order]).draw();
        }
      }
    });
}

function search() {
    const tipo = $("#search").val();
    dataTable1.column(0).search(tipo).draw();
    dataTable2.column(0).search(tipo).draw();
}

const getTotals = (params) => {
    let p = params;

    if (!p || p === undefined || p === null || p === "") {
        p = "";
    } else if (p.charAt(0) == "&") {
        p[0] = "?";
    }
}

const onChangePeriodYear = () => {
    const period = document.getElementById('period');
    const year = document.getElementById('year');
    const entity = document.getElementById('entity');
    const urlembed = "{% url 'app_sellers:old-management' 'ES' %}";
    const newUrl = urlembed + '?period=' + period.value + '&year=' + year.value + '&entity=' + entity.value;
    window.location.href = newUrl;
}

const onload = () => {
    document.getElementById('period').value = '{{period}}';
    document.getElementById('year').value = '{{year}}';
    document.getElementById('entity').value = '{{entity}}';
}

const createCookie = (name, value) => {
    document.cookie = name + "=" + value + "; path=/";
}

const getCookie = (name) => {
    const cookieValue = document.cookie.match("(^|;)\\s*" + name + "\\s*=\\s*([^;]+)");
    return cookieValue ? cookieValue.pop() : "";
}

const onClickButtonModelsTxT = () => {
  let div_table_only_management = $("#id_table1");
  let div_table_model_txt_management = $("#id_table2");
  if (div_table_only_management.is(":hidden")) {
    div_table_only_management.show();
    div_table_model_txt_management.hide();
    showModelTXT = true;
  } else {
    div_table_only_management.hide();
    div_table_model_txt_management.show();
    showModelTXT = false;
  }
}

const cookie = getCookie("allsellersmanagement_show_model_txt");
if (cookie === true || cookie === "true") {
    onClickButtonTxT();
}

onload();
</script>
<!-- JQUERY DATATABLES -->
{% endblock javascripts %}
