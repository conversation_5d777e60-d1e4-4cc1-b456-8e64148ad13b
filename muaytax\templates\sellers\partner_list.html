{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Socios{% endblock title %}

{% block content %}
  <h1>Socios</h1>

  <a class="btn btn-primary" href="{% url 'app_sellers:partner_create' %}" role="button">Nuevo</a>

  <table class="table">
    <thead>
      <tr>
        <th scope="col">Nombre</th>
        <th scope="col">Tipo</th>
        <th scope="col">Porcentaje</th>
        <th scope="col">Acciones</th>
        <th scope="col"></th>
      </tr>
    </thead>
    <tbody>
      {% for object in object_list %}
      <tr>
        <td>
          <a href="{% url 'app_sellers:partner_update' object.pk %}">
            {% if object.entity == 'legal' %}
              {{ object.seller_name }}
            {% else %}
              {{ object.first_name }} {{ object.last_name }}
            {% endif %}
          </a>
        </td>
        <th scope="row">{{ object.get_entity_display }}</th>
        <td>{{ object.shareholding_percentage }}</td>
        <td>{{ object.shares_number }}</td>
        <td><a class="btn btn-danger" href="{% url 'app_sellers:partner_delete' object.pk %}" role="button">Borrar</a></td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
{% endblock content %}
