from django.db import models

class InvoiceStatus(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )

    class Meta:
        verbose_name = "Estado de la Factura"
        verbose_name_plural = "Estados de la Factura"
    
    def __str__(self):
        return self.description
    
# @admin.register(InvoiceStatus)
# class InvoiceStatusAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
