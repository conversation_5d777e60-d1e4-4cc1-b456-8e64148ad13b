import re
from pathlib import Path
from collections import defaultdict

# <PERSON>gar el CSS original
input_path = Path("docs/helpers/ScriptCSS_search/styleFormIVA.css")
output_path = Path("docs/helpers/ScriptCSS_search/styleFormIVA_sorted.css")

css_content = input_path.read_text(encoding="utf-8")
css_blocks = re.findall(r'(\/\*.*?\*\/)?\s*([^{]+)\{([^}]+)\}', css_content, re.DOTALL)

# Clasificación
categories = defaultdict(list)
for comment, selector, rules in css_blocks:
    selector = selector.strip()
    rules = rules.strip().replace(';', ';\n    ')
    block = f"{selector} {{\n    {rules.strip()}\n}}"

    if ":root" in selector:
        categories["Variables globales"].append(block)
    elif selector.startswith("@keyframes"):
        categories["Animaciones"].append(block)
    elif any(keyword in selector for keyword in ["modal", "swal"]):
        categories["Modales y alertas"].append(block)
    elif any(keyword in selector for keyword in ["form", "input", "label", "fieldset", "select", "textarea"]):
        categories["Formularios"].append(block)
    elif any(keyword in selector for keyword in ["btn", "button"]):
        categories["Botones"].append(block)
    elif any(keyword in selector for keyword in ["table", "tr", "td", "th"]):
        categories["Tablas"].append(block)
    elif "card" in selector:
        categories["Tarjetas"].append(block)
    elif any(keyword in selector for keyword in ["row", "col"]):
        categories["Grillas y columnas"].append(block)
    elif any(keyword in selector for keyword in ["nav", "tab"]):
        categories["Navegación / Tabs"].append(block)
    elif "icon" in selector:
        categories["Iconos"].append(block)
    else:
        categories["Otros"].append(block)

# Generar contenido ordenado
ordered_content = []
for name, blocks in categories.items():
    ordered_content.append(f"/* ==== {name} ==== */\n")
    ordered_content.extend([b + "\n" for b in blocks])
    ordered_content.append("\n")

# Guardar
output_path.write_text("\n".join(ordered_content), encoding="utf-8")
print(f"Archivo ordenado guardado en: {output_path}")
