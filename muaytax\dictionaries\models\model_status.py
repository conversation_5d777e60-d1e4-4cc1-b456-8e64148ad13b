from django.db import models

class ModelStatus(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>",
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción",
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )

    class Meta:
        verbose_name = "Estado del Modelo"
        verbose_name_plural = "Estados del Modelo"
    
    def __str__(self):
        return self.description
    

# @admin.register(ModelStatus)
# class ModelStatusAdmin(admin.ModelAdmin):
#     list_display = ["code", "description","order"]
#     search_fields = ["code", "description"]