from muaytax.app_bookings.models.manage_model import ManageModel
from muaytax.app_bookings.models.schedule_manager import Schedule
from muaytax.app_bookings.models.absence import Absence
from muaytax.app_bookings.models.holidays import Holidays
from muaytax.app_bookings.bookingfinder import FindBookingSpots

from muaytax.dictionaries.models.model import Model

from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404

from django.db.models import QuerySet
from datetime import datetime, timedelta

User = get_user_model()

def get_managers_consulting() -> QuerySet:
    """Retrieve managers from the consulting department."""
    return User.objects.filter(department__code='consulting-department')
    
def generate_booking_spots(appointments: QuerySet, manager: object, selected_date, first_open_date, booking_type='app'):
    """Generate available booking spots."""
    if selected_date is None:
        return {}

    finder = FindBookingSpots(
        booking_type=booking_type,
        appointments=appointments,
        manager=manager,
        selected_date=selected_date
    )

    # Prevenir NoneType error
    first_working_param = None
    if first_open_date is not None and selected_date is not None:
        try:
            if selected_date.date() == first_open_date.date():
                first_working_param = first_open_date
        except (AttributeError, TypeError):
            first_working_param = None

    return finder.generate15(first_working_datetime=first_working_param)

def get_first_working_datetime(manager_username: str, subject=None) -> datetime:
    # now = datetime.now().replace(day=20, hour=8, minute=0, second=0, microsecond=0)
    now = datetime.now().replace(second=0, microsecond=0)
    minutes_to_round = 15 - (now.minute % 15)
    datetime_request_rounded = first_working_datetime =  (now + timedelta(minutes=minutes_to_round))
    start_date = datetime_request_rounded.date()

    booking_time_delta = get_booking_margin_delta(subject, now.month)
    
    schedule = Schedule.objects.get(manager__username=manager_username)
    manager_start_time = schedule.start_time
    manager_end_time = schedule.end_time

    holidays = set(Holidays.objects.filter(date__gte=start_date).values_list('date', flat=True))
    absences_all_day = set(Absence.objects.filter(manager__username=manager_username, date__gte=start_date, is_all_day=True).values_list('date', flat=True))

    starting_delta_time = 2
    while True:
        request_date = first_working_datetime.date()
        request_time = first_working_datetime.time()

        if first_working_datetime.weekday() in [5,6] or request_date in holidays or request_date in absences_all_day:
            first_working_datetime = first_working_datetime.replace(hour=manager_start_time.hour, minute=manager_start_time.minute) + timedelta(days=1)
            continue
        
        if request_time <= manager_start_time:
            if booking_time_delta == 24:
                first_working_datetime += timedelta(hours=booking_time_delta)
                booking_time_delta = 0
                starting_delta_time = 0
                continue
            return first_working_datetime.replace(hour=manager_start_time.hour, minute=manager_start_time.minute) + timedelta(hours=starting_delta_time)
        
        datetime_of_request_plus_delta = datetime_request_rounded + timedelta(hours=booking_time_delta)

        if datetime_of_request_plus_delta.date() > request_date:
            if datetime_of_request_plus_delta.time() < manager_start_time:
                first_working_datetime = first_working_datetime.replace(hour=manager_start_time.hour, minute=manager_start_time.minute) + timedelta(days=1)
            elif manager_start_time <= datetime_of_request_plus_delta.time() < manager_end_time:
                first_working_datetime += timedelta(days=1)
                booking_time_delta = 0
                starting_delta_time = 0
            else:
                first_working_datetime += timedelta(days=1)
                booking_time_delta = 0
                starting_delta_time = 2
            continue
        else:
            if datetime_of_request_plus_delta.time() >= manager_end_time:
                first_working_datetime = first_working_datetime.replace(hour=manager_start_time.hour, minute=manager_start_time.minute) + timedelta(days=1)
                if booking_time_delta == 0:
                    starting_delta_time = 0
                else:
                    starting_delta_time = 2
                continue
            else:
                return first_working_datetime + timedelta(hours=starting_delta_time)

def get_manager_of_model(model: object, seller: object) -> list:
    query = ManageModel.objects.filter(model=model)
    if len(query) > 1:
        manager_of_model = ManageModel.objects.filter(model=model, legal_entity=seller.legal_entity).first()
    elif len(query) == 1:
        manager_of_model = ManageModel.objects.filter(model=model).first()
    else:
        return None
    
    return [manager_of_model.manager]
    
def get_rounded_time() -> datetime.time:
    """
    Function that returns the current time rounded to the nearest half hour
    This is for the time slots in the booking form
    """
    # current_time = datetime.now().time().replace(hour=23, minute=20, second=0, microsecond=0)
    current_time = datetime.now().time()
    
    minutes_to_add = 30 - (current_time.minute % 30)

    rounded_time = datetime.combine(datetime.now().date(), current_time) + timedelta(minutes=minutes_to_add)

    return rounded_time.time()

def get_booking_margin_delta(subject: str, current_month: int)-> int:
    """
    This function is a validation for allowing the sellers book calls with a margin of 1 hour, 2 hours or 24 hours
    - 2 hours margin is for the contract-service subject (empar)
    - When it's not presentation month and the call is for sl/freelance
    """
    if subject == 'contract-service':
        return 2
    elif subject == 'contracted-accounting' and current_month not in [1,4,7,10]:
        # return 2
        return 24 # Sara ha pedido el cambio a 24h de nuevo 11/11/2024
    else:
        return 24

def get_managers(subject: str, topics: str, seller: object) -> list:
    """ 
    Get the manager of the seller based on the subject and topics. 
    It has to be a list because it can return more than one manager 
    """
    managers = None
    if subject == "contract-service":
        managers = User.objects.filter(department__code='consulting-department')
    elif subject == "vat-country":
        if topics == "Registro de País IVA" or topics == "Dudas App":
            managers = User.objects.filter(username="amedeoceloria")
        if topics == "Mantenimiento País IVA-España" or topics == "Mantenimiento País IVA-Resto de Países":
            managers = User.objects.filter(username="forecchia")
    elif subject == "contracted-accounting":
        if topics == "Soporte Alta/Migración":
            managers = User.objects.filter(username__in=['abuedo', 'guillermogomez'])
        elif seller.legal_entity == "sl":
            if topics == "Registro de autónomo/SL":
                managers = User.objects.filter(username="abuedo")
            else:
                managers = User.objects.filter(username="m.garcia")
        else:
            if topics == "Registro de autónomo/SL":
                managers = User.objects.filter(username="abuedo")
            else:
                managers = User.objects.filter(username="esthercañada")

    elif subject == "llc-support" or subject == "form-5472-1120" or subject == "boir-support":
        managers = User.objects.filter(department__code='support-usa-department')
    elif subject == "model-disagree":
        model = get_object_or_404(Model, code=topics)
        managers = get_manager_of_model(model, seller)

    return managers

# Obtener el ultimo dia del mes siguiente
def get_next_month_end(date):
    # Mueve la fecha al primer día del siguiente mes
    next_month = (date.replace(day=1) + timedelta(days=32)).replace(day=1)
    # Resta un día para obtener el último día del próximo mes
    last_day_next_month = next_month - timedelta(days=1)
    return last_day_next_month

# Obtener el el ultimo dia del trimestre
def get_quarter_end(date):
    quarter = (date.month - 1) // 3 + 1
    quarter_end_month = quarter * 3
    quarter_end_date = datetime(date.year, quarter_end_month, 1).replace(day=28) + timedelta(days=4)
    quarter_end_date = quarter_end_date - timedelta(days=quarter_end_date.day)
    return quarter_end_date

# Obtener la fecha del ultimo dia del mes proximo al trimetre de una fecha dada
def get_last_day_next_month_quarter(date):
    quarter_end_date = get_quarter_end(date)
    next_month_end = get_next_month_end(quarter_end_date)
    return next_month_end.date()