{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Productos Amazon
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    .tooltip-inner a {
      color:white;
      text-decoration: none; 
    }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Amazon TXT EUR</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name|title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Amazon TXT EUR</a>
            </li>
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
          <a href="{% url 'app_invoices:seller_invoices_upload' seller.shortname %}" class="btn btn-primary">
            Importar TXT
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-2 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."/>
              </div>
            </div>
          </div>
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
              <tr>
                <th>Nombre Fichero</th>
                <th>Amazon ID</th>
                <th>Mes</th>
                <th>Año</th>
                <th>Fecha Carga</th>
                <th style="width:5%;">Estado</th>
                <th style="width:5%;">Acciones</th>
              </tr>
              </thead>
              <tbody>
              {% for object in object_list %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.file }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.amz_id|default:'' }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.month|default:'' }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.year|default:'' }} </span>
                  </td>
                  <td class="align-middle">
                    {{ object.created_at|date:'Y-m-d H:i:s' }}
                  </td>
                  <td class="align-middle">
                      <span class="fs-5" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-html="true"
                        {% if object.error_message != None %}
                            data-bs-title="{{ object.error_message }}"
                        {% endif %}
                        {% if object.error_message == None %}
                            data-bs-title="Sin información"
                        {% endif %}
                      >
                      {% if object.status.code == "processed" %}
                        <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                      {% elif object.status.code == "failed" %}
                        <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                      {% else %}
                        <i class="fa-regular fa-xl fa-circle-question" style="color: #ff9500;"></i>
                      {% endif %}
                      </span>
                  </td>
                  <td class="align-middle">
                    <div>
                      <a data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar" class="btn btn-icon btn-info" href="{{ object.get_file_url }}" target="_blank" download>
                        <i class="fa-solid fa-download"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
  <script>
    $(document).ready(function () {
      const dataTableOptions = {
        paging: false,
        searching: true,
        ordering: true,
        truncation: true,
        info: true,
        footer: true,
        columnDefs: [
          {
          targets: 4,
          render: function (data, type, row) {
            if (data && (type === 'display' || type === 'filter')) {
                const date = new Date(data);

                const day = date.getDate().toString().padStart(2, '0');
                const month = date.toLocaleString('default', { month: 'short' });
                const year = date.getFullYear();
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');

                const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;
                
                return formattedDate;
            }
            return data; // Para otros tipos, como 'sort'
          }
        },
        {
          targets: 6,
          orderable: false,
        },
      ],
        order: [[4, 'desc']],
        language: {
          lengthMenu: "_MENU_",
          zeroRecords: "No se han encontrado productos.",
          info: "_TOTAL_ resultados. ",
          search: "Buscar:",
          infoEmpty: "No hay resultados que coincidan con su búsqueda.",
          infoFiltered: ""
        },
        dom: 'lrtip',
        fixedHeader: true,
      };


      const dataTable = $("#list-table").DataTable(dataTableOptions);

      $("#search").on("input", function () {
        const filtro = $(this).val();
        console.log(filtro)
        dataTable.search(filtro).draw();
      });

    });

    const getFileName = (file) => {
      const fileName = file.split('\\').pop().split('/').pop();
      return fileName;
    }


  </script>
{% endblock javascripts %}
