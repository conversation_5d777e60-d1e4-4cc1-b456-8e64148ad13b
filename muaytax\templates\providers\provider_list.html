{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}
    Proveedores
{% endblock title %}
{% block stylesheets %}
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
    <!-- Limit Characters in Table Span -->
    {% if user.role == 'manager' %}
        <style>
            #list-table td span {
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 27vw;
            }

            .table-head {
                position: sticky;
                top: 0;
                background-color: #f2f2f2;
                z-index: 1;
            }
        </style>
    {% else %}
        <style>

            #list-table td span {
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 19vw;
            }

            .table-head {
                position: sticky;
                top: 0;
                background-color: #f2f2f2;
                z-index: 1;
            }
        </style>
    {% endif %}
{% endblock stylesheets %}
{% block breadcrumb %}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col">
                    <div class="page-header-title">
                        <h5 class="m-b-10">Proveedores</h5>
                    </div>
                    <ul class="breadcrumb">
                        {% if user.role == 'manager' %}
                            <li class="breadcrumb-item">
                                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href=".">Proveedores</a>
                            </li>
                        {% else %}
                            <li class="breadcrumb-item">
                                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href=".">Proveedores</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
                <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
                    <a href="./new/" class="btn btn-primary">
                        Crear Nuevo
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock breadcrumb %}
{% block content %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card user-profile-list">
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-12 d-flex justify-content-center align-items-start">
                            <div class="input-group">
                                <input class="form-control" type="search" id="search" name="search"
                                       placeholder="Buscar..."/>
                            </div>
                        </div>
                    </div>
                    <div class="dt-responsive table-responsive">
                        <table id="list-table" class="table nowrap">
                            <thead class="table-head">
                            <tr>
                                <th>Nombre</th>
                                <th>NIF/CIF/IVA</th>
                                <th>VIES</th>
                                <th>Tipo de proveedor</th>
                                <th>Número de proveedor</th>
                                <th>Acciones</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for object in object_list %}
                                <tr>
                                    <td class="align-middle">
                                        <span>{{ object.name }}</span>
                                    </td>
                                    <td class="align-middle">
                                        <span>{{ object.nif_cif_iva|default:'' }}</span>
                                    </td>
                                    <td class="align-middle">
                                        <span>{{ object.vies|default:'' }}</span>
                                    </td>
                                    <td class="align-middle">
                                        <span>{{ object.provider_type|default:'' }}</span>
                                    </td>
                                    <td class="align-middle">
                                        <span>{{ object.provider_number|default:'' }}</span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="Editar"
                                           class="btn btn-icon btn-success"
                                           href="{% url 'app_providers:detail' object.seller.shortname object.pk %}">
                                            <i class="feather icon-edit"></i>
                                        </a>
                                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="Eliminar"
                                           class="btn btn-icon btn-danger"
                                           href="{% url 'app_providers:delete' object.seller.shortname object.pk %}">
                                            <i class="feather icon-trash-2"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}
{% block javascripts %}
    <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
    <script>
        $(document).ready(function () {
            const dataTableOptions = {
                serverSide: false,
                autoWidth: true,
                truncation: true,
                paging: true,
                searching: true,
                lengthChange: false,
                language: {
                    lengthMenu: "_MENU_",
                    zeroRecords: "No se han encontrado proveedores.",
                    info: "_TOTAL_ resultados. ",
                    search: "Buscar:",
                    infoEmpty: "No hay resultados que coincidan con su búsqueda.",
                    infoFiltered: ""
                },
                dom: 'lrtip',
                fixedHeader: true,
            };
            const dataTable = $("#list-table").DataTable(dataTableOptions);

            $("#search").on("input", function () {
                const filtro = $(this).val();
                dataTable.search(filtro).draw();
            });
        });
    </script>
{% endblock javascripts %}
