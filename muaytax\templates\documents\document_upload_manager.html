{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css"/>
  <style>
    .dropzone-container{
      height: 100% !important;
    }
    .dropzone {
      position: relative;
      width: 100%;
      height: 100% !important;
      background-image: url("https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png");
      background-size: 85px;
      background-repeat: no-repeat;
      background-position: center 25%;
    }
    .dropzone .dz-message {
      display: block; 
      position: absolute;
      top: 33%;
      right: 50%;
      transform: translateX(50%);
      text-align: center;
      padding: 20px;
    }
    .dropzone .dz-preview.dz-error .dz-error-message {
      display: none !important; 
    }


  </style>
{% endblock stylesheets %}
{% block title %}Cargador de Documentos{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Documentos</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name|title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_documents:document_list' seller.shortname %}">Documentos</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Cargador de Documentos</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <!-- Cargar Documentos | START  -->
    <div class="col-12" id="uploads">
      <div class="card">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>Cargador de Documentos</h5>
          </div>
        </div>
        <div class="card-body border">
          
          <div class="row">
            <div class="col-6">
              <div class="container">
                <!-- Campos de documentos -->
                <div class="row justify-content-start">
                  <div class="col-12">
                    <label class="form-label" for="inputCustomerType">
                      País de las Declaraciones a Cargar:
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputSellerVat"
                      v-model="inputSellerVat"
                      @change="inputDocumentType = null"
                    >
                      <option :value="null" selected>Desconocido</option>
                      <option v-for="vat in dj.seller_vat" :key="vat.pk" :value="vat.pk">
                        [[ getCountryNameByCode(vat.vat_country) ]]
                      </option>
                    </select>
                  </div>
                  <div class="col-12 mt-3">
                    <label class="form-label text-dark fw-bold" for="inputCustomerType">
                      Tipo de Documento a Cargar: <span class="text-dark text-danger">*</span>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputDocumentType"
                      v-model="inputDocumentType"
                    >
                      <option :value="null" selected>Desconocido</option>
                      <option v-for="doctype in getDocumentTypeByCountry(inputSellerVat)" :key="doctype.pk"
                              :value="doctype.pk">
                        [[ doctype.description ]]
                      </option>
                    </select>
                  </div>
                </div>
                <div class="row justify-content-start">
                  <div class="col-6 mt-3">
                    <label class="form-label" for="inputCustomerType">
                      Año de las Declaraciones a Cargar:
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputYear"
                      v-model="inputYear"
                      :disabled="inputDocumentType == 'ES-SIGNATURE'"
                    >
                      <option :value="null">Desconocido</option>
                      <option value="2025" selected>2025</option>
                      <option value="2024">2024</option>
                      <option value="2023">2023</option>
                      <option value="2022">2022</option>
                      <option value="2021">2021</option>
                      <option value="2020">2020</option>
                    </select>
                  </div>
                  <div class="col-6 mt-3">
                    <label
                      class="form-label text-dark fw-bold" for="inputPrivacy">
                      ¿El documento es Privado o Público? <span class="text-dark text-danger">*</span>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputPrivacy"
                      v-model="inputPrivacy"
                      @change="handleInputChange"
                      required
                    >
                      <option :value="null">Selecciona</option>
                      <option value="public">Público</option>
                      <option value="private">Privado</option>
                    </select>
                  </div>
                </div>
                <div v-show= "inputDocumentType == 'ES-SIGNATURE'" class="row justify-content-start" style="display:none;">
                  <div class="col-6 mt-3">
                    <label class="form-label text-dark fw-bold" >
                      Fecha de expiración del documento: <span class="text-dark text-danger">*</span>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputExpirationDate"
                      v-model="inputExpirationDate"
                      :required="inputDocumentType == 'ES-SIGNATURE'"/>
                  </div>
                  <div class="col-6 mt-3">
                    <label
                      class="form-label text-dark fw-bold">
                      Contraseña del certificado <span class="text-dark text-danger">*</span>
                    </label>
                    <input
                      type="password"
                      class="form-control"
                      id="inputPassword"
                      v-model="inputPassword"
                      :required="inputDocumentType == 'ES-SIGNATURE'"/>
                  </div>
                </div>
              </div>
            </div>
            
          
            <div class="col-6">
              {% comment %} <div style="border: 2px solid rgba(0, 0, 0, .3); border-radius: 5px; height: 100%;" v-show="!(inputDocumentType && inputPrivacy && inputDocumentType!= 'ES-SIGNATURE') "> {% endcomment %}
                <div style="border: 2px solid rgba(0, 0, 0, .3); border-radius: 5px; height: 100%;" v-show="(!(inputDocumentType && inputPrivacy) && inputDocumentType!= 'ES-SIGNATURE') || 
                                                                                                          (inputDocumentType== 'ES-SIGNATURE' && (!inputPrivacy || !inputExpirationDate || !inputPassword) )">
                
                <div class="swal2-icon swal2-warning swal2-icon-show" style="display: flex;"><div class="swal2-icon-content">!</div></div>

                <div class="container mt-3">
                  {% comment %} <div class="alert alert-warning align-content-center" style="text-align: center;" v-show="!(inputDocumentType && inputPrivacy && inputDocumentType!= 'ES-SIGNATURE')"> {% endcomment %}
                  <div class="alert alert-warning align-content-center" style="text-align: center;" v-show="(!(inputDocumentType && inputPrivacy) && inputDocumentType!= 'ES-SIGNATURE') || 
                                                                                                          (inputDocumentType== 'ES-SIGNATURE' && (!inputPrivacy || !inputExpirationDate || !inputPassword) )">
                    <h4>Seleccione primero los campos de los documentos a subir.</h4>
                  </div>
                </div>
              
              </div>

              {% comment %} <div class="dropzone-container" v-show="(inputDocumentType && inputPrivacy && inputDocumentType!= 'ES-SIGNATURE')" style="display: none;"> {% endcomment %}
              <div class="dropzone-container" v-show="(inputDocumentType && inputPrivacy && inputDocumentType!= 'ES-SIGNATURE') || 
                                                      (inputDocumentType && inputPrivacy && inputDocumentType== 'ES-SIGNATURE' && inputExpirationDate && inputPassword)" 
                                              style="display: none;">
                <form method="post" enctype="multipart/form-data"
                      action="{% url 'app_documents:document_create' seller.shortname %}" id="my-dropzone" class="dropzone" 
                      :style="{
                        backgroundImage: inputDocumentType === 'ES-SIGNATURE'
                          ? 'url(https://www.iconpacks.net/icons/2/free-icon-certificate-3461.png)'
                          : 'url(https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png)',
                        
                      }"
                      >
                  {% csrf_token %}
                  <div class="d-none d-print-none">
                    <input
                      type="hidden"
                      id="id"
                      name="{{ form_create.seller.name }}"
                      value="{{ seller.pk }}"
                      required
                    />
                    <input
                      type="hidden"
                      id="sellerVat"
                      name="sellerVat"
                      v-model="inputSellerVat"
                      required
                    />
                    <input
                      type="hidden"
                      id="documentType"
                      name="documentType"
                      v-model="inputDocumentType"
                      required
                    />
                    <input
                      type="hidden"
                      id="year"
                      name="year"
                      v-model="inputYear"
                    />
                    <input
                      type="hidden"
                      id="status"
                      name="status"
                      v-model="inputStatus"
                    />
                    <input
                      type="hidden"
                      id="privacy"
                      name="privacy"
                      v-model="inputPrivacy"
                      required
                    />
                    <input
                      type="hidden"
                      id="expiration_date"
                      name="expiration_date"
                      v-model="inputExpirationDate"
                      :required="inputDocumentType == 'ES-SIGNATURE'"
                    />
                    <input
                      type="password"
                      id="password"
                      name="password"
                      v-model="inputPassword"
                      :required="inputDocumentType == 'ES-SIGNATURE'"
                    />
                  </div>
                  <div class="fallback">
                    <input
                      type="file"
                      id="file"
                      name="form_create.file.name"
                      multiple
                    />
                  </div>
                </form>
              </div> 
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Cargar Documentos | END  -->
  </div>
{% endblock content %}
{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

  <!-- DROPZONE JS  -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dropzone/dropzone.min-v5.js"></script>
  <script>
    // declare sweetalert
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    Dropzone.options.myDropzone = {
      init: function () {
        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function (file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
          if (inputDocumentType.value == 'ES-SIGNATURE'){
            let fileName = file.name;
            let fileExtension = fileName.split('.').pop();
            let allowedExtensions = ['p12', 'pfx', 'P12', 'PFX']; // Extensiones permitidas Cert.digital
            if (!allowedExtensions.includes(fileExtension)) {
              // Eliminar el archivo y mostrar un mensaje
              this.removeFile(file);
              Toast.fire({
                      icon: 'error',
                      title: "Error: sólo se permiten archivos con extensión p12 o pfx."
                  });
              return;}
          }
          
        });
        this.on("complete", function (file) {
          console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }

            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });
        this.on("success", function (file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          console.log('Success | response: ', response);
        });
        this.on("error", function (file, errorMessage) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: "application/pdf,image/jpeg,image/jpg,image/png,application/x-pkcs12",
      dictDefaultMessage: "Arrastre los archivos aquí",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };


  </script>
  <!-- DROPZONE JS  -->
  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>

    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputSellerVat = ref(null);
    const inputYear = ref(null);
    const inputDocumentType = ref(null);
    const inputPrivacy = ref(null);
    const inputFile = ref(null);
    const inputExpirationDate = ref(null);
    const inputPassword = ref(null);
    const inputStatus = ref("presented");
    const dj = ref({});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      console.log(dj.value);
    };

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
      console.log("fileList: ", fileList);
    }

    const getDocumentTypeByCountry = (seller_vat_id) => {
      let types = [];
      let country = null;

      if (seller_vat_id) {
        seller_vat = dj.value.seller_vat.filter(m => m.pk == seller_vat_id);
        if (seller_vat && seller_vat != null && seller_vat.length > 0) {
          sellerVat = seller_vat[0];
          country = sellerVat.vat_country;
        }
      }

      if (dj.value.document_type && dj.value.document_type != null && dj.value.document_type.length > 0) {
        if (country && country != null && country != "") {
          types = dj.value.document_type.filter(m => m.pk.toString().toUpperCase().startsWith(country.toUpperCase()));
        } else {
          types = dj.value.document_type.filter(m => m.pk.toString().toUpperCase().startsWith("DOC-"));
        }
      } else {
        types = dj.value.document_type.filter(m => m.pk.toString().toUpperCase().startsWith("DOC-"));
      }

      return types;
    }

    // WATCHERS ////////////////////////////////////////////////////////////////////////
    watch(inputDocumentType, (newValue, oldValue) => {
        if (newValue != 'ES-SIGNATURE') {
          inputExpirationDate.value = null;
          inputPassword.value = null;
        }else{
          inputYear.value = null;
        }
      
    });

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      inputId,
      inputSellerVat,
      inputYear,
      inputDocumentType,
      inputPrivacy,
      inputFile,
      inputStatus,
      inputExpirationDate,
      inputPassword,
      getCountryNameByCode,
      getDocumentTypeByCountry,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export}
        }
      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('#uploads', data_export);
  </script>
  <!-- VUE3 JS  -->
{% endblock javascripts %}
