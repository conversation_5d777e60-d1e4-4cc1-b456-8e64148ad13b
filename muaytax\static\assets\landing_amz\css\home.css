@charset "UTF-8";
:root {
  --color-brand-neutral-darkest:#031549;
  --color-brand-neutral-light: #E5ECF7;
  --color-neutral-lightest: #ffffff;
  --brand-font: "Poppins", serif;
  --color-brand-dark: #00AD65;
  --color-brand-light: #D9FFEE;
  --color-brand-medium: #36E093;
  --color-brand-neutral-lightest: #F7FAFF;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: 0;
  font-family: var(--brand-font);
  color: var(--color-brand-neutral-darkest);
}

a {
  text-decoration: none;
}

.header {
  background-color: var(--color-neutral-lightest);
  position: sticky;
  top: 0px;
  border-bottom: 1px solid var(--color-brand-neutral-light);
  padding: 20px 0;
  z-index: 99;
}
.header__cabecera {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 0;
  flex-wrap: nowrap;
}

.cabecera__active {
  display: inherit !important;
}
.cabecera__botones {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 60px;
  position: relative;
}
.cabecera__logo {
  width: 204.44px;
}
.cabecera__idioma a img {
  display: block;
}
.cabecera__listado {
  position: absolute;
  top: 65px;
  left: -5px;
  display: none;
}
.cabecera__login {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px; /* Espacio entre el ícono y el texto */
  border: 2px solid var(--color-brand-neutral-darkest);
  border-radius: 50px;
  padding: 8px 18px;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1px;
  width: 170px; /* Tamaño fijo basado en "Iniciar Sesión" */
  text-align: center;
  background-color: var(--color-neutral-lightest);
  position: relative; /* Para posicionar elementos internos */
}

.cabecera__login img {
  position: absolute;
  left: 15px; /* Espacio fijo desde el borde izquierdo */
  width: 20px; /* Tamaño del ícono */
  height: 20px; /* Tamaño del ícono */
}

.cabecera__login span {
  flex: 1; /* Ocupa todo el espacio restante */
  text-align: center; /* Centra el texto dentro del espacio */
}
.cabecera__login:hover {
  transition: all ease 0.3s;
  border-color: var(--color-brand-medium);
  background-color: var(--color-brand-medium);
}

.cabecera .change-language {
  font-size: 17px;
  font-weight: 700;
}

.listado {
  background-color: var(--color-neutral-lightest);
}
.listado__idioma {
  font-size: 16px;
  font-weight: 700;
  list-style: none;
  color: inherit;
  border-top: 1px solid var(--color-brand-neutral-light);
  padding: 20px 30px;
  width: 300px;
}

/*Empieza Hero*/
.hero {
  background-color: var(--color-brand-light);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.hero .mrgn-bottom-80 {
  margin-bottom: 80px;
  flex-wrap: wrap;
  justify-content: center;
}
.hero__interior {
  margin-top: 80px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 50px;
}
.hero__titulo {
  font-size: 55px;
  font-weight: 700;
  line-height: 70px;
}
.hero__listado {
  margin-left: 50px;
  margin-bottom: 50px;
}
.hero__li {
  font-size: 20px;
  font-weight: 300;
  list-style: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
}
.hero__li::before {
  content: "•";
  font-size: 20px;
  font-weight: 400;
  margin-right: 10px;
  display: inline-block;
  opacity: 0.8;
}
.hero__card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background-color: var(--color-neutral-lightest);
  width: 300px;
  min-height: 120px;
  text-align: center;
  padding: 20px;
  border-radius: 20px;
  font-size: 20px;
  font-weight: 700;
}
.hero__informacion {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/*Acaba Hero*/
/*Botones de compra/info + wrap*/
.btn {
  padding: 15px 30px;
  color: var(--color-neutral-lightest);
  font-weight: 700;
  font-size: 19px;
  background-color: var(--color-brand-neutral-darkest);
  border-radius: 50px;
  display: block;
}

.btn:hover {
  background-color: var(--color-brand-medium);
  transition: all ease 0.3s;
}

.wrap {
  max-width: 90%;
  margin: auto;
}

/* Especialistas*/
.especialistas {
  background-image: url(../image/fondo-especialista.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.especialistas__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  padding: 80px 0;
}
.especialistas__soluciones {
  display: flex;
  gap: 50px;
}
.especialistas__soluciones-titulo {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 10px;
}
.especialistas__soluciones-subtitulo {
  font-size: 17px;
  font-weight: 400;
  margin-bottom: 30px;
}
.especialistas__marketplaces {
  display: flex;
  gap: 50px;
  flex-wrap: wrap;
}
.especialistas__intro {
  margin-bottom: 70px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
}
.especialistas__intro-titulo {
  font-size: 40px;
  font-weight: 700;
}
.especialistas__intro-subtitulo {
  font-size: 24px;
  font-weight: 400;
}
.especialistas__ventajas {
  flex: 1;
}
.especialistas__blocimg {
  flex: 1;
}
.especialistas__boton {
  margin-bottom: 50px;
}

/* Emprendedores */
.emprendedores {
  background-color: var(--color-brand-neutral-darkest);
  background-image: url(../svg/pruebas.svg);
  background-repeat: no-repeat;
  background-size: content;
  background-position: center;
}
.emprendedores__content {
  margin: auto;
  padding: 80px 0;
}
.emprendedores__titulo {
  font-size: 40px;
  font-weight: 700;
  color: var(--color-neutral-lightest);
  text-align: center;
  line-height: 40px;
}
.emprendedores__subtitulo {
  font-size: 24px;
  font-weight: 400;
  color: var(--color-neutral-lightest);
  text-align: center;
  margin-top: 5px;
}

/*Fin emprendedores*/
/* conciliación */
.conciliacion__content {
  display: flex;
  gap: 50px;
  padding: 80px 0;
}
.conciliacion__info {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.conciliacion__ventajas {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.conciliacion__listado {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.conciliacion__imagen {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}
.conciliacion__titulos {
  font-size: 24px;
  font-weight: 600;
}
.conciliacion__subtitulos {
  font-size: 17px;
  font-weight: 400;
}

/*Fin conciliación*/
/*Dudas*/
.dudas {
  width: 40%;
  margin: auto;
  background-color: var(--color-brand-neutral-darkest);
  border-radius: 20px;
  padding: 20px 80px;
}
.dudas__contenido {
  display: flex;
  align-items: center;
  gap: 50px;
}
.dudas__imagen {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}
.dudas__content {
  flex: 1;
}
.dudas__titulo {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-brand-medium);
}
.dudas__subtitulo {
  font-size: 20px;
  font-weight: 400;
  color: var(--color-neutral-lightest);
  margin-bottom: 40px;
}
.dudas__btn {
  background-color: var(--color-brand-medium);
  border-radius: 50px;
  padding: 15px 30px;
  font-size: 19px;
  font-weight: 700;
}
.dudas__btn:hover {
  background-color: var(--color-brand-dark);
}

/*Fin Dudas*/
/* Ayuda */
.ayuda {
  background-color: var(--color-brand-neutral-lightest);
  margin-top: 80px;
  padding: 91px 0;
}
.ayuda__titulo {
  font-size: 40px;
  font-weight: 700;
  text-align: center;
}
.ayuda__tipo {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px;
  flex-wrap: wrap;
  margin-top: 30px;
}
.ayuda__card {
  display: flex;
  flex-direction: column;
  width: 250px;
  background-color: var(--color-neutral-lightest);
  padding: 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 60px;
}
.ayuda__card-text {
  font-size: 19px;
  font-weight: 700;
  text-align: center;
}
.ayuda__card img {
  margin: 10px 0;
}

/* Ayuda Fin */
/* Pie */
.pie {
  background-color: var(--color-brand-neutral-lightest);
}
.pie__logo {
  display: flex;
  justify-content: center;
  border-bottom: 1px solid var(--color-brand-neutral-light);
  padding-bottom: 20px;
}
.pie__datos {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 20px 0;
}
.pie__listado {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 20px;
}
.pie__enlace {
  list-style: none;
  font-size: 14px;
  font-weight: 400;
}
.pie__politicas {
  display: flex;
  align-items: flex-end;
}
.pie__nombre, .pie__calle {
  font-size: 14px;
  font-weight: 400;
}

/* Pie fin*/
/* Responsive */
@media screen and (max-width: 1921px) and (min-width: 1568px) {
  .dudas {
    width: 50%;
  }
}
@media screen and (max-width: 1568px) and (min-width: 1142px) {
  .dudas {
    width: 70%;
  }
}
@media screen and (max-width: 1141px) and (min-width: 700px) {
  .dudas {
    width: 80%;
  }
}
@media screen and (max-width: 699px) {
  .cabecera__botones {
    gap: 20px;
  }
  .cabecera__logo {
    width: 180px;
  }
  .hero .mrgn-bottom-80 {
    margin-bottom: 40px;
  }
  .hero__informacion {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .hero__interior {
    flex-direction: column-reverse;
    gap: 25px;
    margin-top: 30px;
  }
  .hero__titulo {
    font-size: 36px;
    line-height: 45px;
  }
  .hero__listado {
    margin-left: 0;
    margin-top: 20px;
    margin-bottom: 30px;
  }
  .hero__ilustrator img {
    width: 350px;
  }
  .hero__li {
    font-size: 15px;
  }
  .hero__card {
    font-size: 18px;
  }
  .especialistas__ventajas {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 25px;
  }
  .especialistas__intro {
    margin-bottom: 20px;
  }
  .especialistas__intro-titulo {
    font-size: 30px;
    text-align: center;
  }
  .especialistas__intro-subtitulo {
    font-size: 18px;
    text-align: center;
  }
  .especialistas__blocimg {
    width: 350px;
    display: flex;
    justify-content: center;
  }
  .especialistas__marketplaces {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  .especialistas__marketplaces img {
    width: 120px;
  }
  .especialistas__soluciones {
    flex-direction: column-reverse;
    align-items: center;
    gap: 25px;
  }
  .especialistas__soluciones-titulo {
    font-size: 22px;
    text-align: center;
  }
  .especialistas__soluciones-subtitulo {
    font-size: 18px;
    text-align: center;
  }
  .especialistas__imagenes {
    width: 28%;
  }
  .especialistas__image {
    width: 350px;
  }
  .dudas {
    width: 90%;
    padding: 10px 10px;
    margin-top: 50px;
  }
  .dudas__contenido {
    flex-direction: column;
  }
  .dudas__imagen img {
    width: 350px;
  }
  .dudas__titulo {
    font-size: 30px;
    text-align: center;
  }
  .dudas__subtitulo {
    font-size: 18px;
    text-align: center;
  }
  .conciliacion__content {
    flex-direction: column-reverse;
    padding: 50px 0 0 0;
  }
  .conciliacion__imagen img {
    width: 350px;
  }
  .conciliacion__ventajas {
    gap: 20px;
  }
  .conciliacion__listado {
    flex-direction: column;
    width: 90%;
    margin: auto;
    justify-content: center;
  }
  .conciliacion__listado * {
    text-align: center;
  }
  .emprendedores {
    background-size: cover;
  }
  .emprendedores__titulo {
    font-size: 20px;
    line-height: 25px;
  }
  .emprendedores__subtitulo {
    font-size: 16px;
  }
  .emprendedores__content {
    padding: 50px 0;
  }
  .ayuda__titulo {
    font-size: 30px;
  }
  .ayuda__card-text {
    font-size: 18px;
  }
  .pie__datos {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }
  .pie__datos p {
    text-align: center;
    font-size: 14px;
  }
  .pie__listado {
    flex-direction: column;
    align-items: center;
  }
  .wrap {
    max-width: 95%;
    margin: auto;
  }
  .no-mobile {
    display: none;
  }
  .btn {
    font-size: 18px;
  }
}
@media screen and (max-width: 1068px) and (min-width: 1027px) {
  .hero__titulo {
    font-size: 45px;
  }
  .hero__li {
    font-size: 20px;
  }
}
@media screen and (max-width: 1027px) and (min-width: 700px) {
  .hero__interior {
    flex-direction: column-reverse;
  }
  .hero__titulo {
    font-size: 45px;
  }
  .hero__li {
    font-size: 20px;
  }
  .especialistas__soluciones {
    flex-direction: column-reverse;
    justify-content: center;
  }
  .especialistas__blocimg {
    display: flex;
    justify-content: center;
  }
  .especialistas__ventajas {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  .conciliacion__content {
    flex-direction: column-reverse;
  }
  .conciliacion__info {
    justify-content: center;
  }
  .conciliacion__imagen {
    justify-content: center;
  }
}
@media screen and (max-width: 977px) {
  .dudas__contenido {
    flex-direction: column;
  }
  .dudas__content {
    text-align: center;
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 780px) and (min-width: 699px) {
  .pie__datos {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    align-items: center;
  }
}/*# sourceMappingURL=home.css.map */


.productos__cards{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px;
}
.productos__titulos{
  background-color: #031549;
  border-radius: 16px 16px 0 0 ;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 30px 0;
}
.productos__nombre, .productos__precio, .productos__precio sup, .productos__periodo{
  color: var(--color-brand-neutral-lightest);
  text-align: center;
}

.productos__nombre{
  font-family: "Poppins", Sans-serif;
  font-size: 24px;
  font-weight: 700;
}
.productos__precio{
  font-family: "Poppins", Sans-serif;
  font-size: 56px;
  font-weight: 700;
  line-height: 0.9;
  margin-top: 30px;
}
.productos__precio sup{
  font-size: 22px;
}
.productos__periodo{
  font-size: 17px;
}
.productos__descripciones li {
  list-style: none;
  display: flex;
  align-items: center;
}
.productos__descripciones{
  padding: 30px 20px 30px 20px;
  background-color: var(--color-brand-neutral-lightest);
  border-radius: 0 0 16px 16px;
}
.productos__listado li {
  font-size: 16px;
  margin-bottom: 10px;
} 
.productos__listado li svg{
  width: 20px;
  margin-right: 5px;
}
.productos{
  margin-top: 20px;
  margin-bottom: 50px;
}
.productos__link{
  background-color: var(--color-brand-neutral-darkest);
  color: var(--color-brand-neutral-lightest);
  font-size: 18px;
  font-weight: 800;
  border-radius: 50px;
  padding: 15px 20px;
  display: block;
  width: max-content;
  margin: auto; 
  margin-top: 40px;
}
.productos__info{
  font-size: 13px;
  text-align: center;
  margin-top: 10px;
}
.productos__link:hover{
  background-color: var(--color-brand-medium);
}
.mgb-special{
  margin-bottom: 74px;
}
