from datetime import date

from django.contrib import admin
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db import models
from django.dispatch import receiver
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.db.models.signals import post_save, post_delete
from muaytax.signals import disable_for_load_data

from muaytax.app_workers.constants import *


class Worker(models.Model):
    # id -> AutoGen

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="worker_seller",
        verbose_name="Vendedor",
    )

    worker_type = models.CharField(
        max_length=100,
        choices=WORKER_TYPE,
        blank=True,
        null=True,
        verbose_name="Tipo de trabajador",
    )

    first_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="Nombre",
    )

    last_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="Apellidos",
    )

    nif_nie = models.CharField(
        max_length=50,
        verbose_name="DNI/NIE",
    )

    address = models.ForeignKey(
        "address.Address",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="worker_address",
        verbose_name="Dirección del trabajador",
    )

    birthday = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de nacimiento"
    )

    marital_status = models.CharField(
        max_length=100,
        choices=MARITAL_STATUS,
        blank=True,
        null=True,
        verbose_name="Situación familiar",
    )

    spouse_nif_nie = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="NIF/NIE del cónyuge",
        help_text="Sólo si el trabajador está casado",
    )

    disability = models.CharField(
        max_length=100,
        choices=DISSABILITY,
        blank=True,
        null=True,
        verbose_name="Discapacidad",
    )

    contract_type = models.ForeignKey(
        "dictionaries.ContractType",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name="Tipo de contrato",
    )

    geographical_mobility = models.CharField(
        max_length=100,
        choices=[
            ("yes", _("Sí")),
            ("no", _("No")),
        ],
        blank=True,
        null=True,
        verbose_name="Movilidad geográfica",
    )

    is_titular = models.CharField(
        max_length=50,
        choices=IS_TITULAR,
        blank=True,
        null=True,
        verbose_name="Titular unidad de convivencia",
    )

    nif_nie_titular = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="NIF/NIE del titular",
        help_text="Sólo si no es titular de la unidad",
    )

    nuss_naf = models.CharField(
        blank=True,
        null=True,
        max_length=12,
        verbose_name="NUSS/NAF",
        help_text="Número de afiliación a la Seguridad Social",
    )

    nie_upload = models.FileField(
        "DNI/NIE",
        blank=True,
        null=True,
        upload_to="uploads/worker_nie/",
        validators=[FileExtensionValidator(["pdf", "jpg", "png", "jpeg", "JPG", "PNG", "JPEG", "PDF"])]
    )

    start_date = models.DateField(
        null=True,
        verbose_name="Fecha de alta",
    )

    end_date = models.DateField(
        null=True,
        verbose_name="Fecha de baja",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    @property
    def full_name(self):
        return self.first_name + " " + self.last_name

    @property
    def worker_type_name(self):
        return self.get_worker_type_display()

    @property
    def age(self):
        if self.birthday:
            # Obtén la fecha actual
            today = date.today()
            # Calcula la diferencia en años
            return today.year - self.birthday.year - (
                (today.month, today.day) < (self.birthday.month, self.birthday.day)
            )
        else:
            return None

    age.fget.short_description = _("Edad")

    class Meta:
        verbose_name = "Trabajador"
        verbose_name_plural = "Trabajadores"

    def __str__(self):
        if self.first_name and self.last_name:
            return self.nif_nie + " " + self.full_name
        else:
            return self.nif_nie

    def get_absolute_url(self):
        return reverse("app_providers:detail", kwargs={"pk": self.pk})

    def clean(self):
        if self.is_titular == "no" and self.nif_nie_titular == None:
            raise ValidationError(
                _("Si el trabajador no es titular de la unidad de convivencia, debe indicar el NIF/NIE del titular")
            )
        if self.marital_status == "married" and self.spouse_nif_nie == None:
            raise ValidationError(
                _("Si el trabajador está casado, debe indicar el NIF/NIE del cónyuge")
            )
        if self.age and self.age < 18:
            raise ValidationError({'birthday': _("La edad mínima debe ser de 18 años")})

# @receiver(post_save, sender=Worker)
# @disable_for_load_data
# def after_sellervat_save(sender, instance, created, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)

# @receiver(post_delete, sender=Worker)
# @disable_for_load_data
# def after_sellervat_delete(sender, instance, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)





@admin.register(Worker)
class WorkersAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "first_name",
        "last_name",
        "seller",
        "nif_nie",
        "nuss_naf",
        "birthday",
    ]
    search_fields = [
        "id",
        "first_name",
        "last_name",
        "seller__shortname",
        "nif_nie",
        "nuss_naf",
        "birthday",
    ]
