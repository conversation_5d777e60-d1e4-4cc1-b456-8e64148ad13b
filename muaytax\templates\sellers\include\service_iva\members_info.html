{% load crispy_forms_filters crispy_forms_field %}
<style>
    /* Estilos generales */
    .missing-data { color: red; font-weight: bold; }

    /* Estilos de la tabla */
    .table-partners {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border: 2px solid #CBD5E0;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .table-partners thead tr {
        background-color: #F2F6FF;
        height: 36px;
    }
    
    .table-partners thead th {
        text-align: left;
        padding: 8px 12px;
        border-bottom: 2px solid #CBD5E0;
        font-weight: 600;
        white-space: nowrap;
    }

    .table-partners tbody tr {
        height: 40px;
    }
    
    .table-partners tbody td {
        padding: 6px 12px;
        border-bottom: 2px solid #CBD5E0;
        white-space: nowrap;
        font-weight: 500;
    }
    
    /* Estilos específicos de las columnas */
    .table-partners .col-name { 
        width: 70%;
        min-width: 100px; 
    }
    
    .table-partners .col-icons { 
        width: 10%; 
        text-align: center; 
    }
    .table-partners .col-buttons {
        width: 15%;
        text-align: right;
        padding-right: 60px; /* Asegura alineación con margen derecho */
    }
    /* Efecto hover en filas de la tabla */
    .table-partners tbody tr:hover {
        background-color: #F8FAFC; /* Un gris claro para resaltar la fila */
        transition: background-color 0.2s ease-in-out;
    }

    /* Contenedor de iconos */
    .icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }
    
    /* Botones */
    .btn-dark-blue, .btn-green {
        height: 36px;
        padding: 6px 12px;
        min-width: 80px;
        border-radius: 50px;
        font-weight: 700;
        border: solid 2px;
        transition: all 0.2s ease;
    }
    
    .btn-dark-blue {
        background-color: var(--color-black-blue);
        color: white;
        border-color: var(--color-black-blue);
    }
    .btn-dark-blue:hover { background-color: white !important; color: var(--color-black-blue) !important; }

    .btn-green {
        background-color: var(--color-green-dark);
        color: white;
        border-color: var(--color-green-dark);
    }
    .btn-green:hover { background-color: white !important; color: var(--color-green-dark) !important; }

    /* Estilos del botón "Editar" */
    .edit-partner-btn {
        height: 30px;
        min-width: 30px;
        padding: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% if is_new_form %}
<div class="alert alert-info mt-2" role="alert">
    <strong>Información General de los Socios de la Empresa:</strong>
    <br>
    (Es obligatorio al menos agregar la información del miembro principal).
</div>
{% endif %}

<div style="display: flex; justify-content: end; margin-bottom: 1%; margin-right: 20px" id="add-button-container"></div>
<!-- Mensaje de validación (se mostrará si hay socios confirmados) -->
<div id="alert-validate" class="alert alert-danger mt-4 d-flex align-items-center text-start rounded-3 d-none"
    style="padding: 8px 12px; white-space: nowrap; display: none;">
    <i class="mu mu-warning me-2" style="font-size: 30px; color: #E0364C;"></i>
    <strong>Atención:</strong> Es obligatorio Validar y/o Completar la información de los socios que ya están registrados.
</div>

<!-- Mensaje de bloqueo (se mostrará si no hay socios confirmados) -->
<div id="alert-no-partners" class="alert alert-danger mt-4 d-flex align-items-center text-start rounded-3 d-none"
    style="padding: 8px 12px; white-space: nowrap; display: none;">
    <i class="mu mu-warning me-2" style="font-size: 30px; color: #E0364C;"></i>
    <strong>Atención:</strong> No podrá finalizar sin haber registrado e incluido al menos a un miembro.
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div id="partners_table" class="table-container" {% if not partners_formatted %}style="visibility: hidden; opacity: 0; height: 0;"{% endif %}>
            <table class="table-partners">
                <thead>
                </thead>
                <tbody>
                    {% for partner in partners_formatted|dictsortreversed:"confirmation_status" %}
                    <tr id="existing-partner-{{ partner.id }}" data-confirmed="{{ partner.confirmation_status|yesno:'true,false' }}">
                        <!-- Columna Nombre -->
                        <td class="col-name">{{ partner.name }} {{ partner.last_name }}</td>

                        <!-- Columna Íconos -->
                        <td class="col-icons">
                            <div class="icon-container">
                                {% if partner.confirmation_status %}
                                    {% if partner.end_date %}
                                        <!-- Ícono de "No incluido" -->
                                        <span style="color: red; font-weight: bold;">
                                            <svg data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                title="Socio NO incluido en el Formulario"
                                                width="30" height="30"
                                                viewBox="0 0 30 30"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <!-- Círculo exterior -->
                                                <circle cx="15" cy="15" r="12" fill="white" stroke="#E0364C" stroke-width="2"/>
                                                <!-- Símbolo de "X" corregido y centrado -->
                                                <path d="M11 11L19 19" stroke="#E0364C" stroke-width="2" stroke-linecap="round"/>
                                                <path d="M19 11L11 19" stroke="#E0364C" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </span>
                                    {% else %}
                                        <!-- Ícono Check Verde -->
                                        <span class="text-success" style="font-weight: bold;">
                                            <svg data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                title="Socio incluido en formulario"
                                                width="30" height="30"
                                                viewBox="0 0 30 30"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <!-- Círculo exterior -->
                                                <circle cx="15" cy="15" r="12" fill="white" stroke="#00AD65" stroke-width="2"/>
                                                <!-- Check corregido y centrado -->
                                                <path d="M10.5 15L14 19L20 11"
                                                    stroke="#00AD65"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    fill="none"/>
                                            </svg>
                                        </span>
                                    {% endif %}
                                {% else %}
                                        <!-- Botón Validar -->
                                        <button type="button" class="btn btn-sm btn_dark_blue add-partner-btn ms-2"
                                        data-partner-id="{{ partner.id }}"
                                        data-partner-start-date="{{ partner.start_date }}"
                                        data-partner-end-date="{{ partner.end_date }}"
                                        data-partner-name="{{ partner.name }}"
                                        data-partner-lastname="{{ partner.last_name }}"
                                        data-partner-id-number-dni="{{ partner.id_number_dni }}"
                                        data-partner-id-number-passport="{{ partner.id_number_passport }}"
                                        data-partner-shares-percentage="{{ partner.shares_percentage }}"
                                        data-partner-address-address="{{ partner.address.address }}"
                                        data-partner-address-zip="{{ partner.address.address_zip }}"
                                        data-partner-address-city="{{ partner.address.address_city }}"
                                        data-partner-address-state="{{ partner.address.address_state }}"
                                        data-partner-address-country="{{ partner.address.address_country_name }}"
                                        data-partner-address-country-code="{{ partner.address.address_country_iso_code }}"
                                        data-partner-dni-file="{{ partner.documents.dni_file }}"
                                        data-partner-passport-file="{{ partner.documents.passport_file }}">
                                        Validar
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                        <td class="col-buttons">
                            <!-- Columna Botones -->
                            {% if partner.confirmation_status %}
                                <!-- Botón Editar -->
                                <button type="button" class="btn btn-sm edit-partner-btn ms-2"
                                    data-partner-id="{{ partner.id }}"
                                    data-partner-start-date="{{ partner.start_date }}"
                                    data-partner-end-date="{{ partner.end_date }}"
                                    data-partner-name="{{ partner.name }}"
                                    data-partner-lastname="{{ partner.last_name }}"
                                    data-partner-id-number-dni="{{ partner.id_number_dni }}"
                                    data-partner-id-number-passport="{{ partner.id_number_passport }}"
                                    data-partner-shares-percentage="{{ partner.shares_percentage }}"
                                    data-partner-address-address="{{ partner.address.address }}"
                                    data-partner-address-zip="{{ partner.address.address_zip }}"
                                    data-partner-address-city="{{ partner.address.address_city }}"
                                    data-partner-address-state="{{ partner.address.address_state }}"
                                    data-partner-address-country="{{ partner.address.address_country_name }}"
                                    data-partner-address-country-code="{{ partner.address.address_country_iso_code }}"
                                    data-partner-dni-file="{{ partner.documents.dni_file }}"
                                    data-partner-passport-file="{{ partner.documents.passport_file }}">
                                    <svg width="30" height="30" class="edit_btn" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"
                                        data-bs-toggle="tooltip" 
                                        data-bs-placement="top" 
                                        title="Editar Socio">
                                        <path d="M25.4 7.35L21.48 4.45C21.36 4.36 21.18 4.40 21.07 4.54L10.42 18.92C10.39 18.97 10.37 19.02 10.36 19.08L9.67 24.10C9.64 24.32 9.84 24.47 10.05 24.38L14.65 22.26C14.70 22.23 14.75 22.19 14.78 22.15L25.43 7.76C25.54 7.62 25.52 7.44 25.40 7.35Z" 
                                            stroke="#031549" stroke-width="2" stroke-miterlimit="10"/>
                                        <path d="M4.35 26.85H26.55" 
                                            stroke="#031549" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                                    </svg>
                                </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <div id="partners-validation-summary" class="mt-4" style="display: none;">
                <h5 class="mb-3">Resumen de errores encontrados por el gestor:</h5>
                <div id="partners-error-cards-container" class="d-flex flex-column gap-3"></div>
            </div>
            
        </div>
    </div>
</div>

<input type="hidden" id="processed_partners_count" value="{{ processed_partners_count }}">

<script>
    // Variables globales DJ

    // Variables con Datos JSON (debug)
    let debug = {{ debug|yesno:"true,false" }};
    let partnersFormJSON = {{ processed_partners|safe }}; // Socios procesados desde el formulario
    let partnersJSON = {{ partners_json|safe }}; // Lista de socios recuperados desde la BD
    let processedCount = parseInt(document.getElementById("processed_partners_count").value) || 0; // Número de socios procesados
    if (debug) console.log("Socios json_form", partnersFormJSON);
    if (debug) console.log("Socios TOTALES en la DB", partnersJSON);
    if (debug) console.log("Número de socios procesados", processedCount);

    // Inicializa tooltips Bootstrap, eliminando los anteriores y creando nuevos
    function initTooltips(partnerId = null) {
        window.debugLog(`Ejecutando initTooltips(${partnerId})...`);
    
        // Eliminar cualquier instancia previa de tooltip para evitar acumulación
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(el => {
            let tooltipInstance = bootstrap.Tooltip.getInstance(el);
            if (tooltipInstance) {
                tooltipInstance.dispose(); // Destruir cualquier tooltip previo
            }
        });
    
        // Seleccionar todos los tooltips que deben activarse
        let selector = partnerId ? `#existing-partner-${partnerId} [data-bs-toggle="tooltip"]` : '[data-bs-toggle="tooltip"]';
        let tooltipElements = document.querySelectorAll(selector);
        
        window.debugLog(`Elementos con tooltip encontrados: ${tooltipElements.length}`);
    
        tooltipElements.forEach((el, index) => {
            window.debugLog(`🛠 Tooltip ${index + 1} para ID ${partnerId || 'TODOS'}:`, el.getAttribute("title") || el.getAttribute("data-bs-original-title"));
    
            // Crear un nuevo tooltip desde cero
            new bootstrap.Tooltip(el);
        });
    
        window.debugLog("initTooltips() finalizado.");
    }
    
    // Función para actualizar los mensajes de alerta según el estado de los socios
    function updateAlertMessages() {
        window.debugLog(`EJECURANDO la funcion updateAlertMessages`)

        let tableRows = Array.from(document.querySelectorAll(".table-partners tbody tr"));
        let confirmedPartners = Array.from(tableRows).some(row =>
            row.querySelector(".add-partner-btn") === null // Ningún botón de validar
        );
    
        let alertValidate = document.getElementById("alert-validate");
        let alertNoPartners = document.getElementById("alert-no-partners");
    
        if (tableRows.length === 0 || tableRows.some(row => row.querySelector(".add-partner-btn"))) {
            // Mostrar mensaje de bloqueo si no hay socios o alguno no validado
            if (alertNoPartners) alertNoPartners.classList.remove("d-none");
            if (alertValidate) alertValidate.classList.remove("d-none");
        } else {
            // Todo validado, ocultar alertas
            if (alertNoPartners) alertNoPartners.classList.add("d-none");
            if (alertValidate) alertValidate.classList.add("d-none");
        }
    }

    // Función para ordenar la tabla de socios
    function sortTable() {
        let tableBody = document.querySelector(".table-partners tbody");
    
        if (!tableBody) {
            window.debugLog("No hay socios registrados. La tabla no existe.");
            return; // Salir de la función si no hay tabla
        }
    
        let rows = Array.from(tableBody.querySelectorAll("tr"));
    
        rows.sort((rowA, rowB) => {
            let isConfirmedA = rowA.querySelector(".col-icons span.text-success") !== null; // Confirmado
            let isConfirmedB = rowB.querySelector(".col-icons span.text-success") !== null;
            let isBajaA = rowA.querySelector(".col-icons span[style*='color: red']") !== null; // Baja
            let isBajaB = rowB.querySelector(".col-icons span[style*='color: red']") !== null;
    
            if (!isConfirmedA && !isBajaA) return -1; // Sin validar primero
            if (!isConfirmedB && !isBajaB) return 1;
            if (isConfirmedA && !isBajaA) return -1; // Confirmados después
            if (isConfirmedB && !isBajaB) return 1;
            return 0; // Bajas al final
        });
    
        // Volver a agregar las filas ordenadas en la tabla
        rows.forEach(row => tableBody.appendChild(row));
    }

    // Función para obtener los íconos de estado
    function getStatusIcon(type) {
        const icons = {
            "baja_confirmada": `
                <span style="color: red; font-weight: bold;">
                    <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="12" fill="white" stroke="#E0364C" stroke-width="2"/>
                        <path d="M11 11L19 19" stroke="#E0364C" stroke-width="2" stroke-linecap="round"/>
                        <path d="M19 11L11 19" stroke="#E0364C" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </span>
            `,
            "confirmado_agregado": `
                <span style="font-weight: bold;">
                    <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="12" fill="white" stroke="#00AD65" stroke-width="2"/>
                        <path d="M10.5 15L14 19L20 11" stroke="#00AD65" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
            `
        };
        return icons[type] || "";
    }

    // Función para generar botones dinámicamente
    function getButton(type, buttonData = {}) {
        let dataAttrs = Object.entries(buttonData) // Usamos el parámetro correcto
            .filter(([_, value]) => value !== undefined && value !== null) // Evita valores vacíos
            .map(([key, value]) => `data-${key}="${value}"`).join(" ");
            
        const buttons = {
            "edit": `
                <button type="button" class="btn btn-sm edit-partner-btn ms-2" ${dataAttrs}>
                    <svg width="30" height="30" class="edit_btn" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"
                        data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Editar Socio">
                        <path d="M25.4 7.35L21.48 4.45C21.36 4.36 21.18 4.40 21.07 4.54L10.42 18.92C10.39 18.97 10.37 19.02 10.36 19.08L9.67 24.10C9.64 24.32 9.84 24.47 10.05 24.38L14.65 22.26C14.70 22.23 14.75 22.19 14.78 22.15L25.43 7.76C25.54 7.62 25.52 7.44 25.40 7.35Z" 
                            stroke="#031549" stroke-width="2" stroke-miterlimit="10"/>
                        <path d="M4.35 26.85H26.55" 
                            stroke="#031549" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
                    </svg>
                </button>
            `,
            "validate": `
                <button type="button" class="btn btn-sm btn_dark_blue add-partner-btn ms-2" ${dataAttrs}>
                    Validar
                </button>
            `
        };
        return buttons[type] || "";
    }
    
    // Muestra un enlace al archivo si existe
    function updateFileLink(element, filePath) {
        if (!element) return;
        element.innerHTML = filePath ? `<a href="${filePath}" target="_blank">${filePath.split('/').pop()}</a>` : "";
    }

    // Formatea fechas tipo a "YYYY-MM-DD"
    function formatDate(dateStr) {
        if (!dateStr || dateStr.toLowerCase() === "none") return "";  // Manejar valores nulos
    
        // Si ya está en formato "YYYY-MM-DD", devolverlo sin modificar
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            return dateStr;
        }
    
        let months = {
            "Enero": "01", "Febrero": "02", "Marzo": "03", "Abril": "04",
            "Mayo": "05", "Junio": "06", "Julio": "07", "Agosto": "08",
            "Septiembre": "09", "Octubre": "10", "Noviembre": "11", "Diciembre": "12"
        };
    
        // 💡 Detectar si la fecha viene con "de"
        let regex = /^(\d{1,2})\s+de\s+(\w+)\s+de\s+(\d{4})$/;
        let match = dateStr.match(regex);
    
        if (match) {
            let day = match[1].padStart(2, '0'); // Asegurar dos dígitos en día
            let month = months[match[2]];  // Convertir mes a número
            let year = match[3];
            return `${year}-${month}-${day}`;
        } else {
            window.debugLog(`Formato de fecha no reconocido: "${dateStr}"`);
            return "";
        }
    }

    // Función que abre el modal para editar o agregar socio, rellenando los campos si ya existe info
    function openPartnerModal(button) {
        let formElement = document.getElementById("addPartnerForm");
        let modalElement = document.getElementById("addPartnerModal");  
        let modalTitle = document.querySelector("#addPartnerModal .modal-title");
        let endDateInput = document.getElementById("id_end_date");
        let endDateContainer = endDateInput.closest(".form-group");  // Contenedor del campo
        let alertEndDate = document.getElementById("alertEndDate");

        if (!modalElement) {
            window.debugLog("Error: No se encontró el modal 'addPartnerModal' en el DOM.");
            return;
        }

        // Detectar si es un socio nuevo
        let isNewPartner = button.classList.contains("new-partner-btn");
        // Detectar si es edición real (edit-partner-btn)
        let isRealEdit = button.classList.contains("edit-partner-btn");

        // Si es un socio nuevo, limpiar el formulario y establecer modo "add"
        if (isNewPartner) {
            formElement.dataset.mode = "add";
            formElement.dataset.originalPartnerId = "";
            modalTitle.textContent = "Agregar Nuevo Socio";
            formElement.reset(); // Limpiar formulario
            // Asegurar que los nombres de archivos también se limpien
            endDateInput.value = "";
            endDateContainer.classList.add("d-none"); // Ocultar campo de baja
            alertEndDate.classList.add("d-none");
            document.getElementById("dni_file_link").innerHTML = "";
            document.getElementById("passport_file_link").innerHTML = "";
            window.debugLog("Modo: Nuevo Socio (Formulario Limpio)");
        } else {
            let partnerId = button.dataset.partnerId || "";
            formElement.dataset.originalPartnerId = partnerId;
            endDateContainer.classList.remove("d-none"); // Ocultar campo de baja
            // Si es "edit-partner-btn", es edición real, si no, se trata como "add"
            formElement.dataset.mode = isRealEdit ? "edit" : "add-real";
            modalTitle.textContent = isRealEdit ? "Editar Socio" : "Agregar Socio Existente";
            formElement.querySelector("#id_name").value = button.dataset.partnerName || "";
            formElement.querySelector("#id_last_name").value = button.dataset.partnerLastname || "";
            formElement.querySelector("#id_number_dni").value = button.dataset.partnerIdNumberDni || "";
            formElement.querySelector("#id_number_passport").value = button.dataset.partnerIdNumberPassport || "";
            formElement.querySelector("#id_shares_percentage").value = parseFloat(button.dataset.partnerSharesPercentage?.replace('%', '').trim()) || 0;
            formElement.querySelector("#id_address").value = button.dataset.partnerAddressAddress || "";
            formElement.querySelector("#id_address_zip").value = button.dataset.partnerAddressZip || "";
            formElement.querySelector("#id_address_city").value = button.dataset.partnerAddressCity || "";
            formElement.querySelector("#id_address_state").value = button.dataset.partnerAddressState || "";

        
            let countryField = formElement.querySelector("#id_address_country");
            let countryOption = [...countryField.options].find(opt => opt.value === button.dataset.partnerAddressCountryCode);

            if (countryOption) {
                countryField.value = button.dataset.partnerAddressCountryCode;
            } else {
                window.debugLog(` El país con código ISO "${button.dataset.partnerAddressCountryCode}" no está en las opciones.`);
                countryField.value = "";
            }
            
            let startDate = button.dataset.partnerStartDate;
            let endDate = button.dataset.partnerEndDate;
            
            window.debugLog("Fechas recuperadas antes de conversión:", { startDate, endDate });
            
            if (startDate) {
                let startDateInput = formElement.querySelector("#id_start_date");
                let formattedStartDate = formatDate(startDate);
                window.debugLog("Fecha de alta formateada:", formattedStartDate);
                startDateInput.value = formattedStartDate;
            }
            
            let endDateInput = formElement.querySelector("#id_end_date");
            if (endDate) {
                let formattedEndDate = formatDate(endDate);
                window.debugLog("Fecha de baja formateada:", formattedEndDate);
                endDateInput.value = formattedEndDate;
            } else {
                endDateInput.value = "";
                window.debugLog("Sin fecha de baja, se limpia el campo.");
            }

            // Mostrar archivos si existen
            let dniFileLink = document.getElementById("dni_file_link");
            let passportFileLink = document.getElementById("passport_file_link");

            updateFileLink(dniFileLink, button.dataset.partnerDniFile);
            updateFileLink(passportFileLink, button.dataset.partnerPassportFile);

            window.debugLog(`Modo: ${isRealEdit ? "Editar Socio" : "Agregar Socio Existente"}`);
        }
        
        // Asegurar que se obtiene una instancia del modal antes de mostrarlo
        let modalInstance = bootstrap.Modal.getInstance(modalElement);
        // Verifica si el modal ya tiene una instancia activa
        if (!modalInstance) {
            modalInstance = new bootstrap.Modal(modalElement);
        }
        modalInstance.show(); // Ahora abrimos el modal manualmente después de cargar los datos
    }

    // Función para actualizar el estado de un socio en la UI después de agregarlo o editarlo
    function updatePartnerStatus(partnerData) {
        window.debugLog(`EJECURANDO la funcion updatePartnerStatus`)
        let tableContainer = document.querySelector(".table-container");
        let tableBody = document.querySelector(".table-partners tbody");

        // Si la tabla no existe, crearla
        if (!tableContainer) {
            window.debugLog("No hay tabla. Creándola...");

            let newTableContainer = document.createElement("div");
            newTableContainer.classList.add("table-container");
            newTableContainer.style.visibility = "hidden"; // Oculta hasta que se agregue el primer socio
            newTableContainer.style.opacity = "0";
            newTableContainer.style.height = "0";

            newTableContainer.innerHTML = `
                <table class="table-partners">
                    <thead></thead>
                    <tbody></tbody>
                </table>
            `;

            // Insertar la tabla en el DOM antes del formulario
            let parentContainer = document.querySelector(".row.mb-4 .col-md-12");
            if (parentContainer) {
                parentContainer.appendChild(newTableContainer);
            }

            tableContainer = newTableContainer;
            tableBody = tableContainer.querySelector(".table-partners tbody");
        }

        // Mostrar la tabla si estaba oculta
        if (tableContainer.style.visibility === "hidden") {
            window.debugLog("Tabla vacía detectada. Mostrándola...");
            tableContainer.style.visibility = "visible";
            tableContainer.style.opacity = "1";
            tableContainer.style.height = "auto";
        }

        let partnerId = partnerData.partner_id;
        let confirmedStatus = partnerData.confirmation_status ?? false;
        let endDate = partnerData.end_date ?? "";
        let startDate = partnerData.start_date ?? "";
        let countryCode = partnerData.address?.address_country_code || "ES";
    
        window.debugLog("Datos recibidos para actualización:", partnerData);
        window.debugLog(`Fecha de Alta: ${startDate} - Fecha de Baja: ${endDate}`);
        window.debugLog(`Código de país recuperado: ${countryCode}`);

        // Si la tabla está oculta, mostrarla suavemente
        if (tableContainer.style.visibility === "hidden") {
            window.debugLog("Tabla vacía detectada. Mostrándola...");
            tableContainer.style.visibility = "visible";
            tableContainer.style.opacity = "1";
            tableContainer.style.height = "auto";
        }
    
        let existingRow = document.querySelector(`#existing-partner-${partnerId}`);

        const setStatusAttributes = (rowElement) => {
            rowElement.setAttribute("data-confirmed", confirmedStatus ? "true" : "false");
            rowElement.setAttribute("data-baja", endDate ? "true" : "false");
        };
    
        // Diccionario de datos para botones
        let partnerDataDict = {
            "partner-id": partnerId,
            "partner-name": partnerData.name || "",
            "partner-lastname": partnerData.last_name || "",
            "partner-start-date": startDate || "",
            "partner-end-date": endDate || "",
            "partner-id-number-dni": partnerData.id_number_dni || "",
            "partner-id-number-passport": partnerData.id_number_passport || "",
            "partner-shares-percentage": partnerData.shares_percentage || "",
            "partner-address-address": partnerData.address?.address || "",
            "partner-address-zip": partnerData.address?.address_zip || "",
            "partner-address-city": partnerData.address?.address_city || "",
            "partner-address-state": partnerData.address?.address_state || "",
            "partner-address-country": partnerData.address?.address_country_name || "",
            "partner-address-country-code": countryCode,
            "partner-dni-file": partnerData.documents?.dni_file || "",
            "partner-passport-file": partnerData.documents?.passport_file || ""
        };
    
        if (existingRow) {
            // Si la fila ya existe, actualizarla
            existingRow.querySelector(".col-name").textContent = `${partnerData.name} ${partnerData.last_name}`;
    
            existingRow.querySelector(".col-icons").innerHTML = confirmedStatus
                ? (endDate ? getStatusIcon("baja_confirmada") : getStatusIcon("confirmado_agregado"))
                : getButton("validate", partnerDataDict);
    
            existingRow.querySelector(".col-buttons").innerHTML = confirmedStatus
                ? getButton("edit", partnerDataDict)
                : "";
            setStatusAttributes(existingRow);
        } else {
            // Si el socio es nuevo, crear una nueva fila dinámicamente
            let newRow = document.createElement("tr");
            newRow.id = `existing-partner-${partnerId}`;
            newRow.innerHTML = `
                <td class="col-name">${partnerData.name} ${partnerData.last_name}</td>
                <td class="col-icons">
                    <div class="icon-container">
                        ${confirmedStatus 
                            ? (endDate ? getStatusIcon("baja_confirmada") : getStatusIcon("confirmado_agregado"))
                            : getButton("validate", partnerDataDict)}
                    </div>
                </td>
                <td class="col-buttons">
                    ${confirmedStatus ? getButton("edit", partnerDataDict) : ""}
                </td>
            `;
            setStatusAttributes(newRow);
            // Agregar la nueva fila a la tabla
            tableBody.appendChild(newRow);
        }
    
        // Reasignar eventos y actualizar la tabla
        attachEventListenersToButtons();
        sortTable();
        updateAlertMessages();
        renderPartnersSummaryTable();
        setTimeout(() => initTooltips(partnerId), 50);

    }
    
    // Función para guardar el socio mediante AJAX y actualizar la UI dinámicamente
    function savePartnerAjax() {
        let formElement = document.getElementById('addPartnerForm');
        let formData = new FormData(formElement);
        let requiredFields = formElement.querySelectorAll('[required]');
        let modalElement = document.getElementById("addPartnerModal");
        let modal = bootstrap.Modal.getInstance(modalElement);
        let mode = formElement.dataset.mode;
        let originalPartnerId = formElement.dataset.originalPartnerId || null;

        window.debugLog('%c Modo:', 'color: #007bff; font-weight: bold;', mode);
        window.debugLog('%c ID antes de enviar:', 'color: #009688; font-weight: bold;', originalPartnerId);
        // Verificar si hay campos vacíos
        let emptyFields = [];
        requiredFields.forEach(field => {
            if (field.type === "file") {
                const fileLinkId = `${field.name}_link`;  // dni_file_link o passport_file_link
                const existingFile = document.getElementById(fileLinkId);
                const hasExistingFile = existingFile && existingFile.textContent.trim() !== "";
                if (!field.value && !hasExistingFile) {
                    emptyFields.push(field.labels[0]?.innerText || field.name);
                }
            } else {
                if (!field.value.trim()) {
                    emptyFields.push(field.labels[0]?.innerText || field.name);
                }
            }
        });

    
        if (emptyFields.length > 0) {
            modal.hide();
            window.debugLog('%c Error: Faltan campos obligatorios:', 'color: red; font-weight: bold;', emptyFields);
    
            Swal.fire({
                icon: 'error',
                title: 'Campos Incompletos',
                text: 'Todos los campos son obligatorios. Faltan: ' + emptyFields.join(', '),
                confirmButtonColor: '#dc3545',
                allowOutsideClick: false
            }).then(() => modal.show());
            
            return;
        }
    
        // Validación del porcentaje de acciones
        let sharesPercentageInput = formElement.querySelector("#id_shares_percentage");
        let newSharesPercentage = parseFloat(sharesPercentageInput.value) || 0;

        // 🔹 Sumar los porcentajes de socios ya confirmados (íconos verdes)
        let totalConfirmed = 0;
        document.querySelectorAll(".table-partners tbody tr").forEach(row => {
            let hasCheck = row.querySelector(".col-icons svg path[d='M10.5 15L14 19L20 11']");
            if (hasCheck) {
                let shareText = row.querySelector(".col-buttons button")?.dataset.partnerSharesPercentage;
                let value = parseFloat(shareText?.replace("%", "") || 0);
                totalConfirmed += value;
            }
        });

        // 🔹 Si estamos editando, debemos restar el valor original del socio que se edita
        if (mode !== "add" && originalPartnerId) {
            let originalRow = document.querySelector(`#existing-partner-${originalPartnerId}`);
            if (originalRow) {
                let originalValue = parseFloat(originalRow.querySelector(".col-buttons button")?.dataset.partnerSharesPercentage || 0);
                totalConfirmed -= originalValue;
            }
            formData.append("partner_id", originalPartnerId);
        }

        // 🔹 Sumar el nuevo porcentaje propuesto
        let totalProjected = totalConfirmed + newSharesPercentage;

        if (totalProjected > 100) {
            modal.hide();
            Swal.fire({
                icon: 'error',
                title: 'Porcentaje excedido',
                text: `La suma de porcentajes supera el 100%. Actualmente tienes ${totalConfirmed}%.`,
                confirmButtonColor: '#dc3545',
                timer: 2500,
                showConfirmButton: false
            }).then(() => modal.show());
            return;
        }
        
        fetch("{% url 'app_sellers:vat_add_partner' seller.shortname %}", {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(res => res.json())
        .then(data => {
            window.debugLog('%c Respuesta del servidor recibida:', 'color: #4caf50; font-weight: bold;', data);

            if (data.success) {
                window.debugLog('%c Socio agregado/editado correctamente:', 'color: #8bc34a; font-weight: bold;', data.partner_info);
                
                modal.hide();
                Swal.fire({
                    icon: 'success',
                    title: 'Socio guardado correctamente',
                    timer: 2000,
                    showConfirmButton: false
                });
                window.debugLog('%c Socio agregado correctamente:', 'color: #8bc34a; font-weight: bold;', data.partner_info);
                let partnerId = data.partner_info.partner_id;
                let confirmedStatus = data.partner_info.confirmation_status ?? false;
                let endDate = data.partner_info.end_date;
                window.debugLog(`VALUE: partnerId ${partnerId}`)
                window.debugLog(`VALUE: confirmedStatus ${confirmedStatus}`)
                window.debugLog(`VALUE: endDate ${endDate}`)
                // Actualizar la tabla de socios
                reloadPartnersBlock()
                // Actualizar botones de manera reactiva
                updatePartnerStatus(data.partner_info);
                // renderPartnersSummaryTable();     // ✅ Fuerza resumen actualizado
                updateAlertMessages();           // ✅ También alerta si cambia el estado
                setTimeout(() => initTooltips(partnerId), 50);


            } else {
                window.debugLog('%c Error al guardar socio:', 'color: red; font-weight: bold;', data.error);
                modal.hide();
                let title = "Error";
                let text = "Hubo un problema al guardar. Revisa los campos.";

                if (data.error_type === "duplicate_dni") {
                    title = "DNI Duplicado";
                    text = data.message;
                } else if (data.error_type === "duplicate_passport") {
                    title = "Pasaporte Duplicado";
                    text = data.message;
                }

                Swal.fire({
                    icon: 'error',
                    title: title,
                    text: text,
                    confirmButtonColor: '#dc3545'
                }).then(() => modal.show());
            }
        })
        .catch(err => {
            window.debugLog('%c Error de conexión con el servidor:', 'color: red; font-weight: bold;', err);
            modal.hide();
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor.',
                confirmButtonColor: '#dc3545'
            }).then(() => modal.show());
        });
    }
    
    // Asignar evento a los botones de accion
    function attachEventListenersToButtons() {
        document.querySelectorAll(".edit-partner-btn").forEach(button => {
            button.removeEventListener("click", openPartnerModal);
            button.addEventListener("click", function () {
                openPartnerModal(this);
            });
        });
    
        document.querySelectorAll(".add-partner-btn").forEach(button => {
            button.removeEventListener("click", openPartnerModal);
            button.addEventListener("click", function () {
                openPartnerModal(this);
            });
        });
    
        document.addEventListener("click", function (event) {
            if (event.target.classList.contains("new-partner-btn")) {
                openPartnerModal(event.target);
            }
        });
        
    }

    // Inicializa la lógica de socios al cargar el DOM (botones, tabla, eventos)
    document.addEventListener("DOMContentLoaded", function () {
        // Elementos del DOM
        let existingPartnersRow = document.getElementById("existing_partners_row"); // Sección donde se listan los socios existentes
        let existingPartnersContainer = document.getElementById("existing_partners_container"); // Contenedor de socios existentes
        let saveButton = document.getElementById("savePartnerButton"); // Botón para guardar socios
        let addButtonContainer = document.getElementById("add-button-container"); // Botón para agregar socios
        let modalElement = document.getElementById("addPartnerModal"); // Modal para agregar/editar socios
        window.debugLog("Funciona perfecto");
        if (saveButton) {
            saveButton.addEventListener("click", savePartnerAjax);
        }

        if (addButtonContainer) {
            addButtonContainer.innerHTML = `
                <button type="button" class="btn btn-green new-partner-btn"
                    data-bs-toggle="modal"
                    data-bs-target="#addPartnerModal">
                    + Agregar miembro
                </button>
            `;
        }

        // 🔍 Marcar botones de socios con errores pendientes
        let validationPartners = {{ validation_partners|safe }};
        console.log("👀 VALIDATION PARTNERS:", validationPartners);

        const rows = document.querySelectorAll("tr[id^='existing-partner-']");
        console.log("🔍 FILAS DETECTADAS:", rows.length);

        const partnerIdsWithError = new Set();

        for (const key in validationPartners) {
            const validation = validationPartners[key];
            console.log(`📌 Clave: ${key}, Estado: ${validation.status}, Pendiente: ${validation.pending}`);
            if (validation.status === "incorrecto" && validation.pending) {
                const partnerId = key.split("-")[0];
                console.log(`❗ Error detectado para partnerId: ${partnerId}`);
                partnerIdsWithError.add(partnerId);
            }
        }

        rows.forEach(row => {
            const rowId = row.id.replace("existing-partner-", "");
            console.log(`➡ Revisando fila: existing-partner-${rowId}`);
            if (partnerIdsWithError.has(rowId)) {
                console.log(`✅ Aplicando estilo de error a fila de socio: ${rowId}`);
                row.classList.add("partner-error-row");
            }
        });

        // 🧱 Construir tarjetas de errores por socio
        const errorContainer = document.getElementById("partners-error-cards-container");
        const summaryBlock = document.getElementById("partners-validation-summary");

        if (partnerIdsWithError.size > 0 && errorContainer && summaryBlock) {
            summaryBlock.style.display = "block";  // Mostrar contenedor si hay errores

            partnerIdsWithError.forEach(partnerId => {
                const partner = partnersJSON.find(p => p.id.toString() === partnerId);
                const partnerName = partner ? `${partner.name} ${partner.last_name}` : `Socio ID ${partnerId}`;

                let errorListHTML = "";

                for (const key in validationPartners) {
                    if (key.startsWith(partnerId + "-")) {
                        const validation = validationPartners[key];
                        if (validation.status === "incorrecto" && validation.pending) {
                            const fieldName = key.split("-")[1];  // "full", "dni_file", "passport_file", etc.
                            const readableField = {
                                "full": "Información personal",
                                "dni_file": "Documento DNI",
                                "passport_file": "Pasaporte"
                            }[fieldName] || fieldName;

                            errorListHTML += `
                                <div class="partner-error-card-comment">
                                    <strong>${readableField}:</strong> ${validation.comment || "Campo incorrecto"}
                                </div>`;
                        }
                    }
                }

                if (errorListHTML) {
                    const cardHTML = `
                        <div class="partner-error-card">
                            <div class="partner-error-card-title">${partnerName}</div>
                            ${errorListHTML}
                        </div>`;
                    errorContainer.insertAdjacentHTML("beforeend", cardHTML);
                }
            });
        }

        sortTable();
        attachEventListenersToButtons()
        updateAlertMessages();

        if (!isNewForm) {
            const rawEscaped = document.getElementById("managerValidationData")?.dataset?.json || "{}";
            // Revertimos el escape JS (deserializar el string JS escapado)
            const decoded = JSON.parse(`"${rawEscaped}"`);
            // Luego parseamos el JSON real
            const validationJSON = JSON.parse(decoded);
            lockPartners(validationJSON);
        } else {
            window.debugLog("🟢 Formulario nuevo → no se bloquean socios");
        }
        
        
    });
</script>
