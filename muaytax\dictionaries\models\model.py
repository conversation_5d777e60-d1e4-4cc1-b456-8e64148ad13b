from django.db import models


class Model(models.Model):
    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>",
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción",
    )

    country = models.Char<PERSON>ield(
        max_length=2,
        verbose_name="País del modelo",
    )

    class Meta:
        verbose_name = "Modelo"
        verbose_name_plural = "Modelos"

    def __str__(self):
        return self.description


# @admin.register(Model)
# class ModelAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
