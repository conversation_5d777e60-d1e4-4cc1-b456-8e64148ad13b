from django.core import validators
from django.db import models
from django.contrib import admin


class SellerListManagementES(models.Model):

    # Seller
    # Año
    # Periodo: Validation Q1, Q2, Q3, Q4, 0A
    # Show/Mostrar: True/False
    # Modelo XX
    # Modelo XXXX
    # ... -> Modelo List --> ['model_111', 'model_115', 'model_130', 'model_303', 'model_309', 'model_349', 'model_369'] ['model_180', 'model_190', 'model_347', 'model_390']
    # TXT MES 1
    # TXT MES 2
    # … -> TXT Month List --> ['txt_01', 'txt_02', 'txt_03', 'txt_04', 'txt_05', 'txt_06', 'txt_07', 'txt_08', 'txt_09', 'txt_10', 'txt_11', 'txt_12']

    seller = models.ForeignKey(
        "sellers.seller", 
        on_delete=models.CASCADE, 
        verbose_name="empresa"
    )

    show = models.BooleanField(
        default=True,
        verbose_name="Mostrar",
    )

    year = models.PositiveIntegerField(
        verbose_name="Año",
        validators=[
            validators.MinValueValidator(2000),
            validators.MaxValueValidator(2050),
        ],
    )

    period = models.ForeignKey(
        "dictionaries.Period",
        on_delete=models.PROTECT,
        related_name="period_seller_list_management_es",
        verbose_name="Periodo",
        validators=[
            validators.RegexValidator(
                regex=r"Q[1-4]|0A|M[1-12]",
                message="Periodo no válido",
                code="invalid_period",
            )
        ],
    )

    # model = models.ForeignKey(
    #     "dictionaries.Model",
    #     on_delete=models.PROTECT,
    #     related_name="model_seller_list_management_es",
    #     verbose_name="Modelo",
    #     validators=[
    #         validators.RegexValidator(
    #             regex=r"ES-",
    #             message="Modelo no válido",
    #             code="invalid_model",
    #         )
    #     ],
    # )

    # Modelos Trimestrales ############################################################################################################

    model_111 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 111",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_111",
            )
        ],
    )

    model_115 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 115",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_115",
            )
        ],
    )

    model_123 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 123",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_123",
            )
        ],
    )

    model_130 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 130",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_130",
            )
        ],
    )

    model_131 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 131",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_131",
            )
        ],
    )

    model_200 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 200",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_200",
            )
        ],
    )

    model_202 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 202",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_202",
            )
        ],
    )

    model_216 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 216",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_216",
            )
        ],
    )

    model_303 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 303",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_303",
            )
        ],
    )

    model_309 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 309",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_309",
            )
        ],
    )

    model_349 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 349",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_349",
            )
        ],
    )

    # Agregamos el campo del modelo Intrastat
    model_intrastat = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo Intrastat",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_intrastat",
            )
        ],
    )

    model_369 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 369",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_369",
            )
        ],
    )

    # Modelos Anuales ############################################################################################################
    
    model_180 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 180",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_180",
            )
        ],
    )
    
    model_184 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 184",
        validators=[
            validators.RegexValidator(
                regex=r"not-started|not-processed|processed|required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_184",
            )
        ],
    )

    model_190 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 190",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_190",
            )
        ],
    )

    model_347 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 347",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_347",
            )
        ],
    )

    model_390 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 390",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_390",
            )
        ],
    )
    
    model_296 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 296",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_296",
            )
        ],
    )
    
    model_193 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 193",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented|warning00",
                message="Estado no válido",
                code="invalid_model_193",
            )
        ],
    )

    # TXT Mensuales ############################################################################################################

    txt_01 = models.BooleanField(
        default=False,
        verbose_name="TXT Enero",
    )

    txt_02 = models.BooleanField(
        default=False,
        verbose_name="TXT Febrero",
    )

    txt_03 = models.BooleanField(
        default=False,
        verbose_name="TXT Marzo",
    )

    txt_04 = models.BooleanField(
        default=False,
        verbose_name="TXT Abril",
    )

    txt_05 = models.BooleanField(
        default=False,
        verbose_name="TXT Mayo",
    )

    txt_06 = models.BooleanField(
        default=False,
        verbose_name="TXT Junio",
    )

    txt_07 = models.BooleanField(
        default=False,
        verbose_name="TXT Julio",
    )

    txt_08 = models.BooleanField(
        default=False,
        verbose_name="TXT Agosto",
    )

    txt_09 = models.BooleanField(
        default=False,
        verbose_name="TXT Septiembre",
    )

    txt_10 = models.BooleanField(
        default=False,
        verbose_name="TXT Octubre",
    )

    txt_11 = models.BooleanField(
        default=False,
        verbose_name="TXT Noviembre",
    )

    txt_12 = models.BooleanField(
        default=False,
        verbose_name="TXT Diciembre",
    )

    ############################################################################################################

    monthly_muaytax_json_invoices = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON Facturas Muaytax Mensuales",
    )

    model_json_result = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON Resultados de Modelos",
    )

    class Meta:
        verbose_name = "Listado Gestoria España"
        verbose_name_plural = "Listado Gestoria España"
        constraints = [
            models.UniqueConstraint(fields=['seller', 'year', 'period'], name='seller_list_management_es_unique_seller_year_period'),
        ]

    def __str__(self):
        return f"Gestoria ES - {self.seller} - {self.year} - {self.period}"


@admin.register(SellerListManagementES)
class SellerListManagementESAdmin(admin.ModelAdmin):
    list_display = ["id", "seller", "year", "period", "show"]
    list_filter =  ["year", "period"]
    search_fields = ["id", "seller__name", "year", "period__description", "show"]