{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}
{% load static %}
{% block title %}Servicios Contratados{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/htmx/htmx.min-v1.6.0.js"></script>
  <link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">

  <style>
    input[type="date"]::-webkit-calendar-picker-indicator {
      display: none;
    }
  </style>

{% endblock %}
{% block breadcrumb %}
  <div class="page-header">
      <div class="page-block">
        <div class="row align-items-center">
          <div class="col">
            <div class="page-header-title">
              <h5 class="m-b-10">Servicios Contratados</h5>
            </div>
            <ul class="breadcrumb">
              <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_services:service_list' seller.shortname %}"> Lista de Servicios </a>
              </li>
              <li class="breadcrumb-item">
                <a href=".">Servicio Contratado</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-header">
      <h5>Detalle del Servicio</h5>
    </div>
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="{% url 'app_services:service_update' seller.shortname object.pk %}">
        {% csrf_token %}
        {{ form |crispy }}
        <div class="control-group">
          <div class="controls">
            <button type="submit" class="btn btn-primary">Actualizar</button>
          </div> 
        </div>
      </form>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-ui.min-v1.13.1.js"></script>
  <script src="{% static 'assets/js/select2/select2.min.js' %}"></script>
  <script src="{% static 'assets/js/plugins/choices.min.js' %}"></script>
  <script src="{% static 'assets/js/plugins/imask.min.js' %}"></script>
  <script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>
  <script>

(function () {
      const datepickerElements = document.querySelectorAll('.dateinput');
      datepickerElements.forEach(element => {
        const d_week = new Datepicker(element, {
          buttonClass: 'btn',
          format: 'yyyy-mm-dd',
          closeOnSelect: true,
          orientation: 'bottom',
          showOnFocus: true,
          
          autohide: true,

        });
      });
    })();
  </script>
{% endblock %}
