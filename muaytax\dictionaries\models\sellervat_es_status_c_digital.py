from django.db import models

class SellerVatEsStatusCDigital(models.Model):

    code = models.CharField(
        blank=True,
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )


    class Meta:
        verbose_name = "Estado del C.Digital para España"
        verbose_name_plural = "Estados de los C.Digitales para España"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatEsStatusCDigital)
# class SellerVatEsStatusCDigitalAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
