from django.db import models
from django.utils import timezone
from datetime import timedelta

from muaytax.utils.mixins import CustomTimeStampedModel

class HMRCAuthorization(CustomTimeStampedModel):
    seller = models.OneToOneField(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="hmrc_access_seller",
        verbose_name="Empresa/Vendedor",
    )

    def __str__(self):
        return f'Autorización de {self.seller}'
    
    class Meta:
        verbose_name = "Autorización de HMRC"
        verbose_name_plural = "Autorizaciones de HMRC"

    @property
    def is_access_granted(self):
        return self.created_at + timedelta(days=540) > timezone.now()

    @property
    def expiration_date(self):
        return self.created_at + timedelta(days=540)

class HMRCToken(CustomTimeStampedModel):
    authorization = models.OneToOneField(
        HMRCAuthorization,
        on_delete=models.CASCADE,
        related_name="auth_hmrc_token",
        verbose_name="Autorización de acceso"
    )
    access_token = models.CharField(
        max_length=255,
        verbose_name="Access Token",
    )
    refresh_token = models.CharField(
        max_length=255,
        verbose_name="Refresh Token",
    )
    expires_in = models.IntegerField(
        verbose_name="Expira en",
    )
    scope = models.CharField(
        max_length=255,
        verbose_name="Scope",
    )
    token_type = models.CharField(
        max_length=255,
        verbose_name="Tipo de Token",
    )

    def __str__(self):
        return f'{self.access_token}'

    class Meta:
        verbose_name = "Token de acceso para HMRC"
        verbose_name_plural = "Tokens de acceso para HMRC"
    
    @property
    def is_token_expired(self):
        return self.modified_at + timedelta(seconds=self.expires_in) < timezone.now()