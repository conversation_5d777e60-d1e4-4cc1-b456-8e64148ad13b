{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Paises IVA Contratados
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Paises IVA Contratados</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Paises IVA Contratados</a>
            </li>
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;" >
          <a href="./new/" class="btn btn-primary"> 
            Crear Nuevo
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead>
                <tr>
                  <th>Pais IVA</th>
                  <th>Numero IVA</th>
                  <th>VIES</th>
                  <th>Acciones</th>
                </tr>
              </thead>
              <tbody>
                {% for object in object_list %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.vat_country }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.vat_number }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.vat_vies }}</span>
                  </td>
                  <td class="align-middle">
                    <div>
                      <a class="btn btn-icon btn-success" href="{% url 'app_sellers:vat_detail' object.seller.shortname object.pk %}">
                        <i class="feather icon-edit"></i>
                      </a>
                      <a class="btn btn-icon btn-danger" href="{% url 'app_sellers:vat_delete' object.seller.shortname object.pk %}">
                        <i class="feather icon-trash-2"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script>
      const dataTableOptions = {
        paging: true,
        perPageSelect: [5, 10, 15, 20, 25, 50, 100], // Paginator Values
        perPage: 20, // Paginator Default Value
        searchable: true,
        sortable: true,
        truncatePager: true,
        header: true,
        footer: true,
        labels: {
          perPage: "{select}",
          noRows: "No se han encontrado Paises IVA.",
          noResults: "No hay resultados que coincidan con su búsqueda.",
          info: "<p>Mostrando {start} a {end} de {rows} resultados.<p><p>Página {page} de {pages} páginas.</p>"
        },
        layout: {
            top: "{search}{select}",
            bottom: "{info}{pager}"
        }
      };
      const dataTable = new simpleDatatables.DataTable("#list-table", dataTableOptions);
  </script>
{% endblock javascripts %}
