from django.db import models

class Bank(models.Model):

    # id -> AutoGen

    bank_name = models.CharField(
        max_length=100,
        verbose_name="Nombre del Banco",
    )

    bank_accounting_account = models.CharField(
        max_length=100,
        verbose_name="Cuenta Contable",
    )

    bank_account_type = models.ForeignKey(
        "dictionaries.BankType",
        related_name="bank_type",
        on_delete=models.PROTECT,
        verbose_name="Tipo de Cuenta",
    )

    bank_initial_amount = models.DecimalField(
        blank=True,
        null=True,
        default=0,
        max_digits=15,
        decimal_places=2,
        verbose_name="Monto Inicial",
    )

    bank_iban = models.CharField(
        blank=True,
        null=True,
        max_length=34,
        verbose_name="IBAN",
    )

    bank_credit_card = models.CharField(
        blank=True,
        null=True,
        max_length=18,
        verbose_name="Tarjeta de Crédito",
    )

    # bank_credit_card_expiration = models.DateField(
    #     blank=True,
    #     null=True,
    #     verbose_name="Fecha de Expiración Tarjeta Credito",
    # )

    # bank_credit_card_cvv = models.CharField(
    #     blank=True,
    #     null=True,
    #     max_length=4,
    #     verbose_name="CVV Tarjeta Credito",
    # )

    bank_currency = models.ForeignKey(
        "dictionaries.Currency",
        default='EUR',
        related_name="bank_currency",
        on_delete=models.PROTECT,
        verbose_name="Moneda",
    )

    bank_seller = models.ForeignKey(
        "sellers.Seller",
        related_name="bank_seller",
        on_delete=models.PROTECT,
        verbose_name="Vendedor",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Banco"
        verbose_name_plural = "Bancos"
    
    def __str__(self):
        return self.bank_name


