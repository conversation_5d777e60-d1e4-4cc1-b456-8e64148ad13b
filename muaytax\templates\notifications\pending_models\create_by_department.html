{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Notificaciones MuayTax
{% endblock title %}

{% block stylesheets %}
<link rel="stylesheet" crossorigin href="https://use.fontawesome.com/releases/v6.2.1/css/all.css" type="text/css" />

<link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/swal/custom-swal-buttons.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/radio-card-buttons.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/side-progress-tabs.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/bell-animation.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/time-picker/time-picker-clock-jquery.css' %}" />

<style>
    .dashed-input {
        border: 1px dashed #ced4da;
        border-radius: 0.25rem;
        background-color: transparent;
    }

    .dashed-input:focus {
        outline: none;
        box-shadow: none;
        background-color: transparent;
    }

    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }
    input[type="time"]::-webkit-inner-spin-button,
    input[type="time"]::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }
    .custom-disable-date-picker[readonly] {
        background-color: #fff;
    }

    .equal-width-btn {
        width: 200px;
        font-size: 0.875rem;
    }

    .fs-875 {
        font-size: 0.875rem;
    }

    @media (max-width: 576px) {
        .equal-width-btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .d-flex.justify-content-between.flex-wrap {
            justify-content: center;
        }
    }

    .swal2-actions:not(.swal2-loading) .swal2-styled:hover,
    .swal2-actions:not(.swal2-loading) .swal2-styled:active {
        background-image: none!important;
    }
</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                        Notificaciones
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'app_notifications:notifications_dashboard' %}">Centro de notificaciones</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="">Nueva notificación</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}

<div class="col-12 mt-3">
    <div class="row">
        <div class="col-lg-9">
            <form
                id="new-notification-form"
                method="POST"
                enctype="multipart/form-data"
                >
                {% csrf_token %}
                <!-- Formulario -->
                <div class="row d-flex flex-wrap">
                    <!-- progress tabs -->
                    <div class="col-lg-auto d-flex justify-content-center mb-3">
                        <div class="d-flex flex-lg-column gap-4 align-items-center nav" role="tablist">
                            <!-- First Tab -->
                            <a class="side-progress-card active" id="new-notification-tab-1" data-bs-toggle="pill" href="" role="tab" aria-controls="new-notification-panel-1" aria-selected="true">
                                <i class="fas fa-user-tie fa-3x text-dark"></i>
                                <p class="mb-0 fw-bolder small text-dark">REMITENTE</p>
                                <div class="progress-card-conector d-none d-lg-block"></div>
                                <div class="progress-card-conector-horizontal d-lg-none"></div>
                            </a>

                            <!-- Second Tab -->
                            <a class="side-progress-card active" id="new-notification-tab-2" data-bs-toggle="pill" href="" role="tab" aria-controls="new-notification-panel-2" aria-selected="false">
                                <!-- <i class="fas fa-users-cog fa-3x text-dark"></i> -->
                                <i class="fas fa-bell fa-3x text-dark"></i>
                                <p class="mb-0 fw-bolder small text-dark">NOTIFICACIÓN</p>
                                <div class="progress-card-conector d-none d-lg-block"></div>
                                <div class="progress-card-conector-horizontal d-lg-none"></div>
                            </a>

                            <!-- Third Tab -->
                            <div class="side-progress-card active" id="new-notification-tab-3" data-bs-toggle="pill" href="" role="tab" aria-controls="new-notification-panel-3" aria-selected="false">
                                <i class="fas fa-cogs fa-3x text-dark"></i>
                                <p class="mb-0 fw-bolder small text-dark">CONFIG.</p>
                            </div>
                        </div>
                    </div>

                    <!-- tab content -->
                    <div class="col d-flex flex-column mb-3">
                        <!-- Notification title -->
                        <div class="mb-4">
                            <input type="text" class="form-control dashed-input fw-bolder" id="title" name="title" placeholder="Título de la notificación" required maxlength="50">
                        </div>
                        <div class="tab-content p-0 bg-transparent shadow-none flex-fill">
                            <!-- content 3 CONFIGURACIÓN -->
                            <div class="tab-pane fade show active" id="new-notification-panel-3" role="tabpanel" aria-labelledby="new-notification-tab-3">
                                <h4 class="mb-3 fw-bolder">
                                    NOTIFICACIÓN DE MODELOS PENDIENTES
                                </h4>
                                <div class="card rounded">
                                    <div class="card-header">
                                        <h5 class="card-title">Información y programación</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-danger d-none" role="alert" id="is-invalid-date-time">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <span>La fecha y hora son obligatorios. Ten en cuenta que la fecha de la notificación no puede ser anterior a la fecha actual y como mínimo debe ser programada con 1 hora de antelación.</span>
                                        </div>
                                        <div class="form-group row">
                                            <label for="description" class="col-lg-3 col-form-label text-lg-start">Descripción</label>
                                            <div class="col-lg-6">
                                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Escribe una breve descripción de la notificación (opcional)"></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="date" class="col-lg-3 col-form-label text-lg-start">Fecha de notificación*</label>
                                            <div class="col-lg-6">
                                                <input type="date" class="form-control cursor-pointer custom-disable-date-picker" id="scheduled_date" name="scheduled_date" required>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="time" class="col-lg-3 col-form-label text-lg-start">Hora de notificación*</label>
                                            <div class="col-lg-6">
                                                <input type="text" class="form-control clock-library-unset-width" id="scheduled_time" name="scheduled_time" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card rounded">
                                    <div class="card-header">
                                        <h5 class="card-title">Método de notificación</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group row">
                                            <div class="alert alert-danger d-none" role="alert" id="is-invalid-method">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                <span>Debes seleccionar obligatoriamente el método de notificación a utilizar.</span>
                                            </div>
                                            <label for="notification_method" class="col-lg-3 col-form-label text-lg-start">Notificar via*</label>
                                            <div class="col-lg-8">
                                                <div class="radio-card-buttons gap-3">
                                                    <input type="radio" id="notification-method-1" name="notification_method" value="email" class="radio-input" />
                                                    <label for="notification-method-1" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fas fa-envelope-open-text fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Email</p>
                                                        </div>
                                                    </label>
                                                    <input type="radio" id="notification-method-2" name="notification_method" value="sms" class="radio-input" />
                                                    <label for="notification-method-2" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fas fa-comment-dots fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Mensaje SMS</p>
                                                        </div>
                                                    </label>
                                                    <input type="radio" id="notification-method-3" name="notification_method" value="wapp" class="radio-input" />
                                                    <label for="notification-method-3" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fab fa-whatsapp fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Whatsapp</p>
                                                        </div>
                                                    </label>
                                                    <input type="radio" id="notification-method-4" name="notification_method" value="call" class="radio-input" />
                                                    <label for="notification-method-4" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fas fa-phone fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Llamada</p>
                                                        </div>
                                                    </label>
                                                    {% comment %}
                                                    {% endcomment %}
                                                    
                                                
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-lg-3">
            <div class="d-flex flex-column-reverse flex-lg-column">

                <div class="card rounded">
                    <div class="card-header">
                        <h4 class="card-title">
                            Resumen
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Remitente:</p>
                            <p class="fw-bolder mb-0" id="summary_sender">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Tipo:</p>
                            <p class="fw-bolder mb-0" id="summary_task">&nbsp;</p>
                        </div>
                        <!-- add period -->
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Periodo:</p>
                            <p class="fw-bolder mb-0" id="summary_period">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Año:</p>
                            <p class="fw-bolder mb-0" id="summary_year">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Fecha programada:</p>
                            <p class="fw-bolder mb-0" id="summary_date">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Hora programada:</p>
                            <p class="fw-bolder mb-0" id="summary_time">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <p class="text-muted mb-0">Notificar via:</p>
                            <p class="fw-bolder mb-0" id="summary_method">&nbsp;</p>
                        </div>
                        <button type="type" class="btn btn-dark w-100 fs-875" onclick="validateForm(event)">Crear notificación</button>
                    </div>
                </div>
    
                <div class="card rounded d-none d-lg-block">
                    <div class="card-header">
                        <h4 class="card-title">Instrucciones</h4>
                    </div>
                    <div class="card-body">
                        <p>Para <u class="text-dark">crear una nueva notificación programada</u>, sigue estos pasos:</p>

                        <p><strong>1. <u class="text-dark">Configura</u> la fecha y la hora:</strong> Establece la fecha y hora en que se enviará la notificación.</p>
    
                        <p><strong>2. Define la vía de envío:</strong> Elige cómo se enviará la notificación (por ejemplo, correo electrónico, mensaje SMS, etc.).</p>
    
                        <p>Una vez que hayas <u>configurado todos los detalles</u>, haz clic en <u class="text-dark">Crear notificación</u> para finalizar el proceso.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



{% endblock content %}

{% block javascripts %}

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>
<script src="{% static 'assets/js/side-progress-tabs.js' %}"></script>
<script src="{% static 'assets/js/jquery-clock-timepicker.min.js' %}"></script>

<script defer>
    const today = new Date();
    const $dateInputs = $('#scheduled_date');

    $('#scheduled_time').clockTimePicker({
        duration: false,
        durationNegative: false,
        precision: 1,
        minWidth: '200px',
        colors: {
            selectorColor: '#38ac6a',
            popupHeaderBackgroundColor: '#39474e',
            buttonTextColor: '#38ac6a',
            // popupBackgroundColor: '#D9FFEE',
            // clockFaceColor: '#888',
            
        },
        fonts: {
            clockInnerCircleFontSize: '15px',
            clockOuterCircleFontSize: '19px'
        },
        popupWidthOnDesktop: '300px',
        i18n: {
            cancelButton: 'Cancelar',
        },
        onOpen: function() {
            // close all datepickers
            $dateInputs.each(function() {
                this.datepicker.hide();
            });
        },
    })

    const elements = {
        title: document.getElementById('title'),
        dateInput: document.getElementById('scheduled_date'),
        timeInput: document.getElementById('scheduled_time'),
        summaryDate: document.getElementById('summary_date'),
        summarySender: document.getElementById('summary_sender'),
        summaryTask: document.getElementById('summary_task'),
        summaryPeriod: document.getElementById('summary_period'),
        summaryYear: document.getElementById('summary_year'),
        summaryTime: document.getElementById('summary_time'),
        summaryMethod: document.getElementById('summary_method'),
        invalidDateTime: document.getElementById('is-invalid-date-time'),
        invalidMethod: document.getElementById('is-invalid-method'),
        tab3: document.getElementById('new-notification-tab-3'),
        form: document.getElementById('new-notification-form'),
    };

    elements.summarySender.textContent = '{{ department_sender }}';
    elements.summaryTask.textContent = 'Modelos pendientes';
    elements.summaryPeriod.textContent = '{{ period }}';
    elements.summaryYear.textContent = '{{ year }}';

    elements.title.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });

    const addEventListenerToNodeList = (nodeList, event, callback) => {
        nodeList.forEach(node => node.addEventListener(event, callback));
    };

    window.addEventListener('DOMContentLoaded', () => elements.title.focus());

    $dateInputs.each(function() {
        new Datepicker(this, {
            autohide: true,
            buttonClass: 'btn',
            format: 'yyyy-mm-dd',
            minDate: today,
            todayHighlight: true,
            weekStart: 1,
        });

        $(this).on('contextmenu', function(e) {
            e.preventDefault();
            $('#scheduled_time').clockTimePicker('hide');
        });

        $(this).on('keydown', function(e) {
        if (e.key === ' ') {
            e.preventDefault();
        }
    });

        $(this).on('changeDate', function(e) {
            const [year, month, day] = e.target.value.split('-');
            elements.summaryDate.textContent = `${day}/${month}/${year}`;
            $(this).removeClass('is-invalid');
            
            if (elements.timeInput.value && document.querySelector('input[name="notification_method"]:checked')) {
                elements.tab3.classList.remove('invalid');
            }
            if (elements.timeInput.value) {
                elements.invalidDateTime.classList.add('d-none');
            }
        });
    });

    const handleRadioChange = (summaryElement, invalidMessageElement, tabElement, contentSelector) => function() {
        summaryElement.textContent = this.nextElementSibling.querySelector('p').textContent;
        invalidMessageElement.classList.add('d-none');
        if (elements.dateInput.value && elements.timeInput.value) {
            tabElement.classList.remove('invalid');
        }
    };

    elements.timeInput.addEventListener('change', function() {
        elements.summaryTime.textContent = this.value;
        this.classList.remove('is-invalid');
        if (elements.dateInput.value && document.querySelector('input[name="notification_method"]:checked')) {
            elements.tab3.classList.remove('invalid');
        }
        if (elements.dateInput.value) {
            elements.invalidDateTime.classList.add('d-none');
        }
    });

    addEventListenerToNodeList(document.querySelectorAll('input[name="notification_method"]'), 'change', 
        handleRadioChange(elements.summaryMethod, elements.invalidMethod, elements.tab3));
    
    function validateForm(event) {
        event.preventDefault();

        const formData = {
            title: elements.form.querySelector('#title').value,
            date: elements.form.querySelector('#scheduled_date').value,
            time: elements.form.querySelector('#scheduled_time').value,
            method: elements.form.querySelector('input[name="notification_method"]:checked'),
        };

        const validations = [
            { condition: !formData.title, element: elements.title, invalidClass: 'is-invalid' },
            { condition: !formData.date || !formData.time, element: elements.tab3, invalidClass: 'invalid', messageElement: elements.invalidDateTime },
            { condition: !formData.method, element: elements.tab3, invalidClass: 'invalid', messageElement: elements.invalidMethod },
            { condition: !formData.date, element: elements.dateInput, invalidClass: 'is-invalid', messageElement: elements.invalidDateTime },
            { condition: !formData.time, element: elements.timeInput, invalidClass: 'is-invalid', messageElement: elements.invalidDateTime },
        ];

        let formIsValid = true;

        validations.forEach(({ condition, element, invalidClass, messageElement }) => {
            if (condition) {
                formIsValid = false;
                element.classList.add(invalidClass);
                if (messageElement) messageElement.classList.remove('d-none');
            }
        });

        if (!formIsValid) {
            Swal.fire({
                icon: 'error',
                text: 'Debes completar todos los campos obligatorios para continuar.',
                confirmButtonText: 'Completar información',
                customClass: {
                    confirmButton: 'swal2-btn-full-width btn-dark',
                    actions: 'swal2-action-div-full-width',
                },
            });

            return;
        }

        var loadingBellHtml = `{% include 'includes/swal-bell-loading-animation.html' %}`;
        Swal.fire({
            title: 'Creando notificación',
            html: loadingBellHtml,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            willOpen: () => {
                setTimeout(() => {
                    submitCreateNotification();
                }, 1500);
            },
        });
    };

    async function submitCreateNotification () {
        const formData = new FormData(elements.form);
        const department = "{{ department_sender.code }}";
        const period = "{{ period.code }}";
        const year = "{{ year }}";

        const baseUrl = "{% url 'app_notifications:create_pending_model_notif_from_cached_list' 'DEPARTMENT_PLACEHOLDER' %}?period=" + period + "&year=" + year;
        const url =  baseUrl.replace('DEPARTMENT_PLACEHOLDER', department);
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                },
            });

            if (response.ok) {
                const responseData = await response.json();
                const data = responseData.data;

                Swal.fire({
                    icon: 'success',
                    title: 'Notificación creada',
                    text: 'La notificación ha sido creada exitosamente.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showCancelButton: true,
                    showDenyButton: true,
                    confirmButtonText: 'Crear otra notificación',
                    denyButtonText: 'Ir al centro de notificaciones',
                    cancelButtonText: 'Volver al listado',
                    customClass: {
                        confirmButton: 'swal2-btn-full-width btn-dark',
                        denyButton: 'swal2-btn-full-width btn-outline-dark',
                        cancelButton: 'btn-link',
                        actions: 'swal2-action-div-full-width',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    } else if (result.isDenied) {
                        window.location.href = "{% url 'app_notifications:notifications_dashboard' %}";
                    } else if (result.isDismissed) {
                        window.location.href = data.redirect_url + '?period=' + data.period + '&year=' + data.year;
                    }
                });
                
            }
            else if(response.status === 400 ) {
                const { errors } = await response.json();
                renderErrorMessages(errors);
                Swal.close();
            }
            else {
                throw new Error('Error al crear la notificación');
            }
        }
        catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Error al crear notificación',
                text: 'Ha ocurrido un error al intentar crear la notificación. Por favor, inténtalo de nuevo.',
                confirmButtonText: 'Aceptar',
                customClass: {
                    confirmButton: 'swal2-btn-full-width btn-dark',
                    actions: 'swal2-action-div-full-width',
                },
            });
        }
        

    }

    function renderErrorMessages(errors) {
        document.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

        elements.tab3.classList.add('invalid');

        Object.entries(errors).forEach(([key, value]) => {
            if (key === 'notification_method') {
                elements.invalidMethod.classList.remove('d-none');
            }
            else if (key === 'scheduled_date' || key === 'scheduled_time') {
                const input = document.getElementById(key);
                input.classList.add('is-invalid');
                input.insertAdjacentHTML('afterend', `<div class="invalid-feedback">${value}</div>`);
            }
        });
        
    }

    function isMobileDevice(){
        return /mobi|android/i.test(navigator.userAgent);
    }

    if (isMobileDevice()) {
        document.querySelectorAll('.side-progress-card').forEach(function(card) {
            card.style.width = '80px';
            card.style.height = '60px';
            card.style.padding = '10px';
            card.style.fontSize = '12px';
            card.querySelector('i').style.fontSize = '24px';
            card.querySelector('p').style.fontSize = '.5rem';
        });
        document.querySelectorAll('.radio-card-button').forEach(function(methodCard) {
            methodCard.style.width = '70px';
            methodCard.style.height = '60px';
            methodCard.style.padding = '10px';
            methodCard.querySelector('.radio-button-content i').classList.remove('fa-3x');
            methodCard.querySelector('.radio-button-content i').classList.add('fa-lg');
            methodCard.querySelector('p').style.display = 'none';
        });
    }

</script>
{% endblock javascripts %}