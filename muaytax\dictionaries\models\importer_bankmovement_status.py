from django.db import models

class BankMovementImporterStatus(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=100,
        verbose_name="<PERSON>ó<PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Estado del Importador de Movimiento Bancario"
        verbose_name_plural = "Estados del Importador de Movimientos Bancarios"
    
    def __str__(self):
        return self.description
    
# @admin.register(BankMovementImporterStatus)
# class BankTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]