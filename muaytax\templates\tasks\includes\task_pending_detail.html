<!-- Modal para Detalles de Tareas -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">Detalles de la Tarea</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="taskDescription" class="form-label">Descripción</label>
                    <textarea class="form-control" id="taskDescription" rows="3" readonly v-model="taskDescription"></textarea>
                </div>
                <div class="row mb-3">
                    <div class="col-6">
                        <label for="taskDueDate" class="form-label"><PERSON><PERSON></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-calendar icon-large"></i></span>
                            <input type="text" class="form-control" id="taskDueDate" readonly v-model="taskDueDate">
                        </div>
                    </div>
                    <div class="col-6">
                        <label for="taskNotificationType" class="form-label">Tipo de Notificación</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="taskNotificationType" readonly v-model="taskNotificationType">
                            <span class="input-group-text"><i class="bi bi-check-circle icon-large"></i></span>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-7 d-flex align-items-center justify-content-end pe-5">
                        <label for="taskSeen" class="form-label mb-0 me-2">Vista</label>
                        <input class="form-check-input toggle-switch" type="checkbox" id="taskSeen" v-model="taskSeen" disabled>
                    </div>
                    <div class="col-5"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-7 d-flex align-items-center justify-content-end pe-5">
                        <label for="taskCompleted" class="form-label mb-0 me-2">Completada</label>
                        <input class="form-check-input toggle-switch" type="checkbox" id="taskCompleted" v-model="taskCompleted" disabled>
                    </div>
                    <div class="col-5"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
