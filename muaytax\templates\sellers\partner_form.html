{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Datos empresa{% endblock title %}

{% block content %}
  {% if form.instance.pk %}
  <h1>Modificar socio</h1>
  {% else %}
  <h1>Nuevo socio</h1>
  {% endif %}

  {% if form.instance.pk %}
  <form class="form-horizontal" method="post" enctype="multipart/form-data" action="{% url 'app_sellers:partner_update' form.instance.pk %}">
  {% else %}
  <form class="form-horizontal" method="post" enctype="multipart/form-data" action="{% url 'app_sellers:partner_create' %}">
  {% endif %}

    {% csrf_token %}
    {{ form|crispy }}
    <div class="control-group">
      <div class="controls">
        <button type="submit" class="btn btn-primary">Guardar</button>
      </div>
    </div>
  </form>
{% endblock content %}
