import csv
import traceback
import requests
import os

from datetime import datetime

# Django
from django.contrib import admin, messages
from django.core.mail import EmailMultiAlternatives
from django.db import connection, transaction
from django.db.models import F, Case, When
from django.core.exceptions import ValidationError
from django.shortcuts import render, redirect
from django.http import FileResponse
from django.conf import settings


"""imports para el signal""" 
from django.http import HttpResponseRedirect
from django.template.defaultfilters import slugify
from django.template.loader import render_to_string
from django.template.response import TemplateResponse
from django.urls import path, reverse
from django.utils.translation import gettext_lazy as _

# THIRD_PARTY_APPS
import pandas as pd
from allauth.account.forms import SignupForm
from allauth.account.models import EmailAddress

# Modelos - LOCAL_APPS
from muaytax.app_address.models.address import Address
from muaytax.app_representatives.models.representative import Representative
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_affiliate import AffiliateProgram
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.transaction_type import TransactionType
from muaytax.utils.env_resources import (
    logo_url_head_muaytax,
    logo_url_head_amzvat
)
from muaytax.users.models import User
from ..models.seller import Seller
from ..forms.seller import ExcelUploadDateForm


class SellerAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "name",
        "shortname",
        "contracted_accounting_usa_date",
        "contracted_accounting_usa_basic_date",
        #"contracted_maintenance_llc_date",
        #"contracted_accounting_date",
        "maintenance_type",
        "first_name",
        "last_name",
        "nif_registration",
        "legal_entity",
        "oss",
        "contracted_accounting",
        "show_limit_invoice",
        "limit_invoice_promoted",
        "show_total_limit_invoice",
        'affiliate_program',
        'affiliatebpa_program',
        'flat_rate_inss_next_expiration_send_email',
    ]

    search_fields = [
        "id",
        "name",
        "shortname",
        "first_name",
        "last_name",
        "nif_registration",
        "legal_entity",
        "oss",
        "contracted_accounting",
        'is_inactive',
        'affiliate_program',
        'affiliatebpa_program',
    ]

    # exclude = ["limit_invoice"]
    def get_readonly_fields(self, request, obj=None):
        """
        Define los campos de solo lectura dependiendo del entorno.
        """
        readonly_fields = ["seller_address", "limit_invoice", "member_address"]
        if not settings.DEBUG:  # Solo en producción
            readonly_fields.append("flat_rate_inss_next_expiration_send_email")
        return readonly_fields
    
    # readonly_fields = ["seller_address", 'limit_invoice', 'flat_rate_inss_next_expiration_send_email']
    autocomplete_fields = ['user', 'country_registration', 'oss_country', 'phone_country', 'birth_country']

    fieldsets = (
        ("Información general", {
            "fields": (
                'user',
                'shortname',
                'name',
                'first_name',
                'last_name',
                'birthdate_seller',
                'birth_country',
                'gender',
                'seller_address',
                'member_address',
                'phone_country',
                ('phone', 'contact_phone'),

            )
        }),
        ("Información fiscal", {
            "fields": (
                'tax_information_start_date',
                'tax_information_end_date',
                'total_turnover',
                'total_benefits',
                'total_assests',
                'total_liabilities',
                'flat_rate_inss_date',
                'flat_rate_inss_next_expiration_send_email',
            )
        }),
        ("Otra información", {
            "fields": (
                'representative_id',
                'name_representative',
                'last_name_representative',
                'maintenance_type',
            )
        }),
        ("Información de actividad", {
            "fields": (
                'legal_entity',
                'is_direct_estimation',
                'is_fiscally_transparent',
                'is_eu_seller',
                'incorporation_llc_date',
                'country_registration',
                'ein',
                'business_id_eeuu',
                'activity_type',
                'products_and_services',
                'desc_main_activity',
                'manufacture_products',
                'provider_name',
                'seller_provider_address',
                'state_agency_registration',
                'nif_registration',
                'establishment_date',
                'vat_no_origin_country',
                'eori',
            )
        }),
        ("Información de Amazon", {
            "fields": (
                'amazon_sell',
                'amazon_name',
                'amazon_merchant_token',
                'amazon_account_ids',
                'amazon_address',
            )
        }),
        ("Servicios contratados", {
            "fields": (
                'contracted_accounting',
                'contracted_accounting_date',
                'contracted_accounting_end_date',
                'tax_agency_accounting_date',
                'tax_agency_accounting_end_date',
                'contracted_accounting_payment_date',
                'contracted_accounting_usa_date',
                'contracted_accounting_usa_end_date',
                'contracted_accounting_usa_payment_date',
                'contracted_accounting_usa_basic_date',
                'contracted_accounting_usa_basic_end_date',
                'contracted_accounting_usa_basic_payment_date',
                'contracted_accounting_txt',
                'contracted_accounting_txt_date',
                'contracted_accounting_txt_end_date',
                'contracted_accounting_txt_payment_date',
                'contracted_model_presentation',
                'contracted_model_presentation_date',
                'contracted_model_presentation_end_date',
                'contracted_model_presentation_payment_date',
                'contracted_maintenance_llc',
                'contracted_maintenance_llc_date',
                'contracted_maintenance_llc_end_date',
                'contracted_maintenance_llc_payment_date',
                'is_contracted_corporate_payroll',
                'contracted_corporate_payroll_date',
                'contracted_corporate_payroll_end_date',
                'contracted_corporate_payroll_payment_date',
                'is_contracted_labor_payroll',
                'contracted_labor_payroll_date',
                'contracted_labor_payroll_end_date',
                'contracted_labor_payroll_payment_date',
                'eqtax',
                'oss',
                'oss_date',
                'oss_end_date',
                'oss_payment_date',
                'oss_country',
                'withholdings_payments_account_date',
                'withholdings_payments_account_end_date',
                'withholdings_payments_account_payment_date',
                'is_184_contracted',
                'is_5472_1120_contracted',
                'is_5472_1120_inactive_contracted',
                'is_inactive'
            )
        }),
        ("Información bancaria", {
            "fields": (
                'iban',
                'swift',
                'bank_name',
                'seller_bank_address',
                'seller_iae',
            )
        }),
        ("Configuraciones y varios", {
            "fields": (
                'limit_invoice',
                'send_email_limit_invoice',
                'limit_invoice_promoted',
                'email_notification',
                'percent_entity',
                'percentage_affected_activity',
                'share_capital',
                'shares_number',
                'share_value',
            )
        }),
        ("Otros datos", {
            "fields": (
                'api_usage',
                'valid_emails',
                'reg_wizard_steps',
                'logo',
            ),
            "classes": ("collapse",),
        }),
        ("Programas de Afiliados", {
            "fields": (
                'affiliate_program',
                'affiliate_start_date',
                'affiliate_end_date',
                'affiliatebpa_program',
                'affiliatebpa_start_date',
                'affiliatebpa_end_date',
            )
        }),
    )

    def save_model(self, request, obj, form, change):
        if obj.flat_rate_inss_date and obj.legal_entity != 'self-employed':
            messages.error(request, "La fecha de inscripción a tarifa plana solo es válida para vendedores autónomos.")
            return

        #26-03-25: Delete validation | 0x7xyp01
        """
        if obj.flat_rate_inss_date and obj.contracted_accounting_date:
            if obj.flat_rate_inss_date < obj.contracted_accounting_date:
                messages.error(request, "La fecha de inscripción a tarifa plana no puede ser anterior a la fecha de alta como autónomo.")
                return
        """
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            total_limit_invoice=Case(
                When(limit_invoice=-1, then=-1),
                default=F('limit_invoice') + F('limit_invoice_promoted'),
            )
        )
        return queryset

    @admin.display(description="Límite de factura", ordering="limit_invoice")
    def show_limit_invoice(self, obj):
        return "Sin límite" if obj.limit_invoice == -1 else obj.limit_invoice

    @admin.display(description="Total límite de facturas", ordering="total_limit_invoice")
    def show_total_limit_invoice(self, obj):
        return "Sin límite" if obj.total_limit_invoice == -1 else obj.total_limit_invoice

    def get_urls(self):
        urls = super().get_urls()
        new_urls = [
            path("xlsx-upload/", self.admin_site.admin_view(self.upload_xlsx)),
            path("csv-upload/", self.admin_site.admin_view(self.upload_csv)),
            path("sql-exec/", self.admin_site.admin_view(self.sql_exec)),
            path("delete-txt/", self.admin_site.admin_view(self.delete_txt)),
            path("intra-local/", self.admin_site.admin_view(self.intra_to_local)),
            # path("send-unic-mail/", self.admin_site.admin_view(self.send_unic_mail)),
            path('upload_excel_date/', self.admin_site.admin_view(self.upload_excel_date), name='seller_upload_excel_date'),
            path('download_template/', self.admin_site.admin_view(self.download_template_date), name='seller_download_template_date'),
        ]
        return new_urls + urls 

    def send_unic_mail(self, request):
        users = User.objects.all()
        for user in users:

            if user.first_name is not None:
                username = " " + user.first_name
            else:
                username = ""

            to_email = [user.email]
            bcc_email = ['<EMAIL>']

            message = render_to_string("emails/notice_6_10_diciembre.html", {
                'username': username,
                'logo_head_muaytax': logo_url_head_muaytax()
            })
            subject = 'MUAYTAX - Estaremos cerrados del 6 al 10 de diciembre'
            text_content = 'Mantendremos servicios mínimos tanto a nivel fiscal como tecnológico por las urgencias que puedan surgir. '

            from_email = '<EMAIL>'
            reply_to = ['<EMAIL>']
            html_content = message
            tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to,
                                                   bcc=bcc_email)
            tracked_email.attach_alternative(html_content, "text/html")
            resp = tracked_email.send()
            print("~~~~~~~~~~~~~~~~~~~~~~~ Respuesta mail para: " + str(to_email) + " : " + str(resp))

        return HttpResponseRedirect(request.META.get('HTTP_REFERER'))

    def intra_to_local(self, request):
        from muaytax.app_sellers.forms.seller import Sellerintratolocal
        if request.method == "POST":
            formulario = Sellerintratolocal(data=request.POST)
            if formulario.is_valid():
                input_value = formulario.cleaned_data['name']
                seller = Seller.objects.get(shortname=input_value)
                invoices = Invoice.objects.filter(seller=seller)
                for invoice in invoices:
                    if invoice.status is not None and invoice.status.code == "revised" and invoice.transaction_type is not None and invoice.transaction_type.code == "intra-community-sale" and invoice.total_vat_euros is not None and invoice.total_vat_euros > 0:
                        all_TransactionType = TransactionType.objects.filter(code="local-sale")
                        if all_TransactionType is not None and len(all_TransactionType) > 0:
                            invoice.transaction_type = all_TransactionType[0]
                            invoice.save()

                    if invoice.status is not None and invoice.status.code == "revised" and invoice.transaction_type is not None and invoice.transaction_type.code == "intra-community-refund" and invoice.total_vat_euros is not None and invoice.total_vat_euros < 0:
                        all_TransactionType = TransactionType.objects.filter(code="local-refund")
                        if all_TransactionType is not None and len(all_TransactionType) > 0:
                            invoice.transaction_type = all_TransactionType[0]
                            invoice.save()
        else:

            formulario = Sellerintratolocal()

            context = self.admin_site.each_context(request)
            context.update(
                {
                    "form": formulario,
                }
            )


        return TemplateResponse(
            request, "admin/sellers/seller/intra_local.html", context
        )
 
    def delete_txt(self, request):
        from muaytax.app_sellers.forms.seller import SellerDeleteTxt
        if request.method == "POST":
            formulario = SellerDeleteTxt(data=request.POST)
            if formulario.is_valid():
                input_name = formulario.cleaned_data['name']
                print("name: " + str(input_name))
                input_month = formulario.cleaned_data['month']
                print("month: " + str(input_month))
                if input_month == "-1":
                    input_month = "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12"
                print("month2: " + str(input_month))
                input_year = formulario.cleaned_data['year']
                print("year" + str(input_year))
                seller = Seller.objects.get(shortname=input_name)
                with connection.cursor() as cursor:

                    query1 = "DELETE FROM public.invoices_concept con WHERE con.invoice_id in ( SELECT DISTINCT id FROM public.invoices_invoice inv WHERE inv.is_txt_amz = True and inv.seller_id = '" + str(
                        seller.pk) + "' and EXTRACT(year FROM inv.expedition_date) = " + str(
                        input_year) + " AND EXTRACT(month FROM inv.expedition_date) IN (" + str(input_month) + ") )"
                    print(query1)
                    cursor.execute(query1)

                    query2 = "DELETE FROM public.invoices_invoice inv WHERE inv.is_txt_amz = True and inv.seller_id = '" + str(
                        seller.pk) + "' and EXTRACT(year FROM inv.expedition_date) = " + str(
                        input_year) + " AND EXTRACT(month FROM inv.expedition_date) IN (" + str(input_month) + ")"
                    print(query2)
                    cursor.execute(query2)

                # with connection.cursor() as cursor:
                # for seller in sellers:
                #     # DELETE ALL CONCETPS FROM TXT
                #     query1 = "DELETE FROM public.invoices_concept WHERE invoice_id in ( SELECT id FROM public.invoices_invoice WHERE seller_id = '"+ str(seller.pk)+"' and is_txt_amz = true ) "
                #     print(query1)
                #     cursor.execute(query1)

                #     # DELETE ALL INVOICES FROM TXT
                #     query2 = "DELETE FROM public.invoices_invoice WHERE seller_id = '"+ str(seller.pk)+"' and is_txt_amz = true"
                #     print(query2)
                #     cursor.execute(query2)

                #     # DELETE ALL CUSTOMERS WITHOUT INVOICE
                #     query3 = "DELETE FROM public.customers_customer WHERE seller_id = '"+ str(seller.pk)+"' and id not in ( SELECT DISTINCT cus.id FROM public.customers_customer as cus INNER JOIN public.invoices_invoice as inv ON inv.customer_id = cus.id and cus.seller_id = '"+ str(seller.pk)+"' )"
                #     print(query3)
                #     cursor.execute(query3)

                #     # DELETE ALL PROVIDERS WITHOUT INVOICE
                #     query4 = "DELETE FROM public.providers_provider WHERE seller_id = '"+ str(seller.pk)+"' and id not in ( SELECT DISTINCT prov.id FROM public.providers_provider as prov INNER JOIN public.invoices_invoice as inv ON inv.provider_id = prov.id and prov.seller_id = '"+ str(seller.pk)+"' )"
                #     print(query4)
                #     cursor.execute(query4)

                #     # DELETE ALL AMZ PRODUCT WITHOUT PRICE/ASIN
                #     query5= "DELETE FROM public.products_productamz WHERE seller_id = '"+ str(seller.pk)+"' and ( asin is NULL or price is NULL )"
                #     print(query5)
                #     cursor.execute(query5)
        else:
            formulario = SellerDeleteTxt()

        context = self.admin_site.each_context(request)
        context.update(
            {
                "form": formulario,
            }
        )
        return TemplateResponse(
            request, "admin/sellers/seller/delete_txt.html", context
        )

    @transaction.atomic
    def sql_exec(self, request):
        from muaytax.app_sellers.forms.seller import SellerSqlExec
        sql_result = ""
        if request.method == "POST":
            formulario = SellerSqlExec(data=request.POST)
            if formulario.is_valid():
                print('form is valid')
                sql = formulario.cleaned_data['sql']
                sql = str(sql).strip()
                print("SQL: \n{}".format(sql))
                with connection.cursor() as cursor:
                    cursor.execute(sql)
                    if sql.upper().startswith("UPDATE") or sql.upper().startswith("DELETE"):
                        if cursor.rowcount > 0:
                            sql_result = "{} Filas Cambiadas (rows affected): ".format(cursor.rowcount)
                        else:
                            sql_result = "Sin Cambios (no rows affected)"
                    else:
                        try:
                            row = cursor.fetchone()
                            while row is not None:
                                sql_result += str(row) + "\n"
                                row = cursor.fetchone()
                        except:
                            sql_result = "Sin Resultados (no results)"

                    sql_result = sql_result.strip()
                    if (sql_result.strip() == ""):
                        sql_result = "Sin Resultados (no results)"
                    print(sql_result)
        else:
            formulario = SellerSqlExec()

        if (sql_result.strip() == ""):
            sql_result = None

        context = self.admin_site.each_context(request)
        context['sql_result'] = sql_result
        context.update(
            {
                "form": formulario,
            }
        )
        return TemplateResponse(
            request, "admin/sellers/seller/sql_exec.html", context
        )
    
    @transaction.atomic
    def upload_xlsx(self, request):
        from muaytax.app_sellers.forms.seller import SellerXlsxImportForm

        if request.method == "POST":
            form = SellerXlsxImportForm(files=request.FILES)

            #  buscar las facturas del seller con id 571 que sean transaction_type oss o sean oss-refund y que su fecha de contabilizacion sea entre el 1 de ebero de 2024 y el 31 de 
            #  marzo de 2024 y cambiar la fecha de contabilizacion a 1 de abril de 2024, tambien cambiar el campo out_of_time a True

            # invoices = Invoice.objects.filter(seller_id=919, transaction_type__code__in=["oss", "oss-refund"], accounting_date__range=["2024-01-01", "2024-03-31"])
            # for invoice in invoices:
            #     invoice.accounting_date = "2024-04-01"
            #     invoice.out_of_time = True
            #     invoice.save()
                

            if form.is_valid():
                xlsx_file = request.FILES["xlsx_upload"]
                # Usar Pandas para leer el archivo Excel
                df = pd.read_excel(xlsx_file, engine='openpyxl')
                numcreados = 0
                fallidos = []

                for index, row_data in df.iterrows():
                    if row_data["email"]:

                        # imprimir cada dato que guardaria
                        print("Nombre: " + str(row_data["Nombre LLC"]))
                        print("Username: " + str(slugify(row_data["Nombre LLC"].replace(" ", "").replace("&", "").lower())))
                        print("Email: " + str(row_data["email"]))
                        print("Nombre: " + str(row_data["Nombre"]))
                        print("Apellidos: " + str(row_data["Apellidos"]))
                        print("Nº Teléfono: " + str(row_data["Nº Teléfono"]))
                        print("ID Number: " + str(row_data["ID Number"]))
                        print("EIN: " + str(row_data["EIN"]))
                        print("Date (m/d/a): " + str(row_data["Date (m/d/a)"]))
                        print("Dirección USA 1/2: " + str(row_data["Dirección USA 1/2"]))
                        print("Dirección USA 2/2: " + str(row_data["Dirección USA 2/2"]))
                        if row_data["Dirección USA 1/2"] and row_data["Dirección USA 2/2"]:
                            address_long = str(row_data["Dirección USA 1/2"]) + ',' + str(row_data["Dirección USA 2/2"])
                            address_parts = address_long.split(',')

                            address = address_parts[0].strip()  # Eliminar espacios en blanco alrededor del número

                            address_city = address_parts[-3].strip() if len(address_parts) > 3 else None  # La ciudad es el tercer elemento desde el final después de la coma
                            address_state = address_parts[-2].strip() if len(address_parts) > 2 else None  # El estado es el segundo elemento desde el final después de la coma
                            address_zip = address_parts[-1].strip()  if len(address_parts) > 1 else None  # El código postal es el último elemento después de la coma
                        print("Address: " + str(address))
                        print("City: " + str(address_city))
                        print("State: " + str(address_state))
                        print("Zip: " + str(address_zip))
                        print("RA: " + str(row_data["RA"]))
                        if row_data["Dirección RA"]:
                            address_parts = str(row_data["Dirección RA"]).split(',')

                            address = address_parts[0].strip()  # Eliminar espacios en blanco alrededor del número
                            address_continue = address_parts[1].strip() if len(address_parts) > 1 else None  # Si hay un segundo componente después de la coma, asignarlo, de lo contrario, None
                            address_city = address_parts[-2].strip()  if len(address_parts) > 2 else None # La ciudad es el penúltimo elemento después de la coma
                            address_zip = address_parts[-1].strip()  if len(address_parts) > 1 else None # El código postal es el último elemento después de la coma
                        print("Dirección RA : " + str(address))
                        print("Dirección RA Continue: " + str(address_continue))
                        print("Dirección RA City: " + str(address_city))
                        print("Dirección RA Zip: " + str(address_zip))



                        print("=====================================")


                        if row_data["Date (m/d/a)"]:
                            try:
                                # Intenta parsear la fecha con el formato '%m/%d/%Y'
                                row_data["Date (m/d/a)"] = datetime.strptime(str(row_data["Date (m/d/a)"]), '%m/%d/%Y')
                            except ValueError:
                                try:
                                    # Si falla, intenta parsear la fecha con el formato '%Y-%m-%d %H:%M:%S'
                                    row_data["Date (m/d/a)"] = datetime.strptime(str(row_data["Date (m/d/a)"]), '%Y-%m-%d %H:%M:%S')
                                except ValueError:
                                    # Si falla nuevamente, manejar el error apropiadamente
                                    print("El valor de la fecha no es válido:", row_data["Date (m/d/a)"])
                                    # O puedes asignar un valor predeterminado
                                    row_data["Date (m/d/a)"] = None

                        if row_data["Nº Teléfono"]:
                            row_data["Nº Teléfono"] = str(row_data["Nº Teléfono"])
                        
                                    


                        try:
                            user = User.objects.filter(email=row_data["email"].lower().replace(" ", "").replace("\n", "").replace("\r", "").replace("\t", "")).first()
                            if user is not None:
                                seller = user.seller

                                # actualizo campos que no tenga el seller y que vengan en el excel
                                if row_data["Nº Teléfono"] and not seller.contact_phone:
                                    seller.contact_phone = row_data["Nº Teléfono"]
                                if row_data["ID Number"] and not seller.business_id_eeuu:
                                    seller.business_id_eeuu = row_data["ID Number"]
                                if row_data["EIN"] and not seller.ein:
                                    seller.ein = row_data["EIN"]
                                if row_data["Date (m/d/a)"] and not seller.incorporation_llc_date:
                                    seller.incorporation_llc_date = row_data["Date (m/d/a)"].strftime('%Y-%m-%d')
                                if not seller.contracted_maintenance_llc_date:
                                    seller.contracted_maintenance_llc_date = datetime.strptime(str("0001/01/01"), '%Y/%m/%d')
                            
                                if row_data["Dirección USA 1/2"] and row_data["Dirección USA 2/2"] and not seller.seller_address:
                                    address_long = str(row_data["Dirección USA 1/2"]) + ',' + str(row_data["Dirección USA 2/2"])
                                    address_parts = address_long.split(',')

                                    address_name = "Dirección del representante de " + seller.name
                                    address = address_parts[0].strip() if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor del número
                                    address_city = address_parts[-3].strip()  if len(address_parts) > 3 else None # La ciudad es el tercer elemento desde el final después de la coma
                                    address_state = address_parts[-2].strip()  if len(address_parts) > 2 else None # El estado es el segundo elemento desde el final después de la coma
                                    address_zip = address_parts[-1].strip()  if len(address_parts) > 2 else None # El código postal es el último elemento después de la coma

                                    direccion = Address.objects.create(
                                        address_name=address_name,
                                        address=address,
                                        address_zip=address_zip,
                                        address_city=address_city,
                                        address_state=address_state
                                    )
                                    seller.seller_address = direccion
                                    seller.save()

                                if row_data["RA"]:
                                    representative = Representative.objects.create(
                                        seller=seller,
                                        first_name=row_data["RA"]
                                    )

                                    if row_data["Dirección RA"]:
                                        address_long = str(row_data["Dirección RA"])
                                        address_parts = address_long.split(',')

                                        address_name = "Dirección del representante de " + seller.name
                                        address = address_parts[0].strip()  if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor
                                        address_continue = address_parts[1].strip() if len(address_parts) > 1 else None  # Si hay un segundo componente después de la coma, asignarlo, de lo contrario, None
                                        address_city = address_parts[-2].strip()  if len(address_parts) > 3 else None # La ciudad es el penúltimo elemento después de la coma
                                        address_zip = address_parts[-1].strip()  if len(address_parts) > 2 else None # El código postal es el último elemento después de la coma

                                        direccion = Address.objects.create(
                                            address_name=address_name,
                                            address=address,
                                            address_continue=address_continue,
                                            address_zip=address_zip,
                                            address_city=address_city,
                                        )
                                        representative.address = direccion
                                        representative.save()

    
                            
                            else:
                                # trato de buscar de nuevo el usuario, buscando si existe un seller con ese name
                                username = slugify(row_data["Nombre LLC"].replace(" ", "").replace("&", "").lower())
                                user = User.objects.filter(username=username).first()
                                if user is not None:
                                    # si existe, asumimos que es el mismo usuario
                                    seller = user.seller
                                    if row_data["Nº Teléfono"] and not seller.contact_phone:
                                        seller.contact_phone = row_data["Nº Teléfono"]
                                    if row_data["ID Number"] and not seller.business_id_eeuu:
                                        seller.business_id_eeuu = row_data["ID Number"]
                                    if row_data["EIN"] and not seller.ein:
                                        seller.ein = row_data["EIN"]
                                    if row_data["Date (m/d/a)"] and not seller.incorporation_llc_date:
                                        seller.incorporation_llc_date = row_data["Date (m/d/a)"].strftime('%Y-%m-%d')
                                    if not seller.contracted_maintenance_llc_date:
                                        seller.contracted_maintenance_llc_date = datetime.strptime(str("0001/01/01"), '%Y/%m/%d')

                                    if row_data["Dirección USA 1/2"] and row_data["Dirección USA 2/2"] and not seller.seller_address:
                                        address_long = str(row_data["Dirección USA 1/2"]) + ',' + str(row_data["Dirección USA 2/2"])
                                        address_parts = address_long.split(',')

                                        address_name = "Dirección del representante de " + seller.name
                                        address = address_parts[0].strip()  if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor del número
                                        address_city = address_parts[-3].strip()  if len(address_parts) > 3 else None # La ciudad es el tercer elemento desde el final después de la coma
                                        address_state = address_parts[-2].strip()  if len(address_parts) > 2 else None # El estado es el segundo elemento desde el final después de la coma
                                        address_zip = address_parts[-1].strip()  if len(address_parts) > 2 else None # El código postal es el último elemento después de la coma

                                        direccion = Address.objects.create(
                                            address_name=address_name,
                                            address=address,
                                            address_zip=address_zip,
                                            address_city=address_city,
                                            address_state=address_state
                                        )
                                        seller.seller_address = direccion
                                        seller.save()

                                    if row_data["RA"]:
                                        representative = Representative.objects.create(
                                            seller=seller,
                                            first_name=row_data["RA"]
                                        )

                                    if row_data["Dirección RA"]:
                                        address_long = str(row_data["Dirección RA"])
                                        address_parts = address_long.split(',')

                                        address_name = "Dirección del representante de " + seller.name
                                        address = address_parts[0].strip()  if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor
                                        address_continue = address_parts[1].strip() if len(address_parts) > 1 else None  # Si hay un segundo componente después de la coma, asignarlo, de lo contrario, None
                                        address_city = address_parts[-2].strip()  if len(address_parts) > 2 else None # La ciudad es el penúltimo elemento después de la coma
                                        address_zip = address_parts[-1].strip()  if len(address_parts) > 1 else None # El código postal es el último elemento después de la coma

                                        direccion = Address.objects.create(
                                            address_name=address_name,
                                            address=address,
                                            address_continue=address_continue,
                                            address_zip=address_zip,
                                            address_city=address_city,
                                        )
                                        representative.address = direccion
                                        representative.save()

                                else:
                                    try:
                                        # antes de crearlo, buscamos si existe seller con ese nombre

                                        username = slugify(row_data["Nombre LLC"].replace(" ", "").replace("&", "").lower())
                                        seller = Seller.objects.filter(shortname=username).first()
                                        if seller is not None:
                                            # si existe, asumimos que es el mismo usuario, actualizamos sus datos y añadimos el mail como secundario
                                            if row_data["Nº Teléfono"] and not seller.contact_phone:
                                                seller.contact_phone = row_data["Nº Teléfono"]
                                            if row_data["ID Number"] and not seller.business_id_eeuu:
                                                seller.business_id_eeuu = row_data["ID Number"]
                                            if row_data["EIN"] and not seller.ein:
                                                seller.ein = row_data["EIN"]
                                            if row_data["Date (m/d/a)"] and not seller.incorporation_llc_date:
                                                seller.incorporation_llc_date = row_data["Date (m/d/a)"].strftime('%Y-%m-%d')
                                            if not seller.contracted_maintenance_llc_date:
                                                seller.contracted_maintenance_llc_date = datetime.strptime(str("0001/01/01"), '%Y/%m/%d')

                                            if row_data["Dirección USA 1/2"] and row_data["Dirección USA 2/2"] and not seller.seller_address:
                                                address_long = str(row_data["Dirección USA 1/2"]) + ',' + str(row_data["Dirección USA 2/2"])
                                                address_parts = address_long.split(',')

                                                address_name = "Dirección del representante de " + seller.name
                                                address = address_parts[0].strip()  if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor del número
                                                address_city = address_parts[-3].strip()  if len(address_parts) > 3 else None # La ciudad es el tercer elemento desde el final después de la coma
                                                address_state = address_parts[-2].strip()  if len(address_parts) > 2 else None # El estado es el segundo elemento desde el final después de la coma
                                                address_zip = address_parts[-1].strip()  if len(address_parts) > 1 else None # El código postal es el último elemento después de la coma

                                                direccion = Address.objects.create(
                                                    address_name=address_name,
                                                    address=address,
                                                    address_zip=address_zip,
                                                    address_city=address_city,
                                                    address_state=address_state
                                                )
                                                seller.seller_address = direccion
                                                seller.valid_emails += '\n' + row_data["email"]
                                                seller.save()

                                            if row_data["RA"]:
                                                representative = Representative.objects.create(
                                                    seller=seller,
                                                    first_name=row_data["RA"]
                                                )

                                            if row_data["Dirección RA"]:
                                                address_long = str(row_data["Dirección RA"])
                                                address_parts = address_long.split(',')

                                                address_name = "Dirección del representante de " + seller.name
                                                address = address_parts[0].strip()  if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor
                                                address_continue = address_parts[1].strip() if len(address_parts) > 1 else None  # Si hay un segundo componente después de la coma, asignarlo, de lo contrario, None
                                                address_city = address_parts[-2].strip()  if len(address_parts) > 2 else None # La ciudad es el penúltimo elemento después de la coma
                                                address_zip = address_parts[-1].strip()  if len(address_parts) > 2 else None # El código postal es el último elemento después de la coma

                                                direccion = Address.objects.create(
                                                    address_name=address_name,
                                                    address=address,
                                                    address_continue=address_continue,
                                                    address_zip=address_zip,
                                                    address_city=address_city,
                                                )
                                                representative.address = direccion
                                                representative.save()
                                        else:   
                                            # asumimos que si no existe el usuario con ese email o seller con ese nombre, hay que crealo
                                            user = User.objects.create(
                                                username=username,
                                                email=row_data['email'],
                                                name=row_data['Nombre'] + ' ' + row_data['Apellidos'],
                                                first_name=row_data['Nombre'],
                                                last_name=row_data['Apellidos'],
                                            )
                                            user.set_password("!123456.")
                                            user.save()

                                            seller = Seller.objects.create(
                                                user=user,
                                                shortname=username,
                                                name=row_data["Nombre LLC"],
                                                first_name=row_data['Nombre'],
                                                last_name=row_data['Apellidos'],
                                                legal_entity='llc',
                                            )
                                            if row_data["Nº Teléfono"]:
                                                seller.contact_phone = row_data["Nº Teléfono"]
                                            if row_data["ID Number"]:
                                                seller.business_id_eeuu = row_data["ID number"]
                                            if row_data["EIN"]:
                                                seller.ein = row_data["EIN"]
                                            if row_data["Date (m/d/a)"] and not seller.incorporation_llc_date:
                                                seller.incorporation_llc_date = row_data["Date (m/d/a)"].strftime('%Y-%m-%d')
                                            if not seller.contracted_maintenance_llc_date:
                                                seller.contracted_maintenance_llc_date = datetime.strptime(str("0001/01/01"), '%Y/%m/%d')

                                            if row_data["Dirección USA 1/2"] and row_data["Dirección USA 2/2"]:
                                                address_long = str(row_data["Dirección USA 1/2"]) + ',' + str(row_data["Dirección USA 2/2"])
                                                address_parts = address_long.split(',')

                                                address_name = "Dirección del representante de " + seller.name
                                                address = address_parts[0].strip()  if len(address_parts) > 1 else None # Eliminar espacios en blanco alrededor
                                                address_city = address_parts[-3].strip()  if len(address_parts) > 3 else None # La ciudad es el tercer elemento desde el final después de la coma
                                                address_state = address_parts[-2].strip()  if len(address_parts) > 2 else None # El estado es el segundo elemento desde el final después de la coma
                                                address_zip = address_parts[-1].strip()  if len(address_parts) > 2 else None # El código postal es el último elemento después de la coma

                                                direccion = Address.objects.create(
                                                    address_name=address_name,
                                                    address=address,
                                                    address_zip=address_zip,
                                                    address_city=address_city,
                                                    address_state=address_state
                                                )
                                                seller.seller_address = direccion
                                                seller.save()

                                            if row_data["RA"]:
                                                representative = Representative.objects.create(
                                                    seller=seller,
                                                    first_name=row_data["RA"]
                                                )

                                                if row_data["Dirección RA"]:
                                                    address_long = str(row_data["Dirección RA"])
                                                    address_parts = address_long.split(',')

                                                    address_name = "Dirección del representante de " + seller.name
                                                    address = address_parts[0].strip()  # Eliminar espacios en blanco alrededor
                                                    address_continue = address_parts[1].strip() if len(address_parts) > 1 else None  # Si hay un segundo componente después de la coma, asignarlo, de lo contrario, None
                                                    address_city = address_parts[-2].strip()  # La ciudad es el penúltimo elemento después de la coma
                                                    address_zip = address_parts[-1].strip()  # El código postal es el último elemento después de la coma

                                                    direccion = Address.objects.create(
                                                        address_name=address_name,
                                                        address=address,
                                                        address_continue=address_continue,
                                                        address_zip=address_zip,
                                                        address_city=address_city,
                                                    )
                                                    representative.address = direccion
                                                    representative.save()  

                                        numcreados += 1
                                    except Exception as err:
                                        print("Error en crear usuario: "+row_data["email"]+ " error ha sido:" + repr(err))
                                        fallidos.append(row_data["email"])



                        
                        except Exception as err:
                            print("Error en actualizar usuario: "+row_data["email"]+ " error ha sido:" + repr(err))
                            # imprime la linea del error con el numero de linea del codigo 
                            print("Error en la línea: " +traceback.format_exc().splitlines()[-2])


                            fallidos.append(row_data["email"])

                        

                    

                messages.success(request, "Vendedores importados")
                return HttpResponseRedirect(reverse("admin:sellers_seller_changelist"))
        else:
            form = SellerXlsxImportForm()

        context = self.admin_site.each_context(request)
        context.update(
            {
                "form": form,
            }
        )
        return TemplateResponse(
            request, "admin/sellers/seller/csv_upload.html", context
        )

    def upload_csv(self, request):
        from muaytax.app_sellers.forms.seller import SellerCsvImportForm

        if request.method == "POST":
            form = SellerCsvImportForm(files=request.FILES)
            if form.is_valid():
                csv_file = request.FILES["csv_upload"]
                file_data = csv_file.read().decode("utf-8-sig")
                csvreader = csv.reader(
                    file_data.splitlines(), delimiter=";", quoting=csv.QUOTE_MINIMAL
                )
                header = None
                for row in csvreader:
                    print(row)
                    if header is None:
                        header = [x.strip() for x in row]
                        continue
                    row_data = dict(zip(header, row))

                    try:
                        user = User.objects.get(email=row_data["User Email"].lower())
                        try:

                            seller = user.seller
                            for item in header:
                                if (
                                    not item.strip().startswith("IVA ")
                                    or row_data[item].strip() == ""
                                ):
                                    continue
                                _, country_code = item.split(" ")

                                print(f"{seller.shortname} - País encontrado: {country_code}")

                                vat_vies = False
                                try:
                                    if row_data["VIES"].upper() == "SÍ":
                                        vat_vies = True
                                    elif row_data["VIES"].upper() == "NO":
                                        vat_vies = False
                                    elif country_code != "ES":
                                        vat_vies = True
                                    else:
                                        vat_vies = False
                                except:
                                    print("Error in get Vies. Set to False.")
                                    vat_vies = False

                                print(f"{seller.shortname} - VIES: {vat_vies}")

                                is_contracted = False
                                try:
                                    if row_data["Contabilidad EMPRESA/AUTÓNOMO"].upper() == "SÍ":
                                        is_contracted = True
                                    elif row_data["Contabilidad EMPRESA/AUTÓNOMO"].upper() == "NO":
                                        is_contracted = False
                                    else:
                                        is_contracted = False
                                except:
                                    print("Error in get is_contracted. Set to False.")
                                    is_contracted = False

                                print(f"{seller.shortname} - Is Contracted: {is_contracted}")

                                status = SellerVatStatus.objects.get(pk='on')

                                print(f"{seller.shortname} - Status: {status}")

                                SellerVat.objects.update_or_create(
                                    seller=seller,
                                    vat_country_id=country_code,
                                    vat_vies=vat_vies,
                                    is_contracted=is_contracted,
                                    status=status,
                                    defaults={
                                        "vat_number": row_data[item],
                                    },
                                )
                                print(f"{seller.shortname} - País IVA creado: {country_code}")

                        except Exception as err:
                            print("Error en actualizar usuario" + repr(err))


                    except User.DoesNotExist:
                        newusername = slugify(row_data["Empresa de facturacion"].replace(" ", "").lower())
                        user_form = SignupForm(
                            {
                                "email": row_data["User Email"].lower(),
                                "username": newusername,
                                "password1": "!123456.",
                                "password2": "!123456.",
                            }
                        )
                        if not user_form.is_valid():
                            messages.warning(request, user_form.errors)
                            continue
                        user = user_form.save(request)
                        user.name = (
                            row_data["Nombre de facturacion"].capitalize()
                            + " " + row_data["Apellidos de facturacion"].capitalize()
                        ).strip()
                        user.save()
                        email_address: EmailAddress = EmailAddress.objects.get(
                            user=user,
                            email=user.email,
                        )
                        # email_address.send_confirmation(request=request, signup=True)

                        tipo, country = row_data["Tipo"].strip().split(" ")
                        legal_entity = None
                        if tipo == "Autonomo":
                            legal_entity = "self-employed"
                        elif row_data["Empresa de facturacion"].lower().endswith("llc"):
                            legal_entity = "llc"
                        elif tipo == "Empresa" or row_data["Empresa de facturacion"].lower().endswith("sl") or row_data[
                            "Empresa de facturacion"].lower().endswith("s.l."):
                            legal_entity = "sl"
                        country_registration = None
                        is_eu_seller = False
                        try:
                            country_registration = Country.objects.get(iso_code=country)
                            is_eu_seller = country_registration.is_european_union
                        except Country.DoesNotExist:
                            pass
                        seller, _ = Seller.objects.update_or_create(
                            user=user,
                            defaults={
                                "name": row_data["Empresa de facturacion"],
                                "shortname": slugify(row_data["Empresa de facturacion"].replace(" ", "").lower()),
                                "legal_entity": legal_entity,
                                "is_eu_seller": is_eu_seller,
                                "country_registration": country_registration,
                                "nif_registration": row_data["NIF"],
                                "oss": row_data["OSS?"] == "Si",
                                "oss_country": Country.objects.filter(
                                    iso_code=row_data["PAIS OSS"]
                                ).first(),
                                "vat_no_origin_country": row_data["VAT NUMBER"],
                            },
                        )
                        for item in header:
                            if (
                                not item.strip().startswith("IVA ")
                                or not row_data[item].strip()
                            ):
                                continue
                            _, country_code = item.split(" ")

                            print(f"{seller.shortname} - Pais encontrado: {country_code}")

                            vat_vies = False
                            try:
                                if row_data["VIES"].upper() == "SÍ":
                                    vat_vies = True
                                elif row_data["VIES"].upper() == "NO":
                                    vat_vies = False
                                elif country_code != "ES":
                                    vat_vies = True
                                else:
                                    vat_vies = False
                            except:
                                print("Error in get Vies. Set to False.")
                                vat_vies = False

                            print(f"{seller.shortname} - VIES: {vat_vies}")

                            is_contracted = False
                            try:
                                if row_data["Contabilidad EMPRESA/AUTÓNOMO"].upper() == "SÍ":
                                    is_contracted = True
                                elif row_data["Contabilidad EMPRESA/AUTÓNOMO"].upper() == "NO":
                                    is_contracted = False
                                else:
                                    is_contracted = False
                            except:
                                print("Error in get is_contracted. Set to False.")
                                is_contracted = False

                            print(f"{seller.shortname} - Is Contracted: {is_contracted}")

                            status = SellerVatStatus.objects.get(pk='on')

                            print(f"{seller.shortname} - Status: {status}")

                            SellerVat.objects.update_or_create(
                                seller=seller,
                                vat_country_id=country_code,
                                vat_vies=vat_vies,
                                is_contracted=is_contracted,
                                status=status,
                                defaults={
                                    "vat_number": row_data[item],
                                },
                            )
                            print(f"{seller.shortname} - Pais IVA creado: {country_code}")

                messages.success(request, "Vendedores importados")
                return HttpResponseRedirect(reverse("admin:sellers_seller_changelist"))
        else:
            form = SellerCsvImportForm()

        context = self.admin_site.each_context(request)
        context.update(
            {
                "form": form,
            }
        )
        return TemplateResponse(
            request, "admin/sellers/seller/csv_upload.html", context
        )

    def convertCurrencyToEur(self, currency, amount, date):
        baseUrl = 'https://api.currencyapi.com/v3/'
        apikey = settings.CURRENCY_API_KEY
        taxConversion = 0
        if currency is not None and currency == 'EUR':
            taxConversion = 1
        elif date is not None and currency is not None:

            try:
                url = baseUrl
                currentDate = datetime.now()
                invoiceDate = datetime.strptime(date + 'T23:59:59', '%Y-%m-%dT%H:%M:%S')

                if invoiceDate < currentDate:
                    url += 'historical'
                    url += '?currencies=EUR'
                    url += '&date=' + str(date)
                else:
                    url += 'latest'
                    url += '?currencies=EUR'
                url += '&base_currency=' + currency
                url += '&apikey=' + apikey

                r = requests.get(url)
                if r is not None and r.status_code == 200:
                    data = r.json()['data']
                    print(data)
                    taxConversion = float(data['EUR']['value'])
                print('taxConversion:' + str(taxConversion))
            except Exception as error:
                print('Error on getTaxConversion: ', error)

        return taxConversion * amount 

    # def get_urls(self):
    #     urls = super().get_urls()
    #     custom_urls = [
    #         path('upload_excel_date/', self.admin_site.admin_view(self.upload_excel_date), name='seller_upload_excel_date'),
    #         path('download_template/', self.admin_site.admin_view(self.download_template_date), name='seller_download_template_date'),
    #     ]
    #     return custom_urls + urls
    
    @transaction.atomic
    def upload_excel_date(self, request):
        if request.method == "POST":
            form = ExcelUploadDateForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    warnings = Seller.process_excel(request.FILES['excel_file'], request)
                    if warnings:
                        for warning in warnings:
                            messages.warning(request, warning)
                    messages.success(request, "Archivo de Excel procesado exitosamente")
                    return redirect("..")
                except ValidationError as e:
                    messages.error(request, f"Error procesando el archivo de Excel: {e}")
        else:
            form = ExcelUploadDateForm()

        context = {
            'form': form,
            'title': "Cargar archivo de Excel",
            'opts': self.model._meta,
            'app_label': self.model._meta.app_label,
        }
        return render(request, "admin/sellers/seller/upload_excel_date.html", context)
    
    def download_template_date(self, request):
        file_path = os.path.join('muaytax/static/assets/excel/ADD_date_seller.xlsx')
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"El archivo {file_path} no se encuentra.")
        return FileResponse(open(file_path, 'rb'), as_attachment=True, filename='ADD_date_seller.xlsx')