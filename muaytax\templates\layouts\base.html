{% load static %}

<!DOCTYPE html>
<html lang="es">
<head>
    <title>
       Muaytax - {% block title %}{% endblock title %} | App
    </title>
    <!-- HTML5 Shim and Respond.js IE11 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 11]>
        <script src="{{ STATIC_URL }}assets/cdns_locals/js/html/html5shiv.min-v3.7.3.js"></script>
        <script src="{{ STATIC_URL }}assets/cdns_locals/js/respond/respond.min-v1.4.2.js"></script>
        <![endif]-->
    <!-- Meta -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="we love helping entrepreneurs"/>
    <meta name="keywords" content="Professional consulting, amazon consulting services, amazon consulting llc, amazon consulting experts  "/>
    <meta name="author" content="MuayTax" />

    {% if not is_production %}
        <meta name="robots" content="noindex">
    {% endif %}

    <!-- muaytax icon -->
    <link rel="stylesheet" href="{% static 'assets/images/iconos/style.css' %}"/>

    <!-- fontawesome icon -->
    <link rel="stylesheet" href="{% static 'assets/fonts/fontawesome/css/fontawesome-all.min.css' %}"/>

    <!-- Favicon icon -->
    <link rel="icon" href="{% static 'assets/images/favicon.ico' %}" type="image/x-icon"/>

    <!-- animation css -->
    <link rel="stylesheet" href="{% static 'assets/css/plugins/animate.min.css' %}"/>
    <link rel="stylesheet" href="{% static 'assets/css/plugins/perfect-scrollbar.css' %}"/>

    <!-- vendor css -->
    <link rel="stylesheet" href="{% static 'assets/css/style.min.css' %}"/>

    <!-- branding css -->
    <link rel="stylesheet" href="{% static 'assets/css/style.branding.css' %}"/>

    <!-- gravatar css -->
    <link rel="stylesheet" href="{% static 'assets/css/plugins/gravatar.css' %}"/>

    <!-- Specific CSS goes HERE -->
    {% block stylesheets %}{% endblock stylesheets %}

</head>
<body>

    <!-- [ Pre-loader ] start -->
      <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
     </div>
    <!-- [ Pre-loader ] End -->

    {% if request.user.has_permission_manager %}
        <style>
            .fullbgManager{margin-left: 0px !important; width: auto !important;}
        </style>
    {# {% include "includes/sidebar-manager.html" %} #}
    {% else %}
        {% if request.GET.popup is None or request.GET.popup != 'true' %}
            {% include "includes/sidebar.html" %}
        {% else %}
            <style>
                .fullbgManager{margin-left: 0px !important; width: auto !important;}
            </style>
        {% endif %}
    {% endif %}


    {% if request.GET.popup is None or request.GET.popup != 'true' %}
        {% include "includes/navigationManager.html" %}
    {% endif %}
    
    {# {% include "includes/navigation-mobile.html" %} #}
       
    {#  {% include "includes/header-user-list.html" %} #}

    {#  {% include "includes/header-chat.html" %} #}
    

    <!-- [ Main Content ] start -->
    <div class="pcoded-main-container fullbgManager">
        <div class="pcoded-wrapper">
            <div class="pcoded-content">
                <div class="pcoded-inner-content">
                    <!-- [ breadcrumb ] start -->
                    {% if request.GET.popup is None or request.GET.popup != 'true' %}
                        {% block breadcrumb %}{% endblock breadcrumb %}
                    {% endif %}
                    <!-- [ breadcrumb ] end -->
                    <div class="main-body">
                        <div class="page-wrapper">
                            <!-- [ Main Content ] start -->
                            {% block content %}{% endblock content %}
                            <!-- [ Main Content ] end -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->

    {% include "includes/scripts.html" %}
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-S57KZMRE18"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-S57KZMRE18');
    </script>
    
    {% if user.role == 'seller' %}
        <script type="text/javascript">
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "gmyw1r8xk9");
        </script>
    {% endif %}

    {% if user.role == 'manager' %}
        <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "i8d3hdjsv0");
        </script>
    {% endif %}

    <!-- Specific Page JS goes HERE  -->
    {% block javascripts %}{% endblock javascripts %}

    <!--<script src="{% static 'assets/js/menu-setting.js' %}"></script>-->

</body>
</html>
