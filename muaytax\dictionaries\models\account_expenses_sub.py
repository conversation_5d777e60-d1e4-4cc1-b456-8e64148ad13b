from django.db import models

class SubAccountExpenses(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=10,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "SubCuenta de Gastos"
        verbose_name_plural = "SubCuentas de Gastos"
    
    def __str__(self):
        return self.description
    
# @admin.register(SubAccountExpenses)
# class SubAccountExpensesAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]