{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/uppy.min.css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/importDUAwizard/dua-wizard.css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/importDUAwizard/dua-wizard-info-navs.css" />
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

{% endblock stylesheets %}

{% block title %}
  Lector de Importaciones
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              Facturas: Lector de Importaciones
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                {% if user.role == 'manager' %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_sellers:summary' seller.shortname %}">
                      {% if seller.name is not None %}
                        {{ seller.name.capitalize }}
                      {% else %}
                        Resumen Usuario
                      {% endif %}
                    </a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="./import">Lector de Importaciones</a>
                  </li>
                {% else %}
                  <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">
                    Facturas
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}">
                    Lector de Importaciones
                  </a>
                </li>
                {% endif %}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class=" vue" id="uploads">
    {% if not canShowInvoices and not canShowTXT and user.role == "seller" %}
      <div class="center_items margintop40px" >
        <div class="select_iae_div text_font" style="background: #fff3cd;">
          <br/>
          <h2> {% if cantShowTitle %} {{ cantShowTitle }} {% else %} No disponible {% endif %}</h2>
          <h2><i class="fas fa-duotone fa-triangle-exclamation"></i></h2>
          <br/>
          <h4 style= "width: max-content;"> {% if cantShowMessage %} {{ cantShowMessage }} {% else %} Tienes Deshabilitado este apartado. {% endif %} </h4>
          <br/>
        </div>
      </div>
    {% elif user.role == "seller" %}
      <div v-if="dj.economic_activity.length < 1" class="center_items margintop40px">
        <div class="select_iae_div text_font" style="background: #fff3cd;">
          <br>
          <h3><b>No hay actividades económicas disponibles.</b></h3>
          <br>
          <h4 style= "width: max-content;">No puede subir documentos porque no tienen ningun IAE asociado.</h4>
          <h4>Contacte con soporte para solucionar el problema.</h4>
        </div>
      </div>
      <div v-else-if="dj.seller_vat.length < 1 && dj.seller.contracted_accounting != true && dj.seller.contracted_maintenance_llc != true" class="center_items margintop40px">
        <div class="select_iae_div text_font" style="background: #fff3cd;">
          <br>
          <h3 style= "width: max-content;"><b>No tiene contabilidad contratada ni países iva contratatados.</b></h3>
          <br>
          <h4>Contacte con soporte para solucionar el problema.</h4>
        </div>
      </div>
      <div v-else style="display:none;" v-show="true">
        <ul class="nav nav-tabs d-none" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button :class="{'nav-link': true, 'active': dj.economic_activity.length > 1}" id="economic_activity-tab" data-bs-toggle="tab" data-bs-target="#economic_activity" type="button" role="tab" aria-controls="economic_activity" aria-selected="true">Step 1</button>
          </li>
          <li class="nav-item" role="presentation">
            <button :class="{'nav-link': true, 'active': dj.economic_activity.length === 1}" id="select_dua-tab" data-bs-toggle="tab" data-bs-target="#select_dua" type="button" role="tab" aria-controls="select_dua" aria-selected="false">Step 2</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="list_dua-tab" data-bs-toggle="tab" data-bs-target="#list_dua" type="button" role="tab" aria-controls="list_dua" aria-selected="false">Step 3</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="select_invoices-tab" data-bs-toggle="tab" data-bs-target="#select_invoices" type="button" role="tab" aria-controls="select_invoices" aria-selected="false">Step 4</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="close_invoices-tab" data-bs-toggle="tab" data-bs-target="#close_invoices" type="button" role="tab" aria-controls="close_invoices" aria-selected="false">Step 5</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="process_complete-tab" data-bs-toggle="tab" data-bs-target="#process_complete" type="button" role="tab" aria-controls="process_complete" aria-selected="false">Step 6</button>
          </li>
        </ul>
        <div class="tab-content center_items" id="myTabContent">

          <!-- NAV BUTTONS AFTER FINISHING WIZARD-->
          <div class="nav_buttons_top d-none" id="nav_back_charger">
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"  
                      href="/sellers/{{seller.shortname}}/invoices/upload" 
                      role="button">CARGAR FACTURAS</a>
              </span>
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"
                      href="/sellers/{{seller.shortname}}/invoices/upload?type=ticket"  
                      role="button">CARGAR TICKETS/RECIBOS/CUOTAS AUTÓNOMO</a>
              </span>
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"  
                      href="/sellers/{{seller.shortname}}/invoices/upload?type=amz-txt-eur"  
                      role="button">CARGAR INFORME VENTAS AMAZON (TXT)</a>
              </span>
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"  
                      href="/sellers/{{seller.shortname}}/invoices/upload?type=payroll"
                      role="button">NÓMINAS</a>
              </span>
          </div>
          <!-- NAV BUTTONS AFTER FINISHING WIZARD-->

          <!--NAV TABS CUSTOM GUIDE-->
          <div class="div_custom_nav_tabs">
            <ul class="nav nav-tabs nav_tabs_show_steps" id="myTab2" role="tablist">
                <li class="nav-item" v-for="(step, index) in steps" :key="index" role="presentation">
                  <button :class="{ active: activeStep === index, 'disabled-tab': true }" class="nav-link nav-tabs-custom text_font fade-in-tab" style="border-radius: 10px" data-bs-toggle="tab" data-bs-target="#" type="button" role="tab" aria-controls="" aria-selected="false">[[step]]</button>
                </li>
              </ul>
          </div>
          <!--NAV TABS CUSTOM GUIDE-->

          <!-- SELECT ECONOMIC ACTIVITY -->
          <div :class="{'tab-pane fade': true, 'show active': dj.economic_activity.length > 1}" id="economic_activity" role="tabpanel" aria-labelledby="economic_activity-tab">
            <div class="center_items select_iae_div">
              <div class="row" v-if="dj.economic_activity.length >= 1">
                <div class="col-12 mt-3 text-center">
                  <div class="text_size24 text_font marginbottom30px" style="color: var(--color-black-blue);">Debe Seleccionar el tipo de actividad economica</div>
                </div>
                <div class="center_items">
                  <select 
                          class="form-select form-control select_iae text_size14" 
                          name="iae" 
                          id="inputEconomicActivity" 
                          v-model="inputEconomicActivity">
                    <option :value="null" 
                            selected>
                            Seleccionar
                    </option>
                    <option v-for="iae in dj.economic_activity" 
                            :key="iae.pk" 
                            :value="iae.pk"
                            :selected="dj.economic_activity.length === 1">
                            [[ iae.description ]]
                    </option>
                  </select>
                </div>
              </div>
              <div class="row" v-else>
                <div class="col-12 mt-3 text-center alert alert-warning rounded">
                  <br>
                  <h3><b>No hay actividades económicas disponibles.</b></h3>
                  <br>
                  <h4>No puede subir documentos porque no tienen ningun IAE asociado.</h4>
                  <h4>Contacte con soporte para solucionar el problema.</h4>
                </div>
              </div>
            </div>
            <div class="buttons_nav_div">
                <span class="col nav_button_end">
                  <a
                    class="btn button_dark_blue" 
                    @click="changeTab('select_dua-tab'); activeStep = ++activeStep;" 
                    href="#" 
                    role="button" 
                    :disabled="inputEconomicActivity === null"
                    :class="{ 'disabled': inputEconomicActivity === null }"
                    >SIGUIENTE</a>
                </span>
              </div>
          </div>
          <!-- SELECT ECONOMIC ACTIVITY -->

          <!-- SELECT OR UPLOAD DUA -->
          <div :class="{'tab-pane fade': true, 'show active': dj.economic_activity.length === 1}" id="select_dua" role="tabpanel" aria-labelledby="select_dua-tab">
            <div class="center_items">
              <form   
                    method="post" 
                    enctype="multipart/form-data"
                    action="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}"
                    id="myDropzoneImportDua" 
                    class="dropzone select_iae_div">
                    {% csrf_token %}
                    <div class="dz-message mu-black" style="border: none;">
                      <div v-if ="queue_complete_dua == false && (dj.seller.legal_entity != 'self-employed' || dj.seller.legal_entity != 'sl')">
                        <div id="spinner" class="spinner-border spinner70px" role="status">
                          <span class="sr-only">Loading...</span>
                        </div>
                        <p class="text_font" style="color:black"> Cargando facturas, por favor espere</p>
                      </div>
                      <div v-else>
                        <i class="mu mu-up mu-black icon_70px" style="font-weight:bold"></i>
                        <p class= "text_size24 text_font mu-black">Subir DUA</p>
                        <p class="text_font" style="color:black">Pincha o arrastra el archivo para subirlo</p>
                        <span v-if= "(dj.seller.legal_entity == 'self-employed' || dj.seller.legal_entity == 'sl')">
                          <p class="text_font" style="color:black">Sólo puedes subir un único DUA**</p>
                        </span>
                      </div>
                    </div>
                    <div class="d-none">
                      <input
                            type="text"
                            id="id"
                            name="seller"
                            value="{{ seller.pk }}"
                        />
                      <input
                            type="text"
                            id="invoice_category"
                            name="invoice_category"
                            v-model="inputCategoriaExpenses"
                        />
                        <input
                              type="text"
                              id="invoice_type"
                              name="invoice_type"
                              v-model="inputTypeDua"
                        />
                        <input
                              type="text"
                              id="economic_activity"
                              name="iae"
                              v-model="inputEconomicActivity"
                        />
                      <div class="fallback ">
                          <input
                                type="file"
                                id="id_file"
                                name="form_create.file.name"
                          />
                        </div>
                    </div>
              </form>
            </div>
            <div v-if="dj.seller.legal_entity == 'self-employed' || dj.seller.legal_entity== 'sl'"
                class="buttons_nav_div">
              <span class="col">
              <a  v-if="dj.economic_activity.length > 1"
                  class="btn button_transparent" 
                  @click="changeTab('economic_activity-tab'); activeStep = --activeStep;" 
                  href="#" 
                  role="button"
              ><i class="mu mu-l-chevron mu-black"></i>ATRÁS</a>
              </span>
              <span class="col nav_button_center">
              <a  class="btn button_dark_blue" 
                  @click="have_dua = false; changeTab('select_invoices-tab'); activeStep = ++activeStep; steps = changeIndexCustomTab('case1', false);"
                  href="#" 
                  role="button"> 
                  NO TENGO DUA
              </a>
              </span>
              <span class="col nav_button_end">
              <a  class="btn button_dark_blue"
                  style="width: 240px !important;"
                  @click="have_dua = true; changeTab('list_dua-tab'); activeStep = ++activeStep; steps = changeIndexCustomTab('case2', false);" 
                  href="#"
                  :class="{ 'disabled': IDDuaInvoices.length < 1 }"
                  role="button">
                  SELECCIONAR DUA CARGADA
              </a>
              </span>
            </div>
            <div class="buttons_nav_div" v-else>
              <span v-if = "dj.economic_activity.length > 1"  class="col">
              <a  class="btn button_transparent"
                  id = "back_upNoDUALLC"
                  :class="{disabled: queue_complete_dua==false}"
                  @click="changeTab('economic_activity-tab'); activeStep = --activeStep;" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>ATRÁS
              </a>
              </span>
            </div>
          </div>
          <!-- SELECT OR UPLOAD DUA -->

          <!-- LIST OF DUA FILE -->
          <div class="tab-pane fade" id="list_dua" role="tabpanel" aria-labelledby="list_dua-tab">
            <div id="div_select_list_dua" class= "select_iae_div">
              <div class="text_size24 text_font div_select_invoices center_items" style="color: var(--color-black-blue);">
                Selecciona un DUA para asociar
              </div>
              <div class= "center_items gap30px paddingbottom40px">
                <span v-for="(item, index) in IDDuaInvoices" 
                      :key="index" 
                      :id="'IDDuaInvoices' + index" 
                      class="name_invoice_div">
                      [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                  <div class= "col icons_container">
                    <input  type="checkbox" 
                            class="custom-checkbox"
                            @click="handleDUAcheckbox(index)"
                            :id="'selectDUA' + index"
                            :value="item"
                            v-model= "SelectdDUAINvoices">
                    <a  href="#" 
                        @click="showInvoice(item)" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Ver factura">
                        <i class="mu mu-xl mu-eye mu-black"></i>
                    </a>
                    <a  :href="'/media/' + item.file" 
                        download data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Descargar factura">
                        <i class="mu mu-xl mu-down mu-black"></i>
                    </a>
                  </div>
                </span>
              </div>
            </div>
            <div class="buttons_nav_div">
              <span class="col">
              <a  class="btn button_transparent" 
                  @click="changeTab('select_dua-tab'); clearSelectdDUAINvoices(); activeStep = --activeStep; steps = changeIndexCustomTab('case0', true); " 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  ATRÁS
              </a>
              </span>
              <span class="col nav_button_end">
                <a  class="btn button_dark_blue" 
                    @click="changeTab('select_invoices-tab'); activeStep = ++activeStep;" 
                    href="#" 
                    role="button" 
                    :disabled="SelectdDUAINvoices.length < 1"
                    :class="{ 'disabled': SelectdDUAINvoices.length < 1 }"
                    >SIGUIENTE
                </a>
              </span>
            </div>
          </div>
          <!-- LIST OF DUA FILE -->

          <!-- SELECT INVOICES -->
          <div class="tab-pane fade" id="select_invoices" role="tabpanel" aria-labelledby="select_invoices-tab">
            <div id = "div_list_import" class="list_invoices_div"> 
              <!--FACTURAS CUANDO TENEMOS DUA-->
              <div v-show="IDImportInvoices.length >= 1 && have_dua == true" >
                <div class= " div_select_invoices text_size16 text_font color_black-blue center_items fade-in">
                  Para finalizar el proceso de carga, tienes que asociar mínimo una factura al DUA.
                </div>
                <div class= "center_items gap30px">
                  <span v-for="(item, index) in IDImportInvoices" 
                        :key="index" 
                        :id="'IDImportInvoices' + index" 
                        class="name_invoice_div fade-in">
                        [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                    <div class="col icons_container">
                      <input
                            class="custom-checkbox"
                            type="checkbox" 
                            :id="'selectImport' + index"
                            :value="item"
                            v-model= "SelectdImportInvoices">
                      <a  href="#" 
                          @click="showInvoice(item)" 
                          data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Ver factura">
                          <i class="mu mu-xl mu-eye mu-black"></i>
                      </a>
                      <a  :href="'/media/' + item.file" 
                          download data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Descargar factura">
                          <i class="mu mu-xl mu-down mu-black"></i>
                      </a>
                    </div>
                  </span>
                </div>
              </div>
              <!--FACTURAS CUANDO TENEMOS DUA-->

              <!--FACTURAS CUANDO NO TENEMOS DUA-->
              <div v-show="listImportInvoices.length >= 1 && have_dua == false" >
                <div class= "text_size16 text_font div_select_invoices color_black-blue center_items fade-in">
                  Facturas cargadas
                </div>
                <div class= "center_items">
                  <span  v-for="(item, index) in listImportInvoices" 
                        :key="index" 
                        :id="'listImportInvoices' + index" 
                        class="name_invoice_div fade-in">
                        [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                    <div class="col icons_container">
                      <a  href="#" 
                          data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Factura procesada">
                          <i class="mu mu-xl mu-check mu-green"></i>
                      </a>
                      <a  href="#" 
                          @click="showInvoice(item)" 
                          data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Ver factura">
                          <i class="mu mu-xl mu-eye mu-black"></i>
                      </a>
                      <a  :href="'/media/' + item.file" 
                          download data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Descargar factura">
                          <i class="mu mu-xl mu-down mu-black"></i>
                      </a>
                    </div>
                    <span v-if="index === listImportInvoices.length - 1" ref="lastItem"></span>
                  </span>
                  
                </div>
              </div>
              <!--FACTURAS CUANDO NO TENEMOS DUA-->

              <!--DROPZONE PARA SUBIR FACTURAS-->
              <div id = "containerDropzoneImp" class= " form_upload_invoices">

                <div v-if="listImportInvoices.length >= 1 || (have_dua== true && IDImportInvoices.length >= 1 )" class= "marginbottom30px"> 
                  <!--div de espacio entre el dropzone y el listado de facturas cuando se suben importaciones o hay importaciones huerfanas-->
                </div>
                <form   method="post" enctype="multipart/form-data"
                        class="dropzone"
                        action="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}"
                        id="myDropzoneImportInvoice" >
                        {% csrf_token %}
                        <div id="import_dz-message" class="dz-message ">
                          <div v-if="queue_complete_imp == false">
                            <div style="margin-bottom:16px;">
                              <div v-if="listImportInvoices.length >= 1 || (have_dua== true && IDImportInvoices.length >= 1 )" >
                                <div id="spinner" class="spinner-border" role="status">
                                  <span class="sr-only">Loading...</span>
                                </div>
                              </div>
                              <div v-else>
                                <div id="spinner" class="spinner-border spinner70px" role="status">
                                  <span class="sr-only">Loading...</span>
                                </div>
                              </div>
                            </div>
                            <p class="text_font" style="color:black"> Cargando facturas, por favor espere</p>
                          </div>
                          <div v-else>
                            <span v-if="listImportInvoices.length >= 1 || (have_dua== true && IDImportInvoices.length >= 1 )" >
                              <i class="mu mu-up mu-black" style="font-size:35px;"></i>
                              <p class= "text_font mu-black" style="font-size:13px; font-weight:bold;">Subir nueva factura</p>
                              <p class="text_font" style="color:black;" >Pincha o arrastra el archivo para subirlo</p>
                            </span>
                            <span v-else >
                              <i class="mu mu-up mu-black icon_70px" style="font-weight:bold"></i>
                              <p class= "text_size24 text_font mu-black">Subir Facturas</p>
                              <p class="text_font" style="color:black" >Pincha o arrastra el archivo para subirlo</p>
                            </span>
                          </div>
                        </div>
                        <div class="d-none">
                          <input
                              type="text"
                              id="id"
                              name="seller"
                              value="{{ seller.pk }}"
                            />
                          <input
                                type="text"
                                id="invoice_category"
                                name="invoice_category"
                                v-model="inputCategoriaExpenses"
                            />
                            <input
                                type="text"
                                id="invoice_type"
                                name="invoice_type"
                                v-model="inputTypeImport"
                            />
                            <input
                                type="text"
                                id="economic_activity"
                                name="iae"
                                v-model="inputEconomicActivity"
                            />
                          <div class="fallback ">
                              <input
                                type="file"
                                id="id_file"
                                name="form_create.file.name"
                              />
                            </div>
                        </div>
                </form>
              </div>
              <!--DROPZONE PARA SUBIR FACTURAS-->

              <div class= "paddingbottom40px">
                <span v-show="(have_dua == true && listImportInvoices.length >= 1) || (have_dua == true && IDImportInvoices.length >= 1)" class="col">
                  <a class="btn button_dark_blue" 
                    @click="changeTab('close_invoices-tab'); activeStep = ++activeStep;" 
                    href="#" 
                    role="button" 
                    :disabled="SelectdImportInvoices.length < 1"
                    :class="{ 'disabled': SelectdImportInvoices.length < 1 }"
                    >ASOCIAR</a>
                </span>
                <span v-show="listImportInvoices.length >= 1 && have_dua == false">
                  <a class="btn button_dark_blue" 
                    @click="changeTab('process_complete-tab'); activeStep = ++activeStep;"
                    href="#" 
                    role="button"
                    :class="{disabled: queue_complete_imp==false}"
                    >FINALIZAR</a>
                </span>
              </div>

            </div> 
            <div>
              <div v-if="((have_dua == true && listImportInvoices.length >= 1) || (have_dua == true && IDImportInvoices.length >= 1)) ||
                (listImportInvoices.length >= 1 && have_dua == false)" style="margin-top: 40px;">
              </div>
              <span class="col">
                <a v-show="(listImportInvoices.length < 1 && have_dua == false)"
                  id="back_upNoDUA"
                  class="btn button_transparent" 
                  @click="changeTab('select_dua-tab'); activeStep = --activeStep; steps = changeIndexCustomTab('case0', true);" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  ATRÁS</a>
              </span>
              <span class="col" >
                <a v-show="(SelectdDUAINvoices.length == 1 && have_dua == true)"
                  id="back_upyesDUA"
                  class="btn button_transparent"
                  @click="changeTab('list_dua-tab'); clearSelectdDUAINvoices(); activeStep = --activeStep;" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  ATRÁS</a>
              </span>
              <div class="">
                <a v-if="(listImportInvoices.length >= 1 && have_dua == false) || dua_dropzone == true"
                  class="btn nav_button_new"
                  :class="{disabled: queue_complete_imp==false}"
                  @click="newIportation()" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  VOLVER A EMPEZAR</a>
              </div>
            </div>
          </div>
          <!-- SELECT INVOICES -->

          <!-- CLOSE INVOICES -->
          <div class="tab-pane fade" id="close_invoices" role="tabpanel" aria-labelledby="close_invoices-tab">
            <div id= "div_list_invoices_close" class="select_iae_div">
              <div class= "text_size16 text_font color_black-blue center_items div_select_invoices">
                ¿Quieres cerrar las facturas?
              </div>
              <div class= "center_items gap30px marginbottom30px">
                <span v-for="(item, index) in SelectdImportInvoices" 
                      :key="index" 
                      :id="'SelectdImportInvoices' + index" 
                      class="name_invoice_div text_font">
                      [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                  <div class=" col icons_container">
                    <label class="switch">
                      <input 
                        class="toggle" 
                        type="checkbox" 
                        :id="'switchImports' + index"
                        :checked="item.is_dua_completed"
                        @change="toggleIsDuaCompleted(index, $event.target.checked)"
                      >
                      <span class="slider"></span>
                      <span class="yes-text text_font">Sí</span>
                      <span class="no-text text_font">No</span>
                    </label>
                    <a  href="#" 
                        @click="showInvoice(item)" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Ver factura">
                        <i class="mu mu-xl mu-eye mu-black"></i>
                    </a>
                    <a  :href="'/media/' + item.file" 
                        download data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Descargar factura">
                        <i class="mu mu-xl mu-down mu-black"></i>
                    </a>
                  </div>
                  <span v-if="index === listImportInvoices.length - 1" ref="lastItem"></span>
                </span>
              </div>
              <div class="paddingbottom40px">
                <span class="col center_items">
                  <a  v-show="SelectdDUAINvoices.length > 0" 
                      class="btn button_dark_blue"  
                      @click="sendBack()" 
                      id="submit-dua-selected"
                      href="#"
                      role="button">
                      FINALIZAR
                  </a>
                  <a  v-show="SelectdDUAINvoices.length < 1"  
                      class="btn button_dark_blue"  
                      id="submit-button"
                      href="#"
                      role="button">
                      FINALIZAR
                  </a>
                </span>
              </div>
            </div>
            <div class="margintop40px">
                <a  class="btn nav_button_new"
                    :class="{disabled: queue_complete_imp==false}"
                    @click="newIportation()" 
                    href="#" 
                    role="button">
                    <i class="mu mu-l-chevron mu-black"></i>
                    VOLVER A EMPEZAR
                </a>
              </div>
          </div>
          <!-- CLOSE INVOICES -->

          <!-- PROCESS COMPLETE -->
          <div class="tab-pane fade" id="process_complete" role="tabpanel" aria-labelledby="process_complete-tab">
            <div class= "center_items select_iae_div">
              <div class="col ">
                <i class="mu mu-circle-check mu-green icon_70px"></i>
              </div>
              <div class= "text_size16 text_font color_black-blue">
                Proceso completado
              </div>
            </div>

            <div class="buttons_nav_div">
                <span class="col nav_button_center">
                  <a  id="new-import-button"
                      class="btn nav_button_new" 
                      @click="newIportation()" 
                      href="#" 
                      role="button">
                      CARGAR NUEVA IMPORTACIÓN
                  </a>
                </span>
            </div>
          <!-- PROCESS COMPLETE -->
      </div>
    {% else %}
      <div style="display:none;" v-show="true">
        <ul class="nav nav-tabs d-none" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button :class="{'nav-link': true, 'active': dj.economic_activity.length > 1}" id="economic_activity-tab" data-bs-toggle="tab" data-bs-target="#economic_activity" type="button" role="tab" aria-controls="economic_activity" aria-selected="true">Step 1</button>
          </li>
          <li class="nav-item" role="presentation">
            <button :class="{'nav-link': true, 'active': dj.economic_activity.length === 1}" id="select_dua-tab" data-bs-toggle="tab" data-bs-target="#select_dua" type="button" role="tab" aria-controls="select_dua" aria-selected="false">Step 2</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="list_dua-tab" data-bs-toggle="tab" data-bs-target="#list_dua" type="button" role="tab" aria-controls="list_dua" aria-selected="false">Step 3</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="select_invoices-tab" data-bs-toggle="tab" data-bs-target="#select_invoices" type="button" role="tab" aria-controls="select_invoices" aria-selected="false">Step 4</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="close_invoices-tab" data-bs-toggle="tab" data-bs-target="#close_invoices" type="button" role="tab" aria-controls="close_invoices" aria-selected="false">Step 5</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="process_complete-tab" data-bs-toggle="tab" data-bs-target="#process_complete" type="button" role="tab" aria-controls="process_complete" aria-selected="false">Step 6</button>
          </li>
        </ul>
        <div class="tab-content center_items" id="myTabContent">

          <!-- NAV BUTTONS AFTER FINISHING WIZARD-->
          <div class="nav_buttons_top d-none" id="nav_back_charger">
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"  
                      href="/sellers/{{seller.shortname}}/invoices/upload" 
                      role="button">CARGAR FACTURAS</a>
              </span>
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"
                      href="/sellers/{{seller.shortname}}/invoices/upload?type=ticket"  
                      role="button">CARGAR TICKETS/RECIBOS/CUOTAS AUTÓNOMO</a>
              </span>
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"  
                      href="/sellers/{{seller.shortname}}/invoices/upload?type=amz-txt-eur"  
                      role="button">CARGAR INFORME VENTAS AMAZON (TXT)</a>
              </span>
              <span class="col ">
                    <a
                      id="new-import-button"
                      class="btn button_dark_blue"
                      style= "width: auto;"  
                      href="/sellers/{{seller.shortname}}/invoices/upload?type=payroll"
                      role="button">NÓMINAS</a>
              </span>
          </div>
          <!-- NAV BUTTONS AFTER FINISHING WIZARD-->

          <!--NAV TABS CUSTOM GUIDE-->
          <div class="div_custom_nav_tabs">
            <ul class="nav nav-tabs nav_tabs_show_steps" id="myTab2" role="tablist">
                <li class="nav-item" v-for="(step, index) in steps" :key="index" role="presentation">
                  <button :class="{ active: activeStep === index, 'disabled-tab': true }" class="nav-link nav-tabs-custom text_font fade-in-tab" style="border-radius: 10px" data-bs-toggle="tab" data-bs-target="#" type="button" role="tab" aria-controls="" aria-selected="false">[[step]]</button>
                </li>
              </ul>
          </div>
          <!--NAV TABS CUSTOM GUIDE-->

          <!-- SELECT ECONOMIC ACTIVITY -->
          <div :class="{'tab-pane fade': true, 'show active': dj.economic_activity.length > 1}" id="economic_activity" role="tabpanel" aria-labelledby="economic_activity-tab">
            <div class="center_items select_iae_div">
              <div class="row" v-if="dj.economic_activity.length >= 1">
                <div class="col-12 mt-3 text-center">
                  <div class="text_size24 text_font marginbottom30px" style="color: var(--color-black-blue);">Debe Seleccionar el tipo de actividad economica</div>
                </div>
                <div class="center_items">
                  <select 
                          class="form-select form-control select_iae text_size14" 
                          name="iae" 
                          id="inputEconomicActivity" 
                          v-model="inputEconomicActivity">
                    <option :value="null" 
                            selected>
                            Seleccionar
                    </option>
                    <option v-for="iae in dj.economic_activity" 
                            :key="iae.pk" 
                            :value="iae.pk"
                            :selected="dj.economic_activity.length === 1">
                            [[ iae.description ]]
                    </option>
                  </select>
                </div>
              </div>
              <div class="row" v-else>
                <div class="col-12 mt-3 text-center alert alert-warning rounded">
                  <br>
                  <h3><b>No hay actividades económicas disponibles.</b></h3>
                  <br>
                  <h4>No puede subir documentos porque no tienen ningun IAE asociado.</h4>
                  <h4>Contacte con soporte para solucionar el problema.</h4>
                </div>
              </div>
            </div>
            <div class="buttons_nav_div">
                <span class="col nav_button_end">
                  <a
                    class="btn button_dark_blue" 
                    @click="changeTab('select_dua-tab'); activeStep = ++activeStep;" 
                    href="#" 
                    role="button" 
                    :disabled="inputEconomicActivity === null"
                    :class="{ 'disabled': inputEconomicActivity === null }"
                    >SIGUIENTE</a>
                </span>
              </div>
          </div>
          <!-- SELECT ECONOMIC ACTIVITY -->

          <!-- SELECT OR UPLOAD DUA -->
          <div :class="{'tab-pane fade': true, 'show active': dj.economic_activity.length === 1}" id="select_dua" role="tabpanel" aria-labelledby="select_dua-tab">
            <div class="center_items">
              <form   
                    method="post" 
                    enctype="multipart/form-data"
                    action="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}"
                    id="myDropzoneImportDua" 
                    class="dropzone select_iae_div">
                    {% csrf_token %}
                    <div class="dz-message mu-black" style="border: none;">
                      <div v-if ="queue_complete_dua == false && (dj.seller.legal_entity != 'self-employed' || dj.seller.legal_entity != 'sl')">
                        <div id="spinner" class="spinner-border spinner70px" role="status">
                          <span class="sr-only">Loading...</span>
                        </div>
                        <p class="text_font" style="color:black"> Cargando facturas, por favor espere</p>
                      </div>
                      <div v-else>
                        <i class="mu mu-up mu-black icon_70px" style="font-weight:bold"></i>
                        <p class= "text_size24 text_font mu-black">Subir DUA</p>
                        <p class="text_font" style="color:black">Pincha o arrastra el archivo para subirlo</p>
                        <span v-if= "(dj.seller.legal_entity == 'self-employed' || dj.seller.legal_entity == 'sl')">
                          <p class="text_font" style="color:black">Sólo puedes subir un único DUA**</p>
                        </span>
                      </div>
                    </div>
                    <div class="d-none">
                      <input
                            type="text"
                            id="id"
                            name="seller"
                            value="{{ seller.pk }}"
                        />
                      <input
                            type="text"
                            id="invoice_category"
                            name="invoice_category"
                            v-model="inputCategoriaExpenses"
                        />
                        <input
                              type="text"
                              id="invoice_type"
                              name="invoice_type"
                              v-model="inputTypeDua"
                        />
                        <input
                              type="text"
                              id="economic_activity"
                              name="iae"
                              v-model="inputEconomicActivity"
                        />
                      <div class="fallback ">
                          <input
                                type="file"
                                id="id_file"
                                name="form_create.file.name"
                          />
                        </div>
                    </div>
              </form>
            </div>
            <div v-if="dj.seller.legal_entity == 'self-employed' || dj.seller.legal_entity== 'sl'"class="buttons_nav_div">
              <span class="col">
              <a  v-if="dj.economic_activity.length > 1"
                  class="btn button_transparent" 
                  @click="changeTab('economic_activity-tab'); activeStep = --activeStep;" 
                  href="#" 
                  role="button"
              ><i class="mu mu-l-chevron mu-black"></i>ATRÁS</a>
              </span>
              <span class="col nav_button_center">
              <a  class="btn button_dark_blue" 
                  @click="have_dua = false; changeTab('select_invoices-tab'); activeStep = ++activeStep; steps = changeIndexCustomTab('case1', false);"
                  href="#" 
                  role="button"> 
                  NO TENGO DUA
              </a>
              </span>
              <span class="col nav_button_end">
              <a  class="btn button_dark_blue"
                  style="width: 240px !important;"
                  @click="have_dua = true; changeTab('list_dua-tab'); activeStep = ++activeStep; steps = changeIndexCustomTab('case2', false);" 
                  href="#"
                  :class="{ 'disabled': IDDuaInvoices.length < 1 }"
                  role="button">
                  SELECCIONAR DUA CARGADA
              </a>
              </span>
            </div>
            <div class="buttons_nav_div" v-else>
              <span v-if = "dj.economic_activity.length > 1"  class="col">
              <a  class="btn button_transparent"
                  id = "back_upNoDUALLC"
                  :class="{disabled: queue_complete_dua==false}"
                  @click="changeTab('economic_activity-tab'); activeStep = --activeStep;" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>ATRÁS
              </a>
              </span>
            </div>
          </div>
          <!-- SELECT OR UPLOAD DUA -->

          <!-- LIST OF DUA FILE -->
          <div class="tab-pane fade" id="list_dua" role="tabpanel" aria-labelledby="list_dua-tab">
            <div id="div_select_list_dua" class= "select_iae_div">
              <div class="text_size24 text_font div_select_invoices center_items" style="color: var(--color-black-blue);">
                Selecciona un DUA para asociar
              </div>
              <div class= "center_items gap30px paddingbottom40px">
                <span v-for="(item, index) in IDDuaInvoices" 
                      :key="index" 
                      :id="'IDDuaInvoices' + index" 
                      class="name_invoice_div">
                      [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                  <div class= "col icons_container">
                    <input  type="checkbox" 
                            class="custom-checkbox"
                            @click="handleDUAcheckbox(index)"
                            :id="'selectDUA' + index"
                            :value="item"
                            v-model= "SelectdDUAINvoices">
                    <a  href="#" 
                        @click="showInvoice(item)" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Ver factura">
                        <i class="mu mu-xl mu-eye mu-black"></i>
                    </a>
                    <a  :href="'/media/' + item.file" 
                        download data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Descargar factura">
                        <i class="mu mu-xl mu-down mu-black"></i>
                    </a>
                  </div>
                </span>
              </div>
            </div>
            <div class="buttons_nav_div">
              <span class="col">
              <a  class="btn button_transparent" 
                  @click="changeTab('select_dua-tab'); clearSelectdDUAINvoices(); activeStep = --activeStep; steps = changeIndexCustomTab('case0', true); " 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  ATRÁS
              </a>
              </span>
              <span class="col nav_button_end">
                <a  class="btn button_dark_blue" 
                    @click="changeTab('select_invoices-tab'); activeStep = ++activeStep;" 
                    href="#" 
                    role="button" 
                    :disabled="SelectdDUAINvoices.length < 1"
                    :class="{ 'disabled': SelectdDUAINvoices.length < 1 }"
                    >SIGUIENTE
                </a>
              </span>
            </div>
          </div>
          <!-- LIST OF DUA FILE -->

          <!-- SELECT INVOICES -->
          <div class="tab-pane fade" id="select_invoices" role="tabpanel" aria-labelledby="select_invoices-tab">
            <div id = "div_list_import" class="list_invoices_div"> 
              <!--FACTURAS CUANDO TENEMOS DUA-->
              <div v-show="IDImportInvoices.length >= 1 && have_dua == true" >
                <div class= " div_select_invoices text_size16 text_font color_black-blue center_items fade-in">
                  Para finalizar el proceso de carga, tienes que asociar mínimo una factura al DUA.
                </div>
                <div class= "center_items gap30px">
                  <span v-for="(item, index) in IDImportInvoices" 
                        :key="index" 
                        :id="'IDImportInvoices' + index" 
                        class="name_invoice_div fade-in">
                        [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                    <div class="col icons_container">
                      <input
                            class="custom-checkbox"
                            type="checkbox" 
                            :id="'selectImport' + index"
                            :value="item"
                            v-model= "SelectdImportInvoices">
                      <a  href="#" 
                          @click="showInvoice(item)" 
                          data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Ver factura">
                          <i class="mu mu-xl mu-eye mu-black"></i>
                      </a>
                      <a  :href="'/media/' + item.file" 
                          download data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Descargar factura">
                          <i class="mu mu-xl mu-down mu-black"></i>
                      </a>
                    </div>
                  </span>
                </div>
              </div>
              <!--FACTURAS CUANDO TENEMOS DUA-->

              <!--FACTURAS CUANDO NO TENEMOS DUA-->
              <div v-show="listImportInvoices.length >= 1 && have_dua == false" >
                <div class= "text_size16 text_font div_select_invoices color_black-blue center_items fade-in">
                  Facturas cargadas
                </div>
                <div class= "center_items">
                  <span  v-for="(item, index) in listImportInvoices" 
                        :key="index" 
                        :id="'listImportInvoices' + index" 
                        class="name_invoice_div fade-in">
                        [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                    <div class="col icons_container">
                      <a  href="#" 
                          data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Factura procesada">
                          <i class="mu mu-xl mu-check mu-green"></i>
                      </a>
                      <a  href="#" 
                          @click="showInvoice(item)" 
                          data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Ver factura">
                          <i class="mu mu-xl mu-eye mu-black"></i>
                      </a>
                      <a  :href="'/media/' + item.file" 
                          download data-bs-toggle="tooltip" 
                          data-bs-placement="top" 
                          title="Descargar factura">
                          <i class="mu mu-xl mu-down mu-black"></i>
                      </a>
                    </div>
                    <span v-if="index === listImportInvoices.length - 1" ref="lastItem"></span>
                  </span>
                  
                </div>
              </div>
              <!--FACTURAS CUANDO NO TENEMOS DUA-->

              <!--DROPZONE PARA SUBIR FACTURAS-->
              <div id = "containerDropzoneImp" class= " form_upload_invoices">

                <div v-if="listImportInvoices.length >= 1 || (have_dua== true && IDImportInvoices.length >= 1 )" class= "marginbottom30px"> 
                  <!--div de espacio entre el dropzone y el listado de facturas cuando se suben importaciones o hay importaciones huerfanas-->
                </div>
                <form   method="post" enctype="multipart/form-data"
                        class="dropzone"
                        action="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}"
                        id="myDropzoneImportInvoice" >
                        {% csrf_token %}
                        <div id="import_dz-message" class="dz-message ">
                          <div v-if="queue_complete_imp == false">
                            <div style="margin-bottom:16px;">
                              <div v-if="listImportInvoices.length >= 1 || (have_dua== true && IDImportInvoices.length >= 1 )" >
                                <div id="spinner" class="spinner-border" role="status">
                                  <span class="sr-only">Loading...</span>
                                </div>
                              </div>
                              <div v-else>
                                <div id="spinner" class="spinner-border spinner70px" role="status">
                                  <span class="sr-only">Loading...</span>
                                </div>
                              </div>
                            </div>
                            <p class="text_font" style="color:black"> Cargando facturas, por favor espere</p>
                          </div>
                          <div v-else>
                            <span v-if="listImportInvoices.length >= 1 || (have_dua== true && IDImportInvoices.length >= 1 )" >
                              <i class="mu mu-up mu-black" style="font-size:35px;"></i>
                              <p class= "text_font mu-black" style="font-size:13px; font-weight:bold;">Subir nueva factura</p>
                              <p class="text_font" style="color:black;" >Pincha o arrastra el archivo para subirlo</p>
                            </span>
                            <span v-else >
                              <i class="mu mu-up mu-black icon_70px" style="font-weight:bold"></i>
                              <p class= "text_size24 text_font mu-black">Subir Facturas</p>
                              <p class="text_font" style="color:black" >Pincha o arrastra el archivo para subirlo</p>
                            </span>
                          </div>
                        </div>
                        <div class="d-none">
                          <input
                              type="text"
                              id="id"
                              name="seller"
                              value="{{ seller.pk }}"
                            />
                          <input
                                type="text"
                                id="invoice_category"
                                name="invoice_category"
                                v-model="inputCategoriaExpenses"
                            />
                            <input
                                type="text"
                                id="invoice_type"
                                name="invoice_type"
                                v-model="inputTypeImport"
                            />
                            <input
                                type="text"
                                id="economic_activity"
                                name="iae"
                                v-model="inputEconomicActivity"
                            />
                          <div class="fallback ">
                              <input
                                type="file"
                                id="id_file"
                                name="form_create.file.name"
                              />
                            </div>
                        </div>
                </form>
              </div>
              <!--DROPZONE PARA SUBIR FACTURAS-->

              <div class= "paddingbottom40px">
                <span v-show="(have_dua == true && listImportInvoices.length >= 1) || (have_dua == true && IDImportInvoices.length >= 1)" class="col">
                  <a class="btn button_dark_blue" 
                    @click="changeTab('close_invoices-tab'); activeStep = ++activeStep;" 
                    href="#" 
                    role="button" 
                    :disabled="SelectdImportInvoices.length < 1"
                    :class="{ 'disabled': SelectdImportInvoices.length < 1 }"
                    >ASOCIAR</a>
                </span>
                <span v-show="listImportInvoices.length >= 1 && have_dua == false">
                  <a class="btn button_dark_blue" 
                    @click="changeTab('process_complete-tab'); activeStep = ++activeStep;"
                    href="#" 
                    role="button"
                    :class="{disabled: queue_complete_imp==false}"
                    >FINALIZAR</a>
                </span>
              </div>

            </div> 
            <div>
              <div v-if="((have_dua == true && listImportInvoices.length >= 1) || (have_dua == true && IDImportInvoices.length >= 1)) ||
                (listImportInvoices.length >= 1 && have_dua == false)" style="margin-top: 40px;">
              </div>
              <span class="col">
                <a v-show="(listImportInvoices.length < 1 && have_dua == false)"
                  id="back_upNoDUA"
                  class="btn button_transparent" 
                  @click="changeTab('select_dua-tab'); activeStep = --activeStep; steps = changeIndexCustomTab('case0', true);" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  ATRÁS</a>
              </span>
              <span class="col" >
                <a v-show="(SelectdDUAINvoices.length == 1 && have_dua == true)"
                  id="back_upyesDUA"
                  class="btn button_transparent"
                  @click="changeTab('list_dua-tab'); clearSelectdDUAINvoices(); activeStep = --activeStep;" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  ATRÁS</a>
              </span>
              <div class="">
                <a v-if="(listImportInvoices.length >= 1 && have_dua == false) || dua_dropzone == true"
                  class="btn nav_button_new"
                  :class="{disabled: queue_complete_imp==false}"
                  @click="newIportation()" 
                  href="#" 
                  role="button">
                  <i class="mu mu-l-chevron mu-black"></i>
                  VOLVER A EMPEZAR</a>
              </div>
            </div>
          </div>
          <!-- SELECT INVOICES -->

          <!-- CLOSE INVOICES -->
          <div class="tab-pane fade" id="close_invoices" role="tabpanel" aria-labelledby="close_invoices-tab">
            <div id= "div_list_invoices_close" class="select_iae_div">
              <div class= "text_size16 text_font color_black-blue center_items div_select_invoices">
                ¿Quieres cerrar las facturas?
              </div>
              <div class= "center_items gap30px marginbottom30px">
                <span v-for="(item, index) in SelectdImportInvoices" 
                      :key="index" 
                      :id="'SelectdImportInvoices' + index" 
                      class="name_invoice_div text_font">
                      [[ 'Factura ' + item.pk + ' - ' + getFileName(item.file)]]
                  <div class=" col icons_container">
                    <label class="switch">
                      <input 
                        class="toggle" 
                        type="checkbox" 
                        :id="'switchImports' + index"
                        :checked="item.is_dua_completed"
                        @change="toggleIsDuaCompleted(index, $event.target.checked)"
                      >
                      <span class="slider"></span>
                      <span class="yes-text text_font">Sí</span>
                      <span class="no-text text_font">No</span>
                    </label>
                    <a  href="#" 
                        @click="showInvoice(item)" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Ver factura">
                        <i class="mu mu-xl mu-eye mu-black"></i>
                    </a>
                    <a  :href="'/media/' + item.file" 
                        download data-bs-toggle="tooltip" 
                        data-bs-placement="top" 
                        title="Descargar factura">
                        <i class="mu mu-xl mu-down mu-black"></i>
                    </a>
                  </div>
                  <span v-if="index === listImportInvoices.length - 1" ref="lastItem"></span>
                </span>
              </div>
              <div class="paddingbottom40px">
                <span class="col center_items">
                  <a  v-show="SelectdDUAINvoices.length > 0" 
                      class="btn button_dark_blue"  
                      @click="sendBack()" 
                      id="submit-dua-selected"
                      href="#"
                      role="button">
                      FINALIZAR
                  </a>
                  <a  v-show="SelectdDUAINvoices.length < 1"  
                      class="btn button_dark_blue"  
                      id="submit-button"
                      href="#"
                      role="button">
                      FINALIZAR
                  </a>
                </span>
              </div>
            </div>
            <div class="margintop40px">
                <a  class="btn nav_button_new"
                    :class="{disabled: queue_complete_imp==false}"
                    @click="newIportation()" 
                    href="#" 
                    role="button">
                    <i class="mu mu-l-chevron mu-black"></i>
                    VOLVER A EMPEZAR
                </a>
              </div>
          </div>
          <!-- CLOSE INVOICES -->

          <!-- PROCESS COMPLETE -->
          <div class="tab-pane fade" id="process_complete" role="tabpanel" aria-labelledby="process_complete-tab">
            <div class= "center_items select_iae_div">
              <div class="col ">
                <i class="mu mu-circle-check mu-green icon_70px"></i>
              </div>
              <div class= "text_size16 text_font color_black-blue">
                Proceso completado
              </div>
            </div>

            <div class="buttons_nav_div">
                <span class="col nav_button_center">
                  <a  id="new-import-button"
                      class="btn nav_button_new" 
                      @click="newIportation()" 
                      href="#" 
                      role="button">
                      CARGAR NUEVA IMPORTACIÓN
                  </a>
                </span>
            </div>
          <!-- PROCESS COMPLETE -->
      </div>      
    {% endif %}
  </div>

  
  <!--MODAL WARNING ORPHAN DUA-->
  {% include 'invoices/include/modal_orphanDUA.html' %}
  <!--MODAL WARNING ORPHAN DUA-->

  <!--MODAL WARNING ERROR FILE-->
  {% include 'invoices/include/error_fileDUA.html' %}
  <!--MODAL WARNING ERROR FILE-->

  <!--MODAL SHOW INVOICE-->
  {% include 'invoices/include/show_invoice_DUA_wizard.html' %}
  <!--MODAL SHOW INVOICE-->

  <!--MODAL FINISH LOADING-->
  {% include 'invoices/include/finishModalwizardDUA.html' %}
  <!--MODAL FINISH LOADING-->

{% endblock content %}

{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <!-- DROPZONE JS  -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dropzone/dropzone.min-v5.js"></script>
  <!-- sweet alert Js -->
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>


  <script>

    // Initialize sweet alert
    const limitInvToast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      // stop timer when hover
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    // Variables del backend
    let total = {{ total_invoices }};
    const limit = {{ limit_invoices }};
    const user_role = "{{user.role}}"

    // Variables para contador de facturas
    let numFilesToUpload = 0;
    let duaFile = 0;
    let contDuas = 0;
    let extraFiles = 0;

    const countInvoices = (total, limit)=>{
      if (limit == -1){
        return 'Sin límite de facturas';
      }else{
        return `Límite de facturas ${total}/${limit}`;
      }
    }

    const showLimitInvToast = (total, limit)=>{
      limitInvToast.fire({
        icon: 'info',
        title: countInvoices(total, limit)
      });
    }


    Dropzone.options.myDropzoneImportInvoice = {
      init: function () {
        
        let errorFile = false;
        
        this.on("queuecomplete", function () {
          queue_complete_imp.value = true;

          //Habilitar botones de atrás cuando se termina de procesar facturas
          $('#back_upNoDUA').removeClass('disabled');
          $('#back_upyesDUA').removeClass('disabled');

        });

        this.on("addedfile", function (file) {
          numFilesToUpload++
          total = numFilesToUpload + total;
          
          if (user_role != "manager" && limit != -1 && total > limit){
            $('#description_error').text('Límite de facturas alcanzado.')
            $('#errorFileDUAModal').modal('show');
            this.removeFile(file);
          } else {
            if (file.size > 10485760 || !this.options.acceptedFiles.includes(file.type) ) {
              $('#name_DUAfile').text(file.name + " - Error");
              $('#description_error').text('No se ha subido correctamente el documento, los formatos admitidos son: PDF/JPEG/JPG/PNG.')
              $('#errorFileDUAModal').modal('show');
              this.removeFile(file);
            } else {
              queue_complete_imp.value = false;
              //Deshabilitar botones de atrás mientras se procesan facturas
              $('#back_upNoDUA').addClass('disabled');
              $('#back_upyesDUA').addClass('disabled');
            }
          }
          if (numFilesToUpload + total > limit && limit != -1){
            extraFiles = numFilesToUpload + total - limit;
            let totaltoShow = total + numFilesToUpload - extraFiles;
            if (user_role != "manager"){
              showLimitInvToast(totaltoShow, limit);
            }
          } else {
            if (user_role != "manager"){
              showLimitInvToast(total, limit);
            }
          }

          numFilesToUpload = 0;
        });

        this.on("removedfile", function (file) {
          // Decrementar el contador de archivos cuando se elimina un archivo
          if (numFilesToUpload > 0) {
              numFilesToUpload--;
          } 
          if (total > 0) {
              total--;
          }
        });

        this.on("complete", function (file) {
          
        });

        this.on("success", function (file, response) {

          invoice_created = JSON.parse(JSON.stringify(response ))
          //Añadir a todas las facturas de importación que tenemos
          addImportInvoice(invoice_created);

          // Añadir solo la factura de importación que acabamos de crear
          listImportInvoices.value.push(invoice_created);
          
          //Cambiar el estilo del cargador cuando se sube una factura
          if (have_dua.value == true && IDImportInvoices.value.length >= 1){
            $('#div_list_import').addClass('list_invoices_div');
            $('#myDropzoneImportInvoice').removeClass('select_iae_div').addClass('form_upload_invoices fade-in');
            $('#import_dz-message').css('border', '1px solid #91ABBC');
          } else if (have_dua.value == false  && listImportInvoices.value.length >= 1){
            $('#div_list_import').addClass('list_invoices_div');
            $('#myDropzoneImportInvoice').removeClass('select_iae_div').addClass('form_upload_invoices fade-in');
            $('#import_dz-message').css('border', '1px solid #91ABBC');
          } 

        });

        this.on("error", function (file, errorMessage, response) {
          if (response && response.status != 200){
            this.removeFile(file);
            total = total - 1;
            numFilesToUpload = numFilesToUpload - 1;
            $('#description_error').text('Error al cargar el archivo. Por favor, inténtelo de nuevo.')
            $('#errorFileDUAModal').modal('show');
            if (user_role != "manager"){
              showLimitInvToast(total, limit);
            }
          }

        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Haga Click <br>o</br> Arrastre los archivos",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
      previewsContainer: false,
    };

    Dropzone.options.myDropzoneImportDua = {
      init: function () {

        let errorFile = false;

        this.on("queuecomplete", function () {
          queue_complete_dua.value = true;
          activeStep.value = ++ activeStep.value;
          if (errorFile == false){
            changeTab('process_complete-tab');
            $('#LoadingModal').modal('hide');
          }

          //Habilitar botones de atrás cuando se termina de procesar facturas
          $('#back_upNoDUALLC').removeClass('disabled');

        });

        // Evento cuando se añade un archivo
        this.on("addedfile", function (file) {
          duaFile++
          contDuas++
          total = duaFile + total;

          if (user_role != "manager" && limit != -1 && total > limit){
            $('#description_error').text('Límite de facturas alcanzado.')
            $('#errorFileDUAModal').modal('show');
            this.removeFile(file);
          } else {
            if (file.size > 10485760 || !this.options.acceptedFiles.includes(file.type) ) {
              $('#name_DUAfile').text(file.name + " - Error ");
              $('#description_error').text("El archivo es muy grande o no se ajusta al formato, los formatos admitidos son: PDF/JPEG/JPG/PNG.")
              $('#errorFileDUAModal').modal('show');
              this.removeFile(file);
              errorFile = true;
            } else {
              if (dj.value.seller.legal_entity == 'self-employed' || dj.value.seller.legal_entity =='sl'){
                have_dua.value = true;
                dua_dropzone.value = true;
                changeTab('select_invoices-tab');
                steps.value = changeIndexCustomTab('case5', false);
                activeStep.value = ++activeStep.value;
              } else {
                let myDropzone_llc_other = this;
                $('#LoadingModal').modal('show');
                setTimeout(() => {
                  myDropzone_llc_other.processQueue();
                  $('#LoadingModal').modal('hide');
                }, "1000");
              }
              errorFile = false;
            }
          }

          if (dj.value.seller.legal_entity == 'self-employed' || dj.value.seller.legal_entity =='sl'){
            if (contDuas > 1){
              $('#description_error').text('Has añadido más de un DUA, sólo se tendrá en cuenta el primer archivo.')
              $('#errorFileDUAModal').modal('show');
              this.removeFile(file);
            }
          } else {
            queue_complete_dua.value = false;
          }

          if(duaFile + total > limit && limit != -1){
            extraFiles = duaFile + total - limit;
            let totaltoShow = total + duaFile - extraFiles;
            if (user_role != "manager"){
              showLimitInvToast(totaltoShow, limit);
            }
          } else {
            if (user_role != "manager"){
              showLimitInvToast(total, limit);
            }
          }
          duaFile = 0;

          
        });

        this.on("removedfile", function (file) {
          // Decrementar el contador de archivos cuando se elimina un archivo
          if (duaFile > 0) {
              duaFile--;
          } 
          
          if (total > 0) {
              total--;
          }

          if (contDuas > 0){
              contDuas--;
          }
          
        });

        let myDropzone = this;
        const submitButton = document.getElementById('submit-button');
        submitButton.addEventListener("click", function () {
          $('#LoadingModal').modal('show');
          setTimeout(() => {
            myDropzone.processQueue();
            $('#LoadingModal').modal('hide');
          }, "1000");
        });

        // Añadir datos adicionales a cada petición
        this.on("sending", function (file, xhr, formData) {
          formData.append("additional_data", JSON.stringify(toRaw(SelectdImportInvoices.value)));
        });

        this.on("success", function (file, response) {
          addDUAInvoice(response);
          invoice_created = JSON.parse(JSON.stringify(response))
          listDUAInvoices.value.push(invoice_created);

        });

        this.on("complete", function (file, response) {
          
        });

        this.on("error", function (file, errorMessage, response) {
          errorFile = true;
          if (response && response.status != 200){
            $('#LoadingModal').modal('hide');
            $('#description_error').text('Error al vincular las facturas al DUA. Por favor, inténtelo de nuevo.')
            $('#errorFileDUAModal').modal('show');
          }
        });

      },
      
      {% if mail_signature.team == 'AMZVAT' %}
        autoProcessQueue: true,        
      {% else %}
        autoProcessQueue: false,
        maxFiles: 1,
      {% endif %}
      parallelUploads: 4,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
      previewsContainer: false,
    };

  

  </script>
  <!-- DROPZONE JS  -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>

  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch, toRaw} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    let have_dua = ref(null);
    let queue_complete_imp = ref(null);
    let queue_complete_dua = ref(null);
    let dua_dropzone = ref(null);
    let steps = ref([]);
    let activeStep = ref(0);
    const inputCategoriaExpenses = ref('expenses');
    const inputEconomicActivity = ref(null);
    const inputTypeImport = ref('import-invoice');
    const inputTypeDua = ref('import-dua');
    const IDDuaInvoices = ref([]);
    const IDImportInvoices = ref([]);
    const SelectdImportInvoices = ref([]);
    const SelectdDUAINvoices = ref([]);
    const listImportInvoices = ref([]);
    const listDUAInvoices = ref([]);

    const dj = ref({});


    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
       console.log(dj.value); 

      //ADD ALL IMPORT INVOICES FROM CONTEXT TO VARIABLE IDImportInvoices
      addImportInvoiceFiltered(dj);
      //ADD ALL DUA INVOICES FROM CONTEXT TO VARIABLE IDDuaInvoices
      addDUAInvoiceFiltered(dj);
    };

    const changeTab = (tabId) => {
        const tab = new bootstrap.Tab(document.getElementById(tabId));
        tab.show();
    }

    const clearSelectdDUAINvoices = () => {
      SelectdDUAINvoices.value = [];
    };

    const handleDUAcheckbox = (index) => {
      clearSelectdDUAINvoices();
      SelectdDUAINvoices.value.push(IDDuaInvoices.value[index]);
    }

    const addImportInvoice = (invoice) => {
      IDImportInvoices.value.push(invoice);
    }

    const addDUAInvoice = (invoice) => {
      IDDuaInvoices.value.push(invoice);
    }

    const addImportInvoiceFiltered = (dj) => {
      dj.value.import_invoices.forEach(invoice => {

          const filteredInvoice = {
            pk: invoice.pk,
            file: invoice.file,
            related_invoice_many: JSON.stringify([...invoice.related_invoice_many]),
            is_dua_completed: invoice.is_dua_completed
          };
      addImportInvoice(filteredInvoice);
      });
    }

    const addDUAInvoiceFiltered = (dj) => {
      dj.value.orphan_dua.forEach(invoice => {

          const filteredInvoice = {
            pk: invoice.pk,
            file: invoice.file,
            related_invoice_many: JSON.stringify([...invoice.related_invoice_many]),
            is_dua_completed: invoice.is_dua_completed
          };
      addDUAInvoice(filteredInvoice);
      });
    }

    const getFileName = (file) => {
      const startIndex = file.lastIndexOf('/') + 1;
      file.substring(startIndex);
      if(file.length > 60){
        const cutstring = file.substr(1, 60);
        return cutstring + '...';
      }else{
        return file;
      }
    }

    const sendBack = async () => {
      let url = "{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}";
      const csrfToken = '{{ csrf_token }}';
      const postData = {
        selectedDUA: JSON.stringify(toRaw(SelectdDUAINvoices.value)),
        selectedImport: JSON.stringify(toRaw(SelectdImportInvoices.value)),
        seller_id: '{{ seller.pk}}',
      };

      try {
        $('#LoadingModal').modal('show');

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken
          },
          body: (JSON.stringify(postData))
        });

        setTimeout(() => {
          if (response.status == 200) {
            changeTab('process_complete-tab');
            activeStep.value = ++activeStep.value;
            $('#nav_back_charger').removeClass('d-none');
          } else {
            $('#description_error').text('Error al vincular las facturas al DUA. Por favor, inténtelo de nuevo.')
            $('#errorFileDUAModal').modal('show');
          }
          
          $('#LoadingModal').modal('hide');

        }, "1000");

      } catch (error) {
        console.error('Error in sendBack: ', error);
      }
    }

    const toggleIsDuaCompleted = (index, checked) => {
      SelectdImportInvoices.value[index].is_dua_completed = checked;
    }

    const toggleHaveDua = (bool) => {
      have_dua.value = bool;
    }

    //RELOAD DE LA PÁGINA AL FINALIZAR EL WIZARD
    const newIportation = () =>{
      location.reload();
    }

    const showInvoice = (item) => {
      $('#showInvoiceDUA').modal('show');
      $('#showInvoiceDUA').find('.modal-body').html('<iframe src="/media/' + item.file + '" width="100%" height="500px" />');
    }

    const changeIndexCustomTab = (cases, ini ) =>{

      if (cases == 'case0'){
        caseArray = new Array((dj.value.economic_activity.length === 1) ? 1 : 2);
      } else if (cases == 'case1'){
        caseArray = new Array((dj.value.economic_activity.length === 1) ? 3 : 4);
      } else if (cases == 'case2'){
        caseArray = new Array((dj.value.economic_activity.length === 1) ? 5 : 6);
      } else if (cases == 'case3'){
        caseArray = new Array((dj.value.economic_activity.length === 1) ? 2 : 3);
      } else if (cases == 'case5'){
        caseArray = new Array((dj.value.economic_activity.length === 1) ? 4 : 5);
      }

      for (let i = 0; i < caseArray.length; i++) {
        if (i === caseArray.length - 1 && ini == false) {
            caseArray[i] = 'Fin';
        } else {
            caseArray[i] = 'Paso ' + parseInt(i + 1);
        }
            }
      return caseArray;
    }

    
    $(document).ready(function() {
        //Mostrar tu límite actual de facturas
        if (user_role != "manager"){
          showLimitInvToast(total, limit);
        }

        //EVENTOS DE LAS TABS

        //Mostras DUAS Huerfanos cuando sólo se tiene 1 actividad económica 
        if(dj.value.economic_activity.length === 1){
          if (dj.value.orphan_dua.length > 0 && (dj.value.seller.legal_entity == 'self-employed' || dj.value.seller.legal_entity =='sl')){
            $('#orphanDUAModal').modal('show');
          }
        }

        //Eventos del step 2 (DUAS HUERFANOS)
        $('#select_dua-tab').on('shown.bs.tab', function (e) {
          if (dj.value.orphan_dua.length > 0 && (dj.value.seller.legal_entity == 'self-employed' || dj.value.seller.legal_entity =='sl')){
            $('#orphanDUAModal').modal('show');
          }
          if (user_role != "manager"){
            showLimitInvToast(total, limit);
          }
        });

        //Ocultar modal de warning de DUA huerfano al pulsar un boton
        $('#dua_warning_button').on("click", function () {
          $('#orphanDUAModal').modal('hide');
        });

        //Eventos del  step 3 (cambio CSS en función de las facturas  DUA huerfanas que tengamos)
        $('#list_dua-tab').on('shown.bs.tab', function (e) {
          if (IDDuaInvoices.value.length > 2){
            $('#div_select_list_dua').removeClass('select_iae_div').addClass('list_invoices_div');
          }          
        });

        //Eventos del step 4 (cambio CSS en función de las facturas que tengamos)
        $('#select_invoices-tab').on('shown.bs.tab', function (e) {
          if (have_dua.value == false && listImportInvoices.value.length < 1){
            //Tamaño del dropzone
            $('#div_list_import').removeClass('list_invoices_div');
            $('#myDropzoneImportInvoice').removeClass('form_upload_invoices').addClass('select_iae_div');
            $('#import_dz-message').css('border', 'none');
          } else if (have_dua.value == false && listImportInvoices.value.length >= 1){
            $('#div_list_import').addClass('list_invoices_div');
            $('#myDropzoneImportInvoice').removeClass('select_iae_div').addClass('form_upload_invoices'); 
            $('#import_dz-message').css('border', '1px solid #91ABBC');
          } else if (have_dua.value == true && IDImportInvoices.value.length >= 1){
            $('#div_list_import').addClass('list_invoices_div');
            $('#myDropzoneImportInvoice').removeClass('select_iae_div').addClass('form_upload_invoices'); 
            $('#import_dz-message').css('border', '1px solid #91ABBC'); 
          } else if(have_dua.value == true && IDImportInvoices.value.length < 1){
            $('#div_list_import').removeClass('list_invoices_div');
            $('#myDropzoneImportInvoice').removeClass('form_upload_invoices').addClass('select_iae_div'); 
            $('#import_dz-message').css('border', 'none'); 
          }

        });
        
        //Eventos del step 5 (cambio CSS en función de las facturas que tengamos)
        $('#close_invoices-tab').on('shown.bs.tab', function (e) {
          if (SelectdImportInvoices.value.length > 1){
            $('#div_list_invoices_close').removeClass('select_iae_div').addClass('list_invoices_div');
          }

          //EVENTO DEL SWITCH PARA CAMBIAR DE COLOR
          let toggleSwitch = document.querySelectorAll('.toggle');
          let yesText = document.querySelectorAll('.yes-text');
          let noText = document.querySelectorAll('.no-text');
          toggleSwitch.forEach((element, e) => {
            element.addEventListener('change', function () {
              if (element.checked) {
                    yesText[e].style.opacity = '1';  
                    noText[e].style.opacity = '0';                
              } else {
                    yesText[e].style.opacity = '0';
                    noText[e].style.opacity = '1'; 
              }
            });
          });


        });

        //Eventos del step 6 (mostrar botones de navegación para ir a los otros cargadores)
        $('#process_complete-tab').on('shown.bs.tab', function (e) {
          $('#nav_back_charger').removeClass('d-none');
        });
        //--------------------------------------------------------------------------------


        //EVENTOS TAB INFORMATIVAS----------------------------------------------------

        if(dj.value.seller.legal_entity != 'self-employed' && dj.value.seller.legal_entity != 'sl'){
          steps.value = changeIndexCustomTab('case3', false);
        } else{
          steps.value = changeIndexCustomTab('case0', true);
        }

        //-----------------------------------------------------------

        //limpiar el modal de error al cerrarlo
        $('#errorFileDUAModal').on('hidden.bs.modal', function () {
            $('#description_error').text('');
            $('#name_DUAfile').text('');
        });

        //Evitar que se cierre el modal de finalizar al hacer click fuera
        $('#LoadingModal').on('click', function (e) {
            e.stopPropagation();
        });
        $("#LoadingModal").modal({
          backdrop: 'static',
          keyboard: false
        });

        
    });
    

    // WATCHERS ////////////////////////////////////////////////////////////////////////

    watch(SelectdImportInvoices, (newValue) => {
      Vue.nextTick(() => {
        $('[data-bs-toggle="tooltip"]').tooltip(); //Inicializar los tooltips
      });
    });

    // watch(SelectdDUAINvoices, (newValue) => {
    // });

    // watch(have_dua, (newValue) => {
    // });

    watch(listImportInvoices.value, (newValue) => {
      Vue.nextTick(() => {
        $('[data-bs-toggle="tooltip"]').tooltip(); //Inicializar los tooltips
      });
    });

    watch(listDUAInvoices.value, (newValue) => {
      Vue.nextTick(() => {
        $('[data-bs-toggle="tooltip"]').tooltip(); //Inicializar los tooltips
      });
    });

    // watch(activeStep, (newValue) => { 
    // })

    // watch(steps, (newValue) => {
    // })


    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,

      have_dua,
      queue_complete_imp,
      queue_complete_dua,
      dua_dropzone,
      inputCategoriaExpenses,
      inputEconomicActivity,
      inputTypeImport,
      inputTypeDua,
      IDDuaInvoices,
      IDImportInvoices,
      getFileName,
      SelectdImportInvoices,
      SelectdDUAINvoices,
      changeTab,
      handleDUAcheckbox,
      sendBack,
      toggleIsDuaCompleted,
      toggleHaveDua,
      listImportInvoices,
      listDUAInvoices,
      newIportation,
      showInvoice,
      clearSelectdDUAINvoices,
      steps,
      activeStep,
      changeIndexCustomTab,


    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";

      if (data_export.dj._rawValue.economic_activity.length === 1) {
        data_export.inputEconomicActivity = data_export.dj._rawValue.economic_activity[0].pk;
      }

      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export};
        }

      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('#toast', data_export);
    createVue3('.vue', data_export);

  </script>

  <!-- VUE3 JS  --> 
{% endblock javascripts %}
