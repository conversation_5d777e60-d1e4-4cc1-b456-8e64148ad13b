{% extends "layouts/base-fullscreen.html" %}
{% load i18n static %}
{% block title %}
Agenda una llamada telefónica
{% endblock title %}

{% block stylesheets %}
<link rel="canonical" href="https://app.muaytax.com/bookings/public-book/new/" />
<script src="{% static 'assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js' %}"></script>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<link rel="stylesheet" href="{% static 'assets/css/bookings/secondary-calendar.css' %}"/>
<link rel="stylesheet" href="{% static 'assets/css/bookings/public-booking.css' %}"/>
<link rel="stylesheet" href="{% static 'assets/cdns_locals/css/select/select2.min-v4.1.0.css' %}" />
{% endblock stylesheets %}

{% block content %}

{% include 'includes/header-public.html' %}
<div class="min-vh-100-footer w-100 d-flex flex-column p-t-50" style="padding-top: 60px;">

    <section class="m-t-20 d-xs-none d-block" id="title-section">
        <div class="container">
            <header class="d-flex flex-column justify-content-center align-items-center text-center">
                <span class="text-uppercase title-pb">Solicita una llamada con nosotros</span>
                <h5 class="mt-2 f-16">Presentamos nuestro proceso de reserva rápido para clientes y no clientes.</h5>
            </header>
        </div>
    </section>

    <div class="shadow-none tab-content bg-transparent mb-4">
        <div class="tab-pane fade active show" id="info" role="tabpanel" aria-labelledby="info-tab">
            
            <div class="max-w-xl mx-auto">
                <div class="d-flex justify-content-between align-items-sm-center flex-column-reverse flex-sm-row mb-4">
                    <h2 class="wizard-title fs-3">Introduce tu correo electrónico</h2>
                    <p class="text-muted-pb small mb-2 mb-sm-3">
                        <i class="fas fa-circle status-icon"></i>
                        paso 1 de 5
                    </p>
                </div>
                <form
                    action="{% url 'api_rest:api_auth:get_access_token_from_email' %}"
                    method="post">
                    {% csrf_token %}
                    <div class="mb-4">
                        <p class="text-dark">Te enviaremos un email con un código para validar tu identidad y puedas proceder a realizar la cita.</p>
                        <label for="email" class="form-label text-muted-pb">Correo Electrónico</label>
                        <input type="email" class="form-control input-border-pb" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <button type="submit" id="id_email_submit" class="btn btn-navy-pb w-100">
                            Siguiente &nbsp;
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="tab-pane fade" id="access-token" role="tabpanel" aria-labelledby="access-token-tab">
            <div class="max-w-xl mx-auto">
                <div class="d-flex justify-content-between align-items-sm-center flex-column-reverse flex-sm-row mb-4">
                    <h2 class="wizard-title fs-3 f-w-700">Ingresa el código</h2>
                    <p class="text-muted-pb small mb-2 mb-sm-3">
                        <i class="fas fa-circle status-icon"></i>
                        paso 2 de 5
                        <a 
                            href="."
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Volver al inicio"
                            class="ms-1 text-body d-none d-sm-inline-block">
                            <i class="mu mu-home" style="font-weight: bold;"></i>
                        </a>
                    </p>
                </div>
                
                <div class="timer animatable mb-3">
                    <svg>
                        <circle cx="50%" cy="50%" r="30"/>
                        <circle cx="50%" cy="50%" r="30" pathLength="1" />
                        <text x="40" y="45" text-anchor="middle"><tspan id="timeLeftElement"></tspan></text>
                    </svg>
                </div>
                <div id="validateTokenBlock">
                    <form
                        id="access-token-form"
                        action="{% url 'api_rest:api_auth:verify_access_token' %}"
                        method="post">
                        {% csrf_token %}
                        <div class="mb-4">
                            <label for="id_access_code" class="form-label text-muted-pb">Introduce el código recibido en tu email.</label>
                            <input type="text" class="form-control input-border-pb" id="id_access_code" name="access_code" required>
                        </div>
                        <div class="mb-3">
                            <button type="submit" id="id_access_code_submit" class="btn btn-navy-pb w-100">
                                Siguiente &nbsp;
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <div class="d-none" id="resendTokenBlock">
                    <!-- label with token expired message -->
                    <div class="alert mu-alert-white-pb mb-4" role="alert">
                        El código ha expirado. Por favor, solicita un nuevo código.
                    </div>
                    <form
                        id="resendAccessTokenForm"
                        method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <button type="submit" class="btn btn-navy-pb w-100 mb-3">
                                Reenviar código
                            </button>
                            <a href="." class="btn btn-outline-navy-pb w-100 mb-3">
                                Intentar con otra direccion de correo
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="date" role="tabpanel" aria-labelledby="date-tab"> 
            <div class="max-w-xxl mx-auto">
                <div class="d-flex justify-content-between align-items-sm-center flex-column-reverse flex-sm-row mb-4">
                    <h2 class="wizard-title fs-3 f-w-700">Elige la Fecha y Hora</h2>
                    <p class="text-muted-pb small mb-2 mb-sm-3">
                        <i class="fas fa-circle status-icon"></i>
                        paso 3 de 5
                        <a 
                            href="."
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Volver al inicio"
                            class="ms-1 text-body d-none d-sm-inline-block">
                            <i class="mu mu-home" style="font-weight: bold;"></i>
                        </a>
                    </p>
                </div>
                <form action="" id="submit-date">
                    <p class="text-dark">Recuerda que la hora de la llamada se encuentra en la zona horaria GMT +02:00 (Central European Time)</p>
                    {% csrf_token %}
                    <div class="pt-2 mb-4">
                        {% include 'bookings/include/public_calendar.html' %}
                    </div>
                    <div class="mb-3">
                        <button type="button" onclick="submitSelectDate(this)" class="btn btn-navy-pb w-100">
                            Siguiente &nbsp;
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="tab-pane fade" id="comment" role="tabpanel" aria-labelledby="comment-tab">
            <div class="max-w-xl mx-auto">
                <div class="d-flex justify-content-between align-items-sm-center flex-column-reverse flex-sm-row mb-4">
                    <h2 class="wizard-title fs-3 f-w-700">Ya casi hemos terminado!</h2>
                    <p class="text-muted-pb small mb-2 mb-sm-3">
                        <i class="fas fa-circle status-icon"></i>
                        paso 4 de 5
                        <a 
                            href="."
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Volver al inicio"
                            class="ms-1 text-body d-none d-sm-inline-block">
                            <i class="mu mu-home" style="font-weight: bold;"></i>
                        </a>
                    </p>
                </div>
                <form id="commentForm">
                    <p class="text-dark">A continuación escribe tu número de teléfono (en caso de estar incompleto) y la razón por la que solicitas la llamada:</p>
                    <div class="pt-2 mb-3">
                        <label for="id_seller_new_phone" class="form-label text-muted-pb">Teléfono</label>
                        <div class="row">

                            <div class="col-3">
                                <select id="sellerSelectCountryPhone" class=" form-control" required>
                                    <option value="" selected disabled></option>
                                </select>
                                <input type="hidden" id="id_seller_new_country_prefix" name="seller_new_country_prefix">
                            </div>
    
                            <div class="col-9">
                                <input 
                                    id="id_seller_new_phone" placeholder="Número de teléfono sin el prefijo internacional"
                                    name="seller_new_phone" class="form-control input-border-pb"
                                    onkeypress="return isNumberKey(event)" type="tel" 
                                    pattern="\d*" inputmode="numeric" maxlength="15" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="id_public_comment" class="form-label text-muted-pb">Comentarios</label>
                        <textarea class="form-control input-border-pb" id="id_public_comment" name="public_comment" rows="3" placeholder="Cuéntanos brevemente en qué podemos ayudarte durante la llamada." required></textarea>
                    </div>
                    <div class="mb-3">
                        <button
                            type="submit" class="btn btn-navy-pb w-100">
                            Siguiente &nbsp;
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="tab-pane fade" id="new-info" role="tabpanel" aria-labelledby="new-info-tab">
            <div class="max-w-xl mx-auto">
                <div class="d-flex justify-content-between align-items-sm-center flex-column-reverse flex-sm-row mb-4">
                    <h2 class="wizard-title fs-3 f-w-700">Ya casi hemos terminado!</h2>
                    <p class="text-muted-pb small mb-2 mb-sm-3">
                        <i class="fas fa-circle status-icon"></i>
                        paso 4 de 5
                        <a 
                            href="."
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Volver al inicio"
                            class="ms-1 text-body d-none d-sm-inline-block">
                            <i class="mu mu-home" style="font-weight: bold;"></i>
                        </a>
                    </p>
                </div>
                <form
                    id="newInfoForm"
                    action="">
                    <p class="text-dark">A continuación, dinos tu nombre completo, número de teléfono y la razón por la que solicitas una llamada:</p>
                    <div class="pt-2 mb-3">
                        <label for="id_public_new_name" class="form-label text-muted-pb">Nombres</label>
                        <input type="text" class="form-control input-border-pb" id="id_public_new_name" name="public_new_name" required>
                    </div>
                    <div class="pt-2 mb-3">
                        <label for="id_public_new_last_name" class="form-label text-muted-pb">Apellidos</label>
                        <input type="text" class="form-control input-border-pb" id="id_public_new_last_name" name="public_new_last_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="id_public_new_phone" class="form-label text-muted-pb">Teléfono</label>
                        <div class="row">

                            <div class="col-3">
                                <select id="publicSelectCountryPhone" class=" form-control input-border-pb"  required>
                                    <option value="" selected disabled></option>
                                </select>
                                <input type="hidden" id="id_public_new_country_prefix" name="public_new_country_prefix">
                            </div>
    
                            <div class="col-9">
                                <input 
                                    id="id_public_new_phone" placeholder="Número de teléfono sin el prefijo internacional"
                                    name="public_new_phone" class="form-control input-border-pb"
                                    onkeypress="return isNumberKey(event)" type="tel" 
                                    pattern="\d*" inputmode="numeric" maxlength="15" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="id_public_new_comment" class="form-label text-muted-pb">Comentarios</label>
                        <textarea required class="form-control input-border-pb" id="id_public_new_comment" name="public_new_comment" rows="3" placeholder="Cuéntanos brevemente en qué podemos ayudarte durante la llamada." required></textarea>
                    </div>
                    <div class="mb-3">
                        <button
                            type="submit" class="btn btn-navy-pb w-100">
                            Siguiente &nbsp;
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="tab-pane fade " id="confirm" role="tabpanel" aria-labelledby="confirm-tab">
            <div class="max-w-xl mx-auto">
                <div class="d-flex justify-content-between align-items-sm-center flex-column-reverse flex-sm-row mb-4">
                    <h2 class="wizard-title  fs-3 f-w-700">Confirma tu cita telefónica</h2>
                    <p class="text-muted-pb small mb-2 mb-sm-3">
                        <i class="fas fa-circle status-icon"></i>
                        paso 5 de 5
                        <a 
                            href="."
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Volver al inicio"
                            class="ms-1 text-body d-none d-sm-inline-block">
                            <i class="mu mu-home" style="font-weight: bold;"></i>
                        </a>
                    </p>
                </div>
                <div class="card detail-card-pb">
                    <form
                        id="confirmPublicBookingForm"
                        action="" method="POST" id="confirm-form">
                        {% csrf_token %}
                        <div class="card-body">
                            <!-- Personal Details Section -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-6" id="confirm_name_block">
                                    <div class="info-label">
                                        <i class="mu mu-user mu-s"></i>
                                        Nombre y Apellidos:
                                    </div>
                                    <div class="info-value" id="id_confirmed_name_shown">Paolo Andree Macias</div>
                                    <input type="hidden" id="id_confirmed_name" name="confirmed_new_user_name">
                                    <input type="hidden" id="id_confirmed_last_name" name="confirmed_new_user_last_name">
                                </div>
                                <div class="col-md-6 d-none" id="confirm_seller_block">
                                    <div class="info-label">
                                        <i class="mu mu-user mu-m"></i>
                                        Empresa o Razón Social:
                                    </div>
                                    <div class="info-value" id="id_confirmed_seller_name_shown">LLC</div>
                                    <input type="hidden" name="confirmed_seller" id="id_confirmed_seller">
                                </div>
                                <div class="col-md-6">
                                    <div class="info-label">
                                        <i class="mu mu-phone-call mu-s"></i>
                                        Teléfono:
                                    </div>
                                    <div class="info-value" id="id_confirmed_phone_shown" >+51 987 654 321</div>
                                    <input type="hidden" id="id_confirmed_phone" name="confirmed_new_user_phone">
                                </div>
                                <div class="col-12">
                                    <div class="info-label">
                                        <i class="mu mu-envelope mu-s"></i>
                                        Correo Electrónico:
                                    </div>
                                    <div class="info-value" id="id_confirmed_email_shown"><EMAIL></div>
                                    <input type="hidden" id="id_confirmed_email" name="confirmed_new_user_email">
                                </div>
                            </div>

                            <!-- Appointment Details Section -->
                            <div class="row g-4 mt-4 mb-3 border-top">
                                <div class="col-md-6">
                                    <div class="info-label">
                                        <i class="mu mu-calendar mu-s"></i>
                                        Fecha:
                                    </div>
                                    <div class="info-value" id="id_confirmed_date_shown">Martes 14 de Enero, 2025</div>
                                    <input type="hidden" id="id_confirmed_date" name="confirmed_public_selected_date">
                                </div>
                                <div class="col-md-6">
                                    <div class="info-label">
                                        <i class="mu mu-watch mu-s"></i>
                                        Hora:
                                    </div>
                                    <div class="info-value" id="id_confirmed_time_shown">15:00 h - (GMT +2)</div>
                                    <input type="hidden" id="id_confirmed_time" name="confirmed_public_selected_time">
                                </div>
                                <div class="col-12">
                                    <div class="info-label">
                                        <i class="mu mu-sand-watch mu-s"></i>
                                        Duración aproximada:
                                    </div>
                                    <div class="info-value" id="id_confirmed_duration_shown">15 minutos</div>
                                    <input type="hidden" id="id_confirmed_duration" value="1" name="confirmed_public_selected_duration">
                                </div>
                            </div>

                            <input type="hidden" name="confirmed_public_managers" id="id_confirmed_public_managers">
                            <input type="hidden" name="confirmed_public_comment" id="id_confirmed_comment">
                            <div class="g-recaptcha d-flex justify-content-center" data-sitekey="6LfJYu0pAAAAAKPdF8tKrwHZvVTJJwpdfx9sqN7x"></div>
                        </div>
                        <div class="card-block pt-0">
                            <button
                                type="submit"
                                id="id_confirm_button" class="btn btn-navy-pb w-100">
                                Confirmar cita telefónica
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- footer -->
<footer class="container-fluid text-center p-3 d-flex justify-content-center">
    <a href="https://www.instagram.com/muaytaxgroup/" class="me-2 " target="_blank">
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-instagram.png' %}" width="20" alt="">
    </a>
    <a href="https://www.facebook.com/MuayTaxGroup" class="me-2 " target="_blank">
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-facebook.png' %}" width="20" alt="">
    </a>
    <a href="https://www.linkedin.com/company/muaytax" class="me-2 " target="_blank">
        <!-- <i class="fab fa-linkedin"></i> -->
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-linkedin.png' %}" width="20" alt="">
    </a>
    <a href="https://www.youtube.com/@muaytaxgroup" class="me-2 " target="_blank">
        <!-- <i class="fab fa-youtube"></i> -->
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-youtube.png' %}" width="20" alt="">
    </a>
    <a href="https://twitter.com/muaytaxgroup" class="me-2 " target="_blank">
        <!-- <i class="fab fa-twitter"></i> -->
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-x.png' %}" width="20" alt="">
    </a>
    <a href="https://t.me/muaytaxgroup" class="me-2 " target="_blank">
        <!-- <i class="fab fa-telegram"></i> -->
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-telegram.png' %}" width="20" alt="">
    </a>
    <a href="https://www.tiktok.com/@muaytaxgroup" class="me-2 " target="_blank">
        <img src="{% static 'assets/images/iconos/png/blue-lines/icono-tiktok.png' %}" width="20" alt="">
    </a>

</footer>

<div class="modal fade modal-animate anim-blur " id="bookingModal" tabindex="-1" role="dialog"
    aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
            <div class="modal-body d-flex flex-column justify-content-center align-items-center">

                <div class="modal-body">
                    <div class="d-flex justify-content-center align-items-center text-center">
                        <div class="spinner-grow text-success animation-delay-1 " role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-grow text-success animation-delay-2" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-grow text-success animation-delay-3" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>&nbsp;</p>
                        <img style="width:110px;" src="{% static 'assets/images/logo.png' %}" />
                    </div>
                    <p class="text-white text-center mb-0"><b>Se está generando tu solicitud de llamada telefónica</b>
                    </p>
                    <p class="text-white text-center mb-0"><b>Por favor espera...</b></p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade modal-animate anim-blur " id="noAuthModal" tabindex="-1" role="dialog"
    aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
            <div class="modal-body d-flex flex-column justify-content-center align-items-center">

                <div class="modal-body">
                    <div class="d-flex justify-content-center align-items-center text-center">
                        <div class="spinner-grow text-success animation-delay-1 " role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-grow text-success animation-delay-2" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-grow text-success animation-delay-3" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>&nbsp;</p>
                        <img style="width:110px;" src="{% static 'assets/images/logo.png' %}" />
                    </div>
                    <p class="text-white text-center mb-0"><b>No tienes autorización para realizar esta acción</b>
                    </p>
                    <p class="text-white text-center mb-0"><b>Será redirigido a la página inicial...</b></p>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
{% block javascripts %}

<script src="{% static 'assets/js/bookings/public-booking.js' %}"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/select/select2.min-v4.1.0.js"></script>

<script type="application/javascript">

    const getBookingSpotsURL = '{% url "app_bookings:public_get_booking_spots" %}';

    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer)
            toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
    });
    
    const emailSubmit = document.getElementById('id_email_submit');

    const tokenSubmitForm = document.getElementById('access-token-form');
    const resendAccessTokenForm = document.getElementById('resendAccessTokenForm');

    const commentSubmitForm = document.getElementById('commentForm');
    const newInfoSubmitForm = document.getElementById('newInfoForm');
    const confirmForm = document.getElementById('confirmPublicBookingForm');

    let shortnameSellerApp = '';
    let seller_name = '';
    let seller_contact_prefix = '';
    let seller_contact_phone = '';
    let seller_contact_iso_code = '';

    let timeLeft = 300;
    let timer = document.getElementById('timeLeftElement');

    async function initiateSelect2CountryPrefix(client) {
        let data = await getCountryPhoneCodeList();
        let country_options = mapCountriesSelect2(data);

        function formatCountryText (country) {
            if (!country.id || country.disabled) {
                return country.text;
            }
            var baseUrl = "{% static 'assets/images/flags/' %}";
            var $country = $(
                '<span><img src="' + baseUrl  + country.isocode.toLowerCase() + '.svg" class="wid-30 pe-2" /> ' + country.text + '</span>'
            );
            return $country;
        };

        function formatCountrySelection (country) {
            if (!country.id || country.disabled) {
                return country.text;
            }
            var baseUrl = "{% static 'assets/images/flags/' %}";
            var $country = $(
                '<span><img src="' + baseUrl  + country.isocode.toLowerCase() + '.svg" class="wid-30 pe-2" /> ' + country.prefix + '</span>'
            );
            return $country;
        };

        let phonePrefixSelector = $(`#${client}SelectCountryPhone`).select2({
            placeholder: 'País',
            data: country_options,
            templateResult: formatCountryText,
            templateSelection: formatCountrySelection,
        });
        
        if (client === 'seller') {
            if (seller_contact_phone){
                $(`#id_${client}_new_phone`).val(seller_contact_phone);
            }
            if (seller_contact_prefix){
                $(`#id_${client}_new_country_prefix`).val(seller_contact_prefix);
                phonePrefixSelector.val(seller_contact_iso_code).trigger('change');
            }
            
        }

        phonePrefixSelector.on('select2:select', function (e) {
            $(`#id_${client}_new_country_prefix`).val(e.params.data.prefix);
            $(`#id_${client}_new_phone`).focus();
        });

        // Enable phone number autocomplete and sync (Select2)
        enablePhoneAutocompleteAndSync(client);
    }

    function mapCountriesSelect2(countries) {
        const options = [{
            id: 0,
            text: 'Selecciona el país',
            isocode: '',
            disabled: true
        }];

        const countryOptions = countries
            .filter(country => country.phoneprefix) // Filter out countries without phoneprefix
            .map((country, index) => ({
                id: country.iso_code,
                text: `${country.name} ${country.phoneprefix}`,
                isocode: country.iso_code,
                prefix: country.phoneprefix
            }));

        return options.concat(countryOptions);
    }

    async function fetchUpcomingHolidaysPublic() {
        try {
            const token = localStorage.getItem('private_token');
            const url = "{% url 'app_bookings:public_upcoming_holidays_list' %}";
            let response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            const data = await response.json();
            holidays = data.map(holiday => holiday.date);
        } catch (error) {
            console.log("Error catching holidays", error);
        }
    }

    emailSubmit.addEventListener('click', function(e){
        const form = e.target.closest('form');
        if (form.checkValidity()) {
            e.preventDefault();
            const formData = new FormData(form);
            submitEmailForm(formData);
        }
    });

    async function submitEmailForm(formData) {
        emailSubmit.disabled = true;
        const url = emailSubmit.form.action;
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (response.ok) {
                document.querySelector('.tab-pane.active').classList.remove('active', 'show');
                document.getElementById('access-token').classList.add('active', 'show');
                document.getElementById('email').classList.remove('is-invalid');
                document.getElementById('title-section').classList.add('d-none', 'd-sm-block');
                runTimer(document.querySelector('.timer'));
            }
            else {
                const data = await response.json();
                Toast.fire({
                    icon: 'error',
                    title: data.email ? data.email : 'Ha ocurrido un error. Por favor, recarga la página e intenta nuevamente.'
                });
                document.getElementById('email').classList.add('is-invalid');
                emailSubmit.disabled = false;
            }
            
        } catch (error) {
            console.error(error);
        }
    }

    tokenSubmitForm.addEventListener('submit', async function(e){
        e.preventDefault();
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;

        const formData = new FormData(form);
        formData.append('email', document.getElementById('email').value);
        const url = form.action;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (!response.ok) {
                const data = await response.json();
                Toast.fire({
                    icon: 'error',
                    title: data.message
                });
                submitButton.disabled = false;
            }
            else {
                const data = await response.json();
                const publicFirstAvailableURL = "{% url 'app_bookings:public_first_available' %}";

                localStorage.setItem('private_token', data.private_token);
                localStorage.setItem('user_email', document.getElementById('email').value);
                await fetchUpcomingHolidaysPublic();

                const token = localStorage.getItem('private_token');

                if (data.seller) {
                    shortnameSellerApp = data.seller.shortname;
                    seller_name = data.seller.name;
                    if (data.seller.contact_phone) {
                        seller_contact_prefix = data.seller.contact_phone.contact_phone_prefix;
                        seller_contact_phone = data.seller.contact_phone.contact_phone_number;
                        seller_contact_iso_code = data.seller.contact_phone.contact_phone_country_iso;
                    }
                    await findFirstAvailableDayPublic(shortnameSellerApp, publicFirstAvailableURL, token);
                    document.querySelector('.tab-pane.active').classList.remove('active', 'show');
                    document.getElementById('date').classList.add('active', 'show');
                }
                else {
                    await findFirstAvailableDayPublic(null, publicFirstAvailableURL, token);
                    document.querySelector('.tab-pane.active').classList.remove('active', 'show');
                    document.getElementById('date').classList.add('active', 'show');
                }
            }
        } catch (error) {
            console.error(error);
        }
    });

    resendAccessTokenForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;

        const formData = new FormData(form);
        formData.append('email', document.getElementById('email').value);
        const url = "{% url 'api_rest:api_auth:get_access_token_from_email' %}";

        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (response.ok) {
                timeLeft = 300;
                runTimer(document.querySelector('.timer'));
                document.getElementById('validateTokenBlock').classList.remove('d-none');
                document.getElementById('resendTokenBlock').classList.add('d-none');
                document.getElementById('id_access_code').value = '';
                document.getElementById('id_access_code_submit').disabled = false;
                submitButton.disabled = false;
                delete localStorage['private_token'];
            }
        } catch (error) {
            Toast.fire({
                icon: 'error',
                title: 'Ha ocurrido un error. Por favor, recarga la página e intenta nuevamente luego de unos minutos.'
            });
            submitButton.disabled = false;
            console.error(error);
        }



    });

    async function submitSelectDate(button) {
        button.disabled = true;
        const form = document.getElementById('submit-date');
        const formData = new FormData(form);
        const url = "{% url 'app_bookings:booking_public_new' %}";
        try {
            formData.append('date-submit', 'true');
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'error') {
                    Toast.fire({
                        icon: 'error',
                        title: data.message
                    });
                    button.disabled = false;
                }
                else {
                    document.querySelector('.tab-pane.active').classList.remove('active', 'show');
                    if (shortnameSellerApp){
                        document.getElementById('comment').classList.add('active', 'show');
                        initiateSelect2CountryPrefix('seller');
                    }
                    else {
                        initiateSelect2CountryPrefix('public');
                        document.getElementById('new-info').classList.add('active', 'show');
                    }
                }
            }
        } catch (error) {
            console.error(error);
        }

    }

    commentSubmitForm.addEventListener('submit', async function(e){
        e.preventDefault();
        const form = e.target;
        const commentSubmitButton = form.querySelector('button[type="submit"]');
        commentSubmitButton.disabled = true;

        const formData = new FormData(form);
        const url = "{% url 'app_bookings:public_booking_submit_seller_info' %}";
        const private_token = localStorage.getItem('private_token');
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${private_token}`,
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (response.ok) {
                const data = await response.json();
                updateBookingDetailsConfirm();
                document.querySelector('.tab-pane.active').classList.remove('active', 'show');
                document.getElementById('confirm').classList.add('active', 'show');
            }
            else {
                const data = await response.json();
                if (data.code == 'form_validation_error'){
                    document.getElementById('id_seller_new_phone').classList.remove('is-invalid');
                    document.getElementById('id_seller_new_phone').parentNode.querySelector('.invalid-feedback')?.remove();
                    
                    if (data.errors.seller_new_phone || data.errors.contact_phone){
                        document.getElementById('id_seller_new_phone').classList.add('is-invalid');
                        const errorSpan = document.createElement('span');
                        errorSpan.classList.add('invalid-feedback');
                        errorSpan.textContent = data.errors.seller_new_phone || data.errors.contact_phone;
                        document.getElementById('id_seller_new_phone').parentNode.appendChild(errorSpan);
                    }

                    Toast.fire({
                        icon: 'error',
                        title: data.message
                    });

                    
                }
                else {
                    Toast.fire({
                        icon: 'error',
                        title: 'Ha ocurrido un error en el servidor. Por favor, recarga la página e intenta nuevamente luego de unos minutos.'
                    });
                }
                
            }
        } catch (error) {
            Toast.fire({
                icon: 'error',
                title: 'Ha ocurrido un error. Por favor, recarga la página e intenta nuevamente luego de unos minutos.'
            });
        }
        commentSubmitButton.disabled = false;
    });

    newInfoSubmitForm.addEventListener('submit', async function(e){
        e.preventDefault();
        const form = e.target;
        const newInfoSubmitButton = form.querySelector('button[type="submit"]');
        newInfoSubmitButton.disabled = true;

        const formData = new FormData(form);
        formData.append('new-info-submit', 'true');
        const url = "{% url 'app_bookings:booking_public_new' %}";
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'error') {
                    Toast.fire({
                        icon: 'error',
                        title: data.message
                    });
                    newInfoSubmitButton.disabled = false;
                } 
                else {
                    updateBookingDetailsConfirm(true);
                    document.querySelector('.tab-pane.active').classList.remove('active', 'show');
                    document.getElementById('confirm').classList.add('active', 'show');
                }
            }
            else {
                Toast.fire({
                    icon: 'error',
                    title: 'Ha ocurrido un error en el servidor. Por favor, recarga la página e intenta nuevamente luego de unos minutos.'
                });
                newInfoSubmitButton.disabled = false;
            }
        } catch (error) {
            Toast.fire({
                icon: 'error',
                title: 'Ha ocurrido un error. Por favor, recarga la página e intenta nuevamente luego de unos minutos.'
            });
            newInfoSubmitButton.disabled = false;
            console.error(error);
        }

    });

    confirmForm.addEventListener('submit', async function(e){
        e.preventDefault();
        const form = e.target;
        const recaptchaResponse = grecaptcha.getResponse();
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        
        const formData = new FormData(form);
        const url = "{% url 'app_bookings:booking_public_new' %}";
        const private_token = localStorage.getItem('private_token');
        formData.append('g-recaptcha-response', recaptchaResponse);
        formData.append('confirm-public-booking', 'true');

        const bookingModal = document.querySelector("#bookingModal");
        const modal = new bootstrap.Modal(bookingModal, {
            keyboard: false,
            backdrop: 'static'
        });
        modal.show();

        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${private_token}`,
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            });
            if (response.status === 401){
                modal.hide();
                const noAuthModal= new bootstrap.Modal(document.getElementById('noAuthModal'), {
                    keyboard: false,
                    backdrop: 'static'
                });
                noAuthModal.show();
                setTimeout(() => {
                    delete localStorage['private_token'];
                    delete localStorage['user_email'];
                    window.location.href = "{% url 'app_bookings:booking_public_new' %}";
                }, 3000);
            }

            if (response.ok) {
                const data = await response.json();
                if (data.status === 'error') {
                    Toast.fire({
                        icon: 'error',
                        title: data.message
                    });
                    submitButton.disabled = false;
                    modal.hide();
                }
                else {
                    delete localStorage['private_token'];
                    delete localStorage['user_email'];
                    window.location.href = "{% url 'app_bookings:public_booking_success' %}";
                }
            }
        } catch (error) {
            Toast.fire({
                icon: 'error',
                title: 'Ha ocurrido un error. Por favor, recarga la página e intenta nuevamente luego de unos minutos.'
            });
            submitButton.disabled = false;
            console.error(error);
        }

    });

    async function getCountryPhoneCodeList(){
        const url = "{% url 'app_bookings:public_booking_country_list' %}";
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        const data = await response.json();

        return data;
    }

    function updateBookingDetailsConfirm(isNewUser = false) {
        const newPublicUserName = document.getElementById('id_public_new_name').value;
        const newPublicUserLastName = document.getElementById('id_public_new_last_name').value;
        const emailInsertedInitialy = document.getElementById('email').value;
        const newPublicPhoneNumber = document.getElementById('id_public_new_phone').value;
        const publicCountryPrefix = document.getElementById('id_public_new_country_prefix').value;
        const newPublicComment = document.getElementById('id_public_new_comment').value;
        const sellerComment = document.getElementById('id_public_comment').value;

        const seller_phone_prefix = document.getElementById('id_seller_new_country_prefix').value;
        const seller_phone = document.getElementById('id_seller_new_phone').value;

        if (isNewUser) {
            document.getElementById('id_confirmed_name_shown').textContent = `${newPublicUserName} ${newPublicUserLastName}`;
            document.getElementById('id_confirmed_name').value = newPublicUserName;
            document.getElementById('id_confirmed_last_name').value = newPublicUserLastName;

            document.getElementById('id_confirmed_phone_shown').textContent = `${publicCountryPrefix} ${newPublicPhoneNumber}`;
            document.getElementById('id_confirmed_phone').value = `${publicCountryPrefix} ${newPublicPhoneNumber}`;

            document.getElementById('id_confirmed_email').value = emailInsertedInitialy;

            document.getElementById('id_confirmed_comment').value = newPublicComment;
        }
        else {
            document.getElementById('confirm_name_block').classList.add('d-none');
            document.getElementById('confirm_seller_block').classList.remove('d-none');

            document.getElementById('id_confirmed_seller_name_shown').textContent = seller_name;
            document.getElementById('id_confirmed_seller').value = shortnameSellerApp;

            document.getElementById('id_confirmed_phone_shown').textContent = `${seller_phone_prefix} ${seller_phone}`;
            document.getElementById('id_confirmed_phone').value = `${seller_phone_prefix}${seller_phone}`;

            document.getElementById('id_confirmed_comment').value = sellerComment;
        }

        document.getElementById('id_confirmed_email_shown').textContent = emailInsertedInitialy;
    
        document.getElementById('id_confirmed_date_shown').textContent = formatDateSpanish(document.getElementById('id_selected_date').value);
        document.getElementById('id_confirmed_date').value = document.getElementById('id_selected_date').value;

        document.getElementById('id_confirmed_time_shown').textContent = document.getElementById('id_selected_time').value + ' h - (GMT +2)';
        document.getElementById('id_confirmed_time').value = document.getElementById('id_selected_time').value;

        document.getElementById('id_confirmed_public_managers').value = document.getElementById('id_selected_managers').value;

        // document.getElementById('id_confirmed_duration').value = 1; Se mantiene en 1 por defecto directamente en el form

    }

    function formatDateSpanish(dateString) {
        const days = ["Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"];
        const months = [
            "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
            "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
        ];
        const date = new Date(dateString);

        const dayName = days[date.getDay()];
        const day = date.getDate();
        const monthName = months[date.getMonth()];
        const year = date.getFullYear();

        const formattedDate = `${dayName} ${day} de ${monthName}, ${year}`;

        return formattedDate;
    }

    function isTimeLeft() {
        return timeLeft >= 0;
    }

    function runTimer(timerElement) {
        const timerCircle = timerElement.querySelector('svg > circle + circle');
        timerElement.classList.add('animatable');
        timerCircle.style.strokeDashoffset = 1;
        
        let countdownTimer = setInterval(function(){
            if(isTimeLeft()){
                const timeRemaining = timeLeft--;
                const normalizedTime = (timeRemaining - 300) / 300;
                timerCircle.style.strokeDashoffset = normalizedTime;
                timer.innerHTML = timeRemaining;
            } else {
                clearInterval(countdownTimer);
                document.getElementById('validateTokenBlock').classList.add('d-none');
                document.getElementById('resendTokenBlock').classList.remove('d-none');
                timerElement.classList.remove('animatable');
            }  
        }, 1000);
    }

    function isNumberKey(evt) {
        var charCode = (evt.which) ? evt.which : evt.keyCode
        if (charCode > 31 && (charCode < 48 || charCode > 57))
            return false;
        return true;
    }

    // Extrae y sincroniza el prefijo internacional visible de Select2 al input oculto.
    function syncPrefixFromSelect2Rendered(client) {
        const rendered = document.querySelector(`#select2-${client}SelectCountryPhone-container`);
        const prefixInput = document.querySelector(`#id_${client}_new_country_prefix`);
        const select2 = document.querySelector(`#${client}SelectCountryPhone`);

        if (!rendered || !prefixInput || !select2) return;

        const match = rendered.textContent.trim().match(/\+[\d]+/);
        
        if (match) {
            const prefix = match[0];
            prefixInput.value = prefix;
            console.log(`[SYNC] Prefijo extraído desde Select2 render: ${prefix}`);
        } else {
            prefixInput.value = '';
            select2.value = '';
            console.warn('[SYNC] No se detectó ningún prefijo en el render de Select2. Inputs limpiados.');
        }
    }

    // Habilita el autocompletado del teléfono y sincroniza el prefijo al cambiar el render de Select2.
    function enablePhoneAutocompleteAndSync(client) {
        const phoneInput = document.querySelector(`#id_${client}_new_phone`);
        const renderedSelector = document.querySelector(`#select2-${client}SelectCountryPhone-container`);
        const syncPrefix = () => syncPrefixFromSelect2Rendered(client);

        if (phoneInput) {
            phoneInput.setAttribute('autocomplete', 'tel-national');
            phoneInput.addEventListener('input', syncPrefix);
        }

        if (renderedSelector) {
            new MutationObserver(syncPrefix).observe(renderedSelector, { childList: true, subtree: true });
        }
    }

</script>
{% endblock javascripts %}