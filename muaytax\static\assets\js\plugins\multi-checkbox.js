/**
 * @file multi-checkbox.js
 * @fileOverview Multi-checkbox vanilla javascript component. Provides
 * an input with a dropdown of multiple checkboxes that are concatenated.
 * in the input when selected. See the README file for usage.
 * @version 0.1.11
 * <AUTHOR> <<EMAIL>>
 * @copyright ©2021 Stephen Montanus Software Engineering.
 * @license MIT
 */

/** @constant {Object} */


/**
 * Creates a new MultiCheckbox custom HTML element. Utilized in HTML as the
 * <multi-checkbox> tag.
 * @name MultiCheckbox
 * @class
 * @extends HTMLElement
 */
export class MultiCheckbox extends HTMLElement {
    selectAllMessage;
    selectedMessage;
    hoverMessage;

    /**
     * @description Specify observed attributes so that attributeChangedCallback will work.
     * @return {String[]} String array of attributes to be observed.
     */
    static get observedAttributes() {
        return ['separator', 'value', 'disabled'];
    }

    /**
     * @description Create a MultiCheckbox element. Fires when an instance of
     * the element is created or updated.
     */
    constructor(selectAllMessage, selectedMessage, hoverMessage) {
        super();
        this.selectAllMessage = selectAllMessage;
        this.selectedMessage = selectedMessage;
        this.hoverMessage = hoverMessage;
        this.attachShadow({mode: 'open'});
        const template = this.getTemplate(hoverMessage);
        this.shadowRoot.appendChild(template.content.cloneNode(true));
        // Component variables.
        this.dropDownVisible = false;
        this.inputRadius = this.shadowRoot.querySelector('input')
            .style.borderBottomLeftRadius;
        this.toggleRadius = this.shadowRoot.querySelector('#toggle-button')
            .style.borderBottomRightRadius;
        this.items = [];
    }

    /**
     * @description Get the MultiCheckbox element value.
     * @return {string} The value.
     */
    get value() {
        return this.getAttribute('value');
    }

    /**
     * @description Set the MultiCheckbox element value.
     * @param {string} newValue The new value of the element.
     */
    set value(newValue) {
        this.setAttribute('value', newValue);
    }

    /**
     * @description Get the MultiCheckbox item separator value.
     * @return {string} The separator value.
     */
    get separator() {
        return this.getAttribute('separator');
    }

    /**
     * @descriptionSet the MultiCheckbox item separator value.
     * @param {string} newValue The new value of the item separator.
     */
    set separator(newValue) {
        this.setAttribute('separator', newValue);
    }

    /**
     * @description Get the disabled attribute value.
     * @return {boolean} The disabled value.
     */
    get disabled() {
        return this.hasAttribute('disabled');
    }

    /**
     * @description Set the disabled attribute value.
     * @param {boolean} newValue The new disabled value.
     */
    set disabled(newValue) {
        if (newValue) {
            this.setAttribute('disabled', '');
        } else {
            this.removeAttribute('disabled');
        }
    }

    /**
     * @name updateItems
     * @description Update the item list when slot content changes.
     * @return {void}
     */
    updateItems() {
        // Remove original slot data.
        const itemList = this.shadowRoot.querySelector('slot')
            .assignedNodes({flatten: true})[0];
        if (itemList.childNodes[itemList.childNodes.length - 1].nodeName == 'UL') {
            itemList.removeChild(itemList.childNodes[itemList.childNodes.length - 1]);
        }
        // Update slot with new elements.
        this.items = this.shadowRoot.querySelector('slot')
            .assignedElements({flatten: true})[0]
            .children;
        // Add checkboxes to the component list items.
        this.addCheckBoxes();
        // Clear the input and custom element value.
        this.shadowRoot.querySelector('input').value = '';
        this.setAttribute('value', '');
    }

    /**
     * @name addCheckBoxes
     * @description Add check box inputs to the elements of the items list.
     * @return {void}
     */
    addCheckBoxes() {
        // Create the "Select All" checkbox.
        const selectAllId = `select-all`;
        const selectAllCheckbox = `
        <div style="padding: 10px 16px; cursor: pointer; border-bottom: 1px solid #a3a3a34d;">
            <label for="${selectAllId}" style="display: block; cursor:pointer;">
                <input type="checkbox" id="${selectAllId}" style="
                    margin-right: 10px;
                    height: var(--mc-checkbox-height, auto);
                    vertical-align: middle;
                    width: var(--mc-checkbox-width, auto);"
                    value="Select-all" />
                Seleccionar todo
            </label>
        </div>`;

        // Insert the "Select All" checkbox at the beginning of the dropdown.
        if (this.shadowRoot.querySelector('.multi-checkbox-container').querySelector(`#${selectAllId}`) === null) {
            this.shadowRoot.querySelector('.multi-checkbox-container').insertAdjacentHTML('afterbegin', selectAllCheckbox);
        }

        const checkboxes = []; // Store all checkbox elements.

        for (let i = 0; i < this.items.length; i++) {
            const checkboxId = `item-${i}`;
            const dataValue = this.items[i].getAttribute('value');
            const itemText = this.items[i].getAttribute('multi-title');
            this.items[i].innerHTML = `
                <div style="width: 100%; padding: 0px 16px; cursor: pointer;">
                    <input type="checkbox" id="${checkboxId}" style="
                        cursor: pointer;
                        margin-right: 10px;
                        height: var(--mc-checkbox-height, auto);
                        vertical-align: middle;
                        width: var(--mc-checkbox-width, auto);"
                        data-value="${dataValue}"
                        value="${itemText}" />
                        ${itemText}
                </div>`;

            const checkbox = this.items[i].querySelector('input[type="checkbox"]');
            checkboxes.push(checkbox); // Add the checkbox to the array.

            // Add a click event listener to the div element.
            const divElement = this.items[i].querySelector('div');
            divElement.addEventListener('click', () => {
                if (!this.disabled) {
                    checkbox.checked = !checkbox.checked; // Toggle the checkbox's checked state.
                    this.updateValue();
                    const event = new Event('change');
                    checkbox.dispatchEvent(event);
                    
                    checkSelectAll();
                    checkFilterState(); // Variable exclusivamente para la tabla de filtros de facturas
                }
            });

            // Add a click event listener to the checkbox element.
            const checkboxElement = this.items[i].querySelector('input[type="checkbox"]');
            checkboxElement.addEventListener('click', (event) => {
                if (!this.disabled) {
                    // Prevent the click event from propagating to the div element.
                    event.stopPropagation();
                    this.updateValue();
                    checkSelectAll();
                    checkFilterState(); // Variable exclusivamente para la tabla de filtros de facturas 
                }
            });
        }
        // Add a click event listener to the "Select All" checkbox.
        const selectAllElement = this.shadowRoot.querySelector(`#${selectAllId}`);
        selectAllElement.addEventListener('click', () => {
            if (!this.disabled) {
                const selectAllChecked = selectAllElement.checked;
                for (const checkbox of checkboxes) {
                    checkbox.checked = selectAllChecked;
                }
                this.updateValue(); 
                checkFilterState(); // Variable exclusivamente para la tabla de filtros de facturas     
            }
        });

        // Add a click event listener to any checkbox to uncheck "Select All" if any checkbox is unchecked.
        // for (const checkbox of checkboxes) {
        //     checkbox.addEventListener('click', () => {
        //         console.log(`checkeando el box ${checkbox} para deseleccionar el select all`);
        //         if (!this.disabled) {
        //             if (!checkbox.checked) {
        //                 selectAllElement.checked = false;
        //             }
        //             checkSelectAll(); // Check the "Select All" based on individual checkboxes.
        //         }
        //     });
        // }

        // Function to check the "Select All" based on individual checkboxes.
        const checkSelectAll = () => {
            const allChecked = checkboxes.every((checkbox) => checkbox.checked);
            selectAllElement.checked = allChecked;
        };

    }

    /**
     * @name updateValue
     * @description Update the custom element value based on checked boxes.
     * @return {void}
     */
    updateValue() {
        // Only run this when checkboxes are being clicked, not dynamically changed.
        if (this.dropDownVisible == true) {
            // Get the separator string.
            const sep = this.getAttribute('separator');
            // Initialize the string to store the new element value.
            let newVal = '';
            let dataVal = '';
            // Iterate over the list items, adding the values of checked checkboxes to
            // the new string.
            for (let i = 0; i < this.items.length; i++) {
                const chkBox = this.items[i].querySelector('input[type="checkbox"]');
                if (chkBox.checked) {
                    // Concatenate the checked value.
                    newVal = newVal + chkBox.value + sep;
                    dataVal = dataVal + chkBox.getAttribute('data-value') + sep;
                }
            }
            // Remove the last separator.
            newVal = newVal.slice(0, -sep.length);
            dataVal = dataVal.slice(0, -sep.length);
            // Update the value attribute to the new string.
            this.setAttribute('value', newVal);
            this.setAttribute('data-value', dataVal);
        }
    }

    /**
     * @name showDropDown
     * @description Show the drop down item list.
     * @return {void}
     */
    showDropDown() {
        this.shadowRoot.querySelector('.multi-checkbox-container').style.transform = 'scale(1, 1)';
        this.shadowRoot.querySelector('#toggle-button').innerHTML =
            `<svg x="0px" y="0px" viewBox="0 0 330 330">
                <path fill="currentColor" d="M324.001,209.25L173.997,96.75c-5.334-4-12.667-4-18,
                0L6.001,209.25c-6.627,4.971-7.971,14.373-3,21c2.947,3.93,7.451,6.001,12.012,
                6.001c3.131,0,6.29-0.978,8.988-3.001L164.998,127.5l141.003,105.75c6.629,
                4.972,16.03,3.627,21-3C331.972,223.623,330.628,214.221,324.001,209.25z"/>
            </svg>`;
    }

    /**
     * @name hideDropDown
     * @description Hide the drop down item list.
     * @return {void}
     */
    hideDropDown() {
        this.shadowRoot.querySelector('.multi-checkbox-container').style.transform = 'scale(1, 0)';
        this.shadowRoot.querySelector('input').style.borderBottomLeftRadius = this.inputRadius;
        this.shadowRoot.querySelector('#toggle-button')
            .style.borderBottomRightRadius = this.toggleRadius;
        this.shadowRoot.querySelector('#toggle-button').innerHTML =
            `<svg x="0px" y="0px" viewBox="0 0 330 330">
                <path fill="currentColor" d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,
                0.001l-139.39,139.393L25.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,
                5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393
                s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,
                325.607,79.393z"/>
            </svg>`;
    }

    /**
     * @name toggleDropDown
     * @description Toggle display of the drop down item list.
     * @return {void}
     */
    toggleDropDown() {
        this.dropDownVisible = !this.dropDownVisible;
        if (this.dropDownVisible) {
            this.showDropDown();
        } else {
            this.hideDropDown();
        }
    }

    /**
     * @name measureText
     * @description Measure the element text in pixels compared to input element
     * width.
     * @return {boolean}
     */
    measureText() {
        const valuePixelWidth = this.shadowRoot.querySelector('#measure-test')
            .getBoundingClientRect().width;
        const inputPixelWidth = this.shadowRoot.querySelector('input')
            .getBoundingClientRect().width;
        if (valuePixelWidth > (inputPixelWidth - 50)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @name connectedCallback
     * @description Fires when an instance is inserted into the document.
     * @return {void}
     */
    connectedCallback() {
        // Add button event listeners.
        this.shadowRoot.querySelector('#multiple-selector').addEventListener('click', () => {
            if (!this.disabled) {
                this.toggleDropDown();
            }
        });
        this.shadowRoot.querySelector('#toggle-button').addEventListener('click', () => {
            if (!this.disabled) {
                this.toggleDropDown();
            }
        
        });
        // Add slot change event listener
        this.shadowRoot.addEventListener('slotchange', () => this.updateItems());
        // Add checkbox event listener.
        this.shadowRoot.addEventListener('click', (event) => {
            if (event.target && event.target.id.split('-')[0] == 'item') {
                this.updateValue();
            }
        });
        // Add event listener to hide the dropdown when click is outside the component.
        document.addEventListener('click', (event) => {
            if (!event.composedPath().includes(this)) {
                this.dropDownVisible = false;
                this.hideDropDown();
            }
        });
    }

    /**
     * @name disconnectedCallback
     * @description Fires when an instance was removed from the document.
     * @return {void}
     */
    disconnectedCallback() {
        // Remove button event listeners.
        this.shadowRoot.querySelector('#toggle-button')
            .removeEventListener('click', () => this.toggleDropDown());
        this.shadowRoot.querySelector('#close-button')
            .removeEventListener('click', () => this.toggleDropDown());
        // Remove slot event listener.
        this.shadowRoot.querySelector('slot')
            .removeEventListener('slotchange', () => this.updateItems());
    }

    /**
     * @name attributeChangedCallback
     * @description Fires when an attribute was added, removed, or updated.
     * @param {string} attrName The element attribute name.
     * @param {string} oldVal The old value of the attribute.
     * @param {string} newVal The new value of the attribute.
     * @return {void}
     */
    attributeChangedCallback(attrName, oldVal, newVal) {
        switch (attrName) {
            case 'value':
                // Update the text measurement hidden span element.
                this.shadowRoot.querySelector('#measure-test').textContent = newVal;
                // Update the input.
                if (this.measureText()) { // Value string is wider than input width.
                    // Get the separator string.
                    const sep = this.getAttribute('separator');
                    // Split newVal into array using separator and get length.
                    const newValCount = newVal.split(sep).length;
                    // check if the count is equal to the max number of items.
                    if (newValCount == this.items.length) {
                        this.shadowRoot.querySelector('input').value = "Todo seleccionado";
                    } else {
                        this.shadowRoot.querySelector('input').value = newValCount + ' ' + this.selectedMessage;
                    }
                } else { // Value string is narrower than input width.
                    this.shadowRoot.querySelector('input').value = newVal;
                }
                if (this.dropDownVisible == false) {
                    // Get the separator string.
                    const sep = this.getAttribute('separator');
                    // Split newVal into array using separator
                    const newVals = newVal.split(sep);
                    // Update the checkboxes.
                    for (let i = 0; i < this.items.length; i++) {
                        const chkBox = this.items[i].querySelector('input[type="checkbox"]');
                        if (chkBox) { // Filter out nulls on page load.
                            if (newVals.includes(chkBox.value)) {
                                chkBox.checked = true;
                            } else {
                                chkBox.checked = false;
                            }
                        }
                    }
                }
                break;
            case 'separator':
                if (newVal !== oldVal) {
                    let valueString = this.getAttribute('value');
                    // Replace the separator string.
                    valueString = valueString.split(oldVal).join(newVal);
                    // Update the input and custom element value.
                    this.shadowRoot.querySelector('input').value = valueString;
                    this.setAttribute('value', valueString);
                }
                break;
            case 'disabled':
                if (newVal !== oldVal) {
                    if (this.disabled) {
                        this.shadowRoot.querySelector('input').classList.add('disabled');
                        this.shadowRoot.querySelector('#toggle-button').classList.add('disabled');
                    } else {
                        this.shadowRoot.querySelector('input').classList.remove('disabled');
                        this.shadowRoot.querySelector('#toggle-button').classList.remove('disabled');
                    }
                }
                break;
        }
    }

    getTemplate(hoverMessage) {
        const template = document.createElement('template');
        template.innerHTML = `
            <style>
                .disabled {
                    cursor: not-allowed;
                    pointer-events: none;
                    opacity: 1;
                    background-color: #e9ecef;
                }
                .multi-checkbox {
                    position: relative;
                    display: var(--mc-display, inline-block);
                    margin: var(--mc-margin, 0);
                    width: 100%;
                    font-size: var(--mc-font, 0.9em);
                }

                .multi-checkbox-container {
                    translate: 0 4px;
                    background: var(--mc-dropdown-background, #efefef);
                    border-bottom: var(--mc-border, 1px solid #000000);
                    border-left: var(--mc-border, 1px solid #000000);
                    border-right: var(--mc-border, 1px solid #000000);
                    border-top: var(--mc-border, 1px solid #000000);
                    border-bottom-left-radius: var(--mc-border-radius, 2px);
                    border-bottom-right-radius: var(--mc-border-radius, 2px);
                    box-sizing: border-box;
                    -webkit-box-shadow: var(--mc-dropdown-box-shadow, 3px 3px 5px 1px rgba(0,0,0,0.35));
                    -moz-box-shadow: var(--mc-dropdown-box-shadow, 3px 3px 5px 1px rgba(0,0,0,0.35));
                    display: block;
                    max-height: var(--mc-dropdown-max-height, 100px);
                    position: absolute;
                    text-align: var(--mc-dropdown-text-align, left);
                    transform: scale(1, 0);
                    transform-origin: top left;
                    overflow-y: auto;
                    width: var(--mc-dropdown-width, fit-content);
                    z-index: var(--mc-cont-z-index, 20);
                    border-radius: .25rem;
                    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                }

                input {
                    background: var(--mc-target-background, #efefef);
                    border: var(--mc-border, 1px solid #000000);
                    color: var(--mc-target-color, #000000);
                    cursor: var(--mc-target-cursor, default);
                    font: var(--mc-font, 400 0.9em Arial);
                    margin: var(--mc-target-margin, 0);
                    max-height: var(--mc-target-max-height);
                    min-height: var(--mc-target-min-height);
                    max-width: var(--mc-target-max-width);
                    min-width: var(--mc-target-min-width);
                    padding: 10px 16px;
                    outline: var(--mc-target-outline, none);
                    vertical-align: var(--mc-vertical-align, middle);
                    width: var(--mc-target-width, 175px);
                    border-radius: .25rem;
                }
                input::placeholder {
                    color: #212529;
                }
                #measure-test {
                    left: 0;
                    position: absolute;
                    visibility: hidden;
                    white-space: nowrap;
                    max-width: 110%;
                    overflow: hidden; /* Hide overflowing content */
                    text-overflow: ellipsis; /* Add ellipsis (...) for truncated text */
                }

                svg {
                    vertical-align: middle;
                }

                ::slotted(ul) {
                    color: var(--mc-dropdown-color, #000000);
                    font: var(--mc-font, 400 0.9em Arial) !important;
                    line-height: var(--mc-dropdown-line-height, 2em);
                    list-style-type: none;
                    margin: var(--mc-ul-margin, 5px);
                    padding: var(--mc-ul-padding, 0);
                    overflow-wrap: break-word;
                    padding: 10px 0px!important;
                }

                #toggle-button {
                    position: absolute;
                    left: 100%;
                    top: 0;
                    transform: translate(-100%, 0);
                    background: transparent;
                    border: none;
                    border-bottom-right-radius: var(--mc-border-radius, 2px);
                    border-top-right-radius: var(--mc-border-radius, 2px);
                    color: var(--mc-toggle-button-color, #000000);
                    cursor: var(--mc-toggle-button-cursor, pointer);
                    height: var(--mc-toggle-button-height, 30px);
                    margin-left: -5px;
                    outline: var(--mc-toggle-button-outline, none);
                    vertical-align: var(--mc-vertical-align, middle);
                    width: var(--mc-toggle-button-width, 30px);
                    z-index: var(--mc-z-index, 8);
                }

                #close-button {
                    background: var(--mc-close-button-background, #efefef);
                    border: var(--mc-close-button-border, none);
                    border-radius: var(--mc-close-button-border-radius, default);
                    color: var(--mc-close-button-color, #000000);
                    cursor: var(--mc-close-button-cursor, pointer);
                    display: var(--mc-close-button-display, block);
                    height: var(--mc-close-button-height, 22px);
                    margin: var(--mc-close-button-margin, 5px auto);
                    outline: var(--mc-close-button-outline, none);
                    width: var(--mc-close-button-width, 22px);
                }
            </style>

            <div class="multi-checkbox">
                <input style="width:-webkit-fill-available;" placeholder="${hoverMessage}" id="multiple-selector" type="text" readonly  />
                <span id="measure-test"></span>
                <button id="toggle-button">
                    <svg x="0px" y="0px" viewBox="0 0 330 330">
                        <path fill="currentColor" d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,
                        0.001l-139.39,139.393L25.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,
                        5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393
                        s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,
                        325.607,79.393z"/>
                    </svg>
                </button>
                <div class="multi-checkbox-container">
                    <div><slot name="check-values" /></div>

                </div>
            </div>`;
        return template;
    }
}

export class TransactionCheckbox extends MultiCheckbox {
    constructor() {
        super('Todas las transacciones', 'Transacciones seleccionadas', 'Todas las transacciones');
    }
}
export class DepartureCountryCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los países', 'Países seleccionados', 'Todos los países');
    }
}
export class TaxCountryCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los países', 'Países seleccionados', 'Todos los países');
    }
}
export class VatRatesCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los IVA', 'IVA(s) seleccionado(s)', 'Todos los IVA');
    }
}
export class StatusCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los estados', 'Estados seleccionados', 'Seleccione estados');
    }
}
export class InvoiceTypeCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los tipos', 'Tipos seleccionados', 'Seleccione tipos de factura');
    }
}
export class MonthCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los meses', 'Meses seleccionados', 'Seleccione meses');
    }
}
export class EconomicActivityCheckbox extends MultiCheckbox {
    constructor() {
        super('Todas las actividades', 'Actividades seleccionados', 'Seleccione una actividad');
    }
}
export class ArrivalCountryCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los países', 'Países seleccionados', 'Todos los países');
    }
}
export class AccountingAccountCheckbox extends MultiCheckbox {
    constructor() {
        super('Todas las cuentas contables', 'Cuentas Contables seleccionadas', 'Todas las cuentas contables');
    }
}

export class OriginCheckbox extends MultiCheckbox {
    constructor() {
        super('Todos los origenes', 'Origenes seleccionados', 'Todos los origenes');
    }
}

export class ReverseChargeCheckbox extends MultiCheckbox {
    constructor() {
        super('Todas las operaciones', 'Operaciones seleccionadas', 'Todas las operaciones');
    }
}

export class EqtaxCheckbox extends MultiCheckbox {
    constructor() {
        super('Todas los recargos', 'Recargos seleccionados', 'Todos los recargos');
    }
}

// export class MultiOperationVerifactuCheckbox extends MultiCheckbox {
//     constructor() {
//         super('Todas las operaciones', 'Operaciones seleccionadas', 'Todas las operaciones');
//     }
// }

// export class MultiStatusInVerifactuCheckbox extends MultiCheckbox {
//     constructor() {
//         super('Todos los estados', 'Estados seleccionads', 'Todos los estados');
//     }
// }

customElements.define('multi-transaction', TransactionCheckbox);
customElements.define('multi-departurecountry', DepartureCountryCheckbox);
customElements.define('multi-taxcountry', TaxCountryCheckbox);
customElements.define('multi-vatrates', VatRatesCheckbox);
customElements.define('multi-status', StatusCheckbox);
customElements.define('multi-invoicetype', InvoiceTypeCheckbox);
customElements.define('multi-month', MonthCheckbox);
customElements.define('multi-economicactivity', EconomicActivityCheckbox);
customElements.define('multi-arrivalcountry', ArrivalCountryCheckbox);
customElements.define('multi-accountingaccount', AccountingAccountCheckbox);
customElements.define('multi-origin', OriginCheckbox);
customElements.define('multi-reverse-charge', ReverseChargeCheckbox);
customElements.define('multi-eqtax', EqtaxCheckbox);
// customElements.define('multi-operationverifactu', MultiOperationVerifactuCheckbox);
// customElements.define('multi-statusverifactu', MultiStatusInVerifactuCheckbox);