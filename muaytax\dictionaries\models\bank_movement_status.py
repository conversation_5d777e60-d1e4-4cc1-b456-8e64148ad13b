from django.db import models

class BankMovementStatus(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=100,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Estado del Movimiento Bancario"
        verbose_name_plural = "Estados de los Movimientos Bancarios"
    
    def __str__(self):
        return self.description
    
# @admin.register(BankMovementStatus)
# class BankTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]