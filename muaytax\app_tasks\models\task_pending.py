from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError

from datetime import timedelta

from muaytax.users.models import User
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.task_type import TaskType


class TaskPending(models.Model):
    NOTIFICATION_CHOICES = [
        ('none', 'No'),
        ('minimal', 'Mínima'),
        ('invasive', 'Invasiva'),
        ('blocking', 'Bloqueante'),
    ]

    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='tasks',
        verbose_name='Usuario'
    )

    task_type = models.ForeignKey(
        TaskType,
        on_delete=models.CASCADE,
        related_name='tasks',
        verbose_name='Tipo de Tarea',
        null=True
    ) 

    description = models.TextField(
        max_length=200,
        verbose_name='Descripción'
    )

    created_at = models.DateTimeField(default=timezone.now, verbose_name='Fecha de Creación')
    modified_at = models.DateTimeField(auto_now=True, verbose_name='<PERSON>cha de Modificación')

    notification_type = models.CharField(
        max_length=20, 
        choices=NOTIFICATION_CHOICES, 
        default='none',
        verbose_name='Notificación'
    )

    notification_days_before_deadline = models.PositiveIntegerField(
        null=True, 
        blank=True,
        verbose_name='Notificar x-dias Antes',
        help_text='Días antes de la fecha límite para ser notificado'
    )

    due_date = models.DateField(
        null=True, 
        blank=True,
        verbose_name='Fecha Límite',
        help_text='Fecha límite para completar esta tarea'
    )

    seen = models.BooleanField(
        default=False,
        verbose_name='Vista'
    )

    completed = models.BooleanField(
        default=False,
        verbose_name='Completada'
    )

    class Meta:
        verbose_name = 'Tarea Pendiente'
        verbose_name_plural = 'Tareas Pendientes'
    
    # Rol de usuario
    @property
    def user_role(self):
        return self.user.role
    
    @property
    def user_shortname(self):
        if self.user_role == 'manager':
            return self.user.name
        try:
            return self.user.seller.shortname
        except Seller.DoesNotExist:
            return None

    # Dias que faltan para limite (NOTE: NO es necesario)
    @property
    def days_before_due_date(self):
        if self.due_date:
            today = timezone.now().date()
            delta = self.due_date - today
            return delta.days
        return -1
    
    # Fecha en que se vio la tarea
    @property
    def view_date(self):
        if self.seen:
            return self.modified_at.date()
        return None

    # Fecha en que fue notificado
    @property
    def notification_date(self):
        if self.due_date and self.notification_days_before_deadline:
            return self.due_date - timedelta(days=self.notification_days_before_deadline)
        return None
    
    # Ultima accion con el booleano completado
    @property
    def last_action(self):
        return self.completed
    
    def clean(self):
        super().clean()
        errors = {}
        
        if self.user_id is None:
            errors['user'] = "Debe seleccionar primero un Usuario."
        
        else:
            if not self.task_type:
                errors['task_type'] = 'El campo tipo de tarea es obligatorio.'
            
            if not self.description:
                errors['description'] = 'El campo descripción es obligatorio.'
        
        if self.due_date and self.created_at and self.due_date < self.created_at.date():
            errors['due_date'] = 'La fecha límite no puede ser menor que la fecha de creación.'
            
        if self.notification_type == 'none' and self.notification_days_before_deadline is not None:
            errors['notification_days_before_deadline'] = 'El número de días antes de notificar debe ser nulo si la notificación es "No".'
            
        # if self.completed and not self.seen:
        #     errors['seen'] = 'Una tarea no puede marcarse como completada si no está marcada como vista.'
        
        if errors:
            raise ValidationError(errors)
    
    def save(self, *args, **kwargs):
        self.clean()
        print(f'days_before_due_date -->>>>> {self.days_before_due_date}')
        print(f'notification_date -->>>>> {self.notification_date}')
        print(f'view_date -->>>>> {self.view_date}')
        
        # Si la tarea está completada, asegúrate de que también esté marcada como vista
        if self.completed:
            self.seen = True
        
        if self.pk is not None:
            original = TaskPending.objects.get(pk=self.pk)
            if original.completed != self.completed:
                self.modified_at = timezone.now()
        super().save(*args, **kwargs)
        
    def __str__(self):
        return f'Tarea para {self.user} - tipo {self.task_type}'