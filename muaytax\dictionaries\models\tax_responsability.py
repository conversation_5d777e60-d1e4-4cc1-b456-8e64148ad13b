from django.db import models

class TaxResponsability(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    class Meta:
        verbose_name = "Responsable de las tasas"
        verbose_name_plural = "Responsables de las tasas"
    
    def __str__(self):
        return self.description
    
# @admin.register(TaxResponsability)
# class TaxResponsabilityAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
