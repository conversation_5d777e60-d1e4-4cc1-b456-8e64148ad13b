from django.db import models
from django.core import validators

class Service(models.Model):

  seller = models.ForeignKey(
    "sellers.Seller",
    on_delete=models.CASCADE,
    related_name="service_seller",
    verbose_name="Vendedor",
  )

  service_name = models.ForeignKey(
    "dictionaries.ServiceType",
    on_delete=models.CASCADE,
    related_name="service_name",
    verbose_name="Nombre del Servicio",
  )

  contracting_date = models.DateField(
    null=True,
    blank=True,
    verbose_name="Fecha de Contratación",
  )

  contracting_discontinue = models.DateField(
    null=True,
    blank=True,
    verbose_name="Fecha de Contratacion Baja",
  )

  start_contracting_date = models.DateField(
    null=True,
    blank=True,
    verbose_name="Fecha de Inicio de Servicio",
  )

  end_contracting_date = models.DateField(
    null=True,
    blank=True,
    verbose_name="Fecha de Fin de Servicio",
  )

  activation_date = models.DateField(
    null=True,
    blank=True,
    verbose_name="Fecha de Alta en Hacienda",
  )

  deactivation_date = models.DateField(
    null=True,
    blank=True,
    verbose_name="Fecha de Baja en Hacienda",
  )

  quantity = models.IntegerField(
    null=True,
    blank=True,
    default=1,
    verbose_name="Cantidad",
    validators=[
      validators.MinValueValidator(-1),
    ],
  )

  year = models.PositiveIntegerField(
    null=True,
    blank=True,
    verbose_name="Año",
  )

  created_at = models.DateTimeField(auto_now_add=True)

  modified_at = models.DateTimeField(auto_now=True)

  class Meta:
      verbose_name = "Servicio contratado"
      verbose_name_plural = "Servicios contratados"

  def __str__(self):
    return f"{self.service_name.description} - AÑO: {self.year} - CANTIDAD: {self.quantity}"

