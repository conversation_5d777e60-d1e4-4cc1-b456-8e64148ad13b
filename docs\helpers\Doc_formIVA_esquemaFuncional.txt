🧩 ESQUEMA FUNCIONAL – seller_vat_request_service_iva.html

1. NAVEGACIÓN ENTRE SECCIONES (Tabs): La navegación del formulario se organiza mediante pestañas (nav-tabs) controladas por
                                        
                                        <ul class="nav nav-tabs" id="myTab" role="tablist">

    Cada pestaña apunta a un tab-pane individual con ids:

        #general → Información General
        #members → Socio/s
        #documents → Documentos por País
        #summary → Resumen y Finalizar

    function changeTab(tabId) { ... } -> Esta función se encarga de:

        - Activar la pestaña seleccionada.
        - Ocultar las otras.
        - Actualizar la barra de progreso (updateProgressBar(tabId)).

2. INCLUDES PARCIALES POR SECCIÓN: La vista se divide en bloques con includes:

    Sección	Include asociado

        - Empresa	    -> sellers/include/service_iva/company_info_form.html
        - Países	    -> sellers/include/service_iva/contracted_countries_cards.html
        - Migración	    -> sellers/include/service_iva/migration_info_form.html
        - Socios	    -> sellers/include/service_iva/members_info.html
        - Documentos	-> sellers/include/service_iva/country_documents.html
        - Resumen final	-> sellers/include/service_iva/final_summary.html
        - Modal socios	-> sellers/include/service_iva/seller_vat_partners.html
        - Loading modal	-> sellers/include/service_iva/shared/loading_modal.html

3. VALIDACIÓN DEL FORMULARIO: La validación se ejecuta principalmente en:

    async function processedFormularioIVA() -> Validaciones que realiza:

        - Campos requeridos vacíos (por tipo de input, required o .required)
        - Archivos no adjuntados (comprobación vía input.files y texto visible en fileinfo)
        - Porcentaje de socios confirmado = 100%
        - Socios con errores pendientes
        - Países enviados (submittedCountries) no se vuelven a validar

        Si hay errores:

            Swal.fire({
                icon: "error",
                title: "Errores en el formulario",
                html: htmlContent
            });

4. STRUCTURA DE ENVÍO: El formulario se envía vía POST usando FormData, generado por:

                const { json: finalJson, formData } = generateFormJson();

    La funcion generateFormJson() -> Genera el json que se envia en el post, recorriendo todos los formularios identificados por:(#company_info_form, #migration-form-<ISO>, #documents-form-<ISO>)

        Luego Extrae:
            - Inputs normales (input, select, textarea)
            - Archivos (.files[0] o existentes fileinfo)
            - Detecta socios pendientes
        
    Y al final Devuelve la estructura del json completa:

        {
        "iva_form": {
            "company_info_form": {
            "nombre_entidad": "XYZ",
            ...
            },
            "migration_info_DE": {
            ...
            },
            "documents_by_country_FR": {
            ...
            },
            "partners_pending_validation": false
        }
        }
    
    NOTE: Esta estructura JSON va tanto en formData.append("iva_form", JSON.stringify(...)) como en el endpoint final de envío vat_submit_final.

5. LOCKS DE CAMPOS: La función lockReadonlyFields(...) y sus helpers (lockCompanyInfoFields, lockMigrationFormByIso, etc.):

    - Desactivan inputs (readonly + disabled)
    - Sustituyen inputs de tipo file por <p> de solo lectura con nombre de archivo cargado
    - Estilizan botones como bloqueados si ya se ha enviado el país correspondiente

    Esto depende de: isFormProcessed, submittedCountries, partialSaved

# TAB # 1
## ESTRUCTURA FUNCIONAL – TAB 1: Información General
    
    🔹 1. Include: company_info_form.html: Contiene campos básicos del vendedor o empresa.

        > Campos:
            - Campo	Nombre de variable	Observaciones
            - Nombre de la Empresa	company_form.name	Texto
            - Tipo de Entidad	company_form.legal_entity	A convertir a selector limitado (EMPRESA / AUTÓNOMO)
            - Dirección	company_form.seller_address	Texto
            - Teléfono	company_form.phone	Texto
            - Tipo de actividad	company_form.activity_type	Dinámico, activa opciones de productos
            - Productos/Servicios	company_form.products_and_services	Dependiente del anterior
            - Descripción actividad	company_form.desc_main_activity	Texto largo
            - Dirección Amazon VIES	company_form.amazon_vies_screenshot	Archivo
            - Escrituras de Empresa	company_form.business_deeds	Archivo
            - Registro Mercantil (SL)	company_form.mercantile_registry	Solo si SL
            - Certificados LLC	company_form.business_registry, company_form.comparison_certificate	Solo si LLC

        JS relevante: 
            - Rellenado automático con company_info_data
            - Validación visual con validation_company_info
            - Lógica condicional para productos según actividad

    🔹 2. Include: contracted_countries_cards.html: Muestra un resumen de los países contratados para servicio IVA.

        Estructura: 2 Tablas con 3 columnas (Mantenimiento y Alta NUeva con mantenimiento) Sin inputs ni lógica dinámica compleja. Solo informativo y visual:

            > columnas:
                - País (ISO code + nombre del pais)
                - Fecha de compra (fecha de contratación)
                - Estado (Enviado / Editable / Error)

    🔹 3. Include: migration_info_form.html: Muestra países con mantenimiento contratado que requieren migración.

        Estructura:
            - Botones de país: clic para desplegar su formulario
            - Formulario por país (migration-form-<ISO>): campos por país
            - Campos base:
            - Fecha baja gestor anterior
            - Contabilidad año anterior
            - Fecha hasta la que se presentó contabilidad actual
            - Campos condicionales:
            - IT: frecuencia de IVA
            - FR: info gestor anterior + carta desvinculación

        Funcionalidad:
            - Validación con validation_migration
            - Auto-rellenado con migration_info_data
            - Activación de check ✅ si todos los campos están rellenos
            - Lógica de errores por país en botón
        
CONCLUSIÓN: Este esquema permite reorganizar el formulario sin romper:
                - Lógica de validación
                - Envío de datos
                - Separación visual en pestañas
                - Bloqueo dinámico por país o formulario procesado

>> FLUJO COMPLETO DE DATOS

              ┌─────────────────────────────────────────────┐
              │           company_info_form.html            │
              │       (Formulario general de entidad)       │
              └─────────────────────────────────────────────┘
                               ↓
              ┌─────────────────────────────────────────────┐
              │       contracted_countries_cards.html        │
              │     (Tabla resumen de países contratados)    │
              └─────────────────────────────────────────────┘
                               ↓
              ┌─────────────────────────────────────────────┐
              │         migration_info_form.html            │
              │   (Datos adicionales por país contratado)   │
              └─────────────────────────────────────────────┘

>> ESTRUCTURA DEL BLOQUE JSON GENERADO
    {
        "iva_form": {
            "company_info_form": {
            "name": "...",
            "legal_entity": "autonomo/empresa",
            "seller_address": "...",
            ...
            },
            "migration_info_FR": {
            "previous_manager_name": "...",
            ...
            },
            ...
        }
    }


