{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
{{ country.name | title }}
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/all/all.min-v5.15.4.css"> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    fieldset.form-borders {
      border: 1px groove #838383 !important;
      padding: 0 1.4em 1.4em 1.4em !important;
      margin: 0 0 1.5em 0 !important;
      -webkit-box-shadow:  0px 0px 0px 0px #000;
      box-shadow:  0px 0px 0px 0px #000;
    }
    legend.form-borders {
      text-align: left !important;
      width:inherit; /* Or auto */
      padding:0 10px; /* To give a bit of padding on the left and right */
      border-bottom: none;
      float: unset !important;
    }


    #list-table td span {
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }

    .dataTables_filter {
      display: none;
    }
    .head_name{
       width: 40%;
    }

    .pendings{
      width: 9%;
    }
    .login{
        width: 5%;
    }
    .actions{
        width: 5%;
    }
    #showcolumns{
      padding: 10px 10px;
    }
    .modal-size{
      max-width: 80%;
    }
    .list-causes-warning{
      list-style-type: circle;
    }
     .card{
      max-height: 73%;
    }
     .row-cards{
      margin-bottom: -25px;
    } 
    .error-title{
      color: #d11507;
      font-weight: bold;
    }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">IVA {{ country.name | title }}</h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
          </li>
          <li class="breadcrumb-item">
            <a href=".">IVA {{ country.name | title }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
        <div class="dt-responsive">
          <fieldset class="form-borders">
            <legend class="form-borders">Filtros <i class="fa-solid fa-filter"></i></legend>
            <div class="row d-flex mt-3">
              <!-- Search + Pagination -->
              <div class="col-5 d-flex justify-content-center align-items-start">
                <div class="input-group">
                  <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()" />
                </div>
              </div>
              <!-- Search + Pagination -->
              <div class="col-3">
                <select class="form-select form-control" name="period-input" id="period" onchange="onChangePeriodYear()">
                  <option value="0A">Anual</option>
                  <option value="Q1">Trimestre 1</option>
                  <option value="Q2">Trimestre 2</option>
                  <option value="Q3">Trimestre 3</option>
                  <option value="Q4">Trimestre 4</option>
                </select>
              </div>
              <div class="col-3">
                <select class="form-select form-control" name="period-input" id="year" onchange="onChangePeriodYear()">
                  <option value="2022">2022</option>
                  <option value="2023">2023</option>
                  <option value="2024">2024</option>
                  <option value="2025">2025</option>
                </select>
              </div>
              <div class="col-1 d-flex  align-items-center">
                <p class="mx-1">
                  <button id="showcolumns" class="btn btn-info m-0" id="infoModal" data-bs-toggle="modal" data-bs-target="#modal" >
                    <i class="fa-regular fa-circle-question fa-lg"></i>
                    <b>Iconos </b>
                  </button>
                </p>
              </div>
            </div>
          </fieldset>
          <!-- Total pending invoices -->
          <div class="row row-cards">
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL FACTURAS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b id="total_invoices_count">&nbsp</b></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS REQUERIDOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="pending-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-file-contract " style="color: #FF0000;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="revision-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-regular fa-clock fa-sm" style="color: #7c7d7e;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS RECHAZADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="disagreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-xmark" style="color: #FE8330;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS ACEPTADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="agreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-check" style="color: #ffd700;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL MODELOS PRESENTADOS</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted" ><b id="presented-model-count">&nbsp</b>&nbsp&nbsp<i class="fas fa-check-double" style="color: #02c018;"></i></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div id="id_table" style="display: block;">
            <table id="seller-list-table" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th>Nombre</th>
                  <th>Pendientes</th>
                  <th style="display: none">Priority min</th>
                  <th style="display: none">Priority avg</th>
                  <th style="display:">M-303 Q4</th>
                  <th style="display:">M-347</th>
                  <th style="display:">M-390</th>
                  <th style="display:">M-184</th>
                  <th >Último acceso</th>
                  <th style="width:5%;">Acciones</th>
                </tr>
              </thead>
              <tbody>
              </tbody>
            </table>

            <!-- Modal Info-->
            <div class="modal fade " id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
              <div class="modal-dialog modal-dialog-centered modal-lg modal-size" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">Información sobre los iconos del listado</h5>
                  </div>
                    <div class="modal-body">
                      {% comment %} <div class="col form-group form-check p-3">
                        <p> <b> <h4 style= "text-align: center;">¿Está seguro que desea eliminar las facturas seleccionadas?</b></h4></p>
                      </div> {% endcomment %}
                      <div class="row d-flex align-items-center">
                        <div class= "col">
                          <ul>
                            <li><p><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i> <b class="icon-text-size">&nbsp;Modelo requerido</b></li></p>
                            <li><p><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i> <b class="icon-text-size">&nbsp;Modelo pendiente de confirmar</b></li></p>  
                            <li><p><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i> <b class="icon-text-size">&nbsp;Modelo rechazado por el cliente</b></p></li> 
                            <li><p><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i> <b class="icon-text-size">&nbsp;Modelo aceptado por el cliente</b></p></li>
                          </ul>
                        </div>
                        <div class= "col">
                          <ul>
                            <li><p><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i> <b class="icon-text-size">&nbsp;Modelo presentado</b></p></li> 
                            <li><p><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Modelo NO requerido</b></p></li>
                            
                          </ul>
                        </div>
                        
                      </div>
                      <br>
                      
                      
                    </div>
                  <div class="modal-footer d-flex justify-content-center">
                      <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">Cerrar</button>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}

<!-- JQUERY DATATABLES -->
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
<script>
    const debug = true;
    const ajaxData = (d) => {
        let tParams = "";
        let year = document.getElementById("year").value;
        let period = document.getElementById("period").value;
        if (year) {
            d.year = year
            tParams += "&year=" + year;
        }
        if (period) {
            d.period = period
            tParams += "&period=" + period;
        }
        getTotals(tParams);
        return d
    }

    let dataTable = null;

    window.onload = function () {
        let periodSelect = document.getElementById("period");
        let yearSelect = document.getElementById("year");

        function loadDataTable() {
            return new Promise((resolve, reject) => {
                dataTable = $("#seller-list-table").DataTable({
                    "serverSide": false,
                    "ajax": {
                        "dataSrc": "data", 
                        "url": "{% url 'app_sellers:seller_vatcountry_list_dt' 'ES' %}", 
                        "data": function (d) {
                            ajaxData(d);
                        }
                    },
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
                        "lengthMenu": "_MENU_",
                        "zeroRecords": "No se han encontrado vendedores.",
                        "info": "_START_ a _END_ de un total de _TOTAL_",
                        "search": "Buscar:",
                        "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                        "infoFiltered": ""
                    },
                    "columns": [{
                        "data": "user_name", "className": "head_name", "render": function (data, type, row) {
                            let html = '';
                            html += '<td class="align-middle">';
                            html += '<div class="d-inline-block">';
                            html += '<h6 class="m-b-0"><b>';

                            let name = row.seller_name;
                            if (typeof name === 'string') {
                                const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
                                const words = row.seller_name.split(' ').map(function (word) {
                                    const lowerWord = word.toLowerCase();
                                    if (lowerCaseSuffixes.includes(lowerWord)) {
                                        return word.toUpperCase();
                                    } else {
                                        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                                    }
                                });
                                html += words.join(' ');
                            }
                            html += '</b>';
                            if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                                html += ' - ' + row.user_name.split(' ').map(function (word) {
                                    return word.charAt(0).toUpperCase() + word.slice(1);
                                }).join(' ');
                            }

                            html += '</h6>';
                            html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                            html += '</div>';
                            html += '</td>';

                            return html;
                        }
                    }, 
                    {
                        "data": "num_pending_invoices",
                        "className": "pendings",
                        "render": function (data, type, row) {
                            if (data && (type === 'display' || type === 'filter')) {
                                let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                                html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                                html += '<div class="progress" style="height: 15px;">';
                                html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                                html += '</div>';
                                html += '</td>';
                                return html;
                            }
                            return data;
                        }
                    }, 
                    {
                        "data": null, "visible": false, "render": function (data, type, row) {
                            let html = '';
                            html += '<div>' + row.model_min + '</div>';
                            return html;
                        }
                    }, 
                    {
                        "data": null, "visible": false, "render": function (data, type, row) {
                            let html = '';
                            html += '<div>' + row.model_avg + '</div>';
                            return html;
                        }
                    }, 
                    {
                      "data": "model_303_q4", "className": "model", "render": function (data, type, row) {
                          let html = ' ';
                          let m303 = row.model_303_q4;
                          if (m303 == "presented") {
                              html += '<span class="d-none">' + 5 + '</span>';
                              html += '<a href=""><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                          } else if (m303 == "pending") {
                              html += '<span class="d-none">' + 4 + '</span>';
                              html += '<a href="/sellers/' + row.shortname + '/model/180/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                          } else if (m303 == 'agreed') {
                              html += '<span class="d-none">' + 3 + '</span>';
                              html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                          } else if (m303 == 'disagreed') {
                              html += '<span class="d-none">' + 2 + '</span>';
                              html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                          } else if (m303 == 'warning') {
                              html += '<span class="d-none">' + 1 + '</span>';
                              html += '<a href="/sellers/' + row.shortname + '/model/180/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                          } else if (m303 == 'required') {
                              html += '<span class="d-none">' + 0 + '</span>';
                              html += '<a href="/sellers/' + row.shortname + '/model/180/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                          } else if (m303 == 'not-required') {
                              html += '<span class="d-none">' + 6 + '</span>';
                              html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                          }
                          return html;
                      }
                    },
                    {
                        "data": "model_347", "className": "model", "render": function (data, type, row) {
                            let html = ' ';
                            let m347 = row.model_347;
                            if (m347 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href=""><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                            } else if (m347 == "email-sent-347") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-envelope fa-xl" style="color: #7c7d7e;"></i></a>';
                            } else if (m347 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            } else if (m347 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                            } else if (m347 == 'disagreed') {
                                html += '<span class="d-none">' + 2 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                            } else if (m347 == 'warning') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                            } else if (m347 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            } else if (m347 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            }
                            return html;
                        }
                    }, 
                    {
                        "data": "model_390", "className": "model", "render": function (data, type, row) {
                            let html = ' ';
                            let m390 = row.model_390;
                            if (m390 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href=""><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                            } else if (m390 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            } else if (m390 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                            } else if (m390 == 'disagreed') {
                                html += '<span class="d-none">' + 2 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                            } else if (m390 == 'warning') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                            } else if (m390 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            } else if (m390 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            }
                            return html;

                        }
                    },
                    {
                      "data": "model_184",
                      "className": "model",
                      "render": function(data, type, row) {
                        let html = ' ';
                        let model = data;
                        if (model == "presented") {
                          html += '<span class="d-none">' + 5 + '</span>';
                          html += '<a href=""><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                        } else if (model == "pending") {
                          html += '<span class="d-none">' + 4 + '</span>';
                          html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                        } else if (model == 'agreed') {
                          html += '<span class="d-none">' + 3 + '</span>';
                          html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                        } else if (model == 'disagreed') {
                          html += '<span class="d-none">' + 2 + '</span>';
                          html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                        } else if (model == 'warning') {
                          html += '<span class="d-none">' + 1 + '</span>';
                          html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-triangle-exclamation fa-xl" style="color: #f4c22b;"></i></a>';
                        } else if (model == 'required') {
                          html += '<span class="d-none">' + 0 + '</span>';
                          html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                        } else if (model == 'not-required') {
                          html += '<span class="d-none">' + 6 + '</span>';
                          html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                        }
                        return html;
                      }
                    },
                    
                    {
                        "data": "last_login",
                        "className": "login",
                        "render": function (data, type, row) {
                            if (data && (type === 'display' || type === 'filter')) {
                                const date = new Date(data);

                                const day = date.getDate().toString().padStart(2, '0');
                                const month = date.toLocaleString('default', { month: 'short' });
                                const year = date.getFullYear();
                                const hours = date.getHours().toString().padStart(2, '0');
                                const minutes = date.getMinutes().toString().padStart(2, '0');

                                const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;
                                
                                return formattedDate;
                            }
                            return data; // For other types, like 'sort'
                            }
                    }, 
                    {
                        "data": null, "className": "actions", "orderable": false, "render": function (data, type, row) {
                            let html = '<td class="align-middle text-center">';
                            html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                            html += '<i class="feather icon-edit"></i>';
                            html += '</a>';
                            html += '</td>';
                            html = '<div style="text-align: center;">' + html + '</div>';
                            return html;
                        },
                    }],
                    "paging": true,
                    "searching": true,
                    "lengthChange": false,
                    "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
                    "createdRow": function (row, data, dataIndex) {
                        const shortname = data.shortname;
                        const link = '/sellers/' + shortname + '/';
                        $(row).attr('style', 'cursor: pointer;');
                        $(row).attr('onclick', "window.location.href = '" + link + "';");
                    },
                    "initComplete": function (settings, json) {
                        dataTable.settings()[0].nTBody.style.width = "100%";
                        dataTable.settings()[0].nTable.style.width = "100%";
                        dataTable.settings()[0].nTHead.style.width = "100%";
                        $("#showcolumns").prop("disabled", false);
                        document.getElementById("pending-model-count").textContent = json.pending_model_count; // Actualiza el valor en el HTML
                        document.getElementById("total_invoices_count").textContent = json.total_invoices;
                        document.getElementById("revision-model-count").textContent = json.revision_model_count;
                        document.getElementById("disagreed-model-count").textContent = json.disagreed_model_count;
                        document.getElementById("agreed-model-count").textContent = json.agreed_model_count;
                        document.getElementById("presented-model-count").textContent = json.presented_model_count;

                    },
                    "drawCallback": function (settings) {
                        dataTable.settings()[0].nTable.style.width = "100%";
                    }
            
                });
                resolve();
            });
        }
        loadDataTable().then(() => {
            $("#seller-list-table").show();
        });
    }
    
    function search() {
        const tipo = $("#search").val();
        dataTable.column(0).search(tipo).draw();
    }

    const getTotals = (params) => {
        let p = params;

        if (!p || p === undefined || p === null || p === "") {
            p = "";
        } else if (p.charAt(0) == "&") {
            p[0] = "?";
        }
    }
    const onChangePeriodYear = () => {
        const period = document.getElementById('period');
        const year = document.getElementById('year');
        const urlembed = "{% url 'app_sellers:vat' 'ES' %}";
        const newUrl = urlembed + '?period=' + period.value + '&year=' + year.value;
        window.location.href = newUrl;
    }

    const onload = () => {
        document.getElementById('period').value = '{{period}}';
        document.getElementById('year').value = '{{year}}';
    }

    onload();

</script>
<!-- JQUERY DATATABLES -->
{% endblock javascripts %}
