{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Socio{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_partners:list' seller.shortname  %}">Socios</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">{{object.name}}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        {{ form |crispy }}
        <div class="control-group">
          <div class="controls">
            {% if object.pk is not None %} 
              <button type="submit" class="btn btn-primary">Actualizar</button>
            {% else %}
              <button type="submit" class="btn btn-primary">Guardar</button>
            {% endif %}
          </div>
        </div>
      </form>
    </div>
  </div>
{% endblock content %}
