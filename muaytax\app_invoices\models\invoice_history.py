from django.db import models


class InvoiceHistory(models.Model):

    # id -> AutoGen

    original_invoice_id = models.ForeignKey(
        "invoices.Invoice",
        on_delete=models.CASCADE,
        related_name="original_invoice_id",
        verbose_name="Referencia de factura original"
    )

    copy_invoice_id = models.ForeignKey(
        "invoices.Invoice",
        on_delete=models.CASCADE,
        related_name="copy_invoice_id",
        verbose_name="Referencia de factura copia"
    )

    user_manager = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="invoice_user_manager",
        verbose_name="Usuario gestor",
        limit_choices_to={'role': 'manager'}
    )

    processing_time = models.CharField(
        blank=True, 
        null=True, 
        max_length=1000, 
        verbose_name="Tiempo de contabilización en segundos"
    )

    
    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Historial de Factura"
        verbose_name_plural = "Historial de Facturas"

    def __str__(self):
        if self.id and self.id is not None and self.id != "":
            return str(self.id)
        else:
            return "Invoice History{}".format(self.pk)

