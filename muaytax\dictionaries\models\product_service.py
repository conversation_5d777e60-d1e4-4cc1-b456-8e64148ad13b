from django.db import models

class ProductService(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=100,
        verbose_name="<PERSON>ódigo",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    product_service_type = models.CharField(
        max_length=100,
        verbose_name="Tipo",
    )

    class Meta:
        verbose_name = "Producto o Servicio"
        verbose_name_plural = "Productos o Servicios"
    
    def __str__(self):
        return f"{self.code} | {self.description}"