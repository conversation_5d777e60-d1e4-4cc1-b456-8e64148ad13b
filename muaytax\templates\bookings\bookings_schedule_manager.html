{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Soporte MuayTax
{% endblock title %}

{% block stylesheets %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <style>
    fieldset.form-borders {
      border: 1px groove #838383 !important;
      padding: 0 1.4em 1.4em 1.4em !important;
      margin: 0 0 1.5em 0 !important;
      -webkit-box-shadow:  0px 0px 0px 0px #000;
      box-shadow:  0px 0px 0px 0px #000;
    }
    legend.form-borders {
      text-align: left !important;
      width:inherit; /* Or auto */
      padding:0 10px; /* To give a bit of padding on the left and right */
      border-bottom: none;
      float: unset !important;
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
             Soporte
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_bookings:list_bookings_manager' user.username %}">Mis llamadas</a>
            </li>
            <li class="breadcrumb-item">
              <a href="">Mi Horario</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}


<div class="card">
  <div class="card-body">
    <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
      {% if form.errors %}
        <div class="alert alert-danger" id="errors">
            {{ form.non_field_errors }}
        </div>
      {% endif %}
      {% csrf_token %}
      <fieldset class="form-borders" id="workingHours">
        <legend class="form-borders">Mi horario de recepción de llamadas <i class="fa-solid fa-headset"></i></legend>
        <div class="row">
          <div class="col-md-6">
            <div class="card" style="border: 1px solid rgba(0,0,0,.125);">
              <div class="card-header">
                <h5>Hora de entrada <i class="fa-solid fa-right-to-bracket"></i></h5>
              </div>
              <div class="card-body">
                {{ form.start_time|as_crispy_field }}
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card" style="border: 1px solid rgba(0,0,0,.125);">
              <div class="card-header">
                <h5>Hora de salida <i class="fa-solid fa-right-from-bracket"></i></h5>
              </div>
              <div class="card-body">
                {{ form.end_time|as_crispy_field }}
              </div>
            </div>
          </div>
        </div>
      </fieldset>

      <fieldset class="form-borders">
        <legend class="form-borders">Tiempo de espera entre llamadas </legend>
        <div class="row">
          <div class="col-md-6">
            <div class="card" style="border: 1px solid rgba(0,0,0,.125);">
              <div class="card-header">
                <h5>Tiempo de espera <i class="fa-solid fa-clock"></i></h5>
              </div>
              <div class="card-body">
                {{ form.buffer_time|as_crispy_field }}
              </div>
            </div>
          </div>
        </div>
      </fieldset>

      <fieldset class="form-borders">
        <legend class="form-borders">Mi horario de comida <i class="fa-solid fa-utensils"></i></legend>
        <div id="lunchHours">
          <div class="row">
            <div class="col-md-6">
              <div class="card" style="border: 1px solid rgba(0,0,0,.125);">
                <div class="card-header">
                  <h5>Empieza mi descanso <i class="fa-solid fa-battery-quarter"></i></h5>
                </div>
                <div class="card-body">
                  {{ form.start_break_time|as_crispy_field }}
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card" style="border: 1px solid rgba(0,0,0,.125);">
                <div class="card-header">
                  <h5>Termina mi descanso <i class="fa-solid fa-battery-full"></i></h5>
                </div>
                <div class="card-body">
                  {{ form.end_break_time|as_crispy_field }}
                </div>
              </div>
            </div>
          </div>
          <!-- information message -->
          <div class="alert alert-warning">
            <p>- La hora de comida es máximo 1 hora, y tiene que estar dentro del rango de tu horario de recepción de llamadas</p>
            <p>- Si no deseas tener horario de comida, da click en 'Eliminar horario de comida'</b></p>
          </div>
        </div>
      </fieldset>

      <!-- render a button to add (show) lunch form -->
      <div class="row">
        <div class="col-md-12">
          <button type="button" class="btn btn-outline-primary" id="show_lunch_button"><i class="fa-solid fa-plus"></i> Agregar horario de comida</button>
          <button type="button" class="btn btn-outline-danger" id="hide_lunch_button"><i class="fa-solid fa-minus"></i> Eliminar horario de comida</button>
          {% if object.pk is not None %} 
            <button type="submit" class="btn btn-primary">Actualizar</button>
          {% else %}
            <button type="submit" class="btn btn-primary">Guardar</button>
          {% endif %}
        </div>
      </div>

    </form>
  </div>
</div>

<div class="alert alert-info">
  <p><b>INFORMACIÓN:</b></p>
  <p>- Este es el horario durante el cual los clientes podrán solicitar una llamada telefónica contigo.</p>
  <p>- Si no pones tu<b> horario de la comida</b>, tu horario de recepción de llamadas será <b>ininterrumpido.</b></p>
  <p>- Ten en cuenta que la hora de salida no está incluida en las horas disponibles para el cliente. En otras palabras, la última llamada disponible será programada 30 minutos antes de la hora final.</p>
</div>
{% endblock %}

{% block javascripts %}
  <script>
    const lunchHoursDB = "{{ schedule.start_break_time }}";
    const lunchHours = document.getElementById('lunchHours');
    const showLunchButton = document.getElementById('show_lunch_button');
    const hideLunchButton = document.getElementById('hide_lunch_button');

    $(document).ready(function() {;
      if (lunchHoursDB !== "None" && lunchHoursDB) {
        lunchHours.style.display = 'block';
      } else {
        lunchHours.style.display = 'none';
      }
      toggleShowHideLunchButton();
    });

    function toggleShowHideLunchButton () {
      if (lunchHours.style.display === 'none') {
        showLunchButton.style.display = 'inline';
        hideLunchButton.style.display = 'none';
      } else {
        showLunchButton.style.display = 'none';
        hideLunchButton.style.display = 'inline';
      }
    }

    showLunchButton.addEventListener('click', function() {
      lunchHours.style.display = 'block';
      toggleShowHideLunchButton();
    });

    hideLunchButton.addEventListener('click', function() {
      document.getElementById('id_start_break_time').value = null;
      document.getElementById('id_end_break_time').value = null;
      lunchHours.style.display = 'none';
      toggleShowHideLunchButton();
    });


  </script>
{% endblock javascripts %}