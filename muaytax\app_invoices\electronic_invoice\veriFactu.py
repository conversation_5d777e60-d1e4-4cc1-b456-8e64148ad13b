import json
import qrcode
import tempfile
import string
from io import BytesIO
from django.core.files.base import ContentFile


##### Generar y firmar XML (ODOO) ######
# import base64
import hashlib
import random
# import xmlsig
# import pytz
from datetime import datetime, timezone, timedelta
from lxml import etree
# from cryptography import x509
# from cryptography.hazmat.backends import default_backend
# from cryptography.hazmat.primitives.serialization.pkcs12 import load_key_and_certificates
# from cryptography.hazmat.primitives import serialization
# from cryptography.hazmat.primitives.serialization import Encoding
# from django.core.exceptions import ValidationError
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives.serialization import pkcs12
##### Generar y firmar XML (ODOO) ######

##### Enviar XML a AEAT ######
from requests_pkcs12 import post
from django.conf import settings
##### Enviar XML a AEAT ######

from django.db.models import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ExpressionWrapper
from django.db.models.functions import Round, Coalesce
from django.http import JsonResponse, HttpResponse
from cryptography.hazmat.primitives.serialization import pkcs12
from muaytax.app_sellers.models import Seller, SellerVat
from muaytax.app_invoices.models import Concept, VerifactuInv, Invoice
from muaytax.app_documents.models.document import Document
from muaytax.app_invoices.constants import *
from muaytax.email_notifications.verifactu import *
from muaytax.utils.helpers import clean_nif


class VeriFactu():
    def generate_xml(self, data):

        """
        Función que genera un XML para enviar a la AEAT a partir de un diccionario de datos.
        """

        # Crear el elemento raíz y definir los namespaces
        nsmap = {
            'soapenv' : 'http://schemas.xmlsoap.org/soap/envelope/',
            'sum' : 'https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/tike/cont/ws/SuministroLR.xsd',
            'sum1' : 'https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/tike/cont/ws/SuministroInformacion.xsd',
            'xd' : 'http://www.w3.org/2000/09/xmldsig#',
        }

        root = etree.Element(etree.QName(nsmap['soapenv'], 'Envelope'), nsmap=nsmap)


        # Crear el Header y Body
        header = etree.SubElement(root, etree.QName(nsmap['soapenv'], 'Header'))
        body = etree.SubElement(root, etree.QName(nsmap['soapenv'], 'Body'))
        
        reg_sistemaFacturacion = etree.SubElement(body, etree.QName(nsmap['sum'], 'RegFactuSistemaFacturacion'))
        
        ##### CABECERA #####
        cabecera = etree.SubElement(reg_sistemaFacturacion, etree.QName(nsmap['sum'], 'Cabecera'))
        
        #  Node ObligadoEmision
        obEmision = etree.SubElement(cabecera, etree.QName(nsmap['sum1'], 'ObligadoEmision'))

        nombreRazon = etree.SubElement(obEmision, etree.QName(nsmap['sum1'], 'NombreRazon'))
        nombreRazon.text = f"{data.get('seller')}"
        
        nif = etree.SubElement(obEmision, etree.QName(nsmap['sum1'], 'NIF'))
        nif.text = f"{data.get('nif')}"


        # NODE Requerimiento
        if data.get('isRequeriment'):
            remRequerimiento = etree.SubElement(cabecera, etree.QName(nsmap['sum1'], 'RemisionRequerimiento'))

            refRequerimiento = etree.SubElement(remRequerimiento, etree.QName(nsmap['sum1'], 'RefRequerimiento'))
            refRequerimiento.text = f"{data.get('RefRequerimiento')}"


        ##### REGISTRO FACTURA #####
        regFactura = etree.SubElement(reg_sistemaFacturacion, etree.QName(nsmap['sum'], 'RegistroFactura'))

        # NODE RegistroAlta/Anulacion (alta o anulación de la factura)
        tipReg = f"{data.get('tipRegFactura')}"
        tipReg = etree.SubElement(regFactura, etree.QName(nsmap['sum1'], f'{tipReg}'))
        
        idVer = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'IDVersion'))
        idVer.text = '1.0'

        # --- SUBNODE IDFactura ---
        idFactura = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'IDFactura'))

        idEmisorFactura = etree.SubElement(idFactura, etree.QName(nsmap['sum1'], f'IDEmisorFactura{data.get("sufixTipReg")}'))
        idEmisorFactura.text = f"{data.get('IDEmisorFactura')}"

        numSerieFact = etree.SubElement(idFactura, etree.QName(nsmap['sum1'], f'NumSerieFactura{data.get("sufixTipReg")}'))
        numSerieFact.text = f"{data.get('NumSerieFactura')}"

        fechaExp = etree.SubElement(idFactura, etree.QName(nsmap['sum1'], f'FechaExpedicionFactura{data.get("sufixTipReg")}'))
        fechaExp.text = f"{data.get('FechaExpedicionFacturaEmisor')}"
        # --- SUBNODE IDFactura ---

        ######NODOS DE REGISTRO DE ALTA######
        if data.get('isCancel') == False:
            nomRazEmisor = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'NombreRazonEmisor'))
            nomRazEmisor.text = f"{data.get('seller')}"

            if data.get('isCorrection'):
                subsanacion = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'Subsanacion'))
                subsanacion.text = f"{data.get('Subsanacion')}"

            tipFactura = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'TipoFactura'))
            tipFactura.text = f"{data.get('TipoFactura')}"

            if data.get('isRectification'):

                tipRect = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'TipoRectificativa'))
                tipRect.text = f"{data.get('TipoRectificativa')}"

                factRect = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'FacturasRectificadas'))
                
                idFactRect = etree.SubElement(factRect, etree.QName(nsmap['sum1'], 'IDFacturaRectificada'))

                idEmisorFacturaRect = etree.SubElement(idFactRect, etree.QName(nsmap['sum1'], 'IDEmisorFactura'))
                idEmisorFacturaRect.text = f"{data.get('IDEmisorFacturaRectificada')}"

                numSerieFactRect = etree.SubElement(idFactRect, etree.QName(nsmap['sum1'], 'NumSerieFactura'))
                numSerieFactRect.text = f"{data.get('NumSerieFacturaRectificada')}"

                fechaExpFactRect = etree.SubElement(idFactRect, etree.QName(nsmap['sum1'], 'FechaExpedicionFactura'))
                fechaExpFactRect.text = f"{data.get('FechaExpedicionFacturaRectificada')}"

            descOP = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'DescripcionOperacion'))
            descOP.text = f"{data.get('DescripcionOperacion')}"

            # --- SUBNODE Destinatarios --- 
            if data.get('NifDestinatario') != '':
                destinatarios = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'Destinatarios'))
                idDestinatario = etree.SubElement(destinatarios, etree.QName(nsmap['sum1'], 'IDDestinatario'))

                desNomRaz = etree.SubElement(idDestinatario, etree.QName(nsmap['sum1'], 'NombreRazon'))
                desNomRaz.text = f"{data.get('NombreRazonDestinatario')}"
                if data.get('isForeignNIF') is False:
                    desNif = etree.SubElement(idDestinatario, etree.QName(nsmap['sum1'], 'NIF'))
                    desNif.text = f"{data.get('NifDestinatario')}"
                else:
                    idOtro = etree.SubElement(idDestinatario, etree.QName(nsmap['sum1'], 'IDOtro'))

                    codPais = etree.SubElement(idOtro, etree.QName(nsmap['sum1'], 'CodigoPais'))
                    codPais.text = f"{data.get('CodigoPaisDestinatario')}"

                    idType = etree.SubElement(idOtro, etree.QName(nsmap['sum1'], 'IDType'))
                    idType.text = f"{data.get('IDTypeDestinatario')}"

                    idNif = etree.SubElement(idOtro, etree.QName(nsmap['sum1'], 'ID'))
                    idNif.text = f"{data.get('NifDestinatario')}"

            # --- SUBNODE Destinatarios ---

            # --- SUBNODE Desglose ---
            desglose = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'Desglose'))
            
            # --- lINEAS DE DETALLE DEL DESGLOSE (CONCEPTOS) ---
            for con in data.get('concepts'):
                detalleDesglose = etree.SubElement(desglose, etree.QName(nsmap['sum1'], 'DetalleDesglose'))

                claveRegimen = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'ClaveRegimen'))
                claveRegimen.text = f"{data.get('ClaveRegimen')}"

                calificacionOperacion = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'CalificacionOperacion'))
                calificacionOperacion.text = f"{data.get('CalificacionOperacion')}"

                tipoImpositivo = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'TipoImpositivo'))
                tipoImpositivo.text = f"{con.vat:.2f}"

                baseImponible = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'BaseImponibleOimporteNoSujeto'))
                baseImponible.text = f"{con.total_amount:.2f}"

                cuotaRepercutica = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'CuotaRepercutida'))
                cuotaRepercutica.text = f"{con.tax_amount:.2f}"
                if con.eqtax > 0:
                    tipoRecargo = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'TipoRecargo'))
                    tipoRecargo.text = f"{con.eqtax:.2f}"

                    cuotaRecargo = etree.SubElement(detalleDesglose, etree.QName(nsmap['sum1'], 'CuotaRecargo'))
                    cuotaRecargo.text = f"{con.eqtax_amount:.2f}"
            # --- SUBNODE Desglose ---

            cuotaTotal = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'CuotaTotal'))
            cuotaTotal.text = f"{data.get('CuotaTotal')}"

            importeTotal = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'ImporteTotal'))
            importeTotal.text = f"{data.get('ImporteTotal')}"

        # --- SUBNODE Encadenamiento ---
        encadenamiento = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'Encadenamiento'))

        #Solo si existe un registro anterior de facturas de este seller 
        if data.get('RegistroAnterior'):
            registroAnterior = etree.SubElement(encadenamiento, etree.QName(nsmap['sum1'], 'RegistroAnterior'))

            idEmisorFacturaRegAnt = etree.SubElement(registroAnterior, etree.QName(nsmap['sum1'], 'IDEmisorFactura'))
            idEmisorFacturaRegAnt.text = f"{data.get('idEmisorFacturaRegAnt')}"

            numSerieFactRegAnt = etree.SubElement(registroAnterior, etree.QName(nsmap['sum1'], 'NumSerieFactura'))
            numSerieFactRegAnt.text = f"{data.get('NumSerieFacturaRegAnt')}"

            fechaExpRegAnt = etree.SubElement(registroAnterior, etree.QName(nsmap['sum1'], 'FechaExpedicionFactura'))
            fechaExpRegAnt.text = f"{data.get('FechaExpedicionFacturaRegAnt')}"

            huellaRegAnt = etree.SubElement(registroAnterior, etree.QName(nsmap['sum1'], 'Huella'))
            huellaRegAnt.text = f"{data.get('HuellaRegistroAnterior')}"
        else:
            primerRegistro = etree.SubElement(encadenamiento, etree.QName(nsmap['sum1'], 'PrimerRegistro'))
            primerRegistro.text = f"{data.get('PrimerRegistro')}"

        # --- SUBNODE Encadenamiento ---


        # --- SUBNODE Sistema Informatico ---
        sisInformatico = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'SistemaInformatico'))
        
        nombreRazonSisInf = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'NombreRazon'))
        nombreRazonSisInf.text = f"{data.get('NombreRazonSisInf')}"

        nifSisInf = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'NIF'))
        nifSisInf.text = f"{data.get('NIFSisInf')}"

        nombreSisInf = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'NombreSistemaInformatico'))
        nombreSisInf.text = f"{data.get('NombreSistemaInformatico')}"

        idSisInf = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'IdSistemaInformatico'))
        idSisInf.text = f"{data.get('IdSistemaInformatico')}"

        versionSisInf = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'Version'))
        versionSisInf.text = f"{data.get('versionSisInf')}"

        numInstalacion = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'NumeroInstalacion'))
        numInstalacion.text = f"{data.get('NumeroInstalacion')}"

        tipoUsoSoloVerifactu = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'TipoUsoPosibleSoloVerifactu'))
        tipoUsoSoloVerifactu.text = f"{data.get('TipoUsoPosibleSoloVerifactu')}"

        tipoUsoMultiOT = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'TipoUsoPosibleMultiOT'))
        tipoUsoMultiOT.text = f"{data.get('TipoUsoPosibleMultiOT')}"

        indicadorMultiplesOT = etree.SubElement(sisInformatico, etree.QName(nsmap['sum1'], 'IndicadorMultiplesOT'))
        indicadorMultiplesOT.text = f"{data.get('IndicadorMultiplesOT')}"
        # --- SUBNODE Sistema Informatico ---


        fechaHusoHorario = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'FechaHoraHusoGenRegistro'))
        fechaHusoHorario.text = f"{data.get('FechaHoraHusoGenRegistro')}"

        tipoHuella = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'TipoHuella'))
        tipoHuella.text = f"{data.get('TipoHuella')}"

        huella = etree.SubElement(tipReg, etree.QName(nsmap['sum1'], 'Huella'))
        huella.text = f"{data.get('Huella')}"
        
        # Devolver el XML generado
        xml_content = etree.tostring(root, pretty_print=True, xml_declaration=True, encoding='UTF-8')
        return xml_content

    def generate_xml_check_record_aeat(self, data):
        '''
        Función que genera un xml para comprobar un registro existente en la AEAT
        '''

        # Crear el elemento raíz y definir los namespaces

        nsmap = {
            'soapenv' : 'http://schemas.xmlsoap.org/soap/envelope/',
            'con' : 'https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/tike/cont/ws/ConsultaLR.xsd',
            'sum' : 'https://www2.agenciatributaria.gob.es/static_files/common/internet/dep/aplicaciones/es/aeat/tike/cont/ws/SuministroInformacion.xsd',
        }

        root = etree.Element(etree.QName(nsmap['soapenv'], 'Envelope'), nsmap=nsmap)

        # Crear el Header y Body
        header = etree.SubElement(root, etree.QName(nsmap['soapenv'], 'Header'))
        body = etree.SubElement(root, etree.QName(nsmap['soapenv'], 'Body'))

        consulta = etree.SubElement(body, etree.QName(nsmap['con'], 'ConsultaFactuSistemaFacturacion'))
        
        #NODO Cabecera
        cabecera = etree.SubElement(consulta, etree.QName(nsmap['con'], 'Cabecera'))

        idVersion = etree.SubElement(cabecera, etree.QName(nsmap['sum'], 'IDVersion'))
        idVersion.text = '1.0'

        obligadoEmision = etree.SubElement(cabecera, etree.QName(nsmap['sum'], 'ObligadoEmision'))

        nomRazonEmisor = etree.SubElement(obligadoEmision, etree.QName(nsmap['sum'], 'NombreRazon'))
        nomRazonEmisor.text = f"{data.get('seller')}"

        nifEmisor = etree.SubElement(obligadoEmision, etree.QName(nsmap['sum'], 'NIF'))
        nifEmisor.text = f"{data.get('nif')}"

        #NODO FiltroConsulta
        filtroConsulta = etree.SubElement(consulta, etree.QName(nsmap['con'], 'FiltroConsulta'))

        periodoImputacion = etree.SubElement(filtroConsulta, etree.QName(nsmap['con'], 'PeriodoImputacion'))

        ejercicio = etree.SubElement(periodoImputacion, etree.QName(nsmap['sum'], 'Ejercicio'))
        ejercicio.text = f"{data.get('FechaExpedicionFacturaEmisor')[-4:]}"

        periodo = etree.SubElement(periodoImputacion, etree.QName(nsmap['sum'], 'Periodo'))
        periodo.text = f"{data.get('FechaExpedicionFacturaEmisor')[3:5]}"

        numSerieFactura = etree.SubElement(filtroConsulta, etree.QName(nsmap['con'], 'NumSerieFactura'))
        numSerieFactura.text = f"{data.get('NumSerieFactura')}"

        if data.get('NifDestinatario') != '':
            contraparte = etree.SubElement(filtroConsulta, etree.QName(nsmap['con'], 'Contraparte'))
            nomRazonContraparte = etree.SubElement(contraparte, etree.QName(nsmap['sum'], 'NombreRazon'))
            nomRazonContraparte.text = f"{data.get('NombreRazonDestinatario')}"
            if data.get('isForeignNIF') is False:
                nifContraparte = etree.SubElement(contraparte, etree.QName(nsmap['sum'], 'NIF'))
                nifContraparte.text = f"{data.get('NifDestinatario')}"
            else:
                idOtro = etree.SubElement(contraparte, etree.QName(nsmap['sum'], 'IDOtro'))

                codPais = etree.SubElement(idOtro, etree.QName(nsmap['sum'], 'CodigoPais'))
                codPais.text = f"{data.get('CodigoPaisDestinatario')}"

                idType = etree.SubElement(idOtro, etree.QName(nsmap['sum'], 'IDType'))
                idType.text = f"{data.get('IDTypeDestinatario')}"

                idNif = etree.SubElement(idOtro, etree.QName(nsmap['sum'], 'ID'))
                idNif.text = f"{data.get('NifDestinatario')}"

        fechaExpedicionFactura = etree.SubElement(filtroConsulta, etree.QName(nsmap['con'], 'FechaExpedicionFactura'))
        fechaExpedicionFactura.text = f"{data.get('FechaExpedicionFacturaEmisor')}"


        # Devolver el XML generado
        xml_content = etree.tostring(root, pretty_print=True, xml_declaration=True, encoding='UTF-8')
        return xml_content

    def data_invoice_xml(self, seller, invoice, jsonArgs={}, RefRequeriment=None):
        """
        Función para crear un diccionario de datos a partir de una factura que usaremos en la generacion del XML de envio.
        """

        ########## QUERYS PREVIOS Y CONSTRUCCIÓN DE DATOS ##########
        sellerVatES = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
        verify_cert, cert_path, cert_password, cert = verify_cert_digital(seller)

        # Datos del emisor
        sellerName = f"{seller.first_name} {seller.last_name}" if seller.legal_entity == 'self-employed' else seller.name
        sellerNif = sellerVatES.vat_number.replace("ES", '') if sellerVatES and sellerVatES.vat_number is not None and sellerVatES.vat_number != ''  else seller.nif_registration 
        if verify_cert:
            info_cert = get_info_issuer_certificate(seller, cert)
            if info_cert:
                sellerName = info_cert.get('name') if info_cert.get('name') is not None and info_cert.get('name') != '' and not info_cert.get('is_muaytax') else sellerName

        fecha_hora = datetime.now(timezone(timedelta(hours=1))).strftime('%Y-%m-%dT%H:%M:%S%z')
        fecha_hora = fecha_hora[:-2] + ':' + fecha_hora[-2:]


        # Obtener registro anterior, si existe
        lastInv = VerifactuInv.objects.filter(seller=seller, status_in_verifactu__in=['Correcto', 'AceptadoConErrores'] ).order_by('-created_at').first()
        lastInvJSON = None
        regAnterior = True if lastInv and lastInv.json_data != '' and lastInv.json_data is not None and lastInv.json_data != {} else False
        if regAnterior:
            lastInvJSON = json.loads(lastInv.json_data)
        

        # Comprobar si es una correccion buscando si esa factura se ha enviado previamente a verifactu
        existingInv = VerifactuInv.objects.filter(seller=seller, invoice=invoice, status_in_verifactu__in=['Correcto', 'AceptadoConErrores'] ).order_by('-created_at').first()
        isCorrection = False
        if existingInv and existingInv.invoice_id == invoice.id:
            isCorrection = True

        # Comprobar si es una rectificativa y obtener la factura a rectificar
        isRectification = False
        tipeRectification = "R1"
        if invoice.is_rectifying and invoice.rectifying_invoices:
            invRect = Invoice.objects.filter(pk=invoice.rectifying_invoices.pk).first()
            isRectification = True
            tipeRectification = "R5" if invRect.customer.nif_cif_iva is None or invRect.customer.nif_cif_iva == "" else "R1"

        # Comprobar datos del cliente
        isForeignNIF = False
        customerNif = invoice.customer.nif_cif_iva if invoice.customer.nif_cif_iva is not None and invoice.customer.nif_cif_iva != "" else ""
        customerNif= clean_nif(customerNif)
        if invoice.customer and invoice.customer.country and invoice.customer.country.iso_code != 'ES':
            isForeignNIF = True

        # TODO: Modulo planteado para el envio de requerimientos a AEAT, pero no implementado. Según la documentación, la referencia del requerimiento nos la da la AEAT, habría que pasarla a la función
        # Comprobar si es un requerimiento
        isRequeriment = False
        if RefRequeriment and RefRequeriment != '':
            isRequeriment = True

        # CALCS FROM CONCEPTS
        concepts = Concept.objects.filter(invoice_id=invoice.id).annotate(
            total_amount=Round(
                ExpressionWrapper(
                    Coalesce(F('amount_euros'), 0.00) * Coalesce(F('quantity'), 0.00),
                    output_field=FloatField()
                ), 2
            ),
            tax_amount=Round(
                ExpressionWrapper(
                    Coalesce(F('amount_euros'), 0.00) * Coalesce(F('quantity'), 0.00) * Coalesce(F('vat'), 0.00) / 100,
                    output_field=FloatField()
                ), 2
            ),
            eqtax_amount=Round(
                ExpressionWrapper(
                    Coalesce(F('amount_euros'), 0.00) * Coalesce(F('quantity'), 0.00) * Coalesce(F('eqtax'), 0.00) / 100,
                    output_field=FloatField()
                ), 2
            )
        )

        total_calcs = concepts.aggregate(
            total_tax_and_eqtax = Sum('tax_amount') + Sum('eqtax_amount'),
            invoice_total = Sum('total_amount') + Sum('tax_amount') + Sum('eqtax_amount')
        )


        #BUILD BASE DATA JSON
        data = {
            "seller": sellerName,
            "nif": sellerNif,
            "IDEmisorFactura": sellerNif,
            "NumSerieFactura": f"{jsonArgs.get('invoiceReference', None)}" if jsonArgs.get('invoiceReference', None) and jsonArgs.get('invoiceReference', None) != '' else f"{invoice.reference}",
            "FechaExpedicionFacturaEmisor": invoice.expedition_date.strftime("%d-%m-%Y"),
            "TipoFactura": tipeRectification if isRectification == True else "F1" if invoice.customer.nif_cif_iva is not None and invoice.customer.nif_cif_iva !="" else "F2",
            "DescripcionOperacion": invoice.iae.description,

            ####### DATOS DEL CLIENTE #######
            "NombreRazonDestinatario": invoice.customer.name,
            "NifDestinatario": customerNif,
            "CodigoPaisDestinatario": invoice.customer.country.iso_code if invoice.customer.nif_cif_iva is not None and invoice.customer.nif_cif_iva !="" else "",
            "IDTypeDestinatario": "02" if invoice.customer.nif_cif_iva is not None and invoice.customer.nif_cif_iva =="ES" else "04",
            "isForeignNIF": isForeignNIF,
            
            ####### DATOS DEL TIPO DE REGISTRO DE LA FACTURA #######
            "tipRegFactura": "RegistroAnulacion" if jsonArgs.get("isCancel", False) else "RegistroAlta",
            "sufixTipReg": "Anulada" if jsonArgs.get("isCancel", False) else "",
            "isCancel": jsonArgs.get("isCancel", False),

            ####### REQUERIMIENTO #######
            "isRequeriment": isRequeriment,
            "RefRequerimiento": RefRequeriment if isRequeriment else "",

            ####### CORRECION Y SUBSANACION DE ERRORES #######
            "isCorrection": isCorrection,
            "Subsanacion" : "S",

            ####### DATOS FACTURA RECTIFICATIVA #######
            "isRectification": isRectification,
            "TipoRectificativa": "I",
            "IDEmisorFacturaRectificada": sellerNif,
            "NumSerieFacturaRectificada": f"{invRect.reference}" if isRectification else "",
            "FechaExpedicionFacturaRectificada": invRect.expedition_date.strftime("%d-%m-%Y") if isRectification else "",

            ####### INVOICE CALCS #######
            "ClaveRegimen": "01",
            "CalificacionOperacion": "S1",
            "concepts": concepts,
            "CuotaTotal": total_calcs["total_tax_and_eqtax"],
            "ImporteTotal": total_calcs["invoice_total"],

            ####### DATOS ENCADENAMIENTO (DATOS DE LA ÚLTIMA FACTURA ENVIADA) #######
            "PrimerRegistro": "S",
            "RegistroAnterior": regAnterior,
            "idEmisorFacturaRegAnt": lastInvJSON.get('IDEmisorFactura') if regAnterior else "",
            "NumSerieFacturaRegAnt": lastInvJSON.get('NumSerieFactura') if regAnterior else "",
            "FechaExpedicionFacturaRegAnt": lastInvJSON.get('FechaExpedicionFacturaEmisor') if regAnterior else "",
            "HuellaRegistroAnterior": lastInvJSON.get('Huella') if regAnterior else "",

            ####### DATOS SISTEMA INFORMATICO #######
            "NombreRazonSisInf": "MUAYTAX ADVISORS SL",
            "NIFSisInf": "B67659607",
            "NombreSistemaInformatico": "APP MUAYTAX",
            "IdSistemaInformatico": "M1",
            "versionSisInf": "1.0",
            "NumeroInstalacion": "SIF_0014bad21af2fc91662e24f58a2f013ea9567cc5b96406ff7d153bcdcb67daf5f5d",  # SIF_001 + HASH (256) del "NombreRazonSisInf" --> "MUAYTAX ADVISORS SL"
            "TipoUsoPosibleSoloVerifactu": "S",
            "TipoUsoPosibleMultiOT": "S",
            "IndicadorMultiplesOT": "S",
            
            ####### DATOS FECHA Y HUELLA #######
            "FechaHoraHusoGenRegistro": fecha_hora,
            "TipoHuella": "01",
            "Huella": "",
        }

        hash = self.calculate_hash(data, lastInvJSON, jsonArgs.get("isCancel", False))

        data['Huella'] = hash

        return data
    
    def calculate_hash(self, data, lastInvJSON, isCancel=False):
        """
        Función para calcular la huella de un registro de factura encriptada en SHA256 a partir de un diccionario de datos.
        """

        lastHash = lastInvJSON.get('Huella') if lastInvJSON else ""
        
        if isCancel:
            char = f"IDEmisorFacturaAnulada={data.get('IDEmisorFactura')}&NumSerieFacturaAnulada={data.get('NumSerieFactura')}&FechaExpedicionFacturaAnulada={data.get('FechaExpedicionFacturaEmisor')}"
            char += f"&Huella={lastHash}&FechaHoraHusoGenRegistro={data.get('FechaHoraHusoGenRegistro')}"
        else:
            char = f"IDEmisorFactura={data.get('IDEmisorFactura')}&NumSerieFactura={data.get('NumSerieFactura')}&FechaExpedicionFactura={data.get('FechaExpedicionFacturaEmisor')}"
            char += f"&TipoFactura={data.get('TipoFactura')}&CuotaTotal={data.get('CuotaTotal')}&ImporteTotal={data.get('ImporteTotal')}&Huella={lastHash}"
            char += f"&FechaHoraHusoGenRegistro={data.get('FechaHoraHusoGenRegistro')}"
    
        return hashlib.sha256(char.encode()).hexdigest().upper()
    
    def generate_QR_code(self, data):
        """
        Función que genera un código QR a partir de un diccionario de datos
        y lo retorna como un archivo en memoria.
        """
        
        # Crear el código QR
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )

        # Crear el string de datos
        qr_data = '&'.join([f"{key}={value}" for key, value in data.items()]).replace("URL=", "")
        
        qr.add_data(qr_data)
        qr.make(fit=True)

        # Crear la imagen
        img = qr.make_image(fill_color="black", back_color="white")

        # Guardar la imagen en memoria en lugar de en disco
        img_io = BytesIO()
        img.save(img_io, format="PNG")
        img_io.seek(0)

        return ContentFile(img_io.getvalue(), name="qr_code.png")

########## FUNCIONES PARA EL ENVIO A LA AEAT TRATAMIENTO DE DATOS Y VALIDACIONES ##########
def send_xml_VeriFactu_to_AEAT(xml, cert_path, cert_password):
    """
    Función que envía un XML a Verifactu a través de una petición POST con un certificado digital.    
    """
    response = post(
        settings.VERIFACTU_SOAP_URL,
        data=xml,
        pkcs12_filename=cert_path,
        pkcs12_password=cert_password,
        headers={
            'Content-Type': 'application/xml',
        },
        verify=settings.PATH_VERIFY_CA
    )

    return response

def xml_to_dict(element):
    """
    Convierte un elemento XML en un diccionario recursivamente, limpiando los espacios de nombres.
    """
    def clean_tag(tag):
        """
        Limpia los espacios de nombres en los nombres de las etiquetas XML.
        """
        return tag.split('}')[1] if '}' in tag else tag

    if len(element) == 0:
        return element.text
    else:
        result = {}
        for child in element:
            child_dict = xml_to_dict(child)
            tag_name = clean_tag(child.tag)  # Limpiar el nombre de la etiqueta
            # Si la etiqueta ya existe, hacer una lista
            if tag_name in result:
                if isinstance(result[tag_name], list):
                    result[tag_name].append(child_dict)
                else:
                    result[tag_name] = [result[tag_name], child_dict]
            else:
                result[tag_name] = child_dict
        return result

def create_verifactu_instance(invoice, json_data, xml_from_aeat= "", status="no_info"):
    """
    Función que crea una instancia de VerifactuInv en memoria a partir de los datos de una factura y la respuesta de Verifactu.
    """

    if json_data:
        concepts = list(json_data["concepts"].values())  # Convierte el QuerySet de conceptos en una lista
        for concept in concepts:
            concept.pop("created_at", None)  
            concept.pop("modified_at", None)  
        json_data["concepts"] = concepts
    
    json_data = json.dumps(json_data)

    # Crear una instancia de VerifactuInv en memoria, sin guardarla en la base de datos
    inv_verifactu = VerifactuInv(
        invoice=invoice,
        seller=invoice.seller,
        status_in_verifactu=status,
        json_data=json_data,
        response_xml_aeat=xml_from_aeat
    )

    return inv_verifactu

def data_dic_for_QR(data):
    """
    Función que genera un diccionario de datos para el código QR a partir de un diccionario de datos de factura.
    """

    data = json.loads(data) if not isinstance(data, dict) else data

    qr_data = {
        "URL": settings.VERIFACTU_QR_VALIDATOR_URL,
        "nif": data.get('nif'),
        "numserie": data.get('NumSerieFactura'),
        "fecha": data.get('FechaExpedicionFacturaEmisor'),
        "importe": data.get('ImporteTotal'),
    }

    return qr_data

def get_info_issuer_certificate(seller, cert):
    """
    Función que obtiene la información del certificado digital de un seller.
    """

    """
    INFORMACIÓN DE LOS OID (identificadores únicos) DEL CERTIFICADO DIGITAL
    - ********: descriptción que contiene información descriptiva sobre el sujeto del certificado. 
    - *******: serialNumber, Este atributo representa el número de serie del certificado y sirve para identificar de forma única un certificado.
    - ********: commonName, Nombre de la persona física (como consta en el DNI/NIE).
    - *******: surname, Apellidos de la persona física (como consta en el DNI/NIE)
    - *******: commomName,  titular del certificado
    - ********: organizationalIdentifier, contiene un identificador único de la entidad a la que pertenece el titular del certificado, como el NIF, CIF o código oficial de registro
    - ********: organizationName, nombre de la organización a la que pertenece el titular del certificado.
    - *******: countryName, nombre del país al que pertenece el titular del certificado.
    - ********: title, título o cargo del titular del certificado.

    ### NOTA: ###
    # Para obtener el NIF/CIF tomar los 9 ultimos carácteres de los siguientes elementos si es una empresa de ******** y si es un autónomo de ******* 
    # Para obtener el nombre del vendedor, si es un autónomo tomar  ******** (nombre) ******* (apellidos) y si es una empresa tomar ********
    """

    dict_info = None
    oid_info = {}

    if cert:
        # Obtener la información del certificado
        subject = cert.subject
        
        for attribute in subject:
            oid_info.update({attribute.oid.dotted_string: attribute.value})

        dict_info = {
            "oid_info": oid_info,
            "nif": oid_info.get("*******", "")[-9:] if seller.legal_entity == 'self-employed' else oid_info.get("********", "")[-9:] ,
            "name": f'{oid_info.get("*******", " ")} {oid_info.get("********", " ")}' if seller.legal_entity == 'self-employed' else oid_info.get("********", " "),
            "is_muaytax": True if oid_info.get("********", "")[-9:] == "B67659607" else False,
        }

    return dict_info
    
def verify_cert_digital(seller):
    """
    Función que busca el certificado digital de un seller, trata de leerlo a partir del path y la contraseña, y lo devuelve en caso de encontrarlo y poder ser leído.
    """

    certificateFile = Document.objects.filter(documentType='ES-SIGNATURE', seller=seller).first()
    cert_path = certificateFile.file.path if certificateFile else None
    cert_password = certificateFile.get_password() if certificateFile else None

    if cert_path and cert_password:
        try:
            with open(cert_path, "rb") as cert_file:
                cert_data = cert_file.read()
            
            private_key, certificate, additional_certs = pkcs12.load_key_and_certificates(
                cert_data, cert_password.encode() if cert_password else None
            )

            if certificate:
                return True, cert_path, cert_password, certificate

        except Exception as e:
            print(f"Error al verificar el certificado: {e}")
    return False, None, None, None

def validations_issuer_info_cert(certificate, sellerNif, seller=None):
    """
    Función que comprueba la información del emisor a partir de un certificado digital.
    Se comprueba si el certificado está caducado
    Si el certificado es de un representante, si es así este tiene poder para actuar en nombre del vendedor.
    Si el certificado no es de un representante o empoderado, se comprueba que el NIF del vendedor coincide con el del certificado.
    """

    is_representative = False
    same_nif = False
    is_expirated = False
    cert = certificate
    
    if certificate:

        # Comprobar si el certificado está caducado
        if cert.not_valid_after_utc.replace(tzinfo=None) < datetime.now():
            is_expirated = True
        
        issuer_info = get_info_issuer_certificate(seller, cert)


        if issuer_info:
            # Obtener el tipo de titular del certificado 
            is_representative = issuer_info.get("is_muaytax", False)
            
            # Si el certificado no es de un representante (en nuestro caso por defecto el nif de muaytax), comprobar que el NIF del certificado coincide con el del vendedor
            if not is_representative:
                same_nif = True if issuer_info.get("nif", "") == sellerNif else False

    return is_representative, same_nif, is_expirated

def response_from_VeriFactu(data, response_dict, invoice, veriFactu, response, args):
    """
    Función que procesa la respuesta de Verifactu y guarda los datos en la base de datos.
    """

    request_status = response_dict.get('RespuestaRegFactuSistemaFacturacion').get('EstadoEnvio')
    if request_status in ['Correcto', 'ParcialmenteCorrecto']:

        # Creamos el codigo QR y lo guardamos en la factura (en el caso de que no sea una anulación)
        if not args.get('isCancel', False):
            dataQR = data_dic_for_QR(data)
            qrCode = veriFactu.generate_QR_code(dataQR)
            invoice.qr_code_verifactu.save(f"QR_CODE_{data.get('NumSerieFactura')}_{invoice.seller.name}_{''.join(random.choices(string.ascii_letters + string.digits, k=4))}.png", qrCode)

        else:
            invoice.qr_code_verifactu = None
        invoice.is_manager_cancel_sending_to_verifactu = False
        invoice.save()

        # Crear una instancia de VeriFactu EN MEMORIA
        verifactuInstance = create_verifactu_instance(invoice, data)
        verifactuInstance.status_in_verifactu = response_dict.get('RespuestaRegFactuSistemaFacturacion').get('RespuestaLinea').get('EstadoRegistro')
        verifactuInstance.operation_type = response_dict.get('RespuestaRegFactuSistemaFacturacion').get('RespuestaLinea').get('Operacion').get('TipoOperacion')
        
        # Guardar la instancia de VeriFactu en la BBDD siempre que no sea un comprobación de registro en la AEAT y no haya habido fallos en la formación del XML
        verifactuInstance.response_xml_aeat = response.content.decode("utf-8")
        verifactuInstance.save()

        # Email de rastreo a IT que indica que se ha enviado la factura a Verifactu
        try:
            send_tracing_emailVeriFactu_to_IT(invoice, "Factura registrada en la AEAT", response.content)
        except Exception as e:
            print(f"Error al enviar el email de rastreo a IT: {e}")

        
    return response_dict

def send_invoice_toVeriFactu_auto(invoice, kwargs={}):
    """
    Función que envía una factura a Verifactu automáticamente.
    """

    invoice = Invoice.objects.filter(id=invoice).first()
    try:
        if invoice:

            # Inicializamos la clase VeriFactu
            veriFactu = VeriFactu()
            data = veriFactu.data_invoice_xml(invoice.seller, invoice, kwargs)

            response_dict = {}
            error_message = None

            file_name = f"facturaVeriFactu.xml"
            # Generamos el XML en un directorio temporal
            with tempfile.TemporaryDirectory() as temp_dir:
                xml_content = veriFactu.generate_xml(data)
                file_path = f"{temp_dir}/archivo_temporal.xml"

                #Escribir el XML en el archivo temporal
                with open(file_path, 'wb') as file:
                    file.write(xml_content)
                
                with open(file_path, "r", encoding="utf-8") as file:
                        request = file.read()

                response = HttpResponse(request, content_type='application/xml')
                response['Content-Disposition'] = f'attachment; filename="{file_name}"'

                # Verificar el certificado digital
                verify_cert, cert_path, cert_password, cert = verify_cert_digital(invoice.seller)

                if not verify_cert: 
                    error_message = "El certificado digital no es válido o no existe."
                else:
                    is_representative, same_nif, is_expirated = validations_issuer_info_cert(cert, data.get('nif'), invoice.seller)
                    error_message = "El certificado digital está caducado." if is_expirated else "El certificado de firma digital no coincide con el NIF del obligado tributario." if not same_nif and not is_representative else None
                if error_message:
                    return send_error_emailVeriFactu_to_IT(invoice, {"ERROR": error_message}) 
                
                # print(response.content.decode("utf-8"))

                # Email de rastreo a IT que indica que se ha enviado la factura a Verifactu
                try:
                    send_tracing_emailVeriFactu_to_IT(invoice, "Envío" )
                except Exception as e:
                    print(f"Error al enviar el email de rastreo a IT: {e}")

                # Enviar el XML a la AEAT
                response = send_xml_VeriFactu_to_AEAT(response, cert_path, cert_password)


                if response.ok:
                    fault = None

                    try:
                        # Parsear el XML de la respuesta
                        root = etree.fromstring(response.content)

                        # Convertir el XML en un diccionario
                        response_dict = xml_to_dict(root)

                        # Comprobar si hay un error en la formación del XML
                        fault = response_dict.get('Body', {}).get('Fault',{})
                        if fault:
                            error_message = fault.get('detail').get('callstack').split("\n")[0]
                            response_dict = {"ERROR": error_message}
                        else:
                            response_dict = response_dict.get('Body')
                    except etree.XMLSyntaxError as e:
                        return send_error_emailVeriFactu_to_IT(invoice, {"ERROR": f"Error al parsear el XML de la respuesta de la AEAT: {e}"}, response.content)

                    if fault == {}:
                        response_dict = response_from_VeriFactu(data, response_dict, invoice, veriFactu, response, kwargs)
                        # print("response: ", response_dict)
                else:
                    response_dict = {
                        "ERROR": f"Error al enviar el XML a la AEAT {getattr(response, 'text', 'respuesta no válida')}"
                    }
                
                if response_dict.get('ERROR') or response_dict.get('RespuestaRegFactuSistemaFacturacion').get('EstadoEnvio') == 'Incorrecto':
                    return send_error_emailVeriFactu_to_IT(invoice, response_dict, response.content)
    except Exception as e:
        return send_error_emailVeriFactu_to_IT(invoice, {"ERROR": f"Error al enviar la factura a Verifactu: {e}"})

def is_invoice_must_send_to_VeriFactu(invoice, choice=True):
    """
    Función que comprueba si una factura debe ser enviada a Verifactu a través de una serie de condiciones.
    """

    conditions = {
        "is_generated": invoice.is_generated,
        "status_code": invoice.status.code == "revised",
        "choice" : choice, # La variable "choice" se utiliza para comprobar si la factura debe ser enviada a Verifactu o no cuando es un manager quien la crea
        "legal_entity": invoice.seller.legal_entity in ["self-employed", "sl"],
        "tax_country" : invoice.tax_country.iso_code == "ES",
        "is_generated_amz": invoice.is_generated_amz is not True,
    }

    return all(conditions.values())

def get_xml_from_aeat(invoice):
    """
    Función que obtiene el XML de la respuesta de la AEAT a partir de una factura.
    """

    invVerifactu = VerifactuInv.objects.filter(seller=invoice.seller, invoice=invoice, status_in_verifactu__in=['Correcto', 'AceptadoConErrores'] ).order_by('-created_at').first()

    if invVerifactu and invVerifactu.response_xml_aeat:
        try:
            root_element = etree.fromstring(invVerifactu.response_xml_aeat.encode("utf-8"))
            root = etree.tostring(root_element, pretty_print=True, xml_declaration=True, encoding='UTF-8')
            return root
        except etree.XMLSyntaxError as e:
            print(f"Error al parsear el XML de la respuesta de la AEAT: {e}")