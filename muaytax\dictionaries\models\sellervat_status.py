from django.db import models

class SellerVatStatus(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )

    contracted = models.BooleanField(
        blank=True, 
        null=True, 
        verbose_name="Contratado"
    )

    class Meta:
        verbose_name = "Estado del Pais IVA"
        verbose_name_plural = "Estado de los Paises IVA"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatStatus)
# class SellerVatStatusAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
