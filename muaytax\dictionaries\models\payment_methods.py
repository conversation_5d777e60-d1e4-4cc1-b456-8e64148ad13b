from django.db import models

class PaymentMethod(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ódigo"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    class Meta:
        verbose_name = "Metodo de Pago"
        verbose_name_plural = "Metodos de Pago"
    
    def __str__(self):
        return self.description
    
# @admin.register(PaymentMethod)
# class PaymentMethodAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
