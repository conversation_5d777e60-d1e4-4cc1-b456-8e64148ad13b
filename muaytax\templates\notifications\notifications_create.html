{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Notificaciones MuayTax
{% endblock title %}

{% block stylesheets %}
<link rel="stylesheet" crossorigin href="https://use.fontawesome.com/releases/v6.2.1/css/all.css" type="text/css" />

<link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/radio-card-buttons.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/side-progress-tabs.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/bell-animation.css' %}" />

<style>
    .dashed-input {
        border: 1px dashed #ced4da;
        border-radius: 0.25rem;
        background-color: transparent;
    }

    .dashed-input:focus {
        outline: none;
        box-shadow: none;
        background-color: transparent;
    }

    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }
    .custom-disable-date-picker[readonly] {
        background-color: #fff;
    }

    .equal-width-btn {
        width: 200px;
        font-size: 0.875rem;
    }

    .fs-875 {
        font-size: 0.875rem;
    }

    @media (max-width: 576px) {
        .equal-width-btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .d-flex.justify-content-between.flex-wrap {
            justify-content: center;
        }
    }

    .swal2-action-div-full-width{
        margin: 1em 1.6em!important;

    }
    .swal2-styled.swal2-confirm{
        background-color: rgba(33, 37, 41, 1)!important;
    }
    .swal2-styled.swal2-cancel{
        background-color: transparent!important;
        color: rgba(33, 37, 41, 1)!important;
    }
    .swal2-actions:not(.swal2-loading) .swal2-styled:hover,
    .swal2-actions:not(.swal2-loading) .swal2-styled:active {
        background-image: none!important;
    }
    .swal2-styled.swal2-cancel:hover{
        background-color: transparent!important;
        text-decoration: underline!important;
    }
    .swal2-btn-full-width{
        width: 100%!important;
    }
    

</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                        Notificaciones
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'app_notifications:notifications_dashboard' %}">Centro de notificaciones</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="">Nueva notificación</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}

<div class="col-12 mt-3">
    <div class="row">
        <div class="col-lg-9">
            <form
                id="new-notification-form"
                method="POST"
                enctype="multipart/form-data"
                action="{% url 'app_notifications:create_notification' %}"
                >
                {% csrf_token %}
                <!-- Formulario -->
                <div class="row d-flex flex-wrap">
                    <!-- progress tabs -->
                    <div class="col-lg-auto d-flex justify-content-center mb-3">
                        <div class="d-flex flex-lg-column gap-4 align-items-center nav" role="tablist">
                            <!-- First Tab -->
                            <a class="side-progress-card active" id="new-notification-tab-1" data-bs-toggle="pill" href="#new-notification-panel-1" role="tab" aria-controls="new-notification-panel-1" aria-selected="true">
                                <i class="fas fa-user-tie fa-3x text-dark"></i>
                                <p class="mb-0 fw-bolder small text-dark">REMITENTE</p>
                                <div class="progress-card-conector d-none d-lg-block"></div>
                                <div class="progress-card-conector-horizontal d-lg-none"></div>
                            </a>

                            <!-- Second Tab -->
                            <a class="side-progress-card" id="new-notification-tab-2" data-bs-toggle="pill" href="#new-notification-panel-2" role="tab" aria-controls="new-notification-panel-2" aria-selected="false">
                                <!-- <i class="fas fa-users-cog fa-3x text-dark"></i> -->
                                <i class="fas fa-bell fa-3x text-dark"></i>
                                <p class="mb-0 fw-bolder small text-dark">NOTIFICACIÓN</p>
                                <div class="progress-card-conector d-none d-lg-block"></div>
                                <div class="progress-card-conector-horizontal d-lg-none"></div>
                            </a>

                            <!-- Third Tab -->
                            <div class="side-progress-card" id="new-notification-tab-3" data-bs-toggle="pill" href="#new-notification-panel-3" role="tab" aria-controls="new-notification-panel-3" aria-selected="false">
                                <i class="fas fa-cogs fa-3x text-dark"></i>
                                <p class="mb-0 fw-bolder small text-dark">CONFIG.</p>
                            </div>
                        </div>
                    </div>

                    <!-- tab content -->
                    <div class="col d-flex flex-column mb-3">
                        <!-- Notification title -->
                        <div class="mb-4">
                            <input type="text" class="form-control dashed-input fw-bolder" id="title" name="title" placeholder="Título de la notificación" required maxlength="50">
                        </div>
                        <div class="tab-content p-0 bg-transparent shadow-none flex-fill">
                            <!-- content 1 REMITENTE -->
                            <div class="tab-pane fade show active" id="new-notification-panel-1" role="tabpanel" aria-labelledby="new-notification-tab-1">
                                <h4 class="mb-3 fw-bolder">
                                    Remitente
                                </h4>
                                <div class="card rounded">
                                    <div class="card-header">
                                        <h5 class="card-title">Departamento remitente</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group row">
                                            <div class="col-lg-12">
                                                <div class="alert alert-danger d-none" role="alert" id="is-invalid-remitente">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <span>Debes seleccionar obligatoriamente un remitente para continuar.</span>
                                                </div>
                                                <div class="radio-card-buttons gap-3 justify-content-start">
                                                    <input type="radio" id="remitente-1" name="sender_code" value="accounting-es-department" required class="radio-input" />
                                                    <label for="remitente-1" class="radio-card-button w-150x">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <img src="{% static 'assets/images/logos/svg/departamentos/bandera-españa.svg' %}" alt="department-avatar" width="80">
                                                            <p class="text-muted mb-0">Gestoría España</p>
                                                        </div>
                                                    </label>
                                                
                                                    <input type="radio" id="remitente-2" name="sender_code" value="support-usa-department" required class="radio-input" />
                                                    <label for="remitente-2" class="radio-card-button w-150x">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <img src="{% static 'assets/images/logos/svg/departamentos/bandera-eeuu.svg' %}" alt="department-avatar" width="80">
                                                            <p class="text-muted mb-0">Gestoría USA</p>
                                                        </div>
                                                    </label>
                                                
                                                    <input type="radio" id="remitente-3" name="sender_code" value="amzvat-department" required class="radio-input" />
                                                    <label for="remitente-3" class="radio-card-button w-150x">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <img src="{% static 'assets/images/logos/svg/departamentos/isotipo-amzvat.svg' %}" alt="department-avatar" width="80">
                                                            <p class="text-muted mb-0">AMZVAT</p>
                                                        </div>
                                                    </label>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-dark equal-width-btn" onclick="changeTab('new-notification-tab-2')">Siguiente</button>
                                </div>
                            </div>

                            <!-- content 2 NOTIFICACIÓN -->
                            <div class="tab-pane fade" id="new-notification-panel-2" role="tabpanel" aria-labelledby="new-notification-tab-2">
                                <h4 class="mb-3 fw-bolder">
                                    Tipo de Notificación
                                </h4>

                                <div class="card rounded">
                                    <div class="card-header">
                                        <h5 class="card-title">Tipo de notificación</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group row">
                                            <div class="col-lg-12">
                                                <div class="alert alert-danger d-none" role="alert" id="is-invalid-notification-type">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <span>Debes seleccionar obligatoriamente el tipo de notificación a realizar.</span>
                                                </div>
                                                <div class="radio-card-buttons gap-3">
                                                    <input type="radio" id="notyfication-type-1" name="task_function_key" value="pending_models_notification" data-title="Modelos Pendientes" class="radio-input" />
                                                    <label for="notyfication-type-1" class="radio-card-button w-150x">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <img src="{% static 'assets/images/iconos-externos/pending-file.png' %}" alt="department-avatar" width="60" class="mb-2">
                                                            <p class="text-muted mb-0">Modelos pendientes</p>
                                                        </div>
                                                    </label>
                                                </div>

                                                <div class="card mt-3 shadow-none bg-light border rounded d-none" id="info-card-1">
                                                    <div class="card-body">
                                                        <p class="mb-0">
                                                            Esta notificación se enviará a todos los vendedores del departamento seleccionado que, a la fecha indicada en el próximo paso, tengan modelos generados que estén pendientes de aprobación. Asegúrese de que la información proporcionada sea precisa para garantizar una comunicación efectiva y oportuna.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card rounded d-none" id="additional-information-block">
                                    <div class="card-header">
                                        <h5 class="card-title">Información adicional</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group row">
                                            <label for="period-input" class="col-lg-3 col-form-label text-lg-start">Periodo*</label>
                                            <div class="col-lg-6">
                                                <select id="period-input" name="period" class="form-select form-control">
                                                    <option value="" selected disabled>Selecciona un periodo</option>
                                                    <option value="M1">Enero</option>
                                                    <option value="M2">Febrero</option>
                                                    <option value="M3">Marzo</option>
                                                    <option value="M4">Abril</option>
                                                    <option value="M5">Mayo</option>
                                                    <option value="M6">Junio</option>
                                                    <option value="M7">Julio</option>
                                                    <option value="M8">Agosto</option>
                                                    <option value="M9">Septiembre</option>
                                                    <option value="M10">Octubre</option>
                                                    <option value="M11">Noviembre</option>
                                                    <option value="M12">Diciembre</option>
                                                    <option value="Q1">Trimestre 1</option>
                                                    <option value="Q2">Trimestre 2</option>
                                                    <option value="Q3">Trimestre 3</option>
                                                    <option value="Q4">Trimestre 4</option>
                                                    <option value="0A">Anual</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="year-input" class="col-lg-3 col-form-label text-lg-start">Año*</label>
                                            <div class="col-lg-6">
                                                <input type="number" class="form-control" id="year-input" name="year" placeholder="Año" min="2000" max="9999" step="1" oninput="this.value = this.value.slice(0, 4)">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between flex-wrap">
                                    <button type="button" class="btn btn-outline-dark equal-width-btn" onclick="changeTab('new-notification-tab-1')">Atras</button>
                                    <button type="button" class="btn btn-dark equal-width-btn" onclick="changeTab('new-notification-tab-3')">Siguiente</button>
                                </div>
                            </div>

                            <!-- content 3 CONFIGURACIÓN -->
                            <div class="tab-pane fade" id="new-notification-panel-3" role="tabpanel" aria-labelledby="new-notification-tab-3">
                                <h4 class="mb-3 fw-bolder">
                                    CONFIGURACIÓN
                                </h4>
                                <div class="card rounded">
                                    <div class="card-header">
                                        <h5 class="card-title">Información y programación</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-danger d-none" role="alert" id="is-invalid-date-time">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <span>La fecha y hora son obligatorios. Ten en cuenta que la fecha de la notificación no puede ser anterior a la fecha actual y como mínimo debe ser programada con 1 hora de antelación.</span>
                                        </div>
                                        <div class="form-group row">
                                            <label for="description" class="col-lg-3 col-form-label text-lg-start">Descripción</label>
                                            <div class="col-lg-6">
                                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Escribe una breve descripción de la notificación (opcional)"></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="date" class="col-lg-3 col-form-label text-lg-start">Fecha de notificación*</label>
                                            <div class="col-lg-6">
                                                <input type="date" class="form-control cursor-pointer custom-disable-date-picker" id="scheduled_date" name="scheduled_date" required>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="time" class="col-lg-3 col-form-label text-lg-start">Hora de notificación*</label>
                                            <div class="col-lg-6">
                                                <input type="time" class="form-control" id="scheduled_time" name="scheduled_time" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card rounded">
                                    <div class="card-header">
                                        <h5 class="card-title">Método de notificación</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group row">
                                            <div class="alert alert-danger d-none" role="alert" id="is-invalid-method">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                <span>Debes seleccionar obligatoriamente el método de notificación a utilizar.</span>
                                            </div>
                                            <label for="notification_method" class="col-lg-3 col-form-label text-lg-start">Notificar via*</label>
                                            <div class="col-lg-8">
                                                <div class="radio-card-buttons gap-3">
                                                    <input type="radio" id="notification-method-1" name="notification_method" value="email" class="radio-input" />
                                                    <label for="notification-method-1" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fas fa-envelope-open-text fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Email</p>
                                                        </div>
                                                    </label>
                                                    {% comment %}
                                                    <input type="radio" id="notification-method-2" name="notification_method" value="sms" class="radio-input" />
                                                    <label for="notification-method-2" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fas fa-comment-dots fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Mensaje SMS</p>
                                                        </div>
                                                    </label>
                                                
                                                    <input type="radio" id="notification-method-3" name="notification_method" value="wapp" class="radio-input" />
                                                    <label for="notification-method-3" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fab fa-whatsapp fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Whatsapp</p>
                                                        </div>
                                                    </label>
                                                
                                                    <input type="radio" id="notification-method-4" name="notification_method" value="call" class="radio-input" />
                                                    <label for="notification-method-4" class="radio-card-button">
                                                        <div class="checkmark">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="radio-button-content">
                                                            <i class="fas fa-phone fa-3x mb-2"></i>
                                                            <p class="text-muted mb-0">Llamada</p>
                                                        </div>
                                                    </label>
                                                    {% endcomment %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between flex-wrap">
                                    <button type="button" class="btn btn-outline-dark equal-width-btn" onclick="changeTab('new-notification-tab-2')">Atras</button>
                                    <button type="button" class="btn btn-dark equal-width-btn" onclick="submitForm(event)">Crear notificación</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-lg-3 d-none d-lg-block">
            <div class="d-flex flex-column-reverse flex-lg-column">

                <div class="card rounded">
                    <div class="card-header">
                        <h4 class="card-title">
                            Resumen
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Remitente:</p>
                            <p class="fw-bolder mb-0" id="summary_sender">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Tipo:</p>
                            <p class="fw-bolder mb-0" id="summary_task">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Fecha programada:</p>
                            <p class="fw-bolder mb-0" id="summary_date">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-0">Hora programada:</p>
                            <p class="fw-bolder mb-0" id="summary_time">&nbsp;</p>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <p class="text-muted mb-0">Notificar via:</p>
                            <p class="fw-bolder mb-0" id="summary_method">&nbsp;</p>
                        </div>
                        <button type="type" class="btn btn-dark w-100 fs-875" onclick="submitForm(event)">Crear notificación</button>
                    </div>
                </div>
    
                <div class="card rounded">
                    <div class="card-header">
                        <h4 class="card-title">Instrucciones</h4>
                    </div>
                    <div class="card-body">
                        <p>Para <u class="text-dark">crear una nueva notificación programada</u>, sigue estos pasos:</p>
    
                        <p><strong>1. Selecciona el <u class="text-dark">remitente</u>:</strong> Elige el departamento desde el cual se enviará la notificación.</p>
    
                        <p><strong>2. Escoge el tipo de <u class="text-dark">notificación</u>:</strong> Si seleccionas "modelos pendientes", la notificación se enviará solo a los sellers que tengan modelos en el departamento seleccionado y que estén pendientes de aprobación.</p>
    
                        <p><strong>3. <u class="text-dark">Configura</u> la fecha y la hora:</strong> Establece la fecha y hora en que se enviará la notificación.</p>
    
                        <p><strong>4. Define la vía de envío:</strong> Elige cómo se enviará la notificación (por ejemplo, correo electrónico, mensaje SMS, etc.).</p>
    
                        <p>Una vez que hayas <u>configurado todos los detalles</u>, haz clic en <u class="text-dark" role="button">Crear notificación</u> para finalizar el proceso.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



{% endblock content %}

{% block javascripts %}

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>
<script src="{% static 'assets/js/side-progress-tabs.js' %}"></script>

<script defer>
    const today = new Date();
    const $dateInputs = $('#scheduled_date');

    const elements = {
        title: document.getElementById('title'),
        dateInput: document.getElementById('scheduled_date'),
        timeInput: document.getElementById('scheduled_time'),
        summaryDate: document.getElementById('summary_date'),
        summarySender: document.getElementById('summary_sender'),
        summaryTask: document.getElementById('summary_task'),
        summaryTime: document.getElementById('summary_time'),
        summaryMethod: document.getElementById('summary_method'),
        invalidRemitente: document.getElementById('is-invalid-remitente'),
        invalidNotificationType: document.getElementById('is-invalid-notification-type'),
        invalidDateTime: document.getElementById('is-invalid-date-time'),
        invalidMethod: document.getElementById('is-invalid-method'),
        tab1: document.getElementById('new-notification-tab-1'),
        tab2: document.getElementById('new-notification-tab-2'),
        tab3: document.getElementById('new-notification-tab-3'),
        form: document.getElementById('new-notification-form'),
        periodInput: document.getElementById('period-input'),
        yearInput: document.getElementById('year-input'),
    };

    elements.title.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });

    const addEventListenerToNodeList = (nodeList, event, callback) => {
        nodeList.forEach(node => node.addEventListener(event, callback));
    };

    window.addEventListener('DOMContentLoaded', () => elements.title.focus());

    $dateInputs.each(function() {
        new Datepicker(this, {
            autohide: true,
            buttonClass: 'btn',
            format: 'yyyy-mm-dd',
            minDate: today,
            todayHighlight: true,
            weekStart: 1,
        });

        $(this).on('changeDate', function(e) {
            const [year, month, day] = e.target.value.split('-');
            elements.summaryDate.textContent = `${day}/${month}/${year}`;
            $(this).removeClass('is-invalid');
            
            if (elements.timeInput.value && document.querySelector('input[name="notification_method"]:checked')) {
                elements.tab3.classList.remove('invalid');
            }
        });
    });

    function updateDatepicker () {
        const selectedSender = document.querySelector('input[name="sender_code"]:checked');
        const selectedTask = document.querySelector('input[name="task_function_key"]:checked');
        const isSpecialCondition = selectedSender && ['accounting-es-department', 'amzvat-department'].includes(selectedSender.value) && 
        selectedTask && selectedTask.value === 'pending_models_notification';
        const monthsToKeep = isSpecialCondition ? [0, 3, 6, 9] : [];

        $dateInputs.each(function() {
            this.datepicker.destroy();

            new Datepicker(this, {
                autohide: true,
                buttonClass: 'btn',
                format: 'yyyy-mm-dd',
                minDate: today,
                todayHighlight: true,
                weekStart: 1,
                // beforeShowMonth: function(date) {
                //     return isSpecialCondition ? { enabled: monthsToKeep.includes(date.getMonth()) } : true;
                // },
                // beforeShowDay: function(date) {
                //     return isSpecialCondition ? { enabled: monthsToKeep.includes(date.getMonth()) } : { enabled: true };
                // },
            });

        });
    }

    const handleRadioChange = (summaryElement, invalidMessageElement, tabElement, contentSelector) => function() {
        summaryElement.textContent = this.nextElementSibling.querySelector('p').textContent;
        invalidMessageElement.classList.add('d-none');
        tabElement.classList.remove('invalid');
        if (contentSelector) {
            document.querySelectorAll(contentSelector).forEach(card => card.classList.add('d-none'));
            const infoCard = document.getElementById(this.id.replace('notyfication-type-', 'info-card-'));
            if (infoCard) infoCard.classList.remove('d-none');
        }
        if (this.name == 'task_function_key' && this.value === 'pending_models_notification') {
            document.getElementById('additional-information-block').classList.remove('d-none');
            document.getElementById('period-input').setAttribute('required', true);
            document.getElementById('year-input').setAttribute('required', true);
        } else if (this.name == 'task_function_key') {
            document.getElementById('additional-information-block').classList.add('d-none');
            document.getElementById('period-input').removeAttribute('required');
            document.getElementById('year-input').removeAttribute('required');
        }
    };

    addEventListenerToNodeList(document.querySelectorAll('input[name="sender_code"]'), 'change', 
        handleRadioChange(elements.summarySender, elements.invalidRemitente, elements.tab1));

    addEventListenerToNodeList(document.querySelectorAll('input[name="sender_code"]'), 'change', 
        updateDatepicker);

    addEventListenerToNodeList(document.querySelectorAll('input[name="task_function_key"]'), 'change', 
        handleRadioChange(elements.summaryTask, elements.invalidNotificationType, elements.tab2, '.card[id^="info-card-"]'));

    elements.form.querySelector('#period-input')?.addEventListener('change', function() {
        this.classList.remove('is-invalid');
        if (elements.yearInput.value && document.querySelector('input[name="task_function_key"]:checked')) {
            elements.tab2.classList.remove('invalid');
        }
    });

    elements.form.querySelector('#year-input')?.addEventListener('input', function() {
        this.classList.remove('is-invalid');
        if (elements.periodInput.value && document.querySelector('input[name="task_function_key"]:checked')) {
            elements.tab2.classList.remove('invalid');
        }
    });


    elements.timeInput.addEventListener('change', function() {
        elements.summaryTime.textContent = this.value;
        this.classList.remove('is-invalid');
        if (elements.dateInput.value && document.querySelector('input[name="notification_method"]:checked')) {
            elements.tab3.classList.remove('invalid');
        }
        if (elements.dateInput.value) {
            elements.invalidDateTime.classList.add('d-none');
        }
    });

    addEventListenerToNodeList(document.querySelectorAll('input[name="notification_method"]'), 'change', 
        handleRadioChange(elements.summaryMethod, elements.invalidMethod, elements.tab3));
    
    function changeTab(tabId) {
        const elem = document.getElementById(tabId);

        if (elem.closest('.tab-pane')) {
            changeTab(elem.closest('.tab-pane').id + '-tab');
        }
        const tab = new bootstrap.Tab(elem);
        tab.show();

        // add this to update progress side tabs. Function is imported
        const tabLinks = document.querySelectorAll('.side-progress-card');
        const activeIndex = Array.from(tabLinks).findIndex(tabLink => tabLink.id === tabId);
        if (activeIndex !== -1) {
            updateProgress(activeIndex);
        }
    }

    function submitForm(event) {
        event.preventDefault();

        const formData = {
            title: elements.form.querySelector('#title').value,
            sender: elements.form.querySelector('input[name="sender_code"]:checked'),
            task: elements.form.querySelector('input[name="task_function_key"]:checked'),
            date: elements.form.querySelector('#scheduled_date').value,
            time: elements.form.querySelector('#scheduled_time').value,
            method: elements.form.querySelector('input[name="notification_method"]:checked'),
            period: elements.form.querySelector('#period-input') ? elements.form.querySelector('#period-input').value : null,
            year: elements.form.querySelector('#year-input') ? elements.form.querySelector('#year-input').value : null,
        };

        const validations = [
            { condition: !formData.title, element: elements.title, invalidClass: 'is-invalid' },
            { condition: !formData.sender, element: elements.tab1, invalidClass: 'invalid', messageElement: elements.invalidRemitente },
            { condition: !formData.task, element: elements.tab2, invalidClass: 'invalid', messageElement: elements.invalidNotificationType },
            { condition: !formData.date || !formData.time, element: elements.tab3, invalidClass: 'invalid', messageElement: elements.invalidDateTime },
            { condition: !formData.method, element: elements.tab3, invalidClass: 'invalid', messageElement: elements.invalidMethod },
            { condition: !formData.date, element: elements.dateInput, invalidClass: 'is-invalid', messageElement: elements.invalidDateTime },
            { condition: !formData.time, element: elements.timeInput, invalidClass: 'is-invalid', messageElement: elements.invalidDateTime },
            { 
                condition: formData.task && formData.task.value === 'pending_models_notification' && !formData.period, 
                element: elements.form.querySelector('#period-input'), 
                invalidClass: 'is-invalid',

            },
            { 
                condition: formData.task && formData.task.value === 'pending_models_notification' && !formData.year, 
                element: elements.form.querySelector('#year-input'), 
                invalidClass: 'is-invalid' 
            },
            {
                condition: formData.task && formData.task.value === 'pending_models_notification' && (!formData.period || !formData.year),
                element: elements.tab2,
                invalidClass: 'invalid',
            }
        ];

        let formIsValid = true;

        validations.forEach(({ condition, element, invalidClass, messageElement }) => {
            if (condition) {
                formIsValid = false;
                element.classList.add(invalidClass);
                if (messageElement) messageElement.classList.remove('d-none');
            }
        });

        // if (formData.date && formData.time) {
        //     const datetimeSelected = new Date(`${formData.date}T${formData.time}`);
        //     if (datetimeSelected < new Date(today.getTime() + 60 * 60 * 1000)) {
        //         formIsValid = false;
        //         elements.invalidDateTime.classList.remove('d-none');
        //         elements.tab3.classList.add('invalid');
        //         elements.timeInput.classList.add('is-invalid');
        //     }
        // }

        if (!formIsValid) {
            Swal.fire({
                icon: 'error',
                text: 'Debes completar todos los campos obligatorios para continuar.',
                confirmButtonText: 'Completar información',
                customClass: {
                    confirmButton: 'swal2-btn-full-width',
                    actions: 'swal2-action-div-full-width',
                },

            });
            return;
        }

        var loadingBellHtml = `{% include 'includes/swal-bell-loading-animation.html' %}`;
        Swal.fire({
            title: 'Creando notificación',
            html: loadingBellHtml,
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                // Swal.showLoading();
                elements.form.submit();
            },
        });
    };

    updateDatepicker();
</script>
{% endblock javascripts %}