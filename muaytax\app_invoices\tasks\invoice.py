import json
from celery import shared_task, group
from celery.signals import task_prerun

from django.conf import settings

from muaytax.celery import app
from muaytax.app_ocr.utils import UpdateInvoiceFromOCR
from muaytax.app_ocr.controller import MuaytaxOCRProcessor
from muaytax.app_ocr.choices import ProcessingDocument

from muaytax.app_invoices.models import Invoice
from muaytax.app_invoices.electronic_invoice.veriFactu import send_invoice_toVeriFactu_auto

@app.task(queue='invoice_processing')
def process_invoices_with_ocr(invoice_pks: list):
    """Processes the invoices with OCR using their primary keys."""

    try:
        invoices = Invoice.objects.filter(pk__in=invoice_pks)
        if not invoices.exists():
            print(f"\r\033[91mNo se encontraron facturas con los ids {invoice_pks}\033[0m")
            return False

        seller = invoices.first().seller
        processor = MuaytaxOCRProcessor(ProcessingDocument.INVOICE, seller=seller)

        for invoice in invoices:
            try:
                print(f"\r\033[92m**********Procesando la factura {invoice.pk} con OCR******\033[0m")
                extracted_text = processor.extract_text(invoice.file.path)
                json_output = processor.process_extracted_text(extracted_text)

                update_invoice = UpdateInvoiceFromOCR(invoice, json_output, extracted_text)
                update_invoice.process()

                print(f"\r\033[92m**********Factura {invoice.pk} procesada con éxito**********\033[0m")

            except Exception as e:
                print(f"\r\033[91mError al procesar la factura {invoice.pk}, por el motivo: {e}\033[0m")

        return True
    
    except Exception as e:
        print(f"\r\033[91mError general en la función para procesar facturas: {e}\033[0m")
        return False

@shared_task
def auto_invoice_toVeriFactu(invoice, **kwargs):
    print("Empieza la función de VeriFactu")
    # if settings.IS_PRODUCTION:
    send_invoice_toVeriFactu_auto(invoice, kwargs)
    print("Termina la función de VeriFactu")


# @task_prerun.connect
# def lock_seller_invoices_task(sender=None, task_id=None, task=None, args=None, **kwargs):
#     """Evita que múltiples tareas de procesamiento de facturas del mismo vendedor se ejecuten al mismo tiempo."""
#     print(f"\033[93mSENDER {sender.name}\033[0m")
#     print(f"\033[93mSELER {args[0]}\033[0m")