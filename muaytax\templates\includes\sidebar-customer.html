{% load static i18n model_184 model_5472_1120 banks_seller raps %}
{% load seller_vat_utils %}
{% now "Y-m-d" as today_date %}

<ul class="nav pcoded-inner-navbar">
    <li class="nav-item pcoded-menu-caption">
    </li>

    <li data-username="Contabilidad" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
        <span class="pcoded-mtext">Contabilidad</span>
        </a>
        <ul class="pcoded-submenu">
            <li data-username="<PERSON><PERSON> de Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
                {% if filter_country and filter_year and filter_month and filter_iae %}
                <a href="{% url 'users:user_dash_filter' filter_country filter_iae filter_year filter_month %}" class="nav-link">
                    <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
                    <span class="pcoded-mtext">General</span>
                </a>
                {% else %}
                <a href="{% url 'home' %}" class="nav-link">
                    <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
                    <span class="pcoded-mtext">General</span>
                </a>
                {% endif %}
            </li>
            {% show_banks_to_seller as show_banks %}
            {% if show_banks %}
            <li data-username="Todas las Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
                <a href="{% url 'app_banks:bank_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="fas fa-university"></i></span>
                <span class="pcoded-mtext">Conciliaciones</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </li>

    <li data-username="Facturacion" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
        <span class="pcoded-mtext">Facturación</span>
        </a>
        <ul class="pcoded-submenu">
        <li data-username="Facturas de Gasto" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_invoices:seller_invoices_category' user.seller.shortname 'expenses' %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
            <span class="pcoded-mtext">Facturas de Gasto</span>
            </a>
        </li>
        <li data-username="Facturas de Venta" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_invoices:seller_invoices_category' user.seller.shortname 'sales' %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
            <span class="pcoded-mtext">Facturas de Venta</span>
            </a>
        </li>
        <li data-username="Todas las Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_invoices:seller_invoices' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
            <span class="pcoded-mtext">Todas las Facturas</span>
            </a>
        </li>
        <li data-username="Crear Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_invoices:seller_invoice_new' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
            <span class="pcoded-mtext">Crear Facturas</span>
            </a>
        </li>
        <li data-username="Crear Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_invoices:seller_invoices_upload' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-upload-cloud"></i></span>
            <span class="pcoded-mtext">Cargador</span>
            </a>
        </li>
        <li data-username="Contactos" class="nav-item pcoded-hasmenu">
            <a href="#!" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-users"></i></span>
            <span class="pcoded-mtext">Contactos</span>
            </a>
            <ul class="pcoded-submenu">
            <li data-username="Clientes" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_customers:list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-user"></i></span>
                <span class="pcoded-mtext">Clientes</span>
                </a>
            </li>
            <li data-username="Proveedores" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_providers:list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-user"></i></span>
                <span class="pcoded-mtext">Proveedores</span>
                </a>
            </li>
            <li data-username="Socios" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_partners:list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-user"></i></span>
                <span class="pcoded-mtext">Socios</span>
                </a>
            </li>
            <li data-username="Tranajadores" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_workers:list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-user"></i></span>
                <span class="pcoded-mtext">Trabajadores</span>
                </a>
            </li>
            </ul>
        </li>
        <li data-username="Productos" class="nav-item pcoded-hasmenu">
            <a href="#!" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-box"></i></span>
            <span class="pcoded-mtext">Productos</span>
            </a>
            <ul class="pcoded-submenu">
            <li data-username="Clientes" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_products:amz_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-inbox"></i></span>
                <span class="pcoded-mtext">Productos Amazon</span>
                </a>
            </li>
            </ul>
        </li>

        {% if user.seller.pk == 251 %}
            <li data-username="Marketplaces" class="nav-item {% if 'marketplaces' in segment %} active{% endif %}">
            <a href="{% url 'app_marketplaces:marketplaces_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-shopping-cart"></i></span>
                <span class="pcoded-mtext">Marketplaces</span>
            </a>
            </li>
        {% endif %}
        </ul>
    </li>

    <li data-username="Declaraciones" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
        <span class="pcoded-mtext">Declaraciones</span>
        </a>
        <ul class="pcoded-submenu">
        <li data-username="Empresa" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_documents:presented_model_pending_list' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
            <span class="pcoded-mtext">Pendientes</span>
            </a>
        </li>
        <li data-username="Empresa" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_documents:presented_model_enterprise_list' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
            <span class="pcoded-mtext">Empresa</span>
            </a>
        </li>
        <li data-username="IVA" class="nav-item">
            <a href="{% url 'app_documents:presented_model_vat_list' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
            <span class="pcoded-mtext">IVA</span>
            </a>
        </li>
        {% comment %} 
        {% is_m5472_1120_contracted as is_contracted_5472_1120 %}
            {% if is_contracted_5472_1120 %}
                <li data-username="Form 5472-1120" class="nav-item pcoded-hasmenu">
                <a href="#!" class="nav-link">
                    <span class="pcoded-micon"><i class="fab fa-wpforms"></i></span>
                    <span class="pcoded-mtext">Form 5472-1120 </span>
                </a>
                <ul class="pcoded-submenu">
                    {% for m5472 in is_contracted_5472_1120 %}                        
                        <li data-username="Form 5472-1120" class="nav-item {% if ' m-5472' in segment %} active{% endif %}">
                            <a href="{% url 'app_documents:request5472_1120' user.seller.shortname m5472.year %}" class="nav-link">
                                <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
                                <span class="pcoded-mtext">Form 5472-1120 ({{ m5472.year}})</span>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </li>
            {% endif %} 
        {% endcomment %}

        {% get_m5472_1120_contracted_years as is_contracted_5472_1120 %}
        {% if is_contracted_5472_1120 %}
            <li data-username="Modelo 5472-1120" class="nav-item pcoded-hasmenu">
                <a href="#!" class="nav-link">
                    <span class="pcoded-micon"><i class="fab fa-wpforms"></i></span>
                    <span class="pcoded-mtext">Modelo 5472-1120 </span>
                </a>
                <ul class="pcoded-submenu">
                    {% for m5472 in is_contracted_5472_1120 %}                        
                        {% with m5472.year|add:"1"|stringformat:"s"|add:"-01-24" as cutoff_date %}
                            {% if today_date > cutoff_date %}
                                <li data-username="Modelo 5472-1120" class="nav-item {% if 'm-5472' in segment %} active{% endif %}">
                                    <a href="{% url 'app_documents:request5472_1120' user.seller.shortname m5472 %}" class="nav-link">
                                        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
                                        <span class="pcoded-mtext">{{ m5472}}</span>
                                    </a>
                                </li>
                            {% endif %}
                        {% endwith %}                        
                    {% endfor %}
                </ul>
            </li>
        {% endif %}

        {% is_be15_enabled as is_contracted_be15 %}
        {% if is_contracted_be15 %}
        <li data-username="{% blocktranslate with model_n='BE-15' %}Model {{ model_n }}{% endblocktranslate %}" class="nav-item">
            <a href="{% url 'app_documents:model_BE15_seller_form' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
            <span class="pcoded-mtext">{% blocktranslate with model_n='BE-15' %}Form {{ model_n }}{% endblocktranslate %}</span>
            </a>
        </li>
        {% endif %}
        </ul>
    </li>

    <li data-username="Administracion" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
        <span class="pcoded-mtext">Administración</span>
        </a>
        <ul class="pcoded-submenu">
        <li data-username="Lector de Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_sellers:data' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
            <span class="pcoded-mtext">Datos empresa</span>
            </a>
        </li>
        <li data-username="Lector de Facturas" class="nav-item">
            <a href="{% url 'app_sellers:vat_list' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
            <span class="pcoded-mtext">Números de registro</span>
            </a>
        </li>
        {% has_rap as rap %}
        {% if rap %}
            <li data-username="Lector de Facturas" class="nav-item">
                <a href="{% url 'app_services:rap_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
                <span class="pcoded-mtext">Listado de RAPS</span>
                </a>
            </li>
        {% endif %}
        <li data-username="Documentos" class="nav-item pcoded-hasmenu">
            <a href="#!" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file"></i></span>
            <span class="pcoded-mtext">Documentos</span>
            </a>
            <ul class="pcoded-submenu">
            <li data-username="Clientes" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_documents:document_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-file"></i></span>
                <span class="pcoded-mtext">Documentos</span>
                </a>
            </li>
            <li data-username="Clientes" class="nav-item {% if 'documentos' in segment %} active{% endif %}">
                <a href="{% url 'app_documents:document_upload' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-upload-cloud"></i></span>
                <span class="pcoded-mtext">Cargador</span>
                </a>
            </li>
            </ul>
        </li>
        {% m184_is_contracted as is_184_cantracted %}
        {% if is_184_cantracted %}
            <li data-username="Modelo 184" class="nav-item pcoded-hasmenu">
                <a href="#!" class="nav-link">
                    <span class="pcoded-micon"><i class="fab fa-wpforms"></i></span>
                    <span class="pcoded-mtext">Modelo 184 </span>
                </a>
                <ul class="pcoded-submenu">
                    {% for m184 in is_184_cantracted %}                        
                        {% with m184.year|add:"1"|stringformat:"s"|add:"-01-13" as cutoff_date %}
                            {% if today_date > cutoff_date %}
                                <li data-username="Modelo 184" class="nav-item {% if 'm-184' in segment %} active{% endif %}">
                                    <a href="{% url 'app_sellers:request184' user.seller.shortname m184.year %}" class="nav-link">
                                        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
                                        <span class="pcoded-mtext">{{ m184.service_name}} - {{ m184.year}}</span>
                                    </a>
                                </li>
                            {% endif %}
                        {% endwith %}                        
                    {% endfor %}
                </ul>
            </li>
        {% endif %}

        </ul>
    </li>
    {% has_rap as rap %}
    {% has_vat_request_pending user.seller as show_iva_link %}
    {% if rap or show_iva_link %}
    <li data-username="Formularios" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
            <span class="pcoded-mtext">Formularios</span>
        </a>
        <ul class="pcoded-submenu">
        
        {% if rap %}
            <li data-username="RAPS" class="nav-item pcoded-hasmenu">
                <a href="#!" class="nav-link">
                    <span class="pcoded-micon"><i class="fab fa-wpforms"></i></span>
                    <span class="pcoded-mtext">RAPS </span>
                </a>
                <ul class="pcoded-submenu">
                    {% for service in rap %}
                    <li data-username="RAPS" class="nav-item {% if 'raps' in segment %} active{% endif %}">
                        <a href="{% url 'app_services:rap_request' user.seller.shortname service.pk %}" class="nav-link">
                            <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
                            <span class="pcoded-mtext">{{ service.service_name}}</span>
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </li>
        {% endif %}

        {% if show_iva_link %}
            <li class="nav-item">
                <a href="{% url 'app_sellers:vat_request_service' user.seller.shortname %}" class="nav-link">
                    <i class="fas fa-file-alt"></i> Solicitud de Servicio IVA
                </a>
            </li>
        {% endif %}
        </ul>
    </li>
    {% endif %}
    <li class="nav-item pcoded-menu-caption">
        <label>Atención al cliente</label>
    </li>
    <li data-username="Citas" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
        <span class="pcoded-micon"><i class="feather icon-phone"></i></span>
        <span class="pcoded-mtext">Soporte</span>
        </a>
        <ul class="pcoded-submenu">
        <li data-username="Citas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_bookings:list_bookings_seller' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-list"></i></span>
            <span class="pcoded-mtext">Ver mis llamadas</span>
            </a>
        </li>
        <li data-username="Citas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_bookings:new_booking' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-phone-incoming"></i></span>
            <span class="pcoded-mtext">Solicitar una llamada</span>
            </a>
        </li>
        </ul>
    </li>

    {% if user.seller.affiliate_program or user.seller.affiliatebpa_program %}
    <li class="nav-item pcoded-menu-caption">
        <label>Programas de afiliados</label>
    </li>
    <li data-username="Afiliados" class="nav-item pcoded-hasmenu">
        <a href="#!" class="nav-link">
        <span class="pcoded-micon"><i class="feather icon-file-text"></i></span>
        <span class="pcoded-mtext">Información</span>
        </a>
        <ul class="pcoded-submenu">
        {% if user.seller.affiliate_program %}
        <li data-username="Lector de Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_sellers:affiliate_program_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
                <span class="pcoded-mtext">Beneficios (Escalera)</span>
            </a>
        </li>
        {% endif %}
        {% if user.seller.affiliatebpa_program %}
        <li data-username="Lector de Facturas" class="nav-item {% if 'facturas' in segment %} active{% endif %}">
            <a href="{% url 'app_sellers:affiliatebpa_program_list' user.seller.shortname %}" class="nav-link">
                <span class="pcoded-micon"><i class="feather icon-bar-chart-2"></i></span>
                <span class="pcoded-mtext">Beneficios (BPA)</span>
            </a>
        </li>
        {% endif %}
        <li data-username="Crear Facturas" class="nav-item">
            <a href="{% url 'app_invoices:seller_invoices_upload_affiliate' user.seller.shortname %}" class="nav-link">
            <span class="pcoded-micon"><i class="feather icon-upload-cloud"></i></span>
            <span class="pcoded-mtext">Cargador</span>
            </a>
        </li>
        </ul>
    </li>
    {% endif %}

    {% comment %} <li class="nav-item pcoded-menu-caption">
        <label>Mis Tareas</label>
    </li>
    <ul class="pcoded-submenu">
        <li data-username="Tareas Pendientes" class="nav-item {% if 'task-pendings' in segment %} active{% endif %}">
            <a href="{% url 'app_tasks:list_task_pending_seller' user.seller.shortname %}" class="nav-link d-flex justify-content-between align-items-center">
                <div>
                    <span class="pcoded-micon"><i class="feather icon-list"></i></span>
                    <span class="pcoded-mtext m-2">Tareas Pendientes</span>
                </div>
                <div id="pending-task-indicator" class="d-flex justify-content-center align-items-center"></div>
            </a>
        </li>
    </ul> {% endcomment %}
</ul>

{% block javascripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const shortname = '{{ user.seller.shortname }}';
        const alertIs = '{{ alert_pending_notification|yesno:"true,false" }}';
    
        // Realizar la petición AJAX para obtener los conteos de tareas
        fetch(`/api/task_counts/${shortname}/?purpose=notification`)
            .then(response => response.json())
            .then(taskCounts => {
                if (taskCounts.error) {
                    console.error(taskCounts.error);
                    return;
                }
    
                // Mostrar punto rojo en el sidebar si hay tareas no completadas
                if (taskCounts.not_completed > 0) {
                    const pendingTaskIndicator = document.createElement('div');
                    pendingTaskIndicator.classList.add('d-flex', 'flex-column', 'align-items-center');
                    pendingTaskIndicator.innerHTML = '<i class="fas fa-circle text-danger" style="font-size: 10px;"></i>';
                    const pendingTasksMenuItem = document.getElementById('pending-task-indicator');
                    if (pendingTasksMenuItem) {
                        pendingTasksMenuItem.appendChild(pendingTaskIndicator);
                    }
                }
    
                // Mostrar alerta si hay tareas según el tipo
                if (alertIs === "true") {
                    if (taskCounts.blocking) {
                        Swal.fire({
                            title: 'Tareas Importantes',
                            text: 'Tienes tareas importantes por resolver.',
                            icon: 'warning',
                            confirmButtonText: 'Ir a Tareas Pendientes',
                            showCancelButton: false,
                            allowOutsideClick: false,
                            customClass: {
                                popup: 'custom-modal'
                            }
                        }).then(() => {
                            window.location.href = "{% url 'app_tasks:list_task_pending_seller' user.seller.shortname %}";
                        });
                    } else if (taskCounts.invasive) {
                        Swal.fire({
                            title: 'Tareas Pendientes',
                            text: 'Tienes tareas invasivas pendientes.',
                            icon: 'warning',
                            confirmButtonText: 'Ir a Tareas Pendientes',
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            customClass: {
                                popup: 'custom-modal'
                            }
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href = "{% url 'app_tasks:list_task_pending_seller' user.seller.shortname %}";
                            }
                        });
                    } else if (taskCounts.minimal) {
                        Toast.fire({
                            icon: 'info',
                            title: 'Tienes tareas mínimas por ver.',
                            timer: 3000,
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timerProgressBar: true
                        });
                    }
                }
            })
            .catch(error => {
                console.error('Error al obtener los conteos de tareas:', error);
            });
    
        // Código para manejar la apertura y cierre de submenús
        const menuLinks = document.querySelectorAll('.pcoded-hasmenu > a[href="#!"]');
    
        menuLinks.forEach(link => {
            link.addEventListener('click', function (event) {
                event.preventDefault();
    
                // Obtener el submenú correspondiente
                const submenu = this.nextElementSibling;
    
                // Cerrar todos los submenús
                document.querySelectorAll('.pcoded-submenu').forEach(sub => {
                    if (sub !== submenu) {
                        sub.classList.remove('show');
                    }
                });
    
                // Alternar el submenú actual
                if (submenu) {
                    submenu.classList.toggle('show');
                }
            });
        });
    
        // Inicializar el Toast para notificaciones mínimas
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer);
                toast.addEventListener('mouseleave', Swal.resumeTimer);
            }
        });
    });
    
</script>
{% endblock javascripts %}
