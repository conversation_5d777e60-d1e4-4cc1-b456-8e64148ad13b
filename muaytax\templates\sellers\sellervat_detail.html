{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}
{% block title %}Pais IVA{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:vat_list' seller.shortname %}">Paises IVA</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos generales</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <style>
    .option-green {
      background-color: darkgreen;
      color: white;
    }

    .option-red {
      background-color: #ffdddd;
      color: darkred;
    }

    .option-grey {
      background-color: #666;
      color: black;
    }

    .option-blue {
      background-color: lightblue;
      color: darkblue;
    }

    li {
      font-weight: normal;
    }

    /* Conservamos tus estilos originales */
    .dropbtn {
      background-color: #04AA6D;
      color: white;
      padding: 12px;
      font-size: 16px;
      border: none;
      margin-bottom: 10px;
    }

    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      background-color: #f1f1f1;
      min-width: 160px;
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
      z-index: 1;
      top: 0;
      right: 100%;
    }

    .btn > i, .introjs-button > i {
      margin-right: 0px;
      padding-rigth: 5px;
    }

    .dropdown-content a {
      color: black;
      padding: 12px 16px;
      text-decoration: none;
      display: block;
      background-color: #ddd; /* Color de fondo predeterminado */
      width: auto;
    }

    .dropdown-content a:hover {
      background-color: #3e8e41; /* Color de fondo al pasar el ratón */
    }

    .dropdown:hover .dropdown-content {
      display: block;
    }

    .dropdown:hover .dropbtn {
      background-color: #3e8e41;
    }

    /* Color de fondo para cada opción individual */
    .dropdown-content a:nth-child(1):hover {
      background-color: #F4D03F;
    }

    .dropdown-content a:nth-child(2):hover {
      background-color: #F4D03F;
    }

    .dropdown-content a:nth-child(3):hover {
      background-color: #AEB6BF;
    }

    .dropdown-content a:nth-child(4):hover {
      background-color: #6D64DB;
    }
  </style>
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" id="form" enctype="multipart/form-data" action="">
        {% csrf_token %}
        {% comment %} {{ form |crispy }} {% endcomment %}
        <div class="row">
          <div class="d-flex align-items-center justify-content-between">
            <h3 id="title_process">Proceso </h3>
            <div class="dropdown float-right ">
              <span class="dropbtn btn" id="btn_dropmenu"
                    style="padding-right: 18px; padding-left: 17px; background-color: #28a745; border-color: #28a745;"><i
                class="bi bi-sliders"></i></span>
              <div class="dropdown-content">
                <a href="#" class="dropdown-option" id="button_edit_on" onclick="edition_mode('on')"
                   style="display:none;"><i class="bi bi-tools"></i>&nbsp; Modo Edición</a>
                <a href="#" class="dropdown-option" id="button_edit_off" onclick="edition_mode('off')"
                   style="display:none;"><i class="bi bi-tools"></i>&nbsp;Salir Edición</a>
                <a href="#" class="dropdown-option" id="show_days_requireds" onclick="showHideDaysRequired()"><i
                  class="bi bi-calendar-date"></i>&nbsp; Acciones requeridas</a>
                <a href="#" class="dropdown-option" id="button-historial" onclick="showHideHistorial()"><i
                  class="bi bi-clock-history"></i>&nbsp; Historial</a>
              </div>
            </div>
          </div>
          <hr>
        </div>
        {% if form.non_field_errors %}
          <div class="alert alert-danger" role="alert">
            {{ form.non_field_errors }}
          </div>
        {% endif %}
        <div class="row">
          <div class="col-4 mb-3">
            <div class="form-check">
              <input
                type="checkbox"
                id="id_is_contracted"
                name="is_contracted"
                class="checkboxinput form-check-input"
                {% if object.is_contracted == True %} checked="true" {% endif %}
              />
              <label for="id_is_contracted" class="form-check-label">
                Contratado
              </label>
              <div id="hint_id_is_contracted" class="form-text">¿Esta el Nº IVA contratado? Si:Activo | No:Inactivo
              </div>
            </div>
          </div>
          <div class="col-4 mb-3">
            <label for="id_vat_country" class="form-label requiredField">
              País del IVA:
            </label>
            <select id="id_vat_country" name="vat_country" onclick="ShowHideOther()" class="select form-select"
                    required="">
              <option value="">---------</option>
              {% for country in countries %}
                {% if country.iso_code == object.vat_country.iso_code %}
                  <option value="{{ country.iso_code }}" selected>{{ country.name }}</option>
                {% else %}
                  <option value="{{ country.iso_code }}">{{ country.name }}</option>
                {% endif %}
              {% endfor %}
            </select>
          </div>
          <div class="col-4 mb-3" id="div_num_iva">
            <label for="id_vat_number" class="form-label requiredField">
              Numero de IVA:
            </label>
            <input
              type="text"
              maxlength="50"
              id="id_vat_number"
              name="vat_number"
              value="{{ object.vat_number| default:'' }}"
              class="textinput textInput form-control"
            />
          </div>
          <div class="col-4 mb-3">
            <label for="id_type_process" class="form-label">
              Tipo de proceso:
            </label>
            <select id="id_type_process" name="type" class="select form-select" required="">
              <option value="">---------</option>
              {% for status in sellervat_type %}
                {% if status.code == object.type.code %}
                  <option value="{{ status.code }}" selected>{{ status.description }}</option>
                {% else %}
                  <option value="{{ status.code }}">{{ status.description }}</option>
                {% endif %}
              {% endfor %}
            </select>
          </div>
          <div class="col-4 mb-3">
            <label for="id_vat_status" class="form-label requiredField">
              Estado del País IVA:
            </label>
            <select id="id_vat_status" name="vat_status" class="select form-select" required="">
              <option value="">---------</option>
            </select>
          </div>
          <div class="col-4 mb-3" id="process">
            <label for="id_status_process" class="form-label">
              Estado del proceso:
            </label>
            <select id="id_status_process" name="status_process" class="select-status select form-select" required="">
              {% for status in sellervat_status_process %}
                {% if status.code == object.status_process.code %}
                  <option value="{{ status.code }}" selected>{{ status.description }}</option>
                {% elif object.status_process is None %}
                  {% if status.code == "no_info" %}
                    <option value="{{ status.code }}" selected>{{ status.description }}</option>
                  {% else %}
                    <option value="{{ status.code }}">{{ status.description }}</option>
                  {% endif %}
                {% else %}
                  <option value="{{ status.code }}">{{ status.description }}</option>
                {% endif %}
              {% endfor %}
            </select>
          </div>
          <div id="id_div_es_status" class="col-12 mb-3">
            <div class="row">
              <div class="col-3 mb-3" id="div_es_status_activation">
                <label for="id_es_status_activation" class="form-label">
                  ¿Quiere activación?
                </label>
                <select id="id_es_status_activation" name="es_status_activation"
                        class="select-status select form-select">
                  {% for status in status_activation %}
                    {% if status.code == object.es_status_activation.code %}
                      <option value="{{ status.code }}" selected>{{ status.description }}</option>
                    {% elif object.es_status_activation is None %}
                      {% if status.code == "no_info" %}
                        <option value="{{ status.code }}" selected>{{ status.description }}</option>
                      {% else %}
                        <option value="{{ status.code }}">{{ status.description }}</option>
                      {% endif %}
                    {% else %}
                      <option value="{{ status.code }}">{{ status.description }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
              <div class="col-3 mb-3 es_no_activation" id="id_altaiae">
                <label for="id_es_status_alta_iae" class="form-label">
                  Alta IAE:
                </label>
                <select id="id_es_status_alta_iae" name="es_status_altaiae" class="select-status select form-select">
                  {% for status in status_alta_iae %}
                    {% if status.code == object.es_status_altaiae.code %}
                      <option value="{{ status.code }}" selected>{{ status.description }}</option>
                    {% elif object.es_status_altaiae is None %}
                      {% if status.code == "no_info" %}
                        <option value="{{ status.code }}" selected>{{ status.description }}</option>
                      {% else %}
                        <option value="{{ status.code }}">{{ status.description }}</option>
                      {% endif %}
                    {% else %}
                      <option value="{{ status.code }}">{{ status.description }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
              <div class="col-3 mb-3 es_no_activation" id="id_vies">
                <label for="id_es_status_vies" class="form-label">
                  VIES:
                </label>
                <select id="id_es_status_vies" name="es_status_vies" class="select-status select form-select">
                  {% for status in status_vies %}
                    {% if status.code == object.es_status_vies.code %}
                      <option value="{{ status.code }}" selected>{{ status.description }}</option>
                    {% elif object.es_status_vies is None %}
                      {% if status.code == "no_info" %}
                        <option value="{{ status.code }}" selected>{{ status.description }}</option>
                      {% else %}
                        <option value="{{ status.code }}">{{ status.description }}</option>
                      {% endif %}
                    {% else %}
                      <option value="{{ status.code }}">{{ status.description }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="row">
              <div class="col-3 mb-3" id="id_cert">
                <label for="id_es_status_cdigital" class="form-label">
                  Cert digital:
                </label>
                <select id="id_es_status_cdigital" name="es_status_cdigital" class="select-status select form-select">
                  {% for status in status_c_digital %}
                    {% if status.code == object.es_status_cdigital.code %}
                      <option value="{{ status.code }}" selected>{{ status.description }}</option>
                    {% elif object.es_status_cdigital is None %}
                      {% if status.code == "no_info" %}
                        <option value="{{ status.code }}" selected>{{ status.description }}</option>
                      {% else %}
                        <option value="{{ status.code }}">{{ status.description }}</option>
                      {% endif %}
                    {% else %}
                      <option value="{{ status.code }}">{{ status.description }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
              <div class="col-3 mb-3" id="eoriselect" class="es_no_activation">
                <label for="id_es_status_eori" class="form-label">
                  EORI:
                </label>
                <select id="id_es_status_eori" name="es_status_eori" class="select-status select form-select">
                  {% for status in status_eori %}
                    {% if status.code == object.es_status_eori.code %}
                      <option value="{{ status.code }}" selected>{{ status.description }}</option>
                    {% elif object.es_status_eori is None %}
                      {% if status.code == "no_info" %}
                        <option value="{{ status.code }}" selected>{{ status.description }}</option>
                      {% else %}
                        <option value="{{ status.code }}">{{ status.description }}</option>
                      {% endif %}
                    {% else %}
                      <option value="{{ status.code }}">{{ status.description }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="col-12 mb-3">
            <div id="id_div_siret" style="display: none;">
              <label for="id_siret" class="form-label">
                SIRET (Francia)
              </label>
              <input
                type="text"
                maxlength="50"
                id="id_siret"
                name="siret"
                {% if object.siret %} value="{{ object.siret }}" {% endif %}
                class="textinput textInput form-control"
              />
              <div id="hint_id_siret" class="form-text">(Solo rellenar en caso de Francia)</div>
            </div>
            <div id="id_div_steuernummer" style="display: none;">
              <label for="id_steuernummer" class="form-label">
                STEUERNUMMER (Alemania)
              </label>
              <input
                type="text"
                maxlength="50"
                id="id_steuernummer"
                name="steuernummer"
                class="textinput textInput form-control"
                {% if object.steuernummer %} value="{{ object.steuernummer }}" {% endif %}
              />
              <div id="hint_id_steuernummer" class="form-text">(Solo rellenar en caso de Alemania/Austria)</div>
            </div>
          </div>
          <div id="id_div_italy_fields" style="display: none;">
            <div class= "row">
              {% if seller.legal_entity == 'self-employed' or seller.legal_entity == 'self-employed-outside'%}
                <div class="col-4 mb-3" id="div_codice_fiscale">
                  <label for="id_codice_fiscale" class="form-label">
                    CODICE FISCALE (Italia)
                  </label>
                  <input
                    type="text"
                    maxlength="50"
                    id="id_codice_fiscale"
                    name="codice_fiscale"
                    {% if object.codice_fiscale %} value="{{ object.codice_fiscale }}" {% endif %}
                    class="textinput textInput form-control"
                  />
                  <div id="hint_id_codice_fiscale" class="form-text">(Solo rellenar en caso de Italia)</div>
                </div>
              {% endif %}
              <div class="col-4 mb-3" id="div_it_representation_type">
                <label for="it_representation_type" class="form-label requiredField">
                  Tipo de representación:
                </label>
                <select id="it_representation_type" name="it_representation_type" class="select form-select">
                  {% for choice in form.it_representation_type.field.choices %}
                    {% if choice.0 == object.it_representation_type %}
                      <option value="{{ choice.0 }}" selected>{{ choice.1 }}</option>
                    {% else %}
                      <option value="{{ choice.0 }}">{{ choice.1 }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
              {% comment %} <div class="col mb-3" id="div_it_fiscal_representative">
                <label for="it_fiscal_representative" class="form-label requiredField">
                  Codice Fiscale del representante:
                </label>
                <input
                  type="text"
                  maxlength="100"
                  id="it_fiscal_representative"
                  name="it_fiscal_representative"
                  value="{{ object.it_fiscal_representative| default:'' }}"
                  class="textinput textInput form-control"
                />
              </div> {% endcomment %}
              {% comment %} <div class="col mb-3" id="div_it_fiscal_representative_name">
                <label for="it_fiscal_representative_name" class="form-label requiredField">
                  Nombre completo del representante:
                </label>
                <input
                  type="text"
                  maxlength="250"
                  id="it_fiscal_representative_name"
                  name="it_fiscal_representative_name"
                  value="{{ object.it_fiscal_representative_name| default:'' }}"
                  class="textinput textInput form-control"
                />
              </div> {% endcomment %}
              <div class="col-4 mb-3" id="div_vat_representative">
                <label for="vat_representative" class="form-label requiredField">
                  Representante:
                </label>
                <select id="vat_representative" name="vat_representative"  class="select form-select"
                        required="">
                  <option value="">---------</option>
                  {% for repre in representative %}
                    {% if repre.pk == object.vat_representative.pk %}
                      <option value="{{ repre.pk }}" selected>{{ repre.first_name }} {{ repre.last_name }}</option>
                    {% else %}
                      <option value="{{ repre.pk }}">{{ repre.first_name }} {{ repre.last_name }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="row">
              <div class="col-3 mb-3" id="id_div_contracting_date">
                <div>
                  <label for="id_contracting_date" class="form-label">
                    Fecha de Inicio de Contratación:
                  </label>
                  <input
                    type="date"
                    id="id_contracting_date"
                    name="contracting_date"
                    class="dateinput form-control"
                    {% if object.contracting_date %} value="{{ object.contracting_date|date:"Y-m-d" }}" {% endif %}
                  />
                </div>
              </div>
              <div class="col-3 mb-3" id="id_div_contracting_discontinue">
                <div>
                  <label for="id_contracting_discontinue" class="form-label">
                    Fecha de Contratación Baja:
                  </label>
                  <input
                    type="date"
                    id="id_contracting_discontinue"
                    name="contracting_discontinue"
                    class="dateinput form-control"
                    {% if object.contracting_discontinue %}
                    value="{{ object.contracting_discontinue|date:"Y-m-d" }}" {% endif %}
                  />
                </div>
              </div>
              <div class="col-3 mb-3" id="id_div_end_contracting_date">
                <div>
                  <label for="id_contracting_date" class="form-label">
                    Fecha de Fin de Contratación:
                  </label>
                  <input
                    type="date"
                    id="id_end_contracting_date"
                    name="end_contracting_date"
                    class="dateinput form-control"
                    {% if object.end_contracting_date %}
                    value="{{ object.end_contracting_date|date:"Y-m-d" }}" {% endif %}
                  />
                </div>
              </div>
              <div class="col-3 mb-3" id="id_div_activation_date">
                <label for="id_activation_date" class="form-label">
                  Fecha de Alta Nº IVA:
                </label>
                <input
                  type="date"
                  id="id_activation_date"
                  name="activation_date"
                  {% if object.activation_date %} value="{{ object.activation_date|date:"Y-m-d" }}" {% endif %}
                  class="dateinput form-control"
                />
              </div>
              <div class="col-3 mb-3" id="id_div_deactivation_date">
                <div>
                  <label for="id_deactivation_date" class="form-label">
                    Fecha de Baja Nº IVA:
                  </label>
                  <input
                    type="date"
                    id="id_deactivation_date"
                    name="deactivation_date"
                    {% if object.deactivation_date %} value="{{ object.deactivation_date|date:"Y-m-d" }}" {% endif %}
                    class="dateinput form-control"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="col-4 mb-3 mt-3">
            <div class="form-check">
              <input
                type="checkbox"
                id="id_vat_vies"
                name="vat_vies"
                class="checkboxinput form-check-input"
                {% if object.vat_vies == True %} checked="true" {% endif %}
              />
              <label for="id_vat_vies" class="form-check-label">
                VIES
              </label>
              <div id="hint_id_vat_vies" class="form-text">¿Esta el Vies Activo para el Nº IVA? Si:Activo |
                No:Inactivo
              </div>
            </div>
          </div>
          <div class="col-4 mb-3">
            <label for="id_period" class="form-label">
              Periodo de Presentación:
            </label>
            <select id="id_period" name="period" onclick="ShowHideQuarter()" class="select form-select" required>
              <option value="">---------</option>
              {% if object.period and object.period is not None %}
                {% for period in sellervat_period %}
                  {% if period.code == object.period.code %}
                    <option value="{{ period.code }}" selected>{{ period.description }}</option>
                  {% else %}
                    <option value="{{ period.code }}">{{ period.description }}</option>
                  {% endif %}
                {% endfor %}
              {% else %}
                {% for period in sellervat_period %}
                  {% if period.code == 'quarter' %}
                    <option value="{{ period.code }}" selected>{{ period.description }}</option>
                  {% else %}
                    <option value="{{ period.code }}">{{ period.description }}</option>
                  {% endif %}
                {% endfor %}
              {% endif %}
            </select>
          </div>
          <div class="col-4 mb-3">
            <div id="id_div_quarter" style="display: none;">
              <label for="id_quarter" class="form-label">
                Tipo de Trimestre:
              </label>
              <select id="id_quarter" name="quarter" class="select form-select">
                <option value="">---------</option>
                {% for quarter in sellervat_quarter %}
                  {% if quarter.code == object.quarter.code %}
                    <option value="{{ quarter.code }}" selected>{{ quarter.description }}</option>
                  {% else %}
                    <option value="{{ quarter.code }}">{{ quarter.description }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-4 mb-3 mt-3">
            <input
              type="checkbox"
              id="id_is_max_priority"
              onclick="another_selection_max_priority(); calculate_priority();"
              name="is_max_priority"
              class="checkboxinput form-check-input"
              {% if object.is_max_priority == True %} checked="true" {% endif %}
            />

            <label for="id_is_max_priority" class="form-check-label">
              Prioridad máxima
            </label>
            
            <div id="hint_id_is_max_priority" class="form-text">¿Es la prioridad máxima para el Nº IVA?</div>
            <input
              type="checkbox"
              id="another_check_priority"
              name="another_check_priority"
              class="checkboxinput form-check-input"
              {% if object.is_max_priority == True %} checked="true" {% endif %}
              hidden
            />
          </div>
          <div class="col-4 mb-3 mt-3">
            <div class="form-check">
              <input
                type="checkbox"
                id="is_local"
                name="is_local"
                class="checkboxinput form-check-input"
                {% if object.is_local == True %} checked="true" {% endif %}
              />
              <label for="is_local" class="form-check-label">
                Local
              </label>
              <div id="hint_is_local" class="form-text">¿Es el Nº IVA local? </div>
            </div>
          </div>
          <div class="col-4 mb-3 mt-3">
            <label for="id_payment_method_hmrc" class="form-label">
              Método de pago HMRC:
            </label>
            <select id="id_payment_method_hmrc" name="payment_method_hmrc" class="select form-select">
              {% for option in form.payment_method_hmrc.field.choices %}
                  <option value="{{ option.0 }}" {% if option.0 == form.payment_method_hmrc.value %} selected {% endif %}>
                      {{ option.1 }}
                  </option>
              {% endfor %}
          </select>
          </div>
          <div class="col-4 mb-3" id="div_manager_assigned">
            <label for="manager_assigned" class="form-label requiredField">
              Gestor Asignado:
            </label>
            <select id="manager_assigned" name="manager_assigned"  class="select form-select">
              <option value="">---------</option>
              {% for manager in manager_assigned %}
                {% if manager.pk == object.manager_assigned.pk %}
                  <option value="{{ manager.pk }}" selected>{{manager.name}}</option>
                {% else %}
                  <option value="{{ manager.pk }}">{{ manager.name }} </option>
                {% endif %}
              {% endfor %}
            </select>
          </div>
          <div class="col-12 mb-3">
            <label for="id_comment" class="form-label">
              Notas:
            </label>
            <textarea id="id_comment" name="comment" class="textarea form-control">{% if object.comment %}
              {{ object.comment }}{% endif %}</textarea>
          </div>
          <div class=" mt-2 mb-3 d-flex justify-content-center align-items-center">
            {% comment %} <a class="btn btn-secondary" href="{% url 'app_sellers:vat_list' seller.shortname  %}">Volver a listado de Países IVA</a> {% endcomment %}
            {% if object.pk is not None %}
              <button type="submit" id="submit_button" class="btn btn-primary">Actualizar</button>
            {% else %}
              <button type="submit" id="new_submit_button" class="btn btn-primary">Guardar</button>
            {% endif %}
            {% comment %} <button class="btn btn-warning" type="button" id="button_edit_on" onclick="edition_mode('on')" style="display:none;">Modo Edición</button>
            <button class="btn btn-warning" type="button" id="button_edit_off"onclick="edition_mode('off')" style="display:none;">Salir Edición</button>
            <button class="btn btn-secondary" type="button" id="show_days_requireds" onclick="showHideDaysRequired()" >Mostrar Acciones requeridas</button>
            <button class="btn btn-info" type="button" id="button-historial" onclick="showHideHistorial()" >Historial</button> {% endcomment %}
            <select id="mode_edit" name="mode_edit" hidden>
              <option value="off">off</option>
              <option value="on">on</option>
            </select>
          </div>
          <div id="address">
            <div class="row">
              <h3>Datos Dirección</h3>
              <hr>
            </div>
            <div class="row">
              <div class="col-3 mb-3">
                <label for="address_name" class="form-label ">
                  Nombre de la Dirección:
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address_name"
                  name="address_name"
                  {% for address in sellervat_address %}
                  value="{{ address.address_name }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                  required
                />
              </div>
              <div class="col-3 mb-3">
                <label for="address" class="form-label ">
                  Dirección:
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address"
                  name="address"
                  {% for address in sellervat_address %}
                  value="{{ address.address }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                />
              </div>
              <div class="col-3 mb-3">
                <label for="address_number" class="form-label ">
                  Número:
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address_number"
                  name="address_number"
                  {% for address in sellervat_address %}
                  value="{{ address.address_number }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                />
              </div>
              <div class="col-3 mb-3">
                <label for="address_continue" class="form-label ">
                  Dirección (Continuación):
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address_continue"
                  name="address_continue"
                  {% for address in sellervat_address %}
                  value="{{ address.address_continue }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-3 mb-3">
                <label for="address_zip" class="form-label ">
                  Código Postal:
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address_zip"
                  name="address_zip"
                  {% for address in sellervat_address %}
                  value="{{ address.address_zip }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                />
              </div>
              <div class="col-3 mb-3">
                <label for="address_city" class="form-label ">
                  Ciudad:
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address_city"
                  name="address_city"
                  {% for address in sellervat_address %}
                  value="{{ address.address_city }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                />
              </div>
              <div class="col-3 mb-3">
                <label for="address_country" class="form-label requiredField">
                  País:
                </label>
                <select id="address_country" name="address_country" class="select form-select" required="">
                  <option value="">---------</option>
                  {% if sellervat_address %}
                    {% for country in countries %}
                      {% for address in sellervat_address %}
                        {% if country.iso_code == address.address_country.iso_code %}
                          <option value="{{ country.iso_code }}" selected>{{ country.name }}</option>
                        {% else %}
                          <option value="{{ country.iso_code }}">{{ country.name }}</option>
                        {% endif %}
                      {% endfor %}
                    {% endfor %}
                  {% else %}
                    {% for country in countries %}
                      <option value="{{ country.iso_code }}">{{ country.name }}</option>
                    {% endfor %}
                  {% endif %}
                </select>
              </div>
              <div class="col-3 mb-3">
                <label for="address_catastral" class="form-label ">
                  Referencia Catastral:
                </label>
                <input
                  type="text"
                  maxlength="200"
                  id="address_catastral"
                  name="address_catastral"
                  {% for address in sellervat_address %}
                  value="{{ address.address_catastral }}"
                  {% endfor %}
                  class="textinput textInput form-control"
                />
              </div>
            </div>
            <br>
          </div>
          <div class="col-12 mb-3" id="required_days" style="display: none;">
            <div class="row">
              <h3 id="title_required_days">Días de Acción requeridos</h3>
              <hr>
            </div>
            <div class="row">
              <div class="col-3 mb-3">
                <input
                  type="date"
                  id="id_status_last_change_date"
                  name="status_last_change_date"
                  {% if object.status_last_change_date %}
                  value="{{ object.status_last_change_date|date:"Y-m-d" }}" {% endif %}
                  class="dateinput form-control"
                />
                <label for="id_status_priority" class="form-check-label" style="color:darkred;">
                  <b>Fecha desde Acción Requerida</b>
                </label></div>
              <div class="col-3 mb-3">
                <input
                  type="text"
                  maxlength="50"
                  id="id_status_last_change_days"
                  name="status_last_change_days"
                  value="{{ object.status_last_change_days }}"
                  class="textinput textInput form-control"
                />
                <label for="id_status_priority" class="form-check-label" style="color:darkred;">
                  <b>Acumulado días Acción Requerida | actual: {{ object.status_last_change_days }}</b>
                </label></div>
              <div class="col-3 mb-3">
                <input
                  type="date"
                  id="id_status_blue_last_change_date"
                  name="status_blue_last_change_date"
                  {% if object.status_blue_last_change_date %}
                  value="{{ object.status_blue_last_change_date|date:"Y-m-d" }}" {% endif %}
                  class="dateinput form-control "
                />
                <label for="id_status_priority" class="form-check-label" style="color:lightblue;">
                  <b>Fecha desde Acción Requerida Admón/gestor</b>
                </label></div>
              <div class="col-3 mb-3">
                <input
                  type="text"
                  maxlength="50"
                  id="id_status_blue_last_change_days"
                  name="status_blue_last_change_days"
                  value="{{ object.status_blue_last_change_days }}"
                  class="textinput textInput form-control "
                />
                <label for="id_status_priority" class="form-check-label" style="color:lightblue;">
                  <b>Acumulado días Acción Requerida Admón/gestor |
                    actual: {{ object.status_blue_last_change_days }} </b>
                </label></div>
            </div>
            <div class="row">
              <div class="col-3 mb-3">
                <input
                  type="date"
                  id="id_status_grey_last_change_date"
                  name="status_grey_last_change_date"
                  {% if object.status_grey_last_change_date %}
                  value="{{ object.status_grey_last_change_date|date:"Y-m-d" }}" {% endif %}
                  class="dateinput form-control "
                />
                <label for="id_status_priority" class="form-check-label" style="color:#666;">
                  <b>Fecha desde Acción Requerida Externa </b>
                </label>
              </div>
              <div class="col-3 mb-3">
                <input
                  type="text"
                  maxlength="50"
                  id="id_status_grey_last_change_days"
                  name="status_grey_last_change_days"
                  value="{{ object.status_grey_last_change_days }}"
                  class="textinput textInput form-control "
                />
                <label for="id_status_priority" class="form-check-label" style="color:#666;">
                  <b>Acumulado días Acción Requerida Externa | actual: {{ object.status_grey_last_change_days }}</b>
                </label></div>
              <div class="col-3 mb-3">
                <input
                  type="text"
                  maxlength="50"
                  id="total_promedy_days"
                  name="total_promedy_days"
                  value="{{ object.total_promedy_days }}"
                  class="textinput textInput form-control"
                  readonly
                />
                <label for="id_status_priority" class="form-check-label">
                  Promedio total de días acumulados | actual: {{ object.total_promedy_days }}
                </label></div>
              <div class="col-3 mb-3">
                <input
                  type="text"
                  maxlength="50"
                  id="id_status_priority"
                  name="status_priority"
                  value="{{ object.status_priority }}"
                  class="textinput textInput form-control"
                />
                <label for="id_status_priority" class="form-check-label">
                  Prioridad (0-4) | actual: {{ object.status_priority }}
                </label></div>
            </div>
            <div class="col-2 mb-3">
              <label for="id_status_process_color" class="form-label requiredField">
                Color del proceso predominante:
              </label>
              <select id="id_status_process_color" name="status_process_color" class="select form-select">
                <option value="">---------</option>
                {% for status_color in status_process_color %}
                  {% if status_color.code == object.status_process_color.code %}
                    <option value="{{ status_color.code }}" selected>{{ status_color.description }}</option>
                  {% else %}
                    <option value="{{ status_color.code }}">{{ status_color.description }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div id="historial" style="display: none;">
          <div class="row">
            <h3>Historial del proceso</h3>
            <hr>
          </div>
          {% if sellervat_history %}
            {% for history in sellervat_history %}
              {% if history_first.pk == history.pk %}
                <span style="font-weight: 600; color: #555;">
                  {{ history.user }} ha creado un registro en el historial el {{ history.created_at }}
                  </span>
              {% else %}
                <span style="font-weight: 600; color: #555;">
                  {{ history.user }} ha modificado el {{ history.created_at }}
                  </span>
              {% endif %}
              <ul>
                {% if history.vat_number %}
                  <li>Número de IVA a: <b>{{ history.vat_number }}</b></li>
                {% endif %}
                {% if history.vat_country %}
                  <li>País del IVA a: <b>{{ history.vat_country }}</b></li>
                {% endif %}
                {% if history.siret %}
                  <li>Siret a: <b>{{ history.siret }}</b></li>
                {% endif %}
                {% if history.steuernummer %}
                  <li>Steuernummer a: <b>{{ history.steuernummer }}</b></li>
                {% endif %}
                {% if history.vat_vies is not None %}
                  <li>VIES a:
                    {% if history.vat_vies == True %}
                      <b>Activo</b>
                    {% else %}
                      <b>Inactivo</b>
                    {% endif %}
                  </li>
                {% endif %}
                {% if history.is_contracted is not None %}
                  <li> Contratado a :
                    {% if history.is_contracted == True %}
                      <b>Activo</b>
                    {% else %}
                      <b>Inactvo</b>
                    {% endif %}
                  </li>
                {% endif %}
                {% if history.is_local is not None %}
                  <li> ¿Es local? a:
                    {% if history.is_local == True %}
                      <b>Si</b>
                    {% else %}
                      <b>No</b>
                    {% endif %}
                  </li>
                {% endif %}
                {% if history.contracting_date %}
                  <li> Fecha de Inicio de Contratación a: <b> {{ history.contracting_date }} </b></li>
                {% endif %}
                {% if history.contracting_discontinue %}
                  <li> Fecha de Contratación Baja a: <b>{{ history.contracting_discontinue }}</b></li>
                {% endif %}
                {% if history.end_contracting_date %}
                  <li> Fecha de Fin de Contratación a: <b>{{ history.end_contracting_date }}</b></li>
                {% endif %}
                {% if history.activation_date %}
                  <li> Fecha de Alta Nº IVA a: <b>{{ history.activation_date }}</b></li>
                {% endif %}
                {% if history.deactivation_date %}
                  <li> Fecha de Baja Nº IVA a: <b>{{ history.deactivation_date }}</b></li>
                {% endif %}
                {% if history.comment %}
                  <li> Ha añadido el siguiente comentario: <b>{{ history.comment }}</b></li>
                {% endif %}
                {% if history.es_status_activation %}
                  <li> ¿Quiere activación? a: <b>{{ history.es_status_activation }}</b></li>
                {% endif %}
                {% if history.es_status_altaiae %}
                  <li> Alta IAE a: <b>{{ history.es_status_altaiae }}</b></li>
                {% endif %}
                {% if history.es_status_cdigital %}
                  <li> Cert digital a: <b>{{ history.es_status_cdigital }}</b></li>
                {% endif %}
                {% if history.es_status_eori %}
                  <li> EORI a: <b>{{ history.es_status_eori }}</b></li>
                {% endif %}
                {% if history.es_status_vies %}
                  <li> VIES a: <b> {{ history.es_status_vies }} </b></li>
                {% endif %}
                {% if history.status_process %}
                  <li> Estado del proceso a: <b>{{ history.status_process }}</b></li>
                {% endif %}
                {% if history.is_max_priority is not None %}
                  <li> ¿Prioridad máxima? a:
                    {% if history.is_max_priority == True %}
                      <b>Si</b>
                    {% else %}
                      <b>No</b>
                    {% endif %}
                  </li>
                {% endif %}
                {% if history.type %}
                  <li> Tipo de proceso a: <b> {{ history.type }} </b></li>
                {% endif %}
                {% if history.vat_status %}
                  <li> Estado del País IVA a: <b>{{ history.vat_status }}</b></li>
                {% endif %}
                {% if history.deactivation_date %}
                  <li> Fecha de Baja Nº a: <b>{{ history.deactivation_date }}</b></li>
                {% endif %}
                {% if history.period %}
                  <li> Periodo de Presentación a: <b>  {{ history.period }}</b></li>
                {% endif %}
                {% if history.quarter %}
                  <li> Tipo de Trimestre a: <b>{{ history.quarter }}</b></li>
                {% endif %}
                {% if history.address_name %}
                  <li> Nombre de la Dirección a: <b>{{ history.address_name }}</b></li>
                {% endif %}
                {% if history.address %}
                  <li> Dirección a: <b>{{ history.address }} </b></li>
                {% endif %}
                {% if history.address_number %}
                  <li> Número a: <b>{{ history.address_number }} </b></li>
                {% endif %}
                {% if history.address_continue %}
                  <li> Dirección (Continuación) a: <b>{{ history.address_continue }}</b></li>
                {% endif %}
                {% if history.address_zip %}
                  <li> Código Postal a: <b>{{ history.address_zip }}</b></li>
                {% endif %}
                {% if history.address_city %}
                  <li> Ciudad a: <b>{{ history.address_city }}</b></li>
                {% endif %}
                {% if history.address_country %}
                  <li> País a: <b>{{ history.address_country }}</b></li>
                {% endif %}
                {% if history.address_catastral %}
                  <li> Referencia Catastral a: <b>{{ history.address_catastral }}</b></li>
                {% endif %}
              </ul>
            {% endfor %}
          {% else %}
            El historial está vacío
          {% endif %}
        </div>
      </form>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
  <script>
    // ------ < LISTENERS > ------

    //ORDENAR EL DOM
    //Escuchar todos los elementos del DOM página cargada
    document.addEventListener("DOMContentLoaded", function () {
      setTimeout(function () {
      }, 1);
      console.log("--------DOM LOAD--------");
      all_functions()
      console.log("----------")
    });

    //Escuchar todos los eventos de tipo select
    document.querySelectorAll("select").forEach((element) => {
      element.addEventListener("change", (event) => {
        if (document.getElementById("mode_edit").value == "off") {
          console.log("--------SELECT--------");
          all_functions()
          console.log("----------")
        } else if (document.getElementById("mode_edit").value == "on") {

          let status_process = document.getElementById("id_status_process");
          let quiere_act = document.getElementById("id_es_status_activation");
          let cdigital = document.getElementById("id_es_status_cdigital");
          let eoiri = document.getElementById("id_es_status_eori");
          let alta_iae = document.getElementById("id_es_status_alta_iae");
          let vies = document.getElementById("id_es_status_vies");

          if (status_process.value != "notified_client") {
            quiere_act.value = "no_info"
            cdigital.value = "no_info"
          }
          if (quiere_act.value != "yes") {
            alta_iae.value = "no_info"
          }
          if (alta_iae.value != "notified_client") {
            vies.value = "no_info"
          }
          if (cdigital.value != "notified_client") {
            eoiri.value = "no_info"
          }
        }
      });
    });

    //Escuchar el check "Contratado"
    document.getElementById("id_is_contracted").addEventListener("change", function (event) {
      if (document.getElementById("mode_edit").value == "off") {
        console.log("--------CHECKBOX--------");
        all_functions()
        console.log("----------")
      }
    });

    // ------ < FUNCIONES > ------

    //*Calcular prioridad
    const calculate_priority = () => {
      console.log("calculate_priority")
      let status_priority = document.getElementById('id_status_priority');
      let vat_status = document.getElementById("id_vat_status");
      let is_max_priority = document.getElementById('id_is_max_priority').checked;
      let is_contracted = document.getElementById('id_is_contracted').checked;
      let red_days = parseInt(document.getElementById('id_status_last_change_days').value);
      let blue_days = parseInt(document.getElementById('id_status_blue_last_change_days').value);
      let grey_days = parseInt(document.getElementById('id_status_grey_last_change_days').value);
      let color_priority = document.getElementById('id_status_process_color').value;

      let total_days_priority;

      if (isNaN(red_days)) {
        red_days = 0
        document.getElementById('id_status_last_change_days').value = 0

      }
      if (isNaN(blue_days)) {
        blue_days = 0
        document.getElementById('id_status_blue_last_change_days').value = 0

      }
      if (isNaN(grey_days)) {
        grey_days = 0
        document.getElementById('id_status_grey_last_change_days').value = 0

      }

      //PONER LOS DIAS EN LA FUNCION QUE CALCULA DIAS
      switch (color_priority) {
        case "red":
          total_days_priority = red_days + grey_days * 0.75 + blue_days * 0.5;
          break;
        case "grey":
          total_days_priority = red_days * 0.5 + grey_days + blue_days * 0.5;
          break;
        case "blue":
          total_days_priority = red_days * 0.5 + grey_days * 0.5 + blue_days;
          break;
        default:
          total_days_priority = 0;
      }

      document.getElementById("total_promedy_days").value = total_days_priority;

      {% comment %} console.log(typeof(document.getElementById("total_promedy_days").value));
      console.log("promedio dias " +  (document.getElementById("total_promedy_days").value = total_days_priority));
      console.log("red days: " + red_days+ " blue days " + blue_days + " grey days" + grey_days ) {% endcomment %}

      console.log("promedio dias " + total_days_priority)

      if (is_contracted == true) {
        if (is_max_priority) {
          status_priority.value = "4";
        } else if (vat_status.value == 'on') {
          status_priority.value = "0";
        } else if (total_days_priority < 6) {
          status_priority.value = "1";
        } else if (total_days_priority > 5 && total_days_priority < 11) {
          status_priority.value = "2";
        } else if (total_days_priority > 10) {
          status_priority.value = "3";
        } else {
          status_priority.value = "1";
        }
      } else if (is_contracted != true) {
        status_priority.value = "0";
      }
    }

    //Mostrar-ocultar campos que dependen del Estado del proceso -> "Notificado al cliente (verde)"
    const ShowHideOther = () => {
      console.log("ShowHideOther");
      const country = document.getElementById("id_vat_country");
      const div_siret = document.getElementById("id_div_siret");
      const div_steuernummer = document.getElementById("id_div_steuernummer");
      const div_codice_fiscale = document.getElementById("id_div_codice_fiscale");
      const div_italy = document.getElementById("id_div_italy_fields");
      const div_es = document.getElementById("id_div_es_status");
      const div_es_status_activation = document.getElementById("div_es_status_activation");
      const div_altaiae = document.getElementById("id_altaiae");
      const div_vies = document.getElementById("id_vies");
      const div_eori = document.getElementById("eoriselect");
      const div_cert_dig = document.getElementById("id_cert");
      const div_num_iva = document.getElementById("div_num_iva");
      const div_address = document.getElementById("address");

      const id_es_status_alta_iae = document.getElementById("id_es_status_alta_iae").value;
      const id_es_status_vies = document.getElementById("id_es_status_vies");
      const id_status_process = document.getElementById("id_status_process").value;
      const id_es_status_activation = document.getElementById("id_es_status_activation").value;
      const id_type_process = document.getElementById("id_type_process");
      const id_es_status_cdigital = document.getElementById("id_es_status_cdigital").value;
      const id_es_status_eori = document.getElementById("id_es_status_eori");
      const id_it_representation_type = document.getElementById("it_representation_type").value;
      const id_address_name = document.getElementById("address_name");
      const id_address_country = document.getElementById("address_country");
      const button_on = document.getElementById("button_edit_on");
      const button_off = document.getElementById("button_edit_off");

      div_steuernummer.style.display = (country.value == "DE" || country.value == "AT") ? "block" : "none"; 
      if (country.value != "DE" && country.value != "AT") {
        document.getElementById("id_steuernummer").value = "";
      }

      div_siret.style.display = (country.value == "FR") ? "block" : "none";
      if (country.value != "FR") {
        document.getElementById("id_siret").value = "";
      }

      div_italy.style.display = (country.value == "IT") ? "block" : "none";
      if (country.value != "IT" || (id_status_process != "notified_client" && country.value == "IT")) {
        document.getElementById("it_representation_type").value = "";
        document.getElementById("vat_representative").value = "";
        //document.getElementById("it_fiscal_representative").value = "";
        //document.getElementById("it_fiscal_representative_name").value = "";
        if('{{seller.legal_entity}}' == 'self-employed'){
          document.getElementById("id_codice_fiscale").value = "";
          
        }
        div_italy.style.display = "none";
      }

      if(id_status_process == "notified_client" && country.value == "IT"){
        document.getElementById("it_representation_type").setAttribute("required", "");
        if('{{seller.legal_entity}}' == 'self-employed'){
          document.getElementById("id_codice_fiscale").setAttribute("required", "");
        }
        if(id_it_representation_type=='1'){
          //document.getElementById("it_fiscal_representative").setAttribute("required", "");
          //document.getElementById("it_fiscal_representative_name").setAttribute("required", "");
          document.getElementById("vat_representative").setAttribute("required", "");
        }else{
          //document.getElementById("it_fiscal_representative").removeAttribute("required");
          //document.getElementById("it_fiscal_representative_name").removeAttribute("required");
          document.getElementById("vat_representative").removeAttribute("required");
        }
      }else{
        document.getElementById("it_representation_type").removeAttribute("required");
        //document.getElementById("it_fiscal_representative").removeAttribute("required");
        //document.getElementById("it_fiscal_representative_name").removeAttribute("required");
        document.getElementById("vat_representative").removeAttribute("required");
        if('{{seller.legal_entity}}' == 'self-employed'){
          document.getElementById("id_codice_fiscale").removeAttribute("required");
        }
      }

      if (id_status_process == "notified_client" && country.value == "ES") {
        div_es.style.display = "block";

        div_es_status_activation.style.display = "block"
        div_cert_dig.style.display = "block"
        div_altaiae.style.display = "none";
        div_vies.style.display = "none";
        div_eori.style.display = "none";
        button_on.style.display = "block";

        if (id_es_status_activation == "yes") {
          div_es_status_activation.style.display = "none";
          div_altaiae.style.display = "block";
          if (id_es_status_alta_iae == "notified_client") {
            div_vies.style.display = "block";
            div_altaiae.style.display = "none";
          } else if (id_es_status_alta_iae != "notified_client") {
            div_vies.style.display = "none";
          }
        }

        if (id_es_status_cdigital == "notified_client" || id_es_status_cdigital == "yes_cdigital_but_we_dont_have_it") {
          div_eori.style.display = "block";
          div_cert_dig.style.display = "none";
        }
      } else {
        div_es.style.display = "none";
        button_on.style.display = "none";
        button_off.style.display = "none";
      }

      if (id_status_process == "notified_client" && country.value == "IT"){
        if (id_it_representation_type =="1"){
          //document.getElementById("div_it_fiscal_representative").style.display ="block";
          //document.getElementById("div_it_fiscal_representative_name").style.display ="block";
          document.getElementById("div_vat_representative").style.display ="block";
        } else{
          //document.getElementById("div_it_fiscal_representative").style.display ="none";
          //document.getElementById("div_it_fiscal_representative_name").style.display ="none";
          document.getElementById("div_vat_representative").style.display ="none";
          //document.getElementById("it_fiscal_representative").value = "";
          //document.getElementById("it_fiscal_representative_name").value = "";
          document.getElementById("vat_representative").value = "";
        }
      }

      if (id_status_process == "received_nif" || id_status_process == "information_uploaded_to_app" || id_status_process == "active_web_subscription_not_spain" || id_status_process == "notified_client") {

        div_num_iva.style.display = "block";
        div_address.style.display = "block";
        id_address_name.setAttribute("required", "");
        id_address_country.setAttribute("required", "");
        document.getElementById("id_vat_number").setAttribute("required", "");
      } else {
        div_num_iva.style.display = "none";
        div_address.style.display = "none";
        id_address_name.removeAttribute("required");
        id_address_country.removeAttribute("required");
        document.getElementById("id_vat_number").removeAttribute("required");
      }
    }

    //Mostrar-ocultar campo "Tipo de Trimestre" en función de -> "Período de Presentación"
    const ShowHideQuarter = (skipSetQuarter = false) => {
      console.log("ShowHideQuarter");
      var period = document.getElementById("id_period");
      var divQuarter = document.getElementById("id_div_quarter");
      divQuarter.style.display = (period.value == "quarter") ? "block" : "none";
      if (period.value != "quarter") {
        document.getElementById("id_quarter").value = "";
      } else if (!skipSetQuarter) {
        if (document.getElementById("id_quarter").value == "") {
          document.getElementById("id_quarter").value = "01";
        }
      }
    }

    //Pintar "Estado del proceso" por colores, setear "Fecha desde acción requerida" y calcular los días de "Acumulado de días Acción Requerida"
    const colorSelectStatus = () => {
      console.log("colorSelectStatus");
      //const selectors4 = document.getElementsByClassName("select-status");
      let is_max_priority = document.getElementById("id_is_max_priority");
      let another_check = document.getElementById("another_check_priority");
      let status_process = document.getElementById('id_status_process').value;
      let es_status_activation = document.getElementById('id_es_status_activation').value;
      let es_status_cdigital = document.getElementById('id_es_status_cdigital').value;
      let es_status_altaiae = document.getElementById('id_es_status_alta_iae').value;
      let es_status_eori = document.getElementById('id_es_status_eori').value;
      let es_status_vies = document.getElementById('id_es_status_vies').value;
      const status_priority = document.getElementById('id_status_priority');
      let vat_country = document.getElementById("id_vat_country").value;
      let type_process = document.getElementById("id_type_process").value;
      let vat_status = document.getElementById("id_vat_status").value;

      let redstatus = 0;
      let bluestatus = 0;
      let greystatus = 0;
      let greenstatus = 0;

      document.getElementById("id_status_process").classList.remove("option-green", "option-red", "option-grey", "option-blue");
      document.getElementById("id_es_status_activation").classList.remove("option-green", "option-red", "option-grey", "option-blue");
      document.getElementById("id_es_status_cdigital").classList.remove("option-green", "option-red", "option-grey", "option-blue");
      document.getElementById("id_es_status_eori").classList.remove("option-green", "option-red", "option-grey", "option-blue");
      document.getElementById("id_es_status_alta_iae").classList.remove("option-green", "option-red", "option-grey", "option-blue");
      document.getElementById("id_es_status_vies").classList.remove("option-green", "option-red", "option-grey", "option-blue");
      //LÓGICA DE COLORES DE LOS CAMPOS

      //ROJO campos vacíos -> "País del iva" || "Tipo de proceso" || "Estado del proceso"
      if (vat_country == "" || type_process == "" || status_process == "") {
        vat_country == "" ? document.getElementById("id_vat_country").classList.add("option-red") : undefined;
        type_process == "" ? document.getElementById("id_type_process").classList.add("option-red") : undefined;
        status_process == "" ? document.getElementById("id_status_process").classList.add("option-red") : undefined;
        redstatus == 1;
      }

      if (vat_country != "" || type_process != "" || status_process != "") {
        vat_country != "" ? document.getElementById("id_vat_country").classList.remove("option-red") : undefined;
        type_process != "" ? document.getElementById("id_type_process").classList.remove("option-red") : undefined;
        status_process != "" ? document.getElementById("id_status_process").classList.remove("option-red") : undefined;

        (vat_country != "" && type_process != "" && status_process != "") ? redstatus = 0 : redstatus = 1;
      }

      if (another_check.checked == true) {
        is_max_priority.checked = true;
        is_max_priority.value = "on";
      } else {
        is_max_priority.checked = false;
        //is_max_priority.value= false;
      }

      //Colores de los procesos con lógica determinada dependiendo del país
      switch (vat_country) {
        case 'ES':
          if (status_process != "notified_client") {

            //ROJO campo -> "Estado del proceso"
            if (status_process == "paused_by_teammate" || status_process == "information_received" || status_process == "received_nif" ||
              status_process == "information_uploaded_to_app" || status_process == "active_web_subscription_not_spain" || status_process == "no_info") {
              document.getElementById("id_status_process").classList.add("option-red");
              redstatus = 1;
            }
            //ROJO campo -> "Estado del proceso"
            if (status_process == "contracted") {
              document.getElementById("id_status_process").classList.add("option-red");
              is_max_priority.checked = true;
              is_max_priority.value = 'on';
              another_check.checked = false;
              //is_max_priority.value= false;
              //is_max_priority = document.getElementById("id_is_max_priority").checked;
              redstatus = 1;
            }
            //GRIS campo -> "Estado del proceso"
            if (status_process == "stopped_by_other_process" || status_process == "hold_by_client" || status_process == "information_requested") {
              document.getElementById("id_status_process").classList.add("option-grey");
              greystatus = 1;
            }
            //AZUL campo -> "Estado del proceso"
            if (status_process == "send_request_to_admin") {
              document.getElementById("id_status_process").classList.add("option-blue");
              bluestatus = 1;
            }

          } else if (status_process == "notified_client") {
            //Colores para los campos ocultos

            //VERDE
            if (status_process == "notified_client" || es_status_activation == "yes" || es_status_activation == "no" || es_status_cdigital == "notified_client" ||
              es_status_cdigital == "not_requested_digital_certificate" || es_status_cdigital.value == 'yes_cdigital_but_we_dont_have_it' || es_status_altaiae == "notified_client" || es_status_altaiae == "standby" ||
              es_status_eori == "notified_client" || es_status_vies == "notified_client" || es_status_vies == "not_requested_vies") {
              status_process == "notified_client" ? document.getElementById("id_status_process").classList.add("option-green") : undefined;
              (es_status_activation == "yes" || es_status_activation == "no") ? document.getElementById("id_es_status_activation").classList.add("option-green") : undefined;
              (es_status_cdigital == "not_requested_digital_certificate" || es_status_cdigital == "notified_client" || es_status_cdigital == 'yes_cdigital_but_we_dont_have_it') ? document.getElementById("id_es_status_cdigital").classList.add("option-green") : undefined;
              (es_status_altaiae == "notified_client" || es_status_altaiae == "standby") ? document.getElementById("id_es_status_alta_iae").classList.add("option-green") : undefined;
              es_status_eori == "notified_client" ? document.getElementById("id_es_status_eori").classList.add("option-green") : undefined;
              (es_status_vies == "notified_client" || es_status_vies == "not_requested_vies") ? document.getElementById("id_es_status_vies").classList.add("option-green") : undefined;

              greenstatus = 1
            }

            //GRIS campo -> "¿Quiere activación?"" || "Cert digital"
            if (es_status_activation == "asked" || es_status_cdigital == "send_payment_link" || es_status_cdigital == "information_requested") {

              es_status_activation == "asked" ? document.getElementById("id_es_status_activation").classList.add("option-grey") : undefined;
              (es_status_cdigital == "send_payment_link" || es_status_cdigital == "information_requested") ? document.getElementById("id_es_status_cdigital").classList.add("option-grey") : undefined;

              greystatus = 1;
            }
            //GRIS campo -> "Alta IAE"
            if (es_status_activation == "yes") {
              if (es_status_altaiae == "information_requested") {
                document.getElementById("id_es_status_alta_iae").classList.add("option-grey");
                greystatus = 1;
              }
            }
            // GRIS campo -> "VIES"
            if (es_status_altaiae == "notified_client") {
              if (es_status_vies == "information_requested") {
                document.getElementById("id_es_status_vies").classList.add("option-grey");
                greystatus = 1;
              }
            }
            //GRIS campo -> "EORI"
            if (es_status_cdigital == "notified_client" || es_status_cdigital == 'yes_cdigital_but_we_dont_have_it') {
              if (es_status_eori == "information_requested") {
                document.getElementById("id_es_status_eori").classList.add("option-grey");
                greystatus = 1;
              }
            }

            //AZUL campo -> "Cert digital"
            if (es_status_cdigital == "requested_digital_certificate") {
              if (es_status_cdigital == "requested_digital_certificate") {
                document.getElementById("id_es_status_cdigital").classList.add("option-blue");
                bluestatus = 1;
              }
            }
            //AZUL campo -> "EORI"
            if (es_status_cdigital == "notified_client" || es_status_cdigital == 'yes_cdigital_but_we_dont_have_it') {
              if (es_status_eori == "requested_eori") {
                document.getElementById("id_es_status_eori").classList.add("option-blue");
                bluestatus = 1;
              }
            }
            //AZUL campo -> "VIES"
            if (es_status_altaiae == "notified_client") {
              if (es_status_vies == "requested_vies") {
                document.getElementById("id_es_status_vies").classList.add("option-blue");
                bluestatus = 1;
              }
            }

            //ROJO campo -> "¿Quiere activación?"
            if (es_status_activation == "" || es_status_activation == "no_info") {

              (es_status_activation == "" || es_status_activation == "no_info") ? document.getElementById("id_es_status_activation").classList.add("option-red") : undefined;
              redstatus = 1;
            }

            //ROJO campo -> "Alta IAE"
            if (es_status_activation == "yes") {
              if (es_status_altaiae == "information_received" || es_status_altaiae == "requested_iae" || es_status_altaiae == "" || es_status_altaiae == "no_info") {
                document.getElementById("id_es_status_alta_iae").classList.add("option-red");
                redstatus = 1;
              }
            }
            //ROJO campo -> "VIES"
            if (es_status_altaiae == "notified_client") {
              if (es_status_vies == "information_received" || es_status_vies == "received_vies" || es_status_vies == "" || es_status_vies == "no_info") {
                document.getElementById("id_es_status_vies").classList.add("option-red");
                redstatus = 1;
              }
            }
            //ROJO campo "Cert digital"
            if (es_status_cdigital == "no_previous_documents" || es_status_cdigital == "has_previous_documents" || es_status_cdigital == "paid" || es_status_cdigital == "information_received" ||
              es_status_cdigital == "received_digital_certificate" || es_status_cdigital == "" || es_status_cdigital == "no_info") {
              if (es_status_cdigital == "no_previous_documents" || es_status_cdigital == "" || es_status_cdigital == "has_previous_documents" || es_status_cdigital == "paid" ||
                es_status_cdigital == "information_received" || es_status_cdigital == "received_digital_certificate" || es_status_cdigital == "no_info") {
                document.getElementById("id_es_status_cdigital").classList.add("option-red");
                redstatus = 1;
              }
            }
            //ROJO campo -> "EORI"
            if (es_status_cdigital == "notified_client" || es_status_cdigital == 'yes_cdigital_but_we_dont_have_it') {
              if (es_status_eori == "information_received" || es_status_eori == "received_eori" || es_status_eori == "" || es_status_eori == "no_info") {
                document.getElementById("id_es_status_eori").classList.add("option-red");
                redstatus = 1;
              }
            }
          }
          break;

        default:
          //VERDE campo -> "Estado del proceso"
          if (status_process === "notified_client") {
            document.getElementById("id_status_process").classList.add("option-green");
            greenstatus = 1
          }
          //ROJO campo -> "Estado del proceso"
          else if (status_process == "paused_by_teammate" || status_process == "information_received" || status_process == "received_nif" ||
            status_process == "information_uploaded_to_app" || status_process == "active_web_subscription_not_spain" || status_process == "no_info") {
            document.getElementById("id_status_process").classList.add("option-red");
            redstatus = 1;
          }
          //ROJO campo -> "Estado del proceso"
          else if (status_process == "contracted") {
            document.getElementById("id_status_process").classList.add("option-red");
            is_max_priority.checked = true;
            is_max_priority.value = 'on';
            another_check.checked = false;
            //is_max_priority.value= false;

            is_max_priority = document.getElementById("id_is_max_priority").checked;
            redstatus = 1;
          }
          //GRIS campo -> "Estado del proceso"
          else if (status_process == "stopped_by_other_process" || status_process == "hold_by_client" || status_process == "information_requested") {
            document.getElementById("id_status_process").classList.add("option-grey");
            greystatus = 1;
          }
          //AZUL campo -> "Estado del proceso"
          else if (status_process == "send_request_to_admin") {
            document.getElementById("id_status_process").classList.add("option-blue");
            bluestatus = 1;
          }
          //BLANCO
          else if (status_process == "no_info") {
            document.getElementById("id_status_process").classList.remove("option-green", "option-red", "option-grey", "option-blue");
          }
          //ROJO campo-> "Estado del proceso"
          else if (status_process == "paused_by_teammate" || status_process == "information_received" || status_process == "received_nif" || status_process == "information_uploaded_to_app" ||
            status_process == "active_web_subscription_not_spain") {
            document.getElementById("id_status_process").classList.add("option-red");
            redstatus = 1;
          }
      }

      //Color predominante del proceso
      if (redstatus == 1) {
        document.getElementById("id_status_process_color").value = "red";
      } else if (greystatus == 1 && redstatus == 0 && (bluestatus == 0 || bluestatus == 1)) {
        document.getElementById("id_status_process_color").value = "grey";
      } else if (bluestatus == 1 && redstatus == 0 && greystatus == 0) {
        document.getElementById("id_status_process_color").value = "blue";
      } else if (greenstatus == 1) {
        document.getElementById("id_status_process_color").value = "green";
      } else if (redstatus == 0 && greenstatus == 0 && bluestatus == 0 && greenstatus == 0) {
        document.getElementById("id_status_process_color").value = "white";
      }

      if (vat_status == "on") {
        is_max_priority.checked = false;
        is_max_priority.value = false;
        another_check.value = false;
        another_check.checked = false;
      }
      //--------------------------------------------------------------------------------------------------------------

      //---- < Bloque que calcula los días en función del color del estado > ----
      const statusLastChangeDate = document.getElementById('id_status_last_change_date');
      const statusBlueLastChangeDate = document.getElementById('id_status_blue_last_change_date');
      const statusGreyLastChangeDate = document.getElementById('id_status_grey_last_change_date');
      const currentDate = new Date().toISOString().split('T')[0];

      if (redstatus == 1) {
        const status_last_change_days = document.getElementById('id_status_last_change_days');
        statusBlueLastChangeDate.value = null;
        statusGreyLastChangeDate.value = null;
        if (statusLastChangeDate.value != "") {
          const date1 = new Date(statusLastChangeDate.value);
          const date2 = new Date(currentDate);
          const diffTime = Math.abs(date2 - date1);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          let intValue = parseInt(status_last_change_days.value, 10);

          if (isNaN(intValue)) {
            intValue = 0;
          }
          intValue += diffDays;

          status_last_change_days.value = intValue; //aqui habia un .toString()
          statusLastChangeDate.value = currentDate;
        } else {
          statusLastChangeDate.value = currentDate;
        }
      }

      if (bluestatus == 1) {
        const status_blue_last_change_days = document.getElementById('id_status_blue_last_change_days');
        statusGreyLastChangeDate.value = null;
        statusLastChangeDate.value = null;
        if (statusBlueLastChangeDate.value != "") {
          const date1 = new Date(statusBlueLastChangeDate.value);
          const date2 = new Date(currentDate);
          const diffTime = Math.abs(date2 - date1);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          let intValue = parseInt(status_blue_last_change_days.value, 10);

          if (isNaN(intValue)) {
            intValue = 0;
          }
          intValue += diffDays;

          status_blue_last_change_days.value = intValue;  //aqui habia un .toString()
          statusBlueLastChangeDate.value = currentDate;

        } else {
          statusBlueLastChangeDate.value = currentDate;
        }
      }

      if (greystatus == 1) {
        const status_grey_last_change_days = document.getElementById('id_status_grey_last_change_days');
        statusLastChangeDate.value = null;
        statusBlueLastChangeDate.value = null;
        if (statusGreyLastChangeDate.value != "") {
          const date1 = new Date(statusGreyLastChangeDate.value);
          const date2 = new Date(currentDate);
          const diffTime = Math.abs(date2 - date1);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          let intValue = parseInt(status_grey_last_change_days.value, 10);

          if (isNaN(intValue)) {
            intValue = 0;
          }
          intValue += diffDays;

          status_grey_last_change_days.value = intValue //aqui habia un .toString()
          statusGreyLastChangeDate.value = currentDate;

        } else {
          statusGreyLastChangeDate.value = currentDate;
        }
      }

      const status_blue_last_change_days = document.getElementById('id_status_blue_last_change_days'); //SACAR FUERA DE LAS CONDICIONALES LOS VALORES CUANDO ACABE
      if (status_blue_last_change_days.value == "None") {
        status_blue_last_change_days.value = "0";
      }

      const status_grey_last_change_days = document.getElementById('id_status_grey_last_change_days');
      if (status_grey_last_change_days.value == "None") {
        status_grey_last_change_days.value = "0";
      }

    }

    //Mostrar-ocultar campos / cambiar datos de "Estado del País IVA" en función de si está o no contratado
    const ShowHideFieldsContract = () => {
      console.log("ShowHideFieldsContract");
      let isContracted = document.getElementById("id_is_contracted").checked;
      let status_process = document.getElementById("id_status_process");
      let status = document.getElementById("id_vat_status");
      let divDeactivationDate = document.getElementById("id_div_deactivation_date");
      let div_es = document.getElementById("id_div_es_status");
      let process = document.getElementById("process");

      if (!isContracted) { //No contratado
        status.innerHTML = "";
        let sellervat_status = JSON.parse('{{ sellervat_status_json|safe  }}');
        for (let i = 0; i < sellervat_status.length; i++) {
          if (sellervat_status[i].contracted === false) {
            let option = document.createElement("option");
            option.value = sellervat_status[i].code;
            option.text = sellervat_status[i].description;
            if (sellervat_status[i].code == '{{ object.vat_status.code }}') {
              option.selected = true;
              console.log("{{ object.vat_status.code }}");
            }
            status.appendChild(option);
          }
        }
        process.style.display = "none";
        div_es.style.display = "none";
        status_process.removeAttribute("required");


      } else { //Contratado
        status.innerHTML = "";
        let sellervat_status = JSON.parse('{{ sellervat_status_json|safe  }}');
        for (let i = 0; i < sellervat_status.length; i++) {
          if (sellervat_status[i].contracted) {
            let option = document.createElement("option");
            option.value = sellervat_status[i].code;
            option.text = sellervat_status[i].description;
            if (sellervat_status[i].code == '{{ object.vat_status.code }}') {
              option.selected = true;
              console.log("{{ object.vat_status.code }}")
              ;
            }
            status.appendChild(option);
          }
        }
        process.style.display = "block";
        status_process.setAttribute("required", "");
      }

    }

    //Mostrar-ocultar fechas en función de ciertas condiciones
    const ShowHideFieldsDate = () => {
      console.log("ShowHideFieldsDate");
      const chkContracted = document.getElementById("id_is_contracted");
      const idTypeProcess = document.getElementById("id_type_process");
      const vat_status = document.getElementById("id_vat_status");
      const alta_iae = document.getElementById("id_es_status_alta_iae");

      //Inicio de Contratación:
      const divContractingDate = document.getElementById("id_div_contracting_date");
      //"Contratación Baja":
      const divContractingDiscontinue = document.getElementById("id_div_contracting_discontinue");
      //Fin de Contratación:
      const divEndContractingDate = document.getElementById("id_div_end_contracting_date");
      //Alta Nº IVA:
      const divActivationDate = document.getElementById("id_div_activation_date");
      //Baja Nª IVA:
      const divDeactivationDate = document.getElementById("id_div_deactivation_date");

      if (!chkContracted.checked) {

        if (idTypeProcess.value != "deactivation_vat") {
          if (vat_status.value == "active") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "none";
            divEndContractingDate.style.display = "block";

            divActivationDate.style.display = "block";
            document.getElementById("id_activation_date").setAttribute("required", "");

            divDeactivationDate.style.display = "none";
          } else if (vat_status.value == "off") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "block";
            divEndContractingDate.style.display = "block";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "block";
          }
        } else if (idTypeProcess.value == "deactivation_vat") {
          if (vat_status.value == "active") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "block";
            divEndContractingDate.style.display = "block";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "block";
          } else if (vat_status.value == "off") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "none";
            divEndContractingDate.style.display = "block";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "block";
          }
        }
      } else if (chkContracted.checked) {
        if (idTypeProcess.value != "deactivation_vat") {
          if (vat_status.value == "processing") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "none";
            divEndContractingDate.style.display = "none";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "none";
          }
          if (vat_status.value == "processing" && alta_iae.value != "notified_client") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "none";
            divEndContractingDate.style.display = "none";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "none";
          }
          if (vat_status.value == "processing" && alta_iae.value == "notified_client") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "none";
            divEndContractingDate.style.display = "none";

            divActivationDate.style.display = "block";
            document.getElementById("id_activation_date").setAttribute("required", "");

            divDeactivationDate.style.display = "none";
          } else if (vat_status.value == "on") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "none";
            divEndContractingDate.style.display = "none";

            divActivationDate.style.display = "block";
            document.getElementById("id_activation_date").setAttribute("required", "");

            divDeactivationDate.style.display = "none";
          }
        } else if (idTypeProcess.value == "deactivation_vat") {
          if (vat_status.value == "on") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "block";
            divEndContractingDate.style.display = "none";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "block";
          } else if (vat_status.value == "processing") {
            divContractingDate.style.display = "block";
            divContractingDiscontinue.style.display = "block";
            divEndContractingDate.style.display = "none";

            divActivationDate.style.display = "none";
            document.getElementById("id_activation_date").removeAttribute("required");

            divDeactivationDate.style.display = "none";
          }
        }
      }
    }

    //*Lógica del campo "Estado del País IVA" y "Tipo de proceso" en función del "Estado del proceso"
    const status_vat_country = () => {
      console.log("status_vat_country");
      let country = document.getElementById("id_vat_country").value;
      let status_process = document.getElementById("id_status_process").value;
      let es_status_activation = document.getElementById("id_es_status_activation");
      let es_status_alta_iae = document.getElementById("id_es_status_alta_iae");
      let es_status_vies = document.getElementById("id_es_status_vies");
      let es_status_cdigital = document.getElementById("id_es_status_cdigital");
      let es_status_eori = document.getElementById("id_es_status_eori");
      let type = document.getElementById("id_type_process").value;
      let quiere_act = document.getElementById("id_es_status_activation");
      let vat_status = document.getElementById("id_vat_status");
      //let status_piority = document.getElementById("id_status_priority"); //SOBRA
      let is_contracted = document.getElementById("id_is_contracted").checked;

      if (is_contracted) {
        document.getElementById("id_type_process").setAttribute("required", "");
        if (country == 'ES') {
          if (status_process == 'notified_client'
            && es_status_activation.value == 'yes'
            && es_status_alta_iae.value == 'standby'
            && (((es_status_cdigital.value == 'notified_client' || es_status_cdigital.value == 'yes_cdigital_but_we_dont_have_it') && es_status_eori.value == 'notified_client')
              || es_status_cdigital.value == 'not_requested_digital_certificate')
          ) {
            vat_status.value = 'on';
          } else if (status_process == 'notified_client'
            && es_status_activation.value == 'yes'
            && es_status_alta_iae.value == 'notified_client'
            && es_status_vies.value == 'notified_client'
            && (((es_status_cdigital.value == 'notified_client' || es_status_cdigital.value == 'yes_cdigital_but_we_dont_have_it') && es_status_eori.value == 'notified_client')
              || es_status_cdigital.value == 'not_requested_digital_certificate')
          ) {
            vat_status.value = 'on';
          } else if (status_process == 'notified_client'
            && es_status_activation.value == 'yes'
            && es_status_alta_iae.value == 'notified_client'
            && es_status_vies.value == 'not_requested_vies'
            && (((es_status_cdigital.value == 'notified_client' || es_status_cdigital.value == 'yes_cdigital_but_we_dont_have_it') && es_status_eori.value == 'notified_client')
              || es_status_cdigital.value == 'not_requested_digital_certificate')
          ) {
            vat_status.value = 'on';
          } else if (status_process == 'notified_client'
            && es_status_activation.value == 'no'
            && (((es_status_cdigital.value == 'notified_client' || es_status_cdigital.value == 'yes_cdigital_but_we_dont_have_it') && es_status_eori.value == 'notified_client')
              || es_status_cdigital.value == 'not_requested_digital_certificate')
          ) {

            vat_status.value = 'on';
          } else if ((type == 'deactivation_vat' && status_process == 'send_request_to_admin') || (type == "deactivation_maintenance" && status_process == 'send_request_to_admin')) {
            vat_status.value = 'on';
          } else if (status_process == 'hold_by_client') {
            vat_status.value = 'on';
          } else {
            vat_status.value = 'processing';
          }
          if (status_process != "notified_client") {
            quiere_act.value = "no_info";
            es_status_cdigital.value = "no_info";
            es_status_alta_iae.value = "no_info";
            es_status_vies.value = "no_info";
            es_status_eori.value = "no_info";
          }
        }
        if (country != 'ES') {
          if (status_process == 'notified_client') {
            vat_status.value = 'on';
          } else if (status_process == 'hold_by_client') {
            vat_status.value = 'on';
          } else {
            vat_status.value = 'processing'
          }
        }
        if ((type == 'deactivation_vat' && vat_status.value == 'on') || (type == "deactivation_maintenance" && vat_status.value == 'on')) {
          document.getElementById("id_is_contracted").value = false;
          document.getElementById("id_is_contracted").checked = false;
          document.getElementById("id_type_process").value = ""
          document.getElementById("id_type_process").removeAttribute("required");
          ShowHideFieldsContract();
          document.getElementById("id_vat_status").value = "off"
        }
      }
      if (!is_contracted) {
        document.getElementById("id_type_process").value = "";
        document.getElementById("id_type_process").removeAttribute("required");
      }
    }

    //Mantener el check de máxima prioridad cuando "Estado del proceso" != "Contratado"
    const another_selection_max_priority = () => {
      console.log("another_selection_max_priority");
      let is_max_priority = document.getElementById("id_is_max_priority");
      let another_check = document.getElementById("another_check_priority");
      let status_process = document.getElementById("id_status_process").value;
      let check_value = false

      if (status_process != "" && status_process != "contracted" && is_max_priority.checked == true) {
        another_check.value = true;
        another_check.checked = true;
      } else {
        another_check.value = false;
        another_check.checked = false;
      }
    }

    //Activar Modo Edición
    const edition_mode = (mode) => {
      const status_process = document.getElementById("id_status_process");
      const div_es = document.getElementById("id_div_es_status");
      const div_es_status_activation = document.getElementById("div_es_status_activation");
      const div_altaiae = document.getElementById("id_altaiae");
      const div_vies = document.getElementById("id_vies");
      const div_eori = document.getElementById("eoriselect");
      const div_cert_dig = document.getElementById("id_cert");
      const mode_process = document.getElementById("mode_edit").value;
      const button_on = document.getElementById("button_edit_on");
      const button_off = document.getElementById("button_edit_off");
      const submit_button = document.getElementById("submit_button");
      const new_submit_button = document.getElementById("new_submit_button");
      const pk = "{{object.pk}}"

      let quiere_act = document.getElementById("id_es_status_activation");
      let cdigital = document.getElementById("id_es_status_cdigital");
      let eoiri = document.getElementById("id_es_status_eori");
      let alta_iae = document.getElementById("id_es_status_alta_iae");
      let vies = document.getElementById("id_es_status_vies");

      const selectors_esp = document.getElementsByClassName("select-status");

      switch (mode) {
        case "on":
          document.getElementById("id_status_process").classList.remove("option-green", "option-red", "option-grey", "option-blue");
          quiere_act.classList.remove("option-green", "option-red", "option-grey", "option-blue");
          cdigital.classList.remove("option-green", "option-red", "option-grey", "option-blue");
          eoiri.classList.remove("option-green", "option-red", "option-grey", "option-blue");
          alta_iae.classList.remove("option-green", "option-red", "option-grey", "option-blue");
          vies.classList.remove("option-green", "option-red", "option-grey", "option-blue");

          document.getElementById("title_process").textContent = "Proceso - Modo edición";
          document.getElementById("mode_edit").value = "on"

          button_off.style.display = "block";
          button_on.style.display = "none";

          if (!pk) {
            new_submit_button.style.display = "none";
          }

          if (pk) {
            submit_button.style.display = "none";
          }

          //Se muestran  los campos del bloque español
          div_es.style.display = "block";
          div_es_status_activation.style.display = "block";
          div_altaiae.style.display = "block";
          div_vies.style.display = "block";
          div_eori.style.display = "block";
          div_cert_dig.style.display = "block";

          break;
        case "off":
          button_off.style.display = "none";
          button_on.style.display = "block";

          if (pk) {
            submit_button.style.display = "block";
          }
          if (!pk) {
            new_submit_button.style.display = "block";
          }

          document.getElementById("title_process").textContent = "Proceso";
          document.getElementById("mode_edit").value = "off"
          all_functions()

          break;
      }
    }

    //Función que contiene todas las funciones de la lógica en orden de ejecución
    const all_functions = () => {
      //ORDEN (IMPORTANTE RESPETAR ESTE ORDEN)
      ShowHideFieldsContract();                     //1.- Mostrar-ocultar campos -> "Contratado"/"No contratado"
      status_vat_country();                         //2.- Determinar "Estado del país IVA" en función de "estado del Proceso"
      colorSelectStatus();                          //3.- Pintar Estados del proceso y calcular días
      ShowHideOther();                              //4.- Mostrar-ocultar campos que depende del estado del proceso
      ShowHideQuarter();                            //5.- Mostrar-ocultar campo "Tipo trimestre"
      ShowHideFieldsDate();                         //6.- Mostrar-ocultar campos de fechas en función de toda la lógica anterior
      calculate_priority();                         //7.- Calcular la prioridad al finalizar la lógica
    }

    const showHideDaysRequired = () => {
      div_required_days = document.getElementById("required_days")

      if (div_required_days.style.display == "block") {
        div_required_days.style.display = "none";
      } else {
        div_required_days.style.display = "block";
      }
    }

    const showHideHistorial = () => {
      div_historial = document.getElementById("historial");

      console.log(div_historial)
      if (div_historial.style.display === "block") {
        div_historial.style.display = "none"
      } else {
        div_historial.style.display = "block"
      }
    }
  </script>
{% endblock javascripts %}
