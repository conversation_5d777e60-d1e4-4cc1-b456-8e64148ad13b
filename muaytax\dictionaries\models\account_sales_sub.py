from django.db import models

class SubAccountSales(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=10,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "SubCuenta de Ingresos"
        verbose_name_plural = "SubCuentas de Ingresos"
    
    def __str__(self):
        return self.description
    
# @admin.register(SubAccountSales)
# class SubAccountSalesAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
