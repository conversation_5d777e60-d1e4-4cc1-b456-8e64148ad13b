{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}">
  <!-- <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/dropzone/dropzone.min-v5.css' %}" type="text/css"/> -->
  <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css' %}"/>
  <link rel="stylesheet" href="{% static 'assets/datatables/fixedcolumns/5.0.1/css/fixedColumns.dataTables.css' %}" type="text/css"/>

  <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}">
  <link rel="stylesheet" href="{% static 'assets/css/plugins/notifier.css' %}">
  <link rel="stylesheet" href="{% static 'assets/css/custom-dropzone/custom-dropzone.css' %}" />

  <style>
    .uploadzone {
      width: 100%;
      height: 350px !important;
      border: 2px dashed rgba(0, 0, 0, .3);
      box-sizing: border-box;
      padding: 20px 20px;
      margin-bottom: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .uploadzone:hover {
      border-color: #031549;
    }

    .dropzone {
      width: 100%;
      min-height: 50vh !important;
      background-image: url("https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png");
      background-size: 85px;
      background-repeat: no-repeat;
      background-position: center 40%;
    }

    .dropzone .dz-message {
      display: block;
      position: absolute;
      top: 53%;
      width: 95%;
      text-align: center;
    }

    .dropzone .dz-preview.dz-error .dz-error-message {
      display: none !important;
    }

    .tooltip-inner a {
      color: white;
      text-decoration: none;
    }

    /* usa esto para cuando se usa fixed column */
    .table.dataTable thead tr > .dtfc-fixed-start{
      background-color: #748892!important;
    }
    .hidden-until-vue {
      display: none;
    }
    .custom-table-head {
      background: #748892;
      color: #fff;
    }
    .inv-type-btn{
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 5px;
      position: relative;
      width: 160px;
      height: 80px;
      padding: 10px 15px;
      border: 1px solid #03ad65;
      color: #03ad65;
      border-radius: .25rem;
      align-items: center;
      cursor: pointer;
      text-align: center;
      transition: all 0.3s ease-in-out;
      overflow: hidden;
    }
    .inv-type-btn.active {
      background-color: #03ad65;
      color: white;
    }
    .inv-type-btn:not(.active):hover {
      background-color: #03ad65;
      opacity: 0.75;
      color: white;
    }

  </style>
{% endblock stylesheets %}
{% block title %}
  Lector de Facturas
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas: Lector de Facturas
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href=".">Facturas</a>
                </li>
                <li class="breadcrumb-item">
                  <a href="./upload">Lector de Facturas</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <!-- Toast | START  -->
    <div
      id="toast"
      class="toast text-white bg-success w-100 fade border"
      data-bs-animation="true"
      data-bs-autohide="true"
      data-bs-delay="5000"
      role="alert"
    >
      <div class="d-flex">
        <div id="toast-body" class="toast-body">
          <span id="toast-text"></span>
        </div>
        <button
          type="button"
          class="btn-close btn-close-white me-2 m-auto"
          data-bs-dismiss="toast"
        ></button>
      </div>
    </div>
    <!-- Toast | END  -->

    <div class="vue" id="uploads">
      <!-- Errores | START  -->
      {% if error %}
        <div class="col-12 alert alert-danger text-center mx-auto">
          {{ error | safe }}
        </div>
      {% endif %}
      <!-- Errores | END  -->
      
      <!-- UPLOADER START -->
      {% if not canShowInvoices and not canShowTXT %}
        <div class="d-flex justify-content-center align-items-center mw-36-rem mx-auto dvh-75">
          <div class="card rounded border">
            <div class="card-block">
              <div class="d-flex gap-3 align-items-center mb-3">
                <i class="bi bi-exclamation-octagon fa-3x text-danger"></i>
                <h4 class="mb-0 text-danger">
                  <b>{% if cantShowTitle %} {{ cantShowTitle }} {% else %}Cargador de Facturas no disponible.{% endif %}</b>
                </h4>
              </div>
              <div class="row mb-3">
                <div class="col-12">
                  <p>{% if cantShowMessage %}{{ cantShowMessage }}
                    {% else %}
                    Actualmente, no tienes servicios de contabilidad contratados ni países IVA registrados.  
                    Para subir tus facturas, es necesario contratar al menos uno de estos servicios.
                    {% endif %}
                  </p>
                  <p>Si deseas contratar algún servicio o necesitas ayuda, por favor contacta con nuestro equipo de soporte.</p>
                </div>
              </div>
              <div class="d-flex justify-content-end align-items-center">
                <a href="{% url 'app_bookings:new_booking' seller.shortname %}" class="btn btn-dark">Contactar Soporte</a>
              </div>
            </div>
          </div>
        </div>
      {% elif economic_activity.count == 0 %}
        <div class="d-flex justify-content-center align-items-center mw-36-rem mx-auto dvh-75">
          <div class="card rounded border">
            <div class="card-body">
              <div class="d-flex gap-3 align-items-center mb-3">
                <i class="bi bi-exclamation-octagon fa-3x text-danger"></i>
                <h4 class="mb-0 text-danger"><b>No hay actividades económicas disponibles.</b></h4>
              </div>
              <div class="row mb-3">
                <div class="col-12">
                  <p>No puedes subir facturas porque no tienes ningún IAE asociado.</p>
                  <p>
                    Puedes añadir los paises en los que operas desde <a href="{% url 'app_sellers:vat_list' seller.shortname %}">aqui</a> y añadiremos el IAE 665 automaticamente para esos paises.
                    Si tienes alguna otra duda sobre tus epígrafes por favor contacta con soporte para solucionar el problema
                  </p>
                </div>
              </div>
              <div class="d-flex justify-content-end align-items-center">
                <a href="{% url 'app_bookings:new_booking' seller.shortname %}" class="btn btn-outline-secondary">Contactar Soporte</a>
                <a href="{% url 'app_sellers:vat_list' seller.shortname %}" class="btn btn-dark">Añadir País IVA</a>
              </div>
            </div>
          </div>
        </div>
      {% else %}
        <div class="col-12">
          <div class="card rounded">
            <div class="py-5 container">
              <div class="card rounded mb-0 shadow-none border">
                <div class="card-body">
                  <!-- Título y botones-->
                  <div class="row mb-3">
                    <div class="col-12 mb-3">
                      <h4 class="text-center text-dark fw-bold">Tipo de Documento</h4>
                    </div>
                    <div class="col-12 mb-3">
                      <div class="d-flex flex-wrap gap-3 justify-content-center">
                        {% if canShowInvoices %}
                          <!-- Mostrar todos los botones si se pueden subir facturas -->
                          <div class="inv-type-btn" @click="clickInvoiceType('invoice')">
                            <i class="fa-regular fa-file-lines fa-2x"></i>
                            <p class="mb-0">Facturas</p>
                          </div>
                          <div class="inv-type-btn" @click="loadImportWizard()">
                            <i class="fa-solid fa-truck fa-2x"></i>
                            <p class="mb-0">Importaciones</p>
                          </div>
                          <div class="inv-type-btn" id="amz-txt-btn" @click="clickInvoiceType('amz-txt-eur')">
                            <i class="fa-solid fa-bag-shopping fa-2x"></i>
                            <p class="mb-0">Ventas Amazon</p>
                          </div>
                          <div class="inv-type-btn" @click="clickInvoiceType('payroll')">
                            <i class="fa-solid fa-user-group fa-2x"></i>
                            <p class="mb-0">Nóminas</p>
                          </div>
                        {% elif canShowTXT %}
                          <!-- Solo mostrar Amazon si solo está habilitado el TXT -->
                          <div class="inv-type-btn" id="amz-txt-btn" @click="clickInvoiceType('amz-txt-eur')">
                            <i class="fa-solid fa-bag-shopping fa-2x"></i>
                            <p class="mb-0">Ventas Amazon</p>
                          </div>
                        {% endif %}
                      </div>
                      
                    </div>
                  </div>
                  <!-- Seleccion de IAE -->
                  <div class="row mb-3 hidden-until-vue" id="uploadiae" v-if="!inputEconomicActivity && (inputUploadType == 'amz-txt-eur' || (inputUploadType != '' && (limitInvoices == -1 || totalInvoices < limitInvoices)))">
                    <div class="col-12"  >
                        <div class="text-center">
                          <h4 class="text-dark fw-bold mb-3">
                            Ahora selecciona el tipo de actividad económica.
                          </h4>
                        </div>
                        <div class="d-flex justify-content-center my-3">
                          <div class="col-md-6">
                            <select class="form-select form-control" id="inputEconomicActivity" v-model="inputEconomicActivity">
                              <option :value="null" selected>Desconocida</option>
                              <option v-for="iae in dj.economic_activity" :key="iae.pk" :value="iae.pk">
                                [[ iae.description ]]
                              </option>
                            </select>
                          </div>
                        </div>
                    </div>
                  </div>

                  <!-- Limit reached message -->
                  <div class="row hidden-until-vue" id="limit-reached" v-if="inputUploadType!='' && inputUploadType!='amz-txt-eur' && limitInvoices > 0 && totalInvoices >= limitInvoices">
                    <div class="col-12 mt-3">
                      <div class="container">
                        <div class="alert alert-warning rounded" role="alert">
                          <div class="d-flex gap-3 align-items-center">
                            <i class="bi bi-exclamation-octagon fa-2x text-danger"></i>
                            <div class="d-flex flex-column gap-1">
                              <h4 class="mb-0 text-danger fw-bold">¡Ooops! Límite alcanzado</h4>
                              <p class="mb-0">No puedes subir más facturas porque has alcanzado tu límite mensual</p>
                            </div>
                          </div>
                        </div>
                        <p>Entendemos que las necesidades de tu negocio pueden requerir un límite de carga más alto. 
                          Nuestro equipo de soporte está a tu disposición para ayudarte a ampliar tu capacidad y adaptarla mejor a tus requisitos.
                        </p>
                        <p>Por favor, contacta a nuestro equipo de soporte para analizar las opciones disponibles para incrementar tu 
                          límite de carga o para explorar nuestros diferentes planes, que ofrecen capacidades mejoradas.
                        </p>
                        <div class="d-flex justify-content-end">
                          <a href="{% url 'app_bookings:new_booking' seller.shortname %}" class="btn btn-dark">Contactar Soporte</a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Dropzone Area -->
                  <div class="row" id="dropzone-areas" v-show="limitInvoices == -1 || totalInvoices < limitInvoices">
                    <div class="col-12 mt-3">
                      <!-- 1. MAIN INVOICE UPLOADER -->
                      <div class="container" v-show="inputUploadType=='invoice' && inputEconomicActivity">
                        <div
                          class="dropzone-instance"
                          data-dropzone-id="invoices_dropzone"
                          data-form-user="seller"
                          data-form-url="{% url 'app_invoices:seller_invoice_process' seller.shortname %}"
                          :data-form-iae="inputEconomicActivity"
                          data-options-name="factura"
                          data-options-name-plural="Facturas"
                          data-options-img="{% static 'assets/images/custom-dropzone/invoice_dropzone.svg' %}"
                          >
                        </div>
                      </div>
                      <!-- 1. MAIN INVOICE UPLOADER -->
                      <!-- 2. PAYROLL UPLOADER -->
                      <div class="container" v-show="inputUploadType=='payroll' && inputEconomicActivity">
                        <div
                          class="dropzone-instance"
                          data-dropzone-id="payroll_dropzone"
                          data-form-user="seller"
                          data-form-url="{% url 'app_invoices:seller_invoice_process' seller.shortname %}?invoice_type=payroll"
                          :data-form-iae="inputEconomicActivity"
                          data-options-name="nómina"
                          data-options-name-plural="Nóminas"
                          data-options-img="{% static 'assets/images/custom-dropzone/invoice_dropzone.svg' %}"
                          >
                        </div>

                      </div>
                      <!-- 2. PAYROLL UPLOADER -->
                
                    </div>
                  </div>

                  <!-- 3. AMAZON TXT UPLOADER -->
                  <div v-show="inputUploadType=='amz-txt-eur' && inputEconomicActivity" style="display:none;">
                    <div class="container">
                        <form 
                          id="form-uploadtxt"
                          method="post" 
                          class="d-flex flex-column justify-content-center  w-100"
                          enctype="multipart/form-data"
                          action="{% url 'app_invoices:seller_invoice_create_txt' seller.shortname %}">
                          <div class="uploadzone rounded d-flex justify-content-center align-items-center text-center">
                            {% csrf_token %}
                            <div class="d-none d-print-none">
                              <!-- change d-x-none to d-none -->
                              <!-- change type text to hidden -->
                              <input id="id" type="hidden" name="{{ form_create.seller.name }}" value="{{ seller.pk }}"/>
                              <input id="invoice_category" type="hidden" name="invoice_category" v-model="inputCategoriaSales" />
                              <input type="hidden" id="iae" name="iae" v-model="inputEconomicActivity"/>
                              <input type="hidden" id="invoice_type" name="invoice_type" v-model="inputTypeAmzTxt"/>
                            </div>
                            <div>
                              <p for="file">*Solo válido para TXT de Amazon Europa. <br> Contactad a soporte para otros reportes.
                              </p>
                              <br>
                              <div id="fallback" class="fallback">
                                <input type="file" id="id_file_txt" name="file" class="form-control text-center w-100 mx-auto"/>
                                <br><br><br>
                              </div>
                              <div id="spinner" style="display: none;">
                                <div class="spinner-border m-3" role="status">
                                  <span class="sr-only">Cargando...</span>
                                </div>
                                <div>
                                  <h4>Tu TXT se está procesando.</h4>
                                </div>
                              </div>
                            </div>
                            <div id="spinner" style="display: none;">
                              <div class="spinner-border m-5" role="status">
                                <span class="sr-only">Cargando...</span>
                              </div>
                            </div>
                          </div>

                          <div class="row">
                            <div class="mt-3 col-12 text-center">
                              {% comment %}<input type="submit" id="txt-submit-button" value="Continuar" class="btn btn-primary" disabled>{% endcomment %}
                              <button id="txt-submit-button" type="submit" class="btn btn-navy lft-hover m-0 col-12 col-md-5 btn-submit-dropzone" disabled="">
                                  <i class="bi bi-file-earmark-arrow-up me-2"></i>
                                  Subir txt
                              </button>
                            </div>
                          </div>

                        </form>
                    </div>
                  </div>
                  <!-- 3. AMAZON TXT UPLOADER -->
                </div>

                <div class="card-footer">
                  <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                      <div class="">
                        <span class="fw-light text-muted">Subidas: </span>
                        <span id="total-invoices-uploaded" class="text-dark fw-bold">{{ total_invoices }}</span>
                        <span class="mx-2">|</span>
                        <span class="fw-light text-muted">Límite: </span>
                        <span id="total-invoices-limit" class="text-dark fw-bold">{{ limit_invoices }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AMAZON TXT TABLE -->
        {% if canShowTXT is not None and canShowTXT == True and amz_txt_eur %}
          <div class="col-12 my-0 hidden-until-vue" v-if="inputUploadType=='amz-txt-eur'">
            <div class="card rounded">
              <div class="card-body">
                <div class="container">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="text-dark fw-bold">Informes de Amazon cargados</h4>
                  </div>
                  <div class="text-nowrap table-responsive">
                    <table id="txt-list-table" class="table row-border compact hover order-column nowrap dataTable-table" style="width: 100%;">
                      <thead class="custom-table-head">
                      <tr>
                        <th>Nombre Fichero</th>
                        <th>Amazon ID</th>
                        <th>Mes</th>
                        <th>Año</th>
                        <th>Fecha Carga</th>
                        <th style="width:5%;">Estado</th>
                        <th style="width:5%;">Acciones</th>
                      </tr>
                      </thead>
                      <tbody>
                      {% for object in amz_txt_eur %}
                        <tr>
                          <td class="align-middle">
                            <span>{{ object.file|default:"Sin nombre" }}</span>
                          </td>
                          <td class="align-middle">
                            <span>{{ object.amz_id|default:"-" }}</span>
                          </td>
                          <td class="align-middle">
                            <span>{{ object.month|default:"-" }}</span>
                          </td>
                          <td class="align-middle">
                            <span>{{ object.year|default:"-" }}</span>
                          </td>
                          <td class="align-middle">
                            {{ object.created_at }}
                          </td>
                          <td class="align-middle">
                            <h5 class="text-center mb-0">
                              <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-html="true"
                                {% if object.error_message != None %}
                                    data-bs-title="{{ object.error_message }}"
                                {% endif %}
                                {% if object.error_message == None %}
                                    data-bs-title="Sin información"
                                {% endif %}
                              >
                                {% if object.status.code == "processed" %}
                                  <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                                {% elif object.status.code == "failed" %}
                                  <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                                {% else %}
                                  <i class="fa-regular fa-xl fa-circle-question" style="color: #ff9500;"></i>
                                {% endif %}
                              </span>
                            </h5>
                          </td>
                          <td class="align-middle">
                            <div>
                              <a class="btn btn-icon btn-info mb-0" href="{{ object.get_file_url }}" target="_blank" download>
                                <i class="fa-solid fa-download"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                      {% endfor %}
                      </tbody>
                    </table>
                  </div>

                </div>
              </div>
            </div>
          </div>
        {% endif %}
        <!-- AMAZON TXT TABLE -->
      {% endif %}
      <!-- UPLOADER END -->
    </div>

    <!-- make a modal that says that now you can upload the tikets receipts and fees in the invoices buttons -->
    <div class="modal fade" id="modalNewUploaderInfo" tabindex="-1" aria-labelledby="modalNewUploaderInfoLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <i class="bi bi-info-circle text-warning fa-2x fw-bold me-2"></i>
            <h5 class="modal-title" id="modalNewUploaderInfoLabel">Actualización del cargador</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Desde ahora puedes subir tus tickets, recibos y cuotas de autónomo en el apartado de facturas.</p>
            <p>Para ello, selecciona <span class="text-dark fw-bold">"Facturas" </span>en el tipo de documento y cárgalas rápidamente.</p>

            <div class="form-check mt-3">
              <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
              <label class="form-check-label" for="flexCheckDefault">
                No volver a mostrar este mensaje
              </label>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
          </div>
        </div>
      </div>
    </div>

  </div>

{% endblock content %}
{% block javascripts %}
{% if canShowTXT or canShowInvoices %}
  {% if economic_activity.count != 0 %}
  <script src="{% static 'assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js' %}"></script>
  <script src="{% static 'assets/datatables/datatable/2.0.7/js/dataTables.js' %}"></script>
  <script src="{% static 'assets/datatables/fixedcolumns/5.0.1/js/dataTables.fixedColumns.js' %}"></script>
  <script src="{% static 'assets/datatables/fixedcolumns/5.0.1/js/fixedColumns.dataTables.js' %}"></script>

  <!-- CUSTOM DROPZONE SCRIPTS STARTS -->
  <script src="{% static 'assets/js/plugins/notifier.js' %}"></script>
  <script src="{% static 'assets/cdns_locals/js/pdf-lib/pdf-lib.min.js' %}"></script>
  <script src="{% static 'assets/js/custom-dropzone.js' %}"></script>
  <!-- CUSTOM DROPZONE SCRIPTS ENDS -->

  <!-- VUE3 JS STARTS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputCategoriaExpenses = ref('expenses');
    const inputCategoriaSales = ref('sales');
    const inputEconomicActivity = ref(null);
    const inputTypeImport = ref('import-invoice');
    // const inputTypeDua = ref('import-dua');
    const inputTypeTicket = ref('ticket');
    const inputTypeAmzTxt = ref('sales');
    const inputTypePayroll = ref('payroll');
    const inputFile1 = ref(null);
    const inputFile2 = ref(null);
    const inputFile = ref(null);
    const inputFilename1 = ref(null);
    const inputFilename2 = ref(null);
    const inputFilename = ref(null);
    const inputUploadType = ref("{{uploadType}}");
    const uploadTypes = [
      {"code": "invoice", "description": "Lector de facturas"},
      {"code": "import", "description": "Lector de importaciones"},
      {"code": "ticket", "description": "Lector de tickets y recibos bancarios"},
      {"code": "amz-txt-eur", "description": "Lector de TXT Amazon EUR"},
      {"code": "payroll", "description": "Lector de nóminas"}
    ];
    const dj = ref({});

    // Define las variables reactivas aquí
    const totalInvoices = ref({{ total_invoices }});
    const limitInvoices = ref({{ limit_invoices }});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          //console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        //console.error("Error in getDjango: ", error);
        dj.value = {};
      }
    };
    
    function createDT(tableId) {

      const dataTableOptions = {
        "fixedColumns": true,
        "scrollX": true,
        "scrollY": true,
        "scrollCollapse": true,
        "language": {
          "url": "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
          "lengthMenu": "_MENU_",
          "zeroRecords": "No se han encontrado txt.",
          "info": "_START_ a _END_ de un total de _TOTAL_",
          "search": "Buscar:",
          "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
          "infoFiltered": ""
        },
        "paging": true,
        "searching": true,
        "ordering": true,
        "lengthChange": false,
        "info": true,
        "autoWidth": true,
        "responsive": true,
        "columnDefs": [
          {
            targets: 4,
            className: 'dt-right',
            width: '15%',
            type: 'date',
          }
        ],
        "order": [[4, "asc"]],
      }

      const table = new DataTable(tableId, dataTableOptions);
    }

    function destroyDT(tableId) {
      if ($.fn.DataTable.isDataTable(tableId)) {
        $(tableId).DataTable().destroy();
      }
    }

    // const getCountryNameByCode = (code) => {
    //   const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
    //   const countryName = country?.name ? country?.name : `País ${code}`;
    //   return countryName;
    // }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
    }

    const loadImportWizard = () => {
      const url = "{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}";
      window.location.href = url;
    }

    const clickInvoiceType = (type) => {
      inputUploadType.value = type;

      const activities = type === 'amz-txt-eur' 
        ? dj._rawValue.economic_activity_amz_txt 
        : dj._rawValue.economic_activity;

      inputEconomicActivity.value = activities.length === 1 ? activities[0].pk : null;

      const tableElement = document.querySelector('#txt-list-table');
      if (tableElement) {
        destroyDT('#txt-list-table');
      }

      if (type === 'amz-txt-eur') {
        Vue.nextTick(() => {
          if (document.querySelector('#txt-list-table')) {
            createDT('#txt-list-table');
          }
        });
      }
    }

    // WATCHERS ////////////////////////////////////////////////////////////////////////
    watch(inputEconomicActivity, (newValue) => {
        MTDropzones.forEach(dropzone => initializeDropzone(dropzone, newValue));
    });

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,

      inputId,
      inputCategoriaExpenses,
      inputCategoriaSales,
      inputEconomicActivity,
      inputTypeImport,
      // inputTypeDua,
      inputTypeTicket,
      inputTypeAmzTxt,
      inputTypePayroll,
      inputFile,
      inputFile1,
      inputFile2,
      inputFilename,
      inputFilename1,
      inputFilename2,
      inputUploadType,
      uploadTypes,
      totalInvoices,
      limitInvoices,

      // getCountryNameByCode,
      clickInvoiceType,
      loadImportWizard,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";

      if (data_export.dj._rawValue.economic_activity.length === 1) {
        data_export.inputEconomicActivity = data_export.dj._rawValue.economic_activity[0].pk;
      }
      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export};
        },
        mounted() {
          document.querySelectorAll('.hidden-until-vue').forEach((el) => {
            el.classList.remove('hidden-until-vue');
          });
        },
      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('#toast', data_export);
    createVue3('.vue', data_export);

    
  </script>
  <!-- VUE3 JS ENDS -->

  <script>
    const txtForm = document.getElementById('form-uploadtxt');
    const spinner = document.getElementById('spinner');
    const txtSubmitButton = document.getElementById('txt-submit-button');
    const txtFileInput = document.getElementById('id_file_txt');
    const totalInvoicesUploaded = document.getElementById("total-invoices-uploaded");
    const totalInvoicesLimit = document.getElementById("total-invoices-limit");
    
    const csrfToken = "{{ csrf_token }}"; // Cache CSRF token for reuse
    let total = parseInt("{{ total_invoices }}", 0);
    const limit = parseInt("{{ limit_invoices }}", 0);
    
    // Variables para contador de facturas
    let numFilesUploaded = 0;
    let numFilesErrored = 0;
    let numFilesToUpload = 0;

    function updateInvoiceCounters(newTotal) {
      totalInvoicesUploaded.textContent = newTotal;
      totalInvoicesLimit.textContent = limit === -1 ? "Sin límite" : limit;
    }

    function handleDropzoneSuccess(event) {
      const { invoices_created } = event.detail.data || {};
      total += invoices_created || 0;
      totalInvoices.value = total;
      updateInvoiceCounters(total);

      const dropzoneInstance = event.target.dropzoneInstance;
      if (dropzoneInstance) {
        dropzoneInstance.updateTotalInvoices(total);
      }
    }

    const initializeDropzone = (dropzone, iaeValue = null) => {
        const formData = {
            csrfToken,
            url: dropzone.getAttribute('data-form-url'),
            userRole: dropzone.getAttribute('data-form-user'),
            iae: iaeValue || dropzone.getAttribute('data-form-iae'),
        };

        const options = {
            name: dropzone.getAttribute('data-options-name'),
            namePlural: dropzone.getAttribute('data-options-name-plural'),
            dropzoneImg: dropzone.getAttribute('data-options-img'),
            totalInvoices: total,
            limitInvoices: limit,
        };

        const dropzoneId = dropzone.getAttribute('data-dropzone-id');
        dropzone.removeEventListener('dropzoneMTSuccess', handleDropzoneSuccess);

        const dropzoneInstance = new DropzoneMT(dropzone, dropzoneId, formData, options);
        dropzone.dropzoneInstance = dropzoneInstance;
        dropzone.addEventListener('dropzoneMTSuccess', handleDropzoneSuccess);
    };

    const MTDropzones = document.querySelectorAll('.dropzone-instance');
    MTDropzones.forEach(dropzone => initializeDropzone(dropzone));

    // Funcion comun para inicializar Dropzone
    // function createDropzone(selector, containerId) {
    //   Dropzone.options[selector] = {
    //     init: function () {
    //       var myList = document.createElement("ul");
    //       myList.classList.add("list-group");
    //       document.getElementById(containerId).appendChild(myList);
  
    //       // Evento que se dispara al agregar un archivo al Dropzone
    //       this.on("addedfile", (file) => {
    //         // Incrementar el número de archivos a cargar cuando se agrega un archivo
    //         numFilesToUpload++;
    //       });
  
    //       // Evento que se dispara justo antes de enviar los archivos
    //       this.on("sending", (file, xhr, formData) => {
    //         formData.append("total", total);
    //         formData.append("numFilesToUpload", numFilesToUpload);
    //       });
          
    //       // Evento que se dispara cuando un archivo se carga con éxito
    //       this.on("success", (file, response) => {
    //         // Incrementar el número de archivos cargados con éxito
    //         numFilesUploaded++;
    //         total++;
    //         totalInvoices.value++;
    //         $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
    //         // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
    //         var listItem = document.createElement("li");
    //         listItem.innerText = file.name + " - Subido";
    //         listItem.classList.add("list-group-item", "list-group-item-success");
    //         myList.insertBefore(listItem, myList.firstChild);
    //       });
  
    //       // Evento que se dispara cuando ocurre un error durante la carga de un archivo
    //       this.on("error", (file, errorMessage) => {
    //         // Incrementar el número de archivos que fallaron en la carga
    //         numFilesErrored++;
    //         $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
    //         // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
    //         var listItem = document.createElement("li");
    //         listItem.innerText = file.name + " - Error. " + errorMessage;
    //         listItem.classList.add("list-group-item", "list-group-item-danger");
    //         myList.insertBefore(listItem, myList.firstChild);

    //         // Crear botón de eliminar
    //         var removeButton = document.createElement("button");
    //         removeButton.classList.add("btn", "btn-danger", "btn-sm", "float-end");
    //         removeButton.innerText = "Eliminar";

    //         // Evento al hacer clic en el botón de eliminar
    //         removeButton.onclick = () => {
    //           myList.removeChild(listItem);
    //           numFilesErrored--;
    //           numFilesToUpload--;
    //           this.removeFile(file);
    //           countInvoices(total, limit);
    //         };

    //         listItem.appendChild(removeButton);
    //         myList.insertBefore(listItem, myList.firstChild);
    //       });
  
    //       // Evento que se dispara cuando se ha completado la carga
    //       this.on("complete", (file) => {
            
    //         console.log(`numFilesUploaded: ${numFilesUploaded}`)
    //         console.log(`numFilesErrored: ${numFilesErrored}`)
    //         console.log(`numFilesToUpload: ${numFilesToUpload}`)

    //         if (numFilesToUpload === numFilesUploaded + numFilesErrored) {
    //           if (numFilesToUpload === numFilesErrored) {
    //             // ALL ERRORS
    //           } else if (numFilesToUpload === numFilesUploaded) {
    //             // ALL OK
    //           } else {
    //             // MIXED
    //           }
    //           numFilesToUpload = 0;
    //         }
    //         countInvoices(total, limit);
    //       });
    //       numFilesUploaded = 0;
    //       numFilesErrored = 0;
    //       numFilesToUpload = 0;
    //     },
    //   parallelUploads: 4,
    //   maxFiles: 1000,
    //   maxFilesize: 10, // 10 MB en bytes
    //   acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
    //   dictDefaultMessage: "Arrastre los archivos aquí",
    //   dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
    //   dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
    //   dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
    //   dictInvalidFileType: "No puede subir archivos de este tipo.",
    //   dictResponseError: "El servidor respondió con el código {{statusCode}}",
    //   dictCancelUpload: "Cancelar Subida.",
    //   dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
    //   dictRemoveFile: "Eliminar archivo.",
    //   dictMaxFilesExceeded: "No puede subir más archivos."
    //   };
    // }
    
    // Creando las instancias de Dropzone
    // createDropzone('myDropzoneInvoice', 'myListContainerInvoice');
    // createDropzone('myDropzoneImportInvoice', 'myListContainerImportInvoice');
    // createDropzone('myDropzoneImportDua', 'myListContainerImportDua');
    // createDropzone('myDropzoneTicket', 'myListContainerTicket');
    // createDropzone('myDropzonePayroll', 'myListContainerPayroll');

    const invoiceTypeButtons  = document.querySelectorAll('.inv-type-btn');
    invoiceTypeButtons .forEach((btn) => {
      btn.addEventListener('click', () => {
        invoiceTypeButtons .forEach((btn) => btn.classList.remove('active'));
        btn.classList.add('active');
      });
    });

    txtForm.addEventListener('submit', (e) => {
    // txtForm.addEventListener('submit', async (e) => {
      // e.preventDefault(); // Prevenir el envío normal del formulario
      txtSubmitButton.disabled = true;
      document.getElementById("fallback").style.display = 'none';
      spinner.style.display = 'block';

      // try {
      //   const formData = new FormData(txtForm);
      //   const response = await fetch(txtForm.action, {
      //     method: 'POST',
      //     body: formData,
      //     headers: {
      //       'X-Requested-With': 'XMLHttpRequest'
      //     }
      //   });

      //   const data = await response.json();

      //   if (response.ok) {
      //     // Si la subida fue exitosa, redirigir
      //     window.location.href = txtForm.action;
      //   } else if (response.status === 403 && data.swal) {
      //     // Mostrar el mensaje de error con SweetAlert2
      //     await Swal.fire({
      //       title: data.swal.title,
      //       text: data.swal.text,
      //       icon: data.swal.icon,
      //       confirmButtonText: data.swal.confirmButtonText,
      //       confirmButtonColor: data.swal.confirmButtonColor
      //     });
      //     // Restaurar el formulario
      //     txtSubmitButton.disabled = false;
      //     document.getElementById("fallback").style.display = 'block';
      //     spinner.style.display = 'none';
      //   } else {
      //     // Otros errores
      //     throw new Error(data.error || 'Error al procesar el archivo');
      //   }
      // } catch (error) {
      //   // Mostrar error genérico
      //   await Swal.fire({
      //     title: 'Error',
      //     text: error.message || 'Error al procesar el archivo',
      //     icon: 'error',
      //     confirmButtonText: 'Entendido'
      //   });
      //   // Restaurar el formulario
      //   txtSubmitButton.disabled = false;
      //   document.getElementById("fallback").style.display = 'block';
      //   spinner.style.display = 'none';
      // }
    });

    txtFileInput.addEventListener('change', () => {
      if (txtFileInput.files.length > 0 && txtFileInput.files[0].name.endsWith('.txt')) {
        txtSubmitButton.disabled = false;
      } else {
        txtFileInput.value = ''
        txtSubmitButton.disabled = true;
        Swal.fire({
          title: 'Formato no válido',
          text: 'El archivo debe ser un TXT de Amazon',
          icon: 'error',
          confirmButtonText: 'Entendido'
        });
      }
    });

    txtFileInput.addEventListener('click', () => {
      event.stopPropagation();
    });

    const txtUploadZone = document.querySelector('.uploadzone');
    txtUploadZone.addEventListener('click', () => {
      txtFileInput.click();
    });

    updateInvoiceCounters(total);

    $(document).ready(function () {
      const urlParams = new URLSearchParams(window.location.search);
      const type = urlParams.get('type');
      const error = urlParams.get('error');
      const modalId = 'modalNewUploaderInfo';
      const localStorageKey = 'muaytax.invoiceUploader.showInfo';
      
      // Handle 'amz-txt-eur' type with error
      if (type === 'amz-txt-eur' && error) {
        clickInvoiceType('amz-txt-eur');
        document.getElementById('amz-txt-btn').classList.add('active');
        return;
      }

      // Show modal if uploader info is not hidden
      const hideUploaderInfo = localStorage.getItem(localStorageKey);
      if (!hideUploaderInfo) {
        const modalNewUploaderInfo = new bootstrap.Modal(document.getElementById(modalId));
        modalNewUploaderInfo.show();

        document.getElementById(modalId).addEventListener('hidden.bs.modal', () => {
          const checkbox = document.getElementById('flexCheckDefault');
          if (checkbox.checked) {
            localStorage.setItem(localStorageKey, 'false');
          }
        });
      }
    });


  </script>

  {% endif %}
{% endif %}
{% endblock javascripts %}
