{% extends "layouts/base.html" %}
{% load static %}
{% load utils %}
{% load permissions_filters %}
{% load crispy_forms_tags %}

{% block title %}
	Hub Vendedores
{% endblock title %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
    {% comment %} <link rel="stylesheet" href="{% static 'assets/css/plugins/dropzone.min.css' %}" /> {% endcomment %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css" />
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" />
	<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
	<style>
		
		.card-text{
			text-align:center;
		}

		.card-title{
			text-align:center;
			font-size:18px;
			color:black;
		}
		
	</style>
{% endblock stylesheets %}

{% block breadcrumb %}
	<div class="page-header">
		<div class="page-block">
			<div class="row align-items-center">
				<div class="col-md-12">
					<div class="page-header-title">
						<h5 class="m-b-10">
							<a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
							Hub Vendedores
						</h5>
					</div>
					<ul class="breadcrumb">
						<li class="breadcrumb-item">
							<a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
						</li>
						<li class="breadcrumb-item">
							<a href="{% url 'app_sellers:hub' %}">Hub Vendedores</a>
						</li>
				
					</ul>
				</div>
			</div>
		</div>
	</div>
{% endblock breadcrumb %}

{% block content %}
	<div class="mt-0">	
		<div class="card-body"  style="min-height: 68vh;">
			<!--General-->
			<div class="row d-flex mt-0">
				<!-- Todos los Sellers -->
				{% if perms.users.can_view_all_sellers_list or perms.users.is_superuserAPP %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:list' %}" class="card-link">
						<div class="card-body">
							<div class="card-title">Todos los vendedores</div>
							<div class="card-text">
								{% comment %} <h1><i class="fas fa-lg fa-list"></i></i></h1> {% endcomment %}
								<h1><i class="bi bi-person-lines-fill"></i></h1>
							</div>
							
						</div>
					</a>
				</div>
				{% endif %}

				{% if perms.users.can_view_bookings or perms.users.is_superuserAPP %}
					{% if manager_with_booking_schedule %}
					<div class="col-1 card rounded m-2" style="min-width: 17rem;">
						<a href="{% url 'app_bookings:list_bookings_manager' user.username %}" class="card-link">
							<div class="card-body">
								<div class="card-title">Citas telefónicas</div>
								<div class="card-text">
									<h1><i class="bi bi-telephone-outbound"></i></h1>
								</div>
							</div>
						</a>
					</div>
					{% endif %}
				{% endif %}
				<!-- Tareas Pendientes -->
				{% if not user|has_exclusive_permissions_only:'users.can_view_vat_pl,users.can_view_vat_cz,users.can_view_vat_de' %}
				<div id="hub-app" class="col-1 card rounded m-2" style="min-width: 17rem; position: relative;" @mouseover="handleMouseOver">
					<a href="{% url 'app_tasks:list_task_pending_manager' user.username %}" class="card-link">
						<div class="card-body">
							<div class="card-title">Tareas Pendientes</div>
							<div class="card-text">
								<h1><i class="bi bi-list-check"></i></h1>
							</div>
							<div class="position-absolute translate-middle-x" style="top: 5px; right: 0px; display: flex; flex-direction: column; gap: 5px; display:none;" v-show="pendingTasksCount > 0 || seenTasksCount > 0">
								<div v-if="pendingTasksCount > 0" data-bs-toggle="tooltip" :title="'Pendientes de Completar: ' + pendingTasksCount">
									<i class="bi bi-exclamation-circle-fill text-danger" style="font-size: 20px;"></i>
								</div>
								<div v-if="seenTasksCount > 0">
									<i class="bi bi-eye-fill text-warning" data-bs-toggle="tooltip" :title="'Pendientes de Ver: ' + seenTasksCount" style="font-size: 20px;"></i>
								</div>
							</div>
						</div>
					</a>
				</div>
				{% endif %}

				<!-- Notificaciones -->
				{% if not user|has_exclusive_permissions_only:'users.can_view_vat_pl,users.can_view_vat_cz,users.can_view_vat_de' %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem; position: relative;">
					<a href="{% url 'app_notifications:notifications_dashboard' %}" class="card-link">
						<div class="card-body">
							<div class="card-title">Centro de notificaciones</div>
							<div class="card-text">
								<h1><i class="bi bi-bell-fill"></i></h1>
							</div>
						</div>
					</a>
				</div>
				{% endif %}
			</div>
			<!-- Gestoria España -->
			<div class="row d-flex">
				<!-- Titulo -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_management_es or perms.users.can_view_management_usa %}
				<div class="col-12 m-2">
					<h3>Gestoría:</h3>	
				</div>
				{% endif %}

				<!-- Gestoria España -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_management_es %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_lists:new-management' 'es' %}" class="card-link">
						<div class="card-body">
							<div class="card-title">Gestoría España</div>
							<div class="card-text">
								<img src="{% static 'assets/images/flags/es.svg' %}" alt="ES" class="rounded" width="100px">
							</div>		
						</div>
					</a>
					<div class="text-center text-muted mb-3"><a href="{% url 'app_sellers:old-management' 'es' %}" class="text-muted"> - Ver Listado Antiguo - </a></div>
				</div>
				{% endif %}
				<!-- Gestoria España -->

				<!-- Gestoria USA -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_management_usa %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_lists:new-management' 'us' %}?period=0A&year=2024" class="card-link">
						<div class="card-body">
							<div class="card-title">Gestoría USA</div>
							<div class="card-text">
								<img src="{% static 'assets/images/flags/us.svg' %}" alt="ES" class="rounded" width="100px">
							</div>		
						</div>
					</a>
					<div class="text-center text-muted mb-3"><a href="{% url 'app_sellers:old-management' 'us' %}?period=0A&year=2024" class="text-muted"> - Ver Listado Antiguo - </a></div>
				</div>
				{% endif %}

				<!-- Listado vendedores con facturas de Verifactu -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_management_es %}
					<div class="row d-flex">
						<div class="col-1 card rounded m-2" style="min-width: 17rem;">
							<a href="{% url 'app_lists:verifactu_management' %}" class="card-link">
								<div class="card-body">
									<div class="card-title mb-0">Vendedores VeriFactu</div>
									<div class="card-text">
										<img src="{% static 'assets/images/logos/svg/other/aeat_logo.svg' %}" alt="AEAT" class="rounded" width="100px">
									</div>		
								</div>
							</a>
						</div>
					</div>
				{% endif %}
				<!-- Listado vendedores con facturas de Verifactu -->
			</div>

			<!-- Gestoria IVA -->
			<div class="row d-flex">
				<!-- Titulo -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_process or perms.users.can_view_vat_sp or perms.users.can_view_vat_de or perms.users.can_view_vat_fr or perms.users.can_view_vat_it or perms.users.can_view_vat_uk or perms.users.can_view_vat_pl or perms.users.can_view_vat_cz %}
				<div class="col-12 m-2">
					<h3>Gestoría IVA:</h3>	
				</div>
				{% endif %}

				<!-- Procesos -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_process %}
				<!-- Procesos activos -->
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:list_data' 'active' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">Procesos activos</h5>
						<div class="card-text">
							{% comment %} <img src="{% static 'assets/images/flagpack/WORLD.svg' %}" alt="WORLD" class="rounded" width="100px"> {% endcomment %}
							<h1><i class="bi bi-hourglass-split"></i></h1>
						</div>		
					</div>
					</a>
				</div>
				<!-- Procesos finalizados -->
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:list_data' 'finish' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">Procesos finalizados</h5>
						<div class="card-text">
							{% comment %} <img src="{% static 'assets/images/flagpack/WORLD.svg' %}" alt="WORLD" class="rounded" width="100px"> {% endcomment %}
							<h1><i class="bi bi-clipboard2-check"></i></h1>
						</div>		
					</div>
					</a>
				</div>
				<!-- Todos los procesos -->
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:list_data' 'all' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">Todos los procesos</h5>
						<div class="card-text">
							{% comment %} <img src="{% static 'assets/images/flagpack/WORLD.svg' %}" alt="WORLD" class="rounded" width="100px"> {% endcomment %}
							<h1><i class="bi bi-card-list"></i></h1>
						</div>		
					</div>
					</a>
				</div>
				{% endif %}
			</div>
			<div class="row d-flex">
				<!-- España -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_sp %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_lists:vat_management' 'es' %}" class="card-link">
						<div class="card-body">
							<h5 class="card-title">IVA España</h5>
							<div class="card-text">
								<img src="{% static 'assets/images/flags/es.svg' %}" alt="ES" class="rounded" width="100px">
							</div>		
						</div>
					</a>
					<div class="text-center text-muted mb-3"><a href="{% url 'app_sellers:vat' 'ES' %}" class="text-muted"> - Ver Listado Antiguo - </a></div>
				</div>
				{% endif %}
				<!-- Alemania -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_de %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:vat' 'DE' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">IVA Alemania</h5>
						<div class="card-text">
							<img src="{% static 'assets/images/flags/de.svg' %}" alt="DE" class="rounded" width="100px">
						</div>		
					</div>
					</a>
				</div>
				{% endif %}
				<!-- Francia -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_fr %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:vat' 'FR' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">IVA Francia</h5>
						<p class="card-text">
							<img src="{% static 'assets/images/flags/fr.svg' %}" alt="FR" class="rounded" width="100px">
						</p>		
					</div>
					</a>
				</div>
				{% endif %}
				<!-- Italia -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_it %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:vat' 'IT' %}?period=Q1&year=2024" class="card-link">
					<div class="card-body">
						<h5 class="card-title">IVA Italia</h5>
						<div class="card-text">
							<img src="{% static 'assets/images/flags/it.svg' %}" alt="IT" class="rounded" width="100px">
						</div>		
					</div>
					</a>
				</div>
				{% endif %}
				<!--Reino Unido -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_uk %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:vat' 'GB' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">IVA Reino Unido</h5>
						<div class="card-text">
							<img src="{% static 'assets/images/flags/gb.svg' %}" alt="GB" class="rounded" width="100px">
						</div>		
					</div>
					</a>
				</div>
				{% endif %}

				<!-- Polonia -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_pl %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:vat' 'PL' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">IVA Polonia</h5>
						<div class="card-text">
							<img src="{% static 'assets/images/flags/pl.svg' %}" alt="PL" class="rounded" width="100px">
						</div>
					</div>
					</a>
				</div>
				{% endif %}

				<!-- República Checa -->
				{% if perms.users.is_superuserAPP or perms.users.can_view_vat_cz %}
				<div class="col-1 card rounded m-2" style="min-width: 17rem;">
					<a href="{% url 'app_sellers:vat' 'CZ' %}" class="card-link">
					<div class="card-body">
						<h5 class="card-title">IVA República Checa</h5>
						<div class="card-text">
							<img src="{% static 'assets/images/flags/cz.svg' %}" alt="CZ" class="rounded" width="100px">
						</div>
					</div>
					</a>
				</div>
				{% endif %}
			</div>
		</div>
	</div>
{% endblock content %}

{% block javascripts %}
	<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
	<script src="{{ STATIC_URL }}assets/cdns_locals/js/popper/popper.min-v2.9.2.js"></script>
	<script src="{{ STATIC_URL }}assets/cdns_locals/js/vue/vue.global.prod-v3.2.6.js"></script>
	<script src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>

	<script>
		const { ref, onMounted, watch, nextTick } = Vue;

		const hubApp = Vue.createApp({
			delimiters: ['[[', ']]'],
			setup() {
				const pendingTasksCount = ref({{ pending_tasks_count }});
				const seenTasksCount = ref({{ seen_tasks_count }});

				const loadTaskCounts = async () => {
					try {
						const response = await axios.get("{% url 'app_sellers:task_counts' %}");
						pendingTasksCount.value = response.data.pending_tasks_count;
						seenTasksCount.value = response.data.seen_tasks_count;

					} catch (error) {
						console.error("Error fetching task counts:", error);
					}
				};

				const initializeTooltips = () => {
					nextTick(() => {
						const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
						tooltipTriggerList.map(function (tooltipTriggerEl) {
							return new bootstrap.Tooltip(tooltipTriggerEl);
						});
					});
				};

				onMounted(() => {
					loadTaskCounts();
					initializeTooltips();
				});

				watch([pendingTasksCount, seenTasksCount], initializeTooltips);

				return {
					pendingTasksCount,
					seenTasksCount,
					loadTaskCounts,
					initializeTooltips
				};
			}
		}).mount('#hub-app');
	</script>

	<!-- Script for reload page and cache when login -->
	<script>
		if (document.referrer.includes("/accounts/login/")) {
			if ((window.location.pathname.includes('/hub/') || window.location.pathname.includes('/dash/')) && !sessionStorage.getItem('pageReloaded')) {
				sessionStorage.setItem('pageReloaded', 'true');
				location.reload(true); // COMMON LOGIN: RELOAD PAGE & CACHE
			}
		} else {
			sessionStorage.removeItem('pageReloaded');
		}
	</script>
	
{% endblock javascripts %}
