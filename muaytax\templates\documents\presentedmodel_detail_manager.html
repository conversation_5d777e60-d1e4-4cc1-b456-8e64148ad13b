{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Modelos
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}" type="text/css" />
  <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css' %}">

  <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.bootstrap5.min.css' %}" type="text/css"/>
<link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css' %}" type="text/css"/>
  <style>

    .hover-cell {
      position: relative;
    }
    .editable-cell{
      display: flex;
      flex-direction: row;
      width: 100%;
      justify-content: space-between;
    }
    .hover-cell:hover .action-buttons__reset-edit {
        display: flex;
    }
    .action-buttons__save-cancel:not(.d-none) ~ .action-buttons__reset-edit {
        display: none!important;
    }
    .action-buttons__save-cancel{
      display: flex;
      gap: 0.5rem;
    }
    .action-buttons__reset-edit{
      display: none;
      gap: 0.5rem;
    }

    .editable-cell{
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0.5rem;
    }
    .editable-cell .edit-input {
      padding: .375rem .75rem !important;
    }
    .small-spinner {
      width: 1rem;
      height: 1rem;
      border: .15rem solid currentColor;
      border-right-color: transparent;
    }
    .action-buttons {
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }
    .cancel-btn,
    .save-btn {
      font-size: 1rem;
    }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Modelos</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name|title}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_documents:presented_model_list' seller.shortname  %}">Modelos</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos Generales</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        {{ form |crispy }}

        <br>

        {% if json_pdf %}
        <div class="accordion" id="accordionPDF">
          <div class="accordion-item">            
            <h2 class="accordion-header" id="accordion-head">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDiv">
                Campos PDF:
              </button>
            </h2>
            <div id="collapseDiv" class="accordion-collapse collapse" aria-labelledby="accordion-head" data-bs-parent="#accordionPDF">
              <div class="accordion-body">
                <div class="table-responsive">
                  <table id="json-pdf-table" class="table table-bordered table-hover">
                    <thead>
                      <tr>
                        <th scope="col">Nombre</th>
                        <th scope="col">Valor</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        {% endif %}

        <br>

        <!-- Mostrar la tabla del historial de cambios si existe -->
        {% if historial_json %}
          {% include 'documents/include/table_historial_presented_model.html' %}
        {% endif %}
        <!-- Mostrar la tabla del historial de cambios si existe -->

        <br>

        <div class="control-group">
          <div class="controls">
            {% if object.pk is not None %} 
              <button type="submit" class="btn btn-primary">Actualizar</button>
            {% else %}
              <button type="submit" class="btn btn-primary">Guardar</button>
            {% endif %}
          </div>
        </div>
      </form>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="{% static 'assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js' %}"></script>
<script src="{% static 'assets/datatables/datatable/2.0.7/js/dataTables.js' %}"></script>
<script>

  const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
  });

  function showToast(icon, title) {
    Toast.fire({
      icon: icon,
      title: title
    });
  }

  let table;
  let editedCell = new Map();


  window.addEventListener('load', () => {
    const jsonTable = document.getElementById('json-pdf-table');
    if (jsonTable){
      createDT();
      };
    createHistorialDT();
  });

  function createDT() {
    table = $('#json-pdf-table').DataTable({
      responsive: true,
      serverSide: false,
      ajax: {
        url: "{% url 'app_documents:presented_model_json_pdf_data' seller.shortname object.pk %}",
        type: "GET",
        dataSrc: "data"
      },
      language: {
          url: "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
          lengthMenu: "_MENU_",
          zeroRecords: "No se han encontrado registros.",
          info: "_START_ a _END_ de un total de _TOTAL_ campos del JSON",
          search: "Buscar:",
          infoEmpty: "No hay resultados que coincidan con su búsqueda.",
          infoFiltered: ""
      },
      columns: [
        { 
          data: "key",
          render: function(data, type, row) {
            return row.has_change ? `${data} <i class="fas fa-info-circle text-warning" title="Este campo ha sido modificado manualmente"></i>` : data;
          }
        },
        { 
          data: "value",
          className: "hover-cell",
          render: renderEditableCell
        }
      ],
      paging: true,
      autoWidth: false,
      lengthMenu: [[50, 100, 200, -1], [50, 100, 200, 'Todos']],
      ordering: false,
      searching: true,
      info: true,
      lengthChange: true,
      pageLength: 50,
      columnDefs: [
        { width: "30%", targets: 0 },
        { width: "70%", targets: 1 }
      ],
      drawCallback: function () {
        addListenersToTableButtons();
      }
    });
  }

  function renderEditableCell(data, type, row) {
    const resetButtonHTML = row.has_change ? `
      <a class="reset-btn text-danger f-14" role="button" title="Restablecer">
        <i class="fas fa-undo"></i>
      </a>` : '';
    
    return `
      <div class="editable-cell" data-key="${row.key}" data-value="${data}">
          <span class="cell-value">${data}</span>
          <input type="text" class="form-control edit-input me-3 d-none" value="${data}">
          <div class="action-buttons">
            <div class="spinner-border text-success small-spinner d-none" role="status">
              <span class="sr-only">Loading...</span>
            </div>
            <div class="action-buttons__save-cancel d-none">
              <a class="save-btn text-primary f-14" role="button" title="Guardar">
                <i class="fas fa-save"></i>
              </a>
              <a class="cancel-btn text-danger f-14" role="button" title="Cancelar">
                <i class="bi bi-x-circle"></i>
              </a>
            </div>
            <div class="action-buttons__reset-edit">
              ${resetButtonHTML}
              <a class="edit-btn text-warning f-14" role="button" title="Editar">
                <i class="fas fa-edit"></i>
              </a>
            </div>
          </div>
      </div>`;
  }

  function addListenersToTableButtons () {
    document.querySelectorAll('.editable-cell').forEach(cell => {
      const cellValue = cell.querySelector('.cell-value');
      const saveCancelBtns = cell.querySelector('.action-buttons__save-cancel');
      const editInput = cell.querySelector('.edit-input');
      const resetBtn = cell.querySelector('.reset-btn');
      const editBtn = cell.querySelector('.edit-btn');
      const saveBtn = cell.querySelector('.save-btn');
      const cancelBtn = cell.querySelector('.cancel-btn');
      const spinner = cell.querySelector('.small-spinner');

      function toggleEditableState(editing, cellValueEl, btnsEl, inputEl) {
        cellValueEl.classList.toggle('d-none', editing);
        btnsEl.classList.toggle('d-none', !editing);
        inputEl.classList.toggle('d-none', !editing);
        if (editing) {
            inputEl.focus();
            inputEl.selectionStart = inputEl.value.length;
        }
      }

      async function handleSaveBtnClick() {
        editInput.setAttribute('readonly', true);
        toggleSpinner(spinner, true);
        
        saveBtn.removeEventListener('click', handleSaveBtnClick);
        saveBtn.classList.remove('text-primary');
        saveBtn.classList.add('text-muted');
        saveBtn.removeAttribute('role');

        cancelBtn.removeEventListener('click', handleCancelbtnClick);
        cancelBtn.classList.remove('text-danger');
        cancelBtn.classList.add('text-muted');
        cancelBtn.removeAttribute('role');

        const key = cell.getAttribute('data-key');
        const value = editInput.value;

        try{
          response = await updateJsonField(key, value);
    
          if (response) {
            cellValue.textContent = response.data.value;
            toggleEditableState(false, cellValue, saveCancelBtns, editInput);
            spinner.classList.add('d-none');
            showToast('success', response.message);
          }
        } catch (error) {
          console.error('Error:', error);
          showToast('error', 'Error al actualizar el campo');
        } finally {
          table.ajax.reload(null, false);
        }

      }

      function handleCancelbtnClick() {
        toggleEditableState(false, cellValue, saveCancelBtns, editInput);
        editedCell.delete(cell);
      }

      async function handleResetBtnClick() {
        const key = cell.getAttribute('data-key');

        resetBtn.removeEventListener('click', handleResetBtnClick);
        spinner.classList.remove('d-none');
        resetBtn.classList.add('d-none');
        
        try {
          const response = await resetJsonField(key);
          if (response) {
            cellValue.textContent = response.data.value;
            table.ajax.reload(null, false);
            showToast('success', response.message);
          }
        } catch (error) {
          showToast('error', 'Error al restablecer el campo');
          resetBtn.classList.remove('d-none');
          resetBtn.addEventListener('click', handleResetBtnClick);
        } finally {
          spinner.classList.add('d-none');
        }
      }

      editInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          saveBtn.click();
        }
      });

      editBtn.addEventListener('click', () => {
        if (editedCell.size > 0) {
          editedCell.forEach((value, key) => {
            editedCell.delete(key);
            const cancelEl = key.querySelector('.cancel-btn');
            cancelEl.click();
          });
        }
        toggleEditableState(true, cellValue, saveCancelBtns, editInput);
        editedCell.set(cell, true);
      });

      resetBtn?.addEventListener('click', handleResetBtnClick);

      saveBtn.addEventListener('click', handleSaveBtnClick);

      cancelBtn.addEventListener('click', handleCancelbtnClick);

    });
  }

  async function updateJsonField(key, value) {
    return sendRequest('{% url "app_documents:presented_model_update_json_field" seller.shortname object.pk %}', { key, value });
  }

  async function resetJsonField(key) {
    return sendRequest('{% url "app_documents:presented_model_reset_json_field" seller.shortname object.pk %}', { key });
  }

  async function sendRequest(url, data) {
    try {
      const response = await fetch(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': '{{ csrf_token }}'
        }
      });
      if (response.ok) {
        return await response.json();
      } else {
        console.error('Error:', response.statusText);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  }

  function toggleEditableState(cell, editForm, cellValue, editInput, reset = false) {
    cellValue.classList.toggle('d-none', !reset);
    editForm.classList.toggle('d-none', reset);
    if (!reset && editInput) {
      editInput.focus();
      editInput.selectionStart = editInput.value.length;
    }
  }

  function toggleSpinner(spinner, show) {
    spinner.classList.toggle('d-none', !show);
  }

  //---------TABLA DEL HISTORIAL DE CAMBIOS---------

  let tableHistorial;

const createHistorialDT = () => {
  tableHistorial = $('#json-historial-table').DataTable({
    responsive: true,
    serverSide: false,
    ajax: {
      url: "{% url 'app_documents:presented_model_historial_changes' seller.shortname object.pk %}",
      type: "GET",
      dataSrc: function (json) {
        let historialData = [];

        // Si hay información de 'create', la añadimos 
        if (json.data.create) {
          historialData.push({
            user: json.data.create.user,
            username: json.data.create.name_user,
            email: json.data.create.user_email,
            date: json.data.create.date,
            action: "Creación de modelo", 
            data: json.data.create.data 
          });
        }

        // Recorremos las entradas de 'update_model' para extraer la información
        if (json.data.update_model) {
          json.data.update_model.forEach(change => {
            historialData.push({
              user: change.user, 
              username: change.name_user, 
              email: change.user_email, 
              date: change.date, 
              action: "Actualización de modelo", 
              data: change.data 
            });
          });
        }

        return historialData; 
      }
    },
    language: {
      url: "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
      lengthMenu: "_MENU_",
      zeroRecords: "No se han encontrado registros.",
      info: "_START_ a _END_ de un total de _TOTAL_ campos del JSON",
      search: "Buscar:",
      infoEmpty: "No hay resultados que coincidan con su búsqueda.",
      infoFiltered: ""
    },
    columns: [
      { data: "user",
        className: "dt-control",
        defaultContent: ""
      },
      { data: "username" },
      { data: "email" },
      { data: "date" },
      { data: "action" }
    ],
    paging: true,
    autoWidth: false,
    lengthMenu: [[10, 20, 50, -1], [10, 20, 50, 'Todos']],
    ordering: false,
    searching: true,
    info: true,
    lengthChange: true,
    pageLength: 10,
  });
};

const format = (d) => {
  if (!d.data || Object.keys(d.data).length === 0) {
    return '<p class="text-center" style="padding: 7px; margin-bottom: 0">No hay cambios registrados</p>';
  }
  let details;

  if (d.action == 'Creación de modelo') {
    details = `
      <table class="child-table" style="width: 100%; border-collapse: collapse;">
        <thead style="background: #ced4da61;
      color: black;">
          <tr>
            <th style="width: 20%;">Campo</th>
            <th>Valor</th>
          </tr>
        </thead>
        <tbody>
          ${Object.entries(d.data).map(([campo, valor]) => `
            <tr>
              <td><strong>${campo}</strong></td>
              <td>${valor !== null && valor !== '' ? valor : '---'}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;

    return details;
  }else{
    details = `
    <table class="child-table" style="width: 100%; border-collapse: collapse;">
      <thead style="background: #ced4da61;
      color: black;">
        <tr>
          <th style="width: 20%;">Campo Modificado</th>
          <th style="width: 40%;">Valor Antiguo</th>
          <th style="width: 40%;">Valor Nuevo</th>
        </tr>
      </thead>
      <tbody>
        ${Object.entries(d.data).map(([campo, valores]) => `
          <tr>
            <td><strong>${campo}</strong></td>
            <td>${valores.old_value !== null ? valores.old_value : '---' }</td>
            <td>${valores.new_value !== null ? valores.new_value : '---' }</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
  }

  return details;
};


  $('#json-historial-table').on('click', 'td.dt-control', function () {
    let tr = $(this).closest('tr');
    let row = tableHistorial.row(tr);
    if(row.child.isShown()){
      row.child.hide();
      tr.removeClass('shown');
      tr.addClass('notShown');
    }else {
          row.child(format(row.data())).show();
          tr.addClass('shown');
          tr.next('tr').find('td').first().css('padding', '0');
          
      }
  });

</script>
{% endblock javascripts %}
