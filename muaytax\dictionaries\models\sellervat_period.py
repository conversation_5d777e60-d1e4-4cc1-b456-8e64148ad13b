from django.db import models

class SellerVatPeriod(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )

    class Meta:
        verbose_name = "Periodo Fiscal del Pais IVA"
        verbose_name_plural = "Periodo Fiscal de los Paises IVA"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatPeriod)
# class SellerVatPeriodAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
