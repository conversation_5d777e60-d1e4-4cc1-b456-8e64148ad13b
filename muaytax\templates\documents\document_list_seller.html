{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Documentos
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <!-- Limit Characters in Table Span -->
  <style>

    input[type="password"]::-ms-reveal {
        display: none; /* Oculta el ícono de revelado en Edge */
    }

    #list-table td span {     
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    
    #document-preview {
      display: none;
      transition: opacity 0.5s ease, width 0.5s ease;
      opacity: 0;
      height: calc(100vh - 100px);
      position: sticky;
      top: 0px; /* Ajuste para alinear con el contenido superior */
    }
    #document-preview.visible {
      opacity: 1;
    }
    #document-preview .card {
      height: 100%;
    }
    #document-preview iframe {
      width: 100%;
      height: calc(100% - 60px); /* Ajuste para el espacio del encabezado */
    }
    #document-preview .unavailable {
      text-align: center;
      padding: 20px;
    }

    #table-container {
      transition: width 0.5s ease;
    }

    {% comment %} #date_cert {
    background-color: #e9ecef; 
    color: #6c757d; 
    border: 1px solid #ced4da; 
    cursor: not-allowed; 
    opacity: 1; 
    pointer-events: none; 
  }

  #date_cert::-webkit-input-placeholder {
      color: #6c757d; 
  }

  #date_cert:-ms-input-placeholder {
      color: #6c757d; 
  }

  #date_cert::placeholder {
      color: #6c757d;
  } {% endcomment %}
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Documentos</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="."> Documentos </a>
            </li>
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;" >
          <a href="{% url 'app_documents:document_upload' seller.shortname  %}" class="btn btn-primary"> 
            Cargar Documentos
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-12 d-flex justify-content-center align-items-start">
      <div class="input-group">
        <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."  />
      </div>
    </div>
    <div class="col-12" id="table-container">
      <div class="card user-profile-list">
        <div class="card-body pt-4">
          <div class="dt-responsive">
            <table id="list-table" class="table nowrap table-hover" style="cursor: pointer;">
              <thead class="table-head">
                <tr>
                  <th>País</th>
                  <th>Nº IVA</th>
                  <th>Tipo Documento</th>
                  <th style="width:5%;">Año</th>                  
                  <th style="width:3%;">Acciones</th>
                </tr>
              </thead>
              <tbody>
                {% for object in object_list %}
                <tr data-file-url="{{ object.get_file_url }}"
                    data-pass="{{ object.get_password|default:'' }}"
                    data-expiration="{{ object.expiration_date |date:"Y-m-d"}}"
                    data-pk="{{ object.pk }}"
                >
                  <td class="align-middle">
                    <span>{{ object.sellerVat.vat_country }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.sellerVat.vat_number }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.documentType }} 
                    {% if object.documentType.code == 'ES-SIGNATURE' %}
                        <a  data-bs-toggle="tooltip" 
                            data-bs-original-title="F. Expiración: {{object.expiration_date |date:"Y-m-d"}}"
                            data-expiration= "{{object.expiration_date |date:"Y-m-d"}}" 
                            class="icon-expiration">
                          <i class="fa-solid fa-circle-exclamation text-danger d-none"></i>
                        </a>
                        <a  data-bs-toggle="tooltip" 
                            data-bs-original-title="Certificado sin contraseña"
                            class="icon-password"
                            {% if object.get_password %}
                              data-pass= "true"
                            {% else %}
                              data-pass= "false"
                            {% endif %}
                          >
                          <i class="fa-solid fa-circle-exclamation text-warning d-none"></i>
                        </a>
                      {% endif %}
                    </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.year|default:'' }}</span>
                  </td>
                  <td class="align-middle">
                    <div style="align-items: center; display: flex; justify-content: center;"> 
                      <a class="btn btn-icon btn-info" href="{{object.get_file_url}}" target="_blank" download>
                        <i class="fa-solid fa-download"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <!-- Contenedor para la vista previa del documento -->
    <div class="col-4 p-4" id="document-preview">
      <div class="card" id="preview-card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="m-0" id="preview-header">Vista Previa del Documento</h5>
          <button type="button" class="btn-close" aria-label="Close" id="close-preview"></button>
        </div>
        <div class="card-body p-0 m-0">
          <h6 id="document-title" class="p-2 fw-bold text-white"></h6> <!-- Contenedor para el título del documento -->
          <iframe id="document-frame" src=""></iframe>
          <form id="cert_dig_div" style="display: none;">
            {% csrf_token %}
            <div class="card-body">
              <div class="row">
                <div class="col-12">
                  <div class="form-group">
                    <label for="cert_name">Nombre del fichero</label>
                    <p id="cert_name"> </p>
                  </div>
                  <div class="form-group position-relative">
                      <label for="cert_pass">Contraseña del certificado digital *</label>
                      <input type="password" class="form-control" id="cert_pass" name="password" required>
                      <span data-bs-toggle="tooltip" data-bs-original-title="Ver" id="toggle-password" class="position-absolute" style="right: 10px; top: 35px; cursor: pointer;">
                          <i class="fa fa-eye"></i> 
                      </span>
                  </div>
                  <div class="form-group">
                    <label for="date_cert">Fecha de expiración del certificado *</label>
                    <input type="date" class="form-control" id="date_cert" name="expiration_date" required>
                  </div>
                  <div class="form-group">
                    <input type="hidden" class="form-control" id="id_cert" name="id" required>
                  </div>
                  <div class="form-group d-flex justify-content-center" id ="send_button">
                    <button type="button" onclick="updateCert();" class="btn btn-primary">Actualizar</button>
                  </div>
                  <div class="form-group d-flex justify-content-center d-none" id ="loading_button">
                    <button class="btn btn-primary" type="button" disabled>
                      <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Actualizando...
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css"> 
  
  <script>

    // declare sweetalert
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });


    $(document).ready(function(){

      const dataTableOptions = {
        paging: false,
        searching: true, 
        ordering: true,
        truncation: true,
        info: true,
        footer: true,
        columnDefs: [
          { targets: 4, orderable: false }  // Desactivar la opción de ordenar la columna de Acciones
        ],
        language: {
          lengthMenu: "_MENU_",
          zeroRecords: "No se han encontrado documentos.",
          info: "_TOTAL_ resultados. ",
          search: "Buscar:",
          infoEmpty: "No hay resultados que coincidan con su búsqueda.",
          infoFiltered: ""
        },
        dom: 'lrtip',
        fixedHeader: false,
      };
      
      const dataTable =$("#list-table").DataTable(dataTableOptions);
      let currentPreviewUrl = '';
      let visible = false;
    
      function adjustLayout() {
        const tableContainer = document.getElementById('table-container');
        const previewContainer = document.getElementById('document-preview');
        const screenWidth = window.innerWidth;
    
        if (screenWidth <= 1366) {
          tableContainer.classList.remove('col-lg-8');
          previewContainer.classList.remove('col-4');
          previewContainer.classList.add('col-12');
        } else {
          previewContainer.classList.remove('col-12');
          previewContainer.classList.add('col-4');
        }
      }
    
      adjustLayout();
      window.addEventListener('resize', adjustLayout);
        
      $("#search").on("input", function(){
          const filtro =$(this).val();
          console.log(filtro)
          dataTable.search(filtro).draw();
      });

      // Función para manejar clicks en las filas y mostrar vista previa
      document.querySelectorAll('#list-table tbody tr').forEach(function(row) {
        row.addEventListener('click', function(e) {
    
          // Variables del DOM
          const previewContainer = document.getElementById('document-preview');
          const iframe = document.getElementById('document-frame');
          const documentTitle = document.getElementById('document-title'); // Contenedor del título
          const previewHeader = document.getElementById('preview-header'); // Título de la tarjeta
    
          // Para verificar si se hace click en col #6 | Acciones
          const actionsColumnIndex = 4;
          if (e.target.closest('td').cellIndex === actionsColumnIndex) return;
    
          // Lógica para la URL de la vista previa
          const fileUrl = this.dataset.fileUrl;
          if (fileUrl === currentPreviewUrl) return;

          document.getElementById('cert_name').textContent = fileUrl.split('/').pop();
          document.getElementById('cert_pass').value = this.dataset.pass ? this.dataset.pass : '';
          document.getElementById('date_cert').value = this.dataset.expiration ? this.dataset.expiration : '';
          document.getElementById('id_cert').value = this.dataset.pk;
    
          currentPreviewUrl = fileUrl;
          // Verificar si el archivo es una imagen o un pdf
          const fileExtension = fileUrl.split('.').pop().toLowerCase();
          if (fileExtension === 'pdf' || ['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
            // Manejar iframe
            document.getElementById('cert_dig_div').style.display = 'none';
            document.getElementById('document-frame').style.display = 'block';
            iframe.src = fileUrl;
            documentTitle.classList.remove('bg-danger');
            documentTitle.classList.add('btn-primary');
            previewHeader.textContent = 'Vista Previa del Documento';
          } else if (fileExtension === 'p12' ||['P12', 'pfx','PFX'].includes(fileExtension)) {
            //Manejar certificado digital
            iframe.src = '';
            document.getElementById('cert_dig_div').style.display = 'block';
            document.getElementById('document-frame').style.display = 'none';
            documentTitle.classList.remove('bg-danger');
            documentTitle.classList.add('btn-primary');
            previewHeader.textContent = 'Información del Certificado Digital';
          } else {
            // Mensaje para cuando no es una imagen o un pdf
            document.getElementById('cert_dig_div').style.display = 'none';
            document.getElementById('document-frame').style.display = 'block';
            iframe.src = '';
            documentTitle.classList.remove('btn-primary');
            documentTitle.classList.add('bg-danger');
            previewHeader.textContent = 'Vista Previa no disponible';
          }
    
          // Extraer y mostrar el título del documento
          const fileName = fileUrl.split('/').pop();
          documentTitle.textContent = fileName;
    
          // Remover cualquier icono de ojo existente
          removeIcon();
    
          // Agregar icono de ojo a la primera columna de la fila seleccionada
          const firstCellSpan = e.target.closest('tr').querySelector('td:first-child span');
          const eyeIcon = document.createElement('i');
          eyeIcon.className = 'fa-solid fa-eye';
          eyeIcon.style.marginRight = '5px';
          eyeIcon.style.color = '#16bb8a';  // Agregar color al icono
          firstCellSpan.insertBefore(eyeIcon, firstCellSpan.firstChild);
          // Actualizar clases de la vista previa
          previewContainer.classList.add('visible');
          if (visible) {
            console.log(`debug: Visibilidad | vista previs --> ${visible}`)
            previewContainer.style.display = 'block';
          } else {
            console.log(`debug: Visibilidad | vista previs --> ${visible}`)
            setTimeout(function() {
              previewContainer.style.display = 'block';
            }, 500);
          }
          visible = true;
          updateTableContainerClass(visible); // función para actualizar visibilidad de vista previa
        });
      });
    
      // Click fuera de las filas de la tabla | Ocultar vista previa
      document.addEventListener('click', function(e) {
        if (!e.target.closest('#list-table') && !e.target.closest('#document-preview')) {
          document.getElementById('document-preview').classList.remove('visible');
          document.getElementById('document-preview').style.display = 'none';
          currentPreviewUrl = '';
          updateTableContainerClass(false); // Pasar false para indicar que la vista previa está oculta
    
          // Remover cualquier icono de ojo existente
          removeIcon();
        }
      });

      // Mostrar u ocultar contraseña del certificado digital
      document.getElementById('toggle-password').addEventListener('mousedown', function() {
        document.getElementById('cert_pass').type = 'text';
      });
      document.getElementById('toggle-password').addEventListener('mouseup', function() {
        document.getElementById('cert_pass').type = 'password';
      });
    
      // Click en el botón de cerrar para ocultar la vista previa
      document.getElementById('close-preview').addEventListener('click', function() {
        document.getElementById('document-preview').classList.remove('visible');
        document.getElementById('document-preview').style.display = 'none';
        currentPreviewUrl = '';
        updateTableContainerClass(false); // Pasar false para indicar que la vista previa está oculta
    
        // Remover cualquier icono de ojo existente
        removeIcon();
      });

      function removeIcon() {
        document.querySelectorAll('#list-table tbody tr').forEach(function(row) {
          const firstCellSpan = row.querySelector('td:first-child span');
          if (firstCellSpan && firstCellSpan.querySelector('.fa-eye')) {
            firstCellSpan.removeChild(firstCellSpan.querySelector('.fa-eye'));
          }
          row.classList.remove('table-primary');
        });
      }
    
      // Función para actualizar la clase del contenedor de la tabla
      function updateTableContainerClass(previewVisible) {
        const previewContainer = document.getElementById('document-preview');
        const tableContainer = document.getElementById('table-container');
    
        // Verifica si la vista previa está visible y si el título del DataTable está presente
        if (previewVisible) {
          visible = true
          tableContainer.classList.add('col-lg-8');
        } else {
          visible = false
          tableContainer.classList.remove('col-lg-8');
        }
      }
    });


    const isDateNearExpiry = (expirationDate) => {
        const today = new Date();
        const expiryDate = new Date(expirationDate);
        const oneMonthFromToday = new Date(today);
        oneMonthFromToday.setDate(today.getDate() + 30);

        return expiryDate <= oneMonthFromToday;
    }

    // Función para actualizar los iconos de la tabla en función de su fecha de expiración
    const updateTableIcons = () => {
        document.querySelectorAll('a.icon-password').forEach(function (element) {
          const havePassword = element.getAttribute('data-pass');
          const icon = element.querySelector('i');
          console.log(havePassword);
          console.log(element);

          if (havePassword === 'false') {
            icon.classList.remove('d-none');
          } else {
            icon.classList.add('d-none');
          }
        });

        document.querySelectorAll('a.icon-expiration').forEach(function (element) {
          const expirationDate = element.getAttribute('data-expiration');
          const icon = element.querySelector('i');
          const row = element.closest('tr');

          if (expirationDate) {
            if (isDateNearExpiry(expirationDate)) {
              icon.classList.remove('d-none');
            } else {
              icon.classList.add('d-none');
            }
          } else {
            row.querySelector('a.icon-expiration').setAttribute(`data-bs-original-title`, `No hay fecha de expiración`);
            icon.classList.remove('d-none');
          }
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
        updateTableIcons();
    });

    // Función para actualizar la información del certificado digital
    const updateCert = async () => {
      const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
      const form = document.getElementById('cert_dig_div');
      const formData = new FormData(form);
      const id = document.getElementById('id_cert').value;
      const shortname = '{{ seller.shortname }}';
      formData.append('csrfmiddlewaretoken', csrfToken);
      if (!form.checkValidity()) {
           form.reportValidity();
        return; 
      }
      toggleUpButtons();
      try{
        const response = await fetch(`/sellers/${shortname}/docs/document/updateCertInfo/${id}/`, {
          method: 'POST',
          body: formData,
        });

        if(response.ok){
          const data = await response.json();
          //Actualizar los datos de la fila
          const row = document.querySelector(`tr[data-pk="${data.pk}"]`);
            if (row) {
                row.setAttribute('data-pass', data.password);
                row.setAttribute('data-expiration', data.expiration_date);
                row.querySelector('a.icon-password').setAttribute('data-pass', 'true');
                row.querySelector('a.icon-expiration').setAttribute('data-expiration', data.expiration_date);
                row.querySelector('a.icon-expiration').setAttribute(`data-bs-original-title`, `F. Expiración: ${data.expiration_date} `);
                updateTableIcons();
            }else{
              console.log("No se ha encontrado la fila");
            }
          toggleUpButtons();
          Toast.fire({
                      icon: 'success',
                      title: "Certificado actualizado correctamente"
                  });
        }
      }catch(error){
        console.error(error);
        toggleUpButtons();
        Toast.fire({
                      icon: 'error',
                      title: "Ha ocurrido un error al actualizar el certificado"
                  });
      }

    };
    
    // Función para mostrar u ocultar los botones de actualizar y cargando
    const toggleUpButtons = () =>{
      send = document.getElementById('send_button');
      loading = document.getElementById('loading_button');
      if(send.classList.contains('d-none')){
        send.classList.remove('d-none');
        loading.classList.add('d-none');
      }else{
        send.classList.add('d-none');
        loading.classList.remove('d-none');
      }
    }
  </script>
{% endblock javascripts %}
