{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}Vendedores Verifactu{% endblock title %}
{% block stylesheets %}
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.bootstrap5.min.css' %}" type="text/css"/>
    <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css' %}" type="text/css"/>
    <link rel="stylesheet" href="{% static 'assets/datatables/fixedcolumns/5.0.1/css/fixedColumns.dataTables.css' %}" type="text/css"/>
    <!-- FontAwesome -->
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}" type="text/css" />
    <!-- DataTables Buttons -->
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/dataTables/buttons.dataTables.min-v3.0.1.css' %}" type="text/css" />
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/uicons/css/uicons-bold-rounded.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/swal/custom-swal-buttons.css' %}" />

    <style>
        .no-wrap-with-fit-content {
            min-width: fit-content;
            text-wrap: nowrap;
        }
        .switch-small{
            transform: scale(0.7);
        }
        .fs-custom{
            font-size: 0.75rem;
        }
        .fs-custom-md{
            font-size: 0.9em;
        }
        .table{
            table-layout: fixed;
        }
        .custom-table-head {
            background-color: #f2f2f2!important;
        }
        .table.dataTable thead tr > .dtfc-fixed-start{
            background-color: #f2f2f2!important;
        }
        .dtfc-top-blocker{
            background-color: #f2f2f2!important;
            border-bottom: 1px solid rgba(0, 0, 0, 0.3)!important;
            height: 43px!important; /* This value depends on the height of the header */
        }
        .header_name {
            width: 40%!important;
            word-wrap: break-word;
            white-space: normal;
        }
        .pendings,
        .login {
            width: 200px!important;
        }
        .pendings,
        .status_verifactu{
            text-align: start!important;
        }

        .truncate-text{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;    
        }
        tbody tr td.pendings,
        tbody tr td.login {
            font-size: .9em;
        }
        .actions{
            text-align: center!important;
            width: 5%!important;
        }
        .list-causes-warning {
            list-style-type: circle;
        }
        .fix-borders{
            border-top-right-radius: .25rem!important;
            border-bottom-right-radius: .25rem!important;
        }
        .modal-size {
            max-width: 55%;
        }
        .error-title {
            color: #d11507;
            font-weight: bold;
        }
        .month-select {
            width: auto; /* Para que ajuste al contenido */
            min-width: 50px; /* Ajustar el ancho mínimo si es necesario */
            padding-left: 25px; /* Margen interno izquierdo */
            padding-right: 35px; /* Margen interno derecho */
            margin-left: 0; /* Elimina márgenes externos si es necesario */
            margin-right: 0; /* Elimina márgenes externos si es necesario */
            font-size: 0.9em;
        }
        .year-select {
            width: auto !important; /* Para que ajuste al contenido */
            min-width: 80px !important; /* Ajustar el ancho mínimo si es necesario */
            padding-left: 20px !important; /* Margen interno izquierdo */
            padding-right: 40px !important; /* Margen interno derecho */
            margin-left: 0 !important; /* Elimina márgenes externos si es necesario */
            margin-right: 0 !important; /* Elimina márgenes externos si es necesario */
            height: 38px !important;
        }
        /* Ocultar la opción de "Meses" del desplegable una vez abierto */
        select option.placeholder {
            display: none;
        }

    </style>
{% endblock stylesheets %}
{% block breadcrumb %}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h5 class="m-b-10">Listado vendedores VeriFactu</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href=".">Listado vendedores VeriFactu</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
{% endblock breadcrumb %}
{% block content %}
    <div class="row">
        <!-- cartas con valores totales no es necesario--> 
        <div class="col-12">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>FACTURAS PENDIENTES</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="total_invoices_count">&nbsp</b>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>VENDEDORES CON FACTURAS CORRECTAS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b id="correct_invoices_count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-check" style="color: #02c018;"></i></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>VENDEDORES CON FACTURAS CANCELADAS POR GESTOR</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b id="cancel_invoices_count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-user-xmark" style="color: red;"></i></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>VENDEDORES CON FACTURAS PENDIENTES DE ENVÍO</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b id="missing_invoices_count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-file-circle-exclamation" style="color:#f4c22b"></i></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- search input -->
        <div class="col-12 mb-3">
            <div class="row">
                <div class="col-12 col-lg-6 d-flex justify-content-start filters-row">
                    <div class="col-12 col-lg-8 me-3">
                        <div class="input-group">
                            <input class="form-control fix-borders" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()">
                            <span class="mdi mdi-magnify search-icon"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- card with filters and table -->
        <div class="col-12">
            <div class="card rounded">
                <div class="card-body">
                <div class="row">
                    <!-- fitlters and buttons content -->
                    <div class="d-flex flex-wrap flex-column-reverse flex-lg-row">
                    <!-- Filter Section starts -->
                    <div class="gap-3 col-sm-12 col-lg-6 d-flex flex-column flex-lg-row justify-content-center justify-content-lg-start filters-row mb-2 min-width-fit-content flex-fill">
                        <!-- entity -->
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                        <label class="btn btn-light  border align-content-center">
                            <input type="radio" name="entities" class="entity-filter" autocomplete="off" value="all">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Todos</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Todos</span>
                            <span class="d-sm-block d-md-none d-xl-none">Todos</span>
                        </label>
                        <label class="btn btn-light  border align-content-center" title="Sociedad limitada">
                            <input type="radio" name="entities" class="entity-filter" autocomplete="off" value="sl">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Sociedad Ltda</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">SL</span>
                            <span class="d-sm-block d-md-none d-xl-none">SL</span>
                        </label>
                        <label class="btn btn-light  border align-content-center" title="Autónomos">
                            <input type="radio" name="entities" class="entity-filter" autocomplete="off" value="self-employed">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Autónomos</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Aut</span>
                            <span class="d-sm-block d-md-none d-xl-none">Aut</span>
                        </label>
                        </div>
                        <!-- period selector box filter -->
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                        <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 1">
                            <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q1">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q1</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q1</span>
                            <span class="d-sm-block d-md-none d-xl-none">Q1</span>
                        </label>
                        <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 2">
                            <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q2">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q2</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q2</span>
                            <span class="d-sm-block d-md-none d-xl-none">Q2</span>
                        </label>
                        <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 3">
                            <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q3">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q3</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q3</span>
                            <span class="d-sm-block d-md-none d-xl-none">Q3</span>
                        </label>
                        <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 4">
                            <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q4">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q4</span>
                            <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q4</span>
                            <span class="d-sm-block d-md-none d-xl-none">Q4</span>
                        </label>
                        <label class="btn btn-light  border align-content-center" title="Anual">
                            <input type="radio" name="periods" class="period-filter" autocomplete="off" value="0A">
                            <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Anual</span>
                            <span class="d-none d-md-block d-xl-none">Anual</span>
                            <span class="d-sm-block d-md-none d-xl-none">0A</span>
                        </label>

                        <label class="border align-content-center rounded-end" title="Seleccione Mes" style="cursor: default;">
                            <select class="form-select form-select-sm form-control btn-light month-select rounded-0 rounded-end" id="monthFilter">
                            <option value="" class="placeholder">Mensual</option>
                            <option value="M1">Enero</option>
                            <option value="M2">Febrero</option>
                            <option value="M3">Marzo</option>
                            <option value="M4">Abril</option>
                            <option value="M5">Mayo</option>
                            <option value="M6">Junio</option>
                            <option value="M7">Julio</option>
                            <option value="M8">Agosto</option>
                            <option value="M9">Septiembre</option>
                            <option value="M10">Octubre</option>
                            <option value="M11">Noviembre</option>
                            <option value="M12">Diciembre</option>
                            </select>
                        </label>
                        </div>
                        <!-- year selector filter -->
                        <div class="d-flex flex-nowrap">
                        <select role="button" class="form-select form-control btn-light border h-100" name="year-input" id="year" onchange="onChangePeriodYear()">
                            <option value="2022">2022 &nbsp;&nbsp;&nbsp;</option>
                            <option value="2023">2023 &nbsp;&nbsp;&nbsp;</option>
                            <option value="2024">2024 &nbsp;&nbsp;&nbsp;</option>
                            <option value="2025">2025 &nbsp;&nbsp;&nbsp;</option>
                        </select>
                        </div>
                    </div>
                    <!-- Filter Section ends -->

                    <!-- table action buttons starts-->
                    <div class="col-sm-12 col-lg-6 d-flex justify-content-center justify-content-lg-end filters-row mb-2 min-width-fit-content flex-basis-0">
                        <div class="table-action-button-container">
                        <button class="btn btn-outline-light text-dark border-0 mb-0 d-flex flex-nowrap gap-1 align-items-center" onclick="openInfoModal()">
                            <i class="fa-regular fa-circle-question fa-lg me-0"></i>
                            &nbsp;Leyenda
                        </button>
                        </div>
                        <div class="dropdown-area-rtl table-action-button-container">
                        <button class="btn btn-outline-light text-dark border-0 mb-0 me-0 d-flex flex-nowrap gap-1 align-items-center"
                            type="button"
                            id="actionsDropdownMenuButton"
                            data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i class="fa-solid fa-ellipsis-v fa-lg me-0"></i>
                            &nbsp;Acciones
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="actionsDropdownMenuButton">
                            <li>
                            <a class="dropdown-item" href="#" id="exportExcelBtn">
                                <i class="fa-solid fa-download fa-lg me-1"></i> Descargar Excel
                            </a>
                            </li>
                        </ul>
                        </div>
                    </div>
                    <!--table action buttons ends-->
                    </div>
            
                    <!-- table content -->
                    <div class="table-responsive">
                    <table id="seller-list-table2" class="table table-hover stripe nowrap border" style="width: 100%;">
                        <thead class="custom-table-head">
                        <tr>
                            <th>Nombre</th>
                            <th>Email</th>
                            <th>Pendientes</th>
                            <th>Estado Facturas VeriFactu</th>
                            <th>Último acceso</th>
                            <th style="width:5%">Acciones</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    </div>
                </div>
                </div>
            </div>
        </div>
        <!-- card with filters and table -->

        <!-- Modal Info-->
        <div class="modal fade " id="info-modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg modal-size" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">Información sobre los iconos del listado</h5>
                </div>
                <div class="modal-body">
                    <p style="font-size:20px;"><b>Estado Facturas VeriFactu:</b></p>
                    <div class="row d-flex align-items-center">
                        <div class="col">
                            <ul>
                                <li><p><span ><i class="fa-solid fa-check fa-xl" style="color: #02c018;"></i></span> &nbsp: &nbsp
                                    Las facturas del usurio se encuentran al día con respecto al sistema Verifactu, 
                                    ten en cuenta que no es necesario que existan facturas que deban ser enviadas o que hayan sido enviadas a la AEAT. </p> 
                                </li>
                                <li><p><span ><i class="fa-solid fa-file-circle-exclamation fa-xl" style="color:#f4c22b"></i></span>&nbsp: &nbsp
                                    El usuario tiene facturas generadas pendientes de enviar al sistema Verifactu.</p>
                                </li>
                                <li><p><span ><i class="fa-solid fa-user-xmark fa-xl" style="color: red;"></i></span>&nbsp: &nbsp
                                    El usuario tiene facturas generadas que pueden ser enviadas a la AEAT, cuya emisión a VeriFactu ha sido cancelada por un gestor.</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">Cerrar</button>
                </div>
                </div>
            </div>
        </div>
        <!-- Modal Info-->
    </div>
{% endblock content %}

{% block javascripts %}

    <!-- jQuery (requerido por DataTables) -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/jquery/jquery-3.7.0.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="{{ STATIC_URL }}assets/js/plugins/sweetalert2.all.min.js"></script>
    <!-- DataTables 2.0.7 -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables-v2.0.7.js"></script>
    <!-- Plugins de DataTables 2.0.7-->
    <script src="{{ STATIC_URL }}assets/datatables/fixedcolumns/5.0.1/js/dataTables.fixedColumns.js"></script>
    <script src="{{ STATIC_URL }}assets/datatables/fixedcolumns/5.0.1/js/fixedColumns.dataTables.js"></script>
    <!-- Buttons 3.0.1 JS -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/buttons/dataTables.buttons.min.-v3.0.1.js"></script>
    <!-- Buttons HTML5 3.0.1 JS -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/buttons/buttons.html5.min-v3.0.1.js"></script>
    <!-- JSZip (para exportación a Excel) -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/cloudflare/jszip/jszip.min-v3.10.1.js"></script>

    <!-- start DEBUG -->
    <script type="text/javascript">
        const debug = {{ debug|yesno:"true,false" }};
        // Función para debug (imprime en consola solo si debug está habilitado)
        function debugLog(...args) {
        if (debug) {
            console.log(...args);
        }
        }
        debugLog("Debug mode is enabled")
    </script>
    <!-- end DEBUG -->
    <script>
        let table = null;
        let periodTxt = '{{period}}';
        let yearTxt = '{{year}}'
        let entityFilter = '{{entity}}'

        // Función para obtener los datos AJAX y actualizar los totales
        const ajaxData = (d) => {
        let tParams = "";
        let year = document.getElementById("year").value;
        let  period = $('input[name="periods"]:checked').val();
        if ($('#monthFilter').val() !== '') {
            period = $('#monthFilter').val()
        }
        // let period = document.getElementById("period").value;
        let  entity = $('input[name="entities"]:checked').val();
        // let entity = document.getElementById("entity").value;
        if (year) {
            d.year = year
            tParams += "&year=" + year;
        }
        if (period) {
            d.period = period
            tParams += "&period=" + period;
        }
        if (entity) {
            d.entity = entity
            tParams += "&entity=" + entity;
        }
        getTotals(tParams);
        return d
        }

        window.onload = function () {
        const urlParams = new URLSearchParams(window.location.search);
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear(); // Obtener el año actual
        const currentMonth = currentDate.getMonth() + 1; // Obtener el mes actual (enero = 0, por eso se suma 1)

        let period = urlParams.get('period');
        let year = urlParams.get('year') ||  currentYear;
        let entity = urlParams.get('entity') || (('{{entity}}'.length > 0) ? '{{entity}}' : 'all');;

        // Determinar el trimestre anterior basado en el mes actual
        if (!period) {
            if (currentMonth <= 1) {
            period = 'Q4'; // Trimestre anterior es el último del año pasado
            year = currentYear - 1;
            } else if (currentMonth <= 4) {
            period = 'Q1';
            } else if (currentMonth <= 7) {
            period = 'Q2';
            } else if (currentMonth <= 10) {
            period = 'Q3';
            } else {
            period = 'Q4';
            }
        }

        const $entityFilter = $('.entity-filter');
        const $periodFilter = $('.period-filter');
        const monthFilter = document.getElementById('monthFilter');
        const yearFilter = document.getElementById('year'); // Obtener el elemento del año

        // Setear filtros activos
        setActiveFilter($entityFilter, entity);
        setActiveFilter($periodFilter, period);

        // Si el periodo en la URL es un mes (empieza con 'M'), seleccionarlo en el filtro de meses
        if (period && period.startsWith('M')) {
            monthFilter.value = period;
        }

        // Setear el año actual o el que está en la URL
        yearFilter.value = year;
        // Update UI elements
        updateAnualText();

        // Attach event listeners
        $periodFilter.on('change', function () {
            monthFilter.value = ''; // Limpiar el filtro de meses
            updateMonthActiveClass();
            setActiveFilter($periodFilter, $(this).val());
            onChangePeriodYear()
        });

        monthFilter.addEventListener('change', function () {
            setActiveFilter($periodFilter, ''); // Desactivar todos los trimestres
            onChangePeriodYear()
        });

        $entityFilter.on('change', onChangePeriodYear);

        
        $('.search-icon').on('click', function () {
            $(this).closest('.input-group').find('input[type="search"]').focus();
        });
        
        createDT();
        updateMonthActiveClass();
        }

        // Función para setear los filtro de periodo de la URL o el trimestre por defecto
        function setActiveFilter($elements, value) {
        $elements.each(function () {
            const $this = $(this);
            if ($this.val() === value) {
                $this.prop('checked', true).parent().addClass('active');
            } else {
                $this.prop('checked', false).parent().removeClass('active');
            }
        });
        }

        // Función para inicializar la tabla
        const createDT = async () => {
        table = $("#seller-list-table2").DataTable({
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Exportar a Excel',
                    exportOptions: {
                        columns: function (idx, data, node) {
                            // Exportar todas las columnas excepto aquellas con clase .actions,
                            // pero incluir explícitamente la columna de email
                            const visibleColumns = [1];  // Índice de la columna 'email' a exportar
                            return $(node).hasClass('actions') ? false : (visibleColumns.includes(idx) || table.column(idx).visible());
                        },
                        rows: null,  // Exportar todas las filas
                        format: {
                            body: function (data, row, column, node) {
                                debugLog(
                                    "=== Exportando fila ===\n" +
                                    `Datos: ${JSON.stringify(data)}\n` +
                                    `Índice de fila (Real en DataTables): ${row}\n` +           
                                    `Índice de columna (Real en DataTables): ${column}\n`
                                );
                                debugLog("\n ======= START =======\n");
                    
                                let visibleIndexes = table.rows({ search: 'applied' }).indexes().toArray();
                                debugLog("Index - filas visibles:", visibleIndexes);
                                // let rowDataSeg = table.row(visibleIndexes[row]).data();
                                let rowData = table.row(row).data();

                                if (!rowData) {
                                    console.error(`No se encontró la fila ${visibleIndexes[row]} en la Tabla: `);
                                    return "Error de datos";
                                }
                    
                                debugLog("Datos reales de la fila:", rowData);
                    
                                let columnData = table.settings()[0].aoColumns[column];
                                if (!columnData) {
                                    console.warn(`ERROR: Índice de columna ${column} fuera de rango.`);
                                    return "Error de columna";
                                }
                    
                                debugLog(`Procesando columna ${columnData.data} ...`);
                    
                                // Procesar columna `user_name`
                                if (columnData.data === "user_name") {
                                    debugLog("Procesando columna user_name...");
                                    let name = rowData.user_name || "Desconocido";
                                    return `${name}`.trim();
                                }

                                // Procesar columna `email`
                                if (columnData.data === "email") {
                                    debugLog("Procesando columna email...");
                                    let email = rowData.email || "";
                                    return `${email}`.trim();
                                }
                    
                                // Procesar columna `num_pending_invoices`
                                if (columnData.data === "num_pending_invoices") {
                                    debugLog("Procesando columna num_pending_invoices...");
                    
                                    let invoices = rowData.num_pending_invoices ?? "Sin datos";
                                    let percentage = rowData.percentage_pending_invoices ?? "Sin datos";
                    
                                    percentage = (percentage !== "Sin datos") ? `(${percentage}%)` : "";
                    
                                    debugLog(`num_pending_invoices: ${invoices}, percentage_pending_invoices: ${percentage}`);
                    
                                    return `${invoices} ${percentage}`.trim();
                                }
                    
                                // Procesar columna `last_login`
                                if (columnData.data === "last_login") {
                                    debugLog("Procesando columna last_login...");
                    
                                    let rawDate = rowData.last_login;
                                    if (!rawDate) return "Sin datos"; // Si no hay fecha, retorna "Sin datos"
                    
                                    let dateObj = new Date(rawDate.replace(" ", "T"));
                    
                                    if (isNaN(dateObj.getTime())) {
                                    debugLog("Fecha inválida:", rawDate);
                                    return "Sin datos";
                                    }
                    
                                    let options = {
                                    day: "2-digit",
                                    month: "short",
                                    year: "numeric",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    hour12: false
                                    };
                    
                                    let formattedDate = dateObj.toLocaleDateString("es-ES", options).replace(",", " -");
                    
                                    debugLog(`Fecha transformada: ${rawDate} → ${formattedDate}`);
                                    return formattedDate;
                                }
                    
                                // Texto por defecto
                                const plainText = $('<div>').html(data).text().trim() || "Sin datos";
                                debugLog("Texto plano por defecto:", plainText);
                                return plainText;
                            }
                        }
                    },
                    customize: function (xlsx) {
                        debugLog("=== Depuración: Iniciando actualización del formato en Excel ===");
                    
                        let sheet = xlsx.xl.worksheets['sheet1.xml'];
                        let stylesXml = xlsx.xl['styles.xml'];
                    
                        debugLog("Depuración: Contenido inicial del XML de la hoja de cálculo:");
                    
                        // Modificar el título en la celda A1
                        debugLog("Actualizando el título...");
                        let titleCell = $('row:first c[r="A1"] is t', sheet);
                        if (titleCell.length) {
                            titleCell.text("Tabla listado Vendedores con facturas Verifactu");
                        } else {
                            console.error("No se encontró la celda A1 para modificar el título.");
                        }
                    
                        debugLog("Aplicando estilos a la tabla...");
                    
                        // Verificar si `cellXfs` existe en `styles.xml`
                        let cellXfs = $('cellXfs', stylesXml);
                        if (cellXfs.length === 0) {
                            console.error("No se encontró la sección cellXfs en styles.xml.");
                            return;
                        }
                    
                        let fills = $('fills', stylesXml);
                        let borders = $('borders', stylesXml);
                        let newStyleIndex = $('xf', cellXfs).length;
                        let boldCenterIndex = newStyleIndex;
                        let centerIndex = newStyleIndex + 1;
                        let firstColumnStyleIndex = newStyleIndex + 2;
                        let secondRowStyleIndex = newStyleIndex + 3;
                    
                        debugLog(`Agregando nuevos estilos...`);
                    
                        // Agregar fondo verde claro para la primera columna (excepto A1 y A2)
                        let greenFillIndex = $('fill', fills).length;
                        fills.append(`
                            <fill>
                                <patternFill patternType="solid">
                                    <fgColor rgb="C6EFCE"/> <!-- Verde claro -->
                                    <bgColor indexed="64"/>
                                </patternFill>
                            </fill>
                        `);
                    
                        // Agregar fondo gris para la segunda fila
                        let grayFillIndex = $('fill', fills).length;
                        fills.append(`
                            <fill>
                                <patternFill patternType="solid">
                                    <fgColor rgb="E0E0E0"/> <!-- Gris claro -->
                                    <bgColor indexed="64"/>
                                </patternFill>
                            </fill>
                        `);
                    
                        // Agregar borde a la lista de estilos de bordes
                        let borderIndex = $('border', borders).length;
                        borders.append(`
                            <border>
                                <left style="thin"><color auto="1"/></left>
                                <right style="thin"><color auto="1"/></right>
                                <top style="thin"><color auto="1"/></top>
                                <bottom style="thin"><color auto="1"/></bottom>
                            </border>
                        `);
                    
                        // Estilo de negrita y centrado horizontal y vertical (para el título y primera columna)
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="2" fillId="0" borderId="${borderIndex}" applyFont="1" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center"/>
                            </xf>
                        `);
                    
                        // Estilo de solo centrado horizontal y vertical (para el resto de la tabla)
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="0" fillId="0" borderId="${borderIndex}" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center"/>
                            </xf>
                        `);
                    
                        // Estilo de la primera columna con fondo verde claro y bordes (excepto las dos primeras celdas)
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="2" fillId="${greenFillIndex}" borderId="${borderIndex}" applyFont="1" applyFill="1" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center" wrapText="1"/>
                            </xf>
                        `);
                    
                        // Estilo especial para la segunda fila en gris con bordes
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="2" fillId="${grayFillIndex}" borderId="${borderIndex}" applyFont="1" applyFill="1" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center"/>
                            </xf>
                        `);
                    
                        debugLog("Aplicando estilos a las celdas...");
                    
                        // Aplicar negrita y centrado al título
                        $('row:first c[r="A1"]', sheet).attr('s', boldCenterIndex);
                    
                        // Aplicar centrado a toda la tabla
                        $('row:not(:first) c', sheet).attr('s', centerIndex);
                    
                        // Aplicar fondo verde claro a la primera columna (excepto las dos primeras celdas)
                        $('row:not(:lt(2)) c[r^="A"]', sheet).attr('s', firstColumnStyleIndex);
                    
                        // Aplicar fondo gris a la segunda fila
                        $('row:eq(1) c', sheet).attr('s', secondRowStyleIndex);
                    
                        debugLog("Formato de la tabla actualizado.");
                    
                        // Ajustar altura de fila (excepto para las dos primeras filas)
                        debugLog("Ajustando altura de filas...");
                        $('row:not(:lt(2))', sheet).attr('ht', '30').attr('customHeight', '1');
                    
                        // Asegurar que las celdas de la primera columna tengan `wrapText="1"`
                        $('row:not(:lt(2)) c[r^="A"]', sheet).each(function () {
                            $(this).append('<alignment wrapText="1"/>');
                        });
                    
                        debugLog("Altura de filas ajustada y ajuste de texto aplicado.");
                    
                        debugLog("Depuración: XML modificado de la hoja de cálculo:");
                        debugLog(new XMLSerializer().serializeToString(sheet));
                    
                        debugLog("=== Depuración finalizada ===");
                    }
                }
            ],
            "serverSide": false,
            "fixedColumns": true,
            "scrollX": true,
            "scrollY": "calc(100vh - 250px)",
            "scrollCollapse": true,
            "ajax": {
            "dataSrc": "data",
            "url": "{% url 'app_lists:verifactu_managementDT' %}",
            "data": function (d) {
                ajaxData(d);
            }
            },
            "language": {
            "url": "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
            "lengthMenu": "_MENU_",
            "zeroRecords": "No se han encontrado vendedores.",
            "info": "_START_ a _END_ de un total de _TOTAL_",
            "search": "Buscar:",
            "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
            "infoFiltered": ""
            },
            "columns": [
            {   // Columna Nombre
                "data": "user_name",
                "className": "header_name",
                "render": function (data, type, row) {
                let html = '';
                html += '<td class="align-middle">';
                html += '<div class="">';
                html += '<h6 class="m-b-0 fs-custom-md truncate-text"><b>';

                let name = row.seller_name;
                if (typeof name === 'string') {
                    const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
                    const words = row.seller_name.split(' ').map(function (word) {
                    const lowerWord = word.toLowerCase();
                    if (lowerCaseSuffixes.includes(lowerWord)) {
                        return word.toUpperCase();
                    } else {
                        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }
                    });
                    html += words.join(' ');
                }
                html += '</b>';
                if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                    html += ' - ' + row.user_name.split(' ').map(function (word) {
                    return word.charAt(0).toUpperCase() + word.slice(1);
                    }).join(' ');
                }

                html += '</h6>';
                html += '<p class="m-b-0 fs-custom-md">' + row.email.toLowerCase() + '</p>';
                html += '</div>';
                html += '</td>';

                return html;
                }
            },
            {   // Columna Email
                "data": "email",
                "className": "email-column",
                "visible": false,  // No visible en la tabla
                "exportable": true, // Exportable a Excel
                "render": function (data, type, row) {
                    return data || row.email;
                }
            },
            {   // Columna Facturas pendientes
                "data": "num_pending_invoices",
                "className": "pendings",
                "render": function (data, type, row) {
                if (data && (type === 'display' || type === 'filter')) {
                    let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                    html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                    html += '<div class="progress" style="height: 15px;">';
                    html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                    html += '</div>';
                    html += '</td>';
                    return html;
                }
                return data;
                }
            },
            {   // Columna Estado de facturas verifactu
                "data": "status_verifactu",
                "className": "status_verifactu",
                "render": function (data, type, row) {
                    let html= ''
                    let is_manager_cancel = row.invoice_cancel_manager;
                    let status_verifactu = row.status_verifactu;
                    html += '<div class="align-middle text-start">';
                    if (status_verifactu == 'correct'){
                        html += '<span><i class="fa-solid fa-check fa-xl" style="color: #02c018;"></i></span>'
                    } else if (status_verifactu == 'missing'){
                        html += '<span><i class="fa-solid fa-file-circle-exclamation fa-xl" style="color:#f4c22b"></i></span>'
                    }
                    
                    html += is_manager_cancel =='manager_cancel' ? '<span style="margin-left: 20px;">-<i class="fa-solid fa-user-xmark fa-xl" style="color: red; margin-left: 20px;"></i></span>' : '';
                    html += '</div>';                  
                    return html;
                }

            },
            
            {   // Columna de Ultimo acceso
                "data": "last_login",
                "className": "login truncate-text",
                "render": function (data, type, row) {
                if (data && (type === 'display' || type === 'filter')) {
                    const date = new Date(data);

                    const day = date.getDate().toString().padStart(2, '0');
                    const month = date.toLocaleString('default', { month: 'short' });
                    const year = date.getFullYear();
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');

                    const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

                    return formattedDate;
                }
                return data; // For other types, like 'sort'
                }
            },
            {   // Columna de acciones
                "data": null,
                "className": "actions",
                "orderable": false,
                "render": function (data, type, row) {
                let html = '<td class="align-middle text-center">';
                html += '<a href="/sellers/' + row.shortname + '/invoices/veriFactuInvoiceList/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Facturas Verifactu">';
                html += '<i class="feather icon-edit"></i>';
                html += '</a>';
                html += '</td>';
                html = '<div>' + html + '</div>';
                return html;
                },
            }
            ],
            "paging": true,
            "searching": true,
            "ordering": true,
            "lengthChange": false,
            "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
            "layout": {
            topEnd: null,
            },
            "createdRow": function (row, data, dataIndex) {
            const shortname = data.shortname;
            const link = '/sellers/' + shortname + '/';
            $(row).attr('style', 'cursor: pointer; vertical-align: middle;');
            $(row).attr('onclick', "window.location.href = '" + link + "';");
            },
            "initComplete": function (settings, json) {
                // Manteniendo el ajuste de ancho para tabla, cuerpo y cabecera
                const tableSettings = table.settings()[0];
                ['nTBody', 'nTable', 'nTHead'].forEach((key) => {
                    tableSettings[key].style.width = '100%';
                });
                console.log(json)
                let total_inv_pending = 0;
                let total_correct_verifactu = 0;
                let total_missing_verifactu = 0;
                let total_inv_cancel_manager = 0;

                data = json.data;
                if (data){
                    data.forEach(function (row) {
                        total_inv_pending += row.num_pending_invoices;
                        total_correct_verifactu += row.status_verifactu == 'correct' ? 1 : 0;
                        total_missing_verifactu += row.status_verifactu == 'missing' ? 1 : 0;
                        total_inv_cancel_manager += row.invoice_cancel_manager == 'manager_cancel' ? 1 : 0;
                    });
                } 

                // Actualización de contadores
                document.getElementById("total_invoices_count").textContent = total_inv_pending;
                document.getElementById("correct_invoices_count").textContent = total_correct_verifactu;
                document.getElementById("cancel_invoices_count").textContent =  total_inv_cancel_manager;
                document.getElementById("missing_invoices_count").textContent =  total_missing_verifactu;

                const period = "{{period}}";
                const entity = "{{entity}}";
                
                // Mostrar columnas según el periodo seleccionado
                setPeriodColumnsVisibility(period, entity);

                // Mantener visibilidad de columnas de acuerdo con la configuración del usuario
                $('.column-switch').each(function () {
                    const columnClass = $(this).data('column');
                    const column = table.column(`.${columnClass}`);
                    this.checked = column.visible();
                    $(this).trigger('change');
                });

                // Tooltip para celdas truncadas
                const truncatedCells = document.querySelectorAll('.truncate-text');
                truncatedCells.forEach(cell => {
                    cell.setAttribute('title', cell.textContent);
                });

                $('[data-bs-toggle="tooltip"]').tooltip();
            },
            "drawCallback": function (settings) {
            $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });

        function setPeriodColumnsVisibility(period, entity){
            table.columns('.model-quarter, .model-month, .model-year, .model-202').visible(false);
            if (period === '0A') {
            table.columns('.model-year').visible(true);
            } else if (period.startsWith('Q')) {
            table.columns('.model-quarter').visible(true);
            table.columns(`.txt.${period}`).visible(true);

            if (period == 'Q2' && (entity == 'all' || entity == 'sl')) {
                table.columns('.model-q2').visible(true);
            }
            } else if (period.startsWith('M')) {
            table.columns('.model-month').visible(true);
            table.columns(`.month${period.slice(1)}`).visible(true);
            if (['M4', 'M10', 'M12'].includes(period)){
                table.columns('.model-202').visible(true);
            }
            }
        }

        $('.column-switch').change(function () {
            const columnClass = $(this).data('column');
            const column = table.column(`.${columnClass}`);
            if (this.checked) {
            column.visible(true);
            } else {
            column.visible(false);
            }

            const allChecked = checkAllMonths();
            const selectAllButton = document.getElementById('selectAllColumnsButton');
            if (allChecked) {
            selectAllButton.textContent = 'Deseleccionar todos';
            } else {
            selectAllButton.textContent = 'Seleccionar todos';
            }
        });

        // Vincular el enlace del dropdown con el botón "excelHtml5" de DataTables
        const exportExcelBtn = document.getElementById('exportExcelBtn');
            if (exportExcelBtn) {
            exportExcelBtn.addEventListener('click', function () {
                // Disparamos el botón “excelHtml5”
                table.button('.buttons-excel').trigger();
            });
        }

        }
        
        function search() {
        const tipo = $("#search").val();
        table.column(0).search(tipo).draw();
        }

        const getTotals = (params) => {
        let p = params;

        if (!p || p === undefined || p === null || p === "") {
            p = "";
        } else if (p.charAt(0) == "&") {
            p[0] = "?";
        }
        }

        function updateAnualText() {
        const yearSelected = document.getElementById('year').value;  // Captura el año seleccionado
        // Actualizar el texto del botón que muestra "Anual"
        // document.getElementById('anualButtonText').textContent = yearSelected;
        // document.getElementById('anualButtonText-md').textContent = yearSelected;
        // document.getElementById('anualButtonText-sm').textContent = yearSelected; // Texto reducido para vistas móviles
        }

        const onChangePeriodYear = () => {
        const quarterly = $('input[name="periods"]:checked').val();  // Captura el trimestre seleccionado
        const month = $('#monthFilter').val();  // Captura el mes seleccionado
        const year = document.getElementById('year').value;  // Captura el año seleccionado
        const entity = $('input[name="entities"]:checked').val();  // Captura la entidad seleccionada

        // Definir 'period' fuera del bloque 'if'
        let period

        if (month != '') {
            period = month;
        } else {
            period = quarterly;
        }

        // Si no hay ningún período seleccionado, no hacemos nada
        if (period === '') {
            debugLog("No hay periodo seleccionado");
            Swal.fire({
            icon: 'warning',
            title: 'Periodo no seleccionado',
            text: 'Debes seleccionar un trimestre o un mes antes de continuar.',
            confirmButtonText: 'Aceptar',
            customClass: {
                confirmButton: 'btn btn-dark'
            }
            });
            return;
        }

        // Creamos la nueva URL con los parámetros seleccionados
        const urlembed = "{% url 'app_lists:verifactu_management' %}";
        const newUrl = `${urlembed}?period=${period}&year=${year}&entity=${entity}`;

        // Actualizamos el texto de "Anual"
        updateAnualText();

        // Redirigimos a la nueva URL
        window.location.href = newUrl;
        };

        function toggleSelectAllMonths(button) {
        const allChecked = checkAllMonths();
        if (allChecked) {
            document.querySelectorAll('.column-switch').forEach(month => month.checked = false);
            button.textContent = 'Seleccionar todos';
        } else {
            document.querySelectorAll('.column-switch').forEach(month => month.checked = true);
            button.textContent = 'Deseleccionar todos';
        }

        document.querySelectorAll('.column-switch').forEach(month => month.dispatchEvent(new Event('change')));
        }

        function checkAllMonths() {
        const months = document.querySelectorAll('.column-switch');
        return Array.from(months).every(month => month.checked);
        }

        document.getElementById('monthFilter').addEventListener('change', function() {
        if (this.value === '') {
            this.classList.add('placeholder');
        } else {
            this.classList.remove('placeholder');
        }
        });

        // Función para actualizar la clase 'active' del filtro de meses
        function updateMonthActiveClass() {
            const monthFilter = document.getElementById('monthFilter');
            if (monthFilter.value !== '') {
            monthFilter.classList.add('active');
            } else {
            monthFilter.classList.remove('active');
            }
        }

        function openInfoModal() {
        const modal = new bootstrap.Modal(document.getElementById('info-modal'));
        modal.show();
        }

        function showWarning390(){
        Swal.fire({
            icon: 'warning',
            title: 'Modelo 390',
            text: 'Este cliente tiene al menos un modelo 303 pendiente de presentar en este año. Por favor, ten en cuenta que el modelo 390 es obligatorio una vez presentado el modelo 303.',
            confirmButtonText: 'Aceptar',
            customClass: {
            confirmButton: 'btn btn-dark w-100',
            actions: 'swal2-action-div-full-width',
            }
        });
        }

    </script>
    <!-- JQUERY DATATABLES -->
{% endblock javascripts %}