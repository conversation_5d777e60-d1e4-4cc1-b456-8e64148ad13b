{% extends "layouts/base.html" %}
{% load crispy_forms_tags static %}
{% block title %}Trabajador{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.3.min-v3.6.3.js" crossorigin="anonymous"></script>
  <link href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2.min-v4.1.0.css" rel="stylesheet">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/select/select2.min-v4.1.0.js"></script>
  <!-- Limit Characters in Table Span -->
  <style>
    fieldset.form-borders {
      border: 1px groove #838383 !important;
      padding: 0 1.4em 1.4em 1.4em !important;
      margin: 0 0 1.5em 0 !important;
      -webkit-box-shadow: 0px 0px 0px 0px #000;
      box-shadow: 0px 0px 0px 0px #000;
    }

    legend.form-borders {
      text-align: left !important;
      width: inherit; /* Or auto */
      padding: 0 10px; /* To give a bit of padding on the left and right */
      border-bottom: none;
      float: unset !important;
    }

    .select2-container--default .select2-selection--single {
      height: 43px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
      height: 100% !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
      line-height: 40px !important;
      height: 100% !important;
    }

    .select2-container .select2-search {
      display: none;
    }

    .select2-container--default .select2-selection--single {
      border: 1px solid #ced4da;
    }

    /* change border color on focus */
    .select2-container--default.select2-container--focus .select2-selection--single {
      border: 1px solid #86b7fe;
    }

    .select2-container--default.select2-container--disabled .select2-selection--single {
      background-color: #e9ecef;
    }


  </style>
{% endblock stylesheets %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_workers:list' seller.shortname %}">Trabajadores</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">{{ object.first_name }}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" id="form" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <fieldset class="form-borders">
          <legend class="form-borders">Datos personales</legend>
          <div class="row">
            <div class="col-md-4">
              {{ form.worker_type|as_crispy_field }}
            </div>
            <div class="col-md-4">
              {{ form.contract_type|as_crispy_field }}
            </div>
            <div class="col-md-4">
              {{ form.nif_nie|as_crispy_field }}
            </div>
          </div>
          <div class="row">
            <div class="col-md-4">
              {{ form.first_name|as_crispy_field }}
            </div>
            <div class="col-md-4">
              {{ form.last_name|as_crispy_field }}
            </div>
            <div class="col-md-4">
              {{ form.birthday|as_crispy_field }}
            </div>
          </div>
          <div class="row">
            <div class="col-md-4">
              {{ form.nuss_naf|as_crispy_field }}
            </div>
            <div class="col-md-4">
              {{ form.disability|as_crispy_field }}
            </div>
            <div class="col-md-4">
              {{ form.geographical_mobility|as_crispy_field }}
            </div>
          </div>
        </fieldset>
        <fieldset class="form-borders">
          <legend class="form-borders">Datos familiares</legend>
          <div class="row">
            <div class="col-md-3">
              {{ form.marital_status|as_crispy_field }}
            </div>
            <div class="col-md-3">
              {{ form.spouse_nif_nie|as_crispy_field }}
            </div>
            <div class="col-md-3">
              {{ form.is_titular|as_crispy_field }}
            </div>
            <div class="col-md-3">
              {{ form.nif_nie_titular|as_crispy_field }}
            </div>
          </div>
        </fieldset>
        <fieldset class="form-borders">
          <legend class="form-borders">Datos de dirección</legend>
          {% if worker_address_form is not None %}
            <div class="row">
              <div class="col-md-10">
                {{ worker_address_form.address|as_crispy_field }}
              </div>
              <div class="col-md-2">
                {{ worker_address_form.address_number|as_crispy_field }}
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                {{ worker_address_form.address_continue|as_crispy_field }}
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                {{ worker_address_form.address_zip|as_crispy_field }}
              </div>
              <div class="col-md-4">
                {{ worker_address_form.address_city|as_crispy_field }}
              </div>
              <div class="col-md-4">
                {{ worker_address_form.address_country|as_crispy_field }}
              </div>
            </div>
          {% endif %}
        </fieldset>
        <fieldset class="form-borders">
          <legend class="form-borders">Cargador de DNI/NIE</legend>
          <div class="row">
            <div class="col-md-12">
              {{ form.nie_upload|as_crispy_field }}
            </div>
        </fieldset>
        <fieldset class="form-borders">
          <legend class="form-borders">Fechas</legend>
          <div class="row">
            <div class="col-md-6">
              {{ form.start_date|as_crispy_field }}
            </div>
            <div class="col-md-6">
              {{ form.end_date|as_crispy_field }}
            </div>
        </fieldset>
        <div class="alert alert-info" role="alert">
          <strong>*</strong> Campos obligatorios
        </div>
        <div class="control-group">
          <div class="controls">
            {% if object.pk is not None %}
              <button type="submit" class="btn btn-primary">Actualizar</button>
            {% else %}
              <button type="submit" class="btn btn-primary">Guardar</button>
            {% endif %}
          </div>
        </div>
      </form>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
<script>
  const requiredSpanElement = '<span class="asteriskField">*</span>';
  var workerType = $('#id_worker_type');

  var contractTypeField = $('#id_contract_type');
  var dissabilityField = $('#id_disability');

  var isTitularField = $('#id_is_titular');
  var nifNieTitularField = $('#id_nif_nie_titular');

  var maritalStatusField = $('#id_marital_status');
  var spouseNifNieField = $('#id_spouse_nif_nie');

  contractTypeField.select2({
    width: '100%',
  });
  dissabilityField.select2({
    width: '100%',
  });
  isTitularField.select2({
    width: '100%',
  });
  maritalStatusField.select2({
    width: '100%',
  });
  
  $(document).ready(function() {

    function handleFieldRequired(workerTypeValue) {
      const disableFieldsValue = workerTypeValue === '3';
      const dinamicFields = [
        'id_contract_type',
        'id_birthday',
        'id_nuss_naf',
        'id_disability',
        'id_geographical_mobility',
        'id_marital_status',
        'id_is_titular',
        'id_nie_upload',
      ];

      dinamicFields.forEach(fieldId => {
        const field = $(`#${fieldId}`);
        const label = $(`label[for="${fieldId}"]`);

        // check if field is nie_upload
        if (fieldId === 'id_nie_upload') {
          const alreadyNieUpload = document.getElementById('nie_upload-clear_id');
          if (!alreadyNieUpload) {
            field.prop('disabled', disableFieldsValue);
            field.prop('required', !disableFieldsValue);
          }
        } else {
          field.prop('disabled', disableFieldsValue);
          field.prop('required', !disableFieldsValue);
        }

        const spanAlreadyAppended = label.find('span').length > 0;
        if (disableFieldsValue) {
          field.val(null).trigger('change');
          label.find('span').remove();
        } else {
            if (!spanAlreadyAppended) {
              label.append(requiredSpanElement);
            }
        }

      });
      
    }

    function maritalCheck(maritalStatusValue) {
      const label = $(`label[for="id_spouse_nif_nie"]`);
      const spanAlreadyAppended = label.find('span').length > 0;
      
      if (maritalStatusValue === '2') {
        spouseNifNieField.removeAttr("readonly");
        spouseNifNieField.attr("required", true);
        spouseNifNieField.prop('disabled', false);
        if (!spanAlreadyAppended) {
            label.append(requiredSpanElement);
        }
        // setTimeout(function () {
        //     spouseNifNieField.focus();
        // }, 0);
      } else {
        spouseNifNieField.attr("readonly", "readonly");
        spouseNifNieField.removeAttr("required");
        spouseNifNieField.prop('disabled', true);
        spouseNifNieField.val('');
        label.find('span').remove();
      }
    }

    function titularCheck(isTitularValue) {
      const label = $(`label[for="id_nif_nie_titular"]`);
      const spanAlreadyAppended = label.find('span').length > 0;

      if (isTitularValue === '2') {
        nifNieTitularField.removeAttr("readonly");
        nifNieTitularField.attr("required", true);
        nifNieTitularField.prop('disabled', false);
        if (!spanAlreadyAppended) {
            label.append(requiredSpanElement);
        }
        // setTimeout(function () {
        //     nifNieTitularField.focus();
        // }, 0);
      } else {
        nifNieTitularField.attr("readonly", "readonly");
        nifNieTitularField.removeAttr("required");
        nifNieTitularField.prop('disabled', true);
        nifNieTitularField.val('');
        label.find('span').remove();
      }
    }

    workerType.on('change', function() {
      handleFieldRequired(workerType.val());
    });
    handleFieldRequired(workerType.val());

    maritalStatusField.on('change', function() {
      maritalCheck(maritalStatusField.val());
    });
    maritalCheck(maritalStatusField.val());

    isTitularField.on('change', function() {
      titularCheck(isTitularField.val());
    });
    titularCheck(isTitularField.val());
        
  });

</script>
{% endblock javascripts %}
