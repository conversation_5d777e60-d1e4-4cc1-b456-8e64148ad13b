from django.db import models

class BankType(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=100,
        verbose_name="<PERSON>ó<PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Tipo de Cuenta Bancaria"
        verbose_name_plural = "Tipos de Cuentas Bancarias"
    
    def __str__(self):
        return self.description
    
# @admin.register(BankType)
# class BankTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]