from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from muaytax.signals import disable_for_load_data
from muaytax.utils.mixins import CustomTimeStampedModel

MODEL = 'BE-15'
class ModelFormBE15(CustomTimeStampedModel):
    seller = models.ForeignKey(
        "sellers.Seller",
        null=True,
        on_delete=models.CASCADE,
        related_name="modelbe15_seller",
        verbose_name=_("Empresa"),
    )
    year = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("Año de presentación del modelo BE-15")
    )
    is_processed = models.BooleanField(
        default=False,
        verbose_name=_("EL modelo BE-15 ha sido procesado?"),
    )

    translations = models.JSONField(
        verbose_name=_('Traducciones'),
        blank=True,
        null=True,
    )
    
    resp_contacted_bea = models.BooleanField(
        null=True,
        verbose_name=_("¿Has sido contactado por el BEA?"),
        help_text=_("Seleccionado es: 'Si, ha sido contactado'"),
    )
    
    resp_request_bea = models.BooleanField(
        null=True,
        verbose_name=_("¿Enviar mis datos al BEA?"),
        help_text=_("Si ha sido contactado por el BEA este campo debe ser vacío o desconocido"),
    )

    class Meta:
        verbose_name = _('Contracted model {model}').format(model=MODEL)
        verbose_name_plural = _('Contracted models {model}').format(model=MODEL)

    def __str__(self):
        return _('Model {model} from {seller} ({year})').format(model=MODEL, seller=self.seller, year=self.year)
    

@receiver(post_save, sender=ModelFormBE15)
@disable_for_load_data
def after_sellervat_save(sender, instance, created, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk)

@receiver(post_delete, sender=ModelFormBE15)
@disable_for_load_data
def after_sellervat_delete(sender, instance, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk)