from django.db import models

from muaytax.utils.mixins import CustomTimeStampedModel
from muaytax.app_ocr.choices import ProcessingDocument
    
class PromptConfiguration(CustomTimeStampedModel):
    processing_document = models.CharField(
        max_length=50,
        choices=ProcessingDocument.choices,
        unique=True,
        verbose_name="Documento a procesar",
        help_text="Tipo de documento que se procesará en el OCR. En función de este valor se mostrará un prompt u otro.",
    )
    system_prompt = models.TextField(
        verbose_name="Prompt del sistema",
        help_text="Texto del prompt del sistema para el modelo de lenguaje. No olvidar añadir en el prompt '{seller_information}' cuando se trate de un prompt para procesar facturas.",
    )
    user_prompt = models.TextField(
        verbose_name="Prompt del usuario",
        help_text="Texto del prompt del usuario para el modelo de lenguaje.",
    )

    def __str__(self):
        return f"Configuración de prompts para '{self.processing_document.upper()}'"
    
    def truncated_system_prompt(self):
        return (self.system_prompt[:50] + '...') if len(self.system_prompt) > 50 else self.system_prompt
    
    def truncated_user_prompt(self):
        return (self.user_prompt[:50] + '...') if len(self.user_prompt) > 50 else self.user_prompt
    
    truncated_system_prompt.short_description = 'Prompt del sistema (truncado)'
    truncated_user_prompt.short_description = 'Prompt del usuario (truncado)'
    
    
    class Meta:
        verbose_name = "Configuración de Prompts"
        verbose_name_plural = "Configuración de Prompts"
        