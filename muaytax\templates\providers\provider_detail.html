{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}
{% block title %}Proveedor{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_providers:list' seller.shortname %}">Proveedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos generales</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" id="form" method="post" enctype="multipart/form-data" action="">
        {% crispy form %}
      </form>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      let asteriskSpan = document.querySelector('label[for="id_address_zip"] .asteriskField');
      if (!asteriskSpan) {
        // create the asterisk span element
        asteriskSpan = document.createElement('span');
        asteriskSpan.classList.add('asteriskField');
        asteriskSpan.innerHTML = '*';
        // add the asterisk span element
        const label = document.querySelector('label[for="id_address_zip"]');
        label.appendChild(asteriskSpan);
      }
      // set the id of asteriskSpan
      asteriskSpan.id = "span_id_address_zip";
      
      let asteriskSpanNifCifIva = document.querySelector('label[for="id_nif_cif_iva"] .asteriskField');
      if (!asteriskSpanNifCifIva) {
        // create the asterisk span element
        asteriskSpanNifCifIva = document.createElement('span');
        asteriskSpanNifCifIva.classList.add('asteriskField');
        asteriskSpanNifCifIva.innerHTML = '*';
        // add the asterisk span element
        const label = document.querySelector('label[for="id_nif_cif_iva"]');
        label.appendChild(asteriskSpanNifCifIva);
      }
      // set the id of asteriskSpan
      asteriskSpanNifCifIva.id = "span_id_nif_cif_iva";

      // Si Cambia el pais del proveedor, cambia el pais del NIF/CIF/IVA
      const onChangeProviderCountrynifCifIva = () => {
        const countrySelect = document.getElementById("id_country");
        const nifCifIvaCountrySelect = document.getElementById("id_nif_cif_iva_country");
        nifCifIvaCountrySelect.value = countrySelect.value;
      }
      const countrySelect = document.getElementById("id_country");
      countrySelect.addEventListener("change", () => {
        onChangeProviderCountrynifCifIva();
        onChangeProviderCountry();
      });

      onChangeProviderCountrynifCifIva();

      const onChangeProviderCountry = (force_update = true) => {
        const prov_country = document.getElementById('id_country');
        const address_country = document.getElementById('id_address_country');
        const address_zip = document.getElementById('id_address_zip');
        const span_address_zip = document.getElementById('span_id_address_zip');
        const span_nif_cif_iva = document.getElementById('span_id_nif_cif_iva');
        const nif_cif_iva = document.getElementById('id_nif_cif_iva');

        // Si Cambias el pais del proveedor, cambia el pais de la direccion
        if (prov_country && address_country) {
          if (address_country.value == "" || force_update == true) {
            address_country.value = prov_country.value;
          }
        }

        // Si el pais es España, el codigo postal es obligatorio, en caso contrario es opcional
        if (prov_country.value == "ES") {
          span_address_zip.innerHTML = '*';
          span_nif_cif_iva.innerHTML = '*';
          address_zip.setAttribute('required', 'required');
          nif_cif_iva.setAttribute('required', 'required');
        } else {
          span_address_zip.innerHTML = '';
          span_nif_cif_iva.innerHTML = '';
          address_zip.setAttribute('required', 'required');
          address_zip.removeAttribute('required');
          nif_cif_iva.setAttribute('required', 'required');
          nif_cif_iva.removeAttribute('required');
        }
      };
      onChangeProviderCountry(false);
    });
  </script>
{% endblock javascripts %}
