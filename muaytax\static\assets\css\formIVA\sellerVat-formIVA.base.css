/* === UTILIDADES GENERALES (FormIVA ) === */
.hidden {
  display: none;
}

.row-element {
  margin: 5px 0px;
  padding-top: 10px;
}

.row-activity {
  padding-top: 5px;
  padding-bottom: 10px;
}

.accordion-section {
  margin: 10px;
  padding: 10px 0px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.accordion-section h5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  cursor: pointer;
}

.toggle-icon {
  font-size: 20px;
  margin-left: 10px;
}

.col-custom {
  width: 20%;
  float: left;
}

.tooltip-icon {
  position: relative;
  cursor: pointer;
}

fieldset.form-borders {
  border: 1px groove #838383 !important;
  padding: 0 1.4em 1.4em;
  margin: 0 0 1.5em 0 !important;
  box-shadow: 0 0 0 0 #000;
}

legend.form-borders {
  text-align: left !important;
  width: inherit;
  padding: 0 10px;
  border-bottom: none;
  float: unset !important;
}

.col-form-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#general input::-webkit-outer-spin-button,
#general input::-webkit-inner-spin-button,
#percent_input input::-webkit-outer-spin-button,
#percent_input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.spinner-grow.animation-delay-1 {
  animation: spinner-grow .7s .1s linear infinite;
  color: #04ac64 !important;
}

.spinner-grow.animation-delay-2 {
  animation: spinner-grow .7s .3s linear infinite;
  color: #36e093 !important;
}

.spinner-grow.animation-delay-3 {
  animation: spinner-grow .7s .5s linear infinite;
  color: #04ac64 !important;
}

/* === ESTADOS Y EFECTOS === */
.readonly-style {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
  pointer-events: none;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
}

.shake {
  animation: shake 0.3s ease-in-out;
}

.tab-pane {
  display: none !important;
}

.tab-pane.active {
  display: block !important;
}

/* === SECCIÓN POR PAÍSES === */
.country-title {
  font-weight: bold;
  font-size: 1.2rem;
}

.flag-icon {
  width: 20px;
  height: auto;
  vertical-align: middle;
  margin-right: 6px;
  border-radius: 2px;
}

.country-container {
  padding: 20px;
  border-top: 2px solid #ddd;
  margin-top: 20px;
}

.country-documents-btn,
.country-migration-btn {
  background-color: #c8eed3;
  border: none;
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  transition: background-color 0.4s ease, transform 0.4s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.country-documents-btn:hover,
.country-migration-btn:hover {
  transform: translateY(-2px);
}

.country-form {
  display: none;
}

/* === VISUALIZACIÓN READONLY === */
.readonly-file-display {
  background-color: #e8effb;
  border: 1px solid #ced4da;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #495057;
  display: block;
  width: 100%;
  margin-top: 0;
}

.readonly-file-display a:hover {
  text-decoration: underline;
  color: rgb(95, 10, 10);
}

.readonly-country-btn {
  background-color: #f0f0f0 !important;
  color: #6c757d !important;
  border: 1px solid #d6d6d6 !important;
  font-weight: normal;
  cursor: default;
}

.readonly-country-btn:hover {
  background-color: #f0f0f0 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* === MODALES === */
.swal-custom-popup {
  font-family: 'Poppins', sans-serif;
  font-size: 0.95rem;
  line-height: 1.6;
}

.swal-custom-title {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.swal-custom-html ul {
  margin-top: 0;
  margin-bottom: 0.5rem;
  padding-left: 1.2rem;
}

.swal-custom-html li {
  margin-bottom: 0.35rem;
}

/* === COMENTARIOS Y ERRORES === */
.manager-comment {
  margin-top: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #dc3545;
  border-radius: 0.375rem;
  background-color: #f8f9fa;
  font-size: 0.9rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.manager-comment-title {
  color: #0d6efd;
  font-weight: bold;
  min-width: 150px;
}

.manager-comment-text {
  color: #dc3545;
  white-space: pre-wrap;
  flex: 1 1 auto;
}

.error-coment {
  border: 1px solid #dc3545 !important;
  box-shadow: none !important;
}

.partner-error-row {
  background-color: #FFE5E9 !important;
  border-left: 5px solid #E0364C !important;
}

.partner-error-card {
  border: 1px solid #E0364C;
  border-left: 6px solid #E0364C;
  background-color: #FFF4F4;
  padding: 12px 16px;
  border-radius: 8px;
  color: #4B1C1C;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.partner-error-card-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 6px;
}

.partner-error-card-comment {
  margin-left: 12px;
  padding-left: 8px;
  border-left: 2px solid #E0364C;
  font-size: 14px;
}

.partner-error-card-comment strong {
  font-weight: 600;
  color: #A60000;
  display: inline-block;
  margin-bottom: 2px;
}

.manager-card-comment {
  background-color: #FFF5F5;
  border: 1px solid #F5C2C7;
  border-left: 5px solid #E0364C;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 10px;
  font-size: 14px;
  color: #842029;
}

.manager-card-title {
  font-weight: 700;
  margin-bottom: 6px;
}

.manager-card-text {
  font-weight: 500;
}

/* === CONTENEDORES Y ESTRUCTURA === */
.rounded-container-box {
  border: 1px solid #CBD5E0;
  border-radius: 12px;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.section-title {
  font-weight: 600;
  color: #0F1C4D;
  font-size: 1.125rem;
  text-align: left;
  margin-bottom: 16px;
}

/* === TABLAS === */
.custom-country-table {
  border-collapse: collapse;
  width: 100%;
  font-family: "Poppins", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
}

.custom-country-table thead th {
  color: #0F1C4D;
  font-weight: 600;
  border-bottom: 1px solid #E3EAF2;
  padding: 8px;
  text-align: center;
  white-space: nowrap;
}

.custom-country-table thead th:first-child {
  text-align: left;
}

.custom-country-table thead th:last-child {
  text-align: right;
}

.custom-country-table tbody td {
  color: #0F1C4D;
  font-weight: 500;
  padding: 12px 8px;
  border-bottom: 1px solid #E9ECEF;
  vertical-align: middle;
  white-space: nowrap;
}

.custom-country-table tbody td:first-child {
  text-align: left;
}

.custom-country-table tbody td:nth-child(2) {
  text-align: center;
}

.custom-country-table tbody td:last-child {
  text-align: right;
}

/* === BOTONES === */
.pill-btn {
  padding: 6px 14px;
  min-width: 110px;
  text-align: center;
  border-radius: 8px;
  font-weight: 600;
  font-size: 13px;
  display: inline-block;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bg-success {
  background-color: #28C76F !important;
}

.bg-danger {
  background-color: #EA5455 !important;
}

.bg-secondary {
  background-color: #6C757D !important;
}

.no-border-header {
  border-bottom: none !important;
  padding-bottom: 0.3rem !important;
  margin-bottom: 0 !important;
}

.progress {
  height: 3px;
}

.progress-bar {
  transition: width 0.3s ease;
}

.btn_force_red {
  padding: 8px 16px;
  border-radius: 50px;
  background-color: var(--color-red);
  color: white;
  font-weight: 700;
  border: solid 2px var(--color-red);
  transition: all 0.2s ease;
}

button.btn_force_red:hover,
a.btn_force_red:hover {
  background-color: white !important;
  color: var(--color-red) !important;
  border: solid 2px var(--color-red) !important;
}

/* === transformaciones del form-m184 === */
.form-control:disabled, .form-control[readonly] {
  background-color: #e8effb;
  opacity: 1;
  cursor: not-allowed;

}

#phone-country-wrapper,
#phone-input-wrapper {
    transition: all 0.2s ease-in-out;
}

/* Añade * en rojo a los labels requeridos automáticos o con clase manual */
.form-group label[for]::after,
.required-label::after {
  content: "*";
  color: #dc3545;
  margin-left: 3px;
  font-weight: bold;
}

