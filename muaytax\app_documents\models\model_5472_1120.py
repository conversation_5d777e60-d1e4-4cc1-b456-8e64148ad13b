from django.core.validators import MinValueValidator
from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from muaytax.signals import disable_for_load_data
from muaytax.app_address.models.address import Address
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.currencies import Currency
from muaytax.utils.mixins import ExtraFieldsMixin

MODEL = '5472-1120'
TYPE_OF_ACTIVITY = (
    ('1', _('Venta de productos físicos o digitales')),
    ('2', _('Venta de servicios digitales.')),
    ('3', _('Programación y servicios informáticos.')),
)
TRANSACTION_TYPE_CHOICES = (
    ('1', _('Venta de productos')),
    ('2', _('Compra de productos')),
    ('3', _('Venta de servicios')),
    ('4', _('Compra de servicios')),
    ('5', _('Contribución')),
    ('6', _('Constitución')),
    ('7', _('Disolución')),
    ('8', _('Adquisición')),
    ('9', _('Distribución')),
)


class PresentedM54721120(ExtraFieldsMixin):
    seller = models.ForeignKey(
        Seller,
        verbose_name=_("Empresa"),
        related_name="seller_m5472_1120",
        on_delete=models.CASCADE,
        help_text=_("El modelo está asociado a la empresa que representa el administrador"),
    )
    member_address = models.ForeignKey(
        Address,
        verbose_name=_('Address of the member'),
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    # País de nacionalidad del miembro de la empresa
    member_country = models.ForeignKey(
        Country,
        verbose_name=_("País de nacionalidad del miembro"),
        on_delete=models.PROTECT,
        related_name='member_country_m5472_1120',
        null=True,
        blank=True,
    )
    # País de residencia fiscal del miembro de la empresa
    tax_residence_country = models.ForeignKey(
        Country,
        verbose_name=_('País de residencia fiscal del miembro'),
        on_delete=models.PROTECT,
        related_name='tax_residence_country_m5472_1120',
        null=True,
        blank=True,
    )
    # ¿En qué país desarrolla su actividad el miembro de la empresa?
    activity_country = models.ForeignKey(
        Country,
        verbose_name=_('País de actividad del miembro'),
        on_delete=models.PROTECT,
        related_name='activity_country_m5472_1120',
        null=True,
        blank=True,
    )
    passport = models.CharField(
        max_length=20,
        verbose_name=_('Número de pasaporte'),
        blank=True,
        null=True,
    )
    is_itin = models.BooleanField(
        verbose_name=_('¿Tiene ITIN?'),
        blank=True,
        null=True,
    )
    itin = models.CharField(
        max_length=100,
        verbose_name=_('ITIN'),
        blank=True,
        null=True,
        help_text=_('Número de identificación personal del contribuyente'),
    )
    casilla_4b3 = models.CharField(
        max_length=20,
        verbose_name=_('Casilla 4b(3)'),
        blank=True,
        null=True,
        help_text=_('Número de la casilla 4b(3) del formulario 5472 presentado el año anterior'),
    )
    # Casilla 1F (todos los años anteriores)
    casilla_1F_all_previous_years = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('Casilla 1F (todos los años anteriores)'),
        validators=[MinValueValidator(0)],
        blank=True,
        null=True,
        help_text=_('Suma de las casillas 1F del formulario 5472 presentado en años anteriores'),
    )

    # Descripción de la actividad principal
    desc_main_activity = models.TextField(
        max_length=50,
        verbose_name=_('Descripción de la actividad principal'),
        blank=True,
        null=True,
        help_text=_('Escribe máximo 50 caracteres'),
    )
    # Código de actividad NAICS
    type_of_activity = models.CharField(
        max_length=4,
        verbose_name=_('Tipo de Actividad'),
        choices=TYPE_OF_ACTIVITY,
        blank=True,
        null=True,
    )
    # ¿La LLC es una foreign owned disregarded entity?
    is_foreign_owned = models.BooleanField(
        verbose_name=_('¿Tiene la empresa actividad física en EEUU?'),
        # Una LLC se clasifica como foreign owned disregarded entity cuando es una LLC de un solo miembro
        # y este miembro es un extranjero no-residente en USA y además la LLC no tiene presencia en USA.
        help_text=_(
            'A LLC is classified as a foreign-owned disregarded entity when it is a single member LLC and '
            'this member is a non-resident foreigner in the USA and also the LLC has no presence in the USA.'
        ),
        blank=True,
        null=True,
    )
    # ¿Es el primer año que la LLC presenta un Form 5472-1120?
    is_first_year = models.BooleanField(
        verbose_name=_('¿Es el primer año que la empresa presenta un formulario 5472-1120?'),
        blank=True,
        null=True,
    )
    # ¿Es el último año que la LLC presenta este formulario?
    is_last_year = models.BooleanField(
        verbose_name=_('¿Es el último año que la empresa presenta este formulario?'),
        blank=True,
        null=True,
        default=False,
        help_text=_('Indique si es el último año que la empresa presenta el formulario 5472-1120 y se va a disolver.'),
    )

    # Principales países donde la LLC desarrolla sus actividades
    main_activity_countries = models.ManyToManyField(
        Country,
        verbose_name=_('Principales países donde la empresa desarrolla sus actividades'),
        related_name='main_activity_countries_m5472_1120',
        blank=True,
    )
    # Activos totales de la empresa
    total_assets = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('Activos totales de la empresa'),
        validators=[MinValueValidator(0)],
        blank=True,
        null=True,
    )

    is_sl_self_employed = models.BooleanField(
        verbose_name=_('¿Eres freelance o miembro de otra empresa?'),
        blank=True,
        null=True,
    )

    year = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("Año de presentación del modelo 5472-1120")
    )
    is_processed = models.BooleanField(
        default=False,
        verbose_name=_("EL modelo 5472-1120 ha sido procesado?"),
    )

    # Fecha de incorporación
    incorporation_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de incorporación de la empresa"
    )
    # traducciones para el pdf
    translations = models.JSONField(
        verbose_name=_('Traducciones'),
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = _('Contracted model {model}').format(model=MODEL)
        verbose_name_plural = _('Contracted models {model}').format(model=MODEL)

    def __str__(self):
        return _('Model {model} from {seller} ({year})').format(model=MODEL, seller=self.seller, year=self.year)

@receiver(post_save, sender=PresentedM54721120)
@disable_for_load_data
def post_save_presented_m5472_1120(sender, instance, created, **kwargs):
    from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
    from muaytax.app_documents.models import ProcessedForm
    
    if instance.pk:
        processed_form = ProcessedForm.objects.filter(
            seller=instance.seller,
            year=instance.year,
            category_form__code='model_US5472_1120'
        ).first()
        if processed_form:
            processed_form.is_form_processed = instance.is_processed
            processed_form.save()
    
    seller = instance.seller
    if seller and seller.pk:
        update_cached_seller_signal_task.delay(seller_id=seller.pk, year=instance.year)

@receiver(post_delete, sender=PresentedM54721120)
@disable_for_load_data
def post_delete_presented_m5472_1120(sender, instance, **kwargs):
    from muaytax.app_documents.models import ProcessedForm
    from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task

    processed_form = ProcessedForm.objects.filter(
        seller=instance.seller,
        year=instance.year,
        category_form__code='model_US5472_1120'
    ).first()
    if processed_form:
        processed_form.delete()

    seller = instance.seller
    if seller and seller.pk:
        update_cached_seller_signal_task.delay(seller_id=seller.pk, year=instance.year)

class AccountingRecord(models.Model):
    pm5472_1120 = models.ForeignKey(
        PresentedM54721120,
        verbose_name=_('Contracted model {model}').format(model=MODEL),
        related_name='accounting_m5472_1120',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="accounting_record_m5472_1120_seller",
        verbose_name="Vendedor",
    )

    date = models.DateField(
        verbose_name=_('Date'),
        null=True,
        blank=True
    )
    transaction_type = models.CharField(
        max_length=1,
        verbose_name=_('Tipo de transacción'),
        choices=TRANSACTION_TYPE_CHOICES,
        null=True,
        blank=True,
    )
    description = models.TextField(
        verbose_name=_('Descripción'),
        null=True,
        blank=True)

    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('Monto'),
        validators=[MinValueValidator(0)],
        null=True,
        blank=True,
    )
    currency = models.ForeignKey(
        Currency,
        verbose_name=_('Moneda'),
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    # total currency in dollars
    total_currency = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('Total en dólares'),
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.pm5472_1120:
            return _('ID: {id} SELLER: {seller} YEAR: {year} DESC:{desc}').format(
                id=self.id,
                seller=self.pm5472_1120.seller,
                year=self.pm5472_1120.year,
                desc=self.description
            )
        return _('ID: {id} DESC: {desc}').format(id=self.id, desc=self.description)

    class Meta:
        verbose_name = _('Dato contable')
        verbose_name_plural = _('Datos contables')
