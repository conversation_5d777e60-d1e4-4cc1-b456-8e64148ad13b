from django.db import models

class ReconciliationType(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=10,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Tipo de Conciliación"
        verbose_name_plural = "Tipos de Conciliaciónes"
    
    def __str__(self):
        return self.description
    
# @admin.register(ReconciliationType)
# class ReconciliationTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
