{% extends "layouts/base.html" %}
{% load static i18n crispy_forms_field crispy_forms_filters crispy_forms_tags %}
{% block title %}
    {% blocktranslate with model_n='BE-15' %}Model {{ model_n }}{% endblocktranslate %}
{% endblock title %}
{% block stylesheets %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
    <link href="{% static 'assets/css/select2/select2.min.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/m5472-1120/form.css' %}" rel="stylesheet">
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/htmx/htmx.min-v1.6.0.js"></script>
    <link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">
    <style></style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        {% blocktranslate with model_n='BE-15' %}Model {{ model_n }}{% endblocktranslate %}
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="#">{% translate 'Declaraciones' %}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href=".">
                            {% blocktranslate with model_n='BE-15' %}Model {{ model_n }}{% endblocktranslate %}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}
{% block content %}
<div class="card-body">
    <div class="col">
        <form action="" method="post" id="form_BE15" enctype="multipart/form-data">
            {% csrf_token %}
            <!-- tabs -->
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="general-tab" data-bs-toggle="tab" href="#general" role="tab"
                        aria-controls="general" aria-selected="true">
                        <i class="fab fa-wpforms"></i>
                        <span class="d-none d-md-inline">&nbsp;{% translate 'Información personal' %}</span>
                        <i class="feather icon-alert-circle hidden"
                            style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="company-tab" data-bs-toggle="tab" href="#company" role="tab" aria-controls="company"
                        aria-selected="false">
                        <i class="fas fa-users"></i>
                        <span class="d-none d-md-inline">&nbsp;{% translate 'Información de la Empresa' %}</span>
                        <i class="feather icon-alert-circle hidden"
                            style="margin-left: 12px; margin-right: 0; color: #dc3545;"></i>
                    </a>
                </li>
            </ul>
            <!-- content -->
            <div class="tab-content" id="myTabContent">
                <!-- progress bar -->
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                        role="progressbar" aria-valuenow="25" aria-valuemin="0" 
                        aria-valuemax="100" style="width: 25%">
                    </div>
                </div>
                <!-- general tab -->
                <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                    <div class="row">
                        <div class="col">
                            <div class="card" style="box-shadow: none;">
                                <div class="card-header">
                                    <h5>{% translate 'Datos generales del miembro' %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if not is_processed %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        A continuación te solicitaremos información sobre tus datos personales y de residencia para poder aportar la información más actualizada al reporte.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                    {% endif %}
                                    {% include "sellers/include/model_BE15/model_BE15_general_info.html" %}
                                </div>
                                <div class="card-header">
                                    <h5>{% translate 'Datos de dirección del miembro' %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if not is_processed %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        <strong>Recuerda</strong> que a continuación te solicitaremos los datos de dirección correspondientes al miembro miembro.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                    {% endif %}
                                    {% include "sellers/include/model_BE15/model_BE15_general_address.html" %}
                                </div>
                                <div class="card-footer d-flex justify-content-center">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            {% if not is_processed %}
                                            <button type="button" class="btn btn-secondary"
                                                    name="save-submit" id="submit-button"
                                                    onclick="saveForm()">{% translate 'Guardar y seguir editando' %}
                                            </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-dark ml-2" onclick="changeTab('company-tab')">
                                                {% translate 'Siguiente' %}
                                                <i class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- company tab -->
                <div class="tab-pane fade" id="company" role="tabpanel" aria-labelledby="company-tab">
                    <div class="row">

                        <!-- BEA | preguntas -->
                        <div class="col-sm-12" id="question-block" {% if resp_contacted_bea_value %}style="display: none;"{% endif %}>
                            <!-- Pregunta: ¿Has sido contactado por el BEA? -->
                            <div class="card" style="box-shadow: none;" id="resp_contacted_bea_block">
                                <div class="card-header">
                                    <h5>{% translate '¿Has sido contactado por el BEA indicándote que debes presentar el formulario?' %}</h5>
                                </div>  
                                <div class="card-body">
                                    <input type="hidden" id="id_resp_contacted_bea" name="resp_contacted_bea" value="{{ form.resp_contacted_bea.value|default:'' }}">
                                    <button type="button" id="btn1-yes" class="btn btn-primary" onclick="setSelection('True', 'id_resp_contacted_bea')">Sí</button>
                                    <button type="button" id="btn1-no" class="btn btn-primary" onclick="setSelection('False', 'id_resp_contacted_bea')">No</button>
                                </div>
                            </div>
                            <!-- Pregunta: ¿Enviar los datos al BEA? -->
                            <div class="card" style="box-shadow: none; display: none;" id="resp_request_bea_block">
                                <div class="card-header">
                                    <h5>{% translate 'Aún así nuestra recomendación es presentarlo, de esa manera puedes adelantarte a posibles fallos de comunicación por parte del BEA que puedan causarte perjuicio en el futuro' %}</h5>
                                </div> 
                                <div class="card-body">
                                    <p class="font-weight-bold text-danger" style="margin-left: 30px">¿estás de acuerdo?</p>
                                    <div>
                                        <input type="hidden" id="id_resp_request_bea" name="resp_request_bea" value="">
                                        <button type="button" id="btn-yes" class="btn btn-primary" onclick="setSelection('True', 'id_resp_request_bea')">Sí, quiero enviar mis datos al BEA</button>
                                        <button type="button" id="btn-no" class="btn btn-primary" onclick="setSelection('False', 'id_resp_request_bea')">No, únicamente quiero actualizar mis datos en MUAYTAX</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--Formulario completo-->
                        <div class="col-sm-12">
                            <div class="card" id="full-form" style="box-shadow: none; display: {% if resp_contacted_bea_value %}block{% else %}none{% endif %};">
                                <!-- Respuesta de la pregunta BEA -->
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">{% translate 'Respuesta de la pregunta BEA' %}</h5>
                                    <button type="button" class="btn btn-secondary rounded" onclick="editRequestBea()" {% if is_processed %}style="display: none;"{% endif %}>Editar Respuesta</button>
                                </div>
                                <div class="card-body">
                                    {% if not is_processed %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert" id="alert-bea">
                                        <strong>Recuerda</strong> nuestra recomendación es presentarlo, de esa manera puedes adelantarte a posibles fallos de comunicación por parte del BEA.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                    {% endif %}
                                    <div class="form-group d-flex justify-content-center">
                                        <div class="col-lg-6 text-center">
                                            <p>Contactado por el BEA: <span class="mx-4" id="res-contactedBea-text"></span></p>
                                            <p>Enviar mis datos al BEA: <span class="mx-4" id="res-bea-text"></span></p>
                                        </div>
                                    </div>
                                </div>
                                <!-- Datos de la Empresa-->
                                <div class="card-header">
                                    <h5>{% translate 'Datos de la Empresa' %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if not is_processed %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        <strong>Recuerda</strong> que aquí debes ingresar todos los datos correspondientes a la Empresa.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                    {% endif %}
                                    {% include "sellers/include/model_BE15/model_BE15_company_info.html" %}
                                </div>
                                <!-- Dirección de la empresa-->
                                <div class="card-header">
                                    <h5>{% translate 'Dirección de la empresa' %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if not is_processed %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        Indica los datos de dirección de la empresa.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                    {% endif %}
                                    {% include "sellers/include/model_BE15/model_BE15_company_address.html" %}
                                </div>
                                <!-- Datos fiscales-->
                                <div class="card-header">
                                    <h5>{% translate 'Datos fiscales (Año 2023)' %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if not is_processed %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        Indica las cifras a fecha 31 de diciembre y en dólares americanos. Si tienes activos/pasivos en otra moneda, usa la tasa de cambio a 31 de diciembre de 2023.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                    {% endif %}
                                    {% include "sellers/include/model_BE15/model_BE15_company_assets.html" %}
                                </div>
                                <div class="card-footer d-flex justify-content-center">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <button type="button" class="btn btn-light" onclick="changeTab('general-tab')"><i
                                                class="feather icon-arrow-left"></i>{% translate 'Anterior' %}
                                            </button>
                                            {% if not is_processed %}
                                            <button type="button" class="btn btn-secondary"
                                                    name="save-submit" id="submit-button"
                                                    onclick="saveForm()">{% translate 'Guardar y seguir editando' %}
                                            </button>
                                            <button id="submit-button" type="button"
                                                onclick="finishForm()" class="btn btn-primary">
                                                {% translate 'Finish' %}
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- modal confirmación -->
            <div class="modal fade" id="confirmForm"
                tabindex="-1" aria-labelledby="animateModalLabel" 
                aria-modal="true" role="dialog">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body text-center">
                            <div class="swal2-icon swal2-question swal2-icon-show mb-3" style="display: flex; margin-top: 0;">
                                <div class="swal2-icon-content">?</div>
                            </div>
                            <h5>
                                {% blocktranslate with model_n='BE-15' %}
                                Do you want to submit the information for the model {{ model_n }}?
                                {% endblocktranslate %}
                            </h5>
                            <p class="mt-3">{% translate "Before proceeding, make sure all the data is correct." %}</p>
                        </div>
                        <div class="modal-footer justify-content-center">
                            <button type="button" class="btn btn-outline-danger"
                                data-bs-dismiss="modal">
                                {% translate 'No, back' %}
                            </button>
                            <button type="button" id="send-form-be15"
                                onclick="submitForm()"
                                name="submit-process" class="btn btn-primary">
                                {% translate 'Yes, submit' %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>



<div class="modal fade modal-animate anim-blur " id="LoadingModal" tabindex="-1" role="dialog"
    aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
            <div class="modal-body d-flex flex-column justify-content-center align-items-center">
                <div class="modal-body">
                    <div class="d-flex justify-content-center align-items-center text-center">
                        <div class="spinner-grow text-success animation-delay-1 " role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-grow text-success animation-delay-2" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-grow text-success animation-delay-3" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>&nbsp;</p>
                        <img style="width:110px;" src="{% static 'assets/images/logo.png' %}" />
                    </div>
                    <p class="text-white text-center mb-0">
                        <b>{% translate 'The information is sending. Do not close or refresh the page' %}</b>
                    </p>
                    <p class="text-white text-center mb-0"><b>{% translate 'Please wait...' %}</b></p>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
{% block javascripts %}
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script>
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer);
        toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });

    const saveForm = () => {
        $('#form_BE15').find(':input').removeAttr('required');
        Toast.fire({
            icon: 'info',
            title: 'Guardando datos...',
            timer: 30000,
        });

        $('button[id="submit-button"]').prop('disabled', true);
        var form = $('#form_BE15');
        var url = form.attr('action');
        var formData = new FormData(form[0]);
        $.ajax({
            type: 'POST',
            url: url,
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                Toast.fire({
                    icon: 'success',
                    title: 'Datos guardados correctamente'
                });
                $('button[id="submit-button"]').prop('disabled', false);
            },
            error: function(data) {
                Toast.fire({
                    icon: 'error',
                    title: 'Error al guardar los datos'
                });
                $('button[id="submit-button"]').prop('disabled', false);
            }
        });
    }

    const finishForm = () => {
        const form = $('#form_BE15');
        form.find(':input').attr('required', 'required');

        if (!form[0].checkValidity()) {
            let errorElements = form.find(':invalid');

            errorElements.each(function () {
                $(this).addClass('is-invalid');
                // Añadir la clase is-invalid al contenedor de Select2
                if ($(this).hasClass('select2-hidden-accessible')) {
                    $(this).next('.select2-container').find('.select2-selection').addClass('is-invalid');
                }
                // si es un radiobutton, añadir la clase is-invalid al contenedor
                if ($(this).is(':radio')) {
                    const parent = $(this).parent();
                    let p = parent.nextAll('.invalid-feedback:first');
                    if (p.length === 0) {
                        p = $('<p class="invalid-feedback"></p>');
                        // si el validationMessage viene de no cumplir el pattern, se cambia el mensaje por el title
                        if (this.validity.patternMismatch) {
                            p.text(this.title);
                        } else {
                            p.text(this.validationMessage);
                        }
                        parent.parent().append(p);
                    } else {
                        if (this.validity.patternMismatch) {
                            p.text(this.title);
                        } else {
                            p.text(this.validationMessage);
                        }
                    }
                } else {
                    let parentInputElement = '';
                    let p = $(this).nextAll('.invalid-feedback:first');
                    if ($(this).hasClass('choices__input')) {
                        $(this).parent().addClass('choices_is_invalid');
                        parentInputElement = $(this).closest('.col-lg-4');
                        parentInputElement.find('.invalid-feedback').remove();
                    } else if ($(this).parent().hasClass('input-group')) {
                        parentInputElement = $(this).parent().parent();
                        parentInputElement.find('.invalid-feedback').remove();
                    } else {
                        parentInputElement = $(this).parent();
                    }

                    if (p.length === 0) {
                        p = $('<p class="invalid-feedback"></p>');
                        // si el validationMessage viene de no cumplir el pattern, se cambia el mensaje por el title
                        if (this.validity.patternMismatch) {
                            p.text(this.title);
                        } else {
                            p.text(this.validationMessage);
                        }
                        parentInputElement.append(p);
                    } else {
                        if (this.validity.patternMismatch) {
                            p.text(this.title);
                        } else {
                            p.text(this.validationMessage);
                        }
                    }
                }
            });

            const firstErrorElement = errorElements.first();
            const tabId = firstErrorElement.closest('.tab-pane').attr('id') + '-tab';
            const currentTab = $('#myTab a.nav-link.active');
            const currentTabId = currentTab.attr('id');
    
            if (currentTabId !== tabId) {
                changeTab(tabId);
                setTimeout(function () {
                    firstErrorElement[0].focus();
                    firstErrorElement[0].scrollIntoView();
                }, 600);
            }
            removeHiddenInTab();
            return false;
        } else {
            event.preventDefault();
            $('#confirmForm').modal('show');
        }
    }

    const submitForm = () => {
        $('#confirmForm').modal('hide');
        const loadingModal = document.getElementById('LoadingModal');
        const modal = new bootstrap.Modal(loadingModal);
        modal._config.backdrop = 'static';
        modal._config.keyboard = false;
        modal.show();
        $('#form_BE15').append('<input type="hidden" name="process-submit" value="submit-process">');
        $('#form_BE15').submit();
    }
    const changeTab = (tabId) => {
        const elem = document.getElementById(tabId);
        if (elem.closest('.tab-pane')) {
            changeTab(elem.closest('.tab-pane').id + '-tab');
        }
        const tab = new bootstrap.Tab(elem);
        tab.show();
        elem.scrollIntoView();
    }

    function removeHiddenInTab() {
        const tabs = $('#myTab a.nav-link');
        tabs.each(function () {
            const tabId = $(this).attr('id');
            const tab = $('#' + tabId);
            const tabPaneId = tab.attr('aria-controls');
            const tabPane = $('#' + tabPaneId);
            const invalidElements = tabPane.find('.is-invalid');
            if (invalidElements.length > 0) {
                tab.find('.feather').removeClass('hidden');
            }
        });
    }

    const updateProgressBar = () => {
        const activeTab = $('#myTab a.nav-link.active');
        const index = $('#myTab a.nav-link').index(activeTab);
        const progress = (index + 1) / $('#myTab a.nav-link').length * 100;

        $('.progress-bar').css('width', progress + '%');
        $('.progress-bar').attr('aria-valuenow', progress);
    }

    updateProgressBar();

    $('#myTab a').on('shown.bs.tab', e => updateProgressBar());

    $(document).ready(function() {
        var productAndServices = JSON.parse('{{ product_service | escapejs }}');

        function populateSelectOptions(selectedType, savedProductService) {
            $('#id_products_and_services').empty();
            $('#id_products_and_services').append('<option value="" disabled selected>------</option>');
            productAndServices.forEach(function(item) {
                if (item.fields.product_service_type === selectedType) {
                    var optionText = item.pk + ' | ' + item.fields.description;
                    var $option = $('<option>', { value: item.pk }).text(optionText);
                    if (savedProductService && item.pk === savedProductService) {
                        $option.prop('selected', true);
                    }
                    $('#id_products_and_services').append($option);
                }
            });
        }

        $('input[name="activity_type"]').change(function() {
            if ($(this).is(':checked')) {
                var selectedType = $(this).val(); // Get the value of the selected radio button
                populateSelectOptions(selectedType); // Populate select options based on the selected type
            }
        });

        var initialSelectedType = $('input[name="activity_type"]:checked').val();
        var initialDBProductService = $('#id_products_and_services').val();
        populateSelectOptions(initialSelectedType, initialDBProductService);
        
    });

    function showFullForm(value, target) {
        console.log("value: " + value + " target: " + target)
        document.getElementById('question-block').style.display = 'none';
        document.getElementById('full-form').style.display = 'block';

        let responseText = '¿?';
        if (value === 'True') {
            responseText = 'Sí';
        } else if (value === 'False') {
            responseText = 'No';
        } else {
            responseText = '¿?';
        }

        if (target === 'id_resp_contacted_bea') {
            document.getElementById('res-contactedBea-text').innerText = responseText;
        } else if (target === 'id_resp_request_bea') {
            document.getElementById('res-bea-text').innerText = responseText;
        }
    }

    function setSelection(value, target) {
        console.log("SETSelection | value: " + value + " target: " + target)
        document.getElementById(target).value = value;
        let responseText = value === 'True' ? 'Sí' : 'No';
        if (target === 'id_resp_contacted_bea') {
            if (value === 'True') {
                document.getElementById('resp_request_bea_block').style.display = 'none';
                document.getElementById('id_resp_request_bea').value = '';
                document.getElementById('res-bea-text').parentElement.style.display = 'none';
                document.getElementById('alert-bea').style.display = 'none';
                showFullForm(value, target);
            } else {
                document.getElementById('resp_request_bea_block').style.display = 'block';
                document.getElementById('res-contactedBea-text').innerText = responseText;
            }
        }
        if (target === 'id_resp_request_bea') {
            document.getElementById('resp_request_bea_block').style.display = 'block';
            document.getElementById('res-bea-text').parentElement.style.display = 'block';
            document.getElementById('res-bea-text').innerText = responseText;
            showFullForm(value, target);
        }
    }

    function editRequestBea() {
        document.getElementById('full-form').style.display = 'none';
        document.getElementById('question-block').style.display = 'block';
        document.getElementById('resp_request_bea_block').style.display = 'none';
        document.getElementById('res-bea-text').parentElement.style.display = 'block';
    }

    document.addEventListener("DOMContentLoaded", function() {
        // Obtén los valores desde el backend utilizando Django Template Language
        var contactedBeaValue = "{{ resp_contacted_bea_value }}";
        var beaValue = "{{ resp_request_bea_value }}";
        console.log(contactedBeaValue)
        console.log(beaValue)
        // Establece los valores en los elementos HTML al cargar la página
        if (beaValue != "None") {
            document.getElementById("res-bea-text").innerText = beaValue;
            document.getElementById('id_resp_request_bea').value = beaValue.replace('Sí', 'True').replace('No', 'False');
        } else {
            document.getElementById("res-bea-text").parentElement.style.display = 'none';
        }
        
        document.getElementById("res-contactedBea-text").innerText = contactedBeaValue;
        document.getElementById('id_resp_contacted_bea').value = contactedBeaValue.replace('Sí', 'True').replace('No', 'False');
    });

    
    
    
</script>
{% endblock javascripts %}