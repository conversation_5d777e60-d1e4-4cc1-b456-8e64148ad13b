from django.urls import path

from muaytax.app_invoices import views

app_name = "app_invoices"
urlpatterns = [
    # SELLER INVOICES | START ######################################################
    path(
        "sellers/<shortname>/invoice/new/",
        view=views.SellerInvoiceNewView.as_view(),
        name="seller_invoice_new",
    ),
    path(
        "sellers/<shortname>/invoice/<invoiceId>/",
        view=views.SellerInvoiceView.as_view(),
        name="seller_invoice",
    ),
    path(
        "sellers/<shortname>/invoice/<invoiceId>/next/",
        view=views.SellerInvoiceNextView.as_view(),
        name="seller_invoice_next",
    ),
    path(
        "sellers/<shortname>/invoice/<invoiceId>/file/",
        view=views.SellerInvoiceFileGen.as_view(),
        name="seller_invoice_file",
    ),
    path(
        "sellers/<shortname>/invoice/<invoiceId>/fileweb/",
        view=views.SellerInvoiceFileWeb.as_view(),
        name="seller_invoice_fileweb",
    ),
    path(
        "sellers/<shortname>/invoice/<pk>/delete/",
        view=views.InvoiceDeleteView.as_view(),
        name="invoice_delete",
    ),
    path(
        "sellers/<shortname>/generate_csv/",
        view=views.SellerInvoiceListCSV.as_view(),
        name="generate_csv",
    ),
    path(
        "sellers/<shortname>/invoices/",
        view=views.SellerInvoicesView.as_view(),
        name="seller_invoices",
    ),
    path(
        "sellers/<shortname>/invoices/transfers/",
        view=views.SellerInvoicesView.as_view(),
        name="seller_invoices_transfers",
    ),
    path(
        "sellers/<shortname>/invoices/data",
        view=views.SellerInvoicesDTView.as_view(),
        name="seller_invoices_dt",
    ),
    path(
        "sellers/<shortname>/invoices/json",
        view=views.SellerInvoicesJSONView.as_view(),
        name="seller_invoices_json",
    ),
    path(
        "sellers/<shortname>/invoices/upload",
        view=views.SellerInvoiceUploadView.as_view(),
        name="seller_invoices_upload",
    ),
    path(
        "sellers/<shortname>/invoices/upload/ticket",
        view=views.SellerInvoiceUploadView.as_view(),
        name="seller_invoices_upload_ticket",
    ),
    path(
        "sellers/<shortname>/invoices/wizard-dua/import",
        view=views.SellerInvoiceUploadImportView.as_view(),
        name="seller_invoices_upload_import",
    ),
    path(
        "sellers/<shortname>/invoices/uploadtxt",
        view=views.SellerInvoiceUploadView.as_view(),
        name="seller_invoices_upload_txt",
    ),
    path(
        "sellers/<shortname>/invoices/uploadcsv",
        view=views.SellerInvoiceUploadCSVView.as_view(),
        name="seller_invoices_upload_csv",
    ),
    path(
        "sellers/<shortname>/affiliate_program/upload/",
        view=views.AffiliateInvoiceUploadView.as_view(),
        name="seller_invoices_upload_affiliate",
    ),
    path(
        "sellers/<shortname>/invoices/create",
        view=views.SellerInvoiceCreateView.as_view(),
        name="seller_invoice_create",
    ),
    path(
        "sellers/<shortname>/invoices/create/ticket",
        view=views.SellerInvoiceCreateTicketView.as_view(),
        name="seller_invoice_create_ticket",
    ),
    path(
        "sellers/<shortname>/invoices/create/payroll",
        view=views.SellerInvoiceCreatePayrollView.as_view(),
        name="seller_invoice_create_payroll",
    ),
    path(
        "sellers/<shortname>/invoices/createtxt",
        view=views.SellerInvoiceCreatetxtView.as_view(),
        name="seller_invoice_create_txt",
    ),
    # path(
    #     "sellers/<shortname>/invoices/createtxt2",
    #     view=views.SellerInvoiceCreatetxtView2.as_view(),
    #     name="seller_invoice_create_txt2",
    # ),
    path(
        "sellers/<shortname>/invoices/createtcsv",
        view=views.SellerInvoiceCreateCSVView.as_view(),
        name="seller_invoice_create_csv",
    ),
    path(
        "sellers/<shortname>/invoices/massiveInvoices",
        view=views.SellerInvoiceMassiveView.as_view(),
        name="seller_invoices_massive",
    ),
    path(
        "sellers/<shortname>/invoices/<category>",
        view=views.SellerInvoicesView.as_view(),
        name="seller_invoices_category",
    ),
    path(
        "sellers/<shortname>/invoices/massiveDownload/",
        view=views.SellerInvoicesMassiveDownload.as_view(),
        name="seller_invoices_massive_download",
    ),
    path(
        "vatFRList/invoices/Download/",
        view=views.SellerInvoiceFRvatDownloadCSV.as_view(),
        name="vat_FR_list_download_cvs",
    ),
    path(
        "sellers/<shortname>/invoices/setRectifyingInvoice/",
        view=views.SetRectifyngInvoiceView.as_view(),
        name="seller_invoices_set_rectifying_invoice",
    ),
    
    # Solicitud currencyapi
    path('api/get-tax-conversion/', views.get_tax_conversion, name='get_tax_conversion')

    # SELLER INVOICES | END #######################################################
]

# New Invoice Uploader URLS
urlpatterns += [
    path(
        "sellers/<shortname>/invoices/process/",
        view=views.InvoiceUploderProcessView.as_view(),
        name="seller_invoice_process",
    ),
]

# ELECTRONIC INVOICE URLS
urlpatterns += [
    path(
        "sellers/<shortname>/invoices/createtxml/<pk>",
        view=views.CreateXMLInvoiceView.as_view(),
        name="seller_invoice_create_xml",
    ),
    path(
        "sellers/<shortname>/invoices/createxmlVeriFactu/<pk>",
        view=views.CreateXMLVeriFactuView.as_view(),
        name="invoice_create_verifactu_xml",
    ),
    path(
        "sellers/<shortname>/invoices/sendXMLVeriFactuAEAT/<pk>",
        view=views.SendInvoiceVeriFactuToAEATView.as_view(),
        name="invoice_send_verifactu_xml_AEAT",
    ),
    path(
        "sellers/<shortname>/invoices/checkCertDig/",
        view=views.CheckDigitalCertificateView.as_view(),
        name="invoice_check_cert_dig",
    ),
    path(
        "sellers/<shortname>/invoices/veriFactuInvoiceList/",
        view=views.VerifactuInvoiceList.as_view(),
        name="invoice_verifactu_list",
    ),
    path(
        "sellers/<shortname>/invoices/veriFactuInvoiceListDT/",
        view=views.VerifactuInvoiceListDT.as_view(),
        name="invoice_verifactu_listDT",
    ),
    path(
        "verifactu/downloadResponsible/",
        view=views.DownloadResponsibleDeclarationVeriFactu.as_view(),
        name="download_responsible_declaration_verifactu",
    ),
]