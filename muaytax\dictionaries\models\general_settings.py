from django.db import models

class GeneralSettings(models.Model):
    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=150,
        verbose_name="<PERSON>ódigo"
    )

    value = models.Char<PERSON>ield(
        max_length=150,
        verbose_name="Valor"
    )
    description = models.CharField(
        max_length=150,
        verbose_name="Descripción"
    )

    class Meta:
        verbose_name = "Configuración General"
        verbose_name_plural = "Configuraciones Generales"

    def __str__(self):
        return self.code
    
# @admin.register(GeneralSettings)
# class GeneralSettingsAdmin(admin.ModelAdmin):
#     list_display = ["code", "value", "description"]
#     search_fields = ["code", "value", "description"]