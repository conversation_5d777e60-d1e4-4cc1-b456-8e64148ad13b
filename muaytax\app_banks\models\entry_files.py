from django.db import models
from django.core.validators import FileExtensionValidator

class EntryFile (models.Model):

    # id -> AutoGen

    seller = models.ForeignKey(
        "sellers.Seller",
        related_name = "entry_file_seller",
        on_delete = models.CASCADE,
    )

    file = models.FileField(
        "document", upload_to="xls_entry_list/", validators=[FileExtensionValidator(["xlsx","xls", "zip", "rar"])]
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Excel Asiento Contable"
        verbose_name_plural = "Excels Asientos Contables"
    
    def __str__(self):
        if self.file and self.file.name:
            return self.file.name.replace("media/", "").replace("xls_entry_list/", "")
        else:
            return "Excel Reconciliation.{}".format(self.pk)
    
    def get_file_url(self):
        return f'/media/xls_entry_list/{self.file.name}'


