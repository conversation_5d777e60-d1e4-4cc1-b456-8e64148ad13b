from django.core import validators
from django.db import models
from django.contrib import admin


class SellerListManagementUS(models.Model):

    seller = models.ForeignKey(
        "sellers.seller", 
        on_delete=models.CASCADE, 
        verbose_name="empresa",
        related_name="seller_list_management_us"
    )

    show = models.BooleanField(
        default=True,
        verbose_name="Mostrar",
    )

    year = models.PositiveIntegerField(
        verbose_name="Año",
        validators=[
            validators.MinValueValidator(2000),
            validators.MaxValueValidator(2050),
        ],
    )

    period = models.ForeignKey(
        "dictionaries.Period",
        on_delete=models.PROTECT,
        related_name="period_seller_list_management_us",
        verbose_name="Periodo",
        validators=[
            validators.RegexValidator(
                regex=r"Q[1-4]|0A",
                message="Periodo no válido",
                code="invalid_period",
            )
        ],
    )

    model_5472 = models.Char<PERSON>ield(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 5472-1120",
        validators=[
            validators.RegexValidator(
                regex=r"not-started|not-processed|processed|required|not-required|pending|agreed|disagreed|presented|fax_send",
                message="Estado no válido",
                code="invalid_model_5472_1120",
            )
        ],
    )

    model_7004 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo 7004",
        validators=[
            validators.RegexValidator(
                regex=r"required|not-required|pending|agreed|disagreed|presented",
                message="Estado no válido",
                code="invalid_model_7004",
            )
        ],
    )

    model_be_15 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo BE-15",
        validators=[
            validators.RegexValidator(
                regex=r"not-started|not-processed|processed|required|not-required|pending|agreed|disagreed|presented|fax_send",
                message="Estado no válido",
                code="invalid_model_be_15",
            )
        ],
    )

    model_es_184 = models.CharField(
        null=True, blank=True,
        max_length=20,
        verbose_name="Modelo ES-184",
        validators=[
            validators.RegexValidator(
                regex=r"not-started|not-processed|processed|required|not-required|pending|agreed|disagreed|presented",
                message="Estado no válido",
                code="invalid_model_es_184",
            )
        ],
    )

    model_json_result = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON Resultados de Modelos",
    )

    class Meta:
        verbose_name = "Listado Gestoría EEUU"
        verbose_name_plural = "Listado Gestoría EEUU"
        constraints = [
            models.UniqueConstraint(fields=['seller', 'year', 'period'], name='seller_list_management_us_unique_seller_year_period'),
        ]

    def __str__(self):
        return f"Gestoría EEUU - {self.seller} - {self.year} - {self.period}"
    

@admin.register(SellerListManagementUS)
class SellerListVatESAdmin(admin.ModelAdmin):
    list_display = ["id", "seller", "year", "period", "show"]
    list_filter =  ["year", "period"]
    search_fields = ["id", "seller__name", "year", "period__description", "show"]