{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}
{% block stylesheets %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css"/>
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css"/>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
    <style>
        .table .row {
            padding: 5px 0px;
            border-bottom: 1px solid #ddd;
        }

        .tooltip-inner {
            min-width: max-content;
        }

        #modalAgreed .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        #modalAgreed .modal-content {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        #modalAgreed .modal-header {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            background-color: #343a40;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        #modalAgreed .modal-body {
            padding: 1.5rem;
        }

        #modalAgreed .modal-footer {
            padding: 1rem;
            border-top: 1px solid #dee2e6;
            justify-content: space-between;
        }

        #modalAgreed .btn {
            padding: 0.5rem 1.5rem;
            font-weight: 500;
        }

        #modalAgreed .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        #modalAgreed .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        #modalAgreed .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        #modalAgreed .btn-secondary:hover {
            background-color: #5c636a;
            border-color: #565e64;
        }
    </style>
{% endblock stylesheets %}
{% block title %}Resumen Usuario{% endblock title %}
{% block breadcrumb %}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h5 class="m-b-10">
                            <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                            Resumen Usuario
                        </h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                        </li>
                        <!-- <li class="breadcrumb-item">
                            <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                        </li> -->
                        <li class="breadcrumb-item">
                            {% with seller=object.seller %}
                                <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name.capitalize }} </a>
                            {% endwith %}
                        </li>
                        <li class="breadcrumb-item">
                            {% with seller=object.seller %}
                                <a href="{% url 'app_documents:presented_model_pending_list' seller.shortname %}">Modelos</a>
                            {% endwith %}
                        </li>
                        <li class="breadcrumb-item">
                            <a href=".">
                                {% if object.model is not None %}
                                    {{object.model}}
                                {% endif %}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
{% endblock breadcrumb %}
{% block content %}
    <div class="mt-3">
        {% with seller=object.seller %}
            <div class="card-header"><h2>{{ seller.name.capitalize }}</h2></div>
            <div class="card-body" style="min-height: 68vh;">
                <div class="row d-flex">
                    <div class="col-12 m-2">
                        {% if object.model.pk == 'IT-LIPE' and object.period.code == 'Q4' %}
                            <div class="d-flex justify-content-center mb-3 alert alert-warning " style="font-size:larger;">
                                <b>**Este modelo es de carácter informativo ya que el IVA del cuarto trimestre se liquida al presentar el IVA anual (que se envía en marzo). 
                                Es por eso que la casilla VP14 no contiene datos. 
                              
                                 {% if object.result.code == 'credit' %}
                                <span style="color:black;">El resultado de esta declaración y el IVA que tendrás que compensar es de {{vp14}}** </span></b>     
                                {% else %}
                                <span style="color:black;">El resultado de esta declaración y el IVA que tendrás que abonar junto a la declaración anual es de {{vp14}}** </span></b>
                                {% endif %}

                            </div> 
                        {% endif %}
                        <!-- Modal Trigger-->
                        {% if 'ES-' in object.model.pk %}
                            {% if object.status.code == 'pending' or object.status.code == 'disagreed' %}
                                <div class="d-flex justify-content-center">
                                    {% if 'ES-' in object.model.pk and object.status.code in 'pending,disagreed' %}
                                        {% if object.model.pk in fractionated_models %}
                                            {% if seller.is_contracted_accounting_today and object.amount < 50000 and object.result.code == 'deposit' %}
                                                {% if seller.legal_entity == 'self-employed' or seller.legal_entity == 'sl' %}
                                                    <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#choiceFraction"
                                                            class="btn btn-primary"
                                                            onclick="model_nrc()"><b>Conforme</b></button>
                                                {% else %}
                                                    <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#modalAgreed"
                                                            class="btn btn-primary"
                                                            onclick="model_nrc()"><b>Conforme</b></button>
                                                {% endif %}
                                            {% else %}
                                                {% if object.model.pk == 'ES-303' and object.result.code == 'compensate' and object.period.code == 'Q4' %}
                                                    <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#modalAgreed303Q4"
                                                            class="btn btn-primary"
                                                           ><b>Conforme</b></button>
                                                {% elif direct_debit_conditions %}
                                                    <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#choiceFraction"
                                                            class="btn btn-primary"
                                                            onclick="model_nrc()"><b>Conforme</b></button>
                                                {% else %}
                                                    <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#modalAgreed"
                                                            class="btn btn-primary"
                                                            onclick="model_nrc()"><b>Conforme</b></button>
                                                {% endif %}
                                            {% endif %}
                                        {% else %}
                                            {% if object.model.pk == 'ES-111' and object.send_to_external_manager %}
                                            <button type="button" data-bs-toggle="modal"
                                                    data-bs-target="#modalAgreed"
                                                    class="btn btn-primary"><b>Conforme</b></button>
                                            {% elif direct_debit_conditions %}
                                                    <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#choiceFraction"
                                                            class="btn btn-primary"
                                                            onclick="model_nrc()"><b>Conforme</b></button>
                                            {% else %}
                                            <button type="button" data-bs-toggle="modal"
                                                    data-bs-target="#modalAgreed"
                                                    class="btn btn-primary"
                                                    onclick="model_nrc()"><b>Conforme</b></button>
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}
                                    {% if 'ES-' in object.model.pk and object.status.code == 'pending' %}
                                        <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                                data-bs-target="#notAgreed"><b>No Conforme</b></button>
                                    {% else %}
                                        <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                                data-bs-target="#notAgreed"
                                                disabled><b>No Conforme</b></button>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% elif 'IT-' in object.model.pk %}
                            {% if object.status.code == 'pending' or object.status.code == 'disagreed' %}
                                <div class="d-flex justify-content-center">
                                    {% if object.model.pk == 'IT-LIPE' or object.model.pk == 'IT-ACCONTO' or object.model.pk == 'IT-VATANNUALE' %}
                                        <button type="button" data-bs-toggle="modal"
                                                            data-bs-target="#modalAgreed"
                                                            class="btn btn-primary"
                                                            ><b>Aprobar</b></button>
                                        <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                                data-bs-target="#notAgreed">
                                                <b>Denegar</b></button>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% elif 'US-' in object.model.pk %}
                            {% if object.status.code == 'pending' or object.status.code == 'disagreed' %}
                                <div class="d-flex justify-content-center">
                                    <button 
                                        type="button" data-bs-toggle="modal"
                                        data-bs-target="#agreeUSModel"
                                        class="btn btn-primary">
                                        <b>Aprobar</b>
                                    </button>
                                    <button 
                                        type="button" data-bs-toggle="modal"
                                        data-bs-target="#instructionUSModel"
                                        class="btn btn-secondary">
                                        <b>Instrucciones</b>
                                    </button>
                                    <button
                                        type="button" class="btn btn-danger" data-bs-toggle="modal"
                                        data-bs-target="#notAgreed">
                                        <b>Denegar</b>
                                    </button>
                                </div>
                                <script>
                                    $(window).on('load', function() {
                                        $('#instructionUSModel').modal('show');
                                    });
                                </script>
                            {% endif %}
                        {% elif 'GB-' in object.model.pk %}
                            {% if object.status.code == 'pending' or object.status.code == 'disagreed' %}
                                <div class="d-flex justify-content-center">
                                    <button
                                        type="button" data-bs-toggle="modal"
                                        data-bs-target="#modalAgreed"
                                        class="btn btn-primary">
                                        <b>Conforme</b>
                                    </button>
                                    <button
                                        type="button" class="btn btn-danger" data-bs-toggle="modal"
                                        data-bs-target="#notAgreed">
                                        <b>No Conforme</b>
                                    </button>
                                </div>
                            {% endif %}
                            <div class="col-12">
                                <embed 
                                    style="width: 100%; height: 100vh;" 
                                    type="application/pdf" 
                                    class="pdf border" 
                                    src="{{ object.file.url }}">
                                </embed>
                            </div>
                        {% elif 'DE-' in object.model.pk %}
                            {% if object.status.code == 'pending'  %}
                            <div class="d-flex justify-content-center">
                                <!-- Botón "Conforme" que abre el modal para firmar -->
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#signModal">
                                    <b>Conforme</b>
                                </button>
                            
                                <!-- Botón "No Conforme" que pasa el estado del documento -->
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#notAgreed">
                                    <b>No Conforme</b>
                                </button>
                            </div>
                            {% endif %}
                            {% if object.is_signed %}
                                <div class="row">
                                    <!-- Documento Original -->
                                    <div class="col-6">
                                        <h5>Documento Original</h5>
                                        {% if object.original_file %}
                                            <embed style="width: 100%; height: 100vh;;" 
                                                type="application/pdf" 
                                                class="pdf border" 
                                                src="{{ object.original_file.url }}"></embed>
                                        {% else %}
                                            <p>No hay un documento original disponible.</p>
                                        {% endif %}
                                    </div>
                                    <!-- Documento Firmado -->
                                    <div class="col-6">
                                        <h5>Documento Firmado</h5>
                                        <embed style="width: 100%; height: 100vh;;" 
                                            type="application/pdf" 
                                            class="pdf border" 
                                            src="{{ object.file.url }}"></embed>
                                    </div>
                                </div>

                            {% else %}
                                <div class="col-12">
                                    <h5>Documento Original</h5>
                                    {% if object.file %}
                                        <embed style="width: 100%; height: 100vh;;" 
                                            type="application/pdf" 
                                            class="pdf border" 
                                            src="{{ object.file.url }}"></embed>
                                    {% else %}
                                        <p>No hay un documento original disponible.</p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                    <div class="col-12 m-2">
                        <div class="row">
                            <!-- alerta visualizacion no chrome -->
                           {% if form.errors %}
                                {% for field, errors in form.errors.items %}
                                    {% if field == 'is_direct_debit' %}
                                        {% for error in errors %}
                                            <div id="error-div" class="alert alert-danger"><span style = "font-size: x-large;">{{ error }}</span></div>
                                        {% endfor %}
                                    {% endif %}
                                    {% endfor %}
                            {% endif %}


                            <div id="browser-warning" class="alert alert-warning" style="display:none;"></div>
                            {% if object.model.pk == 'ES-111' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-115' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-130' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                                <div class=" col-4">
                                    <div class="table">
                                        <div class="row">
                                            <div class="col-2">Descripci&oacute;n</div>
                                            <div class="col">
                                                <div>
                                                    <div>El Modelo 130 recoge los ingresos y gastos del trimestre,
                                                        acumulados a los
                                                        ingresos y gastos de
                                                        trimestres anteriores (siempre del mismo a&ntilde;o). Sirve para
                                                        calcular el
                                                        beneficio. El 20% de
                                                        ese
                                                        beneficio debe ser ingresado a Hacienda como pago fraccionado
                                                        del IRPF.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 1</div>
                                            <div class="col">
                                                <div>
                                                    <div>Se recogen todos los ingresos, da igual su naturaleza. En caso
                                                        de los
                                                        aut&oacute;nomos en RE
                                                        (Recargo
                                                        de Equivalencia) el IVA recaudado se suma como mayor ingreso.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 2</div>
                                            <div class="col">
                                                <div>
                                                    <div>Se recogen todos los gastos, da igual su naturaleza. En caso de
                                                        los
                                                        aut&oacute;nomos en RE (Recargo
                                                        de
                                                        Equivalencia) el IVA soportado (m&aacute;s el recargo) se suma
                                                        como mayor
                                                        gasto.
                                                        Tambi&eacute;n, y
                                                        solo
                                                        para los aut&oacute;nomos en RE, se incluye el importe del Mod.
                                                        309. En los
                                                        casos en el que la
                                                        casilla 3
                                                        es positiva, o sea, si hay beneficio, la norma permite deducirse
                                                        un 7%
                                                        adicional
                                                        como "gastos de
                                                        dificil
                                                        justificaci&oacute;n" que tambi&eacute;n van incluidos en esta
                                                        casilla.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 3</div>
                                            <div class="col">
                                                <div>
                                                    <div>Es el beneficio acumulado, el resultado de restar el importe de
                                                        la casilla
                                                        02
                                                        al importe de la
                                                        casilla
                                                        03.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 4</div>
                                            <div class="col">
                                                <div>
                                                    <div>Si la casilla 03 tiene un importe positivo aqu&iacute; se
                                                        introduce el 20%
                                                        de
                                                        dicho importe.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 5</div>
                                            <div class="col">
                                                <div>
                                                    <div>Sin uso para el 1T. En los siguientes la suma de los importes
                                                        positivos de
                                                        las
                                                        casillas 07 menos
                                                        los
                                                        importes de las casillas 16 de los trimestres anteriores.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 6</div>
                                            <div class="col">
                                                <div>
                                                    <div>Si se han hecho ventas de meercaderias o prestaci&oacute;n de
                                                        servicios con
                                                        retenci&oacute;n de
                                                        IRPF,
                                                        aqu&iacute; se pone el total anual.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 7</div>
                                            <div class="col">
                                                <div>
                                                    <div>Resta de la casilla 04 menos la casilla 05 menos la casilla
                                                        06
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 8</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 9</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 10</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 11</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 12</div>
                                            <div class="col">
                                                <div>
                                                    <div>Suma de la casilla 07 m&aacute;s la casilla 11</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 13</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 14</div>
                                            <div class="col">
                                                <div>
                                                    <div>Resta de la casilla 12 menos la 13.</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 15</div>
                                            <div class="col">
                                                <div>
                                                    <div>No aplica para el primer trimestre. Si la casilla 14 es
                                                        positiva, entonces
                                                        aqu&iacute; se restan
                                                        las
                                                        casillas 19 negativas de trimestres anteriores.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 16</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 17</div>
                                            <div class="col">
                                                <div>
                                                    <div>Resta de las casillas 14 menos la casilla 15 menos la casilla
                                                        16
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 18</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-2">CASILLA 19</div>
                                            <div class="col">
                                                <div>
                                                    <div>Resultado de la declaraci&oacute;n. Si es positiva significa
                                                        que esa
                                                        cantidad
                                                        es la que hay que
                                                        ingresar a Hacienda como pago fraccionado del IRPF
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-180' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-184' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-190' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-202' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-303' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if model_id == 'excel303' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-309' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-347' %}
                                <div class="col">
                                    <iframe src="{{ object.get_file_url }}" width="100%" height="500"></iframe>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-349' %}
                                <div class="col">
                                    <iframe src="{{ object.get_file_url }}" width="100%" height="500"></iframe>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-369' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'ES-390' %}
                                <div class="col">
                                    <embed style="width: 100%; height: 100vh;" type="application/pdf" class="pdf border"
                                           src="{{ object.get_file_url }}"></embed>
                                </div>
                            {% endif %}
                            {% if object.model.pk == 'IT-LIPE' %}
                            <div class="col">
                                <embed
                                    style="width: 100%; height: 100vh;"
                                    type="application/pdf" class="pdf border"
                                    src="{{ object.get_file_url }}">
                                </embed>
                            </div>
                            {% endif %}
                            {% if object.model.pk == 'IT-ACCONTO' %}
                            <div class="col">
                                <embed
                                    style="width: 100%; height: 100vh;"
                                    type="application/pdf" class="pdf border"
                                    src="{{ object.get_file_url }}">
                                </embed>
                            </div>
                            {% endif %}
                            {% if object.model.pk == 'IT-VATANNUALE' %}
                            <div class="col">
                                <embed
                                    style="width: 100%; height: 100vh;"
                                    type="application/pdf" class="pdf border"
                                    src="{{ object.get_file_url }}">
                                </embed>
                            </div>
                            {% endif %}
                            {% if object.model.pk == 'US-5472' or object.model.pk == 'US-7004'  or object.model.pk == 'US-BE15'  %}
                            <div class="col">
                                <embed
                                    style="width: 100%; height: 100vh;"
                                    type="application/pdf" class="pdf border"
                                    src="{{ object.get_file_url }}">
                                </embed>
                            </div>
                            {% endif %}
                            {% if 'ES-' not in object.model.pk and 'IT-' not in object.model.pk and object.is_signed is None%}
                                <div class="col">
                                    <embed 
                                        style="width: 100%; height: 100vh;"
                                        type="application/pdf" class="pdf border"
                                        src="{{ object.get_file_url }}">
                                    </embed>
                                </div>
                            {% endif %}
                        </div>
                        <br>
                        {% if object.status.code == "presented" and object.model.pk == 'ES-303' and object.is_paid == False and seller.is_contracted_accounting_today != True %}
                            <form method="post" enctype="multipart/form-data"
                                  action=""
                                  id="form-uploadreceipt"
                                  onsubmit="return validateFile('receipt_file')">
                                {% csrf_token %}
                                <div class="form-group text-center mx-auto">
                                    <label class="mb-3" for="receipt_file"><b>Adjuntar recibo: </b></label>
                                    <input type="file" name="receipt_file" id="receipt_file"
                                           class=" form-control w-25 text-center mx-auto mb-3"/>
                                    <div id="file_formats_footer" class="text-muted">*Formatos admitidos: PDF, JPG, JPEG o PNG.</div><br>
                                    <input type="hidden" name="is_paid" id="is_paid" value="True"/>
                                    <input type="hidden" class="form-control" id="id_status" name="status" value="presented">
                                    <input type="submit" id="submit-button" value="Continuar" class="btn btn-primary">
                                </div>
                            </form>
                        {% endif %}
                        <!-- Modal Disagreed -->
                        <div class="modal fade" id="notAgreed" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
                             aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                                <div class="modal-content check-booking">
                                    <div class="modal-body">
                                        <p>¿Deseas agendar una llamada telefónica para tratar el motivo de por qué NO estás conforme?</p>
                                    </div>
                                    <div class="modal-footer d-flex justify-content-center">
                                        <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                                                data-bs-target="#modal"
                                                aria-label="Close">Cancelar
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-bs-toggle="modal"
                                                data-bs-target="#notBooking"
                                                id="noAppointmentBtn">No
                                        </button>
                                        {% if can_book_disagreement %}
                                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#booking" id="confirmAppointmentBtn">Sí</button>
                                        {% else %}
                                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#offPeriod" id="confirmAppointmentBtn">Sí</button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modal disagreed and off period -->
                        <div class="modal fade" id="offPeriod" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header bg-dark">
                                        <h5 class="modal-title text-white">Plazo finalizado</h5>
                                        <button type="button" class="btn-close btn-close-white" id="modalLabel" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <!-- show an alert message -->
                                        <div class="p-3">
                                            <div class="alert alert-warning" role="alert">
                                                <h4 class="alert-heading">¡Lo sentimos!</h4>
                                                <p>El plazo de presentaciones ha finalizado.</p>
                                                <p>Escríbenos a <a href="mailto:{{ support_email }}" class="text_muted">{{ support_email }}</a>  si tienes cualquier gestión urgente.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#notAgreed" aria-label="Close">Atras</button>
                                        <button type="button" class="btn btn-danger" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">Cancelar</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modal Disagreed and Book Appointment-->
                        {% include 'documents/include/booking_model_disagree.html' with hidden=False %}
                        <!-- Modal Disagreed and Book Appointment-->
                        
                        <!-- Modal Disagreed and not Book Appointment-->
                        <div class="modal fade" id="notBooking" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
                             aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                                <div class="modal-content">
                                    <form method="post" enctype="multipart/form-data" action="" onsubmit="return validateFile('nrc_file')">
                                        <div class="modal-header bg-dark">
                                            <h5 class="modal-title text-white">Motivo de No Conforme</h5>
                                            <button type="button" class="btn-close btn-close-white" id="modalLabel" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="col form-group form-check p-3">
                                                {% csrf_token %}
                                                <label class="form-label">
                                                    <b>Por favor escriba el motivo de disconformidad:</b>
                                                </label>
                                                <textarea id="commentNotBooking" name="comment" class="form-control" rows="3"></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" id="cancelNotBookingBtn" class="btn btn-light"
                                                    data-bs-dismiss="modal"
                                                    data-bs-target="#modal" aria-label="Close">Atras
                                            </button>
                                            <button type="submit" id="submitButton" class="btn btn-success">
                                                <b>Enviar</b></button>
                                            <input type="hidden" class="form-control" id="id_status" name="status" value="disagreed">
                                            <input type="hidden" class="form-control" id="is_booking" name="is_booking" value="notBooking">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!-- Modal Disagreed and not Book Appointment-->

                        <!-- Modal Choice-->
                        <div class="modal fade" id="choiceFraction" tabindex="-1" role="dialog"
                             aria-labelledby="modalLabel"
                             aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                                <div class="modal-content check-booking">
                                    <div class="modal-header bg-dark">
                                        <h5 class="modal-title text-white">Método de pago</h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body" style = "padding: 2rem;">
                                        <p>La presente declaración del <b> {{object.model.description}} </b> tiene un resultado a ingresar de <b>{{ object.amount }} €</b></p>
                                        <p>Para aprobar el borrador y para que podamos proceder a su presentación en la AEAT necesitamos que nos proporciones un método de pago.</p>
                                        <p>Elige en que forma deseas efectuar el pago:</p>
                                        <div class="d-flex justify-content-center" style= "padding-bottom: 34px; padding-top: 19px;">
                                            <button type="button" class="btn btn-secondary" data-bs-toggle="modal"
                                                    data-bs-target="#modalAgreed"
                                                    onclick="noFraction()"
                                                    id="noFraction">Pagar con NRC
                                            </button>
                                            {% if fractioned_payment_conditions %}
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                                        data-bs-target="#modalAgreed"
                                                        onclick="confirmFraction()"
                                                        id="confirmFraction">Pagar fraccionado
                                                </button>
                                            {% endif %}
                                            {% if direct_debit_conditions %}
                                                <button type="button" class="btn btn-info" data-bs-toggle="modal"
                                                        data-bs-target="#modalAgreed"
                                                        onclick="confirmDirectDebit()"
                                                        id="confirmFraction">Domiciliación Bancaria
                                                </button>
                                            {% endif %}
                                        </div>
                                        <div>
                                            
                                                <li><b>NRC</b>: paga directamente desde tu proveedor bancario.</li>
                                                {% if fractioned_payment_conditions %}
                                                    <li><span><b>Fraccionado</b>: fija tus pagos en varios plazos</span>
                                                    <span role="button" class="d-inline-block" data-bs-toggle="tooltip" data-bs-placement="top"
                                                        title="Te recomendamos que si no es estrictamente necesario, no pidas el pago fraccionado"
                                                        tabindex="0"
                                                        >
                                                        <i class="feather icon-info"></i>.
                                                    </span>
                                                    </li>
                                                {% endif %}
                                                {% if direct_debit_conditions %}
                                                    <li><b>Domiciliado</b>: solicita que el cobro se realice directamente a tu cuenta.</li>
                                                {% endif %}
                                            
                                        </div>
                                    </div>
                                    <div class="modal-footer d-flex justify-content-center">
                                        <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                                                data-bs-target="#modal"
                                                aria-label="Close">Cancelar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Modal Choice-->

                        <!-- Modal Agreed-->
                        <div class="modal fade" id="modalAgreed" tabindex="-1" role="dialog"
                             aria-labelledby="modalLabel"
                             aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                                <div class="modal-content">
                                    <form method="post" enctype="multipart/form-data" action="">
                                        <div class="modal-header bg-dark">
                                            <h5 class="modal-title text-white">Conforme</h5>
                                            <button type="button" class="btn-close btn-close-white" id="modalLabel" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="col form-group form-check p-3">
                                                {% csrf_token %}
                                                {% if 'ES-' in object.model.pk %}
                                                    {% if object.model.pk == 'ES-349' %}
                                                        <h4 class="mb-4 text-center"><b>Pulse en enviar si está de acuerdo</b></h4>
                                                        <div class="alert alert-primary text-center" role="alert" style="margin-bottom: unset;">
                                                            <p><b>Este modelo es meramente informativo</b></p>
                                                        </div>
                                                    {% elif object.model.pk == 'ES-111' and object.send_to_external_manager %}
                                                        <h4 class="mb-4 text-center"><b>Pulse en enviar si está de acuerdo</b></h4>
                                                    {% else %}
                                                        {% if object.result.code == 'deposit' and object.amount > 0 %}
                                                            <label class="form-label"><p><b>Por favor introduzca los siguientes datos:</b></p></label>
                                                            <br>
                                                            {% if object.model.pk in fractionated_models %}
                                                                {% if seller.is_contracted_accounting_today and object.amount < 50000 %}
                                                                    {% if seller.legal_entity == 'self-employed' or seller.legal_entity == 'sl' %}
                                                                        {% include 'documents/include/include_nrc.html' with hidden=True %}
                                                                        {% include 'documents/include/include_iban_fraction.html' with hidden=True %}
                                                                        {% include 'documents/include/inlude_iban_direct_debit.html' with hidden=True %}
                                                                    {% else %}
                                                                        {% if seller.legal_entity == 'llc' or seller.legal_entity == 'other' or seller.legal_entity == 'self-employed-outside' %}
                                                                            {% include 'documents/include/include_iban_swift.html' with hidden=False %}
                                                                        {% else %}
                                                                            {% include 'documents/include/include_nrc.html' with hidden=False %}
                                                                            {% include 'documents/include/inlude_iban_direct_debit.html' with hidden=True %}
                                                                        {% endif %}
                                                                    {% endif %}
                                                                {% else %}
                                                                    {% if seller.legal_entity == 'llc' or seller.legal_entity == 'other' or seller.legal_entity == 'self-employed-outside' %}
                                                                        {% include 'documents/include/include_iban_swift.html' with hidden=False %}
                                                                    {% else %}
                                                                        {% include 'documents/include/include_nrc.html' with hidden=False %}
                                                                        {% include 'documents/include/inlude_iban_direct_debit.html' with hidden=True %}
                                                                    {% endif %}
                                                                {% endif %}
                                                            {% else %}
                                                                {% include 'documents/include/include_nrc.html' with hidden=False %}
                                                                {% include 'documents/include/inlude_iban_direct_debit.html' with hidden=True %}
                                                            {% endif %}
                                                        {% else %}
                                                            <h4 class="mt-4 text-center"><b>Pulse en enviar si está de acuerdo</b></h4>
                                                        {% endif %}
                                                    {% endif %}
                                                {% elif 'IT-' in object.model.pk %}
                                                    {% if object.model.pk == 'IT-LIPE' or object.model.pk == 'IT-ACCONTO' or object.model.pk == 'IT-VATANNUALE' %}
                                                        {% if object.result.code == 'credit' or object.result.code == 'result0' and invoice > 0 %}
                                                            <h4 class="mt-4 text-center"><b>Pulse en enviar si está de acuerdo</b></h4>
                                                        {% elif  object.result.code == 'result0' and invoice == 0 %}
                                                            <div class="alert alert-warning" role="alert" style="margin-bottom: unset;">
                                                                <h4 class="alert-heading"><b>¡Atención!</b></h4>
                                                                <p>Dado que no hemos recibido información fiscal de tu parte, para aprobar el modelo deberás firmarnos un certificado. 
                                                                    Haciendo clic en <b>firmar</b> te conectaremos con nuestro servidor de firma electrónica.
                                                                </p>
                                                                <input type="hidden" class="form-control" id="is_signing" name="is_signing" value="True">
                                                            </div>
                                                        {% elif  object.period.code != 'Q4' and object.result.code == 'deposit'  or object.model.pk == 'IT-ACCONTO' %}
                                                            {% include 'documents/include/include_iban_it.html' with hidden=False %}
                                                        {% elif  object.result.code == 'deposit' and object.model.pk == 'IT-VATANNUALE' %}
                                                            {% include 'documents/include/include_iban_it.html' with hidden=False %}
                                                        {% else %}
                                                            <h4 class="mt-4 text-center"><b>Pulse en enviar si está de acuerdo</b></h4>
                                                        {% endif %} 
                                                    {% endif %}
                                                {% elif 'US-' in object.model.pk or 'GB-' in object.model.pk %}
                                                    <h4 class="mt-4 text-center"><b>Pulse en enviar si está de acuerdo</b></h4>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="modal-footer d-flex justify-content-center">
                                            <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modalAgreed" aria-label="Close">Cancelar</button>
                                            {% if object.model.pk == 'IT-LIPE' or object.model.pk == 'IT-ACCONTO' and object.result.code == 'result0' and invoice == 0 %}
                                            <button type="submit" id="submitButton" class="btn btn-success"><b>Firmar</b></button>
                                            {% else %}
                                            <button type="submit" id="submitButton" class="btn btn-success" onclick="onclicksubmit(this)"><b>Enviar</b></button>
                                            {% endif %}
                                            <input type="hidden" class="form-control" id="id_status" name="status" value="agreed">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!-- Modal Agreed 303-Q4 Compensate-->
                        {% include 'documents/include/303_Q4_compensate_agree_modal.html'  with hidden=False%}
                    </div>

                    <!-- Modal Agree US Model -->
                    {% include 'documents/include/modal_agree_model_5472.html'  with hidden=False%}
                    <!-- Modal instructions US Model -->
                    {% include 'documents/include/instructions_model_5472.html'  with hidden=False%}

                    <!-- Modal Signature DE (Alemania) -->
                    {% include 'documents/include/signatures/signature_de.html' %}
                </div>

            </div>
        {% endwith %}
    </div>
{% endblock content %}
{% block javascripts %}
    <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
    <script>
        const confirmFraction = () => {
            document.getElementById('div_nrc').style.display = 'none';
            document.getElementById('id_nrc').disabled = true;
            document.getElementById('div_iban').style.display = 'block';
            document.getElementById('id_iban').disabled = false;
            document.getElementById('id_fraction').disabled = false;
            document.getElementById('radiopay1').disabled = false;
            document.getElementById('radiopay2').disabled = false;
            document.getElementById('div_iban_direct_debit') ? document.getElementById('div_iban_direct_debit').style.display = 'none' : null;
            document.getElementById('id_iban_direct_debit') ? document.getElementById('id_iban_direct_debit').disabled = true : null;
            document.getElementById('is_direct_debit') ? document.getElementById('is_direct_debit').checked = false : null;
            console.log('confirmFraction');
        }

        const noFraction = () => {
            document.getElementById('div_iban') ? document.getElementById('div_iban').style.display = 'none' : null;
            document.getElementById('id_iban') ? document.getElementById('id_iban').disabled = true : null;
            document.getElementById('id_fraction') ? document.getElementById('id_fraction').disabled = true : null;
            document.getElementById('radiopay1') ? document.getElementById('radiopay1').disabled = true : null;
            document.getElementById('radiopay2') ? document.getElementById('radiopay2').disabled = true : null;
            document.getElementById('div_nrc').style.display = 'block';
            document.getElementById('id_nrc').disabled = false;
            document.getElementById('div_iban_direct_debit') ? document.getElementById('div_iban_direct_debit').style.display = 'none' : null;
            document.getElementById('id_iban_direct_debit') ? document.getElementById('id_iban_direct_debit').disabled = true : null;
            document.getElementById('is_direct_debit') ? document.getElementById('is_direct_debit').checked = false : null;
            console.log('noFraction');
        }

        const confirmDirectDebit = () =>{
            document.getElementById('div_iban') ? document.getElementById('div_iban').style.display = 'none' : null;
            document.getElementById('id_iban') ? document.getElementById('id_iban').disabled = true : null;
            document.getElementById('id_fraction') ? document.getElementById('id_fraction').disabled = true : null;
            document.getElementById('div_nrc').style.display = 'none';
            document.getElementById('id_nrc').disabled = true;
            document.getElementById('div_iban_direct_debit').style.display = 'block';
            document.getElementById('id_iban_direct_debit').disabled = false;
            document.getElementById('is_direct_debit').checked = true;
            console.log('confirmDirectDebit');
        }

        const model_nrc = () => {
            let objectPkValue = "{{ object.model.pk }}";
            let modelPrefix = objectPkValue.substring(3);

            let nrc = document.getElementById("id_nrc");
            if (nrc != null) {
                let pattern = new RegExp("^" + modelPrefix + "[A-Za-z0-9]{19}$");
                nrc.pattern = pattern.source;
                nrc.title = "Debe tener exactamente 22 caracteres alfanuméricos ";
            }
        }

        const get_swift_from_iban = (elem) => {
            const iban = elem.value;
            if (iban.length > 0) {
                const url = "{% url 'app_banks:bank_get_iban_data' 'XXYYZZ' %}".replace('XXYYZZ', iban)
                $.ajax({
                    url: url,
                    type: "GET",
                    data: {'iban': iban},
                    success: function (data) {
                        console.log("data: ", data);
                        const status = data['status'];
                        data = data['data'];
                        const feedback = document.getElementById('swift_feedback')
                        const form = elem.form;
                        if (status == 200 && data.bic != null) {
                            const swift_elem = document.getElementById('id_swift')
                            if (swift_elem != null && swift_elem != undefined) {
                                swift_elem.value = data.bic;
                                if (feedback != null && feedback != undefined) {
                                    feedback.innerHTML = '';
                                    feedback.classList.remove('invalid-feedback');
                                    feedback.classList.add('valid-feedback');
                                }
                            } else if(swift_elem == null || swift_elem == undefined || swift_elem.value.length == 0) {
                                if (feedback != null && feedback != undefined) {
                                    feedback.innerHTML = 'El Swift no es válido';
                                    feedback.classList.remove('valid-feedback');
                                    feedback.classList.add('invalid-feedback');
                                }
                            }
                        }
                    }
                });
            }
        };

        const check_iban = (elem) => {
            const iban = elem.value;
            if (iban.length > 0) {
                const url = "{% url 'app_banks:bank_get_iban_data' 'XXYYZZ' %}".replace('XXYYZZ', iban)
                $.ajax({
                    url: url,
                    type: "GET",
                    data: {'iban': iban},
                    success: function (data) {
                        console.log("data: ", data);
                        const status = data['status'];
                        const error = data['error'];
                        console.log("error: ", error);
                        data = data['data'];
                        const feedback = document.getElementById('iban_feedback')
                        const form = elem.form;
                        if (status == 200 && data.is_valid == true) {
                            feedback.innerHTML = '';
                            feedback.classList.remove('invalid-feedback');
                            feedback.classList.add('valid-feedback');
                        } else if (status == 200 && error != null && error.length > 0) {
                            feedback.innerHTML = error;
                            feedback.classList.remove('valid-feedback');
                            feedback.classList.add('invalid-feedback');
                        } else {
                            feedback.innerHTML = 'El IBAN no es válido';
                            feedback.classList.remove('valid-feedback');
                            feedback.classList.add('invalid-feedback');
                        }
                    }
                });
            }
        }

        const onclicksubmit = (elem) => {
            console.log('onclicksubmit');
            const form = elem.form;
            let is_valid = validateFile('nrc_file');
            if (is_valid) {
                const form = elem.form;
                form.classList.add("was-validated");
            }else{
                event.preventDefault(); 
            }
        }

        function validateFile(file) {
            const Toast = Swal.mixin({
                toast: true,
                customClass: {
                    popup: 'over-modal'
                },
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                    const toastContainer = toast.parentNode;
                    toastContainer.classList.add('over-modal');
                }
            });

            let fileInput = document.getElementById(file);
            let filePath = fileInput.value;
            let allowedExtensions = /(\.pdf|\.jpg|\.jpeg|\.png)$/i;
            if (file == 'nrc_file'){
                if(filePath != ''){
                    if (!allowedExtensions.exec(filePath)) {
                        Toast.fire({
                            icon: 'error',
                            title: 'Los formatos aceptados son PDF, JPG, JPEG o PNG.',
                        })
                        return false;
                    }else{
                        return true;
                    }
                }else{
                    return true;
                }
            }else{
                if (!allowedExtensions.exec(filePath)) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Los formatos aceptados son PDF, JPG, JPEG o PNG.',
                    })
                    return false;
                } else {
                    return true;
                }
            }
        }

        const cancelBookingBtn = document.getElementById('cancelBookingBtn');
        const cancelNotBookingBtn = document.getElementById('cancelNotBookingBtn');

        cancelNotBookingBtn.addEventListener('click', function () {
            $('#notBooking').modal('hide');
            $('#notAgreed').modal('show');
        });
        cancelBookingBtn.addEventListener('click', function () {
            $('#notBooking').modal('hide');
            $('#notAgreed').modal('show');
        });

        $('#notBooking').on('show.bs.modal', function () {
            $('#commentNotBooking').val('');
        });

        $('#booking').on('show.bs.modal', function () {
            $('#commentBooking').val('');
        });
        
        function openInstructionModal () {
            $('#instructionUSModel').modal('show');
        }

        function openAgreeModal () {
            $('#instructionUSModel').modal('hide');
            $('#agreeUSModel').modal('show');
        }

        // Variable de depuración
        const debug = true;

        function detectBrowser() {
            let userAgent = navigator.userAgent.toLowerCase();
            debug && console.log('User Agent: ' + userAgent); // Log para verificar el user agent
            
            if (userAgent.indexOf('edg') > -1) {
                return 'edge';
            } else if (userAgent.indexOf('opr') > -1) {
                return 'opera';
            } else if (userAgent.indexOf('chrome') > -1 && userAgent.indexOf('safari') > -1) {
                return 'chrome';
            } else if (userAgent.indexOf('firefox') > -1) {
                return 'firefox';
            } else if (userAgent.indexOf('safari') > -1 && userAgent.indexOf('chrome') === -1) {
                return 'safari';
            } else if (userAgent.indexOf('msie') > -1 || userAgent.indexOf('trident') > -1) {
                return 'ie';
            } else {
                return 'other';
            }
        }
        
        // Detectar el navegador y mostrar el mensaje si no es Chrome
        const browser = detectBrowser();
        debug && console.log('Detected Browser: ' + browser); // Log para verificar el navegador detectado
        const warningMessage = document.getElementById('browser-warning');
        if (warningMessage) {
            if (browser !== 'chrome') {
            warningMessage.textContent = `Está usando el navegador ${browser}. Podría tener problemas en la visualización. Sugerimos que use Chrome.`;
            warningMessage.style.display = 'block';
            } else {
            warningMessage.style.display = 'none';
            }
        }

    </script>
{% endblock javascripts %}
