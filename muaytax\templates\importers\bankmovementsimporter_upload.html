{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
    <!-- Limit Characters in Table Span -->
    <style>
      #list-table td span {
        display:inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50vw;
      }
      .table-head {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            z-index: 1;
      }
    </style>
{% endblock stylesheets %}

{% block title %}
  Cargador Movimientos Bancarios
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Bancos</h5>
          </div>
          <ul class="breadcrumb">
            {% if user.role == 'manager' %}
              <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:list' %}">Vendedores</a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_banks:bank_list' seller.shortname  %}">Bancos</a>
              </li>
              <li class="breadcrumb-item">
                <a href=".">Cargador Movimientos Bancarios</a>
              </li>
            {% else %}
              <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_banks:bank_list' seller.shortname  %}">Bancos</a>
              </li>
              <li class="breadcrumb-item">
                <a href=".">Cargador Movimientos Bancarios</a>
              </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <!-- Cargar Documentos | START  -->
    <div class="col-12 mb-3" id="uploads">
      <div class="card my-0">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>Cargador Automático de Movimientos Bancarios</h5>
          </div>
        </div>
        <div class="card-body border">

            <form method="post" enctype="multipart/form-data" action="{% url 'app_banks:bank_movements_create' seller.shortname bank.id %}"  id ="form-uploadcsb43" style="text-align:center;">
              {% csrf_token %}
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />

              </div>
              <p for="file">*Solo válido formato Excel. Contactad a soporte en caso de duda.</p><br>
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />
              </div>
              <div class="fallback">
                <input
                  type="file"
                  id="id_file"
                  name="file"
                  class=" form-control w-25 text-center mx-auto"
                  accept=".xlsx, .xls, .csv"
                />
                <br><br><br>
                <input type="submit" id="submit-button" value="Procesar Documento" class="btn btn-primary">
            </form>

            <div id="spinner" style="display: none;">
              <div class="spinner-border m-5" role="status">
                <span class="sr-only">Cargando...</span>
              </div>
            </div>
        </div>
      </div>
    </div>
    <!-- Cargar Documentos | END  -->
    <!-- Cargar Documentos Manual | START  -->
    <div class="col-12 mb-3" id="uploads">
      <div class="card my-0">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>Cargador Manual de Movimientos Bancarios</h5>
          </div>
        </div>
        <div class="card-body border">

            <form method="post" enctype="multipart/form-data" action="{% url 'app_banks:bank_movements_manual_create' seller.shortname bank.id %}"  id ="form-uploadcsb43" style="text-align:center;">
              {% csrf_token %}
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />

              </div>


              <div class="form-group row">
                <label for="start_row" class="col-3 col-form-label"><b>Fila Inicio Movimientos*</b></label>
                <div class="col-3">
                  <input type="number" class="form-control" id="start_row" name="start_row" value="2"  required >
                </div>
                <label for="start_row" class="col-3 col-form-label"><b>Fila Fin Movimientos*</b></label>
                <div class="col-3">
                  <input type="number" class="form-control" id="end_row" name="end_row" value="2"  required >
                </div>
              </div>
              <div class="form-group row">
                <label for="amount_column" class="col-3 col-form-label">Columna Importe</label>
                <div class="col-3">
                  <select class="form-select" id="amount_column" name="amount_column"  required >
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
                <label for="date_column" class="col-3 col-form-label"><b>Columna Fecha*</b></label>
                <div class="col-3">
                  <select class="form-select" id="date_column" name="date_column" required>
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
              </div>
              <div class="form-group row">
                <label for="amount_in_column" class="col-3 col-form-label">Columna Importe IN</label>
                <div class="col-3">
                  <select class="form-select" id="amount_in_column" name="amount_in_column">
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
                <label for="date_format" class="col-3 col-form-label"><b>Formato Fecha*</b></label>
                <div class="col-3">
                  <select class="form-select" id="date_format" name="date_format"  required >
                    <option value="-">-</option>
                    <option value="%d/%m/%Y">dd/mm/yyyy</option>
                    <option value="%m/%d/%Y">mm/dd/yyyy</option>
                    <option value="%Y/%m/%d">yyyy/mm/dd</option>
                    <option value="%Y/%d/%m">yyyy/dd/mm</option>
                    <option value="%d-%m-%Y">dd-mm-yyyy</option>
                    <option value="%m-%d-%Y">mm-dd-yyyy</option>
                    <option value="%Y-%m-%d">yyyy-mm-dd</option>
                    <option value="%Y-%d-%m">yyyy-dd-mm</option>
                  </select>
                </div>

              </div>
              <div class="form-group row">
                <label for="amount_out_column" class="col-3 col-form-label">Columna Importe OUT</label>
                <div class="col-3">
                  <select class="form-select" id="amount_out_column" name="amount_out_column">
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
                <label for="decimal_separator" class="col-3 col-form-label"><b>Separador Decimales*</b></label>
                <div class="col-3">
                  <select class="form-select" id="decimal_separator" name="decimal_separator"  required >
                    <option value="-">-</option>
                    <option value=".">Punto</option>
                    <option value=",">Coma</option>
                  </select>
                </div>
              </div>
              <div class="form-group row">
                <label for="observations_column" class="col-3 col-form-label">Columna Observaciones</label>
                <div class="col-3">
                  <select class="form-select" id="observations_column" name="observations_column">
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
                <label for="currency_column" class="col-3 col-form-label">Columna Divisa</label>
                <div class="col-3">
                  <select class="form-select" id="currency_column" name="currency_column"  required >
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
              </div>
              <div class="form-group row">
                <label for="concept_column" class="col-3 col-form-label"><b>Columna Concepto*</b></label>
                <div class="col-3">
                  <select class="form-select" id="concept_column" name="concept_column"  required >
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>
                <label for="currency_in_column" class="col-3 col-form-label">Columna Divisa IN</label>
                <div class="col-3">
                  <select class="form-select" id="currency_in_column" name="currency_in_column">
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>

              </div>
              <div class="form-group row">
                <label for="order" class="col-3 col-form-label" required ><b>Orden de Filas*</b></label>
                <div class="col-3">
                  <select class="form-select" id="order" name="order" required>
                    <option value="1">Ascendente</option>
                    <option value="-1">Descendente</option>
                  </select>
                </div>

                <label for="currency_out_column" class="col-3 col-form-label">Columna Divisa OUT</label>
                <div class="col-3">
                  <select class="form-select" id="currency_out_column" name="currency_out_column">
                    <option value="-">-</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G</option>
                    <option value="H">H</option>
                    <option value="I">I</option>
                    <option value="J">J</option>
                    <option value="K">K</option>
                    <option value="L">L</option>
                    <option value="M">M</option>
                    <option value="N">N</option>
                    <option value="O">O</option>
                    <option value="P">P</option>
                    <option value="Q">Q</option>
                    <option value="R">R</option>
                    <option value="S">S</option>
                    <option value="T">T</option>
                    <option value="U">U</option>
                    <option value="V">V</option>
                    <option value="W">W</option>
                    <option value="X">X</option>
                    <option value="Y">Y</option>
                    <option value="Z">Z</option>
                  </select>
                </div>

              </div>
              <div class="form-group row">
                <label for="order" class="col-3 col-form-label">¿Está el signo de la cantidad al revés?</label>
                <div class="col-3">
                  <select class="form-select" id="backward_amount" name="backward_amount">
                    <option value="-">-</option>
                    <option value="true">Sí</option>
                    <option value="false">No</option>
                  </select>
                </div>
              </div>

              <br>

              <p for="file">*Solo válido formato Excel. Contactad a soporte en caso de duda.</p><br>
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />
              </div>
              <div class="fallback">
                <input
                  type="file"
                  id="id_file"
                  name="file"
                  class=" form-control w-25 text-center mx-auto"
                  accept=".xlsx, .xls, .csv"
                />
                <br><br><br>
                <input type="submit" id="submit-button" value="Procesar Documento" class="btn btn-primary">
            </form>

            <div id="spinner" style="display: none;">
              <div class="spinner-border m-5" role="status">
                <span class="sr-only">Cargando...</span>
              </div>
            </div>
        </div>
      </div>
    </div>
    <!-- Cargar Documentos Manual | END  -->

    <br> <br> <br>

    <!-- Listado Documentos | START  -->
    <div class="col-12 mb-3" id="files">
      <div class="dt-responsive table-responsive">
        <table id="list-table" class="table nowrap">
          <thead class="table-head">
            <tr>
              <th>Nombre Fichero</th>
              <th>Modelo Documento</th>
              <th style="width:20%;">Fecha Carga</th>
              <th style="width:5%;">Estado</th>
              <th style="width:5%;">Acciones</th>
            </tr>
          </thead>
          <tbody>
            {% for object in object_list %}
            <tr>
              <td class="align-middle">
                <span>{{ object.get_file_name }} </span>
              </td>
              <td class="align-middle">
                {% if object.template is None %}
                  <span>Desconocido</span>
                {% else %}
                  <span>{{ object.template }}</span>
                {% endif %}
              </td>
              <td class="align-middle">
                <span style="display:none;">{{ object.created_at|date:"Y-m-d H:i:s" }}</span>
                <span>{{ object.created_at }}</span>
              </td>
              <td class="align-middle">
                  <!-- Oculto pero accesible desde JS -->
                  <span id="error-message" data-message="{{ object.error_message|default:'' }}"></span>
                  <span class="fs-5"data-bs-toggle="tooltip" data-bs-placement="top"
                      {% if object.error_message != None %}
                        data-bs-title="{{object.error_message}}"
                      {% endif %}
                      {% if object.error_message == None %}
                        data-bs-title="Sin información"
                      {% endif %}
                      >
                  {% if object.status.code == "processed" %}
                    <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                  {% elif object.status.code == "error" or object.status.code == "rejected" %}
                    <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                  {% elif object.status.code == "pending" %}
                    <i class="fa-regular fa-xl fa-circle-pause" style="color: #ff9500;"></i>
                  {% else %}
                    <i class="fa-regular fa-xl fa-circle-question" style="color: #ff9500;"></i>
                  {% endif %}
                  </span>
              </td>
              <td class="align-middle">
                <div>
                  {% if object.status.code == "pending" %}
                    <a class="btn btn-icon btn-info"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      data-bs-title="Procesar Fichero"
                      href="#"
                    >
                      <i class="fa-solid fa-hammer"></i>
                    </a>
                  {% endif %}

                  <a class="btn btn-icon btn-info"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    data-bs-title="Descargar Fichero"
                    href="{{object.get_file_url}}"
                    target="_blank"
                    download
                  >
                    <i class="fa-solid fa-download"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
     <!-- Listado Documentos | END  -->

  </div>
{% endblock content %}


{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
    <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

  <script>
    let form = document.getElementById('form-uploadtxt');
    let submitButton = document.getElementById('submit-button');
    let spinner = document.getElementById('spinner');

    const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 20000,
    timerProgressBar: true,
    didOpen: (toast) => {
      toast.addEventListener('mouseenter', Swal.stopTimer);
      toast.addEventListener('mouseleave', Swal.resumeTimer);
    }
  });

    $(document).ready(function(){
      // Check for toast message in URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const toastMessage = urlParams.get('toast_message');
      const toastType = urlParams.get('toast_type') || 'error';

      if (toastMessage && toastMessage.trim() !== "") {
        Toast.fire({
          icon: toastType,
          title: toastMessage
        });

        // Remove the parameters from URL without reloading the page
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }

      // We no longer need to check for error messages in the DOM
      // as we're now using URL parameters for all messages

      const dataTableOptions = {
        paging: false,
        searching: true,
        ordering: true,
        truncation: true,
        info: true,
        footer: true,
        order: [ [2, 'desc'] ],
        language: {
          lengthMenu: "_MENU_",
          zeroRecords: "No se han encontrado ficheros de movimientos.",
          info: "_TOTAL_ resultados. ",
          search: "Buscar:",
          infoEmpty: "No hay resultados que coincidan con su búsqueda.",
          infoFiltered: ""
        },
        dom: 'lrtip',
        fixedHeader: true,
      };

      const dataTable =$("#list-table").DataTable(dataTableOptions);

      $("#search").on("input", function(){
          const filtro =$(this).val();
          console.log(filtro)
          dataTable.search(filtro).draw();
      });

    });


    form.addEventListener('submit', function() {
        submitButton.disabled = true;
        spinner.style.display = 'block';
    });

    submitButton.disabled = false;
    spinner.style.display = 'none';
  </script>
{% endblock javascripts %}
