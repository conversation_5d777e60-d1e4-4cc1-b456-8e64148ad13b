# Importaciones estándar de Python
import json
import logging
import uuid
from datetime import datetime
# Importaciones de Django
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.db import models
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from django.views.generic import UpdateView, View, TemplateView
from django.views import View
from django.db import transaction
from django.forms import modelformset_factory
from django.http import Http404
from collections import defaultdict
# Importaciones de Modelos MUAYTAX
from muaytax.app_address.models.address import Address
from muaytax.app_documents.models.document import Document
from muaytax.app_documents.models.processed_form import ProcessedForm
from muaytax.app_partners.models.partner import Partner
from muaytax.app_sellers.models.previous_accounting_manager import PreviousAccountingManager
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.type_form import TypeForm

from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.product_service import ProductService


# Importaciones de Formularios MUAYTAX 
from muaytax.app_sellers.forms.seller_vat_request_forms import (
    AddressForm,
    CompanyInfoForm,
    MigrationInfoForm,
    PartnerForm,
    SellerVatForm,
    CountryDocumentForm,
    CountryDocumentForm,
)

# Permisos
from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission

# Helpers
from muaytax.app_sellers.utils import (
    generate_document_field_mapping,
    get_or_create_document_type,
    get_documents_block_data,
    get_visible_fields_by_block,
    set_document_file_info,
)

from muaytax.email_notifications.form_validation import send_validation_notification


# Variables de configuración
logger = logging.getLogger(__name__)
debug = settings.DEBUG 

## Vista principal del formulario de Servicios IVA.
class SellerVatRequestServiceIVAView(LoginRequiredMixin, IsSellerShortnamePermission, UpdateView):
    """Gestiona el formulario completo de Servicios IVA."""
    
    template_name = "sellers/seller_vat_request_service_iva.html"
    form_class = SellerVatForm
    context_object_name = "sellervat"
    
    # Controla la obtención del objeto
    def get_object(self, queryset=None):
        """Obtiene un SellerVat con contracting_date para este Seller."""
        shortname = self.kwargs.get("shortname")
        seller_vat = SellerVat.objects.filter(
            seller__shortname=shortname,
            contracting_date__isnull=False
        ).first()

        if not seller_vat:
            return None  # Permite manejar casos sin SellerVat

        return seller_vat
    
    # Restringe el acceso de usuario antes de get o post (is_staff=True)
    def dispatch(self, request, *args, **kwargs):
        """Verifica permisos: Solo el dueño del Seller o un staff pueden acceder."""
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        if request.user != seller.user and not request.user.is_staff:
            raise PermissionDenied("No tienes permiso para acceder a esta página.")

        return super().dispatch(request, *args, **kwargs)
    
    # Genera el contexto necesario para la vista
    def get_context_data(self, **kwargs):
        """Genera los formularios a procesar y otra info"""
        context = super().get_context_data(**kwargs)

        # Obtener la instancia del `Seller` basado en el `shortname` de la URL
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        # Filtrar todos los `SellerVat` que tienen `contracting_date` definido
        seller_vats = SellerVat.objects.filter(
            seller=seller,
            contracting_date__isnull=False,
            maintenance_type__isnull=False,
        )
        
        seller_vats_with_maintenance = seller_vats.filter(maintenance_type__isnull=False)
        document_countries = list(seller_vats_with_maintenance.values_list("vat_country__iso_code", flat=True))
        debug and print(f"Países con documentación adicional dinámica: {document_countries}")

        document_mappings = generate_document_field_mapping(document_countries)
        country_docs = document_mappings["country_documents"]

        debug and print("\n**DEBUG: Países en SellerVat con contracting_date:**")
        # Verificando TODOS los países con contracting_date
        country_debug_info = [
            f"{vat.vat_country.iso_code}: {vat.maintenance_type}" for vat in seller_vats
        ]
        for info in country_debug_info:
            debug and print(f"{info}")

        # Obtener el ProcessedForm si existe
        category_form = TypeForm.objects.get(code="iva_form")
        processed_form = ProcessedForm.objects.filter(seller=seller, category_form=category_form).first()
        
        # Cargar el JSON si existe y está activo
        json_data = json.loads(processed_form.json_form or "{}") if processed_form and processed_form.json_form else {}
        iva_form = json_data.get("iva_form", {})

        # Determinar si se deben usar los datos del JSON
        use_json_data = bool(processed_form and not processed_form.is_form_processed)
        debug and print(f"\nuse_json_data: {use_json_data}")

        # Extraer bloque de datos del JSON si aplica
        json_company = iva_form.get("company_info_form", {}) if use_json_data else {}
        debug and print(f"Bloque company_info_form (JSON): {json_company}")

        company_info_data = {
            "name": seller.name,
            "legal_entity": seller.legal_entity,
            "company_address": {
                "address": seller.seller_address.address if seller.seller_address else "",
                "address_zip": seller.seller_address.address_zip if seller.seller_address else "",
                "address_city": seller.seller_address.address_city if seller.seller_address else "",
                "address_state": seller.seller_address.address_state if seller.seller_address else "",
                "address_country": seller.seller_address.address_country.iso_code if seller.seller_address and seller.seller_address.address_country else "",
            },
            "phone_country": seller.phone_country.iso_code if seller.phone_country else "",
            "contact_phone": str(seller.contact_phone) if seller.contact_phone else "",
        }

        editable_fields = ["activity_type", "desc_main_activity", "products_and_services"]

        for field in editable_fields:
            json_value = json_company.get(field, "")
            db_value = getattr(seller, field, "")
            if field == "products_and_services":
                db_value = db_value.code if db_value else ""
            company_info_data[field] = json_value or db_value if use_json_data else db_value or json_value

        # Documentos siempre desde DB
        company_docs = generate_document_field_mapping()["company_info"]
        doc_data = get_documents_block_data(company_docs, seller)
        company_info_data.update(doc_data)

        # Imprimir resultado final
        debug and print("\nDatos iniciales cargados en CompanyInfoForm:")
        for key, val in company_info_data.items():
            debug and print(f"########################→ {key}: {val} (tipo: {type(val)})")


        # Filtrar países con `maintenance_type="maintenance"`
        seller_vats_maintenance = seller_vats.filter(maintenance_type="maintenance")
        # Obtener países con "Solo Mantenimiento"
        migration_countries = list(seller_vats_maintenance.values_list("vat_country__iso_code", flat=True))
        # Países con requisitos adicionales (Francia e Italia)
        countries_with_docs = [c for c in migration_countries if c in ["IT", "FR"]]
        
        # Diccionario de {ISO_CODE: Nombre del país}
        country_names = {vat.vat_country.iso_code: vat.vat_country.name for vat in seller_vats}

        # Determinar si hay SellerVats con "Solo Mantenimiento"
        has_only_maintenance = seller_vats_maintenance.exists()

        # Obtener documentos de migración desde la DB
        migration_docs = document_mappings["migration"]
        migration_docs_data = get_documents_block_data(migration_docs, seller)

        migration_info_data = {}
        for vat in seller_vats_maintenance:
            iso = vat.vat_country.iso_code
            block_key = f"migration_info_{iso}"
            json_block = iva_form.get(block_key, {}) if use_json_data else {}

            debug and print(f"\nPaís: {iso}")
            debug and print(f"🔸 JSON block: {json_block}")

            # Obtener instancia del gestor anterior
            previous_manager = PreviousAccountingManager.objects.filter(seller_vat=vat).first()
            if previous_manager:
                debug and print(f"PreviousAccountingManager encontrado para {iso}")
            else:
                debug and print(f"No hay PreviousAccountingManager para {iso}")

            # Dirección del gestor anterior
            address = previous_manager.previous_manager_address if previous_manager and previous_manager.previous_manager_address else None
            if address:
                debug and print(f"Dirección encontrada: ID {address.id}")

            address_data = {
                "address": json_block.get("address") or (address.address if address else ""),
                "address_city": json_block.get("address_city") or (address.address_city if address else ""),
                "address_state": json_block.get("address_state") or (address.address_state if address else ""),
                "address_zip": json_block.get("address_zip") or (address.address_zip if address else ""),
                "address_country": json_block.get("address_country") or (address.address_country.iso_code if address and address.address_country else ""),
            }

            for key, val in address_data.items():
                origen = "JSON" if json_block.get(key) else "DB"
                debug and print(f"{key}: {val} (desde {origen})")

            # Documento de Francia: solo si el país es FR
            field_key = "france_old_manager_letter"
            doc_data = migration_docs_data.get(field_key, {}) if iso == "FR" else {}
            json_doc = json_block.get(field_key)
            doc_final = json_doc if json_doc else doc_data

            origen_doc = "JSON" if json_doc else ("DB" if doc_data else "VACÍO")
            debug and print(f"{field_key}: {doc_final} (origen: {origen_doc})")

            data = {
                "previous_manager_end_date": json_block.get("previous_manager_end_date") or (previous_manager.previous_manager_end_date.strftime("%Y-%m-%d") if previous_manager and previous_manager.previous_manager_end_date else ""),
                "previous_accounting_filed": json_block.get("previous_accounting_filed") or (str(previous_manager.previous_accounting_filed) if previous_manager and previous_manager.previous_accounting_filed is not None else ""),
                "current_accounting_filed_date": json_block.get("current_accounting_filed_date") or (previous_manager.current_accounting_filed_date.strftime("%Y-%m-%d") if previous_manager and previous_manager.current_accounting_filed_date else ""),
                "previous_manager_name": json_block.get("previous_manager_name") or (previous_manager.previous_manager_name if previous_manager else ""),
                field_key: doc_final,
            }

            # Solo Italia → agregar vat_frequency
            if iso == "IT":
                data["vat_frequency"] = json_block.get("vat_frequency") or (vat.vat_frequency if vat.vat_frequency else "")
                debug and print(f"vat_frequency: {data['vat_frequency']} (desde {'JSON' if json_block.get('vat_frequency') else 'DB'})")

            # Mezclar con dirección
            data.update(address_data)

            # Convertir a JSON para usarlo en el template JS
            migration_info_data[block_key] = json.dumps(data, default=str)


        # DOCUMENTOS POR PAÍS: combinar datos del JSON y DB
        documents_by_country_data = {}

        for seller_vat in seller_vats_with_maintenance:
            iso = seller_vat.vat_country.iso_code
            block_key = f"documents_by_country_{iso}"
            debug and print(f"\nProcesando documentos para país: {iso}")

            # Obtener documentos existentes de ese país
            docs_queryset = Document.objects.filter(seller=seller, sellerVat=seller_vat)
            docs_db_data = get_documents_block_data(country_docs, seller, seller_vat=seller_vat)

            # Obtener bloque desde JSON si aplica
            json_block = iva_form.get(block_key, {}) if use_json_data else {}

            combined_docs = {}

            for field_name in country_docs.keys():
                if not field_name.startswith(iso):
                    continue

                json_doc = json_block.get(field_name)
                db_doc = docs_db_data.get(field_name)

                final_doc = json_doc or db_doc
                origen = "JSON" if json_doc else "DB" if db_doc else "VACÍO"
                debug and print(f"Campo {field_name}: {final_doc} (origen: {origen})")

                if final_doc:
                    combined_docs[field_name] = final_doc

            # Convertimos el dict final a JSON string
            documents_by_country_data[block_key] = json.dumps(combined_docs, default=str)

        
        # Socios del JSON (confirmados)
        partners_json = iva_form.get("partners", [])
        processed_partners = json.dumps(partners_json, default=str) # Convertir None a null en el JSON antes de pasarlo al template
        processed_partners_count = len(partners_json)

        # Obtener los socios asociados al Seller y seleccionar solo los campos relevantes
        partners_from_model = Partner.objects.filter(seller=seller).values(
            "id", "name", "last_name", "id_number", "id_passport", "start_date", "end_date",
            "shares_percentage", "address__address", "address__address_zip","address__address_city", 
            "address__address_state", "address__address_country__name", "address__address_country__iso_code",
        )

        # Formatear la información de los socios y agregar sus documentos en un list[dict[str, Any]]
        partners_formatted = []
        for p in partners_from_model:
            partner_id = p["id"]

            # Buscar el documento de DNI del socio en Documnt <- model
            dni_document = Document.objects.filter(seller=seller, documentType__code="DOC-DNI", partner_id=partner_id).first()
            # Buscar el documento de Pasaporte del socio en Documnt <- model
            passport_document = Document.objects.filter(seller=seller, documentType__code="DOC-Passport", partner_id=partner_id).first()

            # Obtener el estado de confirmación desde el JSON Form si existe
            confirmation_status = False  # Por defecto en False si no está en el JSON
            # Construir un dict: {partner_id: confirmation_status}
            confirmation_status_map = {
                p["partner_id"]: p.get("confirmation_status", False)
                for p in iva_form.get("partners", [])
            }
            confirmation_status = confirmation_status_map.get(partner_id, False)

            # Construir el diccionario con la información del socio
            partner_data = {
                "id": partner_id,
                "name": p["name"],
                "last_name": p["last_name"],
                "id_number_dni": p["id_number"],  # Número de identificación (DNI/NIF)
                "id_number_passport": p["id_passport"],  # Número de pasaporte
                "shares_percentage": p["shares_percentage"],  # Porcentaje de participación en la empresa
                "start_date": p["start_date"],  # Fecha de alta del socio
                "end_date": p["end_date"],  # Fecha de baja del socio (si aplica)
                "confirmation_status": confirmation_status,  # Estado de confirmación del formulario de IVA en el json_form
                "address": {
                    "address": p["address__address"],  # Dirección principal
                    "address_zip": p["address__address_zip"],  # Código postal
                    "address_city": p["address__address_city"],  # Complemento de la dirección
                    "address_state": p["address__address_state"],  # Complemento de la dirección
                    "address_country_name": p["address__address_country__name"],  # Nombre del país de la dirección
                    "address_country_iso_code": p["address__address_country__iso_code"],  # Código ISO del país
                },
                "documents": {
                    "dni_file": dni_document.file.url if dni_document else "",  # URL del DNI
                    "passport_file": passport_document.file.url if passport_document else "",  # URL del pasaporte
                }
            }

            # Agregar la información del socio a la lista final
            partners_formatted.append(partner_data)

        debug and print("\nSocios recuperados desde `Partner` y formateados:")
        debug and print(json.dumps(partners_formatted, indent=4, ensure_ascii=False, default=str))

        # Calcular la suma total de shares_percentage de los socios en json_form
        total_shares_percentage_json = sum(float(p.get("shares_percentage", 0) or 0) for p in partners_json)

        partners_json = json.dumps(partners_formatted, default=str)
        
        ### SE PASAN TODOS LOS FORMULARIOS DE LOS INCLUDE ###
        # Formulario principal
        seller_vat_form = {}

        for vat in seller_vats_with_maintenance:
            iso = vat.vat_country.iso_code
            debug and print(f"\nGenerando SellerVatForm para país: {iso}")
            debug and print(f"→ VAT Number: {vat.vat_number}")
            debug and print(f"→ Fecha contracting_date (valor): {vat.contracting_date}")
            debug and print(f"→ Fecha contracting_date (tipo): {type(vat.contracting_date)}")
            debug and print(f"→ Tipo mantenimiento: {vat.maintenance_type}")

            form = SellerVatForm(instance=vat, prefix=iso)
            debug and print(f"→ Initial en el form: {form.initial.get('contracting_date')} (tipo: {type(form.initial.get('contracting_date'))})")

            seller_vat_form[iso] = form

        # Pasar también el activity_type al form para filtrar el queryset
        activity_type = company_info_data.get("activity_type")
        
        # Inicializar formulario
        company_form = CompanyInfoForm(
            initial=company_info_data,
            seller=seller,
            activity_type=activity_type,  # NECESARIO para preselección
        )

        # Generar formularios de migración IVA
        migration_forms = {
            vat.vat_country.iso_code: MigrationInfoForm(instance=vat, seller_vat=vat)
            for vat in seller_vats_maintenance
        }
        # Formulario de socios
        partner_form = PartnerForm()

        # Formulario documentos con todos los países contratados con mantenimiento

        country_document_forms = {}

        for seller_vat_instance in seller_vats_with_maintenance:
            country_iso = seller_vat_instance.vat_country.iso_code
            existing_documents = Document.objects.filter(sellerVat=seller_vat_instance)
            
            form = CountryDocumentForm(
                instance=existing_documents.first(),
                seller_vat=seller_vat_instance,
            )

            debug and print(f"Generado formulario de documentos para {country_iso} con campos: {list(form.fields.keys())}")
            
            country_document_forms[country_iso] = form


        for country_iso, form in country_document_forms.items():
            debug and print(f"\nPaís: {country_iso}, Campos: {list(form.fields.keys())}")

        # Apartado final 
        seller_vats_with_maintenance_list = list(seller_vats_with_maintenance.values_list("vat_country__iso_code", flat=True))

        existing_docs = {
            "amazon_vies_screenshot": Document.objects.filter(
                seller=seller,
                documentType__code="DOC-AmazonViesScreenshot"
            ).first(),
        }

        all_product_services_qs = ProductService.objects.values("code", "description", "product_service_type")
        all_product_services_list = list(all_product_services_qs)
        debug and print("Productos / Servicios disponibles:", all_product_services_list)
        all_product_services_json = json.dumps(all_product_services_list, default=str)

        # Nuevo: determina si el formulario general está procesado
        is_form_processed = bool(processed_form and processed_form.is_form_processed)
        is_partial_saved = bool(processed_form and processed_form.is_partial_opening)
        submitted_vats = seller_vats.filter(form_submitted=True)
        # Nuevo: obtener lista de países ya enviados (form_submitted=True)
        # submitted_vats = SellerVat.objects.filter(seller=seller, form_submitted=True)
        submitted_countries = list(submitted_vats.values_list("vat_country__iso_code", flat=True))
        unsubmitted_vats = seller_vats.filter(form_submitted=False)
        unsubmitted_countries = list(unsubmitted_vats.values_list("vat_country__iso_code", flat=True))
        debug and print("is_form_processed:", is_form_processed)
        debug and print("is_partial_saved:", is_partial_saved)
        debug and print("submitted_countries:", submitted_countries)

        # Obtener las validaciones del gestor
        manager_validation_data = json.loads(processed_form.manager_validation or "{}") if processed_form else {}
        debug and print(f'//////////////////////////////{manager_validation_data}/////////////////////////////////')
        validation_company_info = manager_validation_data.get("company_info_form", {})
        validation_partners = manager_validation_data.get("partner", {})
        validation_documents = {k: v for k, v in manager_validation_data.items() if k.startswith("documents_by_country_")}
        validation_migration = {k: v for k, v in manager_validation_data.items() if k.startswith("migration_info_")}

        is_new_form = processed_form is None or (
            not processed_form.is_form_processed and not processed_form.is_partial_opening
        )

        # Crear dict: {ISO: {"count": X, "blocks": ["documentos", "migración"]}}

        countries_errors_info = {}

        for block_key, fields in manager_validation_data.items():
            if not isinstance(fields, dict):
                continue

            if block_key.startswith("documents_by_country_"):
                iso = block_key.split("_")[-1]
                doc_errors = sum(
                    1 for f in fields.values()
                    if isinstance(f, dict) and f.get("status") == "incorrecto" and f.get("pending") is True
                )
                if doc_errors:
                    countries_errors_info.setdefault(iso, {"total": 0, "messages": []})
                    countries_errors_info[iso]["total"] += doc_errors
                    countries_errors_info[iso]["messages"].append(
                        f"{doc_errors} error{'es' if doc_errors > 1 else ''} en Documentación"
                    )

            elif block_key.startswith("migration_info_"):
                iso = block_key.split("_")[-1]
                mig_errors = sum(
                    1 for f in fields.values()
                    if isinstance(f, dict) and f.get("status") == "incorrecto" and f.get("pending") is True
                )
                if mig_errors:
                    countries_errors_info.setdefault(iso, {"total": 0, "messages": []})
                    countries_errors_info[iso]["total"] += mig_errors
                    countries_errors_info[iso]["messages"].append(
                        f"{mig_errors} error{'es' if mig_errors > 1 else ''} en Migración"
                    )


        debug and print(f'El seller {seller.shortname} tiene un formulario nuevo para?? -> {is_new_form} ?')
        # Pasar todos los datos al contexto
        context.update({
            "seller": seller,
            "active_seller_vats": seller_vats,
            "debug" : debug,
            "company_form": company_form,
            "seller_vat_form": seller_vat_form,
            "migration_forms": migration_forms,
            "migration_countries": migration_countries,
            "countries_with_docs": countries_with_docs,
            "country_names": country_names,
            "has_only_maintenance": has_only_maintenance,
            "partners_formatted": partners_formatted,  # Socios existentes aún no agregados
            "partners_json": partners_json,  # Json con todos los socios en la DB 
            "partner_form": partner_form, # Formulario de socios
            "processed_partners": processed_partners,  # Socios que ya fueron agregados
            "processed_partners_count": processed_partners_count,  # Cantidad de socios en ProcessedForm
            "legal_entity": seller.legal_entity,
            "total_shares_percentage_json": total_shares_percentage_json,
            "document_countries": document_countries,
            "document_countries_json": json.dumps(document_countries),
            "country_document_forms": country_document_forms,
            "seller_vats_with_maintenance_list": seller_vats_with_maintenance_list,
            "company_info_data": json.dumps(company_info_data, default=str),
            "migration_info_data": migration_info_data,
            "documents_by_country_data": documents_by_country_data,
            "existing_docs": existing_docs,
            # Validacion y logica de reapertura/cierre parcial o final
            "is_new_form": is_new_form,
            "all_product_services_json": all_product_services_json,
            "is_form_processed": is_form_processed,
            "is_partial_saved": is_partial_saved,
            "submitted_countries": json.dumps(submitted_countries),
            "unsubmitted_countries": json.dumps(unsubmitted_countries),
            # Por bloques
            "manager_validation_data": json.dumps(manager_validation_data),
            "validation_company_info": json.dumps(validation_company_info),
            "validation_partners": json.dumps(validation_partners),
            "validation_documents": json.dumps(validation_documents),
            "validation_migration": json.dumps(validation_migration),
            "countries_errors_info": countries_errors_info,
        })

        return context
    
    # Maneja acciones GET dinámicas como refrescar bloques específicos por 'action'.
    def get(self, request, *args, **kwargs):
        action = request.GET.get("action")
        print(f"🔁 Acción recibida en GET: {action}")

        if action == "get_partners_block":
            return self.get_partners_block(request)

        return super().get(request, *args, **kwargs)
    
    # Devuelve el bloque HTML con la tabla de socios actualizada dinámicamente
    def get_partners_block(self, request):
        print("📦 get_partners_block ejecutado desde SellerVatRequestServiceIVAView")

        self.object = self.get_object()  # Requiere cargar el objeto del formulario
        context = self.get_context_data()

        html = render_to_string(
            "sellers/include/service_iva/members_info.html",  # O el path correcto
            context,
            request
        )
        return JsonResponse({"html": html})

    # Proceso de guardado parcial en processedForm (json_form)
    def post(self, request, *args, **kwargs):
        try:
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            category_form = TypeForm.objects.get(code="iva_form")

            seller_vats_with_maintenance = SellerVat.objects.filter(
                seller=seller,
                contracting_date__isnull=False,
                maintenance_type__isnull=False,
                form_submitted=False
            )
            country_names = {
                vat.vat_country.iso_code: vat.vat_country.name
                for vat in seller_vats_with_maintenance
            }
            iso_country_codes = list(seller_vats_with_maintenance.values_list("vat_country__iso_code", flat=True))
            for iso in iso_country_codes:
                if iso == "DE":
                    get_or_create_document_type("DE-CURRENTVATDOCS-1", "Alemania", " #1")
                    get_or_create_document_type("DE-CURRENTVATDOCS-2", "Alemania", " #2")
                else:
                    doc_code = f"{iso}-CURRENTVATDOCS"
                    country_name = country_names.get(iso, iso)
                    get_or_create_document_type(doc_code, country_name)

            # Usar función centralizada para obtener el mapeo
            FIELD_TO_DOCUMENT_TYPE = generate_document_field_mapping(iso_country_codes)["full_mapping"]

            # Obtener JSON desde request.POST
            iva_form_raw = request.POST.get("iva_form", "{}")
            debug and print("\nJSON recibido (raw):")
            debug and print(json.dumps(json.loads(iva_form_raw), indent=4, ensure_ascii=False))

            iva_form_data = json.loads(iva_form_raw)

            processed_form, _ = ProcessedForm.objects.get_or_create(
                seller=seller,
                category_form=category_form,
                defaults={"year": datetime.now().year, "json_form": "{}"}
            )

            old_json = json.loads(processed_form.json_form or "{}")
            old_iva_form = old_json.get("iva_form", {})

            debug and print("\nJSON previo en DB:")
            debug and print(json.dumps(old_json, indent=4, ensure_ascii=False))

            # Si el frontend mandó partners vacíos, NO sobrescribas los anteriores
            if "partners" not in iva_form_data or not iva_form_data["partners"]:
                debug and print("Socios vacíos: se mantienen los existentes")
                iva_form_data["partners"] = old_iva_form.get("partners", [])
            else:
                debug and print("Socios nuevos recibidos → se sobrescriben")

            # Actualizar siempre partners_pending_validation
            iva_form_data["partners_pending_validation"] = iva_form_data.get("partners_pending_validation", False)
            debug and print(f"partners_pending_validation: {iva_form_data['partners_pending_validation']}")

            # Guardar archivos o mantener los anteriores
            for block_key, block_data in iva_form_data.items():
                if block_key.startswith("documents_by_country_") or block_key == "company_info_form" or block_key.startswith("migration_info"):
                    for field_name, field_content in block_data.items():
                        if not isinstance(field_content, dict):
                            continue

                        uploaded_file = request.FILES.get(field_name)
                        document_code = FIELD_TO_DOCUMENT_TYPE.get(field_name)
                        if not document_code:
                            continue

                        doc_type = DocumentType.objects.filter(code=document_code).first()
                        if not doc_type:
                            continue

                        iso_code = None
                        if "documents_by_country_" in block_key or "migration_info_" in block_key:
                            iso_code = block_key.split("_")[-1]

                        seller_vat = SellerVat.objects.filter(seller=seller, vat_country__iso_code=iso_code).first() if iso_code else None

                        if uploaded_file:
                            document, _ = Document.objects.update_or_create(
                                seller=seller,
                                sellerVat=seller_vat,
                                documentType=doc_type,
                                defaults={"file": uploaded_file}
                            )
                            field_content["file"] = document.file.url
                            field_content["value"] = uploaded_file.name
                            debug and print(f"Documento guardado: {uploaded_file.name} → {document.file.url}")

                        else:
                            set_document_file_info(field_content, seller, seller_vat, doc_type)

            # Asegurarse de que los nuevos campos de empresa estén en el JSON aunque sean vacíos
            company_info_block = iva_form_data.get("company_info_form", {})
            for field in ["activity_type", "products_and_services", "desc_main_activity"]:
                if field not in company_info_block:
                    debug and print(f"Campo nuevo '{field}' no encontrado → Se agrega vacío")
                    company_info_block[field] = ""
            iva_form_data["company_info_form"] = company_info_block

            # Guardar resultado final
            processed_form.json_form = json.dumps({"iva_form": iva_form_data}, default=str)
            processed_form.save()

            debug and print("\nJSON final guardado en ProcessedForm:")
            debug and print(json.dumps({"iva_form": iva_form_data}, indent=4, ensure_ascii=False))

            return JsonResponse({"success": True, "message": "Formulario guardado correctamente."})

        except Exception as e:
            logger.error(f"Error al guardar formulario IVA: {e}")
            debug and print(f"Error en POST: {e}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)

    # Redirige a la misma URL desde la que se hizo la solicitud.
    def get_success_url(self):
        """Redirige tras el guardado."""
        return self.request.path

## Vista del Modal que envia el formulario de Socios.
class AddPartnerView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    """Guarda o actualiza un socio en Partner, Address y Document y registra en ProcessedForm."""

    # Solicitud POST - Recibe el formulario
    def post(self, request, *args, **kwargs):
        try:
            # Obtener Seller
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            category_form = TypeForm.objects.get(code="iva_form")

            # Obtener o crear el ProcessedForm (con bloque "iva_form" como dict)
            processed_form, _ = ProcessedForm.objects.get_or_create(
                seller=seller,
                category_form=category_form,
                defaults={"json_form": json.dumps({"iva_form": {}}, default=str), "year": datetime.now().year}
            )

            # Leer JSON actual del formulario
            json_data = json.loads(processed_form.json_form or "{}")
            iva_form = json_data.get("iva_form", {})
            partners_list = iva_form.get("partners", [])

            # Datos recibidos
            post_data = request.POST.dict()
            debug and print("Datos recibidos en el POST:", post_data)

            # Obtener DNI, Pasaporte e ID (si existe)
            dni_number = post_data.get("id_number_dni", "").strip()
            passport_number = post_data.get("id_number_passport", "").strip() or ''
            partner_id = post_data.get("partner_id", "").strip()
            debug and print(f'###############################partner_id = {partner_id}')
            # ───────────────────────────────
            # 1. Detectar si es edición o creación
            # ───────────────────────────────
            if partner_id:
                debug and print(f"Modo edición: socio con partner_id={partner_id}")
                existing_partner = Partner.objects.filter(id=partner_id, seller=seller).first()

                if not existing_partner:
                    return JsonResponse({
                        "success": False,
                        "error_type": "invalid_partner",
                        "message": f"No se encontró el socio con ID {partner_id}."
                    }, status=400)

                # Validar que el nuevo DNI o Pasaporte no estén en uso por otro socio
                if Partner.objects.filter(seller=seller, id_number=dni_number).exclude(id=partner_id).exists():
                    return JsonResponse({
                        "success": False,
                        "error_type": "duplicate_dni",
                        "message": f"El DNI {dni_number} ya está en uso por otro socio."
                    }, status=400)

                if passport_number and Partner.objects.filter(seller=seller, id_passport=passport_number).exclude(id=partner_id).exists():
                    return JsonResponse({
                        "success": False,
                        "error_type": "duplicate_passport",
                        "message": f"El pasaporte {passport_number} ya está en uso por otro socio."
                    }, status=400)

            else:
                debug and print(f"Modo creación: DNI={dni_number}, Pasaporte={passport_number}")

                # Validar que no exista ya el socio
                if Partner.objects.filter(seller=seller, id_number=dni_number).exists():
                    return JsonResponse({
                        "success": False,
                        "error_type": "duplicate_dni",
                        "message": f"El DNI {dni_number} ya está en uso por otro socio."
                    }, status=400)

                if passport_number and Partner.objects.filter(seller=seller, id_passport=passport_number).exists():
                    return JsonResponse({
                        "success": False,
                        "error_type": "duplicate_passport",
                        "message": f"El pasaporte {passport_number} ya está en uso por otro socio."
                    }, status=400)

                existing_partner = None

            # 4. Buscar socio en el JSON (dentro del JSON del formulario)
            existing_entry = next(
                (p for p in partners_list if (
                    partner_id and str(p["partner_id"]) == partner_id
                ) or (
                    not partner_id and p["id_number_dni"] == dni_number
                )),
                None
            )


            confirmation_status = existing_entry["confirmation_status"] if existing_entry else False

            if existing_partner:
                # Actualizar socio existente
                existing_partner.name = post_data["name"]
                existing_partner.last_name = post_data["last_name"]
                existing_partner.id_number = dni_number  # DNI siempre obligatorio
                existing_partner.id_passport = passport_number  # Pasaporte siempre obligatorio
                existing_partner.shares_percentage = float(post_data.get("shares_percentage", 0))
                existing_partner.start_date = post_data.get("start_date")
                existing_partner.end_date = post_data.get("end_date") or None
                existing_partner.save()
                debug and print(f"Socio actualizado: {existing_partner.name} - DNI: {existing_partner.id_number} - Pasaporte: {existing_partner.id_passport}")
            else:
                # Crear nuevo socio
                existing_partner = Partner.objects.create(
                    name=post_data["name"],
                    last_name=post_data["last_name"],
                    id_number=dni_number,  # DNI obligatorio
                    id_passport=passport_number,  # Pasaporte obligatorio
                    shares_percentage=float(post_data.get("shares_percentage", 0)),
                    start_date=post_data.get("start_date") or None,
                    end_date=post_data.get("end_date") or None,
                    seller=seller
                )
                debug and print(f"Nuevo socio creado: {existing_partner.name} - DNI: {existing_partner.id_number} - Pasaporte: {existing_partner.id_passport}")

            # Crear o actualizar Dirección correctamente
            address, _ = Address.objects.update_or_create(
                id=existing_partner.address.id if existing_partner.address else None,  # Usa el ID si ya tiene dirección
                defaults={
                    "address": post_data.get("address", "").strip(),
                    "address_zip": post_data.get("address_zip", "").strip(),
                    "address_city": post_data.get("address_city", "").strip(),
                    "address_state": post_data.get("address_state", "").strip(),
                    "address_country_id": post_data.get("address_country", "").strip(),
                }
            )

            # Asignar la dirección al socio
            existing_partner.address = address
            existing_partner.save()
            debug and print(f"Dirección guardada para {existing_partner.name}: {address.address}")

            # Guardar Documentos (DNI y Pasaporte) ahora vinculados con Partner
            documents = {}
            for doc_type_code, file_key in [("DOC-DNI", "dni_file"), ("DOC-Passport", "passport_file")]:
                if file_key in request.FILES:
                    doc_type = DocumentType.objects.get(code=doc_type_code)

                    # Verificar si ya existe un documento del mismo tipo para este socio
                    existing_doc = Document.objects.filter(
                        documentType=doc_type,
                        seller=seller,
                        partner=existing_partner,  # Filtrar por socio
                        sellerVat=None  # Opcional si los documentos son solo del socio y no de un SellerVat
                    ).first()

                    if existing_doc:
                        existing_doc.file = request.FILES[file_key]  # Sobrescribir archivo
                        existing_doc.save()
                    else:
                        existing_doc = Document.objects.create(
                            file=request.FILES[file_key],
                            documentType=doc_type,
                            seller=seller,
                            partner=existing_partner  # Asignar socio
                        )

                    # Guardar URL del archivo para el frontend
                    documents[file_key] = existing_doc.file.url
                    debug and print(f"Documento guardado: {existing_doc.file.name}")

            # confirmation_status = existing_entry["confirmation_status"] if existing_entry else True

            partner_data = {
                "id": existing_entry["id"] if existing_entry else str(uuid.uuid4()),
                "partner_id": existing_partner.id,
                "name": existing_partner.name,
                "last_name": existing_partner.last_name,
                "id_number_dni": existing_partner.id_number,
                "id_number_passport": existing_partner.id_passport,
                "shares_percentage": existing_partner.shares_percentage,
                "start_date": str(existing_partner.start_date),
                "end_date": str(existing_partner.end_date) if existing_partner.end_date else "",
                "address": {
                    "address": address.address,
                    "address_zip": address.address_zip,
                    "address_city": address.address_city,
                    "address_state": address.address_state,
                    "address_country_name": address.address_country.name,
                    "address_country_code": address.address_country_id,
                },
                "documents": documents,
                "confirmation_status": True
            }

            # Reemplazar o agregar socio en partners_list
            if existing_entry:
                existing_entry.update(partner_data)
            else:
                partners_list.append(partner_data)

            # Guardar en JSON final
            iva_form["partners"] = partners_list
            iva_form["partners_pending_validation"] = any(not p["confirmation_status"] for p in partners_list)
            json_data["iva_form"] = iva_form
            processed_form.json_form = json.dumps(json_data, default=str)
            processed_form.save()
            debug and print(f"ProcessedForm actualizado con socio: {existing_partner.name}")

            return JsonResponse({"success": True, "partner_info": partner_data})

        except Exception as e:
            debug and print(f"Error al guardar socio: {e}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)

    # Guardar datos del formulario enviado válido
    def form_valid(self, form):
        self.object = form.save()
        seller = self.object.seller
        partner = form.instance

        # Mantener documentos previos si no se sube uno nuevo
        dni_file = self.request.FILES.get("dni_file")
        passport_file = self.request.FILES.get("passport_file")

        if dni_file:
            Document.objects.update_or_create(
                seller=seller,
                documentType=DocumentType.objects.get(code="DOC-DNI"),
                defaults={"file": dni_file},
            )

        if passport_file:
            Document.objects.update_or_create(
                seller=seller,
                documentType=DocumentType.objects.get(code="DOC-Passport"),
                defaults={"file": passport_file},
            )

        return redirect(self.get_success_url())

class FinalSubmitIVAView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        try:
            debug and print("🚀 Iniciando FinalSubmitIVAView POST")

            seller = get_object_or_404(Seller, shortname=kwargs["shortname"])
            category_form = TypeForm.objects.get(code="iva_form")

            processed_form, _ = ProcessedForm.objects.get_or_create(
                seller=seller,
                category_form=category_form,
                defaults={"json_form": json.dumps({"iva_form": {}}, default=str), "year": datetime.now().year}
            )

            old_json = json.loads(processed_form.json_form or "{}")
            old_iva_form = old_json.get("iva_form", {})

            iva_form_raw = request.POST.get("iva_form", "{}")
            debug and print("📩 JSON recibido desde frontend:")
            debug and print(json.dumps(json.loads(iva_form_raw), indent=4, ensure_ascii=False))

            new_iva_form = json.loads(iva_form_raw)

            # 🧠 Merge de datos antiguos con nuevos
            if "partners" not in new_iva_form:
                new_iva_form["partners"] = old_iva_form.get("partners", [])
            if "partners_pending_validation" not in new_iva_form:
                new_iva_form["partners_pending_validation"] = old_iva_form.get("partners_pending_validation", False)

            updated_iva_form = {**old_iva_form, **new_iva_form}

            debug and print("\n📦 Iniciando guardado de bloques...")

            # 🔹 Información de migración
            for key, block in updated_iva_form.items():
                if key.startswith("migration_info_"):
                    iso = key.split("_")[-1]
                    sv = SellerVat.objects.filter(seller=seller, vat_country__iso_code=iso).first()
                    if not sv:
                        continue

                    debug and print(f"\n📤 Procesando MigrationInfo para país {iso}")
                    previous, _ = PreviousAccountingManager.objects.get_or_create(seller=seller, seller_vat=sv)
                    previous.previous_manager_end_date = block.get("previous_manager_end_date")
                    previous.previous_accounting_filed = block.get("previous_accounting_filed")
                    previous.current_accounting_filed_date = block.get("current_accounting_filed_date")
                    previous.previous_manager_name = block.get("previous_manager_name")

                    # 🏠 Dirección del gestor anterior
                    addr_data = {
                        "address": block.get("address"),
                        "address_zip": block.get("address_zip"),
                        "address_city": block.get("address_city"),
                        "address_state": block.get("address_state"),
                        "address_country": block.get("address_country"),
                    }
                    if any(addr_data.values()):  # Solo si hay datos
                        addr_instance = previous.previous_manager_address or Address()
                        for field in ["address", "address_zip", "address_city", "address_state", "address_country"]:
                            value = addr_data.get(field)
                            if field == "address_country" and value:
                                value = Country.objects.filter(iso_code=value).first()
                            setattr(addr_instance, field, value)
                        addr_instance.save()
                        previous.previous_manager_address = addr_instance
                        debug and print("🏠 Dirección gestor anterior guardada")

                    previous.save()
                    sv.vat_frequency = block.get("vat_frequency")
                    sv.save()
                    debug and print(f"✅ Migration info guardada para {iso}")

            # 🔹 Guardado de documentos
            debug and print("\n📎 Guardando documentos...")

            seller_vats_with_maintenance = SellerVat.objects.filter(
                seller=seller,
                contracting_date__isnull=False,
                maintenance_type__isnull=False
            )
            iso_country_codes = list(seller_vats_with_maintenance.values_list("vat_country__iso_code", flat=True))
            for iso in iso_country_codes:
                if iso == "DE":
                    get_or_create_document_type("DE-CURRENTVATDOCS-1", "Alemania", " #1")
                    get_or_create_document_type("DE-CURRENTVATDOCS-2", "Alemania", " #2")
                else:
                    get_or_create_document_type(f"{iso}-CURRENTVATDOCS", iso)

            field_to_doc_type = generate_document_field_mapping(iso_country_codes)["full_mapping"]

            for block_key, block_data in updated_iva_form.items():
                if block_key.startswith(("company_info_form", "migration_info_", "documents_by_country_")):
                    for field_name, field_content in block_data.items():
                        if not isinstance(field_content, dict):
                            continue
                        uploaded_file = request.FILES.get(field_name)
                        doc_code = field_to_doc_type.get(field_name)
                        if not doc_code:
                            continue
                        doc_type = DocumentType.objects.filter(code=doc_code).first()
                        if not doc_type:
                            continue

                        iso_code = None
                        if "migration_info_" in block_key or "documents_by_country_" in block_key:
                            iso_code = block_key.split("_")[-1]
                        seller_vat = SellerVat.objects.filter(seller=seller, vat_country__iso_code=iso_code).first() if iso_code else None

                        if uploaded_file:
                            document, _ = Document.objects.update_or_create(
                                seller=seller,
                                sellerVat=seller_vat,
                                documentType=doc_type,
                                defaults={"file": uploaded_file}
                            )
                            field_content["file"] = document.file.url
                            field_content["value"] = uploaded_file.name
                            debug and print(f"📄 Documento actualizado: {uploaded_file.name} ({field_name})")
                        else:
                            set_document_file_info(field_content, seller, seller_vat, doc_type)

            # 🔹 Datos generales del Seller
            debug and print("\n💾 Guardando datos generales de Seller...")

            company_info_form = updated_iva_form.get("company_info_form", {})
            editable_fields = ["activity_type", "desc_main_activity", "products_and_services"]

            for field in editable_fields:
                value = company_info_form.get(field)
                if field == "products_and_services":
                    if value:
                        ps_instance = ProductService.objects.filter(code=value).first()
                        if ps_instance:
                            seller.products_and_services = ps_instance
                            debug and print(f"✔️ Producto/Servicio asignado: {ps_instance}")
                        else:
                            debug and print(f"❌ Producto/Servicio no encontrado: {value}")
                    else:
                        seller.products_and_services = None
                else:
                    setattr(seller, field, value or "")
                    debug and print(f"✏️ Campo {field}: {value}")

            seller.save()
            debug and print("✅ Seller actualizado correctamente.")

            # 🔹 Guardar JSON final
            processed_form.json_form = json.dumps({"iva_form": updated_iva_form}, default=str)
            processed_form.is_form_processed = True
            processed_form.is_partial_opening = False

            # Marcar como enviados todos los países contratados con mantenimiento
            for vat in seller_vats_with_maintenance:
                vat.form_submitted = True
                vat.save()
                debug and print(f"📦 País {vat.vat_country.iso_code} marcado como enviado (form_submitted=True)")

            # 🔁 Actualizar los errores marcados como incorrectos para poner pending=False
            if processed_form.manager_validation:
                try:
                    validation_data = json.loads(processed_form.manager_validation)
                    for block_key, fields in validation_data.items():
                        for field_id, field_data in fields.items():
                            if field_data.get("status") == "incorrecto" and field_data.get("pending") is True:
                                field_data["pending"] = False
                    processed_form.manager_validation = json.dumps(validation_data)
                    debug and print("🔧 Campos con errores marcados como pendientes actualizados (pending=False).")
                except Exception as e:
                    debug and print(f"⚠️ Error al actualizar pending=False en validaciones: {e}")

            processed_form.save()

            debug and print("\n📝 JSON final guardado en ProcessedForm.")
            debug and print(json.dumps({"iva_form": updated_iva_form}, indent=4, ensure_ascii=False))

            request.session["form_success"] = True
            return JsonResponse({"success": True, "message": "Formulario final enviado correctamente."})

        except Exception as e:
            debug and print(f"💥 Error en FinalSubmitIVAView: {e}")
            logger.error(f"Error en FinalSubmitIVAView: {e}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)

class VatFormSuccessView(LoginRequiredMixin, IsSellerShortnamePermission, TemplateView):
    template_name = "sellers/include/service_iva/iva_form_success.html"

    def dispatch(self, request, *args, **kwargs):
        if not request.session.get("form_success"):
            # Opcional: puedes redirigir al formulario en lugar de error
            raise Http404("Acceso no autorizado a esta página.")
            # return redirect("app_sellers:vat_request_service", shortname=kwargs["shortname"])

        # Una vez mostrado, se elimina la marca para evitar recargas posteriores
        del request.session["form_success"]
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context["seller"] = seller
        return context

##### Vistas del apartado del manager #####

# Vista principal del manager - Validar el formulario IVA
class ManagerReviewView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    template_name = "sellers/seller_vat_review_manager_service_iva.html"
    model = SellerVat
    context_object_name = "sellervat"
    form_class = SellerVatForm  # aunque no edites, puedes mantenerlo por coherencia

    def get_object(self, queryset=None):
        shortname = self.kwargs.get("shortname")
        seller_vat = SellerVat.objects.filter(
            seller__shortname=shortname,
            contracting_date__isnull=False
        ).first()
    
        if not seller_vat:
            return None  # Permite manejar casos sin SellerVat

        return seller_vat
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_staff:
            raise PermissionDenied("No tienes permiso para acceder a esta página.")
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        seller_vats = SellerVat.objects.filter(
            seller=seller,
            contracting_date__isnull=False,
            maintenance_type__isnull=False
        )

        # Diccionario de nombre de países por ISO
        country_names = {
            vat.vat_country.iso_code: vat.vat_country.name for vat in seller_vats
        }

        # Diccionario de formularios por país
        seller_vat_form = {
            vat.vat_country.iso_code: SellerVatForm(instance=vat)
            for vat in seller_vats
        }

        # Diccionario tipos de mantenimiento por pais
        maintenance_map = {
            vat.vat_country.iso_code: vat.maintenance_type
            for vat in seller_vats
        }

        # Filtrar países con `maintenance_type="maintenance"`
        seller_vats_maintenance = seller_vats.filter(maintenance_type="maintenance")

        document_countries_all = list(seller_vats.values_list("vat_country__iso_code", flat=True))
        submitted_countries = [
            vat.vat_country.iso_code
            for vat in seller_vats
            if vat.form_submitted
        ]
        list_iso_document_countries = list(set(document_countries_all) & set(submitted_countries))  # solo documentos de enviados
        debug and print(f'document_countries {list_iso_document_countries}')

        # Todos los países contratados
        contracted_countries = list(seller_vats.values_list("vat_country__iso_code", flat=True))
        debug and print(f'contracted_countries {contracted_countries}')
        # Países solo mantenimiento (ya lo tienes)
        list_iso_migration_countries = list(seller_vats_maintenance.values_list("vat_country__iso_code", flat=True))
        debug and print(f'migration_countries {list_iso_migration_countries}')
        # Países alta + mantenimiento = contratados - solo mantenimiento
        list_iso_setup_and_maintenance_countries = list(set(contracted_countries) - set(list_iso_migration_countries))
        debug and print(f'setup_and_maintenance_countries {list_iso_setup_and_maintenance_countries}')

        category_form = TypeForm.objects.get(code="iva_form")
        processed_form = ProcessedForm.objects.filter(seller=seller, category_form=category_form).first()
        json_data = json.loads(processed_form.json_form or "{}") if processed_form and processed_form.json_form else {}
        iva_form = json_data.get("iva_form", {})
        manager_validation_data = json.loads(processed_form.manager_validation or "{}")

        use_json_data = bool(processed_form and not processed_form.is_form_processed)
        json_company = iva_form.get("company_info_form", {}) if use_json_data else {}

        # Campos manuales fijos
        company_info_data = {
            "name": seller.name,
            "legal_entity": seller.legal_entity,
            "seller_address": seller.seller_address.address if seller.seller_address else "",
            "phone": seller.phone,
        }

        editable_fields = ["activity_type", "desc_main_activity", "products_and_services"]
        for field in editable_fields:
            json_value = json_company.get(field, "")
            db_value = getattr(seller, field, "")
            if field == "products_and_services":
                db_value = f"{db_value.code} - {db_value.description}" if db_value else ""
            company_info_data[field] = json_value or db_value if use_json_data else db_value or json_value

        # Documentos
        doc_mapping = generate_document_field_mapping()["company_info"]
        company_info_data.update(get_documents_block_data(doc_mapping, seller))

        # Etiquetas legibles
        field_list = [
            # Campos generales
            ("name", "Nombre"),
            ("legal_entity", "Tipo de Entidad"),
            ("seller_address", "Dirección"),
            ("phone", "Teléfono"),
            ("activity_type", "Tipo de Actividad"),
            ("desc_main_activity", "Descripción de la actividad principal"),
            ("products_and_services", "Producto o Servicio principal"),
            ("mercantile_registry", "Certificado Mercantil"),
            ("business_deeds", "Escrituras de Constitución"),
            ("business_registry", "Registro Comercial"),
            ("amazon_vies_screenshot", "Captura de Amazon VIES"),
            ("comparison_certificate", "Certificado Comparativo"),
            # Campos de migración
            ("previous_manager_end_date", "Fecha de baja con el gestor anterior"),
            ("previous_accounting_filed", "¿Contabilidad del año anterior presentada?"),
            ("current_accounting_filed_date", "Fecha hasta la que se presentó contabilidad actual"),
            ("vat_frequency", "Frecuencia de presentación del IVA"),
            ("previous_manager_name", "Nombre del antiguo gestor"),
            ("address", "Dirección del gestor anterior"),
            ("address_city", "Ciudad"),
            ("address_state", "Provincia"),
            ("address_zip", "Código postal"),
            ("address_country", "País"),
            ("france_old_manager_letter", "Carta de desvinculación (Francia)"),
        ]

        field_labels = {k: v for k, v in field_list}

        # Agregar campos de documentos dinámicos
        document_field_mapping = generate_document_field_mapping(contracted_countries)["country_documents"]

        # Consultar descripciones desde DocumentType (evita acceder una por una)
        codes_to_fetch = list(document_field_mapping.values())
        descriptions_map = {
            doc_type.code: doc_type.description
            for doc_type in DocumentType.objects.filter(code__in=codes_to_fetch)
        }

        # Agregar al diccionario final usando el nombre del campo del formulario como clave
        for field_name, doc_code in document_field_mapping.items():
            field_labels[field_name] = descriptions_map.get(doc_code, doc_code)  # fallback al código si no hay descripción

        visible_fields = get_visible_fields_by_block(iva_form, contracted_countries)
        non_validated_fields = ["name", "legal_entity", "seller_address", "phone"]
        block_key = "company_info_form"

        # Generar la estructura agrupada y legible para el template
        company_info_review_data = []
        for field_id in visible_fields.get(block_key, []):
            val = company_info_data.get(field_id, "")
            value = val.get("value") if isinstance(val, dict) else val
            file = val.get("file") if isinstance(val, dict) else ""
            field_type = "readonly" if field_id in non_validated_fields else ("document" if file else "editable")

            company_info_review_data.append({
                "field_id": field_id,
                "label": field_labels.get(field_id, field_id.replace("_", " ").title()),
                "value": value or "",
                "file": file or "",
                "type": field_type,
                "full_id": f"{block_key}-{field_id}"
            })

        # === BLOQUE DE SOCIOS: Partners ===
        partners_from_model = Partner.objects.filter(seller=seller).values(
            "id", "name", "last_name", "id_number", "id_passport", "start_date", "end_date",
            "shares_percentage", "address__address", "address__address_zip", "address__address_city", 
            "address__address_state", "address__address_country__name"
        )

        # Mapeo de confirmación desde el JSON
        confirmation_map = {
            p.get("partner_id"): p.get("confirmation_status", False)
            for p in iva_form.get("partners", [])
        }

        # 🔥 Limpiar validaciones de socios dados de baja
        partner_validation_data = manager_validation_data.get("partner", {})

        confirmed_resigned_ids = [
            str(p["id"]) for p in partners_from_model
            if confirmation_map.get(p["id"], False) and p["end_date"]
        ]

        # Eliminar entradas del JSON de socios dados de baja
        for pid in confirmed_resigned_ids:
            keys_to_delete = [k for k in partner_validation_data if k.startswith(f"{pid}-")]
            for k in keys_to_delete:
                del partner_validation_data[k]

        # Actualizar el manager_validation_data limpio
        manager_validation_data["partner"] = partner_validation_data

        # Documentos por socio
        partners_review_data = []
        for p in partners_from_model:
            partner_id = p["id"]
            confirmation_status = confirmation_map.get(partner_id, False)

            is_confirmed_resigned = confirmation_status and p["end_date"]

            dni_doc = Document.objects.filter(seller=seller, partner_id=partner_id, documentType__code="DOC-DNI").first()
            passport_doc = Document.objects.filter(seller=seller, partner_id=partner_id, documentType__code="DOC-Passport").first()

            # Dirección completa
            address_str = ", ".join(filter(None, [
                p["address__address"],
                f'{p["address__address_zip"]} {p["address__address_city"]}',
                p["address__address_state"],
                p["address__address_country__name"]
            ]))

            fields = [
                {"field_id": "name", "label": "Nombre", "value": p["name"], "file": "", "type": "editable", "full_id": f"partner-{partner_id}-name"},
                {"field_id": "last_name", "label": "Apellidos", "value": p["last_name"], "file": "", "type": "editable", "full_id": f"partner-{partner_id}-last_name"},
                {"field_id": "id_number_dni", "label": "DNI / Número de Identificación", "value": p["id_number"], "file": "", "type": "editable", "full_id": f"partner-{partner_id}-id_number_dni"},
                {"field_id": "id_number_passport", "label": "Pasaporte", "value": p["id_passport"], "file": "", "type": "editable", "full_id": f"partner-{partner_id}-id_number_passport"},
                {"field_id": "shares_percentage", "label": "Porcentaje de Participación", "value": p["shares_percentage"], "file": "", "type": "editable", "full_id": f"partner-{partner_id}-shares_percentage"},
                {"field_id": "start_date", "label": "Fecha de Alta", "value": p["start_date"].isoformat() if p["start_date"] else "", "file": "", "type": "editable", "full_id": f"partner-{partner_id}-start_date"},
                {"field_id": "end_date", "label": "Fecha de Baja", "value": p["end_date"].isoformat() if p["end_date"] else "", "file": "", "type": "editable", "full_id": f"partner-{partner_id}-end_date"},
                {"field_id": "dni_file", "label": "Documento de Identidad (DNI)", "value": dni_doc.file.name.split("/")[-1] if dni_doc else "", "file": dni_doc.file.url if dni_doc else "", "type": "document", "full_id": f"partner-{partner_id}-dni_file"},
                {"field_id": "passport_file", "label": "Pasaporte", "value": passport_doc.file.name.split("/")[-1] if passport_doc else "", "file": passport_doc.file.url if passport_doc else "", "type": "document", "full_id": f"partner-{partner_id}-passport_file"},
            ]

            # Construir info_text para el modal general (excluye documentos)
            info_text = []
            for f in fields:
                if f["type"] != "document" and f["value"]:
                    info_text.append(f"{f['label']}: {f['value']}")
            if address_str:
                info_text.append(f"Dirección: {address_str}")

            partner_full_id = f"partner-{partner_id}-full"
            partner_validation = manager_validation_data.get(partner_full_id, {})

            # Inyectar validación en cada campo
            for f in fields:
                f["validation"] = manager_validation_data.get(f["full_id"], {})

            partners_review_data.append({
                "id": partner_id,
                "name": p["name"],
                "last_name": p["last_name"],
                "confirmation_status": confirmation_status,
                "end_date": p["end_date"],
                "fields": fields,
                "address": {
                    "address": p["address__address"],
                    "address_zip": p["address__address_zip"],
                    "address_city": p["address__address_city"],
                    "address_state": p["address__address_state"],
                    "address_country_name": p["address__address_country__name"],
                },
                "info_text": "\n".join(info_text),  # Para el modal general
                "validation": partner_validation,  # ✅ Agregado para ícono del socio completo
                "readonly": is_confirmed_resigned,
            })

        # === BLOQUES POR PAÍS: DOCUMENTOS Y MIGRACIÓN ===
        documents_by_country_data = {}
        migration_info_data = {}

        mapping = generate_document_field_mapping(contracted_countries)
        country_documents_mapping = mapping["country_documents"]
        migration_documents_mapping = mapping["migration"]

        visible_fields = get_visible_fields_by_block(iva_form, contracted_countries)
        
        # Procesar cada país contratado
        for vat in seller_vats:
            iso = vat.vat_country.iso_code
            block_key_docs = f"documents_by_country_{iso}"
            block_key_mig = f"migration_info_{iso}"

            # DOCUMENTOS POR PAÍS
            visible_doc_fields = visible_fields.get(block_key_docs, [])
            doc_fields = {k: v for k, v in country_documents_mapping.items() if k in visible_doc_fields}
            doc_values = get_documents_block_data(doc_fields, seller, seller_vat=vat)

            documents_by_country_data[block_key_docs] = []
            for field_id in visible_doc_fields:
                val = doc_values.get(field_id, {})
                full_id = f"{block_key_docs}-{field_id}"
                documents_by_country_data[block_key_docs].append({
                    "field_id": field_id,
                    "label": field_labels.get(field_id, field_id.replace("_", " ").title()),
                    "value": val.get("value", ""),
                    "file": val.get("file", ""),
                    "type": "document",
                    "full_id": full_id,
                    "validation": manager_validation_data.get(full_id, {}),
                })

            # MIGRACIÓN POR PAÍS (solo maintenance)
            if vat.maintenance_type.code == "maintenance":
                migration_data = iva_form.get(block_key_mig, {})
                visible_mig_fields = visible_fields.get(block_key_mig, [])

                # OJO: si no hay campos visibles, skip
                if not visible_mig_fields:
                    debug and print(f"[{iso}] No hay campos visibles para migración.")
                    continue

                migration_doc_fields = {
                    k: v for k, v in migration_documents_mapping.items()
                    if k in visible_mig_fields
                }
                migration_docs_values = get_documents_block_data(migration_doc_fields, seller, seller_vat=vat)

                migration_info_data[block_key_mig] = []
                for field_id in visible_mig_fields:
                    raw_val = migration_data.get(field_id, "")
                    doc_val = migration_docs_values.get(field_id, {})

                    # Prioridad: valor del documento, luego del JSON
                    raw_value = doc_val.get("value") or raw_val
                    file = doc_val.get("file") or ""
                    field_type = "document" if file else "editable"

                    # Convertir "True"/"False" strings a bool y luego a "Sí"/"No"
                    if raw_value in ["True", "False"]:
                        raw_value = raw_value == "True"
                    if isinstance(raw_value, bool):
                        raw_value = "Sí" if raw_value else "No"

                    # Agrupar dirección si es el campo principal
                    if field_id == "address":
                        raw_value = ", ".join(filter(None, [
                            migration_data.get("address", ""),
                            migration_data.get("address_zip", ""),
                            migration_data.get("address_city", ""),
                            migration_data.get("address_state", ""),
                            migration_data.get("address_country", ""),
                        ]))

                    # Omitir subcampos de dirección
                    if field_id in ["address_zip", "address_city", "address_state", "address_country"]:
                        continue

                    full_id = f"{block_key_mig}-{field_id}"  # ✅ definir antes de usar
                    migration_info_data[block_key_mig].append({
                        "field_id": field_id,
                        "label": field_labels.get(field_id, field_id.replace("_", " ").title()),
                        "value": raw_value or "",
                        "file": file,
                        "type": field_type,
                        "full_id": full_id,
                        "validation": manager_validation_data.get(full_id, {}),
                    })

        debug and print(f"migration_info_data {migration_info_data}")


        # Inyectar validación en campos de company_info
        for field in company_info_review_data:
            full_id = field["full_id"]
            field["validation"] = manager_validation_data.get("company_info_form", {}).get(field["field_id"], {})

        # Inyectar validación en socios (campos individuales)
        for partner in partners_review_data:
            for field in partner["fields"]:
                full_id = field["full_id"].replace("partner-", "")
                field["validation"] = manager_validation_data.get("partner", {}).get(full_id, {})
            # Validación del socio completo (modal general)
            full_validation_id = f"{partner['id']}-full"
            partner["validation"] = manager_validation_data.get("partner", {}).get(full_validation_id, {})
        # Ordenar socios según relevancia u confirmacion de estado
        partners_review_data.sort(
            key=lambda p: (
                not (p["confirmation_status"] and not p["end_date"]),  # Los confirmados activos primero
                p["fields"][5]["value"] or "",  # start_date (puede estar vacío)
                p["id"]  # Desempate por ID
            )
        )

        # Inyectar validación en documentos por país
        for block_key_docs, doc_list in documents_by_country_data.items():
            for field in doc_list:
                block = block_key_docs
                field["validation"] = manager_validation_data.get(block, {}).get(field["field_id"], {})

        # Inyectar validación en migración por país
        for block_key_mig, mig_list in migration_info_data.items():
            for field in mig_list:
                block = block_key_mig
                field["validation"] = manager_validation_data.get(block, {}).get(field["field_id"], {})

        debug and print(f'##################### - > manager_validation {manager_validation_data }')
        
        # === 🟡🔴 Resumen de estado por país para la tabla ===
        countries_errors_info = {}

        for vat in seller_vats:
            iso = vat.vat_country.iso_code
            block_docs = f"documents_by_country_{iso}"
            block_mig = f"migration_info_{iso}"

            # Documentos y migración pueden no existir aún
            doc_fields = manager_validation_data.get(block_docs, {})
            mig_fields = manager_validation_data.get(block_mig, {})

            doc_errors = sum(1 for f in doc_fields.values() if f.get("status") == "incorrecto" and f.get("pending") is True)
            mig_errors = sum(1 for f in mig_fields.values() if f.get("status") == "incorrecto" and f.get("pending") is True)

            doc_warnings = sum(1 for f in doc_fields.values() if f.get("status") == "incorrecto" and f.get("pending") is False)
            mig_warnings = sum(1 for f in mig_fields.values() if f.get("status") == "incorrecto" and f.get("pending") is False)

            if doc_errors or mig_errors or doc_warnings or mig_warnings:
                messages = []
                if doc_errors:
                    messages.append(f"{doc_errors} error/es en documentación")
                if mig_errors:
                    messages.append(f"{mig_errors} error/es en migración")
                if doc_warnings:
                    messages.append(f"{doc_warnings} corregido/s en documentación")
                if mig_warnings:
                    messages.append(f"{mig_warnings} corregido/s en migración")

                if doc_errors or mig_errors:
                    color = "danger"
                    label = "Errores"
                elif (doc_warnings or mig_warnings) and iso in submitted_countries:
                    color = "warning"
                    label = "Corregido"
                else:
                    color = ""
                    label = ""

                countries_errors_info[iso] = {
                    "color": color,
                    "label": label,
                    "messages": messages
                }
        # Agrupar campos activity_type + products_and_services
        activity_block = {}
        activity_type_field = next((f for f in company_info_review_data if f["field_id"] == "activity_type"), None)
        product_field = next((f for f in company_info_review_data if f["field_id"] == "products_and_services"), None)

        if activity_type_field and product_field:
            activity_block = {
                "activity_type": activity_type_field,
                "products_and_services": product_field,
                "validation": activity_type_field.get("validation", {}),
                "full_id": activity_type_field["full_id"],
            }

        # Elimina los campos duplicados del listado principal
        company_info_review_data = [
            f for f in company_info_review_data
            if f["field_id"] not in ["activity_type", "products_and_services"]
        ]

        # === 🟡🔴 Resumen de estado por bloque general (info empresa y socios) ===
        general_tabs_errors_info = {}

        # 1. Información General (company_info_form)
        company_errors = sum(
            1 for f in manager_validation_data.get("company_info_form", {}).values()
            if f.get("status") == "incorrecto" and f.get("pending") is True
        )
        company_warnings = sum(
            1 for f in manager_validation_data.get("company_info_form", {}).values()
            if f.get("status") == "incorrecto" and f.get("pending") is False
        )

        if company_errors or company_warnings:
            messages = []
            if company_errors:
                messages.append(f"{company_errors} error/es en datos de empresa")
            if company_warnings:
                messages.append(f"{company_warnings} corregido/s en datos de empresa")
            general_tabs_errors_info["company_info_form"] = {
                "color": "danger" if company_errors else "warning",
                "label": "Errores" if company_errors else "Corregido",
                "messages": messages,
            }

        # 2. Socios (partner)
        partner_validation = manager_validation_data.get("partner", {})
        partner_errors = sum(
            1 for f in partner_validation.values()
            if f.get("status") == "incorrecto" and f.get("pending") is True
        )
        partner_warnings = sum(
            1 for f in partner_validation.values()
            if f.get("status") == "incorrecto" and f.get("pending") is False
        )

        if partner_errors or partner_warnings:
            messages = []
            if partner_errors:
                messages.append(f"{partner_errors} error/es en socios")
            if partner_warnings:
                messages.append(f"{partner_warnings} corregido/s en socios")
            general_tabs_errors_info["partner"] = {
                "color": "danger" if partner_errors else "warning",
                "label": "Errores" if partner_errors else "Corregido",
                "messages": messages,
            }

        # Detectar países contratados que aún no tienen datos en iva_form
        processed_countries = [
            key.split("_")[-1]
            for key in iva_form.keys()
            if key.startswith("documents_by_country_") or key.startswith("migration_info_")
        ]

        new_contracted_countries = list(set(contracted_countries) - set(processed_countries))
        debug and print(f"📘 Países nuevos contratados pero aún no procesados: {new_contracted_countries}")

        context.update({
            # --- Bloque General ---
            "seller": seller,  # Instancia del Seller actual
            "seller_vat_form": seller_vat_form,  # Diccionario {ISO: Formulario SellerVatForm para ese país}
            "submitted_countries": submitted_countries,  # Lista de países cuyo formulario ya fue enviado (form_submitted=True)
            "country_names": country_names,  # Diccionario {ISO: Nombre del país}
            "contracted_countries": contracted_countries,  # Lista de códigos ISO de países contratados (SellerVat)
            "new_contracted_countries": new_contracted_countries,  # Lista de países contratados pero no procesados
            "maintenance_type_map": maintenance_map,  # tipo de mantenimiento por país
            # --- Bloque Info Empresa ---
            "company_info_review_data": company_info_review_data,  # Lista de dicts con los campos de info general (bloque empresa)
            "activity_block": activity_block,
            "contracted_seller_vats": seller_vats,  # Queryset con los SellerVat contratados (con contracting_date y maintenance_type)
            # --- Bloque Socios ---
            #"partners_formatted": partners_formatted,  # Bloque de revisión de socios
            "partners_review_data": partners_review_data,
            "info_text": info_text,
            # --- Bloque Doc y Migracion ---
            "documents_by_country_data": documents_by_country_data,  # Diccionario {documents_by_country_{ISO}: lista de dicts con documentos por país}
            "migration_info_data": migration_info_data,  # Diccionario {migration_info_{ISO}: lista de dicts con datos y docs de migración por país}
            "document_countries": list(documents_by_country_data.keys()),  # Lista de claves tipo 'documents_by_country_DE', etc.
            "list_iso_document_countries": list_iso_document_countries, # Liata de iso_codes de paises con procesados con documentos adicionales
            "migration_countries": list(migration_info_data.keys()),  # Lista de claves tipo 'migration_info_FR', etc.
            "list_iso_migration_countries": list_iso_migration_countries, # Liata de iso_codes de paises con solo mantenimiento
            "list_iso_setup_and_maintenance_countries": list_iso_setup_and_maintenance_countries,  # # Liata de iso_codes de paises con alta nueva (no requiere migración)
            "has_only_maintenance": seller_vats.filter(maintenance_type="maintenance").exists(),  # Booleano: ¿existen países solo con mantenimiento?
            # --- Recuperar validaciones pendientes ---
            "manager_validation_data": manager_validation_data,
            "countries_errors_info": countries_errors_info,
            "general_tabs_errors_info": general_tabs_errors_info,
        })

        return context

    # Maneja acciones GET dinámicas como refrescar bloques específicos por 'action'.
    def get(self, request, *args, **kwargs):
        action = request.GET.get("action")
        print(f"🔎 Acción GET recibida: {action}")

        if action == "get_invalid_fields":
            return self.get_invalid_fields_response(request, *args, **kwargs)

        if action == "get_countries_table":
            return self.get_contracted_countries_table(request)

        if action == "get_company_info_block":
            return self.get_company_info_block(request)

        if action == "get_partners_block":
            return self.get_partners_block(request)

        if request.GET.get("refresh_general_tabs"):
            return self.get_general_tabs_card(request)
        
        if request.GET.get("refresh_country_tabs"):
            return self.get_country_tabs_card(request)

        return super().get(request, *args, **kwargs)

    # Devuelve un resumen JSON con los campos inválidos agrupados por bloque
    def get_invalid_fields_response(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=kwargs["shortname"])
        category_form = get_object_or_404(TypeForm, code="iva_form")
        processed_form = get_object_or_404(ProcessedForm, seller=seller, category_form=category_form)

        manager_validation = json.loads(processed_form.manager_validation or "{}")

        # === Diccionario de etiquetas legibles (bloques base) ===
        readable_labels = {
            "name": "Nombre",
            "legal_entity": "Tipo de Entidad",
            "seller_address": "Dirección",
            "phone": "Teléfono",
            "activity_type": "Tipo de Actividad",
            "desc_main_activity": "Descripción de la actividad principal",
            "products_and_services": "Producto o Servicio principal",
            "mercantile_registry": "Certificado Mercantil",
            "business_deeds": "Escrituras de Constitución",
            "business_registry": "Registro Comercial",
            "amazon_vies_screenshot": "Captura de Amazon VIES",
            "comparison_certificate": "Certificado Comparativo",
            "previous_manager_end_date": "Fecha de baja con el gestor anterior",
            "previous_accounting_filed": "¿Contabilidad del año anterior presentada?",
            "current_accounting_filed_date": "Fecha hasta la que se presentó contabilidad actual",
            "vat_frequency": "Frecuencia de presentación del IVA",
            "previous_manager_name": "Nombre del antiguo gestor",
            "address": "Dirección del gestor anterior",
            "france_old_manager_letter": "Carta de desvinculación (Francia)",
        }

        # === Añadir etiquetas legibles para documentos por país ===
        try:
            seller_vats = SellerVat.objects.filter(seller=seller, contracting_date__isnull=False)
            contracted_countries = list(seller_vats.values_list("vat_country__iso_code", flat=True))

            document_field_mapping = generate_document_field_mapping(contracted_countries)["country_documents"]
            doc_codes = list(document_field_mapping.values())

            # Obtener descripciones desde DocumentType
            descriptions_map = {
                doc.code: doc.description
                for doc in DocumentType.objects.filter(code__in=doc_codes)
            }

            for field_name, doc_code in document_field_mapping.items():
                readable_labels[field_name] = descriptions_map.get(doc_code, doc_code)

        except Exception as e:
            print("⚠️ Error cargando descripciones dinámicas:", e)

        # === Obtener nombres de socios para poder mostrar "Socio: Ana" en vez de "94" ===
        partner_map = {
            str(partner.id): f"{partner.name} {partner.last_name}"
            for partner in Partner.objects.filter(seller=seller)
        }

        # === Etiquetas legibles para campos de socio ===
        partner_field_labels = {
            "full": "Información general del socio",
            "dni_file": "Documento de DNI",
            "passport_file": "Documento de Pasaporte"
        }

        # === Procesar campos inválidos ===
        invalid_fields = {}

        for block_key, fields in manager_validation.items():
            if block_key == "partner":
                partner_group = {}
                for field_id, field_data in fields.items():
                    if (
                        isinstance(field_data, dict)
                        and field_data.get("status") == "incorrecto"
                        and field_data.get("comment", "").strip()
                    ):
                        try:
                            partner_id, field_name = field_id.split("-", 1)
                            partner_label = partner_map.get(partner_id, f"Socio ID {partner_id}")
                            field_label = partner_field_labels.get(field_name, field_name.replace("_", " ").capitalize())
                            partner_group.setdefault(partner_label, []).append(field_label)
                        except ValueError:
                            continue  # En caso de que el field_id no tenga '-'
                if partner_group:
                    invalid_fields["partner"] = partner_group
            else:
                for field_id, field_data in fields.items():
                    if (
                        isinstance(field_data, dict) and
                        field_data.get("status") == "incorrecto" and
                        field_data.get("comment", "").strip()
                    ):
                        label = readable_labels.get(field_id, field_id.replace("_", " ").capitalize())
                        invalid_fields.setdefault(block_key, []).append(label)

        return JsonResponse({
            "status": "ok",
            "invalid_fields": invalid_fields
        })

    # Renderiza solo el bloque de información general (company info)
    def get_company_info_block(self, request):
        print("📦 get_company_info_block llamado")

        self.object = self.get_object()  # 🔥 Esto previene el error
        context = self.get_context_data()

        html = render_to_string(
            "sellers/include/service_iva/manager/company_info_review_block.html",
            context,
            request
        )
        return JsonResponse({"html": html})

    # Renderiza solo el bloque de revisión de socios
    def get_partners_block(self, request):
        print("📦 get_partners_block llamado")

        self.object = self.get_object()
        context = self.get_context_data()

        html = render_to_string(
            "sellers/include/service_iva/manager/partners_review_block.html",
            context,
            request
        )
        return JsonResponse({"html": html})

    # Renderiza la tarjeta completa de pestañas generales (info + socios)
    def get_general_tabs_card(self, request):
        print("📦 get_general_tabs_card llamado")

        self.object = self.get_object()
        context = self.get_context_data()  # Ya contiene company_info_review_data, partners_review_data, etc.

        html = render_to_string(
            "sellers/include/service_iva/manager/review/manager_general_tabs_card.html",
            context,
            request
        )
        return JsonResponse({"html": html})

    # Renderiza la tarjeta con pestañas por país (documentos y migración)
    def get_country_tabs_card(self, request):
        print("📦 get_country_tabs_card llamado")

        self.object = self.get_object()
        context = self.get_context_data()  # 🧠 Usas el contexto que ya está preparado

        html = render_to_string(
            "sellers/include/service_iva/manager/review/country_tabs_card.html",
            context,
            request
        )
        return JsonResponse({"html": html})

    # Renderiza la tabla de países contratados con su estado de validación
    def get_contracted_countries_table(self, request):
        print("📦 get_contracted_countries_table llamado")

        self.object = self.get_object()
        context = self.get_context_data()  # 🧠 Usas el contexto que ya está preparado
        
        html = render_to_string(
            "sellers/include/service_iva/manager/contracted_countries_table.html",
            context,
            request
        )
        return JsonResponse({"html": html})
    
    def post(self, request, *args, **kwargs):
        if request.headers.get("Content-Type") != "application/json":
            return JsonResponse({"status": "error", "message": "Formato inválido"}, status=400)

        data = json.loads(request.body)
        if data.get("action") != "validate_full_form":
            return JsonResponse({"status": "error", "message": "Acción inválida"}, status=400)

        is_forced = data.get("force", False)
        debug and print(f"💡 ¿Validación forzada?: {is_forced}")

        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        category_form = get_object_or_404(TypeForm, code="iva_form")
        processed_form = get_object_or_404(ProcessedForm, seller=seller, category_form=category_form)

        seller_vats = SellerVat.objects.filter(seller=seller)
        manager_validation = json.loads(processed_form.manager_validation or "{}")
        iva_form = json.loads(processed_form.json_form or "{}").get("iva_form", {})

        debug and print(f"\n📥 JSON original de validación recibido: {json.dumps(manager_validation, indent=4, ensure_ascii=False)}")
        debug and print(f"\n📦 JSON del formulario IVA actual: {json.dumps(iva_form, indent=4, ensure_ascii=False)}")

        if is_forced:
            validated_data = {}
            for block_key, fields in iva_form.items():
                if isinstance(fields, dict):
                    validated_data[block_key] = {
                        field_id: {
                            "status": "correcto",
                            "comment": "",
                            "pending": False
                        } for field_id in fields
                    }

            validated_data["partner"] = {}
            for partner in Partner.objects.filter(seller=seller):
                pid = str(partner.id)
                for suffix in ["full", "dni_file", "passport_file"]:
                    full_key = f"{pid}-{suffix}"
                    validated_data["partner"][full_key] = {
                        "status": "correcto",
                        "comment": "",
                        "pending": False
                    }

            processed_form.manager_validation = json.dumps(validated_data)
            processed_form.is_form_processed = True
            processed_form.is_partial_opening = False

            for vat in seller_vats:
                vat.form_submitted = True
                vat.save()

            processed_form.save()

            debug and print("✅ Validación forzada: todo marcado como correcto y enviado")

            send_validation_notification(
                seller.user.email,
                has_errors=False,
                countries=[v.vat_country.iso_code for v in seller_vats]
            )

            return JsonResponse({
                "status": "ok",
                "message": "Validación forzada: todos los campos se marcaron como correctos.",
                "reopened": False,
                "invalid_fields": {},
            })

        validated_data = {}
        invalid_by_block = {}

        # Paso 1: Validar todos los bloques estándar (empresa, migración, docs por país)
        for block_key, fields in iva_form.items():
            if not isinstance(fields, dict):
                continue

            validated_data[block_key] = {}

            for field_id in fields:
                existing_validation = manager_validation.get(block_key, {}).get(field_id, {})
                status = existing_validation.get("status", "correcto")
                comment = existing_validation.get("comment", "")
                pending = status == "incorrecto"

                validated_data[block_key][field_id] = {
                    "status": status,
                    "comment": comment,
                    "pending": pending
                }

                if pending:
                    invalid_by_block.setdefault(block_key, []).append(field_id)

        # Paso 2: Validar socios
        partners = Partner.objects.filter(seller=seller)
        validated_data["partner"] = validated_data.get("partner", {})
        partner_validation = manager_validation.get("partner", {})

        for partner in partners:
            pid = str(partner.id)
            for suffix in ["full", "dni_file", "passport_file"]:
                full_key = f"{pid}-{suffix}"
                existing = partner_validation.get(full_key, {})
                status = existing.get("status", "correcto")
                comment = existing.get("comment", "")
                pending = status == "incorrecto"

                validated_data["partner"][full_key] = {
                    "status": status,
                    "comment": comment,
                    "pending": pending
                }

                if pending:
                    invalid_by_block.setdefault("partner", []).append(full_key)

        debug and print("\n✅ Validated data FINAL (todo incluido):")
        debug and print(json.dumps(validated_data, indent=4, ensure_ascii=False))
        debug and print("\n⚠️ Campos incorrectos por bloque:")
        debug and print(json.dumps(invalid_by_block, indent=4, ensure_ascii=False))

        # Guardar validaciones normalizadas
        processed_form.manager_validation = json.dumps(validated_data)

        # Paso 3: Evaluar errores
        has_general_errors = any(
            (block.startswith("company_info_form") or block.startswith("partner"))
            for block in invalid_by_block
        ) # Determinar bloques con errores generales

        countries_with_errors = [
            iso for iso in seller_vats.values_list("vat_country__iso_code", flat=True)
            if f"documents_by_country_{iso}" in invalid_by_block or f"migration_info_{iso}" in invalid_by_block
        ] # Determinar bloques con errores por país

        debug and print(f"\n🔍 Errores generales: {has_general_errors}")
        debug and print(f"🌍 Países con errores: {countries_with_errors}")

        # Obtener lista de países procesados en el formulario (es decir, con bloques en el JSON)
        processed_countries = [
            key.split("_")[-1]
            for key in iva_form.keys()
            if key.startswith("documents_by_country_") or key.startswith("migration_info_")
        ]
        valid_countries = set(processed_countries) - set(countries_with_errors)

        # Paso 4: Actualizar estado
        if not has_general_errors and not countries_with_errors:
            debug and print("🎉 Todo válido → formulario procesado completamente")
            processed_form.is_form_processed = True
            processed_form.is_partial_opening = False

            for vat in seller_vats:
                iso = vat.vat_country.iso_code
                if iso in valid_countries:
                    vat.form_submitted = True
                    vat.save()
        else:
            debug and print("🔁 Errores encontrados → se reabre total o parcialmente")
            processed_form.is_form_processed = False
            processed_form.is_partial_opening = True

            for vat in seller_vats:
                iso = vat.vat_country.iso_code
                if iso in processed_countries:
                    vat.form_submitted = iso not in countries_with_errors
                    vat.save()

        debug and print("📌 FINAL - Evaluación:")
        debug and print(f"is_form_processed: {processed_form.is_form_processed}")
        debug and print(f"is_partial_opening: {processed_form.is_partial_opening}")
        debug and print(f"invalid_by_block: {json.dumps(invalid_by_block, indent=2)}")

        processed_form.save()

        # Paso 5: Notificación
        send_validation_notification(
            seller.user.email,
            has_errors=bool(invalid_by_block),
            countries=[v.vat_country.iso_code for v in seller_vats]
        )

        return JsonResponse({
            "status": "ok",
            "message": "El formulario ha sido validado y enviado. El cliente ha sido notificado por correo electrónico.",
            "reopened": bool(invalid_by_block),
            "invalid_fields": invalid_by_block
        })
    
class ManagerFieldValidationView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def post(self, request, shortname):
        try:
            data = json.loads(request.body)
            field_id = data.get("field_id")
            validation_status = data.get("validation_status")
            comment = data.get("comment", "")

            if not field_id or validation_status not in ["correcto", "incorrecto"]:
                return JsonResponse({"error": "Datos inválidos."}, status=400)

            seller = Seller.objects.get(shortname=shortname)
            processed_form = ProcessedForm.objects.filter(
                seller=seller,
                category_form__code="iva_form"
            ).first()

            if not processed_form:
                return JsonResponse({"error": "Formulario procesado no encontrado."}, status=404)

            manager_validation = json.loads(processed_form.manager_validation or "{}")

            # 🔎 PARTNERS: detectar IDs tipo partner-89-dni_file
            if field_id.startswith("partner-"):
                parts = field_id.split("-")
                if len(parts) >= 3:
                    partner_id = parts[1]
                    field_suffix = "-".join(parts[2:])  # full, dni_file, etc.
                    key = f"{partner_id}-{field_suffix}"
                    manager_validation.setdefault("partner", {})[key] = {
                        "status": validation_status,
                        "comment": comment if validation_status == "incorrecto" else "",
                        "pending": validation_status == "incorrecto"
                    }
                else:
                    return JsonResponse({"error": "ID de campo partner inválido."}, status=400)

            else:
                # Resto de bloques normales
                if "-" not in field_id:
                    return JsonResponse({"error": "ID de campo inválido."}, status=400)
                block_key, field_name = field_id.split("-", 1)
                manager_validation.setdefault(block_key, {})[field_name] = {
                    "status": validation_status,
                    "comment": comment if validation_status == "incorrecto" else "",
                    "pending": validation_status == "incorrecto"
                }

            processed_form.manager_validation = json.dumps(manager_validation, ensure_ascii=False, indent=2)
            processed_form.save()

            debug and print("🔄 JSON actualizado por campo:")
            debug and print(json.dumps(manager_validation, indent=2, ensure_ascii=False))

            return JsonResponse({"success": True})

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

