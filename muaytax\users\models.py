from django.contrib.auth.models import AbstractUser
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, DateTimeField, ForeignKey, SET_NULL
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from muaytax.dictionaries.models import MuaytaxDepartment


class User(AbstractUser):
    """
    Default custom user model for muaytax.
    If adding fields that need to be filled at user signup,
    check forms.SignupForm and forms.SocialSignupForms accordingly.
    """

    #: First and last name do not cover name patterns around the globe
    name = Char<PERSON><PERSON>(
        blank=True, 
        max_length=255,
        verbose_name="Nombre Completo del user",
        help_text = "El nombre completo incluye nombre + apellidos del usuario (persona fisica, no de la empresa)."
    )

    first_name = Char<PERSON>ield(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="Nombre del user",
        help_text = "Nombre de la persona fisica (no de la empresa)."
    )

    last_name = Char<PERSON><PERSON>(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Apellidos del user",
        help_text="Apellidos de la persona fisica (no de la empresa).",
    )

    role = Char<PERSON>ield(
        _("role"),
        max_length=50,
        choices=[
            ("seller", _("seller")),
            ("manager", _("Manager")),
        ],
        default="seller",
    )

    department = ForeignKey(
        MuaytaxDepartment,
        on_delete=SET_NULL,
        null=True,
        blank=True,
        related_name="user_manager_department",
        verbose_name="Departamento",
        help_text="Departamento al que pertenece el usuario. Solo para usuarios de tipo Manager."
    )


    last_activity = DateTimeField(
        blank=True, 
        null=True
    )


    class Meta:
        permissions = [
            ("can_view_all_sellers_list", "Puede ver la lista de todos los vendedores"),
            ("can_view_bookings", "Puede ver citas"),
            ("can_view_management_es", "Puede ver listado gestoría España"),
            ("can_view_management_usa", "Puede ver listado gestoría USA"),
            ("can_view_process", "Puede ver procesos"),
            ("can_view_vat_sp", "Puede ver listado IVA España"),
            ("can_view_vat_de", "Puede ver listado IVA Alemania"),
            ("can_view_vat_fr", "Puede ver listado IVA Francia"),
            ("can_view_vat_it", "Puede ver listado IVA Italia"),
            ("can_view_vat_uk", "Puede ver listado IVA Reino Unido"),
            ("can_view_vat_pl", "Puede ver listado IVA Polonia"),
            ("can_view_vat_cz", "Puede ver listado IVA República Checa"),
            ("is_superuserAPP", "Puede ver la APP como SuperManager")
        ]

    def has_permission_manager(self):
        # check permissions here
        # return True or False
        if self.role == "manager" or self.role == "manager_lite":
            return True

    def get_absolute_url(self):
        """Get url for user's detail view.

        Returns:
            str: URL for user detail.

        """
        return reverse("users:detail", kwargs={"username": self.username})
