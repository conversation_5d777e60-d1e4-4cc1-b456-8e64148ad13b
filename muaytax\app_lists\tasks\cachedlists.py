from celery import shared_task
from muaytax.celery import app
from muaytax.app_lists.utils import update_and_create_seller_cached_lists
from muaytax.app_sellers.models import Seller

@app.task(queue='cache_updates')
def update_cached_seller_signal_task(*args, **kwargs):
    print("\r\033[94m**********Empieza la función en CELERY para actualización de listas**********\033[0m")
    seller_id = kwargs.get('seller_id', None)

    if not seller_id:
        return "\r[SEÑAL DEL SELLER FALLIDA]. No se ha proporcionado un ID de vendedor"

    try:
        year = kwargs.get('year', None)
        seller = Seller.objects.get(id=seller_id)
        update_and_create_seller_cached_lists(seller, year)
    except Exception as e:
        print(f"\r[SEÑAL DEL SELLER FALLIDA]. Error al actualizar las listas de vendedor: {e}")
        return f"\r[SEÑAL DEL SELLER FALLIDA]. Error al actualizar las listas de vendedor: {e}"
    finally:
        print("\r\033[94m**********Termina la función en CELERY para actualización de listas**********\033[0m")
        return True