from django.db import models


class Country(models.Model):
    name = models.Char<PERSON>ield(
        max_length=50,
        verbose_name="Nombre del pais",
    )

    iso_code = models.CharField(
        max_length=2,
        primary_key=True,
        verbose_name="Código del País",
        help_text="Código ISO de 2 letras asignado a ese país o territorio.",
    )

    iso_code_3 = models.CharField(
        max_length=3,
        verbose_name="Código del País en iso code alpha 3",
        help_text="Código ISO de 3 letras asignado a ese país o territorio.",
        null=True, blank=True,
    )

    is_european_union = models.BooleanField(
        default=False,
        verbose_name="¿Es de la Union Europea?",
        help_text="Campo para determinar si el pais pertenece a la Union Europea."
    )

    phoneprefix = models.Char<PERSON>ield(
        max_length=5,
        verbose_name="Prefijo telefónico",
        help_text="Prefijo telefónico internacional del país.",
        null=True, blank=True,
    )

    class Meta:
        verbose_name = "País"
        verbose_name_plural = "Paises"
        ordering = ["name"]

    def __str__(self):
        return self.name


# @admin.register(Country)
# class CountryAdmin(admin.ModelAdmin):
#     list_display = ["name", "iso_code", "phoneprefix", "is_european_union"]
#     search_fields = ["name", "iso_code", "phoneprefix"]
