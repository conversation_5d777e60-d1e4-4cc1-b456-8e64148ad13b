{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Vendedores{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title d-flex align-items-center">
            <h5>
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              {{ object.name }}
              ( {{ object.user.email }} )
            </h5>
          </div>
          <ul class="breadcrumb mt-3">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' object.shortname %}">
                {{ object.name }}
              </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos generales</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="{% url 'app_sellers:detail' object.shortname %}">
        {% csrf_token %}
        {{ form |crispy }}
        <div class="control-group">
          <div class="controls">
            <button type="submit" class="btn btn-primary">Actualizar</button>
          </div>
        </div>
      </form>
    </div>
  </div>
{% endblock content %}
