from dataclasses import dataclass
from muaytax.app_bookings.models import Schedule, Absence
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import QuerySet

@dataclass
class FindBookingSpots:
    booking_type: str
    appointments: QuerySet
    manager: object
    selected_date: object

    def generate15(self, first_working_datetime=None) -> dict:
        """Generates a dictionary with the available appointments for the selected date and manager"""
        schedule = Schedule.objects.get(manager=self.manager)
        buffer_time = schedule.buffer_time
        absences_not_all_day = Absence.objects.filter(manager=self.manager, date=self.selected_date, is_all_day=False).order_by('start_time')
        
        available_appointments = {}
        start_time = schedule.start_time
        end_time = schedule.end_time

        end_time = (datetime.combine(datetime.today(),schedule.end_time) - timedelta(minutes=15)).time()
        if first_working_datetime:
            if first_working_datetime.time() >= schedule.end_time:
                return available_appointments
            else:
                if first_working_datetime.time() > schedule.start_time:
                    start_time = first_working_datetime.time()

        # obtiene un diccionario con los horarios disponibles desde el inicio hasta el final del horario del manager
        while start_time <= end_time:
            time_slot = start_time.strftime('%H:%M')
            available_appointments[time_slot] = True
            start_time = (datetime.combine(datetime.today(), start_time) + timedelta(minutes=15)).time()

        if available_appointments:
            self._check_for_absences(absences_not_all_day, available_appointments)
            self._check_for_lunch_break(schedule, available_appointments)
            self._check_for_previous_appointment(available_appointments, buffer_time)

        return available_appointments
    
    def _check_for_lunch_break(self, schedule: object, available_appointments: dict):
        """
        This method checks if the manager has a lunch break and sets the time slots as unavailable
        """
        if schedule.start_break_time and schedule.end_break_time:
            start_time = (datetime.combine(datetime.today(), schedule.start_break_time)).time()
            end_time = schedule.end_break_time

            while start_time < end_time:
                if start_time.strftime('%H:%M') in available_appointments.keys():
                    available_appointments[start_time.strftime('%H:%M')] = False
                start_time = (datetime.combine(datetime.today(), start_time) + timedelta(minutes=15)).time()

    def _check_for_previous_appointment(self, available_appointments: dict, buffer_time: str):
        """"
        This method gets the list of appointments, then makes and ordered list of the available times
        and checks if the appointment time is in the list of available times, if it is, it sets the time
        as unavailable.
        """
        if not self.appointments:
            return

        available_times = [datetime.strptime(time, '%H:%M').time() for time in available_appointments.keys()]
        available_times.sort()

        for appointment in self.appointments:
            appointment_time = appointment.date.astimezone(timezone.get_current_timezone()).time()

            # Convertir la duración a minutos
            minutes_dict = {
                "1": 15,  # 15 minutos
                "2": 30,  # 30 minutos
                "3": 45,  # 45 minutos
                "4": 60,  # 60 minutos
            }

            duration_minutes = minutes_dict.get(appointment.duration, 15)

            # Obtener el tiempo de espera entre llamadas 
            buffer_wait = minutes_dict.get(buffer_time, 15)

            # Calcular el tiempo de fin de la cita
            end_time = (datetime.combine(datetime.today(), appointment_time) + timedelta(minutes=duration_minutes)).time()

            # Buffer antes de la cita (15 minutos)
            buffer_start = (datetime.combine(datetime.today(), appointment_time) - timedelta(minutes=buffer_wait)).time()

            # Buffer después de la cita (15 minutos)
            buffer_end = (datetime.combine(datetime.today(), appointment_time) + timedelta(minutes=buffer_wait)).time()

            # Si el buffer después de la cita es menor que el tiempo de fin de la cita, lo ajustamos
            if buffer_end <= end_time:
                buffer_end = (datetime.combine(datetime.today(), end_time) + timedelta(minutes=15)).time()

            # Cuando tenemos tiempos de espera entre llamadas de más de 45 minutos o más
            # if buffer_wait >= 45:
            #     calculate_buffer = buffer_wait - 45 #Calcular minutos restantes para la siguiente llamada
            #     buffer_end = (datetime.combine(datetime.today(), buffer_end) + timedelta(minutes=calculate_buffer)).time()

            # Marcar todos los slots dentro del rango como no disponibles
            for time_slot in available_times:
                # Si el slot está dentro del buffer antes, la cita, o el buffer después
                if buffer_start < time_slot < buffer_end:
                    available_appointments[time_slot.strftime('%H:%M')] = False

    def _check_for_absences(self, absences_not_all_day: object, available_appointments: dict):
        if not absences_not_all_day:
            return

        available_times = [datetime.strptime(time, '%H:%M').time() for time in available_appointments.keys()]
        available_times.sort()

        for absence in absences_not_all_day:
            buffer_before_absence = 15 if self.manager in ['emparlerin', 'juandiego', 'webmaster'] else 0
            start_time = (datetime.combine(datetime.today(), absence.start_time) - timedelta(minutes=buffer_before_absence)).time()

            # Mark time slots within the absence time as unavailable
            for time_slot in available_times:
                if start_time <= time_slot < absence.end_time:
                    available_appointments[time_slot.strftime('%H:%M')] = False
