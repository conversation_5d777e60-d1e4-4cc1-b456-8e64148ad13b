from django.contrib import admin
from ..models import *
from django.utils.safestring import mark_safe

class AmortizationEntryAdmin(admin.ModelAdmin):
    list_display = (
        "invoice", 
        "start_date", 
        "total_years", 
        "total_months", 
        "annual_quota", 
        "monthly_quota", 
        "final_monthly_quota", 
        "total_amount", 
        "coefficient_reference", 
        "created_at"
    )
    list_filter = (
        "start_date", 
        "coefficient_reference", 
        "created_at"
    )
    search_fields = (
        "invoice__id", 
        "invoice__invoice_number", 
        "coefficient_reference__name"
    )
    readonly_fields = (
        "schedule", 
        "annual_quota", 
        "monthly_quota", 
        "final_monthly_quota", 
        "total_years", 
        "total_months", 
        "created_at"
    )
    raw_id_fields = ("invoice",)  # Mejora de Performance
    
    fieldsets = (
        ("Información de Amortización", {
            "fields": (
                "invoice",
                "start_date",
                "total_years",
                "total_months",
                "annual_quota",
                "monthly_quota",
                "final_monthly_quota",
                "total_amount",
                "coefficient_reference",
            )
        }),
        ("Tabla de Amortización (Vista Detallada)", {
            "fields": ("schedule",),
            "classes": ("collapse",),
        }),
    )
    
    def schedule(self, obj):
        """Renderiza el schedule como HTML."""
        return mark_safe(obj.schedule)

    schedule.short_description = "Tabla de Amortización"
