from django.db import models

class SellerVatEsStatusEori(models.Model):

    code = models.CharField(
        blank=True,
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )


    class Meta:
        verbose_name = "Estado del EORI para España"
        verbose_name_plural = "Estados de los EORI para España"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatEsStatusEori)
# class SellerVatEsStatusEoriAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
