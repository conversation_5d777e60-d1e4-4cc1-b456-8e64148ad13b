import os
import re

# === CONFIGURACIÓN ===
BASE_DIR = os.path.dirname(__file__)
CSS_FILE = os.path.join(BASE_DIR, "allstyle.css")
OUTPUT_FILE = os.path.join(BASE_DIR, "styleFormIVA.css")

TEMPLATE_PATHS = [
    "muaytax/templates/sellers/seller_vat_request_service_iva.html",
    "muaytax/templates/sellers/seller_vat_review_manager_service_iva.html"
]

INCLUDE_DIR = "muaytax/templates/sellers/include/service_iva"

# Agrega todos los .html dentro del include
for file in os.listdir(INCLUDE_DIR):
    if file.endswith(".html"):
        TEMPLATE_PATHS.append(os.path.join(INCLUDE_DIR, file))


# === FUNCIONES ===

def extract_css_blocks(css_text):
    """
    Extrae bloques CSS del archivo como (selector, bloque completo)
    """
    pattern = r"([.#][\w\-]+)\s*\{[^}]+\}"
    blocks = re.findall(r"([.#][\w\-]+)\s*\{[^}]+\}", css_text)
    block_dict = {}

    for match in re.finditer(r"([.#][\w\-]+)\s*\{([^}]+)\}", css_text):
        selector = match.group(1).strip()
        full_block = match.group(0).strip()
        block_dict[selector] = full_block

    return block_dict


def load_template_contents(paths):
    """
    Carga el contenido de todos los templates.
    """
    contents = ""
    for path in paths:
        with open(path, "r", encoding="utf-8") as f:
            contents += f.read()
    return contents


def find_used_classes(template_text, css_classes):
    """
    Devuelve un conjunto con las clases y IDs que sí se usan en los templates.
    """
    used = set()
    for cls in css_classes:
        if cls.startswith("."):
            if re.search(r'class\s*=\s*["\'].*?\b' + re.escape(cls[1:]) + r'\b.*?["\']', template_text):
                used.add(cls)
        elif cls.startswith("#"):
            if re.search(r'id\s*=\s*["\']' + re.escape(cls[1:]) + r'["\']', template_text):
                used.add(cls)
    return used


def write_used_styles(css_blocks, used_classes, output_file):
    """
    Escribe en output_file solo los estilos usados.
    """
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("/* Estilos utilizados detectados automáticamente */\n\n")
        for cls in sorted(used_classes):
            block = css_blocks.get(cls)
            if block:
                f.write(block + "\n\n")


# === PROCESO PRINCIPAL ===

def main():
    with open(CSS_FILE, "r", encoding="utf-8") as f:
        css_content = f.read()

    css_blocks = extract_css_blocks(css_content)
    template_content = load_template_contents(TEMPLATE_PATHS)
    used_classes = find_used_classes(template_content, css_blocks.keys())
    write_used_styles(css_blocks, used_classes, OUTPUT_FILE)

    print(f"✅ Estilos extraídos: {len(used_classes)} clases/IDs utilizadas.")
    print(f"💾 Archivo generado: {OUTPUT_FILE}")


if __name__ == "__main__":
    main()
