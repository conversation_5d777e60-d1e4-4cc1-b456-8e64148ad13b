import json
from datetime import datetime

from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponseRedirect, JsonResponse
from django.urls import reverse
from django.views.generic import View, ListView
from django_datatables_view.base_datatable_view import BaseDatatableView
from django.forms.models import model_to_dict
from django.conf import settings
from django.db import connection, DatabaseError
from django.db.models import Q

from muaytax.app_invoices.models.invoice import Invoice
from muaytax.users.permissions import IsManagerRolePermission, IsSellerShortnamePermission
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_lists.models.seller_list_management_es import SellerListManagementES
from muaytax.app_lists.models.seller_list_management_us import SellerListManagementUS
from muaytax.utils.general import get_year, get_period, get_first_and_last_date

debug = settings.DEBUG # Variable de depuración global

class SellerManagementCachedView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller

    def dispatch(self, request, *args, **kwargs):
        country = self.kwargs["country"].upper()
        if country not in ['ES', 'US']:
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)

    def get_template_names(self):
        country = self.kwargs["country"].upper()
        if country == 'ES':
            return ["sellers/seller_list/seller_management_es.html"]
        elif country == 'US':
            return ["sellers/seller_list/seller_management_us.html"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_year(self.request)
        period = self.get_period(self.request)
        entity = self.get_entity(self.request)
        context['debug'] = debug
        context['period'] = period
        context['year'] = year
        context['entity'] = entity
        return context 
    
    def get_year(self, request):
        year = request.GET.get('year')
        month = datetime.now().month
        if year is None:
            year = datetime.now().year
            if month == 1:
                year = year - 1
        return year

    def get_period(self, request):
        period = request.GET.get('period')
        if period is None:
            month = datetime.now().month
            if 2 <= month <= 4:
                period = "Q1"
            elif 5 <= month <= 7:
                period = "Q2"
            elif 8 <= month <= 10:
                period = "Q3"
            elif 11 <= month <= 12:
                period = "Q4"
            if month == 1:
                period = "Q4"
        return period

    def get_entity(self, request):
        entity = request.GET.get('entity')
        if entity is None:
            entity = "all"
        return entity
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerManagementESCachedListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        entity = self.request.GET.get('entity')
        return self.process_sql(request, year, period, entity)
    
    def post(self, request, *args, **kwargs):
        year = self.request.POST.get('year')
        period = self.request.POST.get('period')
        entity = self.request.POST.get('entity')
        return self.process_sql(request, year, period, entity)
    
    def process_sql(self, request, year, period, entity):
        start_time = datetime.now()
        response = { "data": [] }
        try:
            sql = f"SELECT func_managementES_cached_list_json({year},'{period}', '{entity}');"
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                result_data = json.loads(result_json)
                
                end_time = datetime.now()
                time_processing = (end_time - start_time).total_seconds()

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data['data'],
                    'pending_model_count': result_data['pending_model_count'],
                    'revision_model_count': result_data['required_model_count'],
                    'required_model_count': result_data['required_model_count'],
                    'disagreed_model_count': result_data['disagreed_model_count'],
                    'agreed_model_count': result_data['agreed_model_count'],
                    'presented_model_count': result_data['presented_model_count'],
                    'total_invoices': result_data['total_pending_invoices'],
                    'time_processing': time_processing
                }
            return JsonResponse(response)
        except Exception as e:
            print("Error:", e)
            response = {
                'data': [],
                "error": str(e)
            }
            return JsonResponse(response)

class UpdateSellerManagementESList(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year') if self.request.GET.get('year') else datetime.now().year
        period = self.request.GET.get('period') if self.request.GET.get('period') else '0A'
        entity = self.request.GET.get('entity') if self.request.GET.get('entity') else 'all'
        return self.process(year, period, entity)
    
    def post(self, request, *args, **kwargs):
        year = self.request.POST.get('year') if self.request.POST.get('year') else datetime.now().year
        period = self.request.POST.get('period') if self.request.POST.get('period') else '0A'
        entity = self.request.POST.get('entity') if self.request.POST.get('entity') else 'all'
        return self.process(year, period, entity)

    def process(self, year, period, entity):
        if period == '0A':
            sql = f"SELECT func_gestoria_es_yearly({year},'{period}','{entity}');"
        else:
            sql = f"SELECT func_managementES_list_json({year},'{period}', '{entity}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                result_data = json.loads(result_json)

                full_list = []
                for item in result_data:
                    seller_list = SellerListManagementES.objects.filter(seller__pk = item.get("seller_id", ''), year = year, period__pk = period).first()

                    if seller_list is None and item.get("seller_id", None) is not None:
                        seller_list = SellerListManagementES.objects.create(
                            seller_id = item.get("seller_id", None),
                            year = year,
                            period_id = period,
                            show = True
                        )
                    
                    if seller_list is not None:
                        seller_list.show = True
                        seller_list.model_111 = item.get("model_111", None)
                        seller_list.model_115 = item.get("model_115", None)
                        seller_list.model_130 = item.get("model_130", None)
                        seller_list.model_303 = item.get("model_303", None)
                        seller_list.model_309 = item.get("model_309", None)
                        seller_list.model_349 = item.get("model_349", None)
                        seller_list.model_369 = item.get("model_369", None)
                        seller_list.model_180 = item.get("model_180", None)
                        seller_list.model_184 = item.get("model_184", None)
                        seller_list.model_190 = item.get("model_190", None)
                        seller_list.model_349 = item.get("model_349", None)
                        seller_list.model_390 = item.get("model_390", None)
                        seller_list.model_296 = item.get("model_296", None)
                        seller_list.model_193 = item.get("model_193", None)
                        seller_list.model_202 = item.get("model_202", None)
                        seller_list.txt_01 = item.get("month1", False)
                        seller_list.txt_02 = item.get("month2", False)
                        seller_list.txt_03 = item.get("month3", False)
                        seller_list.txt_04 = item.get("month4", False)
                        seller_list.txt_05 = item.get("month5", False)
                        seller_list.txt_06 = item.get("month6", False)
                        seller_list.txt_07 = item.get("month7", False)
                        seller_list.txt_08 = item.get("month8", False)
                        seller_list.txt_09 = item.get("month9", False)
                        seller_list.txt_10 = item.get("month10", False)
                        seller_list.txt_11 = item.get("month11", False)
                        seller_list.txt_12 = item.get("month12", False)                        
                        seller_list.save()
                        full_list.append(model_to_dict(seller_list))

                response = {
                    'data': full_list,
                }
                return JsonResponse(response)

        except Exception as e:
            print("Error:", e)
            response = {
                'data': [],
                "error": str(e)
            }
            return JsonResponse(response)

class SellerManagementUSCachedListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    model = SellerListManagementUS

    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        excludes = self.request.GET.get('excludes')

        if not year or not period:
            return JsonResponse({'error': 'Missing required parameters'}, status=400)
        
        return self.process_sql(request, year, period, excludes)
    
    def process_sql(self, request, year, period, excludes=None):
        if excludes is not None:
            excludes_sql = "{" + excludes + "}"
        else:
            excludes_sql = "{}"
                
        sql = f"SELECT management_us_cached_list_json({year},'{period}','{excludes_sql}');"

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data['data'],
                    'required_notstarted_model_count': result_data['required_notstarted_model_count'],
                    'required_notcompleted_model_count': result_data['required_notcompleted_model_count'],
                    'pending_model_count': result_data['pending_model_count'],
                    'required_model_count': result_data['required_model_count'],
                    'disagreed_model_count': result_data['disagreed_model_count'],
                    'agreed_model_count': result_data['agreed_model_count'],
                    'fax_send_model_count': result_data['fax_send_model_count'],
                    'presented_model_count': result_data['presented_model_count'],
                    'total_invoices': result_data['total_pending_invoices'],
                }
                return JsonResponse(response)
            
        except DatabaseError as e:
            print("Error:", e)
            response = {
                'data': [],
                "error": str(e)
            }
            return JsonResponse(response)

class SellerManagementUsDTFixer(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    model = Seller

    def get(self, request, *args, **kwargs):
        from muaytax.app_documents.models import PresentedModel

        shortnames = shortnames = [
            "curio", "waveburstllc", "cristinamanyerbaselga", "aldorandollc", "southolidayllc", "onlineuniversellc",
            "nalaatelierllc", "pegatinllc", "labfuturellc", "marserrllc", "darsityllc", "acrprimellc", "turingconsultingllc",
            "reddiamondinfinityllc", "marcfontanals", "digitalcontentexpertsllc", "joser", "gibsonkatllc", "krosmakerllc",
            "scrummatellc", "fluxuryllc", "retailtrade45", "infullfitllc", "innodevllc", "ivansantiagopoza",
            "digitalmarketsensellc", "informcosmosllc", "grupomirollc", "webwardenllc", "yshagraphicsllc", "ipardronllc",
            "david.mullc", "caribbeanbluestorellc", "kineticplayllc", "miguelmoralbeitia", "smmkgloballogistics", "inversiallc",
            "alautoqaservicesllc", "versatileservicesllc", "fabinvega", "extremelinellc", "gasparallc",
            "franciscojavierducha", "danielgaeta", "koldobikalarraaga", "pezzllc", "raulperezlopez", "moodnspacellc",
            "sebastianvillarcervera", "graphicwizardsllc", "vanillaitservicesllc", "juanjosprezmadrid", "ceciliaprezcaso",
            "masterylearninghub", "perinawallc", "gebengloballlc", "enjoypm", "alejandrogilmerino", "ecomflarellc",
            "odolganengenhariadesoftwarellc", "pokomukillc", "rubngarcamateo", "rhinosadvisorllc", "braquillc", "fzdogllc",
            "lunaglobalproductsllc", "julianagarciaf", "andreanavarrocastro", "anvbisllc", "wisebridgegroupllc",
            "proelsystemsllc", "joseluishidalgogarcia", "educatingcryptolabs", "mariapalomaresllc", "hectorrodriguezfornies",
            "cgideallc", "renatelck", "uhurunomadsllc", "robertpaulkortenoeverlepe", "pedropradelosuna", "rosalvision",
            "nestorcastroquintero", "alejandrodovaltapia", "viviralmximosl", "lunelzollc", "bluebrickcapitalllc",
            "digitaltradellc", "talateam", "olivsolutionsllc", "jorgemiguelmorenohernndez", "embrace",
            "bazzinoconsultinggroupllc", "scrumservicesllc", "techdigitalhorizonllc", "pharmagalenllc", "retailonlinellc",
            "ascbllc", "softscoresolutionsllc", "tosomallc", "javiermenendez", "silosprellc", "innovusgroupllc",
            "aragonscalingllc", "globalwebenterprisellc", "stockoptionsmasterllc", "ibizafioravantillc", "latincommercellc",
            "3rbusinessllc", "geniusservicesaillc", "adrianperalesvizuete", "digitalsafetripllc", "tolariawestllc",
            "luisgomezcarballo", "deosconsultingllc", "eternalrainbowharmonyllc", "jopeads", "boostads", "onlinebusinesssalesllc",
            "mechindsolutionsllc", "digitalservices", "fsaallc", "micromediallc", "neltorsolutionsllc", "taunaisllc",
            "ceroredllc", "velpexllc", "precisiontestllc", "corpimarllc", "pochosallc", "ecommmservicellc", "consulting",
            "oceanoazulllc", "contabusinessllc", "independentmarketllc", "retailsourcedepotllc", "gaxerllc", "nomadfyllc",
            "mlbmarketingdigitalllc", "kulannecompanyllc", "ezequielsack", "zenithwithinllc", "atrellollc", "wolfventurellc",
            "sergiogarciacastro", "quiskdynamicsllc", "amznllc", "elwinsbourgeon", "icarollc", "guiasnaturales",
            "megabusinessadvisorsllc", "katzecomllc", "littlegiantsllc", "aunnolose", "wikconsultingllc", "gladiatorsvallc",
            "softinkllc", "arthycoconsultant", "26camelsllc", "bodymessagesllc", "joserodrigollc", "beyondboundsbusinessllc",
            "jennifersanchezcano", "yergsllc", "wombocombokeysllc", "usflecollc", "olvynllc", "accountaxconsultingllc",
            "aguirretruckingllc", "elkitrade", "iigocasillas", "europamasvivallc", "davislorence", "fernandoaniortemartnez",
            "3dmllc", "romulowolffpereira", "jesusmanuelgonzalezrueda", "softappsolutionsllc", "neonorteagencyllc",
            "aromejoliellc", "bondoillc", "on-netgloballlc", "karunaschoolhouse", "aivanasllc", "2mventerprisellc",
            "angelightllc", "geonfoxllc", "pipicsllc", "patriciagainzabeloqui", "lauracarballovisiedo", "amazezononlinellc",
            "cmmstore", "renzoantoniodasilva", "cardenaltechllc", "aldorando", "automationytllc", "llc", "extoiclifellc",
            "camiloguerrerocastro", "egroup", "magaliviedmallc", "carlosdeuribarrimarbn", "amanfelllc", "vevishopllc",
            "marmogroupemallc", "soltariamllc", "biomundusllc", "openingcodellc", "mokaddemienterprise", "nikkendigitalllc",
            "pedrocabezuelorosello", "veritanallc", "camiloalejandrorodriguezruiz", "mindworkcommllc", "eralysdigital",
            "cristianlopezmarquez", "finlibllc", "bevicisllc", "arestllc", "ecasborllc", "daonlypetsllc", "jeboltllc",
            "digitmindcompanyllc", "geedeon", "brisossllc", "bilamyllc", "branstyllc", "vaimarkllc", "creceusallc",
            "eshopablellc", "arncommomentllc", "jskllc", "zenithgloballlc", "wavecoastllc", "josramonserranojuanes",
            "maykupllc", "zulimposkllc", "traumllc", "digitaldatallc", "moradooneservices", "sergioiznaola", "madaramartllc",
            "benjamnseplvedanawrath", "helenreyeshernandez", "franciscoadasatvieradomnguez", "lamelimaker",
            "vvonlineservicesllc", "adolfofernandezbarrera", "raulmanuelbernardogil", "lauramartinezrodriguez",
            "evertyshopllc", "jorgegonzlezvivas", "watsonandmonday", "roxanapaolaolivafalasco", "taniaortegadez",
            "quixoticspiritbooksllc", "appallinglimitedcompany", "marantacapitalllc", "alvibooksgrouplc", "skykamasllc",
            "quantumrocketllc", "feedmillorg", "bernardodiazcastellano", "romizzu", "innovatechsolutionsllc",
            "gigsolutionsllc", "bestindieappsllc", "timetobuyllc", "chlorinetechllc", "starlordcreativellc", "lactomllc",
            "selfytechnologiesllc", "rkproductsllc", "vanessachiquinquirarodriguezperez", "massiale2513llc", "hypehubllc",
            "opharanallc", "wooficllc", "khawnanmanagementsolutionsllc", "algorithmicsolutionsllc", "grupocordllc", "pinillatr",
            "919itllc", "idolsignaturellc", "italgamellc", "greenpipelinellc", "neurabytetechinnovationsllc",
            "globalmigrationadvisorsllc", "enriquelopez", "infandvmdigitalsolutionsllc", "kahartillc", "ivanmanzanocasanova",
            "infinitysolutionsoftechnologiesllc", "amanllc", "mmdigitalstrategyllc", "denuballc", "amandaastudillo",
            "firstrulemanagementllc", "kayimimallc", "ninabluesllc", "ecuaforestar", "rivermumllc", "wabimartllc",
            "mocraservices", "alfresbrok", "carloslpezfernndez", "kreactollc", "belwillc", "relianflorescu",
            "r2fenergyefficientsolutionsllc", "platifyllc", "leonidasauqui", "ijmcompanyllc", "moonskyeventsllc",
            "pacasagroupllc", "jacarfilterllc", "curzolandiallc", "myqualitycollectionllc", "juanalejandropearomero",
            "miwacullc", "osvaldocesarmartn", "nomoretasksus", "luisgonzlezfalcn", "mamllc", "livingtothefullestllc",
            "ntmdigitalmarketing", "fitlionmanagement", "pgmediallc", "priloollc", "montexcorllc", "davidpulidofondon",
            "admvenlc", "spotlightestudiollc", "lemotechmasterdataconsultingllc", "americandollc", "jonascasllc",
            "andrescalerorodrguez", "kabacolllc", "escueladesabiduriallc", "shesapiens", "lamiradadevenusllc", "alexjaitehjaurs",
            "zetafwsl", "gaizkazubiondo", "c130mechanicllc", "spaceview", "slnsoulwealthllc", "maversoulllc", "xeopolyllc",
            "adrinmartntoro", "vuelaplumallc", "colmenarespctechllc", "davidofstradellc", "ocreenllc", "dagozxllc",
            "oriolquerol", "ascapitalllc", "micaellpezpereiras", "biaindsolutionsllc", "carinaveronicagiardina",
            "davidantoniolpezlpez", "degoboomslu", "soufingllc", "bensikaillc", "comfortnest", "arrticaltd",
            "globalwisesolutions", "luisalvescampos", "oceanfalkon", "oscarmic", "suargom", "hernanserratmiquel",
            "naturetoolsllc", "nictomiallc", "alejandrofernandezromero", "miguelibarburu", "issesafetysl",
            "ibrahimabajtourabajtour", "svaconsultingllc", "unidiscan", "jeypyllc", "businticllc", "josmanuelprincepsurez",
            "francesecommercellc", "vilaglobalservicesllc", "closerest", "fitkoshopllc", "taxencadenado1",
            "parkerlewiswebservicesllc", "dalaidigitalllc", "eassypol", "viderlab"
        ]

        sellers = Seller.objects.filter(shortname__in=shortnames)

        data = []

        for seller in sellers:
            model_5474 = PresentedModel.objects.filter(
                seller=seller, model__code='US-5472', year=2024
            ).first()

            status = 'processed'
            if model_5474 is not None:
                if model_5474.fax_destination_id:
                    status = 'fax_send'
                else:
                    status = model_5474.status.code 

            item = {
                "email": seller.user.email,
                "seller_id": seller.id,
                "shortname": seller.shortname,
                "user_name": f"{seller.user.first_name} {seller.user.last_name}" if seller.user else "",
                "model_5472": status,
                "seller_name": seller.name,
            }

            data.append(item)

        response = {
            "draw": int(request.GET.get('draw', 1)),
            "data": data,
            "required_notstarted_model_count": 0,
            "required_notcompleted_model_count": 0,
            "pending_model_count": 0,
            "required_model_count": 0,
            "disagreed_model_count": 0,
            "agreed_model_count": 0,
            "fax_send_model_count": 0,
            "presented_model_count": 0,
        }

        return JsonResponse(response, safe=False)


class UpdateAllSellerList(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    # def get(self, request, *args, **kwargs):
    #     year = self.request.GET.get('year') if self.request.GET.get('year') else datetime.now().year
    #     period = self.request.GET.get('period') if self.request.GET.get('period') else None
    #     list_code = self.request.GET.get('list') if self.request.GET.get('list') else None
    #     return self.process(year, period, list_code)
    
    def post(self, request, *args, **kwargs):
        year = self.request.POST.get('year', datetime.now().year)
        period = self.request.POST.get('period', None)
        list_code = self.request.POST.get('list', None)

        if not period or not list_code:
            return JsonResponse({'error': 'Missing required parameters'}, status=400)
        return self.process(year, period, list_code)

    def process(self, year: str, period: str, list_code: str) -> JsonResponse:
        response = { "status": "error", "error": "Error" }
        try:
            from muaytax.app_lists.utils import update_and_create_all_cached_lists
            if list_code == 'all':
                update_and_create_all_cached_lists('management_es', year, period)
                update_and_create_all_cached_lists('management_us', year, period)
                update_and_create_all_cached_lists('vat_es', year, period)
                update_and_create_all_cached_lists('vat_de', year, period)
                update_and_create_all_cached_lists('vat_fr', year, period)
                update_and_create_all_cached_lists('vat_it', year, period)
                update_and_create_all_cached_lists('vat_gb', year, period)
            else:
                update_and_create_all_cached_lists(list_code, year, period)
            response = {"status": "success", "error": ''}

        except Exception as e:
            print("Error:", e)
            response = {"status": "error", "error": str(e)}
        
        return JsonResponse(response)
    
    def handle_no_permission(self) -> HttpResponseRedirect:
        return super().handle_no_permission()
    

class SellerManagementUsFixer(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller

    def get_template_names(self):
        return ["sellers/seller_list/seller_management_us_fixer.html"]
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = '2024'
        period = '0A'
        entity = self.get_entity(self.request)
        context['debug'] = debug
        context['period'] = period
        context['year'] = year
        context['entity'] = entity
        return context 

    def get_entity(self, request):
        entity = request.GET.get('entity')
        if entity is None:
            entity = "all"
        return entity

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class SellerManagementESVerifactuList(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller
    def get_template_names(self):
        return ["sellers/seller_list/seller_management_es_verifactu.html"]
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = get_year(self.request.GET.get('year'))
        period = get_period(self.request.GET.get('period'))
        context['period'] = period
        context['year'] = year
        context['entity'] = self.request.GET.get('entity', 'all')
        return context
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class SellerManagementESVerifactuListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    def get(self, request, *args, **kwargs):
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        entity = self.request.GET.get('entity')
        first_date, last_date = get_first_and_last_date(year, period)
        return self.process_sql(request, first_date, last_date, entity)

    
    def post(self, request, *args, **kwargs):
        year = self.request.POST.get('year')
        period = self.request.POST.get('period')
        entity = self.request.POST.get('entity')
    
    def process_sql(self, request, first_date, last_date, entity):
        response = { "data": [] }
        try:
            sql = f"SELECT func_verifactu_seller_list_json('{first_date}', '{last_date}', '{entity}');"
            with connection.cursor() as cursor:
                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data,
                }
            return JsonResponse(response)
        except Exception as e:
            print("Error:", e)
            response = {
                'data': [],
                "error": str(e)
            }
            return JsonResponse(response)