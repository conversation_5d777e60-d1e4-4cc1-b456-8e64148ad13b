{% load static %}

{% block stylesheets %}
<link rel="stylesheet" href="{% static 'assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css' %}" />
<link rel="stylesheet" href="{% static 'assets/css/plugins/notifier.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/custom-dropzone/custom-dropzone.css' %}" />
{% endblock stylesheets %}

{% block content %}
<div class="container">
    <!-- warning alert -->
    <div class="alert alert-warning alert-dismissible fade show d-none" role="alert" id="limitInvoiceAlertMessage">
        <strong>¡Atención!</strong> Has excedido el límite de facturas puedes cargar. Por favor, elimina algunas facturas para continuar o contacta a soporte para cambiar el límite.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <form
        id="uploadDropzoneForm"
        action="{% url 'app_invoices:seller_invoice_process' seller.shortname %}"
        >
        {% csrf_token %}
        <div class="row">
            <div id="dropzoneContainer" class="dropzone-wrapper">
                <div id="customDropzone" class="custom-dropzone upload">
                    <div class="upload-progress"></div>
                    <div class="dropzone-img-container">
                        <img src="{% static 'assets/images/custom-dropzone/invoice_dropzone.svg' %}" alt="Dropzone">
                    </div>
                    <!-- <i class="bi bi-cloud-upload text-muted"></i> -->
                    <h4 class="mt-2 mb-0 text-dark fw-bold">
                        Arrastra & suelta 
                        <span class="text-success">tus facturas</span>
                    </h4>
                    <h6 class="text-dark">o haz clic para agregarlas desde tu ordenador</h6>
                    <input id="fileInput" type="file" accept=".pdf,.jpg,.jpeg,.png" multiple class="d-none">
                    <input type="hidden" id="seller" name="seller" value="{{ seller.pk }}">
                    <input type="hidden" id="iae" name="iae" v-model="inputEconomicActivity"/>
                    <p class="mt-3 mb-0 text-success small">*Formatos aceptados: PDF, JPG, PNG</p>
                </div>
            </div>
            
            <div id="fileListContainer" class="mb-3 file-list-container">
                <div class="w-100">
                    <div id="fileList" class="file-list scroll-div ps">
                        <!-- rendered in javascript -->
                    </div>
                    <div id="fileCount" class="mt-2 file-count text-start d-none">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="mt-3 col-12 text-center">
                <button id="submitDropzoneBtn" type="submit" class="btn btn-navy lft-hover m-0 btn-submit-dropzone upload" disabled>
                    <div class="d-flex flex-row justify-content-center align-items-center upload-button-text">
                        <i class="bi bi-cloud-upload me-2"></i>
                        Procesar Facturas
                    </div>
                    <div class="upload-hint">Procesando...</div>
                </button>
            </div>
        </div>
        
    </form>
</div>
{% endblock content %}
