from django.db import models


class PresentedModelResults(models.Model):
    code = models.CharField(
        primary_key=True,
        max_length=50,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Resultado del Modelo Presentado"
        verbose_name_plural = "Resultados del Modelo Presentado"

    def __str__(self):
        return self.description


# @admin.register(PresentedModelResults)
# class PresentedModelResultsAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
