from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from muaytax.signals import disable_for_load_data

class Concept(models.Model):

    # id -> AutoGen

    invoice = models.ForeignKey(
        "invoices.Invoice",
        on_delete=models.CASCADE,
        verbose_name="Factura",
    )

    concept = models.CharField(
        max_length=500,
        verbose_name="Concepto Factura",
    )

    percentage = models.IntegerField(
        default=100,
        verbose_name="Porcentaje %",
    )

    quantity = models.IntegerField(
        default=1,
        verbose_name="Cantidad",
    )

    amount_currency = models.FloatField(
        default=0,
        verbose_name="Base",
    )

    amount_euros = models.FloatField(
        default=0,
        verbose_name="Base (€)",
    )
    
    amount_original = models.FloatField(
        default=0,
        verbose_name="Base Original",
    )

    vat = models.FloatField(
        default=0,
        verbose_name="IVA (%)",
    )

    vat_currency = models.FloatField(
        default=0,
        verbose_name="IVA",
    )

    vat_euros = models.FloatField(
        default=0,
        verbose_name="IVA (€)",
    )

    irpf = models.FloatField(
        default=0,
        verbose_name="IRPF (%)",
    )

    irpf_currency = models.FloatField(
        default=0,
        verbose_name="IRPF",
    )

    irpf_euros = models.FloatField(
        default=0,
        verbose_name="IRPF (€)",
    )

    eqtax = models.FloatField(
        default=0,
        verbose_name="Recargo de equivalencia (%)",
    )

    eqtax_currency = models.FloatField(
        default=0,
        verbose_name="Recargo de equivalencia",
    )

    eqtax_euros = models.FloatField(
        default=0,
        verbose_name="Recargo de equivalencia (€)",
    )

    total_currency = models.FloatField(
        default=0,
        verbose_name="Total Concepto",
    )

    total_euros = models.FloatField(
        default=0,
        verbose_name="Total Concepto (€)",
    )

    asin = models.CharField(
        null=True,
        blank=True,
        max_length=50,
        verbose_name="ASIN",
    )

    is_supplied = models.BooleanField(
        default=False,
        verbose_name="¿Es suplido?"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Concepto de la Factura"
        verbose_name_plural = "Conceptos de la Factura"

    def __str__(self):
        return self.concept

@receiver(post_save, sender=Concept)
@disable_for_load_data
def after_concept_save(sender, instance, created, **kwargs):
    concept = instance
    
    # FIX for amount_original
    if concept is not None:
        if (
            (concept.amount_original is None and concept.amount_currency is not None) or 
            (concept.amount_original is not None and concept.amount_original == 0   and concept.amount_currency is not None and concept.amount_currency != 0) or
            (concept.amount_original is not None and concept.amount_original == '0' and concept.amount_currency is not None and concept.amount_currency != 0)
        ):
            percentage = 1
            try:
                percentage =  100 / concept.percentage
            except ZeroDivisionError:
                percentage = 0
            except Exception as e:
                percentage = 1

            try:
                concept.amount_original = concept.amount_currency * percentage
            except Exception as e:
                concept.amount_original = concept.amount_currency

            concept.save()
