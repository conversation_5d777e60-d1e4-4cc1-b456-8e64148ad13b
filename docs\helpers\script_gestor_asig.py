from muaytax.app_sellers.models import Seller, SellerVat
from muaytax.users.models import User

def cambiar_gestor_asignado(shortname_or_all: str, gestor_name="gestoria"):
    """
    Cambia el gestor asignado en SellerVat para un Seller específico por shortname o para todos con 'all'.
    """
    # Verifica que el gestor exista
    gestor = User.objects.filter(username__iexact=gestor_name, role="manager").first()
    if not gestor:
        return f"El gestor '{gestor_name}' no existe."

    if shortname_or_all.lower() == "all":
        # Cambiar para TODOS los SellerVat sin gestor asignado
        sin_gestor = SellerVat.objects.filter(manager_assigned__isnull=True)
        total = sin_gestor.update(manager_assigned=gestor)
        return f"Gestor '{gestor_name}' asignado a {total} SellerVat sin gestor asignado."

    else:
        # Cambiar para un Seller específico usando su shortname
        seller = Seller.objects.filter(shortname=shortname_or_all).first()
        if not seller:
            return f"No se encontró ningún Seller con el shortname '{shortname_or_all}'."
        
        # Obtener todos los SellerVat asociados a este Seller sin gestor asignado
        seller_vats = SellerVat.objects.filter(seller=seller, manager_assigned__isnull=True)
        print(f"Seller: {seller}, SellerVats sin gestor: {seller_vats.count()}")
        if not seller_vats.exists():
            return f"El Seller '{seller.shortname}' no tiene SellerVat sin gestor asignado."
        
        total = seller_vats.update(manager_assigned=gestor)
        return f"Gestor '{gestor_name}' asignado a {total} SellerVat del Seller '{seller.shortname}'."


# uso:
"""
# Importar el script y ejecutar la funcion
from docs.helpers.script_gestor_asig import cambiar_gestor_asignado

# 1. Para cambiar el gestor a un Seller específico
shortname = '2mventerprisellc'
cambiar_gestor_asignado(shortname)

# 2. Para cambiar el gestor a todos los SellerVat sin gestor asignado
cambiar_gestor_asignado("all")
"""