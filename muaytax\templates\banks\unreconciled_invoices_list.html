{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}

{% block stylesheets %}
  <!-- Archivos CSS y scripts estáticos necesarios -->
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <script type="module" src="{{ STATIC_URL }}assets/js/plugins/multi-checkbox.js"></script>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/loading.css"/>
  <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">

  <!-- Estilos específicos de DataTables -->
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v1.13.4.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.10.25.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css" type="text/css"/>

  <!-- Estilos de Select2 -->
  <link href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2.min-v4.1.0.css" rel="stylesheet" />
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2-bootstrap-5-theme.min-v5.css" />
  
  <!-- Estilos personalizados -->
  <style>
    thead tr th:after, thead tr th:before {
        display: none !important;
    }
    .dropdown-menu.show:before {
        display: none;
    }
    .tableLimit {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 13vw;
    }
    table.dataTable tbody tr td.select-checkbox:before {
      display: none;
    }
    table.dataTable tbody tr.selected td.select-checkbox:after {
      display: none;
    }
    .column-width-limit {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 13vw;
    }
    table.table.dataTable>tbody>tr {
      background-color: #fff!important;
    }
    #invoices-table tbody tr:hover td {
      background: #d6d6d6d2!important;
    }
    table tr td, table tr th {
      vertical-align: middle;
      border: none;
    }
    input#miId,
    input#select_all {
      margin-left: 8px;
      width: 14px;
      height: 14px;
    }
    .table-head {
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    input#miId {
      margin-left: 0px;
      width: 14px;
      height: 14px;
    }
    #select_all {
      width: 14px;
      height: 14px;
    }

    /* Asegura que la columna de los checkboxes sea de un ancho fijo */
    .select-checkbox-column {
      width: 20px !important; /* Puedes ajustar este valor según lo necesites */
      text-align: center; /* Centra horizontalmente */
      vertical-align: middle; /* Centra verticalmente */
    }
    .tooltip-wrapper {
        position: relative;
    }
    .tooltip-wrapper::after {
        content: attr(data-bs-original-title);
        position: absolute;
        bottom: 115%;
        left: 50%;
        transform: translateX(-50%);
        padding: 12px;
        background-color: black;
        color: white;
        border-radius: 20px;
        font-size: 14px;
        font-weight: normal;
        width: 200px;
        white-space: pre-line;
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 9999 !important;
    }
    .tooltip-wrapper:hover::after {
        visibility: visible;
        opacity: 1;
    }
    .tooltip-button {
        white-space: nowrap;
        width: 150px;
    }
    .tooltip-button:hover::after {
        white-space: nowrap;
        width: auto;
    }
    .table-head {
        position: sticky;
        top: 0;
        background-color: #f2f2f2;
        z-index: 1;
    }

    /* multiselectors style | START */
    multi-transaction,
    multi-invoicetype,
    multi-month,
    multi-economicactivity,
    multi-arrivalcountry,
    multi-departurecountry,
    multi-taxcountry,
    multi-vatrates {
      /* Element */
      --mc-z-index: 8 !important;
      --mc-cont-z-index: 20 !important;
      --mc-border: 1px solid #ced4da;;
      --mc-display: inline-block;
      --mc-font: 14px !important;
      --mc-margin: 0;
      --mc-vertical-align: middle;

      /* Dropdown */
      --mc-dropdown-background: #fff;
      --mc-dropdown-box-shadow: 0 1px 2px 0 rgba(57, 70, 92, .05);
      --mc-dropdown-max-height: 350px;
      --mc-dropdown-text-align: left;
      --mc-dropdown-width: 100%;

      /* Input */
      --mc-target-background: #fff;
      --mc-target-color: #000000;
      --mc-target-cursor: pointer;
      --mc-target-margin: 0;
      --mc-target-padding: 0px 0px 0px 3px;
      --mc-target-outline: none;
      --mc-vertical-align: middle;
      --mc-target-width: 268px;

      /* Dropdown Line Items */
      --mc-dropdown-line-height: 2em;
      --mc-ul-margin: 0;
      --mc-ul-padding: 0;
      --mc-checkbox-height: 30px;
      --mc-checkbox-width: 20px;
      --mc-li-cursor: pointer;

      /* Toggle Button */
      --mc-toggle-button-background: #ffffff;
      --mc-toggle-button-color: #000000;
      --mc-toggle-button-cursor: pointer;
      --mc-toggle-button-height: 46px;
      --mc-toggle-button-outline: none;
      --mc-toggle-button-width: 23px;

      /* Close Button */
      --mc-close-button-background: #efefef;
      --mc-close-button-border: none;
      --mc-close-button-border-radius: default;
      --mc-close-button-color: #000000;
      --mc-close-button-cursor: pointer;
      --mc-close-button-display: block;
      --mc-close-button-height: 22px;
      --mc-close-button-margin: 5px auto;
      --mc-close-button-outline: none;
      --mc-close-button-width: 22px;
    }
    #multi-value:hover {
      background-color: #0d6efd;
      --mc-dropdown-color: #fff;
    }
    #multi-value:hover div {
      color: #fff;
    }
    /* multiselectors style | END */

    .theme-bg { background: linear-gradient(-135deg, #1de9b6 0%, #03ad65 100%); }
    .theme-bg2 { background: linear-gradient(-135deg,#899fd4 0,#a389d4 100%); }
    .theme-bg3 { background: linear-gradient(-135deg,#04a9f5 0,#085f88 100%); }
    .def-position { 
        position: unset !important;
        top: 50% !important;
        transform: unset !important;
        opacity: unset !important;
    }
    .vh-88 {
      height: 88vh;
    }
    .icon-text {
      font-size: 1rem;
      color: #ffffff;
    }

    /* accordion reset settings */
    .accordion-button:not(.collapsed) {
      color: #111;
      background-color: transparent;
      box-shadow: none;
    }
    .accordion-button:focus {
      z-index: 3;
      border-color: #86b7fe;
      outline: 0;
      box-shadow: none;
    }
    /* accordion reset settings */

    /* table in details */
    #tableColapse1 table tr td:first-child,
    #tableColapse2 table tr td:first-child,
    #tableColapse3 table tr td:first-child{
      min-width: max-content;
    }
    .table-hover tbody tr:hover, .table-striped tbody tr:nth-of-type(odd) {
      background-color: #e8eaed99!important;
    }
    /* table in details */

    .list-action-btn-block{
      position: relative;
    }
    .list-action-btn-block > *{
      font-size: 12px;
      margin-bottom: unset!important;
      margin-right: unset!important;
    }
    .list-action-btn-block button{
      min-width: max-content;
    }
    .dropdown-form {
      display: block;
      min-width: 500px;
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      padding: 10px;
      z-index: 1000;
      transform: translate(-70%, 10px);
      opacity: 0;
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    .btn.dropdown-toggle:after {
      display: none
    }
    .top-right-badge{
      position: absolute!important;
      top: -10px!important;
      right: -5px!important;
      width: 25px!important;
      height: 25px!important;
      align-items: center;
      display: flex;
      justify-content: center;
    }
    .search-container {
      display: inline-block;
      position: relative;
      width: 200px; 
      transition: width 0.4s ease-in-out;
    }
    .search-container > input {
      font-size: 12px;
    }
    .search-container:focus-within {
      width: 100%; /* Expanded width */
    }
  </style>
{% endblock stylesheets %}

{% block title %}
  Facturas No Conciliadas
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas No Conciliadas
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:summary' seller.shortname %}">
                    {% if seller.name is not None %}
                      {{ seller.name.capitalize }}
                    {% else %}
                      Resumen
                    {% endif %}
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_banks:bank_unreconciled_invoices_list' seller.shortname %}">Facturas no conciliadas</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <!--- CONCILIATION (Amz o CC) - by row selection | start--->
    <div class="col-xl-6 col-sm-12">
      <div class="d-flex align-items-center gap-2 m-2 p-0 justify-content-start">
        <div class="col-xl-6 col-sm-12">
          <div class="d-flex align-items-center justify-content-start">
            <form
              method="post" id="formSelect"
              enctype="multipart/form-data"
              action="{% url 'app_invoices:seller_invoices_massive' seller.shortname %}">
              {% csrf_token %}

              <div class="col-12" id="massiveAction">
                <div class="col-12 d-flex">
                  <select class="form-control form-select" id="selectValue" onchange="urlForm()" disabled>
                    <option value="empty">Selecciona una acción de conciliación</option>
                    <option value="conciliationAMZ">Conciliar Amazon</option>
                    <option value="conciliationCC">Conciliar Cuenta Contable</option>
                  </select>
                  <button 
                    id="enabledButton" type="submit"
                    class="btn btn-dark mx-2 my-auto" disabled 
                    style="display:block; width: 120px;">
                    <b>Confirmar</b>
                  </button>
                  <input type="hidden" name="selectedPk" id="selectedPk">
                  <!-- Modal Amz-->
                  <!-- Modal CC-->
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    <!--- CONCILIATION (Amz o CC) - by row selection | end--->
    <!-- FILTERS and SEARCH | start -->
    <div class="col-xl-6 col-sm-12">
      <div class="d-flex align-items-center gap-2 justify-content-end">
        <!-- Buscador -->
        <div class="list-action-btn-block flex-fill d-flex justify-content-end">
          <div class="search-container">
            <input class="form-control" type="search" id="search" name="search" placeholder="Escribe y presiona enter &#x21B5; para poder buscar...">
          </div>
        </div>
        <!-- Filtro de selección múltiple -->
        <div class="list-action-btn-block">
          <button
            id="dropdownButton" class="btn btn-dark"
            type="button" title="Filtros">
            <i class="mdi mdi-filter-outline fa-xl me-0"></i>
            <span class="badge top-right-badge rounded-pill bg-danger d-none" id="id-filter-notification">2</span>
          </button>
          <div id="dropdownFiltersForm" class="dropdown-form shadow p-3">
            <form id="filtersFormID">
              <div class="row mb-2">
                <!-- Filtro por Año -->
                <div class="col-6 mb-3">
                  <label for="year" class="mb-1">Año</label>
                  <select class="form-control form-select" name="year" id="year" onchange="filter(); onChangeYear();">
                    <option value="">Todos los años</option>
                    {% for year in years_list %}
                      <option value="{{ year }}">{{ year }}</option>
                    {% endfor %}
                  </select>
                </div>
                <!-- meses -->
                <div class="col-6 mb-3">
                  <label for="multiple-month" class="mb-1">Meses</label> 
                  <multi-month separator=", " value="" id="multiple-month"> 
                    <ul slot="check-values">
                      <li class="cursor-default" id="multi-value" value="1" multi-title="Enero">Enero</li>
                      <li class="cursor-default" id="multi-value" value="2" multi-title="Febrero">Febrero</li>
                      <li class="cursor-default" id="multi-value" value="3" multi-title="Marzo">Marzo</li>
                      <li class="cursor-default" id="multi-value" value="4" multi-title="Abril">Abril</li>
                      <li class="cursor-default" id="multi-value" value="5" multi-title="Mayo">Mayo</li>
                      <li class="cursor-default" id="multi-value" value="6" multi-title="Junio">Junio</li>
                      <li class="cursor-default" id="multi-value" value="7" multi-title="Julio">Julio</li>
                      <li class="cursor-default" id="multi-value" value="8" multi-title="Agosto">Agosto</li>
                      <li class="cursor-default" id="multi-value" value="9" multi-title="Septiembre">Septiembre</li>
                      <li class="cursor-default" id="multi-value" value="10" multi-title="Octubre">Octubre</li>
                      <li class="cursor-default" id="multi-value" value="11" multi-title="Noviembre">Noviembre</li>
                      <li class="cursor-default" id="multi-value" value="12" multi-title="Diciembre">Diciembre</li>
                    </ul>
                  </multi-month>
                </div>
                <!-- tax country -->
                <div class="col-6 mb-3">
                  <label for="multiple-taxcountries" class="mb-1">País de impuestos</label>
                  <multi-taxcountry separator=", " value="" id="multiple-taxcountries">
                    <ul slot="check-values">
                      {% for country in invoices_tax_country %}
                        <li 
                          class="cursor-default" 
                          id="multi-value" 
                          value="{{ country.pk }}"
                          multi-title="{{ country.name }} ( {{ country.pk }} )"
                        ></li>
                      {% endfor %}
                    </ul>
                  </multi-taxcountry>
                </div>
                <!-- departure country -->
                <div class="col-6 mb-3">
                  <label for="multiple-departurecountries" class="mb-1">País de salida</label>
                  <multi-departurecountry separator=", " value="" id="multiple-departurecountries">
                    <ul slot="check-values">
                      {% for country in invoices_departure_country %}
                        <li 
                          class="cursor-default" 
                          id="multi-value" 
                          value="{{ country.pk }}"
                          multi-title="{{ country.name }} ( {{ country.pk }} )"
                        ></li>
                      {% endfor %}
                    </ul>
                  </multi-departurecountry>
                </div>
                <!-- tipos de facturas -->
                <div class="col-12 mb-3">
                  <label for="multiple-invoicetype" class="mb-1">Tipo de factura</label>
                  <multi-invoicetype separator=", " value="" id="multiple-invoicetype">
                    <ul slot="check-values">
                      {% for invoice in invoice_types %}
                      <li
                          class="cursor-default" 
                          id="multi-value" 
                          value="{{ invoice.code }}"
                          multi-title="{{ invoice.description }}"
                      >{{ invoice.description }}</li>
                      {% endfor %}
                    </ul>
                  </multi-invoicetype>
                </div>
                <!-- tipos de transacciones -->
                <div class="col-12 mb-3">
                  <label for="multiple-transactions" class="mb-1" id="multiple-transactions-label">Tipo de transacción</label>
                  <multi-transaction separator=", " value="" id="multiple-transactions">
                    <ul slot="check-values">
                      {% for transaction_type in transaction_types %}
                        <li 
                          class="cursor-default" 
                          id="multi-value" 
                          value="{{ transaction_type.code }}"
                          multi-title="{{ transaction_type.description }}"
                        ></li>
                      {% endfor %}
                    </ul>
                  </multi-transaction>
                </div>
              </div>
              <hr>
              <div class="row">
                <div class="col-12 text-end">
                  <button type="button" class="btn btn-light" id="clearFiltersButton" onclick="resetFilters()" disabled>
                    Limpiar
                  </button>
                  <button type="button" id="applyFiltersButton" class="btn btn-dark" onclick="applyFilters()">
                    Aplicar
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
        <!-- Paginación -->
        <div class="list-action-btn-block">
          <select class="form-control form-select" name="show" id="show" onchange="filter()">
            <option value="50" default>50</option>
            <option value="100">100</option>
            <option value="200">200</option>
          </select>
        </div>
      </div>
    </div>
    <!-- FILTERS and SEARCH | end -->

    <!-- TABLA LISTADO DE FACTURAS | start -->
    <div class="col-12 user-profile-list">
      <table id="invoices-table" class="table table-striped table-bordered dt-responsive nowrap border" style="width:100%">
        <thead class="table-head">
          <tr>
            <th>
              <input type="checkbox" id="select_all">
            </th>
            <th style="width:1%;">ID</th>
            <th style="width:1%;">Estado</th>
            <th>Número</th>
            <th>Cliente</th>
            <th>Proveedor</th>
            <th>Cliente/Proveedor</th>
            <th style="width:1%;">Categoria</th>
            <th style="width:1%;">Fecha Contabilización</th>
            <th style="width:1%;">Fecha Factura</th>
            <th style="width:1%;">IVA (€)</th>
            <th style="width:1%;">Base (€)</th>
            <th style="width:1%;">Total (€)</th>
            <th style="width:1%;">Acciones</th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
    <!-- TABLA LISTADO DE FACTURAS | end -->
  </div>
  <!-- MULTIPLE CONCILIATION (AMZ) | start Modal -->
  <div class="modal fade" id="multiConciliateAmazonModal" tabindex="-1" aria-labelledby="multiConciliateAmazonModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Conciliación Amazon (Multiselección)</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <!-- Texto: Orientación -->
        <div class="modal-body" id="multiConciliateAmazonModalBody">
          <p>Recuerda que estás conciliando una o varias facturas al mismo tiempo.</p>
          <p>Se va a crear un movimiento en el banco Amazon. ¿Estás seguro?</p>
        </div>
        <!-- Spinner - "Procesando..." -->
        <div class="modal-body" id="processingSpinnerModal" style="display: none; text-align: center;">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Procesando...</span>
          </div>
          <p>Procesando...</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" id="multiConciliateAmazonButton" class="btn btn-primary">Conciliar</button>
        </div>
      </div>
    </div>
  </div>
  <!-- MULTIPLE CONCILIATION (AMZ) | end Modal -->
  <!-- MULTIPLE CONCILIATION (CC) | start Modal -->
  <div class="modal fade" id="multiConciliateAccountModal" tabindex="-1" aria-labelledby="multiConciliateAccountModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Conciliación Contra Cuentas Contables (Multiselección)</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <!-- Texto: Orientación -->
        <div class="modal-body" id="multiConciliateAccountModalBody">
          <p>Recuerda que estás conciliando una o varias facturas al mismo tiempo. Asegúrate de seleccionar aquellas con la misma cuenta contable.</p>
          <!-- Selector de cuenta contable -->
          <label for="accountSelectorMulti">Seleccione Cuenta Contable:</label>
          <select class="form-control select2" name="accountSelectorMulti" id="accountSelectorMulti">
            <option value="0" disabled selected>Seleccione una cuenta contable</option>
            {% for account in accounting_account %}
              <option value="{{account.pk}}" data-description="{{account.description}}">
                {{account.pk}} - {{account.description}}
              </option>
            {% endfor %}
          </select>
        </div>
        <!-- Spinner - "Procesando..." -->
        <div class="modal-body" id="processingSpinnerAccountModalMulti" style="display: none; text-align: center;">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Procesando...</span>
          </div>
          <p>Procesando...</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" id="multiConciliateAccountButton" class="btn btn-primary" disabled>Conciliar</button>
        </div>
      </div>
    </div>
  </div>
  <!-- MULTIPLE CONCILIATION (CC) | end Modal -->
  <!-- SHOW INVOICE | start Modal -->
  <div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModal" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-xl" style="max-width: 90% !important;">
      <div class="modal-content border rounded">
        <div class="modal-header">
          <h5 class="modal-title" id="staticBackdropLabel">Factura <span id='invoiceModalSpanReference'></span> </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-0">
          <div class="col-12 m-0">
            <div id="invoiceModalIframeLoading" class="w-100 vh-88" width="100%" style="display: flex;">
              <div class="d-flex justify-content-center align-items-center text-center w-100">
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
              </div>
              <br>
            </div>
            <fieldset disabled>
              <iframe id="invoiceModalIframe" src="" class="w-100 vh-88" width="100%" style="display: none;">
                "No es posible visualizar el contenido"
              </iframe>
            </fieldset>
          </div>
        </div>
      </div>  
    </div>
  </div>
  <!-- SHOW INVOICE | end Modal -->
  <!-- CONFIRM CONCILIATION - ¿ARE YOU SURE? YES / NO | start Modal-->
  <div class="modal fade" id="concilationModal" tabindex="-1" aria-labelledby="concilationModal" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-md">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="staticBackdropLabel">Conciliar Factura <span id='concilationModalSpanReference1'></span> </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>
            <h6>
              Vas a conciliar la <b>factura <span id='concilationModalSpanReference2'>X</span></b> con un importe de <b><span id='concilationModalSpanAmount'>X</span> €.</b>
            </h6>
          </p>
          <p>
            <h6>
              Se va a crear un <b>Movimiento de <span id='concilationModalSpanAmountMovement'>X</span> €</b> en el <b>Banco <span id='concilationModalSpanBank' class="text-capitalize">X</span></b>.
            <h6>
          </p>
          <p>
            <h6>
              <b>¿Estás seguro?</b>
            </h6>
          </p>
        </div>
        <div class="modal-footer">
          {% csrf_token %}
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
          <button type="button" class="btn btn-primary" id="concilationModalButton">Conciliar</button>
        </div>
      </div>
    </div>
  </div>
  <!-- CONFIRM CONCILIATION - ¿ARE YOU SURE? YES / NO | end Modal-->
  <!-- SIMPLE CONCILIATION (CC) | start Modal -->
  <div class="modal fade" id="modalConciliateAccount" tabindex="-1" role="dialog" aria-labelledby="modalConciliateAccountLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalConciliateAccountLabel">Conciliar Contra Cuentas Contables</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12">
              <div id="modalConciliateAccountContent">
                <label for="modalConciliateAccountInputBank">Seleccione Cuenta Contable:</label>
                <select class="select2" name="modalConciliateAccountInputBank" id="modalConciliateAccountInputBank">
                  <option value="0" disabled="disabled"> Seleccione Cuenta Contable</option>
                  {% for account in accounting_account %}
                    <option value="{{account.pk}}" data-description="{{account.description}}"> {{account.pk}} - {{account.description}}</option>
                  {% endfor %}
                </select>
              </div> 
            </div>            
            <div class="col-12 mt-3">
              <label for="modalConciliateAccountInputAmount">Cantidad a Conciliar:</label>
              <input type="number" class="form-control" id="modalConciliateAccountInputAmount" readonly>
            </div>
          </div>        
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" id="modalConciliateAccountBtn">Conciliar</button>
        </div>
      </div>
    </div>
  </div>
  <!-- SIMPLE CONCILIATION (CC) | end Modal -->
{% endblock content %}

{% block javascripts %}

  <!-- JQUERY DATA TABLE -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{% static 'assets/js/loading.js' %}"></script>
  <!-- Incluye el script de Select2 -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/select/select2.min-v4.1.0.js"></script>
  <!-- start DEBUG -->
  <script type="text/javascript">
    const debug = {{ debug|yesno:"true,false" }};
    // Función para debug (imprime en consola solo si debug está habilitado)
    function debugLog(...args) {
      if (debug) {
        console.log(...args);
      }
    }
    debugLog("Debug mode is enabled")
  </script>
  <!-- end DEBUG -->
  <!-- MAIN SCRIPT -->
  <script type="text/javascript">

    // Variables globales
    let table = null;
    let timer;
    let arrayRowData = [];
    let arrayStatus = [];
    let filtersDictCount = {
      'year': false,
      'month': false,
      'multiple-taxcountries': false,
      'multiple-vatrates': false,
      'multiple-departurecountries': false,
      'multiple-arrivalcountries': false,
      'invoice_type': false,
      'multiple-transactions': false,
      'multiple-economicactivities': false,
      'multiple-invoicetype': false,
      'multiple-month': false,
    }

    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });
    
    // Elementos DOM
    const multipleMonth = document.getElementById('multiple-month');
    multipleMonth.addEventListener('change', () => filter());
    const multipleTaxCountries = document.getElementById('multiple-taxcountries');
    multipleTaxCountries.addEventListener('change', () => filter());
    
    const multipleDepartureCountries  = document.getElementById('multiple-departurecountries');
    multipleDepartureCountries .addEventListener('change', () => filter());

    const multipleInvoiceType = document.getElementById('multiple-invoicetype');
    multipleInvoiceType.addEventListener('change', () => filter());
    
    const multiCheckbox = document.getElementById('multiple-transactions');
    multiCheckbox.addEventListener('change', () => filter());

    const filterForm = document.getElementById('filtersFormID');

    const search_invoice = document.getElementById('search');

    search_invoice.addEventListener("focus", () => {
      search_invoice.parentElement.style.width = "100%";
    });

    search_invoice.addEventListener("blur", () => {
        search_invoice.parentElement.style.width = "200px";
    });

    search_invoice.addEventListener("keydown", (e) => {
      debugLog('resultado de la busqueda: ', e);
      if (e.key === "Enter") {
        // clearTimeout(timer);
        filter();
      }
    });

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    // Función para preparar y devolver los datos que serán enviados en la solicitud AJAX
    const ajaxData = (d) => {
      debugLog('ajaxData | data: ', d);
      
      const getDataValue = (elementId) => {
        const el = document.getElementById(elementId);
        return el ? el.dataset.value : null;
      };
    
      const getValue = (elementId) => {
        const el = document.getElementById(elementId);
        return el ? el.value : null;
      };
      
      let unreconciled = true;
      let status = 'revised';

      let year = getValue("year");
      let multiple_month = getDataValue("multiple-month");
      let country = getDataValue("multiple-taxcountries");
      let multiple_invoicetype = getDataValue("multiple-invoicetype");
      let multiple_transactions = getDataValue("multiple-transactions");
      let departure_country = getDataValue("multiple-departurecountries");

      let search = search_invoice.value;
      let show = getValue("show");
      let fc_transfer = false;
      let category = "";
      let tParams = "";
      let selectedRows = [];

      // Aplicar filtros a los datos enviados
      if (unreconciled) {
        debugLog('filterDT | unreconciled: ', unreconciled);
        d.unreconciled = true;
        tParams += "&unreconciled=" + unreconciled;
      }

      if (status) {
        debugLog('filterDT | estado: ', status);
        d.status_id = status;
        tParams += "&status=" + status;
      }

      if (year) {
        debugLog('filterDT | year: ', year);
        d.expedition_date_year = year;
        tParams += "&year=" + year;
      }

      if (multiple_month) {
        debugLog('filterDT | multiple_month: ', multiple_month);
        const selectedMonthValues = multiple_month.split(', ');
        d.months = JSON.stringify(selectedMonthValues);
        tParams += "&months=" + JSON.stringify(selectedMonthValues);;
      }

      if (country) {
        country = JSON.stringify(country.split(", "));
        d.tax_country_id = country;
        tParams += "&country=" + country;
      }

      if (departure_country) {
        debugLog('filterDT | departure_country: ', departure_country);
        departure_country = JSON.stringify(departure_country.split(", "));
        d.departure_country_id = departure_country;
        tParams += "&departure_country=" + departure_country;
      }

      if (multiple_invoicetype) {
        debugLog('filterDT | multiple_invoicetype: ', multiple_invoicetype);
        const selectedInvoiceTypeValues = multiple_invoicetype.split(', ');
        d.invoice_type_id = JSON.stringify(selectedInvoiceTypeValues);
        tParams += '&invoice_type_id=' + JSON.stringify(selectedInvoiceTypeValues);
      }

      if (multiple_transactions) {
        debugLog('filterDT | multiple_transactions: ', multiple_transactions);
        const selectedValues = multiple_transactions.split(', ');
        d.transaction_type_id = JSON.stringify(selectedValues);
        tParams += '&transaction=' + JSON.stringify(selectedValues);
      }

      if (search) {
        debugLog('filterDT | search: ', search);
        d.search = search.trim();
        tParams += "&search=" + search.trim();
      }

      if (category) {
        debugLog('filterDT | category: ', category);
        d.invoice_category_id = category;
      }

      if (fc_transfer) {
        debugLog('filterDT | fc_transfer: ', fc_transfer);
        d.fc_transfer = fc_transfer;
        tParams += "&transfer=" + fc_transfer;
      }

      // Aplicar orden a los resultados
      if (d.order.length > 0) {
        orderby = [];
        for (const o of d.order) {
          name = d.columns[o.column].data;
          if (name == 'contact') {
            orderby.push({"dir": o.dir, "name": "customer"});
            orderby.push({"dir": o.dir, "name": "provider"});
          } else if (name == 'account_sales_expenses') {
            orderby.push({"dir": o.dir, "name": "account_sales"});
            orderby.push({"dir": o.dir, "name": "account_expenses"});
          } else if (name == 'date') {
            orderby.push({"dir": o.dir, "name": "expedition_date"});
            orderby.push({"dir": o.dir, "name": "invoice_date"});
            orderby.push({"dir": o.dir, "name": "accounting_date"});
          } else if (name == 'status') {
            orderby.push({"dir": o.dir, "name": "status__order"});
          } else {
            orderby.push({"dir": o.dir, "name": name});
          }
          orderby.push({"dir": 'desc', "name": "pk"});
        }
        debugLog('filterDT | orderBy: ', orderby);
        d.order = JSON.stringify(orderby);
      }

      return d;
    }

    // Función para crear la tabla DataTable con las configuraciones
    const createDT = () => {
      table = $('#invoices-table').DataTable({
        "serverSide": true,
        "ajax": {
          "url": "{% url 'app_invoices:seller_invoices_dt' seller.shortname %}",
          "data": function (d) {
            ajaxData(d);
          },
        },
        "language": {
          "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
        },
        "searching": false,
        "lengthChange": false,
        "lengthMenu": [[50, 100, 200], [50, 100, 200]],
        "order": [[8, 'desc']],
        "select": {"style": 'multi', "selector": 'custom-checkbox'},
        "columns": [
          {
            "data": null,
            "className": 'select-checkbox-column select-checkbox miId',
            "visible": true,
            "orderable": false,
            "render": function (data, type, full, meta) {
              if (type === 'display') {
                return '<input type="checkbox" class="custom-checkbox" data-select="true" id="miId">';
              }
              return data;
            }
          },
          {"data": "pk", "visible": true},
          {
            "data": "status", "render": function (data, type, row) {
              let html = "";
              if (row.status) {
                let bg = "";
                if (row.status == "Pendiente" || row.status == "Revision Pendiente") {
                  bg = "bg-warning";
                } else if (row.status == "Revisada") {
                  bg = "bg-success";
                } else if (row.status == "Descartada") {
                  bg = "bg-danger";
                }
                html = `<span class="rounded ${bg} text-white p-1">
                                <b> &nbsp; ${row.status} &nbsp; </b>
                            </span>`;
              }
              return html;
            }
          },
          {"data": "reference", className: "tableLimit"},
          {"data": "customer", "visible": false},
          {"data": "provider", "visible": false},
          {
            "data": "contact", className: "tableLimit", "render": function (data, type, row) {
              let contact = "";
              if (row.customer) {
                contact = row.customer;
              } else if (row.provider) {
                contact = row.provider;
              }
              return contact;
            }
          },
          {"data": "invoice_category"},
          {"data": "accounting_date", "orderable": true, "visible": true},
          {"data": "invoice_date", "orderable": true, "visible": true},
          {"data": "vat_euros"},
          {
            "data": "amount_euros", "render": function (data, type, row) {
              let amount_euros = "";
              if (row.transaction_type == "Import DUA") {
                if (row.status != "Pendiente" && row.status != "Revision Pendiente") {
                  amount_euros = "-";
                }
              } else if (row.amount_euros) {
                amount_euros = row.amount_euros;
              }
              return amount_euros;
            }
          },
          {
            "data": "total_euros_concp", "render": function (data, type, row) {
              let total_euros_concp = "";
              if (row.transaction_type == "Import DUA") {
                total_euros_concp = row.vat_euros;
              } else if (row.total_euros_concp) {
                total_euros_concp = row.total_euros_concp;
              }
              return total_euros_concp;
            }
          },
          {
            "data": "file", "orderable": false, "render": function (data, type, row) {
              let htmlseparated = "";
              let button1 = "";
              let button2 = "";
              let button3 = "";

              if (row.pk) {
                button1 = `
                      <btn class="btn btn-success btn-icon tooltip-wrapper tooltip-button px-0" 
                      href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}" 
                      onclick="showInvoice('${row.pk}', '${row.reference}')"
                      data-bs-toggle="tooltip" 
                      data-bs-placement="top" 
                      data-bs-original-title="Visualizar Factura"
                      >
                      <i class="fa-regular fa-xl fa-eye"></i>
                      </btn>
                  `;
                button2 = `
                      <btn class="btn btn-warning btn-icon tooltip-wrapper tooltip-button px-0" 
                      onclick="onClickConciliateModal('${row.pk}', '${row.reference}', '${row.total_euros_concp}', 'amazon')"
                      data-bs-toggle="tooltip" 
                      data-bs-placement="top" 
                      data-bs-original-title="Conciliar Amazon"
                      >
                      <i class="fa-brands fa-xl fa-amazon"></i>
                      </btn>
                  `;
                button3 = `
                      <btn 
                      id="btn_conciliate_account_${row.pk}" 
                      name="btn_conciliate_account_${row.pk}"
                      class="btn btn-warning btn-icon tooltip-wrapper tooltip-button px-0" 
                      onclick="onClickConciliateAccountModal('${row.pk}', '${row.reference}', '${row.total_euros_concp}')"
                      data-bs-toggle="tooltip" 
                      data-bs-placement="top" 
                      data-bs-original-title="Conciliar Cuenta Contable"
                      title="Cuentas Contables"
                      >
                      <i class="fa-solid  icon-text"> C C </i>
                      </btn>
                  `;
              }

              if (row.pk || row.file) {
                htmlseparated = `
                      ${button1}
                      ${button2}
                      ${button3}
                  `;
              }
              return htmlseparated;
            }
          },
        ],
        // Añadir esta función para ocultar filas con valores 0
        "createdRow": function(row, data, dataIndex) {
          // Obtener y convertir los valores a números
          const vatValue = parseFloat(data.vat_euros) || 0;
          const amountValue = parseFloat(data.amount_euros) || 0;
          const totalValue = parseFloat(data.total_euros_concp) || 0;
          
          // Si todos los valores son 0, ocultar la fila
          if (vatValue === 0 && amountValue === 0 && totalValue === 0) {
            $(row).hide();
          }
        },
        "language": {
          "lengthMenu": "_MENU_",
          "zeroRecords": "No se han encontrado facturas.",
          "info": "_START_ a _END_ de un total de _TOTAL_",
          "search": "Buscar:",
          "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
          "infoFiltered": "",
          "emptyTable": "Cargando...",
          "paginate": {
            "first": "Primero",
            "last": "Último",
            "previous": "Anterior",
            "next": "Siguiente"
          },
        },
        "drawCallback": function (settings, json) {
          $('#select_all').prop("checked", false);
          arrayRowData = [];
          arrayStatus = [];
        }
      });
      filter();

      $('#page-length').change(function () {
        table.page.len($(this).val()).draw();
      });
    }

    // Función para aplicar filtros y recargar la tabla
    const filter = () => {
      let show = document.getElementById("show").value;
      if (show) {
        table.page.len(show);
      }
      table.draw();
    }

    // Función para mostrar la factura en el modal
    const showInvoice = (invoiceId, invoiceReference) => {
      debugLog('El invoice ID es:' + invoiceId);

      // Obtener el modal y el iframe donde se mostrará la factura
      const modal = document.getElementById("invoiceModal");
      const iframe = document.getElementById("invoiceModalIframe");

      // Ocultar iframe inicialmente
      iframe.style = "display: none;";

      // Establecer la URL en el iframe
      iframe.src = "{% url 'app_sellers:summary' seller.shortname %}invoice/" + invoiceId + "/?popup=true";

      // Mostrar el cargando del modal
      document.getElementById("invoiceModalIframeLoading").style = "display: flex;";

      // Establecer la referencia de la factura en el modal
      document.getElementById("invoiceModalSpanReference").innerHTML = invoiceReference;

      // Mostrar el modal
      $('#invoiceModal').modal('show');
    }

    // Función para mostrar el iframe una vez que ha cargado el contenido
    const showIframeModalInvoices = () => {
      debugLog(`showIframeModalInvoices`)
      // Obtener el iframe
      const iframe = document.getElementById("invoiceModalIframe");

      // Mostrar el iframe
      iframe.style = "display: block;";

      // Ocultar el cargando del modal
      document.getElementById("invoiceModalIframeLoading").style = "display: none;";

      // Eliminar los botones dentro del iframe si existen
      if (iframe && iframe.contentDocument) {
        let iframeContent = iframe.contentDocument;
        if (iframeContent) {
          const divButtons = iframeContent.getElementById('buttons');
          if (divButtons) {
            divButtons.style = "display: none;";
            divButtons.remove();
          }
        }
      }
    }

    // Función para abrir el modal de conciliación de cuentas contables
    const onClickConciliateAccountModal = (invoice_id, reference, amount = 0) => {
      const account_modal = $("#modalConciliateAccount");
      const select = $("#modalConciliateAccountInputBank");
      const input = $("#modalConciliateAccountInputAmount");
      const btn = $("#modalConciliateAccountBtn");

      // Obtener datos del vendedor desde JSON
      let dj = JSON.parse(JSON.stringify({{ json | safe }}));
      let sellers = JSON.parse(dj.seller);
      let shortname = sellers[0].fields.shortname;

      // Debug para mostrar la selección
      debugLog(`-----> Funcion onClickConciliateAccountModal
      - Factura de "${shortname}" select:
        • Factura con invoice_id: ${invoice_id}
        • Factura con reference: ${reference}
        • Monto total (amount = ${amount})
      `);

      amount = parseFloat(amount) || 0;
      debugLog(`amount: ${amount}`);

      // Establecer el valor del input con el monto
      input.val(amount);

      // Establecer los límites del input de acuerdo al monto
      input.attr({
        min: Math.min(0, amount),
        max: Math.max(0, amount),
        step: 0.01
      });

      debugLog(`input.val(): ${input.val()}`);

      // Habilitar todos los valores del select
      select.find("option").attr('disabled', false);

      // Deshabilitar la opción del select con valor '0'
      select.find("option[value='0']").attr('disabled', true);

      // Habilitar input, select y botón
      select.attr("disabled", false);
      input.attr("disabled", false);
      btn.attr("disabled", true);

      // Eliminar eventos anteriores y añadir nuevos
      btn.off("click").on("click", function () {
        const selectedOption = select.find('option:selected');
        const account_id = selectedOption.val();
        const account_description = selectedOption.data('description');

        select.add(input).add(btn).attr("disabled", true);
        debugLog("Valor del input: ", input.val());
        debugLog("Valor del select: ", account_id);
        debugLog("Descripción de la cuenta: ", account_description);
        onClickConciliateAccount(invoice_id, account_id, account_description, input.val());
        account_modal.modal("hide");
      });

      input.off("input").on("input", function () {
        let isValid = (amount >= 0) ? (input.val() > 0 && input.val() <= amount) : (input.val() < 0 && input.val() >= amount);
        btn.attr("disabled", !(select.val() && isValid));
        debugLog("Cambio en el input: ", input.val());
      });

      // Inicializar Select2 en el select del modal
      select.off("change");
      select.select2({
        dropdownParent: account_modal,
        placeholder: "Seleccione una opción",
        allowClear: true,
        theme: 'bootstrap-5',
      });
      select.on("change", function () {
        let isValid = (amount >= 0) ? (input.val() > 0 && input.val() <= amount) : (input.val() < 0 && input.val() >= amount);
        btn.attr("disabled", !(select.val() && isValid));
        debugLog("Cambio en el select: ", select.val());
      });

      // Refrescar el select para asegurar que se muestre la selección
      select.val(null).trigger('change');

      // Mostrar el modal
      account_modal.modal("show");
    }

    // Función llamada al conciliar la cuenta contable
    const onClickConciliateAccount = (invoice_id, account_id, account_description, amount) => {
      // Debug para mostrar la conciliación
      debugLog(`-----> Conciliando contra cuentas contables
        • Id de la factura: ${invoice_id}
        • Cuenta Contable número: ${account_id}
        • Descripción de la Cuenta Contable: ${account_description}
        • Monto seleccionado (amount = ${amount})
      `);
      onClickConciliateInvoiceAccount(invoice_id, account_id, amount);
    };

    // Función para manejar el modal de conciliación de facturas
    const onClickConciliateModal = (invoiceId, invoiceReference, total, shop) => {
      debugLog('onClickConciliateModal');
      let show = false;

      // Establecer la referencia de la factura en el modal
      document.getElementById("concilationModalSpanReference1").innerHTML = invoiceReference;
      document.getElementById("concilationModalSpanReference2").innerHTML = invoiceReference;

      // Establecer el monto de la factura
      document.getElementById("concilationModalSpanAmount").innerHTML = total * 1;
      
      // Establecer el monto del movimiento de la factura
      document.getElementById("concilationModalSpanAmountMovement").innerHTML = -total;
      
      // Establecer el banco relacionado con la factura
      document.getElementById("concilationModalSpanBank").innerHTML = shop;

      // Configurar el botón de acción según la tienda
      if (shop == 'amazon') {
          show = true;
          document.getElementById("concilationModalButton").onclick = () => {
              onClickConciliateAmazon(invoiceId, invoiceReference, total);
          };
      } else {
          show = false;
          debugLog('Tienda Desconocida: ', shop);
      }

      // Mostrar el modal si se cumple la condición
      if (show) {
          $('#concilationModal').modal('show');
      }
    };

    // Función para conciliar una factura con una cuenta contable
    const onClickConciliateInvoiceAccount = (invoiceId, accountId, total) => {
      debugLog('onClickConciliateInvoiceAccount');
      console.log("invoiceId: ", invoiceId);
      console.log("accountId: ", accountId);
      console.log("total: ", total);

      // Obtener el token CSRF
      const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;
      debugLog('CSRF:' + csrfToken);

      // Configurar las notificaciones con SweetAlert
      const Toast = Swal.mixin({
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          didOpen: (toast) => {
              toast.addEventListener('mouseenter', Swal.stopTimer);
              toast.addEventListener('mouseleave', Swal.resumeTimer);
          }
      });

      // Realizar la llamada AJAX para conciliar la factura
      $.ajax({
          type: "POST",
          url: "{% url 'app_banks:bank_unreconciled_invoice_new' seller.shortname %}",
          data: {
              'invoice_id': invoiceId,
              'account_id': accountId,
              'bank_name': 'accounts'
          },
          headers: {
              'X-CSRFToken': csrfToken
          },
          success: function (data) {
              if (data.result == 'ok') {
                debugLog('SUCCESS: ', data);
                // Actualizar la tabla
                table.ajax.reload(null, false);
                Toast.fire({
                  icon: 'success',
                  title: 'Conciliación realizada correctamente.'
                });
              } else {
                debugLog('ERROR: ', data);
                Toast.fire({
                  icon: 'error',
                  title: 'Error al generar la conciliación.'
                });
              }
          },
          error: function (data) {
              debugLog('ERROR: ', data);
              Toast.fire({
                  icon: 'error',
                  title: 'Error al generar la conciliación.'
              });
          }
      });
    };

    // Función para conciliar una factura con Amazon
    const onClickConciliateAmazon = (invoiceId, invoiceReference, total) => {
      debugLog('onClickConciliateAmazon');
      
      // Obtener el token CSRF
      const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;
      debugLog('CSRF:' + csrfToken);

      // Configurar las notificaciones con SweetAlert
      const Toast = Swal.mixin({
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          didOpen: (toast) => {
              toast.addEventListener('mouseenter', Swal.stopTimer);
              toast.addEventListener('mouseleave', Swal.resumeTimer);
          }
      });

      // Realizar la llamada AJAX para conciliar la factura con Amazon
      $.ajax({
          type: "POST",
          url: "{% url 'app_banks:bank_unreconciled_invoice_new' seller.shortname %}",
          data: {
              'invoice_id': invoiceId,
              'bank_name': 'amazon'
          },
          headers: {
              'X-CSRFToken': csrfToken
          },
          success: function (data) {
              if (data.result == 'ok') {
                  debugLog('SUCCESS: ', data);
                  // Actualizar la tabla
                  table.ajax.reload(null, false);
                  Toast.fire({
                      icon: 'success',
                      title: 'Conciliación realizada correctamente.'
                  });

                  // Eliminar la fila correspondiente a la factura en la tabla
                  const fila = table.rows().nodes().to$().filter(function () {
                      return $(this).find('td:first').text() == invoiceId;
                  });
                  if (fila.length > 0) {
                      fila.remove();
                  }
              } else {
                  debugLog('ERROR: ', data);
                  Toast.fire({
                      icon: 'error',
                      title: 'Error al generar la conciliación.'
                  });
              }

              // Ocultar el modal
              $('#concilationModal').modal('hide');
          },
          error: function (data) {
              debugLog('ERROR: ', data);
              Toast.fire({
                  icon: 'error',
                  title: 'Error al generar la conciliación.'
              });

              // Ocultar el modal
              $('#concilationModal').modal('hide');
          }
      });
    };

    // Función para conciliar múltiples facturas con Amazon de forma asíncrona
    async function onClickConciliateMultiAmazon(invoiceId) {
      const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;

      return new Promise((resolve, reject) => {
          $.ajax({
              type: "POST",
              url: "{% url 'app_banks:bank_unreconciled_invoice_new' seller.shortname %}",
              data: {
                  'invoice_id': invoiceId,
                  'bank_name': 'amazon'
              },
              headers: {
                  'X-CSRFToken': csrfToken
              },
              success: function (data) {
                  if (data.result === 'ok') {
                      // Eliminar la fila correspondiente de la tabla
                      const row = table.row((idx, rowData) => rowData.pk == invoiceId);
                      row.remove().draw(false);  // Actualiza la tabla sin recargar la página

                      resolve();  // Resolver la promesa si es exitosa
                  } else {
                      reject();  // Rechazar la promesa si hay error
                  }
              },
              error: function () {
                  reject();  // Rechazar la promesa si hay error
              }
          });
      });
    }

    // EVENTOS ////////////////////////////////////////////////////////////

    // Escuchar cambios en todos los selects y actualizar el estado del botón de limpieza de filtros
    document.querySelectorAll('select').forEach(select => {
      select.addEventListener('change', toggleClearButtonState);
      select.addEventListener('focusout', toggleClearButtonState); // Verificar cuando se pierde el foco
      select.addEventListener('click', toggleClearButtonState);    // Verificar al hacer clic dentro o fuera
    });

    // Escuchar cambios en todos los multi-selectores
    document.querySelectorAll('[separator]').forEach(multiSelector => {
      multiSelector.addEventListener('change', toggleClearButtonState);
      multiSelector.addEventListener('focusout', toggleClearButtonState); // Verificar cuando se pierde el foco
      multiSelector.addEventListener('click', toggleClearButtonState);    // Verificar al hacer clic dentro o fuera
    });

    // Evento para manejar la conciliación múltiple con Amazon
    document.getElementById('multiConciliateAmazonButton').addEventListener('click', async function () {
      const selectedPk = JSON.parse(document.getElementById('selectedPk').value);  // Facturas seleccionadas
      let successCount = 0;
      let failedInvoices = [];

      // Ocultar el contenido original del modal y mostrar el spinner
      document.getElementById('multiConciliateAmazonModalBody').style.display = 'none';
      document.getElementById('processingSpinnerModal').style.display = 'block';

      // Deshabilitar botones del modal durante el procesamiento
      document.querySelector('#multiConciliateAmazonModal .modal-footer').style.display = 'none';

      // Procesar cada factura seleccionada
      for (const invoiceId of selectedPk) {
          try {
              await onClickConciliateMultiAmazon(invoiceId);  // Llamada a la función para cada factura
              successCount++;
          } catch (error) {
              failedInvoices.push(invoiceId);  // Si falla, guardar el ID de la factura
          }
      }

      // Ocultar el spinner cuando termine el proceso
      document.getElementById('processingSpinnerModal').style.display = 'none';

      // Mostrar un solo toast al final del proceso
      setTimeout(() => {
          if (failedInvoices.length === 0) {
              // Mostrar toast de éxito
              Swal.fire({
                  icon: 'success',
                  title: 'Todas las facturas fueron conciliadas con éxito',
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 3000
              });
          } else {
              // Mostrar toast de advertencia con los errores
              Swal.fire({
                  icon: 'warning',
                  title: 'Algunas facturas no pudieron ser procesadas',
                  text: `Hubo errores con las siguientes facturas: ${failedInvoices.join(', ')}`,
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 4000
              });
          }
      }, 2000);  // Retraso de 2 segundos

      // Cerrar el modal
      $('#multiConciliateAmazonModal').modal('hide');

      // Restaurar el contenido original del modal para la próxima vez
      document.getElementById('multiConciliateAmazonModalBody').style.display = 'block';
      document.querySelector('#multiConciliateAmazonModal .modal-footer').style.display = 'flex';
    });

    // Función para realizar la conciliación de una factura contra una cuenta contable
    async function conciliateInvoiceWithAccount(invoiceId, accountId) {
      const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;

      return new Promise((resolve, reject) => {
          $.ajax({
              type: "POST",
              url: "{% url 'app_banks:bank_unreconciled_invoice_new' seller.shortname %}",
              data: {
                  'invoice_id': invoiceId,
                  'account_id': accountId,
                  'bank_name': 'accounts' // Especifica que es para cuentas contables
              },
              headers: {
                  'X-CSRFToken': csrfToken
              },
              success: function (data) {
                  if (data.result === 'ok') {
                      // Eliminar la fila correspondiente de la tabla reactivamente
                      const row = table.row((idx, rowData) => rowData.pk == invoiceId);
                      row.remove().draw(false);  // Actualiza la tabla sin recargar la página

                      resolve();  // Resolver la promesa si es exitosa
                  } else {
                      reject();  // Rechazar la promesa si hay error
                  }
              },
              error: function () {
                  reject();  // Rechazar la promesa si hay error
              }
          });
      });
    }

    // Función para manejar la conciliación múltiple con cuentas contables
    document.getElementById('multiConciliateAccountButton').addEventListener('click', async function () {
      const selectedPk = JSON.parse(document.getElementById('selectedPk').value);  // Facturas seleccionadas
      const selectedAccountId = $('#accountSelectorMulti').val();  // Cuenta contable seleccionada
      let successCount = 0;
      let failedInvoices = [];

      // Validar selección de cuenta contable
      if (!selectedAccountId || selectedAccountId === '0') {
          Swal.fire({
              icon: 'warning',
              title: 'Debe seleccionar una cuenta contable',
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              timer: 3000
          });
          return;
      }

      // Ocultar contenido original y mostrar spinner
      document.getElementById('multiConciliateAccountModalBody').style.display = 'none';
      document.getElementById('processingSpinnerAccountModalMulti').style.display = 'block';

      // Deshabilitar botones durante el procesamiento
      document.querySelector('#multiConciliateAccountModal .modal-footer').style.display = 'none';

      // Procesar todas las facturas seleccionadas
      for (const invoiceId of selectedPk) {
          try {
              await conciliateInvoiceWithAccount(invoiceId, selectedAccountId);  // Esperar la conciliación de cada factura
              successCount++;
          } catch (error) {
              failedInvoices.push(invoiceId);  // Si falla, guardar el ID de la factura
          }
      }

      // Ocultar el spinner cuando termine el proceso
      document.getElementById('processingSpinnerAccountModalMulti').style.display = 'none';

      // Mostrar un solo toast al final del proceso
      setTimeout(() => {
          if (failedInvoices.length === 0) {
              // Mostrar toast de éxito
              Swal.fire({
                  icon: 'success',
                  title: 'Todas las facturas fueron conciliadas con éxito',
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 3000
              });
          } else {
              // Mostrar toast de advertencia con los errores
              Swal.fire({
                  icon: 'warning',
                  title: 'Algunas facturas no pudieron ser procesadas',
                  text: `Hubo errores con las siguientes facturas: ${failedInvoices.join(', ')}`,
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 4000
              });
          }
      }, 2000);  // Retraso de 2 segundos

      // Cerrar el modal
      $('#multiConciliateAccountModal').modal('hide');

      // Actualizar la tabla eliminando filas de facturas conciliadas correctamente
      if (successCount > 0) {
          for (const invoiceId of selectedPk) {
              if (!failedInvoices.includes(invoiceId)) {
                  const row = table.row((idx, rowData) => rowData.pk == invoiceId);
                  row.remove().draw(false);  // Eliminar dinámicamente las filas exitosas
              }
          }
      }

      // Restaurar el contenido original del modal
      document.getElementById('multiConciliateAccountModalBody').style.display = 'block';
      document.querySelector('#multiConciliateAccountModal .modal-footer').style.display = 'flex';
    });

    // Escuchar eventos del campo de búsqueda de facturas
    search_invoice.addEventListener("input", () => {
      clearTimeout(timer);
      timer = setTimeout(filter, 500);
    });

    // Manejar la selección y deselección de filas en la tabla de facturas
    $('#invoices-table').on('click', 'input.custom-checkbox[data-select="true"]', function (e) {
      e.stopPropagation(); // Evita que el clic se propague

      var row = $(this).closest('tr');
      var dataTable = $('#invoices-table').DataTable();

      if (dataTable.row(row).nodes().to$().hasClass('selected')) {
          dataTable.row(row).deselect();
      } else {
          dataTable.row(row).select();
      }
    });

    // Manejar la selección de todas las filas en la tabla
    $('#select_all').on('change', function () {
      var isChecked = $(this).prop('checked');
      var rows = table.rows({page: 'current'}).nodes();

      // Selecciona o deselecciona todas las filas según el estado de la casilla
      $(rows).find('input[type="checkbox"]').prop('checked', isChecked);

      // Disparar el evento 'select.dt' después de seleccionar todas las filas
      if (isChecked) {
          table.rows({page: 'current'}).select();
      } else {
          table.rows({page: 'current'}).deselect();
      }
    });

    // Evento para manejar la selección de filas en la tabla
    $('#invoices-table').on('select.dt', function (e, dt, type, indexes) {
      let rowData = dt.rows(indexes).data().toArray();
      let status = dt.rows(indexes).data().toArray();

      arrayRowData = arrayRowData.concat(rowData.map(row => row.pk));
      arrayStatus = arrayStatus.concat(status.map(row => row.status));

      document.getElementById("selectedPk").value = JSON.stringify(arrayRowData);
      if (arrayRowData.length > 0) {
          document.getElementById("selectValue").removeAttribute("disabled");
      }
      urlForm();
    });

    // Evento para manejar la deselección de filas en la tabla
    $('#invoices-table').on('deselect.dt', function (e, dt, type, indexes) {
      let rowDataDelete = dt.rows(indexes).data().toArray();
      let statusDelete = dt.rows(indexes).data().toArray();
      let pkValue = rowDataDelete[0].pk;
      let index = arrayRowData.indexOf(pkValue);

      arrayStatus.splice(index, 1);
      arrayRowData = arrayRowData.filter(row => !rowDataDelete.some(r => r.pk === row));

      document.getElementById("selectedPk").value = JSON.stringify(arrayRowData);
      if (arrayRowData.length == 0) {
          document.getElementById("selectValue").setAttribute("disabled", "disabled");
          document.getElementById("selectValue").value = "empty";
          // document.getElementById("massiveAction").style.display = "none";
      }
      urlForm();
    });

    // Función para actualizar el formulario de selección masiva
    function urlForm() {
      const selectValue = document.getElementById('selectValue').value;
      const form = document.getElementById('formSelect');

      // Verificar si se ha seleccionado alguna opción de conciliación
      if (selectValue !== 'empty' && arrayRowData.length > 0) {
          // Habilitar el botón de confirmar solo si hay facturas seleccionadas
          document.getElementById('enabledButton').disabled = false;
      } else {
          document.getElementById('enabledButton').disabled = true;
      }
    }

    // Evento para manejar el envío del formulario de selección masiva
    document.getElementById('formSelect').addEventListener('submit', function (e) {
      e.preventDefault();  // Evitar que el formulario se envíe por HTTP

      const selectValue = document.getElementById('selectValue').value;

      if (selectValue === 'conciliationAMZ') {
          // Mostrar modal de confirmación de Amazon
          $('#multiConciliateAmazonModal').modal('show');
      } else if (selectValue === 'conciliationCC') {
          // Mostrar modal de conciliación de cuentas contables
          $('#multiConciliateAccountModal').modal('show');
      }
    });

    // INICIALIZACIÓN ////////////////////////////////////////////////////////////

    // Configurar y crear la tabla de facturas al cargar el documento
    $(document).ready(function () {
      const categoryTable = '{{ category }}' || 'All';
      const filterCookieName = `DataTable_Invoices_List_${categoryTable}`;
      const invoicesURL = window.location.href;

      const currentYear = new Date().getFullYear();
      const yearInput = document.getElementById('year');
      
      onChangeYear()
      addFiltersBadge(yearInput);
      dropdownFilterFormHack();
      createDT();

      $("#invoiceModalIframe").on("load", showIframeModalInvoices);

      // Habilitar el botón de conciliar solo cuando se seleccione una cuenta contable válida
      $('#accountSelectorMulti').on('change', function () {
          if ($(this).val() !== '0') {
              $('#multiConciliateAccountButton').prop('disabled', false);
          } else {
              $('#multiConciliateAccountButton').prop('disabled', true);
          }
      });

      const dropdownButton = document.getElementById('dropdownButton');
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');

      // Mostrar u ocultar el formulario de filtros
      dropdownButton.addEventListener('click', function (event) {
          dropdownFiltersForm.style.display = dropdownFiltersForm.style.display === 'block' ? 'none' : 'block';
          if (dropdownFiltersForm.style.display === 'block') {
              dropdownFiltersForm.scrollIntoView({behavior: "smooth", block: "center", inline: "center"});
          }
      });

      document.addEventListener('click', function (event) {
          if (!dropdownFiltersForm.contains(event.target) && !dropdownButton.contains(event.target)) {
              dropdownFiltersForm.style.display = 'none';
          }
      });

      dropdownFiltersForm.addEventListener('click', function (event) {
          hideMultiDropDown(event);
          event.stopPropagation();
      });

      function hideMultiDropDown(event) {
          const multiSelectorElements = document.querySelectorAll('[separator]');

          multiSelectorElements.forEach((element) => {
              if (!event.composedPath().includes(element)) {
                  element.dropDownVisible = false;
                  element.hideDropDown();
              }
          });
      }

      
      addListenerToMultiSelector("multiple-month");
      addListenerToMultiSelector("multiple-departurecountries");
      addListenerToMultiSelector("multiple-transactions");
      addListenerToMultiSelector("multiple-invoicetype");
      
      handleMultiCheckboxChange(multipleMonth);
      handleMultiCheckboxChange(multipleDepartureCountries);
      handleMultiCheckboxChange(multiCheckbox);
      handleMultiCheckboxChange(multipleInvoiceType);

      const selectFilters = filterForm.querySelectorAll('select');
      selectFilters.forEach(select =>
        select.addEventListener('change', () => {
          addFiltersBadge(select);
          checkFilterState();
        })
      );
      
      resetCookie(filterCookieName);
      toggleApplyFiltersButton(false);
    });

    //***** INICIO | MANEJO DINÁMICO DE FILTROS ********//
    
    // Añadir badge de filtros si se han aplicado
    function addFiltersBadge(element) {
      const elementID = element.id;
      filtersDictCount[elementID] = !!element.value || !!element.dataset.value;
    
      const badgeNumber = Object.values(filtersDictCount).filter(value => value === true).length;
      const badge = document.getElementById('id-filter-notification');
    
      if (badgeNumber > 0) {
        badge.classList.remove('d-none');
        badge.innerHTML = badgeNumber;
      } else {
        badge.classList.add('d-none');
        badge.innerHTML = '';
      }
    }
    
    // DYNAMIC HANDLING FOF MULTICHECK FILTERS
    function handleMultiCheckboxChange(element, callback) {

      // Verificar si el elemento existe
      if (!element) {
        console.warn("Elemento no encontrado para manejar el cambio de checkbox múltiple.");
        return; // Salir de la función si el elemento no existe
      }
    
      // Si el elemento existe, aplicar el evento
      element.addEventListener('change', async function () {
        addFiltersBadge(element);
        checkFilterState();
        if (callback) await callback();
      });
    }

    function addListenerToMultiSelector(selectorId, changeIvaCallback = null) {
      const element = document.getElementById(selectorId);
      if (!element) {
        console.warn(`Elemento con id ${selectorId} no encontrado.`);
        return; // Salir de la función si el elemento no existe
      }
    
      // Intentar acceder al shadowRoot o continuar sin él
      const shadowRoot = element.shadowRoot || element; // Usar el elemento directamente si no hay shadowRoot
      const liElements = shadowRoot.querySelectorAll('ul li');
      const selectAllInput = shadowRoot.querySelector('#select-all');
    
      const handleClick = (e) => {
        if (changeIvaCallback) {
          changeIvaCallback();
        }
        e.stopPropagation();
        addFiltersBadge(element);
      };
    
      // Añadir listeners a los elementos <li> y al checkbox de selección general
      liElements.forEach((li) => {
        li.addEventListener('click', handleClick);
      });
    
      if (selectAllInput) {
        selectAllInput.addEventListener('click', handleClick);
      }
    }

    // Activar o desactivar el botón de limpiar filtros
    function toggleClearButtonState() {
      const clearFiltersButton = document.getElementById("clearFiltersButton");
      if (filtersSelected()) {
          clearFiltersButton.disabled = false; // Activa el botón
      } else {
          clearFiltersButton.disabled = true; // Desactiva el botón
      }
    }

    // Verificar si hay algún filtro seleccionado
    function filtersSelected() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');

      // Verifica si alguno de los selects o multiselects tiene un valor seleccionado
      return Array.from(selectInputs).some(select => select.value) || Array.from(multiSelectors).some(multiSelector => multiSelector.dataset.value);
    }

    // Activar o desactivar el botón de aplicar filtros
    function toggleApplyFiltersButton(state) {
      const applyFiltersButton = document.getElementById("applyFiltersButton");
      applyFiltersButton.disabled = !state;
    }

    // Aplicar los filtros seleccionados
    function applyFilters() {
      if (checkFilterState()) {
        filter();
      }
      setCookieFilterState();
      toggleApplyFiltersButton(false);
      // Cerrar el desplegable de filtros con un pequeño retraso
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      setTimeout(() => {
        dropdownFiltersForm.style.display = 'none';
      }, 2000); // 2 segundos
    }

    // Restablecer todos los filtros
    function resetFilters() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      // Restablecer los valores de los selects
      selectInputs.forEach((select) => {
        select.value = '';
      });
      // Restablecer los valores de los multi-selectores
      multiSelectors.forEach((multiSelector) => {
        multiSelector.dataset.value = ''; // Resetear
        multiSelector.value = '';
        multiSelector.updateItems(); // Actualizar
      });
      // Llamada a las funciones para deshabilitar los selectores
      onChangeYear();
      // Filtrar los resultados y actualizar el estado del botón
      filter();
      clearFiltersBadge();
      setCookieFilterState();
      toggleApplyFiltersButton(false);
      // Cerrar el desplegable de filtros con un pequeño retraso
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      setTimeout(() => {
        dropdownFiltersForm.style.display = 'none';
      }, 2000); // 2 segundos
    }

    // Limpiar el badge de filtros
    const clearFiltersBadge = () => {
      Object.keys(filtersDictCount).forEach(key => filtersDictCount[key] = false);
      const badge = document.getElementById('id-filter-notification');
      badge.classList.add('d-none');
      badge.innerHTML = '';
    }

    // Obtener los valores de los filtros seleccionados
    function getFilterValues() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      let filterDict = {};

      // Guardar los valores de los selects en el diccionario
      selectInputs.forEach((select) => {
          filterDict[select.id] = select.value;
      });

      // Guardar los valores de los multi-selectores en el diccionario
      multiSelectors.forEach((multiSelector) => {
          if (multiSelector.dataset.value) {
              filterDict[multiSelector.id] = multiSelector.dataset.value.split(multiSelector.getAttribute('separator'));
          } else {
              filterDict[multiSelector.id] = [];
          }
      });

      return filterDict;
    }

    // Verificar el estado de los filtros activos
    function checkFilterState() {
      const currentFilter = getFilterValues();
      const categoryTable = '{{ category }}' || 'All';
      const cookieName = `DataTable_Invoices_List_${categoryTable}`;
      const cookieFilter = JSON.parse(getCookie(cookieName) || '{}');

      toggleApplyFiltersButton(JSON.stringify(currentFilter) !== JSON.stringify(cookieFilter));

      return JSON.stringify(currentFilter) !== JSON.stringify(cookieFilter);
    }

    //***** FIN | MANEJO DINÁMICO DE FILTROS Y COOKIES ********//

    // Hack para mostrar el formulario de filtros dropdown
    const dropdownFilterFormHack = () => {
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      dropdownFiltersForm.style.opacity = '1';
      dropdownFiltersForm.style.display = 'none';
    }

    function getCookie(name) {
      const cookie = document.cookie.split('; ').find(row => row.startsWith(name + '='));
      return cookie ? decodeURIComponent(cookie.split('=')[1]) : null;
    }

    function setCookieFilterState() {
      const filter = getFilterValues();
      const categoryTable = '{{ category }}' || 'All';
      const cookieName = `DataTable_Invoices_List_${categoryTable}`;
      document.cookie = `${cookieName}=${JSON.stringify(filter)}; path=/`;
    }

    function resetCookie(name) {
      const defaultValues = getFilterValues();
      document.cookie = `${name}=${JSON.stringify(defaultValues)}; path=/`;
    }

    // Manejar el cambio de año y habilitar/deshabilitar el mes
    const onChangeYear = () => {
      const year = document.getElementById("year");
      const period = document.getElementById("multiple-month");
      debugLog("EL AÑO ES: ", year.value);
      if (year && year.value) {
        period.removeAttribute("disabled");  // Habilitar selector de meses
      } else {
        period.setAttribute("disabled", "disabled");  // Deshabilitar selector de meses
        period.value = '';  // Limpia el valor del selector
        addFiltersBadge(period);
      }
    }
  </script>
{% endblock javascripts %}
