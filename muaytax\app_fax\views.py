import json
from datetime import datetime

from django.conf import settings
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.mail import send_mail
from django.http import HttpResponseRedirect, HttpResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import FormView

from muaytax.app_documents.models.presented_model import PresentedModel
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.email_notifications.fax_notifications import send_fax_notification, send_discard_model_fax
from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from .forms import Model5472SignedForm
from .utils import get_fax_report, get_fax, validate_email, retry_send_fax

EMAIL_SENDING_ERROR = '<EMAIL>'


class FaxView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), FormView):
    form_class = Model5472SignedForm
    template_name = 'fax/fax_signed.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['presented_model'] = get_object_or_404(PresentedModel, pk=self.kwargs['pk'], )
        context['seller'] = get_object_or_404(Seller, shortname=self.kwargs['shortname'])
        signed = context['form'].instance.m5472_signed
        context['is_valid'] = signed and signed.name.lower().endswith('.pdf')
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['instance'] = get_object_or_404(PresentedModel, pk=self.kwargs['pk'])
        return kwargs

    def form_valid(self, form):
        pm = form.instance
        # si el boton presionado es 'Enviar Fax'
        if 'send_fax' in self.request.POST:
            if pm.model_id == 'US-5472':
                path = form.cleaned_data['m5472_signed'].path

            elif pm.model_id != 'US-5472':
                path = f"/app/muaytax{form.instance.get_file_url()}"
            email = form.instance.seller.user.email

            from_number = settings.FAXPLUS_SENDING_NUMBER
            to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER]
            if pm.model_id == 'US-5472':
                to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER_US_5472]
            elif pm.model_id == 'US-7004':
                to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER_US_7004]
            elif pm.model_id == 'US-BE15':
                to_number_list = [settings.FAXPLUS_RECEIVING_NUMBER_US_BE15]
                
            response = retry_send_fax(path, from_number, to_number_list, email)
            if 'error' in response:
                form.add_error('m5472_signed', f'FaxPlus Error {response["error"]}: {response["description"]}')
                return super().form_invalid(form)

            fax_id = list(response['ids'].values())[0]
            form.instance.fax_destination_id = fax_id
            form.instance.save()

            messages.success(
                self.request,
                _('El fax se ha enviado correctamente. Espere la respuesta en su correo electrónico.'),
                extra_tags='fax'
            )

        # si el boton presionado es 'Descartar modelo'
        elif 'discard_model' in self.request.POST:
            inputDiscardReasonNotes = self.request.POST.get('inputDiscardReasonNotes')
            # el modelo pasa a pendiente
            pm.status = ModelStatus.objects.get(code='pending')
            # se elimina m5472_signed
            pm.m5472_signed.delete()
            # enviar correo al vendedor de que el fax ha sido descartado por el siguiente motivo
            send_discard_model_fax(self.request, pm, inputDiscardReasonNotes)

            messages.success(self.request, _('El modelo ha sido descartado correctamente.'), extra_tags='fax')

        return super().form_valid(form)

    def get_success_url(self):
        return reverse('app_documents:presented_model_list', kwargs={'shortname': self.kwargs['shortname']})

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


@method_decorator(csrf_exempt, name='dispatch')
class FaxPlusWebhookView(View):
    def post(self, request, *args, **kwargs):
        # Convierte la respuesta JSON del webhook en un diccionario de Python
        webhook_json = json.loads(request.body)

        hook = webhook_json['hook']
        data = webhook_json['data']

        # hook_example = {
        #     'id': 'b0d56754f22942259c2573067da7591d',
        #     'event': 'fax_sent',
        #     'event_time': '2024-04-10 16:44:42',
        #     'target': 'https://some-url.com/fax-webhook/'
        # }

        # data_example = {
        #     'id': 'b0d56754f22942259c2573067da7591d',
        #     'uid': '4cdc05e1570f475999f7979602b87be9',
        #     'pages': 1,
        #     'from': '+1 ***-***-****',
        #     'to': '+1 ***-***-****',
        #     'start_time': '2000-01-01 00:00:00',
        #     'file': '274eff6ba2e64144b7379a4201505f6d.tiff',
        #     'file_name': 'fax-to-16469076027',
        #     'status': 'success',
        #     'cost': 1.0
        # }

        fax_id = data['id']
        status = data['status']
        event = hook['event']

        # si el evento es 'fax_sent'
        if event == 'fax_sent':
            pm = PresentedModel.objects.filter(fax_destination_id=fax_id).first()
            if pm:
                if status == 'success':
                    # cambiar el estado del modelo presentado a 'presented'
                    pm.status = ModelStatus.objects.get(code='presented')

                    # guardar el informe del fax en el modelo presentado
                    file_name = f'{datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}_{pm.seller.shortname}_{pm.id}_report-fax.pdf'
                    pm.report_fax.save(file_name, open(get_fax_report(fax_id), 'rb'))

                    if pm.model.code == 'US-5472':
                        # enviar un correo electrónico al vendedor
                        send_fax_notification(request, pm)

                else:
                    pm.fax_destination_id = ''
                    pm.save()
                    # enviar <NAME_EMAIL> de que hubo un error de parte de Fax+

                    email = pm.seller.user.email

                    send_mail(
                        subject='FaxPlus Webhook Error',
                        message=f'Ha habido un error al enviar el fax de {email}: {status}',
                        from_email='<EMAIL>',
                        recipient_list=[EMAIL_SENDING_ERROR]
                    )

            else:
                fax = get_fax(fax_id).to_dict()
                email = fax['comment'].strip()
                if validate_email(email):
                    send_mail(
                        subject='FaxPlus Webhook Error',
                        message=f'Ha habido un error al enviar el fax de {email}',
                        from_email='<EMAIL>',
                        recipient_list=[EMAIL_SENDING_ERROR]
                    )

        # si el evento es 'fax_received'
        else:
            pass

        # Responde al webhook para confirmar la recepción
        return HttpResponse('Webhook recibido', content_type='text/plain')
