import os
import re

# === CONFIGURACIÓN ===
BASE_DIR = os.path.dirname(__file__)
CSS_FILE = os.path.join(BASE_DIR, "allstyle.css")
OUTPUT_USED = os.path.join(BASE_DIR, "style_formater.css")
OUTPUT_UNUSED = os.path.join(BASE_DIR, "style_restos.css")

TEMPLATE_PATHS = [
    "muaytax/templates/sellers/seller_vat_request_service_iva.html",
    "muaytax/templates/sellers/seller_vat_review_manager_service_iva.html"
]

INCLUDE_DIR = "muaytax/templates/sellers/include/service_iva"
for file in os.listdir(INCLUDE_DIR):
    if file.endswith(".html"):
        TEMPLATE_PATHS.append(os.path.join(INCLUDE_DIR, file))

# === Palabras clave para mantener siempre ===
keywords_to_keep = [
    ":root", ".hidden", ".tooltip-icon", ".accordion-section", "@keyframes shake", ".shake",
    ".readonly-style", ".form-control[readonly]", ".invalid-feedback", ".swal-", ".modal-",
    ".btn_", ".back_btn", ".edit_btn", "#partnerTable", ".table-partners", ".select2-", ".choices__",
    ".form-group", "#deactivation_info", ".card-header-grey"
]

# === Función para cargar todos los templates y combinarlos como texto plano ===
def load_template_content(paths):
    full_text = ""
    for path in paths:
        with open(path, encoding="utf-8") as f:
            full_text += f.read().lower()
    return full_text

# === Función para extraer todos los bloques de estilo del archivo CSS ===
def extract_css_blocks(css_text):
    pattern = re.compile(r'([^{]+)\{([^}]+)\}', re.MULTILINE)
    return pattern.findall(css_text)

# === Función para determinar si un bloque debe conservarse ===
def is_block_used(selector, html_text):
    selector_clean = selector.strip().lower()
    # Buscar clases, ids y otros selectores en el HTML
    possible_matches = re.findall(r'[\w\-\#\.\:\[\]=]+', selector_clean)
    for piece in possible_matches:
        if any(piece.startswith(kw) for kw in keywords_to_keep):
            return True
        if piece in html_text:
            return True
    return False

# === MAIN ===
with open(CSS_FILE, encoding="utf-8") as f:
    css_text = f.read()

css_blocks = extract_css_blocks(css_text)
html_text = load_template_content(TEMPLATE_PATHS)

used_blocks = []
unused_blocks = []

for selector, rules in css_blocks:
    block = f"{selector.strip()} {{{rules.strip()}}}\n\n"
    if is_block_used(selector, html_text):
        used_blocks.append(block)
    else:
        unused_blocks.append(block)

with open(OUTPUT_USED, "w", encoding="utf-8") as f:
    f.write("/* === Estilos Usados o Críticos === */\n\n")
    f.writelines(used_blocks)

with open(OUTPUT_UNUSED, "w", encoding="utf-8") as f:
    f.write("/* === Estilos No Detectados como Usados === */\n\n")
    f.writelines(unused_blocks)

print(f"[✔] Generado: {OUTPUT_USED}")
print(f"[✔] Generado: {OUTPUT_UNUSED}")
