{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}
{% block stylesheets %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/loading.css"/>
  <script type="module" src="{{ STATIC_URL }}assets/js/plugins/multi-checkbox.js"></script>
  <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables-v2.0.8.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v2.0.8.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.10.25.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css" type="text/css"/>
  <style>
    /* styles data table start */
    thead tr th:after, thead tr th:before {
      display: none !important;
    }
    .dropdown-menu.show:before {
      display: none;
    }
    .column-width-limit {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 13vw;
    }
    table.table.dataTable>tbody>tr {
      background-color: #fff!important;
    }
    #invoices-table tbody tr:hover td {
      background: #d6d6d6d2!important;
    }
    table tr td, table tr th {
      vertical-align: middle;
      border: none;
    }
    table.dataTable tbody tr td.select-checkbox:before {
      display: none;
    }

    table.dataTable tbody tr.selected td.select-checkbox:after {
      display: none;
    }
    input#miId,
    input#select_all {
      margin-left: 8px;
      width: 14px;
      height: 14px;
    }
    .table-head {
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    /* styles datatable end */

    /* multiselectors style */
    multi-accountingaccount,
    multi-transaction,
    multi-status,
    multi-invoicetype,
    multi-month,
    multi-economicactivity,
    multi-arrivalcountry,
    multi-departurecountry,
    multi-taxcountry,
    multi-origin,
    multi-reverse-charge,
    multi-eqtax,
    multi-vatrates {
      /* Element */
      --mc-z-index: 8 !important;
      --mc-cont-z-index: 20 !important;
      --mc-border: 1px solid #ced4da;;
      --mc-display: inline-block;
      --mc-font: 14px !important;
      --mc-margin: 0;
      --mc-vertical-align: middle;
      /* Dropdown */
      --mc-dropdown-background: #fff;
      --mc-dropdown-box-shadow: 0 1px 2px 0 rgba(57, 70, 92, .05);
      --mc-dropdown-max-height: 350px;
      --mc-dropdown-text-align: left;
      --mc-dropdown-width: 100%;
      /* Input */
      --mc-target-background: #fff;
      --mc-target-color: #000000;
      --mc-target-cursor: pointer;
      --mc-target-margin: 0;
      --mc-target-padding: 0px 0px 0px 3px;
      --mc-target-outline: none;
      --mc-vertical-align: middle;
      --mc-target-width: 268px;
      /* Dropdown Line Items */
      --mc-dropdown-line-height: 2em;
      --mc-ul-margin: 0;
      --mc-ul-padding: 0;
      --mc-checkbox-height: 30px;
      --mc-checkbox-width: 20px;
      --mc-li-cursor: pointer;
      /* Toggle Button */
      --mc-toggle-button-background: #ffffff;
      --mc-toggle-button-color: #000000;
      --mc-toggle-button-cursor: pointer;
      --mc-toggle-button-height: 46px;
      --mc-toggle-button-outline: none;
      --mc-toggle-button-width: 23px;
      /* Close Button */
      --mc-close-button-background: #efefef;
      --mc-close-button-border: none;
      --mc-close-button-border-radius: default;
      --mc-close-button-color: #000000;
      --mc-close-button-cursor: pointer;
      --mc-close-button-display: block;
      --mc-close-button-height: 22px;
      --mc-close-button-margin: 5px auto;
      --mc-close-button-outline: none;
      --mc-close-button-width: 22px;
    }
    #multi-value:hover {
      background-color: #0d6efd;
      --mc-dropdown-color: #fff;
    }
    #multi-value:hover div {
      color: #fff;
    }
    /* multiselectors style */

    /* accordion reset settings */
    .accordion-button:not(.collapsed) {
      color: #111;
      background-color: transparent;
      box-shadow: none;
    }
    .accordion-button:focus {
      z-index: 3;
      border-color: #86b7fe;
      outline: 0;
      box-shadow: none;
    }
    /* accordion reset settings */

    /* table in details */
    #tableColapse1 table tr td:first-child,
    #tableColapse2 table tr td:first-child,
    #tableColapse3 table tr td:first-child{
      min-width: max-content;
    }
    .table-hover tbody tr:hover, .table-striped tbody tr:nth-of-type(odd) {
      background-color: #e8eaed99!important;
    }
    /* table in details */

    .list-action-btn-block{
      position: relative;
    }
    .list-action-btn-block > *{
      font-size: 12px;
      margin-bottom: unset!important;
      margin-right: unset!important;
    }
    .list-action-btn-block button{
      min-width: max-content;
    }
    .dropdown-form {
      display: block;
      min-width: 500px;
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      padding: 10px;
      z-index: 1000;
      transform: translate(-70%, 10px);
      opacity: 0;
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    .btn.dropdown-toggle:after {
      display: none
    }

    /* Estilos para el selector de filas por página */
    .list-action-btn-block select.form-select {
      position: relative;
      padding-right: 2.5rem !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 0.75rem center;
      background-size: 16px 12px;
      transition: background-image 0.2s ease;
    }

    .list-action-btn-block select.form-select.dropdown-open {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14 11l-6-6-6 6'/%3e%3c/svg%3e");
    }

    .top-right-badge{
      position: absolute!important;
      top: -10px!important;
      right: -5px!important;
      width: 25px!important;
      height: 25px!important;
      align-items: center;
      display: flex;
      justify-content: center;
    }
    .search-container {
      display: inline-block;
      position: relative;
      width: 200px;
      transition: width 0.4s ease-in-out;
    }
    .search-container > input {
      font-size: 12px;
    }
    .search-container:focus-within {
      width: 100%; /* Expanded width */
    }
  </style>
{% endblock stylesheets %}
{% block title %}
  Facturas
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas: Listado Facturas
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:summary' seller.shortname %}">
                    {% if seller.name is not None %}
                      {{ seller.name.capitalize }}
                    {% else %}
                      Resumen
                    {% endif %}
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Facturas</a>
                </li>
                {% if transfer %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_invoices:seller_invoices_transfers' seller.shortname %}">Transfers</a>
                  </li>
                {% endif %}
                {% if category %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname category.pk %}">Facturas de {{ category }}</a>
                  </li>
                {% endif %}
              </ul>
            </div>
            {% if perms.users.is_superuserAPP or perms.invoices.change_invoice or perms.invoices.add_invoice %}
            <div class="col col-auto" style="display: none;">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}uploadtxt">Subir TXT Amazon</a>
                </li>
              </ul>
            </div>
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}upload">Subir Facturas</a>
                </li>
              </ul>
            </div>
            {% endif %}
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'expenses' %}">Facturas de Gasto</a>
                </li>
              </ul>
            </div>
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'sales' %}">Facturas de Venta</a>
                </li>
              </ul>
            </div>
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Todas las Facturas</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <!-- Totals | START -->
    {% if category %}
      {% if category.pk == "sales" or category.pk == "expenses" %}
        <div class="vue">
          <div class="row mt-3 mb-0" style="display: none;" v-show="true">
            <div class="col-md-12 col-lg-6 col-xl-4">
              <div class="card rounded-3 shadow-none border">
                <div class="card-body">
                  {% if category.pk == 'sales' %}
                    <h6 class="mt-0 fw-bold">Entradas</h6>
                    <span :class="['my-2', 'h3', 'fw-bold', getPosNegClass(invoiceDataJSON.total_sales_amount, 1), ]">
                      [[ invoiceDataJSON.total_sales ]] €
                    </span>
                    <i class="fas fa-level-down-alt float-end f-30 text-success"></i>
                    {% if is_self_employed and seller.eqtax == True %}
                      <h6 class="mt-0 mb-3 text-muted">(Base + IVA + Req. Eq.)</h6>
                    {% else %}
                      <h6 class="mt-0 mb-3 text-muted">(Base + IVA)</h6>
                    {% endif %}
                  {% elif category.pk == 'expenses' %}
                    <h6 class="mt-0 fw-bold">Salidas</h6>
                    <span :class="['my-2', 'h3', 'fw-bold', getPosNegClass(invoiceDataJSON.total_expenses, -1), ]">
                      [[ invoiceDataJSON.total_expenses ]] €
                    </span>
                    <i class="fas fa-level-up-alt float-end f-30 text-danger"></i>
                    {% if is_self_employed and seller.eqtax == True %}
                      {% if seller.is_direct_estimation %}
                        <h6 class="mt-0 mb-3 text-muted" style="min-width: max-content;">(Base + IVA + Req. Eq. + Gastos dif.just.)</h6>
                      {% else  %}
                        <h6 class="mt-0 mb-3 text-muted">(Base + IVA + Req. Eq.)</h6>
                      {% endif %}
                    {% elif is_self_employed and seller.is_direct_estimation  %}
                      <h6 class="mt-0 mb-3 text-muted">(Base + IVA + Gastos dif.just.)</h6>
                    {% else %}
                      <h6 class="mt-0 mb-3 text-muted">(Base + IVA)</h6>
                    {% endif %}
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xl-8">
              <div class="card rounded-3 shadow-none border">
                <div class="card-body">
                  {% if category.pk == 'sales' %}
                  <table class="table-striped" style="width: 100%;">
                    <tr>
                      <td >Base</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_amount, 1), ]">
                        [[ invoiceDataJSON.total_sales_amount ]] €
                      </td>
                    </tr>
                    <tr>
                      <td >IVA</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_vat, 1), ]">
                        [[ invoiceDataJSON.total_sales_vat ]] €
                      </td>
                    </tr>
                    {% if is_self_employed and seller.eqtax == True %}
                    <tr>
                      <td >Req. Equivalencia</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_eqtax, 1), ]">
                        [[ invoiceDataJSON.total_sales_eqtax ]] €
                      </td>
                    </tr>
                    {% endif %}
                    <tr>
                      <td >IRPF</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_irpf, 1), ]">
                        [[ invoiceDataJSON.total_sales_irpf ]] €
                      </td>
                  </tr>
                    <tr>
                      <td class="fw-bold text-black">Total</td>
                      {% if is_self_employed and seller.eqtax == True %}
                      <td class="float-end fw-bold text-black">Base + IVA + Req. Eq.</td>
                      {% else %}
                      <td class="float-end fw-bold text-black">Base + IVA</td>
                      {% endif %}
                    </tr>
                  </table>
                  {% elif category.pk == 'expenses' %}
                  <table class="table-striped" style="width: 100%;">
                    <tr>
                      <td>Base</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_amount, -1), ]">
                        [[ invoiceDataJSON.total_expenses_amount ]] €
                      </td>
                    </tr>
                    <tr>
                      <td>IVA</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_vat, -1), ]">
                        [[ invoiceDataJSON.total_expenses_vat ]] €
                      </td>
                    </tr>
                    {% if is_self_employed and seller.eqtax == True %}
                    <tr>
                      <td>Req. Equivalencia</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_eqtax, -1),]">
                        [[ invoiceDataJSON.total_expenses_eqtax ]] €
                      </td>
                    </tr>
                    {% endif %}
                    {% if is_self_employed and seller.is_direct_estimation  %}
                    <tr>
                      <td>
                        Gastos dif. just. &nbsp;
                        <i
                          role="button" class="fas fa-info-circle text-muted"
                          data-bs-toggle="tooltip" data-bs-placement="top"
                          data-bs-original-title="Gastos dificilmente justificables (5% beneficios)"></i>
                      </td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sevent_porcent, -1)]">
                        [[ invoiceDataJSON.total_sevent_porcent ]] €
                      </td>
                    </tr>
                    {% endif %}
                    <tr>
                      <td>IRPF</td>
                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_irpf, -1), ]">
                        [[ invoiceDataJSON.total_expenses_irpf ]] €
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-bold text-black">Total</td>
                      {% if is_self_employed and seller.eqtax == True %}
                        {% if seller.is_direct_estimation %}
                          <td class="float-end fw-bold text-black">Base + IVA + Req. Eq. + Gastos dif.just.</td>
                        {% else  %}
                          <td class="float-end fw-bold text-black">Base + IVA + Req. Eq.</td>
                        {% endif %}
                      {% elif is_self_employed and seller.is_direct_estimation  %}
                        <td class="float-end fw-bold text-black">Base + IVA + Gastos dif.just.</td>
                      {% else %}
                        <td class="float-end fw-bold text-black">Base + IVA</td>
                      {% endif %}
                    </tr>
                  </table>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    {% else %}
      <div class="vue">
        <div class="row mt-3" v-show="true">
          <div class="col-md-12 col-lg-4">
            <div class="card rounded-3 shadow-none border">
              <div class="card-body pb-0">
                <h6 class="mt-0 fw-bold">Entradas</h6>
                <span :class="['my-2', 'h3', 'fw-bold', getPosNegClass(invoiceDataJSON.total_sales_amount, 1), ]">
                  [[ invoiceDataJSON.total_sales ]] €
                </span>
                {% if is_self_employed and seller.eqtax == True %}
                <h6 class="mt-0 text-muted">(Base + IVA + Req. Eq.)</h6>
                {% else %}
                <h6 class="mt-0 text-muted">(Base + IVA)</h6>
                {% endif %}
                <!-- <i class="fas fa-level-down-alt float-end f-30 text-success"></i> -->
              </div>
              <div class="card-body pt-0">
                <div class="accordion-item border-0">
                  <div class="d-flex justify-content-end">
                    <h2 class="accordion-header" id="heading3">
                      <button
                        class="accordion-button p-0 collapsed fw-bold" type="button"
                        data-bs-toggle="collapse" data-bs-target="#collapse1"
                        aria-expanded="false" aria-controls="collapse1">
                      </button>
                    </h2>
                  </div>
                  <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                    <div id="tableColapse1" class="pt-2">
                      <table class="table-striped" style="width: 100%;">
                        <tr>
                          <td>Base</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_amount, 1), ]">
                            [[ invoiceDataJSON.total_sales_amount ]] €
                          </td>
                        </tr>
                        <tr>
                          <td>IVA</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_vat, 1), ]">
                            [[ invoiceDataJSON.total_sales_vat ]] €
                          </td>
                        </tr>
                        {% if is_self_employed and seller.eqtax == True %}
                        <tr>
                          <td>Req. Equivalencia</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_eqtax, 1), ]">
                            [[ invoiceDataJSON.total_sales_eqtax ]] €
                          </td>
                        </tr>
                        {% endif %}
                        <tr>
                          <td>IRPF</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_irpf, 1), ]">
                            [[ invoiceDataJSON.total_sales_irpf ]] €
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12 col-lg-4">
            <div class="card rounded-3 shadow-none border">
              <div class="card-body pb-0">
                <h6 class="mt-0 fw-bold">Salidas</h6>
                <span :class="['my-2', 'h3', 'fw-bold', getPosNegClass(invoiceDataJSON.total_expenses, -1), ]">
                  [[ invoiceDataJSON.total_expenses ]] €
                </span>
                {% if is_self_employed and seller.eqtax == True %}
                  {% if seller.is_direct_estimation %}
                    <h6 class="mt-0 text-muted" style="min-width: max-content;">(Base + IVA + Req. Eq. + Gastos dif.just.)</h6>
                  {% else  %}
                    <h6 class="mt-0 text-muted">(Base + IVA + Req. Eq.)</h6>
                  {% endif %}
                {% elif is_self_employed and seller.is_direct_estimation  %}
                  <h6 class="mt-0 text-muted">(Base + IVA + Gastos dif.just.)</h6>
                {% else %}
                  <h6 class="mt-0 text-muted">(Base + IVA)</h6>
                {% endif %}
                <!-- <i class="fas fa-level-up-alt float-end f-30 text-danger"></i> -->
              </div>
              <div class="card-body pt-0">
                <div class="accordion-item border-0">
                  <div class="d-flex justify-content-end">
                    <h2 class="accordion-header" id="heading3">
                      <button
                        class="accordion-button p-0 collapsed fw-bold" type="button"
                        data-bs-toggle="collapse" data-bs-target="#collapse1"
                        aria-expanded="false" aria-controls="collapse1">
                      </button>
                    </h2>
                  </div>
                  <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                    <div id="tableColapse2" class="pt-2">
                      <table class="table-striped" style="width: 100%;">
                        <tr>
                          <td>Base</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_amount, -1) ]">
                            [[ invoiceDataJSON.total_expenses_amount ]] €
                          </td>
                        </tr>
                        <tr>
                          <td>IVA</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_vat, -1) ]">
                            [[ invoiceDataJSON.total_expenses_vat ]] €
                          </td>
                        </tr>
                        {% if is_self_employed and seller.eqtax == True %}
                        <tr>
                          <td>Req. Equivalencia</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_eqtax, -1)]">
                            [[ invoiceDataJSON.total_expenses_eqtax ]] €
                          </td>
                        </tr>
                        {% endif %}
                        {% if is_self_employed and seller.is_direct_estimation  %}
                        <tr>
                          <td>
                            Gastos dif. just. &nbsp;
                            <i
                              role="button" class="fas fa-info-circle text-muted"
                              data-bs-toggle="tooltip" data-bs-placement="top"
                              data-bs-original-title="Gastos dificilmente justificables (5% beneficios)"></i>
                          </td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sevent_porcent, -1)]">
                            [[ invoiceDataJSON.total_sevent_porcent ]] €
                          </td>
                        </tr>
                        {% endif %}
                        <tr>
                          <td>IRPF</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_irpf, -1), ]">
                            [[ invoiceDataJSON.total_expenses_irpf ]] €
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12 col-lg-4">
            <div class="card rounded-3 shadow-none border">
              <div class="card-body pb-0">
                <h6 class="mt-0 fw-bold">Resultado</h6>
                <span :class="['my-2', 'h3', 'fw-bold', getPosNegClass(invoiceDataJSON.total_result, 1), ]">
                  [[ invoiceDataJSON.total_result ]] €
                </span>
                {% if is_self_employed and seller.eqtax == True %}
                <h6 class="mt-0 text-muted">(Base + IVA + Req. Eq.)</h6>
                {% else %}
                <h6 class="mt-0 text-muted">(Base + IVA)</h6>
                {% endif %}
                <!-- <i class="fas fa-hand-holding-usd float-end f-30 text-primary"></i> -->
              </div>
              <div class="card-body pt-0">
                <div class="accordion-item border-0">
                  <div class="d-flex justify-content-end">
                    <h2 class="accordion-header" id="heading3">
                      <button
                        class="accordion-button p-0 collapsed fw-bold" type="button"
                        data-bs-toggle="collapse" data-bs-target="#collapse1"
                        aria-expanded="false" aria-controls="collapse1">
                      </button>
                    </h2>
                  </div>
                  <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                    <div id="tableColapse3" class="pt-2">
                      <table class="table-striped" style="width: 100%;">
                        <tr>
                          <td>Base</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_amount, 1), ]">
                            [[ invoiceDataJSON.total_profit_amount ]] €
                          </td>
                        </tr>
                        <tr>
                          <td>IVA</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_vat, 1), ]">
                            [[ invoiceDataJSON.total_profit_vat ]] €
                          </td>
                        </tr>
                        {% if is_self_employed and seller.eqtax == True %}
                        <tr>
                          <td >Req. Equivalencia</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_eqtax, 1), ]">
                            [[ invoiceDataJSON.total_profit_eqtax ]] €
                          </td>
                        </tr>
                        {% endif %}
                        <tr>
                          <td >IRPF</td>
                          <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_irpf, 1), ]">
                            [[ invoiceDataJSON.total_profit_irpf ]] €
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    <!-- Totals | END -->

    {% comment %}
    <!-- NIF Info | START  -->
    <div class="col-12 mt-2 mb-2">
      <div class="card mb-0">
        <div class="card-block mb-0">
          <div class="row d-flex align-items-center mb-0">
            <div class="col">
              <h6><b>NIF SELLER</b></h6>
              <h3 class="f-w-300 d-flex align-items-center mb-1 text-muted">
                {% if seller and seller.nif_registration %}
                  <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Copiar" data-text="{{ seller.nif_registration }}">
                    {{ seller.nif_registration }}
                  </span>
                {% else %}
                  - Sin NIF -
                {% endif %}
              </h3>
              <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                {% if seller and seller.contracted_accounting == True %}
                  Contabilidad: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                {% else %}
                  Contabilidad: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                {% endif %}
              </h5>
              <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                {% if seller and seller.oss == True %}
                  OSS: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                {% else %}
                  OSS: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                {% endif %}
                &nbsp;  &nbsp;
                {% if seller and seller.eqtax == True %}
                  R.EQ: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                {% else %}
                  R.EQ: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                {% endif %}
              </h5>
            </div>
            {% if seller_vat %}
              {% for sv in seller_vat %}
                <div class="col" style="border-left: 4px solid #F5F7FA; height: auto;">
                  <h6><b>NIF {{sv.vat_country}}</b></h6>
                  <h3 class="f-w-300 d-flex align-items-center mb-1 text-muted">
                    {% if sv and sv.vat_number %}
                      <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Copiar" data-text="{{ object.nrc }}">
                        {{ sv.vat_number }}
                      </span>
                    {% else %}
                      - Sin NIF -
                    {% endif %}
                  </h3>
                  <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                    {% if sv and sv.is_contracted == True %}
                      Contabilidad: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                    {% else %}
                      Contabilidad: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                    {% endif %}
                  </h5>
                </div>
              {% endfor %}
            {% endif%}
          </div>
        </div>
      </div>
    </div>
    <!-- NIF Info | END  -->
    {% endcomment %}

    <!-- Checkbox Selection and pagination -->
    <div class="col-12 mb-3">
      <div class="row gy-3 d-flex justify-content-between">
        <div class="col-xl-6 col-sm-12">
          <div class="d-flex align-items-center gap-2 justify-content-start">
            <form
              method="post" id="formSelect"
              enctype="multipart/form-data"
              action="{% url 'app_invoices:seller_invoices_massive' seller.shortname %}">
              {% csrf_token %}
              <div class="col-12" id="massiveAction">
                <div class="col-12 d-flex">
                  <select class="form-control form-select" id="selectValue" onchange="urlForm()" disabled>
                    <option value="empty">Selecciona una acción múltiple</option>
                    <option value="delete">Eliminar facturas selecionadas</option>
                    <option value="pending">Cambiar estado a Pendiente</option>
                    <option value="revision-pending">Cambiar estado a Revisión Pendiente</option>
                    <option value="discard">Cambiar estado a Descartada</option>
                    <option value="revised">Cambiar estado a Revisada</option>
                    <option value="download">Descargar facturas</option>
                  </select>
                  <button
                    id="enabledButton" type="submit"
                    class="btn btn-dark mx-2 my-auto" disabled
                    style="display:block; width: 120px;">
                    <b>Confirmar</b>
                  </button>
                  <!-- Modal Trigger-->
                  <button
                    id="deleteButton" type="button"
                    class="btn btn-danger mx-2 my-auto d-none"
                    style="width: 120px;"
                    data-bs-toggle="modal" data-bs-target="#modal">
                    <b>Eliminar</b>
                  </buttontype=>
                  <input type="hidden" name="selectAction" id="selectAction">
                  <input type="hidden" name="selectedPk" id="selectedPk">
                </div>
                <footer id="footer" style="color:red; display:none;">
                  <b>* No está permitido eliminar facturas revisadas</b>
                </footer>
              </div>
              <!-- Modal Disagreed-->
              <div
                class="modal fade" id="modal"
                tabindex="-1" role="dialog"
                aria-labelledby="modalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title" id="modalLabel">Eliminar</h5>
                    </div>
                    <div class="modal-body">
                      <div class="col form-group form-check p-3">
                        <p><b><h4 style="text-align: center;">¿Está seguro que desea eliminar las facturas
                          seleccionadas?</h4></b></p>
                      </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                      <button
                        type="button" class="btn btn-light"
                        data-bs-dismiss="modal" data-bs-target="#modal"
                        aria-label="Close">
                        Cancelar
                      </button>
                      <button type="submit" id="submitButton" class="btn btn-danger"><b>Eliminar</b></button>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div class="col-xl-6 col-sm-12">
          <div class="d-flex align-items-center gap-2 justify-content-end">

            <!-- search -->
            <div class="list-action-btn-block flex-fill d-flex justify-content-end">
              <div class="search-container">
                  <input class="form-control" type="search" id="search" name="search" placeholder="Escribe y presiona enter &#x21B5; para poder buscar...">
              </div>
            </div>

            <!-- filters -->
            <div class="list-action-btn-block">
              <button
                id="dropdownButton" class="btn btn-dark"
                type="button"
                data-bs-toggle="tooltip" data-bs-placement="top" title="Filtros"
                >
                <i class="mdi mdi-filter-outline fa-xl me-0"></i>
                <span class="badge top-right-badge rounded-pill bg-danger d-none" id="id-filter-notification">2</span>
              </button>
              <div id="dropdownFiltersForm" class="dropdown-form shadow p-3">
                <form id="filtersFormID">
                  <div class="row mb-2">
                    <!-- status -->
                    <div class="col-12 mb-2">
                      <label for="multiple-status" class="mb-1">Estado</label>
                      <multi-status separator=", " value="" id="multiple-status">
                        <ul slot="check-values">
                          {% for status in invoice_statuses %}
                          <li
                            class="cursor-default"
                            id="multi-value"
                            value="{{ status.pk }}"
                            multi-title="{{ status.description }}"
                          >{{ status.description }}</li>
                          {% endfor %}
                        </ul>
                      </multi-status>
                    </div>
                    <!-- actividad económica -->
                    {% if economic_activity|length > 1 %}
                    <div class="col-12 mb-3">
                    {% else %}
                    <div class="col-12 mb-3" style="display:none;">
                    {% endif %}
                      <label for="multiple-economicactivities" class="mb-1">Actividad Económica</label>
                      <multi-economicactivity separator=", " value="" id="multiple-economicactivities">
                        <ul slot="check-values">
                          {% for iae in economic_activity %}
                          <li
                            class="cursor-default"
                            id="multi-value"
                            value="{{ iae.code }}"
                            multi-title="{{ iae.description }} "
                          >{{ iae.description }}</li>
                          {% endfor %}
                        </ul>
                      </multi-economicactivity>
                    </div>
                    <!-- años -->
                    <div class="col-6 mb-3">
                      <label for="year" class="mb-1">Año</label>
                      <select class="form-control form-select" name="year" id="year" onchange="onChangeYear();">
                        <option value="">Todos los años</option>
                        {% for year in years_with_invoices %}
                          <option value="{{ year }}">{{ year }}</option>
                        {% endfor %}
                      </select>
                    </div>
                    <!-- meses -->
                    <div class="col-6 mb-3">
                      <label for="multiple-month" class="mb-1">Meses</label>
                      <multi-month separator=", " value="" id="multiple-month">
                        <ul slot="check-values">
                          <li class="cursor-default" id="multi-value" value="1" multi-title="Enero">Enero</li>
                          <li class="cursor-default" id="multi-value" value="2" multi-title="Febrero">Febrero</li>
                          <li class="cursor-default" id="multi-value" value="3" multi-title="Marzo">Marzo</li>
                          <li class="cursor-default" id="multi-value" value="4" multi-title="Abril">Abril</li>
                          <li class="cursor-default" id="multi-value" value="5" multi-title="Mayo">Mayo</li>
                          <li class="cursor-default" id="multi-value" value="6" multi-title="Junio">Junio</li>
                          <li class="cursor-default" id="multi-value" value="7" multi-title="Julio">Julio</li>
                          <li class="cursor-default" id="multi-value" value="8" multi-title="Agosto">Agosto</li>
                          <li class="cursor-default" id="multi-value" value="9" multi-title="Septiembre">Septiembre</li>
                          <li class="cursor-default" id="multi-value" value="10" multi-title="Octubre">Octubre</li>
                          <li class="cursor-default" id="multi-value" value="11" multi-title="Noviembre">Noviembre</li>
                          <li class="cursor-default" id="multi-value" value="12" multi-title="Diciembre">Diciembre</li>
                        </ul>
                      </multi-month>
                    </div>
                    <!-- tax country -->
                    <div class="col-6 mb-3">
                      <label for="multiple-taxcountries" class="mb-1">País de impuestos</label>
                      <multi-taxcountry separator=", " value="" id="multiple-taxcountries">
                        <ul slot="check-values">
                          {% for country in invoices_tax_country %}
                            <li
                              class="cursor-default"
                              id="multi-value"
                              value="{{ country.pk }}"
                              multi-title="{{ country.name }} ( {{ country.pk }} )"
                            >{{ country.name }} ( {{ country.pk }} )</li>
                          {% endfor %}
                        </ul>
                      </multi-taxcountry>
                    </div>
                    <!-- ivas -->
                    <div class="col-6 mb-3">
                      <label for="multiple-vatrates" class="mb-1">IVA's</label>
                      <multi-vatrates separator=", " value="" id="multiple-vatrates" disabled>
                        <ul slot="check-values">
                        </ul>
                      </multi-vatrates>
                    </div>
                    <!-- inversion de sujeto pasivo -->
                    <div class="col-6 mb-3">
                      <label for="multiple-reverse-charge" class="mb-1" id="multiple-reverse-charge-label">Operación sujeto pasivo</label>
                      <multi-reverse-charge separator=", " value="" id="multiple-reverse-charge" disabled>
                        <ul slot="check-values">
                          <li
                            class="cursor-default" id="multi-value" value="true"
                            multi-title="Si">
                            Si
                          </li>
                          <li
                            class="cursor-default" id="multi-value" value="false"
                            multi-title="No">
                            No
                          </li>
                        </ul>
                      </multi-reverse-charge>
                    </div>
                    <!-- recargo equivalencia -->
                    <div class="col-6 mb-3">
                      <label for="multiple-eqtax" class="mb-1" id="multiple-eqtax-label">Recargo de equivalencia</label>
                      <multi-eqtax separator=", " value="" id="multiple-eqtax" disabled>
                        <ul slot="check-values">
                          <li
                            class="cursor-default" id="multi-value" value="0"
                            multi-title="0%">
                            0%
                          </li>
                          <li
                            class="cursor-default" id="multi-value" value="0.5"
                            multi-title="0,5%">
                            0,5%
                          </li>
                          <li
                            class="cursor-default" id="multi-value" value="1.4"
                            multi-title="1,4%">
                            1,4%
                          </li>
                          <li
                            class="cursor-default" id="multi-value" value="5.2"
                            multi-title="5,2%">
                            5,2%
                          </li>
                        </ul>
                      </multi-eqtax>
                    </div>
                    <!-- departure country -->
                    <div class="col-6 mb-3">
                      <label for="multiple-departurecountries" class="mb-1">País de salida</label>
                      <multi-departurecountry separator=", " value="" id="multiple-departurecountries">
                        <ul slot="check-values">
                          {% for country in invoices_departure_country %}
                            <li
                            class="cursor-default" id="multi-value"
                            value="{{ country.pk }}" multi-title="{{ country.name }} ( {{ country.pk }} )">
                            {{ country.name }} ( {{ country.pk }} )
                          </li>
                          {% endfor %}
                        </ul>
                      </multi-departurecountry>
                    </div>
                    <!-- arrival country -->
                    <div class="col-6 mb-3">
                      <label for="multiple-arrivalcountries" class="mb-1">País de llegada</label>
                      <multi-arrivalcountry separator=", " value="" id="multiple-arrivalcountries">
                        <ul slot="check-values">
                          {% for country in invoices_arrival_country %}
                            <li
                            class="cursor-default" id="multi-value"
                            value="{{ country.pk }}" multi-title="{{ country.name }} ( {{ country.pk }} )">
                            {{ country.name }} ( {{ country.pk }} )
                          </li>
                          {% endfor %}
                        </ul>
                      </multi-arrivalcountry>
                    </div>
                    <!-- tipos de facturas -->
                    <div class="col-6 mb-2">
                      <label for="multiple-invoicetype" class="mb-1">Tipo de factura</label>
                      <multi-invoicetype separator=", " value="" id="multiple-invoicetype">
                        <ul slot="check-values">
                          {% for invoice in invoice_type %}
                          <li
                              class="cursor-default"
                              id="multi-value"
                              value="{{ invoice.code }}"
                              multi-title="{{ invoice.description }}"
                          >{{ invoice.description }}</li>
                          {% endfor %}
                        </ul>
                      </multi-invoicetype>
                    </div>
                    <!-- tipos de transacciones -->
                    <div class="col-6 mb-3">
                      <label for="multiple-transactions" class="mb-1" id="multiple-transactions-label">Tipo de transacción</label>
                      <multi-transaction separator=", " value="" id="multiple-transactions">
                        <ul slot="check-values">
                          {% for transaction in transaction_types %}
                            <li
                              class="cursor-default" id="multi-value" value="{{ transaction.code }}"
                              multi-title="{{ transaction.description }}">
                              {{ transaction.description }}
                            </li>
                          {% endfor %}
                        </ul>
                      </multi-transaction>
                    </div>

                    <!-- origen de la factura -->
                    <div class="col-6 mb-3">
                      <label for="multiple-origin" class="mb-1" id="multiple-origin-label">Origen facturas</label>
                      <multi-origin separator=", " value="" id="multiple-origin">
                        <ul slot="check-values">
                          <li
                            class="cursor-default" id="multi-value" value="true"
                            multi-title="Reporte Amazon TXT">
                            Reporte Amazon TXT
                          </li>
                          <li
                            class="cursor-default" id="multi-value" value="false"
                            multi-title="Cargador">
                            Cargador
                          </li>
                        </ul>
                      </multi-origin>
                    </div>

                    <!-- tipos de cuenta contable multi-accountingaccount-->
                    <div class="col-12 mb-2">
                      <label for="multiple-accountingaccount" class="mb-1">Cuenta Contable</label>
                      <multi-accountingaccount separator=", " value="" id="multiple-accountingaccount">
                        <ul slot="check-values">
                          {% for account in accounting_accounts %}
                          <li
                            class="cursor-default"
                            id="multi-value"
                            value="{{ account.code }}"
                            multi-title="{{ account.description }} ({{ account.code }})"
                          >
                            {{ account.description }} ({{ account.code }})
                          </li>
                          {% endfor %}
                        </ul>
                      </multi-accountingaccount>
                    </div>
                  </div>
                  <hr>
                  <div class="row">
                    <div class="col-12 text-end">
                      <button type="button" class="btn btn-light" onclick="resetFilters()">
                        Limpiar
                      </button>
                      <button type="button" id="applyFiltersButton" class="btn btn-dark" onclick="applyFilters()">
                        Aplicar
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>

            {% if category.pk != "sales" and category.pk != "expenses" %}
            <!-- details -->
            <div class="list-action-btn-block">
              <button
                id="toogleDetailButton" class="btn btn-dark" type="button"
                data-bs-toggle="collapse" data-bs-target="#collapse1"
                aria-expanded="false" aria-controls="collapse1"
                title="Detalles" data-bs-placement="top">
                <i class="mdi mdi-eye fa-xl me-0"></i>
                <!-- Detalles -->
              </button>
            </div>
            {% endif %}

            <!-- download -->
            <div class="list-action-btn-block">
              <button
                class="btn btn-dark dropdown-toggle"
                type="button"
                id="downloadButton"
                data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true"
                title="Descargas" data-bs-placement="top">
                <i class="mdi mdi-download fa-xl me-0"></i>
                <!-- Descargar -->
              </button>
              <div
                class="dropdown-menu"
                style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(0px, 45px);"
                data-popper-placement="bottom-start">
                <a class="dropdown-item" role="button" onclick="downloadInvoices()">Descargar facturas
                  <!-- <a class="dropdown-item" data-bs-toggle="tooltip" data-bs-placement="top" title="Solo se descargarán las facturas que estén cargadas en la aplicación" role="button" onclick="downloadInvoices()" >Descargar facturas -->
                  <span class="badge" style="background-color: #6c757d;">.zip</span>
                </a>
                <a class="dropdown-item" role="button" onclick="generateCSV()">Exportar excel
                  <span class="badge float-end bg-success">.xlsx</span>
                </a>
              </div>
            </div>

            <!-- pages -->
            <div class="list-action-btn-block">
              <select class="form-control form-select" name="show" id="show" onchange="filter()"
                      data-bs-toggle="tooltip" data-bs-placement="top" title="Filas por página">
                <option value="50" default>50</option>
                <option value="100">100</option>
                <option value="200">200</option>
              </select>
            </div>

          </div>
        </div>
      </div>
    </div>
    <!-- Tabla Listado Facturas | START -->
    <div class="col-12">
      <div class="table-responsive">
        <table
          id="invoices-table"
          class="table table-striped table-hover nowrap border"
          style="width:99.9%; overflow:hidden;">
          <thead class="table-head">
          <tr>
            <th><input type="checkbox" name="select_all" id="select_all"></th>
            <th>ID</th>
            <th>Estado</th>
            <th>Número</th>
            <th>Cliente</th>
            <th>Cuenta Ingresos</th>
            <th>Proveedor</th>
            <th>Cuenta Gastos</th>
            <th>Cliente/Proveedor</th>
            <th>Cuenta Contable</th>
            <th>Fecha Contabilización</th>
            <th>Fecha Factura</th>
            <th>Fecha Expedición</th>
            <th>Fecha</th>
            <th>País IVA</th>
            <th>Pais IVA</th>
            <th>País Salida</th>
            <th>País Llegada</th>
            <th>Categoría</th>
            <th>Tipo</th>
            <th>Tipo de Transacción</th>
            <th>IAE</th>
            <th>IRPF (€)</th>
            <th style="width:5%;">Re.EQ (€)</th>
            <th>Generada/Subida</th>
            <th>JSON VATS</th>
            <th>IVA 2,1%(€)</th>
            <th>IVA 3%(€)</th>
            <th>IVA 4%(€)</th>
            <th>IVA 4,8%(€)</th>
            <th>IVA 5%(€)</th>
            <th>IVA 5,5%(€)</th>
            <th>IVA 6%(€)</th>
            <th>IVA 7%(€)</th>
            <th>IVA 8%(€)</th>
            <th>IVA 9%(€)</th>
            <th>IVA 9,5%(€)</th>
            <th>IVA 10%(€)</th>
            <th>IVA 12%(€)</th>
            <th>IVA 13%(€)</th>
            <th>IVA 13,5%(€)</th>
            <th>IVA 14%(€)</th>
            <th>IVA 15%(€)</th>
            <th>IVA 17%(€)</th>
            <th>IVA 18%(€)</th>
            <th>IVA 19%(€)</th>
            <th>IVA 20%(€)</th>
            <th>IVA 21%(€)</th>
            <th>IVA 22%(€)</th>
            <th>IVA 23%(€)</th>
            <th>IVA 24%(€)</th>
            <th>IVA 25%(€)</th>
            <th>IVA 27%(€)</th>
            {% comment %}
                <th>IVA  4%(€)</th>
                <th>IVA 10%(€)</th>
                <th>IVA 21%(€)</th>
                {% endcomment %}
            <th style="width:5%;">IVA (€)</th>
            <th style="width:5%;">Base (€)</th>
            <th style="width:5%;">Total (€)</th>
            <th style="width:5%;">Tags</th>
            <th style="width:5%;">Notes</th>
            <th style="width:5%;">Discard Reason</th>
            <th style="width:5%;">Discard Reason Notes</th>
            <th style="width:5%;">Info</th>
            <th style="width:5%;">Acciones</th>
          </tr>
          </thead>
          <tbody>
          </tbody>
        </table>
      </div>
    </div>
    <!-- Tabla Listado Facturas | END -->
    <!-- Modal to show info notes and others -->
    <div class="modal fade" id="infoModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header justify-content-center">
            <h4 class="mt-2">INFORMACIÓN DE LA FACTURA</h4>
          </div>
          <div class="modal-body">
            <div class="info-content">
              <p style="font-size: 16px;" id="tags"></p>
              <p style="font-size: 16px;" id="notes"></p>
              <p style="font-size: 16px;" id="notes_private"></p>
              <p style="font-size: 16px;" id="discard"></p>
              <p style="font-size: 16px;" id="discard_notes"></p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary mt-2" data-bs-dismiss="modal">Cerrar</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal to show info notes and others -->
    <!-- modal for showing loading -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header justify-content-center">
            <h4 class="mt-2">Descargando</h4>
          </div>
          <div class="modal-body">
            <div id="spinner-animation"
                 class="d-flex justify-content-center align-items-center text-center mb-3 d-none">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <div id="folder-animation" class="folder-animation-wrapper mb-3">
              <div class="file-animation file-logo">
                <div class="page page1">
                  <p class="mt-1" style="font-size: 12px;">Factura 1</p>
                </div>
                <div class="page page2">
                  <p class="mt-1" style="font-size: 12px;">Factura 2</p>
                </div>
                <div class="page page3">
                  <p class="mt-1" style="font-size: 12px;">Factura 3</p>
                </div>
              </div>
            </div>
            <div class="d-flex-column justify-content-center align-items-center text-center">
              <h4 id="textDownloadingAnimation" class="mb-2">Preparando archivos...</h4>
              <p style="font-size: 16px;">Por favor, no cierres ni recargues la página</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- modal for showing loading -->
  </div>
{% endblock content %}
{% block javascripts %}
  <!-- JQUERY DATA TABLE JS -->.
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{% static 'assets/js/loading.js' %}"></script>
  <!-- <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables-v2.0.8.js"></script> -->
  <!-- https://datatables.net/extensions/select/examples/initialisation/checkbox.html -->
  <!-- VUE3 JS -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <!-- start DEBUG -->
  <script type="text/javascript">
    const debug = {{ debug|yesno:"true,false" }};
    // Función para debug (imprime en consola solo si debug está habilitado)
    function debugLog(...args) {
      if (debug) {
        console.log(...args);
      }
    }
    debugLog("Debug mode is enabled")
  </script>
  <!-- end DEBUG -->
  <!-- start MAIN SCRIPT -->
  <script type="text/javascript">
    let table = null;
    let retryAttempts = 0;
    let timer;
    let filtersDictCount = {
      'year': false,
      'month': false,
      'multiple-taxcountries': false,
      'multiple-vatrates': false,
      'multiple-departurecountries': false,
      'multiple-accountingaccount': false,
      'multiple-arrivalcountries': false,
      'invoice_type': false,
      'multiple-transactions': false,
      'multiple-status': false,
      'multiple-economicactivities': false,
      'multiple-invoicetype': false,
      'multiple-month': false,
      'multiple-origin': false,
      'multiple-reverse-charge': false,
      'multiple-eqtax': false,
    }
    // let exportCSV = false; // Use inputExportCSV from VUE

    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    const multipleStatus = document.getElementById('multiple-status');
    const multipleEconomicActivities = document.getElementById('multiple-economicactivities');
    const multipleMonth = document.getElementById('multiple-month');
    const multipleInvoiceType = document.getElementById('multiple-invoicetype');
    const multipleTaxCountries = document.getElementById('multiple-taxcountries');
    const multipleVatRates = document.getElementById("multiple-vatrates");
    const multipleDepartureCountries = document.getElementById('multiple-departurecountries');
    const multipleAccountingAccount = document.getElementById('multiple-accountingaccount');
    const multipleArrivalCountries = document.getElementById('multiple-arrivalcountries');
    const multiCheckbox = document.getElementById('multiple-transactions');
    const multipleOrigin = document.getElementById('multiple-origin');
    const multipleReverseCharge = document.getElementById('multiple-reverse-charge');
    const multipleEqtax = document.getElementById('multiple-eqtax');
    const filterForm = document.getElementById('filtersFormID');

    const search_invoice = document.getElementById('search');
    search_invoice.addEventListener("focus", () => {
      search_invoice.parentElement.style.width = "100%";
    });

    search_invoice.addEventListener("blur", () => {
        search_invoice.parentElement.style.width = "200px";
    });

    search_invoice.addEventListener("keydown", (e) => {
      debugLog('resultado de la busqueda: ', e);
      if (e.key === "Enter") {
        // clearTimeout(timer);
        filter();
      }
    });

    search_invoice.addEventListener("input", () => {
      if (search_invoice.value == '') {
        filter();
      }
    });

    const ajaxData = (d) => {
      debugLog('ajaxData | data: ', d);

      const getDataValue = (elementId) => {
        const el = document.getElementById(elementId);
        return el ? el.dataset.value : null;
      };

      const getValue = (elementId) => {
        const el = document.getElementById(elementId);
        return el ? el.value : null;
      };

      let multiple_status = getDataValue("multiple-status");
      let multiple_economic_activities = getDataValue("multiple-economicactivities");
      let year = getValue("year");
      let multiple_month = getDataValue("multiple-month");
      let country = getDataValue("multiple-taxcountries");
      let departure_country = getDataValue("multiple-departurecountries");
      let accounting_account = getDataValue("multiple-accountingaccount");
      let arrival_country = getDataValue("multiple-arrivalcountries");
      let multiple_invoicetype = getDataValue("multiple-invoicetype");
      let multiple_transactions = getDataValue("multiple-transactions");
      let multiple_vat_rates = getDataValue("multiple-vatrates");
      let multiple_origin = getDataValue("multiple-origin");
      let multiple_reverse_charge = getDataValue("multiple-reverse-charge");
      let multiple_eqtax = getDataValue("multiple-eqtax");

      let search = search_invoice.value;
      let show = getValue("show");
      let fc_transfer = false;
      let category = "";
      let tParams = "";

      {% if category and category.pk %}
        category = "{{category.pk}}";
      {% endif %}

      {% if transfer and transfer == True %}
        fc_transfer = true;
      {% endif %}

      if (multiple_economic_activities) {
        debugLog('filterDT | multiple_economic_activities: ', multiple_economic_activities);
        const selectedEconomicActivityValues = multiple_economic_activities.split(', ');
        d.iae = JSON.stringify(selectedEconomicActivityValues);
        tParams += "&iae=" + JSON.stringify(selectedEconomicActivityValues);
      }

      if (multiple_status) {
        debugLog('filterDT | multiple_status: ', multiple_status);
        const selectedStatusValues = multiple_status.split(', ');
        d.status_id = JSON.stringify(selectedStatusValues);
        tParams += '&status_id=' + JSON.stringify(selectedStatusValues);
      }

      if (year) {
        debugLog('filterDT | year: ', year);
        d.expedition_date_year = year;
        tParams += "&year=" + year;
      }

      if (multiple_month) {
        debugLog('filterDT | multiple_month: ', multiple_month);
        const selectedMonthValues = multiple_month.split(', ');
        d.months = JSON.stringify(selectedMonthValues);
        tParams += "&months=" + JSON.stringify(selectedMonthValues);;
      }

      if (multiple_invoicetype) {
        debugLog('filterDT | multiple_invoicetype: ', multiple_invoicetype);
        const selectedInvoiceTypeValues = multiple_invoicetype.split(', ');
        d.invoice_type_id = JSON.stringify(selectedInvoiceTypeValues);
        tParams += '&invoice_type_id=' + JSON.stringify(selectedInvoiceTypeValues);
      }

      if (country) {
        debugLog('filterDT | country: ', country);
        country = JSON.stringify(country.split(", "));
        d.tax_country_id = country;
        tParams += "&country=" + country;
      }

      if (departure_country) {
        debugLog('filterDT | departure_country: ', departure_country);
        departure_country = JSON.stringify(departure_country.split(", "));
        d.departure_country_id = departure_country;
        tParams += "&departure_country=" + departure_country;
      }

      if (accounting_account) {
        debugLog('filterDT | accounting_account: ', accounting_account);
        accounting_account = JSON.stringify(accounting_account.split(", "));
        d.accounting_accounts = accounting_account;
        tParams += "&accounting_accounts=" + accounting_account;
      }

      if (arrival_country) {
        debugLog(`filterDT | arrival_country: ${arrival_country}`)
        arrival_country = JSON.stringify(arrival_country.split(", "));
        d.arrival_country_id = arrival_country;
        tParams += "&arrival_country=" + arrival_country;
      }

      if (multiple_vat_rates) {
        debugLog('filterDT | multiple_vat_rates: ', multiple_vat_rates);
        multiple_vat_rates = JSON.stringify(multiple_vat_rates.split(", "));
        d.vat_rate_id = multiple_vat_rates;
        tParams += "&vat_rate=" + multiple_vat_rates;
      }

      if (multiple_transactions) {
        debugLog('filterDT | multiple_transactions: ', multiple_transactions);
        const selectedValues = multiple_transactions.split(', ');
        d.transaction_type_id = JSON.stringify(selectedValues);
        tParams += '&transaction=' + JSON.stringify(selectedValues);
      }

      if (multiple_origin) {
        debugLog('filterDT | multiple_origin: ', multiple_origin);
        const selectedValues = multiple_origin.split(', ');
        d.origin = JSON.stringify(selectedValues);
        tParams += '&origin=' + JSON.stringify(selectedValues);
      }

      if (multiple_reverse_charge) {
        debugLog('filterDT | multiple_reverse_charge: ', multiple_reverse_charge);
        const selectedValues = multiple_reverse_charge.split(', ');
        d.reverse_charge = JSON.stringify(selectedValues);
        tParams += '&reverse_charge=' + JSON.stringify(selectedValues);
      }

      if (multiple_eqtax) {
        debugLog('filterDT | multiple_eqtax: ', multiple_eqtax);
        const selectedValues = multiple_eqtax.split(', ');
        d.eqtax = JSON.stringify(selectedValues);
        tParams += '&eqtax=' + JSON.stringify(selectedValues);
      }

      if (search) {
        debugLog('filterDT | search: ', search);
        d.search = search.trim();
        tParams += "&search=" + search.trim();
      }

      if (category) {
        debugLog('filterDT | category: ', category);
        d.invoice_category_id = category;
      }

      if (fc_transfer) {
        debugLog('filterDT | fc_transfer: ', fc_transfer);
        d.fc_transfer = fc_transfer;
        tParams += "&transfer=" + fc_transfer;
      }

      if (d.order.length > 0) {
        orderby = [];
        for (const o of d.order) {
          name = d.columns[o.column].data;
          if (name == 'contact') {
            orderby.push({"dir": o.dir, "name": "customer"});
            orderby.push({"dir": o.dir, "name": "provider"});
          } else if (name == 'account_sales_expenses') {
            orderby.push({"dir": o.dir, "name": "account_sales"});
            orderby.push({"dir": o.dir, "name": "account_expenses"});
          } else if (name == 'date') {
            orderby.push({"dir": o.dir, "name": "expedition_date"});
            orderby.push({"dir": o.dir, "name": "invoice_date"});
            orderby.push({"dir": o.dir, "name": "accounting_date"});
          } else if (name == 'status') {
            orderby.push({"dir": o.dir, "name": "status__order"});
          } else {
            orderby.push({"dir": o.dir, "name": name});
          }
          // orderby.push({ "dir": o.dir, "name": "status__order"});
          orderby.push({"dir": 'desc', "name": "pk"});
        }
        debugLog('filterDT | orderBy: ', orderby);
        d.order = JSON.stringify(orderby);
      }

      if(d.start == 0){
        getTotals(tParams);
      }
      return d;

    }

    const renderNumber = (data, type = null, row = null) => {
      if (data == null || data == undefined || data == "" || data == 0) {
        data = 0.0;
      } else {
        data = parseInt(parseFloat(data) * 100) / 100;
      }
      return data;
    }

    const renderVAT = (vatNumber, vats) => {
      let r = 0;

      vatint = parseInt(vatNumber.toString()).toString();
      vat1dec = parseFloat(vatNumber.toString()).toFixed(1).toString();
      vat2dec = parseFloat(vatNumber.toString()).toFixed(2).toString();

      if (vats) {
        if (vats[vatint]) {
          r = vats[vatint];
        } else if (vats[vat1dec]) {
          r = vats[vat1dec];
        } else if (vats[vat2dec]) {
          r = vats[vat2dec];
        }
      }

      return r;
    }

    const createDT = () => {
      const seller = dj.value.seller;
      const irpf_visibility = seller && seller.contracted_accounting == true ? true : false;
      const eqtax_visibility = seller && seller.contracted_accounting == true && seller.eqtax == true ? true : false;
      let jsonvat = null;
      let vats = {};
      table = $('#invoices-table').DataTable({
        scrollX: true,
        scrollY: '80vh',
        scrollCollapse: true,
        fixedHeader: true,
        fixedColumns: {
          leftColumns: 1
        },
        "serverSide": true,
        "ajax": {
          "url": "{% url 'app_invoices:seller_invoices_dt' seller.shortname %}",
          "data": function (d) {
            ajaxData(d);
          },
          "error": function(xhr, error, thrown) {
            if(xhr.status === 500 && retryAttempts < 3) {
                // retryAttempts + 1
                retryAttempts++;
                // Reload DT
                setTimeout(function() {
                  table.ajax.reload(null, false)
                }, 1000 ); // Retry after 1 seconds

            } else {
                const Toast = Swal.mixin({
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 10000,
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });
                Toast.fire({
                  icon: 'error',
                  title: 'Ha Ocurrido un error en la carga de datos, por favor recargue la página.'
                });
            }
          },
        },
        "language": {
          "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
        },
        "searching": false,
        "lengthChange": false,
        "lengthMenu": [[50, 100, 200], [50, 100, 200]],
        "order": [[2, 'asc'], [1, 'desc'],],
        "select": {"style": 'multi', "selector": 'custom-checkbox'},
        "columns": [
          {
            "data": null,
            "className": 'select-checkbox miId',
            "visible": true,
            "orderable": false,
            "render": function (data, type, full, meta) {

              if (type === 'display') {
                return '<input type="checkbox" class="custom-checkbox" data-select="true" id="miId">';
              }

              return data;
            }
          },
          {"data": "pk", "visible": true},
          {
            "data": "status", "render": function (data, type, row) {
              {% comment %} debugLog(row); {% endcomment %}
              let html = "";
              if (row.status) {
                let bg = "";
                if (row.status == "Pendiente" || row.status == "Revision Pendiente") {
                  bg = "bg-warning";
                } else if (row.status == "Revisada") {
                  bg = "bg-success";
                } else if (row.status == "Descartada") {
                  bg = "bg-danger";
                }
                html = `<span class="rounded ${bg} text-white p-1">
                            <b> &nbsp; ${row.status} &nbsp; </b>
                          </span>`;
              }
              return html;
            }
          },
          {"data": "reference", className: "column-width-limit"},
          {"data": "customer", "visible": false},
          {"data": "account_sales", "visible": false},
          {"data": "provider", "visible": false},
          {"data": "account_expenses", "visible": false},
          {
            "data": "contact", className: "column-width-limit", "render": function (data, type, row) {
              let contact = "";
              if (row.customer) {
                contact = row.customer;
              } else if (row.provider) {
                contact = row.provider;
              }
              return contact;
            }
          },
          {
            "data": "account_sales_expenses", className: "column-width-limit", "render": function (data, type, row) {
              let account = "";
              if (row.account_sales) {
                account = row.account_sales;
              } else if (row.account_expenses) {
                account = row.account_expenses;
              }
              if (account == null || account == undefined || account == "") {
                account = "";
              }
              return account;
            }
          },
          {"data": "accounting_date", "orderable": false, "visible": false},
          {"data": "invoice_date", "orderable": false, "visible": false},
          {"data": "expedition_date", "orderable": false, "visible": false},
          {
            "data": "date", "type": "date", "render": function (data, type, row) {
              let date = "";
              if (row.accounting_date) {
                date = row.accounting_date;
              } else if (row.expedition_date) {
                date = row.expedition_date;
              } else if (row.invoice_date) {
                date = row.invoice_date;
              }
              // cambia el formato de la fecha de 2023-07-01 a 01/jul/2023
              if (date) {
                const [year, month, day] = date.split('-');
                const monthNames = [
                  "ene", "feb", "mar",
                  "abr", "may", "jun", "jul",
                  "ago", "sep", "oct",
                  "nov", "dic"
                ];
                date = day + '/' + monthNames[parseInt(month) - 1] + '/' + year;
              }
              {% comment %}
              if (date && date != "" && date.length >= 8) {
                const newDate = new Date(date);
                const day = newDate.getDate().toString().padStart(2, '0');
                const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
                const year = newDate.getFullYear().toString();
                date = day + '/' + month + '/' + year;
              }
              {% endcomment %}
              return date;
            }
          },
          {"data": "tax_country_id"},
          {"data": "tax_country", "visible": false},
          {"data": "departure_country_id"},
          {"data": "arrival_country_id"},
          {"data": "invoice_category", "visible": false},
          {"data": "invoice_type"},
          {
            "data": "transaction_type", render: function (data, type, row) {
              let transaction = "(Sin categorizar)";
              if (row.transaction_type) {
                transaction = row.transaction_type;
              }
              return transaction;
            }
          },
          {"data": "iae", "visible": false},
          {"data": "total_irpf_concp", "visible": irpf_visibility, "render": renderNumber},
          {"data": "total_eqtax_concp", "visible": eqtax_visibility, "render": renderNumber},
          {"data": "is_generated", "visible": false},
          {
            "data": "json_vat", "visible": false, "render": function (data, type, row) {
              vats = {};
              jsonvat = data.replaceAll("&quot;", '"');
              if (jsonvat && jsonvat != "" && jsonvat != "null") {
                vats = JSON.parse(jsonvat);
              }
              return data;
            }
          },
          {
            "data": "total_vat_euros_2_1", "visible": false, "render": function (data, type, row) {
              return renderVAT("2.1", vats);
            }
          },
          {
            "data": "total_vat_euros_3", "visible": false, "render": function (data, type, row) {
              return renderVAT("3", vats);
            }
          },
          {
            "data": "total_vat_euros_4", "visible": false, "render": function (data, type, row) {
              return renderVAT("4", vats);
            }
          },
          {
            "data": "total_vat_euros_4_8", "visible": false, "render": function (data, type, row) {
              return renderVAT("4.8", vats);
            }
          },
          {
            "data": "total_vat_euros_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("5", vats);
            }
          },
          {
            "data": "total_vat_euros_5_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("5.5", vats);
            }
          },
          {
            "data": "total_vat_euros_6", "visible": false, "render": function (data, type, row) {
              return renderVAT("6", vats);
            }
          },
          {
            "data": "total_vat_euros_7", "visible": false, "render": function (data, type, row) {
              return renderVAT("7", vats);
            }
          },
          {
            "data": "total_vat_euros_8", "visible": false, "render": function (data, type, row) {
              return renderVAT("8", vats);
            }
          },
          {
            "data": "total_vat_euros_9", "visible": false, "render": function (data, type, row) {
              return renderVAT("9", vats);
            }
          },
          {
            "data": "total_vat_euros_9_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("9.5", vats);
            }
          },
          {
            "data": "total_vat_euros_10", "visible": false, "render": function (data, type, row) {
              return renderVAT("10", vats);
            }
          },
          {
            "data": "total_vat_euros_12", "visible": false, "render": function (data, type, row) {
              return renderVAT("12", vats);
            }
          },
          {
            "data": "total_vat_euros_13", "visible": false, "render": function (data, type, row) {
              return renderVAT("13", vats);
            }
          },
          {
            "data": "total_vat_euros_13_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("13.5", vats);
            }
          },
          {
            "data": "total_vat_euros_14", "visible": false, "render": function (data, type, row) {
              return renderVAT("14", vats);
            }
          },
          {
            "data": "total_vat_euros_15", "visible": false, "render": function (data, type, row) {
              return renderVAT("15", vats);
            }
          },
          {
            "data": "total_vat_euros_17", "visible": false, "render": function (data, type, row) {
              return renderVAT("17", vats);
            }
          },
          {
            "data": "total_vat_euros_18", "visible": false, "render": function (data, type, row) {
              return renderVAT("18", vats);
            }
          },
          {
            "data": "total_vat_euros_19", "visible": false, "render": function (data, type, row) {
              return renderVAT("19", vats);
            }
          },
          {
            "data": "total_vat_euros_20", "visible": false, "render": function (data, type, row) {
              return renderVAT("20", vats);
            }
          },
          {
            "data": "total_vat_euros_21", "visible": false, "render": function (data, type, row) {
              return renderVAT("21", vats);
            }
          },
          {
            "data": "total_vat_euros_22", "visible": false, "render": function (data, type, row) {
              return renderVAT("22", vats);
            }
          },
          {
            "data": "total_vat_euros_23", "visible": false, "render": function (data, type, row) {
              return renderVAT("23", vats);
            }
          },
          {
            "data": "total_vat_euros_24", "visible": false, "render": function (data, type, row) {
              return renderVAT("24", vats);
            }
          },
          {
            "data": "total_vat_euros_25", "visible": false, "render": function (data, type, row) {
              return renderVAT("25", vats);
            }
          },
          {
            "data": "total_vat_euros_27", "visible": false, "render": function (data, type, row) {
              return renderVAT("27", vats);
            }
          },
          {"data": "vat_euros"},
          {
            "data": "amount_euros", "render": function (data, type, row) {
              let amount_euros = "";
              if (row.transaction_type == "Import DUA") {
                if (row.status != "Pendiente" && row.status != "Revision Pendiente") {
                  amount_euros = "-";
                }
              } else if (row.amount_euros) {
                amount_euros = row.amount_euros
              }
              return amount_euros;
            }
          },
          {
            "data": "total_euros_concp", "render": function (data, type, row) {
              let total_euros_concp = "";
              if (row.transaction_type == "Import DUA") {
                total_euros_concp = row.vat_euros;
              } else if (row.total_euros_concp) {
                total_euros_concp = row.total_euros_concp
              }
              return total_euros_concp;
            }
          },
          {"data": "tags", "orderable": false, "visible": false},
          {"data": "notes", "orderable": false, "visible": false},
          {"data": "discard_reason", "orderable": false, "visible": false},
          {"data": "discard_reason_notes", "orderable": false, "visible": false},
          {
            "data": "notes_private", "orderable": false, "render": function (data, type, row) {
              let info = "";
              const status = row.status
              const tags = row.tags;
              const notes = row.notes;
              let discard_reason = "";
              let discard_reason_notes = "";

              if (status == "Descartada") {
                discard_reason = row.discard_reason;
                if (discard_reason == "Otro") {
                  discard_reason_notes = row.discard_reason_notes;
                }
              }

              const infoData = {
                tags: tags,
                notes: notes,
                notes_private: data,
                discard_reason: discard_reason,
                discard_notes: discard_reason_notes,
              }
              if (data || tags || notes || discard_reason) {
                const infoJsonString = JSON.stringify(infoData).replace(/"/g, '&quot;');
                info = '<i id="triggerModal" class="cursor-pointer fas fa-info fa-xl" data-toggle="modal" data-target="#disagreedModal" onclick="showInfo(\'' + infoJsonString + '\')" data-row-id="' + row.pk + '" data-content="' + data + '"></i>';
              } else {
                info = '<i class="fas fa-info fa-xl" style="color: #999999;"></i>';
              }
              return info;
            }
          },
          {
            "data": "file", "orderable": false, "render": function (data, type, row) {
              let html = "";
              let button1 = "";
              let button2 = "";
              let button3 = "";
              let button4 = "";
              if (row.pk) {
                if('{{ perms.users.is_superuserAPP }}' == 'True'){
                button1 = `
                    <a class="btn btn-success btn-icon tooltip-wrapper tooltip-button" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Categorizar Factura">
                      <i class="fa-solid fa-pen-to-square"></i>
                    </a>
                  `;}else if('{{ perms.invoices.view_invoice }}' == 'True'){
                    button1 = `
                    <a class="btn btn-success btn-icon tooltip-wrapper tooltip-button" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Ver Factura">
                      <i class="fas fa-eye"></i>
                    </a>
                  `;}
              }
              if (row.file) {
                button2 = `
                    <a class="btn btn-info btn-icon" href="/media/${row.file}"
                        target="_blank"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="Descargar Factura"
                        download >
                      <i class="fa-solid fa-download"></i>
                    </a>
                  `;
              } else if (row.is_generated == "True") {
                button2 = `
                    <a class="btn btn-info btn-icon" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}/file/"
                        target="_blank"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="Descargar Factura"
                        download >
                      <i class="fa-solid fa-download"></i>
                    </a>
                  `;
              }
              if(row.is_generated == "True" && row.is_rectifying != "True" && row.status == "Revisada"){
                if('{{ perms.users.is_superuserAPP }}' == 'True'){
                button4 = `
                    <a class="btn btn-icon tooltip-wrapper tooltip-button" style="background-color: #FE8330; color: white;" href="{% url 'app_invoices:seller_invoice_new' seller.shortname %}?rectInv=${row.reference}&customer=${row.customer_pk}" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Emitir Factura Rectificativa">
                      <i class="fa-solid fa-file-pen"></i>
                    </a>
                  `;}
              }
              if (row.pk) {
                if (row.status != "Revisada") {
                  if('{{ perms.users.is_superuserAPP }}' == 'True'){
                  button3 = `
                      <a class="btn btn-danger btn-icon tooltip-wrapper tooltip-button"
                          href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}/delete"
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          data-bs-original-title="Eliminar Factura"
                          >
                        <i class="feather icon-trash-2"></i>
                      </a>
                    `;}
                }
              }
              if (row.pk || row.file) {
                html = `
                  <div class="text-center justify-content-center">
                      <button type="button" class="btn btn-secondary border  text-nowrap" data-bs-toggle ="dropdown" aria-expanded="false">
                        <i class="fa-solid fa-xl fa-ellipsis m-0"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end">
                        <li class= mb-1>${button1}</li>
                        <li class= mb-1>${button2}</li>
                        <li>${button4}</li>
                        <li>${button3}</li>
                      </ul>
                    </div>
                  `;
                htmlseparated = `
                    ${button1}
                    ${button2}
                    ${button4}
                    ${button3}
                  `;
              }
              return htmlseparated;
            }
          },
          {"data": "is_rectifying", "visible": false},
          {"data": "customer_pk", "visible": false }
        ],
        "language": {
          "lengthMenu": "_MENU_",
          "zeroRecords": "No se han encontrado facturas.",
          "info": "_START_ a _END_ de un total de _TOTAL_",
          "search": "Buscar:",
          "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
          "infoFiltered": "",
          "emptyTable": "Cargando...",
          "paginate": {
            "first": "Primero",
            "last": "Último",
            "previous": "Anterior",
            "next": "Siguiente"
          },
        },
        "drawCallback": function (settings, json) {
          $('#select_all').prop("checked", false);
          arrayRowData = [];
          arrayStatus = [];
          // document.getElementById("massiveAction").style.display = "none";

          // Destruir instancias existentes de dropdowns para evitar duplicados
          const existingDropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
          existingDropdowns.forEach(element => {
            const dropdownInstance = bootstrap.Dropdown.getInstance(element);
            if (dropdownInstance) {
              dropdownInstance.dispose();
            }
          });

          // Inicializar todos los dropdowns de Bootstrap
          const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
          dropdownElements.forEach(element => {
            new bootstrap.Dropdown(element);
          });

          // Inicializar todos los tooltips estándar
          $('[data-bs-toggle="tooltip"]').tooltip();

          // Inicializar tooltips para elementos que tienen otros data-bs-toggle
          const elementsWithTooltip = document.querySelectorAll('[title][data-bs-placement]');
          elementsWithTooltip.forEach(element => {
            if (!element.hasAttribute('data-bs-toggle') || element.getAttribute('data-bs-toggle') !== 'tooltip') {
              new bootstrap.Tooltip(element, {
                trigger: 'hover focus'
              });
            }
          });

          // Forzar que los tooltips desaparezcan al hacer click
          document.addEventListener('click', function(event) {
            // Ocultar todos los tooltips activos
            const tooltips = document.querySelectorAll('[data-bs-original-title], [title]');
            tooltips.forEach(element => {
              const tooltipInstance = bootstrap.Tooltip.getInstance(element);
              if (tooltipInstance) {
                tooltipInstance.hide();
              }
            });
          });
        }
      });

      $('#page-length').change(function () {
        table.page.len($(this).val()).draw();
      });

      $(table.table().node()).on('xhr.dt', function (e, settings, json) {
        if (inputExportCSV.value == true) {
          exportTableToCSV();
        }
      });
    }

    $(document).ready(function () {
      const seller = dj.value.seller;
      const categoryTable = '{{ category }}' || 'All';
      const filterCookieName = `DataTable_Invoices_List_${categoryTable}_${seller.shortname}`;
      const invoicesURL = window.location.href;

      const currentYear = new Date().getFullYear();
      const yearInput = document.getElementById('year');
      //yearInput.value = currentYear;

      const taxCountry = document.getElementById("multiple-taxcountries");

      // Llama a `onTaxCountryChange` solo si `taxCountry` está definido
      if (taxCountry) {
        // onTaxCountryChange();
        // taxCountry.addEventListener('change', onTaxCountryChange);
      } else {
        console.warn("El elemento 'multiple-taxcountries' no está disponible en el DOM.");
      }

      onChangeYear()
      addFiltersBadge(yearInput);

      dropdownFilterFormHack();
      createDT();
      massiveOptionPerm();

      if (!(invoicesURL.includes("/invoices/sales")) && !(invoicesURL.includes("/invoices/expenses"))) {
        checkCollapse();
        $('#collapse1').on('hidden.bs.collapse', function () {
          const toogleDetailButton = document.getElementById("toogleDetailButton");
          toogleDetailButton.innerHTML = `<i class="mdi mdi-eye fa-xl me-0"></i>`;
        });
        $('#collapse1').on('shown.bs.collapse', function () {
          const toogleDetailButton = document.getElementById("toogleDetailButton");
          toogleDetailButton.innerHTML = `<i class="mdi mdi-eye-off fa-xl me-0"></i>`;
        });
      }

      const dropdownButton = document.getElementById('dropdownButton');
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');

      dropdownButton.addEventListener('click', function (event) {
        dropdownFiltersForm.style.display = dropdownFiltersForm.style.display === 'block' ? 'none' : 'block';
        if (dropdownFiltersForm.style.display === 'block') {
          dropdownFiltersForm.scrollIntoView({behavior: "smooth", block: "center", inline: "center"});
        }
      });

      document.addEventListener('click', function (event) {
        if (!dropdownFiltersForm.contains(event.target) && !dropdownButton.contains(event.target)) {
          dropdownFiltersForm.style.display = 'none';
        }
      });

      dropdownFiltersForm.addEventListener('click', function (event) {
        hideMultiDropDown(event);
        event.stopPropagation();

      });

      function hideMultiDropDown(event) {
        const multiSelectorElements = document.querySelectorAll('[separator]');

        multiSelectorElements.forEach((element) => {
          if (!event.composedPath().includes(element) && typeof element.hideDropDown === 'function') {
            element.dropDownVisible = false;
            element.hideDropDown();
          }
        });
      }

      addListenerToMultiSelector(multipleStatus);
      addListenerToMultiSelector(multipleEconomicActivities);
      addListenerToMultiSelector(multipleMonth);
      addListenerToMultiSelector(multipleTaxCountries, onTaxCountryChange);
      addListenerToMultiSelector(multipleVatRates);
      addListenerToMultiSelector(multipleDepartureCountries);
      addListenerToMultiSelector(multipleAccountingAccount);
      addListenerToMultiSelector(multipleArrivalCountries);
      addListenerToMultiSelector(multiCheckbox);
      addListenerToMultiSelector(multipleInvoiceType);

      const selectFilters = filterForm.querySelectorAll('select');
      selectFilters.forEach(select =>
        select.addEventListener('change', () => {
          addFiltersBadge(select);
          checkFilterState();
        })
      );

      // Manejar el chevron del selector de filas por página
      const showSelect = document.getElementById('show');
      if (showSelect) {
        let isDropdownOpen = false;

        // Detectar cuando se abre el dropdown
        showSelect.addEventListener('mousedown', function(e) {
          // Pequeño delay para permitir que el dropdown se abra
          setTimeout(() => {
            if (document.activeElement === showSelect) {
              isDropdownOpen = true;
              this.classList.add('dropdown-open');
            }
          }, 10);
        });

        // Detectar cuando se cierra el dropdown
        showSelect.addEventListener('blur', function() {
          isDropdownOpen = false;
          this.classList.remove('dropdown-open');
        });

        showSelect.addEventListener('change', function() {
          isDropdownOpen = false;
          this.classList.remove('dropdown-open');
        });

        // Manejar teclas (Enter, Escape, etc.)
        showSelect.addEventListener('keydown', function(e) {
          if (e.key === 'Escape' || e.key === 'Enter') {
            isDropdownOpen = false;
            this.classList.remove('dropdown-open');
          }
        });
      }

      resetCookie(filterCookieName);
      toggleApplyFiltersButton(false);

      // Inicializar tooltip del botón de descarga
      const downloadButton = document.getElementById('downloadButton');
      if (downloadButton) {
        // Crear tooltip manualmente
        const downloadTooltip = new bootstrap.Tooltip(downloadButton, {
          trigger: 'hover',
          placement: 'top',
          title: downloadButton.getAttribute('title')
        });

        // Ocultar tooltip cuando se abre el dropdown
        downloadButton.addEventListener('shown.bs.dropdown', function () {
          downloadTooltip.hide();
        });

        // Ocultar tooltip cuando se hace click
        downloadButton.addEventListener('click', function () {
          downloadTooltip.hide();
        });
      }

    });

    let arrayRowData = [];
    let arrayStatus = [];

    $('#invoices-table').on('click', 'input.custom-checkbox[data-select="true"]', function (e) {
      e.stopPropagation(); // Evita que el clic se propague

      var row = $(this).closest('tr');
      var dataTable = $('#invoices-table').DataTable();

      if (dataTable.row(row).nodes().to$().hasClass('selected')) {
        dataTable.row(row).deselect();
      } else {
        dataTable.row(row).select();
      }
    });

    $('#select_all').on('change', function () {
      var isChecked = $(this).prop('checked');
      var rows = table.rows({page: 'current'}).nodes();

      // Selecciona o deselecciona todas las filas según el estado de la casilla
      $(rows).find('input[type="checkbox"]').prop('checked', isChecked);

      // Dispara el evento 'select.dt' después de seleccionar todas las filas
      if (isChecked) {
        table.rows({page: 'current'}).select();
      } else {
        table.rows({page: 'current'}).deselect();
      }
    });

    // Select rows
    $('#invoices-table').on('select.dt', function (e, dt, type, indexes) {
      let rowData = dt.rows(indexes).data().toArray();
      let status = dt.rows(indexes).data().toArray();

      arrayRowData = arrayRowData.concat(rowData.map(row => row.pk));
      arrayStatus = arrayStatus.concat(status.map(row => row.status));

      document.getElementById("selectedPk").value = JSON.stringify(arrayRowData);
      if (arrayRowData.length > 0) {
        document.getElementById("selectValue").removeAttribute("disabled");
        // document.getElementById("massiveAction").style.display = "block";
      }
      urlForm();
    });

    // Deselect rows
    $('#invoices-table').on('deselect.dt', function (e, dt, type, indexes) {
      let rowDataDelete = dt.rows(indexes).data().toArray();
      let statusDelete = dt.rows(indexes).data().toArray();
      let pkValue = rowDataDelete[0].pk;
      let index = arrayRowData.indexOf(pkValue);

      arrayStatus.splice(index, 1);
      arrayRowData = arrayRowData.filter(row => !rowDataDelete.some(r => r.pk === row));

      document.getElementById("selectedPk").value = JSON.stringify(arrayRowData);
      if (arrayRowData.length == 0) {
        document.getElementById("selectValue").setAttribute("disabled", "disabled");
        document.getElementById("selectValue").value = "empty";
        // document.getElementById("massiveAction").style.display = "none";
      }
      urlForm();
    });

    const filter = () => {
      let show = document.getElementById("show").value;
      if (show) {
        table.page.len(show);
      }
      retryAttempts = 0;
      table.draw();
    }

    //***** HANDLING FILTERS AND COOKIES ********//
    function filtersSelected() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      let hasValue = false;

      return Array.from(selectInputs).some(select => select.value) || Array.from(multiSelectors).some(multiSelector => multiSelector.dataset.value);
    }

    function toggleApplyFiltersButton(state) {
      const applyFiltersButton = document.getElementById("applyFiltersButton");
      applyFiltersButton.disabled = !state;
    }

    // Función para aplicar filtros y cerrar el desplegable
    function applyFilters() {
      if (checkFilterState()) {
        filter();
      }
      setCookieFilterState();
      toggleApplyFiltersButton(false);

      table.on('draw', function () {
        const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
        dropdownFiltersForm.style.display = 'none';
      });
    }

    // Restablecer todos los filtros
    function resetFilters() {

      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');

      // Restablecer los valores de los selects
      selectInputs.forEach((select) => {
        select.value = '';
        console.log(`Reseteando select #${select.id} - valor actual: ${select.value}`);
      });

      // Restablecer los valores de los multi-selectores
      multiSelectors.forEach((multiSelector) => {
        let listenerCallback = null;

        multiSelector.dataset.value = ''; // Resetear dataset
        multiSelector.value = '';

        if (typeof multiSelector.updateItems === 'function') {
          multiSelector.updateItems();
        }

        // Sincronizar visualmente el estado de los checkboxes
        const shadowRoot = multiSelector.shadowRoot || multiSelector;
        const allCheckboxes = shadowRoot.querySelectorAll('input[type="checkbox"]');
        const selectAllInput = shadowRoot.querySelector('#select-all');

        // Restablecer "Select All" y checkboxes
        allCheckboxes.forEach((checkbox) => {
          checkbox.checked = false;
        });

        if (selectAllInput) {
          selectAllInput.checked = false;
          console.log(`"Select All" reseteado en #${multiSelector.id}`);
        }

        // Forzar actualización del dataset y del estado
        multiSelector.setAttribute('value', '');
        multiSelector.setAttribute('data-value', '');

        // check if multicheck is "multiple-taxcountries" to call onTaxCountryChange
        if (multiSelector.id === 'multiple-taxcountries') {
          listenerCallback = onTaxCountryChange;
        }

        addListenerToMultiSelector(multiSelector, listenerCallback);

      });

      // Llamada a funciones dependientes
      onTaxCountryChange();
      onChangeYear();

      // Actualizar los filtros y el estado del botón
      filter();
      clearFiltersBadge();
      setCookieFilterState();
      toggleApplyFiltersButton(false);


      table.on('draw', function () {
        const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
        dropdownFiltersForm.style.display = 'none';
      });
    }

    // Añadir badge de filtros si se han aplicado
    function addFiltersBadge(element) {
      const elementID = element.id;
      filtersDictCount[elementID] = !!element.value || !!element.dataset.value;

      const badgeNumber = Object.values(filtersDictCount).filter(value => value === true).length;
      const badge = document.getElementById('id-filter-notification');

      if (badgeNumber > 0) {
        badge.classList.remove('d-none');
        badge.innerHTML = badgeNumber;
      } else {
        badge.classList.add('d-none');
        badge.innerHTML = '';
      }
    }

    const clearFiltersBadge = () => {
      Object.keys(filtersDictCount).forEach(key => filtersDictCount[key] = false);
      const badge = document.getElementById('id-filter-notification');
      badge.classList.add('d-none');
      badge.innerHTML = '';
    }

    function getFilterValues() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      let filterDict = {};

      selectInputs.forEach((select) => {
        filterDict[select.id] = select.value;
      });

      multiSelectors.forEach((multiSelector) => {
        if (multiSelector.dataset.value) {
          filterDict[multiSelector.id] = multiSelector.dataset.value.split(multiSelector.getAttribute('separator'));
        } else {
          filterDict[multiSelector.id] = [];
        }
      });

      return filterDict;
    }

    function checkFilterState(){
      const seller = dj.value.seller;
      const currentFilter = getFilterValues();
      const categoryTable = '{{ category }}' || 'All';
      const cookieName = `DataTable_Invoices_List_${categoryTable}_${seller.shortname}`;
      const cookieFilter = JSON.parse(getCookie(cookieName) || '{}');

      // Comprueba si algún filtro tiene valor
      const hasActiveFilters = Object.values(currentFilter).some(value => {
        return Array.isArray(value) ? value.length > 0 : !!value;
      });

      toggleApplyFiltersButton(JSON.stringify(currentFilter) !== JSON.stringify(cookieFilter));

      return JSON.stringify(currentFilter) !== JSON.stringify(cookieFilter);
    }

    function getCookie(name) {
      const cookie = document.cookie.split('; ').find(row => row.startsWith(name + '='));
      return cookie ? decodeURIComponent(cookie.split('=')[1]) : null;
    }

    function setCookieFilterState() {
      const seller = dj.value.seller;
      const filter = getFilterValues();
      const categoryTable = '{{ category }}' || 'All';
      const cookieName = `DataTable_Invoices_List_${categoryTable}_${seller.shortname}`;
      document.cookie = `${cookieName}=${JSON.stringify(filter)}; path=/`;
    }

    function resetCookie(name) {
      const defaultValues = getFilterValues();
      document.cookie = `${name}=${JSON.stringify(defaultValues)}; path=/`;
    }
    //***** HANDLING FILTERS AND COOKIES ********//

    function getTotals(params) {
      let p = params;

      if (!p || p == undefined || p == null || p == "") {
        p = "";
      } else if (p.charAt(0) == "&") {
        p[0] = "?";
      }

      invoiceDataJSON.value = {
        "status_array": [],
        "months_array": [],
        "country": [],
        "departure_country": [],
        "accounting_accounts": [],
        "arrival_country": [],
        "year": "all",
        "economic_activity": [],
        "regime": [],
        "transaction_type": [],
        "invoice_type": [],
        "invoices_count": 0,

        // Totales de ventas
        "total_sales_amount": 0,
        "total_sales_vat": 0,
        "total_sales_irpf": 0,
        "total_sales_eqtax": 0,
        "total_sales": 0,

        // Totales de gastos
        "total_expenses_amount": 0,
        "total_expenses_vat": 0,
        "total_expenses_irpf": 0,
        "total_expenses_eqtax": 0,
        "total_expenses": 0,

        // Porcentaje de gastos deducibles al 70%
        "total_sevent_porcent": 0,

        // Totales de beneficios
        "total_profit_amount": 0,
        "total_profit_vat": 0,
        "total_profit_irpf": 0,
        "total_profit_eqtax": 0,
        "total_result": 0
      };

      const url = "{% url 'app_invoices:seller_invoices_json' seller.shortname %}?" + p;
      $.ajax({
        url: url,
        type: "GET",
        dataType: "JSON",
        success: function (data) {

          // status
          let s = document.getElementById("multiple-status");
          s = s && s.dataset.value ? s.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const ds = data.status_array ? data.status_array.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-status es ds=: ${ds}`);

          // Economic activity
          let ea = document.getElementById("multiple-economicactivities");
          ea = ea && ea.dataset.value ? ea.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dea = data.economic_activity ? data.economic_activity.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-status es ds=: ${ds}`);

          // year and month
          const y = document.getElementById("year").value.toString().toLowerCase().trim();
          let m = document.getElementById("multiple-month");
          m = m && m.dataset.value ? m.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dm = data.months_array ? data.months_array.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-month es dm=: ${dm}`);

          // invoice types
          let it = document.getElementById("multiple-invoicetype");
          it = it && it.dataset.value ? it.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dit = data.invoice_type ? data.invoice_type.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-transactions es dit=: ${dit}`);

          // Tax country
          let c = document.getElementById("multiple-taxcountries");
          c = c && c.dataset.value ? c.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;

          // Departure country
          let dc = document.getElementById("multiple-departurecountries");
          dc = dc && dc.dataset.value ? dc.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const ddc = data.departure_country ? data.departure_country.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-departurecountries es ddc=: ${ddc}`);

          // Accounting account
          let aa = document.getElementById("multiple-accountingaccount");
          aa = aa && aa.dataset.value ? aa.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const aac = data.accounting_accounts ? data.accounting_accounts.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-accountingaccount es aac=: ${aac}`);

          // Arrival country
          let ac = document.getElementById("multiple-arrivalcountries");
          ac = ac && ac.dataset.value ? ac.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dac = data.arrival_country ? data.arrival_country.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-arrivalcountries es dac=: ${dac}`);

          // Multiple transaction
          let mt = document.getElementById("multiple-transactions");
          mt = mt && mt.dataset.value ? mt.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dmt = data.transaction_type ? data.transaction_type.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-transactions es dmt=: ${dmt}`);

          let or = document.getElementById("multiple-origin");
          or = or && or.dataset.value ? or.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dor = data.origin ? data.origin.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;

          let rc = document.getElementById("multiple-reverse-charge");
          rc = rc && rc.dataset.value ? rc.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const drc = data.reverse_charge ? data.reverse_charge.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;

          let eq = document.getElementById("multiple-eqtax");
          eq = eq && eq.dataset.value ? eq.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const deq = data.eqtax ? data.eqtax.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;

          let sameIAE = false;
          let sameStatus = false;
          let sameMonth = false;
          let sameInvoiceType = false;
          let sameYear = false;
          let sameCountry = false;
          let sameDepartureCountry = false;
          let sameAccountingAccounty = false;
          let sameArrivalCountry = false;
          let sameMultipleTransactions = false;

          if (
            (s && ds && s === ds) || (ds === "" && !s)) {
            sameStatus = true;
            debugLog(`sameStatus es ea=: ${sameStatus}`);
          }

          if ((ea && dea && ea === dea) ||(dea === "" && !ea)) {
            sameIAE = true;
            debugLog(`sameIAE es ea=: ${sameIAE}`);
          }

          if ((m && dm && m === dm) ||(dm === "" && !m)) {
            sameMonth = true;
          }

          if ((mt && dmt && mt === dmt) || (dmt === "" && !mt)) {
            sameMultipleTransactions = true;
          }

          if ((it && dit && it === dit) || (dit === "" && !it)) {
            sameInvoiceType = true;
          }

          if (y == data.year || (data.year == "all" && !y)) {
            sameYear = true;
          }

          if ((dc && ddc && dc === ddc) || (ddc === "" && !dc)) {
            sameDepartureCountry = true;
          }

          if ((aa && aac && aa === aac) || (aac === "" && !aa)) {
            sameAccountingAccounty = true;
          }

          if ((ac && dac && ac === dac) || (dac === "" && !ac)) {
            sameArrivalCountry = true;
          }

          if (
            (c && data.country && c === data.country.join(',').toString().toLowerCase().trim()) ||
            (data.country.toString().toLowerCase().trim() == "" && !c)
          ) {
            sameCountry = true;
          }

          if (sameIAE && sameStatus && sameYear && sameMonth && sameCountry && sameDepartureCountry && sameArrivalCountry && sameInvoiceType && sameMultipleTransactions) {
            // Aquí se guarda la peticion
            invoiceDataJSON.value = data;
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          // Aquí se ejecuta el código cuando hay un error en la petición
          console.error("Error en la peticion AJAX")
        }
      });
    }
    const onClickExportCSV = () => {
      inputExportCSV.value = true;
      document.getElementById("show").value = -1;
      table.page.len(-1);
      table.draw();
    }

    const exportTableToCSV = async () => {
      // Disable Export CSV
      inputExportCSV.value = false;

      // Obtenemos los datos de la tabla
      let data = table.ajax.json().data;

      // Creamos la variable CSV
      let csv = '\ufeff';

      // Creamos el encabezado de las columnas
      let header = [];

      // Creamos la lista de columnas a excluir
      const exclude = [
        "account_sales_expenses", "Cuenta Contable",
        "contact", "Cliente/Proveedor",
        "invoice_date", "Fecha Factura",
        "date", "Fecha",
        "file", "Acciones",
        "pdf_amazon", "PDF Amazon",
        "json_vat", "JSON VATS",
        "tax_country_id", "País IVA"
      ];

      // Creamos el separador de columnas
      const separator = ";";

      // Obtenemos los country codes y los vat rates
      let countries = [];
      let validVats = [];
      data.forEach(function (row) {
        debugLog(row);
        if (row.tax_country_id && !countries.includes(row.tax_country_id)) {
          countries.push(row.tax_country_id);
        }
      });

      if (countries && countries.length > 0) {
        dj.value.vat_rates.forEach(function (vat) {
          debugLog("vat.country_code: ", vat.country_code);
          debugLog("vat.vat_rates: ", vat.vat_rates);
          if (countries.includes(vat.country_code)) {
            vat.vat_rates.split(',').forEach(function (vat_rate) {
              if (!validVats.includes(vat_rate.trim())) {
                validVats.push(vat_rate.trim());
              }
            });
          }
        });
      }

      {% comment %} debugLog("countries: ", countries); {% endcomment %}
      {% comment %} debugLog("validVats: ", validVats); {% endcomment %}
      {% comment %} debugLog("exclude: ", exclude); {% endcomment %}

      // Obtenemos los encabezados de las columnas
      table.columns().every(function () {
        let temp_header = [];
        temp_header.push(this.header().innerText);
        for (item of temp_header) {
          if (!exclude.includes(item)) {
            if (item.includes("IVA") && item.includes("%(€)")) {
              validVats.forEach(function (vat_rate) {
                vat_rate = vat_rate.replaceAll(".", ",").replaceAll(" ", "");
                if (item.includes("IVA " + vat_rate + "%(€)")) {
                  header.push(item);
                }
              });
            } else {
              header.push(item);
            }
          }
        }
      });

      // Agregamos los encabezados al string CSV
      csv += header.join(separator) + '\n';

      // Agregamos los datos al string CSV
      data.forEach(function (row) {
        var rowData = [];
        var vats = {};
        // Recorremos cada celda de la fila
        for (var prop in row) {
          if (!exclude.includes(prop)) {
            if (!(prop.includes("€") || prop.includes("euros") || prop.includes("total"))) {
              rowData.push(`"${row[prop]}"`);
            } else if (prop.includes("total_vat_euros_")) {
              let vatNumber = prop.trim().replace("total_vat_euros_", "").trim();
              vatNumber = vatNumber.replace("_", ".").trim();
              if (validVats.includes(vatNumber)) {
                vatValue = renderVAT(vatNumber, vats).toString().replace(',', '').replace('.', ',');
                {% comment %} debugLog("Export CSV | vatNumber: ", vatNumber + " | vatValue: ", vatValue); {% endcomment %}
                rowData.push(`"${vatValue}"`);
              }
            } else {
              let data = row[prop];
              data = renderNumber(data);
              let dataEsp = data.toString().replace(',', '').replace('.', ',');
              rowData.push(`"${dataEsp}"`)
            }
          } else if (prop.includes("json_vat")) {
            let jsonvat = row[prop].toString().replaceAll("&quot;", '"');
            if (jsonvat && jsonvat != "" && jsonvat != "null") {
              vats = JSON.parse(jsonvat);
            }
            {% comment %} debugLog("Export CSV | vats: ", vats); {% endcomment %}
          }
        }
        csv += rowData.join(separator) + '\n';
      });

      // Descargamos el archivo CSV
      let csvData = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csv.replaceAll("&amp;", "&").replaceAll("&#x27;", "&"));
      //csvData = csvData.replace("&amp;", "&");
      let link = document.createElement('a');
      link.href = csvData;
      {% if seller.shortname %}
        link.download = 'facturas_{{seller.shortname}}.csv';
      {% else %}
        link.download = 'facturas.csv';
      {% endif %}
      link.click();

      // Disable Export CSV
      inputExportCSV.value = false;
    }

    const urlForm = () => {
      const confirmButton = document.getElementById("enabledButton");
      const deleteButton = document.getElementById("deleteButton");
      const footerMessage = document.getElementById("footer");

      let select = document.getElementById("selectValue").value;
      document.getElementById("selectAction").value = select;

      let resultStatus = $.inArray('Revisada', arrayStatus);

      if (select != "empty" && resultStatus == -1) {
        confirmButton.removeAttribute("disabled");
        footerMessage.style.display = "none";

        if (select == "delete" && resultStatus != -1) {
          confirmButton.classList.add("d-none");
          deleteButton.classList.remove("d-none");
        } else if (select == "delete" && resultStatus == -1) {
          footerMessage.style.display = "none";
          confirmButton.classList.add("d-none");
          confirmButton.setAttribute("disabled", "disabled");
          deleteButton.classList.remove("d-none");
        } else {
          footerMessage.style.display = "none";
          confirmButton.classList.remove("d-none");
          deleteButton.classList.add("d-none");
          confirmButton.removeAttribute("disabled");
        }

      } else if (select != "empty" && resultStatus != -1 && select != "delete") {
        confirmButton.removeAttribute("disabled");
        footerMessage.style.display = "none";
        deleteButton.classList.add("d-none");
        confirmButton.classList.remove("d-none");

      } else {
        footerMessage.style.display = "none";
        deleteButton.classList.add("d-none");
        confirmButton.classList.remove("d-none");
        confirmButton.setAttribute("disabled", "disabled");
        if (select == "delete") {
          footerMessage.style.display = "block";
        }

      }
    }

    const formSelect = document.getElementById("formSelect");
    formSelect.addEventListener("submit", function (event) {
      let select = document.getElementById("selectValue").value;
      if (select == "download") {
        const url = formSelect.getAttribute("action");
        const method = formSelect.getAttribute("method");
        const data = new FormData(formSelect);

        fetch(url, {
          method: method,
          body: data,
        }).then(response => {

          const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            didOpen: (toast) => {
              toast.addEventListener('mouseenter', Swal.stopTimer)
              toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
          });

          if (response.status == 200) {
            Toast.fire({
              icon: 'success',
              title: 'Facturas descargadas correctamente'
            })
          } else if (response.status == 204) {
            Toast.fire({
              icon: 'error',
              title: 'Ocurrió un error o no hay facturas para descargar'
            })
          } else {
            Toast.fire({
              icon: 'error',
              title: 'Error al descargar las facturas'
            })
          }
        })
          .catch(error => {
            const Toast = Swal.mixin({
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              timer: 2500,
            });
            Toast.fire({
              icon: 'error',
              title: 'No se ha podido conectar con el servidor. Comunícate con el departamento de IT'
            })
          });
      }
    });

    const showInfo = (infoData) => {
      const parsedInfoData = JSON.parse(infoData.replace(/&quot;/g, '"'));

      const modal = document.getElementById("infoModal");
      const modalBody = modal.querySelector('.modal-body');
      const tagsParagraph = modalBody.querySelector('#tags');
      const notesParagraph = modalBody.querySelector('#notes');
      const notesPrivateParagraph = modalBody.querySelector('#notes_private');
      const discardParagraph = modalBody.querySelector('#discard');
      const discardNotesParagraph = modalBody.querySelector('#discard_notes');

      // Clear the content of all paragraphs
      tagsParagraph.textContent = '';
      notesParagraph.textContent = '';
      notesPrivateParagraph.textContent = '';
      discardParagraph.textContent = '';
      discardNotesParagraph.textContent = '';

      if (parsedInfoData.tags) {
        tagsParagraph.textContent = "ETIQUETAS: " + parsedInfoData.tags;
      }
      if (parsedInfoData.notes) {
        notesParagraph.textContent = "NOTAS: " + parsedInfoData.notes;
      }
      if (parsedInfoData.notes_private) {
        notesPrivateParagraph.textContent = "NOTAS PRIVADAS: " + parsedInfoData.notes_private;
      }
      if (parsedInfoData.discard_reason) {
        discardParagraph.textContent = "RAZÓN DE DESCARTE: " + parsedInfoData.discard_reason;
      }
      if (parsedInfoData.discard_notes) {
        discardNotesParagraph.textContent = "NOTA DE DESCARTE: " + parsedInfoData.discard_notes;
      }

      $('#infoModal').modal('show');
    }

    const generateCSV = async () => {
      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static'
      });
      modalLoading.show();
      await new Promise(r => setTimeout(r, 1000));

      let dataFiltered = prepareFilterData();
      let jsonData = JSON.stringify(dataFiltered);

      const generateCSVResponse = await fetch("{% url 'app_invoices:generate_csv' seller.shortname %}", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': "{{ csrf_token }}"
        },
        body: jsonData
      });


      if (generateCSVResponse.status == 200) {
        const blob = await generateCSVResponse.blob();
        const blobUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.setAttribute('download', `Listado_facturas_{{seller.shortname}}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Toast.fire({
          icon: 'success',
          title: generateCSVResponse.headers.get('X-Message')
        });

      } else if (generateCSVResponse.status == 204) {
        Toast.fire({
          icon: 'warning',
          title: generateCSVResponse.headers.get('X-Message')
        });
      } else if (generateCSVResponse.status == 500) {
        Toast.fire({
          icon: 'error',
          title: generateCSVResponse.headers.get('X-Message')
        });
      } else {
        Toast.fire({
          icon: 'error',
          title: 'Ha surgido un error al generar el archivo'
        });
      }
      modalLoading.hide();
    }

    const downloadInvoices = async () => {
      const shortname = "{{seller.shortname}}";
      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static'
      });
      modalLoading.show();
      await new Promise(r => setTimeout(r, 1000));

      let dataFiltered = prepareFilterData();

      // Timeout para evitar que el modal se quede colgado
      const timeoutId = setTimeout(() => {
        modalLoading.hide();
        Toast.fire({
          icon: 'error',
          title: 'La descarga está tardando demasiado. Por favor, intenta de nuevo.'
        });
      }, 300000); // 5 minutos

      try {
        const invoicesResponse = await fetch("{% url 'app_invoices:seller_invoices_massive_download' seller.shortname %}", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': "{{ csrf_token }}"
          },
          body: JSON.stringify(dataFiltered)
        });

        clearTimeout(timeoutId); // Limpiar el timeout si la respuesta llega antes

        if (invoicesResponse.status == 200) {
          modalLoading.hide();
          const blob = await invoicesResponse.blob();
          const blobUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = blobUrl;
          link.setAttribute('download', `Facturas_${shortname}.zip`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          Toast.fire({
            icon: 'success',
            title: 'Facturas descargadas correctamente'
          });

        } else if (invoicesResponse.status == 204) {
          modalLoading.hide();
          Toast.fire({
            icon: 'warning',
            title: 'No hay facturas para descargar'
          });
        } else if (invoicesResponse.status == 500) {
          modalLoading.hide();
          Toast.fire({
            icon: 'error',
            title: 'Error interno del servidor. Por favor, intenta con menos facturas.'
          });
        } else {
          modalLoading.hide();
          Toast.fire({
            icon: 'error',
            title: 'Ha surgido un error al descargar las facturas'
          });
        }
      } catch (error) {
        clearTimeout(timeoutId);
        console.error("Error in downloadInvoices: ", error);
        Toast.fire({
          icon: 'error',
          title: 'Error al descargar las facturas: ' + error.message
        });
      } finally {
        modalLoading.hide();
      }
    }

    // Obtiene valores de filtros seleccionados
    function prepareFilterData() {
      // transactions
      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;
      let selectedTransactionValues = multiple_transactions ? multiple_transactions.split(', ') : [];

      // departurecountries
      let multiple_departurecountries = document.getElementById("multiple-departurecountries").dataset.value;
      let selectedDepartureCountryValues = multiple_departurecountries ? multiple_departurecountries.split(', ') : [];

      // accountingaccount
      let multiple_accountingaccount = document.getElementById("multiple-accountingaccount").dataset.value;
      let selectedAccountingAccountValues = multiple_accountingaccount ? multiple_accountingaccount.split(', ') : [];

      // arrivalcountries
      let multiple_arrivalcountries = document.getElementById("multiple-arrivalcountries").dataset.value;
      let selectedArrivalCountryValues = multiple_arrivalcountries ? multiple_arrivalcountries.split(', ') : [];
      // taxcountries
      let multiple_taxcountries = document.getElementById("multiple-taxcountries").dataset.value;
      let selectedTaxCountryValues = multiple_taxcountries ? multiple_taxcountries.split(', ') : [];
      // vatrates
      let multiple_vatrates = document.getElementById("multiple-vatrates").dataset.value;
      let selectedVatRateValues = multiple_vatrates ? multiple_vatrates.split(', ') : [];
      // status
      let multiple_status = document.getElementById("multiple-status").dataset.value;
      let selectedStatusValues = multiple_status ? multiple_status.split(', ') : [];
      // economic_activity
      let multiple_economic_activity = document.getElementById("multiple-economicactivities").dataset.value;
      let selectedeconomicactivityValues = multiple_economic_activity ? multiple_economic_activity.split(', ') : [];
      // month
      let multiple_month = document.getElementById("multiple-month").dataset.value;
      let selectedMonthValues = multiple_month ? multiple_month.split(', ') : [];
      // invoice_type
      let multiple_invoicetype = document.getElementById("multiple-invoicetype").dataset.value;
      let selectedInvoiceTypeValues = multiple_invoicetype ? multiple_invoicetype.split(', ') : [];

      // origin
      let multiple_origin = document.getElementById("multiple-origin").dataset.value;
      let selectedOriginValues = multiple_origin ? multiple_origin.split(', ') : [];

      // reverse_charge
      let multiple_reverse_charge = document.getElementById("multiple-reverse-charge").dataset.value;
      let selectedReverseChargeValues = multiple_reverse_charge ? multiple_reverse_charge.split(', ') : [];

      // eqtax
      let multiple_eqtax = document.getElementById("multiple-eqtax").dataset.value;
      let selectedEqtaxValues = multiple_eqtax ? multiple_eqtax.split(', ') : [];

      let seachInput = document.getElementById("search").value;
      let currentURL = window.location.href;
      let invoice_type;

      if (currentURL.includes("/invoices/sales")) {
        invoice_type = "sales";
      } else if (currentURL.includes("/invoices/expenses")) {
        invoice_type = "expenses";
      } else if (currentURL.includes("/invoices/transfers")) {
        invoice_type = "transfers";
      } else {
        invoice_type = "all";
      }

      return {
        invoice_status: selectedStatusValues,
        iae: selectedeconomicactivityValues,
        year: document.getElementById("year").value,
        month: selectedMonthValues,
        tax_country_id: selectedTaxCountryValues,
        departure_country_id: selectedDepartureCountryValues,
        accounting_account_id: selectedAccountingAccountValues,
        arrival_country_id: selectedArrivalCountryValues,
        vat_rate_id: selectedVatRateValues,
        transaction_type: selectedTransactionValues,
        invoice_type: invoice_type,
        search: seachInput,
        multiple_invoice_type: selectedInvoiceTypeValues,
        origin: selectedOriginValues,
        reverse_charge: selectedReverseChargeValues,
        eqtax: selectedEqtaxValues,
      };

    }

    // DYNAMIC HANDLING FOF MULTICHECK FILTERS
    function addListenerToMultiSelector(element, callback) {
      // Verificar si el elemento existe
      if (!element) {
        console.warn("Elemento no encontrado para manejar el cambio de checkbox múltiple.");
        return; // Salir de la función si el elemento no existe
      }


      const shadowRoot = element.shadowRoot || element;
      const selectAll = shadowRoot.querySelector('#select-all');
      const inputsInELement = element.querySelectorAll('input[type="checkbox"]');


      if (selectAll) {
          selectAll.addEventListener('change', function () {
              addFiltersBadge(element);
              if (callback) callback();
          });
      }

      inputsInELement.forEach(input => {
        input.addEventListener('change', function () {
          addFiltersBadge(element);
          if (callback) callback();
        });
      });
    }

    // Función para actualizar el estado del selector de IVA
    function onTaxCountryChange() {
      const taxCountry = document.getElementById("multiple-taxcountries");
      const vatRatesSelector = document.getElementById("multiple-vatrates");
      const taxCountryValue = taxCountry && taxCountry.dataset.value ? taxCountry.dataset.value.split(',').map(item => item.trim()) : [];

      debugLog("Selected taxCountryValue:", taxCountryValue);

      // Deshabilitar y limpiar el selector de IVA inicialmente
      vatRatesSelector.setAttribute("disabled", "disabled");
      vatRatesSelector.setAttribute("data-value", "");
      vatRatesSelector.value = '';
      vatRatesSelector.updateItems();

      // Si hay un país seleccionado, activar el selector y rellenar valores
      if (taxCountryValue.length > 0) {
        vatRatesSelector.removeAttribute("disabled");
        const vatRates = getVatRateValues(taxCountryValue);
        updateMultiCheckboxOptions(vatRatesSelector.id, vatRates);
      }

      updateESCheckboxDependences(taxCountryValue);

    }

    function getVatRateValues(taxCountryValues) {
      // Aseguramos que taxCountryValues sea un arreglo
      if (!Array.isArray(taxCountryValues)) {
        taxCountryValues = taxCountryValues.split(',').map(item => item.trim());
      }

      const vatRatesSet = new Set(); // Usamos un Set para evitar duplicados

      // Iteramos sobre los países seleccionados y obtenemos sus valores de IVA
      taxCountryValues.forEach(countryCode => {
        const vatRates = dj.value.vat_rates.find(rate => rate.country_code === countryCode);
        if (vatRates) {
          vatRatesSet.add('0'); // Añadimos el 0% de IVA
          vatRates.vat_rates.split(',').forEach(rate => vatRatesSet.add(rate.trim()));
        }
      });

      // Convertimos el Set en un Array ordenado
      return Array.from(vatRatesSet).map(Number).sort((a, b) => a - b);
    }

    const updateMultiCheckboxOptions = (multiCheckboxId, ratesArray) => {
      const multiCheckbox = document.querySelector(`#${multiCheckboxId}`);
      if (multiCheckbox) {
        const ulElement = document.createElement('ul');
        ulElement.setAttribute('slot', 'check-values');
        ratesArray.forEach(rate => {
          const liElement = document.createElement('li');
          liElement.className = 'cursor-default';
          liElement.setAttribute('id', 'multi-value');
          liElement.setAttribute('value', rate);
          liElement.setAttribute('multi-title', rate + '%');
          ulElement.appendChild(liElement);

          const existingSlot = multiCheckbox.querySelector('ul[slot="check-values"]');
          if (existingSlot) {
              multiCheckbox.removeChild(existingSlot);
          }
          multiCheckbox.appendChild(ulElement);
        });
        multiCheckbox.updateItems();

        setTimeout(() => {
            addListenerToMultiSelector(multiCheckbox);
        }, 100);
      }
    }

    const updateESCheckboxDependences = (taxCountryValue) =>{
      const reverseChargeSelector = document.getElementById("multiple-reverse-charge");
      const eqtaxSelector = document.getElementById("multiple-eqtax");
      if (taxCountryValue.includes("ES")){
        reverseChargeSelector.removeAttribute("disabled");
        eqtaxSelector.removeAttribute("disabled");
      } else {
        reverseChargeSelector.setAttribute("disabled", "disabled");
        reverseChargeSelector.setAttribute("data-value", "");
        reverseChargeSelector.value = '';
        reverseChargeSelector.updateItems();

        eqtaxSelector.setAttribute("disabled", "disabled");
        eqtaxSelector.setAttribute("data-value", "");
        eqtaxSelector.value = '';
        eqtaxSelector.updateItems();
      }
    }

    // DYNAMIC HANDLING FOF MULTICHECK FILTERS
    function massiveOptionPerm() {
      let perms = "{{ perms.users.is_superuserAPP }}";
      if (perms == "False") {
        $('#selectValue').empty().append('<option value="empty">Selecciona una acción múltiple</option><option value="download">Descargar facturas</option>');
      }
    }

    function checkCollapse() {
      const collapse = document.getElementById("collapse1");
      const incTable = document.getElementById("tableColapse1");
      const expTable = document.getElementById("tableColapse2");
      const resTable = document.getElementById("tableColapse3");

      const expTableHeight = getExpenseDetailCardHeight(expTable) + "px";

      incTable.style.height = expTableHeight;
      resTable.style.height = expTableHeight;
    }

    function getExpenseDetailCardHeight(element) {
      const collapseElement = element.parentElement;
      const originalDisplay = collapseElement.style.display;
      collapseElement.style.display = 'block';

      const height = element.scrollHeight;
      collapseElement.style.display = originalDisplay;

      return height;
    }

    function dropdownFilterFormHack() {
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      dropdownFiltersForm.style.opacity = '1';
      dropdownFiltersForm.style.display = 'none';
    }

  </script>
  <!-- end MAIN SCRIPT -->
  <!-- start VUE -->
  <script type="text/javascript">
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputPaisIva = ref(null);
    const inputCategoria = ref(null);
    const inputFile = ref(null);
    const inputFilename = ref(null);
    const inputExportCSV = ref(false);
    const invoiceDataJSON = ref({});
    const dj = ref({});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          debugLog("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      debugLog(dj.value);
    };

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
      debugLog("fileList: ", fileList);
    }

    // Manejar el cambio de año y habilitar/deshabilitar el mes
    const onChangeYear = () => {
      const year = document.getElementById("year");
      const period = document.getElementById("multiple-month");
      debugLog("EL AÑO ES: ", year.value);
      if (year && year.value) {
        period.removeAttribute("disabled");  // Habilitar selector de meses
      } else {
        period.setAttribute("disabled", "disabled");  // Deshabilitar selector de meses
        period.value = '';  // Limpia el valor del selector
        addFiltersBadge(period);
      }
    }

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      invoiceDataJSON,

      inputId,
      inputPaisIva,
      inputCategoria,
      inputFile,
      inputFilename,
      inputExportCSV,

      getCountryNameByCode,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";
      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export}
        },
        methods: {
          getPosNegClass(value, sign){
            const numericValue = parseFloat(value) * sign;
            if (numericValue > 0) {
              return 'text-success';
            } else if (numericValue < 0) {
              return 'text-danger fw-bold';
            } else {
              return 'text-muted';
            }
          },
        }
      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('.vue', data_export);
    createVue3('#toast', data_export);
    createVue3('#export', data_export);
  </script>
  <!-- end VUE3 -->
{% endblock javascripts %}
