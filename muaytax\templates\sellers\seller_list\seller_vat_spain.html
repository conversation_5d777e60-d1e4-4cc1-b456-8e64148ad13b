{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}
{{ country.name | title }}
{% endblock title %}
{% block stylesheets %}
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.bootstrap5.min.css' %}" type="text/css"/>
    <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css' %}" type="text/css"/>
    <link rel="stylesheet" href="{% static 'assets/datatables/fixedcolumns/5.0.1/css/fixedColumns.dataTables.css' %}" type="text/css"/>
    <!-- FontAwesome -->
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}" type="text/css" />
    <!-- DataTables Buttons -->
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/dataTables/buttons.dataTables.min-v3.0.1.css' %}" type="text/css" />
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/uicons/css/uicons-bold-rounded.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/swal/custom-swal-buttons.css' %}" />
    <style>

    .modal-size {
        max-width: 55%;
    }

    .no-wrap-with-fit-content {
        min-width: fit-content;
        text-wrap: nowrap;
    }
    .switch-small{
        transform: scale(0.7);
    }
    .fs-custom{
        font-size: 0.75rem;
    }
    .fs-custom-md{
        font-size: 0.9em;
    }
    .table{
        table-layout: fixed;
    }
    .custom-table-head {
        background-color: #f2f2f2!important;
    }
    .table.dataTable thead tr > .dtfc-fixed-start{
        background-color: #f2f2f2!important;
    }
    .dtfc-top-blocker{
        background-color: #f2f2f2!important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.3)!important;
        height: 43px!important; /* This value depends on the height of the header */
    }
    .header_name {
        width: 25%;
        word-wrap: break-word;
        white-space: normal;
    }
    .pendings,
    .login {
        width: 150px!important;
    }
    .month9, .month11, .month12 {
        width: 100px!important;
    } 
    .truncate-text{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;    
    }
    tbody tr td.pendings,
    tbody tr td.login {
        font-size: .9em;
    }
    .actions{
        text-align: center!important;
    }
    

    .list-causes-warning {
        list-style-type: circle;
    }

    .fix-borders{
        border-top-right-radius: .25rem!important;
        border-bottom-right-radius: .25rem!important;
    }

    .error-title {
        color: #d11507;
        font-weight: bold;
    }
</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <div class="page-header-title">
                    <h5 class="m-b-10">IVA {{ country.name | title }}</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href=".">IVA {{ country.name | title }}</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}
{% block content %}
    <div class="row">
        <!-- cartas con valores totales -->
        <div class="col-12">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>FACTURAS PENDIENTES</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="total_invoices_count">&nbsp</b>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS REQUERIDOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b
                                        id="required-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-file-contract "
                                        style="color: #FF0000;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS PENDIENTES</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b
                                        id="pending-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-regular fa-clock fa-sm"
                                        style="color: #7c7d7e;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS RECHAZADOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b
                                        id="disagreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-xmark"
                                        style="color: #FE8330;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS ACEPTADOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b
                                        id="agreed-model-count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-check"
                                        style="color: #ffd700;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS PRESENTADOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted"><b
                                        id="presented-model-count">&nbsp</b>&nbsp&nbsp<i class="fas fa-check-double"
                                        style="color: #02c018;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- search input -->
        <div class="col-12 mb-3">
            <div class="row">
                <div class="col-12 col-lg-6 d-flex justify-content-start filters-row">
                    <div class="col-12 col-lg-8 me-3">
                        <div class="input-group">
                            <input class="form-control fix-borders" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()">
                            <span class="mdi mdi-magnify search-icon"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- card with filters and table -->
        <div class="col-12">
            <div class="card rounded">
                <div class="card-body">
                    <div class="row">
                        <div class="d-flex flex-wrap flex-column-reverse flex-lg-row">
                            <!-- Filter Section starts -->
                            <div class="gap-3 col-sm-12 col-lg-6 d-flex flex-column flex-lg-row justify-content-center justify-content-lg-start filters-row mb-2 min-width-fit-content flex-fill">
                                <div class="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 1">
                                        <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q1">
                                        <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q1</span>
                                        <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q1</span>
                                        <span class="d-sm-block d-md-none d-xl-none">Q1</span>
                                    </label>
                                    <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 2">
                                        <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q2">
                                        <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q2</span>
                                        <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q2</span>
                                        <span class="d-sm-block d-md-none d-xl-none">Q2</span>
                                    </label>
                                    <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 3">
                                        <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q3">
                                        <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q3</span>
                                        <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q3</span>
                                        <span class="d-sm-block d-md-none d-xl-none">Q3</span>
                                    </label>
                                    <label class="btn btn-light  border align-content-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Trimestre 4">
                                        <input type="radio" name="periods" class="period-filter" autocomplete="off" value="Q4">
                                        <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Q4</span>
                                        <span class="d-none d-md-block d-xl-none no-wrap-with-fit-content">Q4</span>
                                        <span class="d-sm-block d-md-none d-xl-none">Q4</span>
                                    </label>
                                    <label class="btn btn-light  border align-content-center" title="Anual">
                                        <input type="radio" name="periods" class="period-filter" autocomplete="off" value="0A">
                                        <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Anual</span>
                                        <span class="d-none d-md-block d-xl-none">Anual</span>
                                        <span class="d-sm-block d-md-none d-xl-none">0A</span>
                                    </label>
                                </div>
                                <div class="d-flex flex-nowrap">
                                    <select role="button" class="form-select form-control btn-light border" name="year-input" id="year" onchange="onChangePeriodYear()">
                                        <option value="2022">2022 &nbsp;&nbsp;&nbsp;</option>
                                        <option value="2023">2023 &nbsp;&nbsp;&nbsp;</option>
                                        <option value="2024">2024 &nbsp;&nbsp;&nbsp;</option>
                                        <option value="2025">2025 &nbsp;&nbsp;&nbsp;</option>
                                    </select>
                                </div>
                                <div class="d-flex flex-nowrap">
                                    <select role="button" class="form-select form-control btn-light border" name="manager-input" id="manager_assigned" onchange="manager_assigned()">
                                        <option value="">Mostrar Todos &nbsp;&nbsp;&nbsp;</option>
                                        {% for  manager in managers %}
                                            <option value="{{ manager.id }}">{{ manager.name }} &nbsp;&nbsp;&nbsp;</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <!-- Filter Section ends -->
                            <!-- starts table action buttons -->
                            <div class="col-sm-12 col-lg-6 d-flex justify-content-center justify-content-lg-end filters-row mb-2 min-width-fit-content flex-basis-0">
                                <div class="table-action-button-container">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 d-flex flex-nowrap gap-1 align-items-center" onclick="openInfoModal()">
                                        <i class="fa-regular fa-circle-question fa-lg me-0"></i>
                                        &nbsp;Leyenda
                                    </button>
                                </div>
                                <div class="table-action-button-container position-relative">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 justify-content-center d-flex flex-nowrap gap-1 align-items-center" id="columnsDropdownButton">
                                        <i class="fas fa-columns fa-lg me-0"></i>
                                        &nbsp;Columnas
                                    </button>
                                    <div id="columnsDropdownArea" class="dropdown-form-columns card rounded">
                                        <div class="card-header p-3">
                                            <h6 class="mb-0"><b>Configuración de columnas</b></h6>
                                        </div>
                                        <div class="py-3">
                                            <div class="px-3">
                                                <p class="mb-1">Columnas Txt</p>
                                            </div>
                                            <div id="monthsContainer">
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month1">Enero</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month1" class="column-switch" data-column="month1">
                                                            <label for="month1" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month2">Febrero</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month2" class="column-switch" data-column="month2">
                                                            <label for="month2" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month3">Marzo</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month3" class="column-switch" data-column="month3">
                                                            <label for="month3" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month4">Abril</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month4" class="column-switch" data-column="month4">
                                                            <label for="month4" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month5">Mayo</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month5" class="column-switch" data-column="month5">
                                                            <label for="month5" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month6">Junio</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month6" class="column-switch" data-column="month6">
                                                            <label for="month6" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month7">Julio</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month7" class="column-switch" data-column="month7">
                                                            <label for="month7" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month8">Agosto</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month8" class="column-switch" data-column="month8">
                                                            <label for="month8" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month9">Septiembre</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month9" class="column-switch" data-column="month9">
                                                            <label for="month9" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month10">Octubre</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month10" class="column-switch" data-column="month10">
                                                            <label for="month10" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month11">Noviembre</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month11" class="column-switch" data-column="month11">
                                                            <label for="month11" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="month12">Diciembre</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="month12" class="column-switch" data-column="month12">
                                                            <label for="month12" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="p-3">
                                            <div class="d-grid">
                                                <button id="selectAllColumnsButton" class="btn btn-dark border-0 me-0" onclick="toggleSelectAllMonths(this)">
                                                    Seleccionar todos
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-action-button-container">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 d-flex flex-nowrap gap-1 align-items-center" onclick="confirmUpdateTable()">
                                        <i class="fi fi-br-refresh me-0"></i>
                                        &nbsp;Actualizar
                                    </button>
                                </div>
                                <div class="dropdown-area-rtl table-action-button-container">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 me-0 d-flex flex-nowrap gap-1 align-items-center"
                                        type="button"
                                        id="actionsDropdownMenuButton"
                                        data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                    <i class="fa-solid fa-ellipsis-v fa-lg me-0"></i>
                                    &nbsp;Acciones
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="actionsDropdownMenuButton">
                                    <li>
                                        <a class="dropdown-item" href="#" id="exportExcelBtn">
                                        <i class="fa-solid fa-download fa-lg me-1"></i> Descargar Excel
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="javascript:void(0)" onclick="goToCreateNotification('accounting-es-department')">
                                        <i class="fa-solid fa-bell fa-lg me-0"></i>
                                        &nbsp;Crear notificación
                                        </a>
                                    </li>
                                    </ul>
                                </div>
                            </div>
                            <!--ends table action buttons -->
                        </div>
                        <div class="table-responsive">
                            <table id="seller-vat-list-table" class="table table-hover stripe nowrap border">
                                <thead class="custom-table-head">
                                    <tr>
                                        <th>Nombre</th>
                                        <th>Email</th>
                                        <th>Pendientes</th>
                                        <th>M-184</th>
                                        <th>M-303</th>
                                        <th>M-347</th>
                                        <th>M-349</th>
                                        <th>M-369</th>
                                        <th>M-390</th>
                                        <th>Enero</th>
                                        <th>Febrero</th>
                                        <th>Marzo</th>
                                        <th>Abril</th>
                                        <th>Mayo</th>
                                        <th>Junio</th>
                                        <th>Julio</th>
                                        <th>Agosto</th>
                                        <th>Septiembre</th>
                                        <th>Octubre</th>
                                        <th>Noviembre</th>
                                        <th>Diciembre</th>
                                        <th>Último acceso</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Info -->
        <div class="modal fade " id="info-modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg modal-size" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalLabel">Información sobre los iconos del listado</h5>
                    </div>
                    <div class="modal-body">
                        <div class="row d-flex align-items-center">
                            <div class="col">
                                <ul>
                                    <li>
                                        <p><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i><b class="icon-text-size">&nbsp;Modelo requerido</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i><b class="icon-text-size">&nbsp;Modelo pendiente de confirmar</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i><b class="icon-text-size">&nbsp;Modelo rechazado por el cliente</b></p>
                                    </li>
                                </ul>
                            </div>
                            <div class="col">
                                <ul>
                                    <li>
                                        <p><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i><b class="icon-text-size">&nbsp;Modelo aceptado por el cliente</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Modelo presentado</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Modelo NO requerido</b></p>
                                    </li>
                                </ul>
                            </div>
                            <div class="col">
                                <ul>
                                    <li>
                                        <p><i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Txt subido</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fa-solid fa-circle fa-xl" style="color: #7c7d7e;"></i><b class="icon-text-size">&nbsp;Txt NO subido</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Txt NO requerido</b></p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="row d-flex align-items-center">
                            <div class="col">
                            </div>
                            <div class="col">
                            </div>
                            <div class="col">
                                <ul>
                                    <li>
                                        <p><i class="fa-solid fa-square fa-xl" style="color: #02c018;"></i><b class="icon-text-size">&nbsp;Factura de Muaytax subida</b></p>
                                    </li>
                                    <li>
                                        <p><i class="fa-solid fa-square fa-xl" style="color: #7c7d7e;"></i><b class="icon-text-size">&nbsp; NO tiene facturas de Muaytax</b></p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <br>
                        <p style="font-size:20px; color: #FF0000;"><b>Iconos de resultado:</b></p>
                        <div class="row d-flex align-items-center">
                            <div class="col">
                            <ul>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">P&nbsp;:</i></span> A pagar.</p></li>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">F&nbsp;:</i></span> Aplazamiento/Fraccionamiento.</p></li>
                            </ul>
                            </div>
                            <div class="col">
                            <ul>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">D&nbsp;:</i></span> Domiciliación.</p></li>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">Dv&nbsp;:</i></span> Devolución.</p></li>
                            </ul>
                            </div>
                            <div class="col">
                            <ul>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">C&nbsp;:</i></span> Compensar.</p></li>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">0&nbsp;:</i></span> Resultado 0.</p></li>
                                <li><p><span ><i class="fa-solid " style="color: #000000;">I&nbsp;:</i></span> Informativo.</p></li>
                            </ul>
                            </div>
                        </div>
                        <p style="font-size:20px; color: #FF0000;"><b>Códigos de errores XX:</b></p>
                        <div class="col " style="margin-left: 20px;">
                            <b>MODELO 303:</b>
                            <ul class="list-causes-warning">
                                <li> <span class="error-title">01:</span> País IVA España NO contratado (ficha país IVA) y además tiene facturas contabilizadas en este período.</li>
                                <li> <span class="error-title">02:</span> País IVA España no se encuentra registrado entre sus países IVA y además tiene facturas contabilizadas con país de tasas en España en este período.</li>
                                <li> <span class="error-title">03:</span> País IVA: España contratado (ficha país IVA), "¿Quiere activación?" &rarr; no o "Alta IAE" &rarr; Stand-by y además tiene facturas contabilizadas en este período.</li>
                                <li> <span class="error-title">04:</span> Facturas revisadas con fecha de contabilización vacía.
                                </li>
                            </ul>
                        </div>
                        <div class="col" style="margin-left: 20px;">
                            <b>MODELO 349:</b>
                            <ul class="list-causes-warning">
                                <li> <span class="error-title">05:</span> País IVA España NO contratado (ficha país IVA) y además tiene facturas contabilizadas en este período con tipo de transacción "intra comunitario".</li>
                                <li> <span class="error-title">06:</span> País IVA España no se encuentra registrado entre sus países IVA y además tiene facturas contabilizadas en este período con tipo de transacción "intra comunitario".</li>
                                <li> <span class="error-title">07:</span> País IVA España contratado (ficha país IVA), "¿Quiere activación?" &rarr; no o "Alta IAE" &rarr; Stand-by y además tiene facturas contabilizadas en este período con tipo de transacción "intra comunitario".</li>
                                <li> <span class="error-title">08:</span> Facturas revisadas con fecha de contabilización vacía con tipo de transacción "intra comunitario".</li>
                            </ul>
                        </div>
                        <div class="col " style="margin-left: 20px;">
                            <b>MODELO 369:</b>
                            <ul class="list-causes-warning">
                                <li> <span class="error-title">09:</span> OSS &rarr; NO/Desconocido y tiene facturas de tipo OSS</li>
                                <li> <span class="error-title">10:</span> País IVA España no se encuentra registrado entre sus países IVA y además tiene facturas contabilizadas con país de tasas en España en este período.</li>
                                <li> <span class="error-title">11:</span> País IVA: España contratado (ficha país IVA), "¿Quiere activación?" &rarr; no o "Alta IAE" &rarr; Stand-by y además tiene facturas contabilizadas en este período.</li>
                                <li> <span class="error-title">12:</span> Facturas revisadas con fecha de contabilización vacía.</li>
                                <li> <span class="error-title">14:</span> País IVA España NO contratado (ficha país IVA) y además tiene facturas contabilizadas en este período.</li>
                                <li> <span class="error-title">15:</span> OSS Contratada y País IVA España NO contratado/Existente.</li>
                            </ul>
                        </div>
        
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">Cerrar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block javascripts %}
    <!-- jQuery (requerido por DataTables) -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/jquery/jquery-3.7.0.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="{{ STATIC_URL }}assets/js/plugins/sweetalert2.all.min.js"></script>
    <!-- DataTables 2.0.7 -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables-v2.0.7.js"></script>
    <!-- Plugins de DataTables 2.0.7-->
    <script src="{{ STATIC_URL }}assets/datatables/fixedcolumns/5.0.1/js/dataTables.fixedColumns.js"></script>
    <script src="{{ STATIC_URL }}assets/datatables/fixedcolumns/5.0.1/js/fixedColumns.dataTables.js"></script>
    <!-- Buttons 3.0.1 JS -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/buttons/dataTables.buttons.min.-v3.0.1.js"></script>
    <!-- Buttons HTML5 3.0.1 JS -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/buttons/buttons.html5.min-v3.0.1.js"></script>
    <!-- JSZip (para exportación a Excel) -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/cloudflare/jszip/jszip.min-v3.10.1.js"></script>

    <!-- start DEBUG -->
    <script type="text/javascript">
        const debug = {{ debug|yesno:"true,false" }};
        // Función para debug (imprime en consola solo si debug está habilitado)
        function debugLog(...args) {
            if (debug) {
                console.log(...args);
            }
        }
        debugLog("Debug mode is enabled")
    </script>

    <script>
        let table = null;
        let periodTxt = '{{period}}';
        let yearTxt = '{{year}}'

        $(document).ready(function () {
            const $periodFilter = $('.period-filter');

            $periodFilter.each(function() {
                const $this = $(this);
                if ($this.val() === periodTxt) {
                    $this.prop('checked', true)
                        .parent().addClass('active');
                }
            });
            document.getElementById('year').value = '{{year}}';

            $periodFilter.on('change', function() {
                onChangePeriodYear();
            });

            createDT();
            
            $('.search-icon').click(function(){
                $(this).closest('.input-group').find('input[type="search"]').focus();
            });
        });

        const createDT = async () => {
            table = $('#seller-vat-list-table').DataTable({
                buttons: [
                    {
                        extend: 'excelHtml5',
                        text: 'Exportar a Excel',
                        exportOptions: {
                            columns: function (idx, data, node) {
                                // Exportar todas las columnas excepto aquellas con clase .actions,
                                // pero incluir explícitamente la columna de email
                                const visibleColumns = [1];  // Índice de la columna 'email' a exportar
                                return $(node).hasClass('actions') ? false : (visibleColumns.includes(idx) || table.column(idx).visible());
                            },
                            rows: null,  // Exportar todas las filas
                            format: {
                                body: function (data, row, column, node) {
                                    debugLog(
                                        "=== Exportando fila ===\n" +
                                        `Datos: ${JSON.stringify(data)}\n` +
                                        `Índice de fila (Real en DataTables): ${row}\n` +           
                                        `Índice de columna (Real en DataTables): ${column}\n`
                                    );
                                    debugLog("\n ======= START =======\n");
                        
                                    let visibleIndexes = table.rows({ search: 'applied' }).indexes().toArray();
                                    debugLog("Index - filas visibles:", visibleIndexes);
                                    // let rowDataSeg = table.row(visibleIndexes[row]).data();
                                    let rowData = table.row(row).data();

                                    if (!rowData) {
                                        console.error(`No se encontró la fila ${visibleIndexes[row]} en la Tabla: `);
                                        return "Error de datos";
                                    }
                        
                                    debugLog("Datos reales de la fila:", rowData);
                        
                                    let columnData = table.settings()[0].aoColumns[column];
                                    if (!columnData) {
                                        console.warn(`ERROR: Índice de columna ${column} fuera de rango.`);
                                        return "Error de columna";
                                    }
                        
                                    debugLog(`Procesando columna ${columnData.data} ...`);
                        
                                    // Procesar columna `user_name`
                                    if (columnData.data === "user_name") {
                                        debugLog("Procesando columna user_name...");
                                        let name = rowData.user_name || "Desconocido";
                                        return `${name}`.trim();
                                    }

                                    // Procesar columna `email`
                                    if (columnData.data === "email") {
                                        debugLog("Procesando columna email...");
                                        let email = rowData.email || "";
                                        return `${email}`.trim();
                                    }
                        
                                    // Procesar columna `num_pending_invoices`
                                    if (columnData.data === "num_pending_invoices") {
                                        debugLog("Procesando columna num_pending_invoices...");
                        
                                        let invoices = rowData.num_pending_invoices ?? "Sin datos";
                                        let percentage = rowData.percentage_pending_invoices ?? "Sin datos";
                        
                                        percentage = (percentage !== "Sin datos") ? `(${percentage}%)` : "";
                        
                                        debugLog(`num_pending_invoices: ${invoices}, percentage_pending_invoices: ${percentage}`);
                        
                                        return `${invoices} ${percentage}`.trim();
                                    }
                        
                                    // Diccionario para mapear valores de modelos y meses
                                    const statusMapping = {
                                        // Estados del modelo
                                        "presented": "Presentado",
                                        "pending": "Pendiente de confirmar",
                                        "disagreed": "Rechazado por el cliente",
                                        "agreed": "Aceptado por el cliente",
                                        "not-required": "NO requerido",
                                        "required": "Requerido",
                                        "not-started": "No ha iniciado",
                                        "not-processed": "Formulario Incompleto",
                                        "303-required": "Requerido por el modelo 303",
                            
                                        // Códigos de errores o Advertencias
                                        "warning": "Información faltante",
                                        "warning09": "OSS",
                                        "warning13": "Nóminas sin contabilizar",
                                        "warning17": "Nóminas Faltantes",
                                        "warning18": "Nóminas Sobrantes",
                                        "warning19": "Alquileres Faltantes",
                                        "warning20": "Alquileres Sobrantes",
                                        "warning21": "Cuotas Seg.Social Faltantes",
                                        "warning22": "Cuotas Seg.Social Sobrantes",
                            
                                        // Resultados del modelo
                                        "P": "A pagar",
                                        "F": "Aplazamiento/Fraccionamiento",
                                        "D": "Domiciliación",
                                        "Dv": "Devolución",
                                        "C": "Compensar",
                                        "0": "Resultado 0",
                                        "I": "Informativo",
                                        
                                        // Estados de facturas y TXT
                                        "fa-ban": "Txt NO requerido",
                                        "fa-circle-green": "Txt subido",
                                        "fa-circle-grey": "Txt NO subido",
                                        "fa-square-green": "Factura de Muaytax subida",
                                        "fa-square-grey": "NO tiene facturas de Muaytax"
                                    };
                        
                                    // Función para mapear modelos
                                    function mapModelValuesForExcel(rowData, modelNumber) {
                                        let stringJsonResult = rowData.model_json_result;
                                        let letterValue = getLetter(stringJsonResult, `ES-${modelNumber}`);
                                        let modelKey = `model_${modelNumber}`;
                                        let modelValue = rowData[modelKey];
                        
                                        let mappedModelValue = statusMapping[modelValue] || "Estado desconocido";
                                        let mappedLetterValue = statusMapping[letterValue] || `Valor desconocido (${letterValue})`;
                        
                                        return letterValue !== null ? `${mappedModelValue} / ${mappedLetterValue}` : mappedModelValue;
                                    }
                        
                                    // Función para mapear meses
                                    function mapMonthValuesForExcel(rowData, monthNumber) {
                                        debugLog(`Depuración: Mapeando month${monthNumber} - Datos de la fila:`, rowData);
                        
                                        let monthKey = `month${monthNumber}`;
                                        let txtStatus = "Txt desconocido";
                        
                                        if (rowData.status_amazon_txt === "no_require") {
                                        txtStatus = statusMapping["fa-ban"];
                                        } else if (rowData[monthKey] === true) {
                                        txtStatus = statusMapping["fa-circle-green"];
                                        } else if (rowData[monthKey] === false) {
                                        txtStatus = statusMapping["fa-circle-grey"];
                                        }
                        
                                        let invoiceStatus = "Factura desconocida";
                                        let invoiceData = null;
                        
                                        if (rowData.monthly_muaytax_json_invoices) {
                                        try {
                                            const parsedInvoices = JSON.parse(rowData.monthly_muaytax_json_invoices);
                                            invoiceData = parsedInvoices[monthNumber];
                                        } catch (error) {
                                            console.error("ERROR al parsear monthly_muaytax_json_invoices:", error);
                                        }
                                        }
                        
                                        if (invoiceData === true) {
                                        invoiceStatus = statusMapping["fa-square-green"];
                                        } else if (invoiceData === false) {
                                        invoiceStatus = statusMapping["fa-square-grey"];
                                        }
                        
                                        return `${txtStatus} / ${invoiceStatus}`;
                                    }
                        
                                    // Mapeo para model_X
                                    if (typeof columnData.data === "string" && columnData.data.startsWith("model_")) {
                                        return mapModelValuesForExcel(rowData, columnData.data.split("_")[1]);
                                    }
                        
                                    // Mapeo para month_X
                                    if (typeof columnData.data === "string" && columnData.data.startsWith("month")) {
                                        return mapMonthValuesForExcel(rowData, columnData.data.replace("month", ""));
                                    }
                        
                                    // Procesar columna `last_login`
                                    if (columnData.data === "last_login") {
                                        debugLog("Procesando columna last_login...");
                        
                                        let rawDate = rowData.last_login;
                                        if (!rawDate) return "Sin datos"; // Si no hay fecha, retorna "Sin datos"
                        
                                        let dateObj = new Date(rawDate.replace(" ", "T"));
                        
                                        if (isNaN(dateObj.getTime())) {
                                        debugLog("Fecha inválida:", rawDate);
                                        return "Sin datos";
                                        }
                        
                                        let options = {
                                        day: "2-digit",
                                        month: "short",
                                        year: "numeric",
                                        hour: "2-digit",
                                        minute: "2-digit",
                                        hour12: false
                                        };
                        
                                        let formattedDate = dateObj.toLocaleDateString("es-ES", options).replace(",", " -");
                        
                                        debugLog(`Fecha transformada: ${rawDate} → ${formattedDate}`);
                                        return formattedDate;
                                    }
                        
                                    // Texto por defecto
                                    const plainText = $('<div>').html(data).text().trim() || "Sin datos";
                                    debugLog("Texto plano por defecto:", plainText);
                                    return plainText;
                                }
                            }
                        },
                        customize: function (xlsx) {
                            debugLog("=== Depuración: Iniciando actualización del formato en Excel ===");
                        
                            let sheet = xlsx.xl.worksheets['sheet1.xml'];
                            let stylesXml = xlsx.xl['styles.xml'];
                        
                            debugLog("Depuración: Contenido inicial del XML de la hoja de cálculo:");
                        
                            // Modificar el título en la celda A1
                            debugLog("Actualizando el título...");
                            let titleCell = $('row:first c[r="A1"] is t', sheet);
                            if (titleCell.length) {
                                titleCell.text("Tabla listado Gestoría España");
                            } else {
                                console.error("No se encontró la celda A1 para modificar el título.");
                            }
                        
                            debugLog("Aplicando estilos a la tabla...");
                        
                            // Verificar si `cellXfs` existe en `styles.xml`
                            let cellXfs = $('cellXfs', stylesXml);
                            if (cellXfs.length === 0) {
                                console.error("No se encontró la sección cellXfs en styles.xml.");
                                return;
                            }
                        
                            let fills = $('fills', stylesXml);
                            let borders = $('borders', stylesXml);
                            let newStyleIndex = $('xf', cellXfs).length;
                            let boldCenterIndex = newStyleIndex;
                            let centerIndex = newStyleIndex + 1;
                            let firstColumnStyleIndex = newStyleIndex + 2;
                            let secondRowStyleIndex = newStyleIndex + 3;
                        
                            debugLog(`Agregando nuevos estilos...`);
                        
                            // Agregar fondo verde claro para la primera columna (excepto A1 y A2)
                            let greenFillIndex = $('fill', fills).length;
                            fills.append(`
                                <fill>
                                    <patternFill patternType="solid">
                                        <fgColor rgb="C6EFCE"/> <!-- Verde claro -->
                                        <bgColor indexed="64"/>
                                    </patternFill>
                                </fill>
                            `);
                        
                            // Agregar fondo gris para la segunda fila
                            let grayFillIndex = $('fill', fills).length;
                            fills.append(`
                                <fill>
                                    <patternFill patternType="solid">
                                        <fgColor rgb="E0E0E0"/> <!-- Gris claro -->
                                        <bgColor indexed="64"/>
                                    </patternFill>
                                </fill>
                            `);
                        
                            // Agregar borde a la lista de estilos de bordes
                            let borderIndex = $('border', borders).length;
                            borders.append(`
                                <border>
                                    <left style="thin"><color auto="1"/></left>
                                    <right style="thin"><color auto="1"/></right>
                                    <top style="thin"><color auto="1"/></top>
                                    <bottom style="thin"><color auto="1"/></bottom>
                                </border>
                            `);
                        
                            // Estilo de negrita y centrado horizontal y vertical (para el título y primera columna)
                            cellXfs.append(`
                                <xf numFmtId="0" fontId="2" fillId="0" borderId="${borderIndex}" applyFont="1" applyAlignment="1" applyBorder="1">
                                    <alignment horizontal="center" vertical="center"/>
                                </xf>
                            `);
                        
                            // Estilo de solo centrado horizontal y vertical (para el resto de la tabla)
                            cellXfs.append(`
                                <xf numFmtId="0" fontId="0" fillId="0" borderId="${borderIndex}" applyAlignment="1" applyBorder="1">
                                    <alignment horizontal="center" vertical="center"/>
                                </xf>
                            `);
                        
                            // Estilo de la primera columna con fondo verde claro y bordes (excepto las dos primeras celdas)
                            cellXfs.append(`
                                <xf numFmtId="0" fontId="2" fillId="${greenFillIndex}" borderId="${borderIndex}" applyFont="1" applyFill="1" applyAlignment="1" applyBorder="1">
                                    <alignment horizontal="center" vertical="center" wrapText="1"/>
                                </xf>
                            `);
                        
                            // Estilo especial para la segunda fila en gris con bordes
                            cellXfs.append(`
                                <xf numFmtId="0" fontId="2" fillId="${grayFillIndex}" borderId="${borderIndex}" applyFont="1" applyFill="1" applyAlignment="1" applyBorder="1">
                                    <alignment horizontal="center" vertical="center"/>
                                </xf>
                            `);
                        
                            debugLog("Aplicando estilos a las celdas...");
                        
                            // Aplicar negrita y centrado al título
                            $('row:first c[r="A1"]', sheet).attr('s', boldCenterIndex);
                        
                            // Aplicar centrado a toda la tabla
                            $('row:not(:first) c', sheet).attr('s', centerIndex);
                        
                            // Aplicar fondo verde claro a la primera columna (excepto las dos primeras celdas)
                            $('row:not(:lt(2)) c[r^="A"]', sheet).attr('s', firstColumnStyleIndex);
                        
                            // Aplicar fondo gris a la segunda fila
                            $('row:eq(1) c', sheet).attr('s', secondRowStyleIndex);
                        
                            debugLog("Formato de la tabla actualizado.");
                        
                            // Ajustar altura de fila (excepto para las dos primeras filas)
                            debugLog("Ajustando altura de filas...");
                            $('row:not(:lt(2))', sheet).attr('ht', '30').attr('customHeight', '1');
                        
                            // Asegurar que las celdas de la primera columna tengan `wrapText="1"`
                            $('row:not(:lt(2)) c[r^="A"]', sheet).each(function () {
                                $(this).append('<alignment wrapText="1"/>');
                            });
                        
                            debugLog("Altura de filas ajustada y ajuste de texto aplicado.");
                        
                            debugLog("Depuración: XML modificado de la hoja de cálculo:");
                            debugLog(new XMLSerializer().serializeToString(sheet));
                        
                            debugLog("=== Depuración finalizada ===");
                        }
                    }
                ],
                "serverSide": false,
                "fixedColumns": true,
                "scrollX": true,
                "scrollY": "calc(100vh - 250px)",
                "scrollCollapse": true,
                "ajax": {
                    "dataSrc": "data",
                    "url": "{% url 'app_lists:vat_management_dt_list' country.iso_code %}",
                    "data": await function (d) {
                        ajaxData(d);
                    }
                },
                "language": {
                    "url": "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
                    "lengthMenu": "_MENU_",
                    "zeroRecords": "No se han encontrado vendedores.",
                    "info": "_START_ a _END_ de un total de _TOTAL_ registros",
                    "search": "Buscar:",
                    "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                    "infoFiltered": ""
                },
                "columns": [
                    {   // Columna Nombre
                        "data": "user_name",
                        "className": "header_name",
                        "render": function (data, type, row) {
                            let html = '';
                            html += '<td class="align-middle">';
                            html += '<div class="">';
                            html += '<h6 class="m-b-0 fs-custom-md truncate-text"><b>';

                            let name = row.seller_name;
                            if (typeof name === 'string') {
                            const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
                            const words = row.seller_name.split(' ').map(function (word) {
                                const lowerWord = word.toLowerCase();
                                if (lowerCaseSuffixes.includes(lowerWord)) {
                                return word.toUpperCase();
                                } else {
                                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                                }
                            });
                            html += words.join(' ');
                            }
                            html += '</b>';
                            if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                            html += ' - ' + row.user_name.split(' ').map(function (word) {
                                return word.charAt(0).toUpperCase() + word.slice(1);
                            }).join(' ');
                            }

                            html += '</h6>';
                            html += '<p class="m-b-0 fs-custom-md">' + row.email.toLowerCase() + '</p>';
                            html += '</div>';
                            html += '</td>';

                            return html;
                        }
                    },
                    {   // Columna Email
                        "data": "email",
                        "className": "email-column",
                        "visible": false,  // No visible en la tabla
                        "exportable": true, // Exportable a Excel
                        "render": function (data, type, row) {
                            return data || row.email;
                        }
                    },
                    {   // Columna Facturas pendientes
                        "data": "num_pending_invoices",
                        "className": "pendings",
                        "render": function (data, type, row) {
                            if (data && (type === 'display' || type === 'filter')) {
                            let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                                html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                                html += '<div class="progress" style="height: 15px;">';
                                html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                                html += '</div>';
                                html += '</td>';
                                return html;
                            }
                            return data;
                        }
                    },
                    {   // Columna Modelo184
                        "data": "model_184",
                        "visible": false,
                        "className": "model model-year text-center",
                        "render": function (data, type, row) {
                            let html = ' ';
                            let m184 = row.model_184;
                            let letter184 = getLetter(row.model_json_result, 'ES-184');
                            if (m184 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href="/' + row.shortname + '/model/184/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                                if (letter184) {
                                    html += `<a href="/${row.shortname}/model/184/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter184}</i></a>`;
                                }
                            } else if (m184 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            } else if (m184 == 'disagreed') {
                            html += '<span class="d-none">' + 2 + '</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                            } else if (m184 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                                if (letter184) {
                                    html += `<a href="/${row.shortname}/model/184/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter184}</i></a>`;
                                }
                            } else if (m184 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            } else if (m184 == 'required' || m184 == 'processed') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            } else if (m184 == 'not-processed') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Formulario Incompleto/Inacabado " href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-pencil fa-xl" style="color: #7c7d7e;"></i></a>';                
                            } else if (m184 == 'not-started') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No ha iniciado" href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-file-contract fa-xl" style="color: #7c7d7e;"></i></a>';
                            } else if (m184 == 'warning13') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-xl" style="color: #FF0000;">13</i></a>';
                            } else if (m184 == 'warning17') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-xl" style="color: #FF0000;">17</i></a>';
                            } else if (m184 == 'warning18') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-xl" style="color: #FF0000;">18</i></a>';
                            } else if (m184 == 'warning00') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-xl" style="color: #FF0000;">00</i></a>';
                            }
                            return html;
                        }
                    },
                    {   // Columna Modelo303
                        "data": "model_303",
                        "visible": false,
                        "className": "model model-quarter model-year text-center",
                        "render": function (data, type, row) {
                            if (periodTxt === '0A') {
                                periodTxt = 'Q4';
                            }
                            let html = ' ';
                            let m303 = row.model_303;
                            let letter303 = getLetter(row.model_json_result, 'ES-303');
                            if (m303 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href="/' + row.shortname + '/model/303/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                                if (letter303) {
                                    html += `<a href="/${row.shortname}/model/303/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter303}</i></a>`;
                                }
                            }
                            else if (m303 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            }
                            else if (m303 == 'disagreed') {
                                html += '<span class="d-none">' + 2 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                            }
                            else if (m303 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                                if (letter303) {
                                    html += `<a href="/${row.shortname}/model/303/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter303}</i></a>`;
                                }
                            }
                            else if (m303 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            }
                            else if (m303 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            }
                            else if(m303 == 'warning01'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">01</i>';
                            }
                            else if(m303 == 'warning02'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">02</i>';
                            }
                            else if(m303 == 'warning03'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">03</i>';
                            }
                            else if(m303 == 'warning04'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">04</i>';
                            }
                            else if(m303 == 'warningA1'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">A1</i>';
                            } else if (m303 == 'warning00') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/303/"><i class="fa-solid fa-xl" style="color: #FF0000;">00</i></a>';
                            }
                            return html;
                        }
                    },
                    {   // Columna Modelo347
                        "data": "model_347",
                        "visible": false,
                        "className": "model model-year text-center",
                        "render": function (data, type, row) {
                            let html = ' ';
                            let m347 = row.model_347;
                            let letter347 = getLetter(row.model_json_result, 'ES-347');
                            if (m347 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href="/' + row.shortname + '/model/347/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                                if (letter347) {
                                    html += `<a href="/${row.shortname}/model/347/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter347}</i></a>`;
                                }
                            } else if (m347 == "pending") {                        
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';                        
                            } else if (m347 == 'disagreed') {
                                html += '<span class="d-none">' + 2 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                            } else if (m347 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                                if (letter347) {
                                    html += `<a href="/${row.shortname}/model/347/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter347}</i></a>`;
                                }
                            } else if (m347 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            } else if (m347 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            } else if (m347 == 'warning13') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-xl" style="color: #FF0000;">13</i></a>';
                            } else if (m347 == 'warning17') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-xl" style="color: #FF0000;">17</i></a>';
                            } else if (m347 == 'warning18') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-xl" style="color: #FF0000;">18</i></a>';
                            } else if (m347 == 'warning00') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/347/"><i class="fa-solid fa-xl" style="color: #FF0000;">00</i></a>';
                            }
                            return html;
                        }
                    },
                    {   // Columna Modelo349
                        "data": "model_349",
                        "visible": false,
                        "className": "model model-quarter text-center",
                        "render": function (data, type, row) {
                            let html = ' ';
                            let m349 = row.model_349;
                            let letter349 = getLetter(row.model_json_result, 'ES-349');
                            if (m349 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href="/' + row.shortname + '/model/349/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                                if (letter349) {
                                    html += `<a href="/${row.shortname}/model/349/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter349}</i></a>`;
                                }
                            }
                            else if (m349 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            }
                            else if (m349 == 'disagreed') {
                                html += '<span class="d-none">' + 2 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                            }
                            else if (m349 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                                if (letter349) {
                                    html += `<a href="/${row.shortname}/model/349/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter349}</i></a>`;
                                }
                                }
                            else if (m349 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            }
                            else if (m349 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            }
                            else if(m349 == 'warning05'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">05</i>';
                            }
                            else if(m349 == 'warning06'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">06</i>';
                            }
                            else if(m349 == 'warning07'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">07</i>';
                            }
                            else if(m349 == 'warning08'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">08</i>';
                            }
                            else if(m349 == 'warningB1'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">B1</i>';
                            } else if(m349 == 'warning00'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/349/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">00</i>';
                            }
                            return html;
                        }
                    },
                    {   // Columna Modelo369
                        "data": "model_369",
                        "visible": false,
                        "className": "model model-quarter text-center",
                        "render": function (data, type, row) {
                            let html = ' ';
                            let m369 = row.model_369;
                            let letter369 = getLetter(row.model_json_result, 'ES-369');
                            if (m369 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href="/' + row.shortname + '/model/369/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                                if (letter369) {
                                    html += `<a href="/${row.shortname}/model/369/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter369}</i></a>`;
                                }
                            }
                            else if (m369 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            }
                            else if (m369 == 'disagreed') {
                                html += '<span class="d-none">' + 2 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                            }
                            else if (m369 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                                if (letter369) {
                                    html += `<a href="/${row.shortname}/model/369/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter369}</i></a>`;
                                }
                            }
                            else if (m369 == 'not-required') {
                                html += '<span class="d-none">' + 6 + '</span>';
                                html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            }
                            else if (m369 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            }
                            else if(m369 == 'warning09'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">09</i>';
                            }
                            else if(m369 == 'warning10'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">10</i>';
                            }
                            else if(m369 == 'warning11'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">11</i>';
                            }
                            else if(m369 == 'warning12'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">12</i>';
                            }
                            else if(m369 == 'warning13'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">13</i>';
                            }
                            else if(m369 == 'warning14'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">14</i>';
                            }
                            else if(m369 == 'warning15'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">15</i>';
                            } else if(m369 == 'warning00'){
                                html += '<span class="d-none">'+ 1 +'</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/369/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fa-solid fa-xl " style="color: #FF0000;">00</i>';
                            }
                            return html;
                        }
                    },
                    {   // Columna Modelo390
                        "data": "model_390",
                        "visible": false,
                        "className": "model model-year text-center",
                        "render": function (data, type, row) {
                            let html = ' ';
                            let m390 = row.model_390;
                            let letter390 = getLetter(row.model_json_result, 'ES-390');
                            if (m390 == "presented") {
                                html += '<span class="d-none">' + 5 + '</span>';
                                html += '<a href="/' + row.shortname + '/model/390/pdf/?period=' + periodTxt + '&year=' + yearTxt + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                                if (letter390) {
                                    html += `<a href="/${row.shortname}/model/390/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter390}</i></a>`;
                                }
                            } else if (m390 == "pending") {
                                html += '<span class="d-none">' + 4 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                            } else if (m390 == 'disagreed') {
                            html += '<span class="d-none">' + 2 + '</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FE8330;"></i></a>';
                            } else if (m390 == 'agreed') {
                                html += '<span class="d-none">' + 3 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                                if (letter390) {
                                    html += `<a href="/${row.shortname}/model/390/pdf/?period=${periodTxt}&year=${yearTxt}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter390}</i></a>`;
                                }
                            } else if (m390 == 'not-required') {
                            html += '<span class="d-none">' + 6 + '</span>';
                            html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                            } else if (m390 == 'required') {
                                html += '<span class="d-none">' + 0 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                            } else if (m390 == '303-required') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<i class="fi fi-br-file-exclamation fa-xl" style="color: #FF0000;" onclick="event.stopPropagation(); showWarning390()"></i>';
                            } else if (m390 == 'warning13') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-xl" style="color: #FF0000;">13</i></a>';
                            } else if (m390 == 'warning17') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-xl" style="color: #FF0000;">17</i></a>';
                            } else if (m390 == 'warning18') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-xl" style="color: #FF0000;">18</i></a>';
                            } else if (m390 == 'warning00') {
                                html += '<span class="d-none">' + 1 + '</span>';
                                html += '<a href="/sellers/' + row.shortname + '/model/390/"><i class="fa-solid fa-xl" style="color: #FF0000;">00</i></a>';
                            }
                            return html;
                        }
                    },
                    {   // Columna mes de Enero
                        "data": "month1",
                        "visible": false,
                        "className": "txt Q1 month1 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month1, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '1');
                        }
                    },
                    {   // Columna mes de Febrero
                        "data": "month2",
                        "visible": false,
                        "className": "txt Q1 month2 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month2, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '2');
                        }
                    },
                    {   // Columna mes de Marzo
                        "data": "month3",
                        "visible": false,
                        "className": "txt Q1 month3 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month3, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '3');
                        }
                    },
                    {   // Columna mes de Abril
                        "data": "month4",
                        "visible": false,
                        "className": "txt Q2 month4 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month4, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '4');
                        }
                    },
                    {   // Columna mes de Mayo
                        "data": "month5",
                        "visible": false,
                        "className": "txt Q2 month5 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month5, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '5');
                        }
                    },
                    {   // Columna mes de Junio
                        "data": "month6",
                        "visible": false,
                        "className": "txt Q2 month6 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month6, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '6');
                        }
                    },
                    {   // Columna mes de Julio
                        "data": "month7",
                        "visible": false,
                        "className": "txt Q3 month7 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month7, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '7');
                        }
                    },
                    {   // Columna mes de Agosto
                        "data": "month8",
                        "visible": false,
                        "className": "txt Q3 month8 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month8, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '8');
                        }
                    },
                    {   // Columna mes de Septiembre
                        "data": "month9",
                        "visible": false,
                        "className": "txt Q3 month9 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month9, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '9');
                        }
                    },
                    {   // Columna mes de Octubre
                        "data": "month10",
                        "visible": false,
                        "className": "txt Q4 month10 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month10, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '10');
                        }
                    },
                    {   // Columna mes de Noviembre
                        "data": "month11",
                        "visible": false,
                        "className": "txt Q4 month11 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month11, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '11');
                        }
                    },
                    {   // Columna mes de Diciembre
                        "data": "month12",
                        "visible": false,
                        "className": "txt Q4 month12 text-center",
                        "render": function (data, type, row) {
                            return renderAmazonStatus(row.status_amazon_txt, row.month12, row) + '&nbsp;&nbsp;&nbsp;' + getMuayInv(row.monthly_muaytax_json_invoices, '12');
                        }
                    },
                    {   // Columna de Ultimo acceso
                        "data": "last_login",
                        "className": "login truncate-text",
                        "render": function (data, type, row) {
                            if (data && (type === 'display' || type === 'filter')) {
                            const date = new Date(data);

                            const day = date.getDate().toString().padStart(2, '0');
                            const month = date.toLocaleString('default', { month: 'short' });
                            const year = date.getFullYear();
                            const hours = date.getHours().toString().padStart(2, '0');
                            const minutes = date.getMinutes().toString().padStart(2, '0');

                            const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

                            return formattedDate;
                            }
                            return data; // For other types, like 'sort'
                        }
                    },
                    {  // Columna Gestor asignado
                        "data": "manager_assigned",
                        'visible': false,
                    },
                    {   // Columna de acciones
                        "data": null,
                        "className": "actions",
                        "orderable": false,
                        "render": function (data, type, row) {
                            let html = '<td class="align-middle text-center">';
                            html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success mb-0 border-0" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                            html += '<i class="feather icon-edit"></i>';
                            html += '</a>';
                            html += '</td>';
                            html = '<div>' + html + '</div>';
                            return html;
                        },
                    }
                ],
                "paging": true,
                "searching": true,
                "ordering": true,
                "lengthChange": false,
                "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
                "layout": {
                    topEnd: null,
                },
                "createdRow": function (row, data, dataIndex) {
                    const shortname = data.shortname;
                    const link = '/sellers/' + shortname + '/';
                    $(row).attr('style', 'cursor: pointer; vertical-align: middle;');
                    $(row).attr('onclick', "window.location.href = '" + link + "';");
                },
                "initComplete": function (settings, json) {
                    table.settings()[0].nTBody.style.width = "100%";
                    table.settings()[0].nTable.style.width = "100%";
                    table.settings()[0].nTHead.style.width = "100%";
                    document.getElementById("pending-model-count").textContent = json.pending_model_count;
                    document.getElementById("total_invoices_count").textContent = json.total_invoices;
                    document.getElementById("required-model-count").textContent = json.required_model_count;
                    document.getElementById("disagreed-model-count").textContent = json.disagreed_model_count;
                    document.getElementById("agreed-model-count").textContent = json.agreed_model_count;
                    document.getElementById("presented-model-count").textContent = json.presented_model_count;
                    const period = "{{period}}";
                    if (period == '0A') {
                        $columns = table.columns(`.model-year`);
                        $columns.visible(true);
                    }
                    else {
                        $columns = table.columns(`.model-quarter`);
                        $txtColumns = table.columns(`.txt.${period}`);
                        $columns.visible(true);
                        $txtColumns.visible(true);
                    }

                    $('.column-switch').each(function () {
                        const columnClass = $(this).data('column');
                        const column = table.column(`.${columnClass}`);
                        this.checked = column.visible();
                        $(this).trigger('change');
                    });

                    const columnsDropdownButton = document.getElementById('columnsDropdownButton');
                    columnsDropdownButton.addEventListener('click', function (event) {
                        // event.stopPropagation();
                        const columnsDropdown = document.getElementById('columnsDropdownArea');
                        columnsDropdown.classList.toggle('active');
                    });

                    document.addEventListener('click', function (event) {
                        const columnsDropdown = document.getElementById('columnsDropdownArea');
                        if (!columnsDropdown.contains(event.target) && event.target !== columnsDropdownButton) {
                            columnsDropdown.classList.remove('active');
                        }
                    });

                    const truncatedCells = document.querySelectorAll('.truncate-text');
                        truncatedCells.forEach(cell => {
                        cell.setAttribute('title', cell.textContent);
                    });

                },
                "drawCallback": function (settings) {
                    table.settings()[0].nTable.style.width = "100%";
                    const period = "{{period}}";
                    if (period == '0A') {
                        $columns = table.columns(`.model-year`);
                        $columns.visible(true);
                    } else {
                        $columns = table.columns(`.model-quarter`);
                        $columns.visible(true);
                    }
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            $('.column-switch').change(function () {
                const columnClass = $(this).data('column');
                const column = table.column(`.${columnClass}`);
                if (this.checked) {
                    column.visible(true);
                } else {
                    column.visible(false);
                }

                const allChecked = checkAllMonths();
                const selectAllButton = document.getElementById('selectAllColumnsButton');
                if (allChecked) {
                    selectAllButton.textContent = 'Deseleccionar todos';
                } else {
                    selectAllButton.textContent = 'Seleccionar todos';
                }
            });

            // Vincular el enlace del dropdown con el botón "excelHtml5" de DataTables
            const exportExcelBtn = document.getElementById('exportExcelBtn');
                if (exportExcelBtn) {
                    exportExcelBtn.addEventListener('click', function () {
                    // Disparamos el botón “excelHtml5”
                    table.button('.buttons-excel').trigger();
                });
            }

            const getLetter = (row_json, model)=>{
                let modelJsonResult = row_json ? JSON.parse(row_json) : {};
                let value = modelJsonResult[model]||null;
                return value;
            }

            const getMuayInv = (row_json, month)=>{
                let html= '';
                let modelJsonResult = row_json ? JSON.parse(row_json) : {};
                let value = modelJsonResult[month]

                if(value){
                    html += '<a href="#" ><i class="fa-solid fa-square fa-xl" style="color: #02c018;"></i></a>';
                }else{
                    html += '<a href="#"><i class="fa-solid fa-square fa-xl" style="color: #7c7d7e;"></i></a>';
                }
                return html;
            }

        }

        const ajaxData = (d) => {
            let tParams = "";
            let year = document.getElementById("year").value;
            let  period = $('input[name="periods"]:checked').val();
            if (year) {
                d.year = year
                tParams += "&year=" + year;
            }
            if (period) {
                d.period = period
                tParams += "&period=" + period;
            }
            // getTotals(tParams);
            return d
        }

        function renderAmazonStatus(status, monthValue, row) {
            let html = '';
            let hrefLink = '';
        
            if (status === "no_require") {
            hrefLink = '/sellers/' + row.shortname + '/';
            html += '<span class="d-none">' + 3 + '</span>';
            html += '<a href="' + hrefLink + '"><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i></a>';
            } else {
            if (monthValue === true) {
                hrefLink = '/sellers/' + row.shortname + '/AmazonTxtEur/';
                html += '<span class="d-none">' + 2 + '</span>';
                html += '<a href="' + hrefLink + '"><i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i></a>';
            } else {
                hrefLink = '/sellers/' + row.shortname + '/AmazonTxtEur/';
                html += '<span class="d-none">' + 1 + '</span>';
                html += '<a href="' + hrefLink + '"><i class="fa-solid fa-circle fa-xl" style="color: #7c7d7e;"></i></a>';
            }
            }
            return html;
        }

        function search() {
            const tipo = $('#search').val();
            table.search(tipo).draw();
        }

        const onChangePeriodYear = () => {
            const period = $('input[name="periods"]:checked').val();
            const year = document.getElementById('year');
            const urlembed = "{% url 'app_lists:vat_management' 'ES' %}";
            const newUrl = urlembed + '?period=' + period + '&year=' + year.value;
            window.location.href = newUrl;
        }

        const manager_assigned =()=> {
            let tipo = $("#manager_assigned").val();
            table.column(22).search(tipo).draw();
        }

        function openInfoModal() {
            const modal = new bootstrap.Modal(document.getElementById('info-modal'));
            modal.show();
        }
        
        function toggleSelectAllMonths(button) {
            const allChecked = checkAllMonths();
            if (allChecked) {
                document.querySelectorAll('.column-switch').forEach(month => month.checked = false);
                button.textContent = 'Seleccionar todos';
            } else {
                document.querySelectorAll('.column-switch').forEach(month => month.checked = true);
                button.textContent = 'Deseleccionar todos';
            }

            document.querySelectorAll('.column-switch').forEach(month => month.dispatchEvent(new Event('change')));
        }

        function checkAllMonths() {
            const months = document.querySelectorAll('.column-switch');
            return Array.from(months).every(month => month.checked);
        }

        function confirmUpdateTable() {
            Swal.fire({
                title: '¿Quieres actualizar la tabla?',
                text: "Se recalcularán los datos para el periodo y año seleccionados.",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Sí, actualizar <i class="fas fa-sync-alt"></i>',
                cancelButtonText: 'Cancelar',
                customClass: {
                    confirmButton: 'swal2-btn-full-width btn-dark',
                    cancelButton: 'btn-link',
                    actions: 'swal2-action-div-full-width',
                },
            }).then((result) => {
                if (result.isConfirmed) {
                    updateTable();
                }
                if (result.isDismissed) {
                    setTimeout(function() {
                        document.activeElement.blur();
                    }, 300);
                }
            });
        }

        async function updateTable() {

            const year = document.getElementById("year").value;
            const period = $('input[name="periods"]:checked').val();

            Swal.fire({
                icon: 'warning',
                title: 'Actualizando',
                text: `Se están recalculando los datos para el periodo y año seleccionados.`,
                timerProgressBar: true,
                didOpen: () => Swal.showLoading(),
                showCancelButton: false,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false
            });

            const url = "{% url 'app_lists:update_all_seller_list' %}";
            const data = new URLSearchParams({
                list: 'vat_es',
                year: year,
                period: period
            });

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: data.toString()
            });

            if (response.ok) {
                const result = await response.json();

                Swal.fire({
                    icon: 'success',
                    title: 'Actualizado',
                    text: `Se han actualizado los datos para el periodo y año seleccionados.`,
                    timer: 2000,
                    timerProgressBar: true,
                    didOpen: () => Swal.showLoading(),
                    showCancelButton: false,
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    allowEnterKey: false
                });

                table.ajax.reload();
            }

        }

        function showWarning390(){
            Swal.fire({
                icon: 'warning',
                title: 'Modelo 390',
                text: 'Este cliente tiene al menos un modelo 303 pendiente de presentar en este año. Por favor, ten en cuenta que el modelo 390 es obligatorio una vez presentado el modelo 303.',
                confirmButtonText: 'Aceptar',
                customClass: {
                confirmButton: 'btn btn-dark w-100',
                actions: 'swal2-action-div-full-width',
                }
            });
        }

        function goToCreateNotification(department) {
            const period = $('input[name="periods"]:checked').val();
            const year = document.getElementById('year').value;
            const url = "{% url 'app_notifications:create_pending_model_notif_from_cached_list' 'DEPARTMENT_PLACEHOLDER' %}"
                .replace('DEPARTMENT_PLACEHOLDER', department);
            window.location.href = url + `?period=${period}&year=${year}`;
        }
        
    </script>
{% endblock javascripts %}