from django.db import models


class Entry(models.Model):

    # id -> AutoGen

    entry_seller = models.ForeignKey(
        "sellers.Seller",
        related_name="entry_seller",
        on_delete=models.CASCADE,
        verbose_name="Vendedor",
        null=True,
        blank=True,
        db_index=True,
    )

    entry_date = models.DateField(
        verbose_name="Fecha de asiento", null=True, blank=True, db_index=True
    )

    entry_num = models.IntegerField(
        verbose_name="Número de asiento", null=True, blank=True, db_index=True
    )

    entry_document = models.CharField(
        max_length=150,
        verbose_name="Documento",
        null=True,
        blank=True,
    )

    entry_concept = models.CharField(
        max_length=150,
        verbose_name="Concepto",
        null=True,
        blank=True,
    )

    entry_accounting_account = models.CharField(
        max_length=150,
        verbose_name="Cuenta Contable",
        null=True,
        blank=True,
        db_index=True,
    )

    entry_reconciliation = models.ForeignKey(
        "banks.Reconciliation",
        related_name="banks_reconciliation",
        on_delete=models.CASCADE,
        verbose_name="Conciliación Bancaria",
        null=True,
        blank=True,
    )

    entry_invoice = models.ForeignKey(
        "invoices.Invoice",
        related_name="entry_invoice",
        on_delete=models.CASCADE,
        verbose_name="Factura",
        null=True,
        blank=True,
    )

    entry_accounting_account_description = models.CharField(
        max_length=150,
        verbose_name="Descripción de Cuenta Contable de Conciliación Bancaria",
    )

    entry_debit = models.DecimalField(
        blank=True,
        null=True,
        default=0,
        max_digits=15,
        decimal_places=2,
        verbose_name="Debe",
    )

    entry_credit = models.DecimalField(
        blank=True,
        null=True,
        default=0,
        max_digits=15,
        decimal_places=2,
        verbose_name="Haber",
    )

    entry_export = models.ForeignKey(
        "banks.EntryFile",
        related_name="entry_file_export",
        on_delete=models.PROTECT,
        verbose_name="Exportación de asiento contable",
        null=True,
        blank=True,
    )

    is_entry_balanced_result = models.BooleanField(default=False)

    used_in_export = models.BooleanField(default=False, db_index=True)

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Asiento contable"
        verbose_name_plural = "Asientos contables"
        indexes = [
            models.Index(fields=["entry_seller", "entry_date"]),
            models.Index(fields=["entry_seller", "entry_accounting_account"]),
            models.Index(fields=["entry_seller", "entry_num"]),
            models.Index(fields=["entry_accounting_account", "entry_date"]),
            models.Index(fields=["entry_date", "entry_num"]),
        ]

    def __str__(self):
        return str(self.id)

    def is_entry_balanced(self):
        related_entries = Entry.objects.none()
        # if self.entry_invoice is not None:
        #     related_entries |= Entry.objects.filter(entry_document=self.entry_invoice.reference, entry_seller=self.entry_seller)
        # if self.entry_reconciliation is not None and self.entry_reconciliation.invoice is not None:
        #     related_entries |= Entry.objects.filter(entry_document=self.entry_reconciliation.invoice.reference, entry_seller=self.entry_seller)
        # if self.entry_reconciliation is not None and self.entry_reconciliation.accounting_account is not None:
        #     related_entries |= Entry.objects.filter(entry_accounting_account=self.entry_reconciliation.accounting_account, entry_seller=self.entry_seller)
        # # if self.entry_reconciliation is not None and self.entry_reconciliation.movement_transfer is not None:
        # #     related_entries |= Entry.objects.filter(movement_transfer=self.entry_reconciliation.movement_transfer, entry_seller=self.entry_seller)
        related_entries = Entry.objects.filter(
            entry_num=self.entry_num, entry_seller=self.entry_seller
        )

        total_debit = 0
        total_credit = 0
        for entry in related_entries:
            total_debit += round(float(entry.entry_debit), 2)
            total_credit += round(float(entry.entry_credit), 2)

        is_balanced = (total_debit == total_credit) or (
            round(total_debit - total_credit, 2) == 0
        )
        if is_balanced:
            is_balanced = True
            # print("is_balanced - id: " + str(self.id))
            for entry in related_entries:
                entry.is_entry_balanced_result = True
                entry.save()
        else:
            is_balanced = False
            # print("is_not_balanced - id: " + str(self.id))
            for entry in related_entries:
                entry.is_entry_balanced_result = False
                entry.save()

        self.is_entry_balanced_result = is_balanced
        self.save()

        print(
            f"Entry Num: {self.entry_num} - Total Debit: {total_debit} - Total Credit: {total_credit} - Is Balanced: {is_balanced}"
        )

        return is_balanced
