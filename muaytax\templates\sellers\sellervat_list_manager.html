{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Paises IVA Contratados
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <style>
  
     .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }
  
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Paises IVA Contratados</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Paises IVA Contratados</a>
            </li>
          </ul>
        </div>
        <div class="col-4 d-flex justify-content-end" style="padding: 0px 25px;" >
          <a href="./new/" class="btn btn-primary"> 
            Crear nuevo proceso
          </a>
          <a href="./new_sellervat_not_contracted/" class="btn btn-primary"> 
            Crear nuevo país IVA no contratado
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-center align-items-start">
          <div class="input-group">
            <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."  />
          </div>
        </div>
      </div>
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th>País IVA</th>
                  <th>Número IVA</th>
                  <th style="width:10%;">Fecha Alta</th>
                  <th style="width:10%;">Fecha Baja</th>
                  <th style="width:10%;">Fecha Contratación</th>
                  <th style="width:7%;">Contratado</th>
                  <th style="width:7%;">VIES</th>
                  <th style="width:7%;">Estado</th>
                  <th style="width:5%;">Acciones</th>
                </tr>
              </thead>
              <tbody>
                {% for object in object_list %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.vat_country }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.vat_number }}</span>
                  </td>
                  <td class="align-middle">
                    {% if object.activation_date %}
                      <span>{{ object.activation_date|date:"d/M/Y"|lower }}</span>
                    {% else %}
                      <span class="text-center">-</span>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    {% if object.deactivation_date %}
                      <span>{{ object.deactivation_date|date:"d/M/Y"|lower }}</span>
                    {% else %}
                      <span class="text-center">-</span>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    {% if object.contracting_date %}
                      <span>{{ object.contracting_date|date:"d/M/Y"|lower }}</span>
                    {% else %}
                      <span class="text-center">-</span>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    <span data-bs-toggle="tooltip" data-bs-placement="top" 
                      {% if object.is_contracted == True %} data-bs-title="Contratado" {% else %} data-bs-title="Sin Contratacion" {% endif %}
                    >
                      {% if object.is_contracted %}
                        <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                      {% else %}
                        <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                      {% endif %}
                    </span>
                  </td>
                  <td class="align-middle">
                    <span data-bs-toggle="tooltip" data-bs-placement="top" 
                     {% if object.vat_vies == True %} data-bs-title="Activo" {% else %} data-bs-title="Inactivo" {% endif %}
                    >
                      {% if object.vat_vies %}
                        <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                      {% else %}
                        <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                      {% endif %}
                    </span>
                  </td>
                  <td class="align-middle">
                    {% if object.vat_status != None  %}
                    <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="{{ object.vat_status }}">
                    {% else  %}
                    <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Sin Estado">
                    {% endif %}
                      {% if object.type and object.vat_status %}
                      {% if object.type.code == "deactivation" and object.vat_status.code == "on" %}
                      <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                      {% elif object.type.code != "deactivation" and object.vat_status.code == "on" %}
                       <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                      {% elif object.type.code == "deactivation" and object.vat_status.code == "processing" and object.status_process_color.code == "grey" %}
                      <i class="fas fa-exclamation  fa-xl"></i>
                      {% elif object.type.code != "deactivation" and object.vat_status.code == "processing" and object.status_process_color.code == "grey" %}
                      <i class="fas fa-exclamation  fa-xl"></i>
                      {% elif object.type.code == "deactivation" and object.vat_status.code == "processing" and object.status_process_color.code == "blue" %}
                      <i class="fas fa-exclamation  fa-xl" style="color: #0088ff ;"></i>
                      {% elif object.type.code != "deactivation" and object.vat_status.code == "processing" and object.status_process_color.code == "blue" %}
                      <i class="fas fa-exclamation  fa-xl" style="color: #0088ff ;"></i>
                      {% elif object.type.code == "deactivation" and object.vat_status.code == "processing" %}
                      <i class="fas fa-spinner  fa-xl"></i>
                      {% elif object.type.code != "deactivation" and object.vat_status.code == "processing" %}
                      <i class="fas fa-spinner  fa-xl"></i>
                      {% elif object.vat_status.code == "active" %}
                      <i class="fa-regular fa-xl fa-circle-check" style="color: #ffdf00;"></i>
                      {% endif %}
                    {% endif %}
                    </span>
                  </td>
                  <td class="align-middle">
                    <div>
                      {% if object.is_contracted == True %}
                        <a class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Editar" href="{% url 'app_sellers:vat_detail' object.seller.shortname object.pk %}">
                          <i class="feather icon-edit"></i>
                        </a>
                      {% else %}
                        <a class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Editar" href="{% url 'app_sellers:vat_sellervat_not_contracted_update' object.seller.shortname object.pk %}">
                          <i class="feather icon-edit"></i>
                        </a>
                      {% endif %}
                      {% if object.document == True %}
                        <a class="btn btn-icon btn-dark " data-bs-toggle="tooltip" data-bs-placement="top" title="Existen documentos para este pais IVA" href="">
                          <i class="feather icon-trash-2"></i>
                        </a>
                      {% else %}
                        <a class="btn btn-icon btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Eliminar" href="{% url 'app_sellers:vat_delete' object.seller.shortname object.pk %}">
                          <i class="feather icon-trash-2"></i>
                        </a>
                      {% endif %}

                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css"> 
  <script>
      $(document).ready(function(){

        const dataTableOptions = {
          paging: false,
          searching: true, 
          ordering: true,
          truncation: true,
          info: true,
          footer: true,
          columnDefs: [
            { targets: 8, orderable: false }
          ],
          {% comment %}
          columnDefs: [
            { targets: 3, orderData: [3, 'desc'] }
          ],
          {% endcomment %}
          language: {
            lengthMenu: "_MENU_",
            zeroRecords: "No se han encontrado paises contratados.",
            info: "_TOTAL_ resultados. ",
            search: "Buscar:",
            infoEmpty: "No hay resultados que coincidan con su búsqueda.",
            infoFiltered: ""
          },
          dom: 'lrtip',
          fixedHeader: true,
        };
        const dataTable =$("#list-table").DataTable(dataTableOptions);
      
          
          $("#search").on("input", function(){
                      const filtro =$(this).val();
                      console.log(filtro)
                      dataTable.search(filtro).draw();
                  });


    });
      


  </script>
{% endblock javascripts %}
