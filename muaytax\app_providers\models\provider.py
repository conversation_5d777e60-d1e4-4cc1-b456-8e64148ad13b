from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.urls import reverse
from muaytax.signals import disable_for_load_data


class Provider(models.Model):
    # id -> AutoGen

    name = models.CharField(
        max_length=100,
        verbose_name="Nombre"
    )

    nif_cif_iva = models.CharField(
        blank=True, null=True,
        max_length=50,
        verbose_name="NIF/CIF/IVA",
        help_text="NIF/CIF/IVA (si disponemos de él)",
    )

    zip = models.CharField(
        max_length=50,
        verbose_name="Código Postal",
        blank=True, null=True,
    )

    provider_address = models.ForeignKey(
        "address.Address",
        related_name="provider_address",
        on_delete=models.PROTECT,
        verbose_name="Dirección del Proveedor",
        blank=True, null=True,
    )

    country = models.ForeignKey(
        "dictionaries.Country",
        related_name="provider_country",
        on_delete=models.PROTECT,
        verbose_name="País",
        blank=True, null=True,
    )

    is_origin_country = models.BooleanField(
        verbose_name="País origen",
        help_text="¿Es el Pais de Origen del NIF/CIF/IVA?",
        default=False,
    )

    nif_cif_iva_country = models.ForeignKey(
        "dictionaries.Country",
        related_name="provider_nif_cif_iva_country",
        on_delete=models.PROTECT,
        verbose_name="País NIF/CIF/IVA",
        blank=True, null=True,
    )

    vies = models.CharField(
        max_length=50,
        verbose_name="VIES",
        blank=True, null=True,
    )

    provider_type = models.ForeignKey(
        "dictionaries.ProviderType",
        on_delete=models.PROTECT,
        verbose_name="Tipo de proveedor",
        blank=True, null=True,
    )

    provider_number = models.CharField(
        max_length=6,
        verbose_name="Número de proveedor",
        blank=True, null=True,
    )

    account_expenses = models.ForeignKey(
        "dictionaries.AccountExpenses",
        on_delete=models.PROTECT,
        verbose_name="Cuenta de gasto por defecto",
        blank=True, null=True,
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="provider_seller",
        verbose_name="Vendedor",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    @property
    def provider_accounting_account(self):
        if self.provider_number:
            return f'400{self.provider_number}'
        else:
            return None

    class Meta:
        verbose_name = "Proveedor"
        verbose_name_plural = "Proveedores"
        constraints = [
            models.UniqueConstraint(fields=['seller', 'nif_cif_iva'], name='provider_unique_seller_nif_cif_iva'),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse("app_providers:detail", kwargs={"pk": self.pk})

    def save(self, *args, **kwargs):
        if self.provider_number is None:
            last_provider = Provider.objects.filter(seller=self.seller, provider_number__isnull=False).order_by(
                '-provider_number').first()
            if last_provider is None:
                self.provider_number = "000001"
            else:
                next_number = int(last_provider.provider_number) + 1
                self.provider_number = f"{next_number:06}"
        super().save(*args, **kwargs)

# @receiver(post_save, sender=Provider)
# @disable_for_load_data
# def after_sellervat_save(sender, instance, created, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)

# @receiver(post_delete, sender=Provider)
# @disable_for_load_data
# def after_sellervat_delete(sender, instance, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)