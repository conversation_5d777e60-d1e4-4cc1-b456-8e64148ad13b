import math
import os
import tempfile
import zipfile
from datetime import datetime
from typing import Tuple

import pdfkit
from django.conf import settings
from django.db.models import F, Q
from django.http import HttpResponse, HttpResponseBadRequest
from django.template.loader import render_to_string
from django.utils import timezone


from muaytax.app_documents.models import PresentedModel
from muaytax.app_invoices.models import Concept, Invoice
from muaytax.app_invoices.models.amortization_entry import AmortizationEntry
from muaytax.app_sellers.models import Seller, SellerVat
from muaytax.utils.general import get_actual_first_and_last_date


# Calcula el mes mínimo del período actual
def get_min_period_date():
    periods = {
        'Q1': [1, 2, 3],
        'Q2': [4, 5, 6],
        'Q3': [7, 8, 9],
        'Q4': [10, 11, 12],
    }
    current_month = datetime.now().date().month
    current_year = datetime.now().date().year

    # Recorre el diccionario de los períodos para saber el mes mínimo del mes actual
    for period, months in periods.items():
        if current_month in months:
            min_month = min(months)
            min_date = datetime(current_year, min_month, 1)
            return timezone.make_aware(min_date, timezone.get_current_timezone())

def get_current_period(month: int) -> str:
    """
    Función que recibe un mes y devuelve el código de período
    correspondiente al mes ingresado.
    """
    periods = {
        1: 'Q1', 2: 'Q1', 3: 'Q1',
        4: 'Q2', 5: 'Q2', 6: 'Q2',
        7: 'Q3', 8: 'Q3', 9: 'Q3',
        10: 'Q4', 11: 'Q4', 12: 'Q4'
    }

    return periods.get(month, None)

def download_massive_invoices_selected(seller, invoiceId: list) -> HttpResponse:
    """
    Función para descargar un .zip con las facturas seleccionadas
    Además adjunta en el .zip un .txt con los datos de facturas que ha fallado al descargar
    """
    ivoices_to_donwload = []
    failed_invoices = []

    for invoice in invoiceId:
        try:
            if invoice.is_txt_amz is not True and invoice.file:
                file_exists = os.path.exists(invoice.file.path)
                if file_exists:
                    ivoices_to_donwload.append(invoice.file.path)
                else:
                    failed_invoices.append(f"ID de Factura: {invoice.id}; Numero de factura: {invoice.reference}; Motivo: No se ha encontrado el archivo")
            if invoice.is_generated:
                pdf_generated = generate_invoice_pdf(seller, invoice)
                if pdf_generated:
                    ivoices_to_donwload.append(pdf_generated)
                else:
                    failed_invoices.append(f"ID de Factura: {invoice.id}; Numero de factura: {invoice.reference}; Motivo: Ha fallado al generar la factura")
        except Exception as e:
            print(f"Error procesando factura {invoice.id}: {e}")
            failed_invoices.append(f"ID de Factura: {invoice.id}; Numero de factura: {invoice.reference}; Motivo: Error al procesar - {str(e)}")

    if not ivoices_to_donwload and not failed_invoices:
        response =  HttpResponse(status=204)
        return response

    temp_dir = tempfile.mkdtemp()

    try:
        zip_file = os.path.join(temp_dir, f"facturas_seleccionadas_{seller.shortname}.zip")

        with zipfile.ZipFile(zip_file, "w") as zipf:
            for pdf_path in ivoices_to_donwload:
                    pdf_filename = os.path.basename(pdf_path)
                    zipf.write(pdf_path, arcname=pdf_filename)

            if failed_invoices:
                failed_invoices_txt = os.path.join(temp_dir, "Facturas_fallidas.txt")
                with open(failed_invoices_txt, "w") as f:
                    for failed_invoice in failed_invoices:
                        f.write(f"{failed_invoice}\n")
                zipf.write(failed_invoices_txt, arcname="Facturas_fallidas.txt")

        # Enviar el ZIP
        with open(zip_file, "rb") as f:
            response = HttpResponse(f.read(), content_type="application/zip")
            response["Content-Disposition"] = f"attachment; filename=Facturas_seleccionadas_{seller.shortname}.zip"
            return response

    except Exception as e:
        print(f"Error when creating .zip in massive invoices: {e}")
        return HttpResponseBadRequest("Error al crear el archivo .zip con las facturas seleccionadas")

    finally:
        # Eliminar el directorio temporal
        try:
            for file in os.listdir(temp_dir):
                os.remove(os.path.join(temp_dir, file))
            os.rmdir(temp_dir)
        except Exception as e:
            print(f"Error limpiando directorio temporal: {e}")

def get_period_months(period: str):
    if period == "-1":
        return "01", "03"
    elif period == "-2":
        return "04", "06"
    elif period == "-3":
        return "07", "09"
    elif period == "-4":
        return "10", "12"

def generate_invoice_pdf(seller: Seller, invoice: Invoice):
    if (invoice.currency.pk == "EUR"):
        invoice.currency_symbol='€'
    elif (invoice.currency.pk == "USD"):
        invoice.currency_symbol='$'
    elif (invoice.currency.pk == "GBP"):
        invoice.currency_symbol='£'
    elif (invoice.currency.pk == "JPY"):
        invoice.currency_symbol='¥'
    else:
        invoice.currency_symbol = invoice.currency.pk

    # Get Concepts
    concepts = Concept.objects.filter(invoice_id=invoice.id)
    concepts = concepts.annotate(total_amount_euros=F('amount_euros') * F('quantity'))
    concepts = concepts.annotate(total_vat_euros=F('amount_euros') * F('quantity') * F('vat') / 100)
    concepts = concepts.annotate(total_irpf_euros=F('amount_euros') * F('quantity') * F('irpf') / 100)
    concepts = concepts.annotate(total_amount_currency=F('amount_currency') * F('quantity'))
    concepts = concepts.annotate(total_vat_currency=F('amount_currency') * F('quantity') * F('vat') / 100)
    concepts = concepts.annotate(total_irpf_currency=F('amount_currency') * F('quantity') * F('irpf') / 100)
    concepts = concepts.order_by("id")

    # Get Seller Vat
    seller_vat = SellerVat.objects.filter(seller_id=seller.id, vat_country=invoice.tax_country).first()

    # Get Filename
    file_name = invoice.name.strip() if invoice.name and len(invoice.name.strip()) > 0 else "{}_{}.pdf".format(seller.shortname, invoice.reference)
    file_name = file_name.replace(" ", "_").replace("/", "_").replace("\\", "_")

    # temp_dir = tempfile.mkdtemp()

    try:

        # Get Environment URL
        environments = {
            'local': 'http://localhost',
            'dev': 'https://dev.muaytax.com',
            'prod': 'https://app.muaytax.com'
        }
        environment = os.environ.get('DJANGO_ENV', 'local')
        env_url = environments.get(environment, 'http://localhost')

        # Ruta donde se guardará el PDF generado
        output_path = os.path.join(settings.MEDIA_ROOT, 'uploads', file_name)

        # Cargar el template
        template = 'invoices/invoice_template.html'

        logo_exist = os.path.exists(seller.logo.path) if seller.logo else False

        data = {
            "seller": seller,
            "invoice": invoice,
            "concepts": concepts,
            "seller_vat": seller_vat,
            "logo_exists": logo_exist,
            "env_url": env_url,
            "show_verifactu": False, #TODO: Poner a true cuando se implemente la verifactu
            "env_url": env_url,
            "show_verifactu": False, #TODO: Poner a true cuando se implemente la verifactu
        }

        # Renderizar el template con los datos
        rendered_template = render_to_string(template, data)

        pdfkit.from_string(rendered_template, output_path)

        return output_path

    except Exception as e:
        print(f"General error when generating PDF: {e}")
        return None

    except pdfkit.configuration.ConfigurationError as pdf_error:
        print(f"Erroren de la librería pdfkit: {pdf_error}")
        return None

def query_invoices_all_filters(seller: Seller, filters: dict) -> list:
    """
    Función para filtrar las facturas por uno o todos los filtros del listado de facturas
    """

    invoice_status = filters['invoice_status']
    year = filters['year']
    month = filters['month']
    tax_country_id = filters['tax_country_id']
    departure_country_id = filters['departure_country_id']
    invoice_type = filters['invoice_type']
    transaction_type = filters['transaction_type']
    search = filters['search']

    invoices_filtered = Invoice.objects.filter(
            seller=seller
        ).exclude(invoice_category__code__icontains='_copy')

    if invoice_status:
        invoices_filtered = invoices_filtered.filter(status_id=invoice_status)

    if year:
        if month in ["-1", "-2", "-3", "-4"]:
            first_month, last_month = get_period_months(month)
            invoices_filtered = invoices_filtered.filter(
                Q (
                    accounting_date=None,
                    expedition_date__year=year,
                    expedition_date__month__range=(first_month,last_month)
                ) |
                Q (
                    accounting_date__isnull=False,
                    accounting_date__year=year,
                    accounting_date__month__range=(first_month,last_month)
                )
            )
        elif month:
            invoices_filtered = invoices_filtered.filter(
                Q(
                    accounting_date=None,
                    expedition_date__year=year,
                    expedition_date__month=month
                ) |
                Q(
                    accounting_date__isnull=False,
                    accounting_date__year=year,
                    accounting_date__month=month

                )
            )
        else:
            invoices_filtered = invoices_filtered.filter(
                Q (
                    accounting_date=None,
                    expedition_date__year=year
                ) |
                Q (
                    accounting_date__isnull=False,
                    accounting_date__year=year
                )
            )

    if tax_country_id:
        invoices_filtered = invoices_filtered.filter(tax_country_id=tax_country_id)

    if departure_country_id:
        invoices_filtered = invoices_filtered.filter(departure_country_id=departure_country_id)

    if invoice_type == 'sales':
        invoices_filtered = invoices_filtered.filter(invoice_category__code='sales').exclude(transaction_type__code__icontains='-transfer')
    elif invoice_type == 'expenses':
        invoices_filtered = invoices_filtered.filter(invoice_category__code='expenses').exclude(transaction_type__code__icontains='-transfer')
    elif invoice_type == 'transfers':
        invoices_filtered = invoices_filtered.filter(transaction_type__code__icontains='transfer')
    elif invoice_type == 'all':
        invoices_filtered = invoices_filtered.exclude(transaction_type__code__icontains='-transfer')

    if transaction_type:
        invoices_filtered = invoices_filtered.filter(transaction_type__code__in=transaction_type)

    if search:
        invoices_filtered = invoices_filtered.filter(
            Q(amz_txt_eur__file__icontains=search) |
            Q(reference__icontains=search) |
            Q(customer__name__icontains=search) |
            Q(provider__name__icontains=search) |
            Q(status__description__icontains=search) |
            Q(tax_country__name__icontains=search) |
            Q(invoice_category__description__icontains=search) |
            Q(invoice_type__description__icontains=search) |
            Q(transaction_type__description__icontains=search) |
            Q(total_amount_euros__icontains=search) |
            Q(total_euros__icontains=search)
        )

    return invoices_filtered

def txt_upload_previous_pm_validation(seller: Seller, year: int, month: int) -> bool:
    """
    Función para validar si el archivo TXT subido corresponde al mes anterior
    """
    # Obtenemos el período trimestral correspondiente al mes
    trimestral_period = get_current_period(month)

    # Obtenemos el período mensual correspondiente al mes
    mensual_period = f"M{month}"

    if trimestral_period is None:
        return False

    # Verificamos si existe algún modelo presentado para el período trimestral o mensual
    # excluyendo los que están en estado "disagreed"
    query = Q(period=trimestral_period) | Q(period=mensual_period)
    return PresentedModel.objects.filter(seller=seller, year=year).filter(query).exclude(status__code='disagreed').exists()

def validate_invoice_uploader_availability(seller: Seller, sellervats: SellerVat) -> Tuple[bool, bool, str, str]:
    """
    Función para cerrar el cargador de facturas en época de presentación o si el seller no cumple los requisitos
    """

    qstart, qend = get_actual_first_and_last_date()
    current_date = datetime.now().date()
    # current_date = datetime(2025, 4, 9).date() # Descomentar para pruebas, con lo cual no hace falta cambiar la fecha del sistema
    year = current_date.year

    # Fechas de cierre de presentación
    presentation_periods = [
        (f'{year}-04-10', f'{year}-04-22'), # cambio de fecha de inicio de bloqueo pedido en tarjeta https://trello.com/c/hHMBLXHe
        (f'{year}-07-10', f'{year}-07-22'),
        (f'{year}-10-10', f'{year}-10-22'),
        (f'{year}-01-13', f'{year}-02-01')
    ]

    # Verificar si está dentro del período de presentación
    for start, end in presentation_periods:
        if datetime.strptime(start, '%Y-%m-%d').date() < current_date <= datetime.strptime(end, '%Y-%m-%d').date():
            return False, False, "Sin Acceso", "Se ha cerrado el periodo de presentación de documentos."

    # Verificar si el seller cumple los requisitos para presentar facturas
    if seller.contracted_accounting_date and seller.contracted_accounting_date <= qend:
        if not seller.contracted_accounting_end_date or seller.contracted_accounting_end_date >= qstart:
            return True, True, "", ""

    if seller.contracted_maintenance_llc_date and seller.contracted_maintenance_llc_date <= qend:
        if not seller.contracted_maintenance_llc_end_date or seller.contracted_maintenance_llc_end_date >= qstart:
            return True, True, "", ""

    if sellervats:
        for vat in sellervats:
            if vat.contracting_date and vat.contracting_date <= qend:
                if not vat.end_contracting_date or vat.end_contracting_date >= qstart:
                    return True, True, "", ""

    # Verificar acceso a carga de txt
    canShowTXT = False
    if seller.oss_date and seller.oss_date <= qend:
        if not seller.oss_end_date or seller.oss_end_date >= qstart:
            canShowTXT = True

    if seller.contracted_accounting_txt_date and seller.contracted_accounting_txt_date <= qend:
        if not seller.contracted_accounting_txt_end_date or seller.contracted_accounting_txt_end_date >= qstart:
            canShowTXT = True

    return canShowTXT, False, "Sin Acceso", "No tienes acceso a la presentación de facturas porque no cumples los requisitos."

    # comento porque a partir de aquí no se ejecutaba. 30-04-2025. Si ves este comentario y han pasado unos meses, recomiendo borrar toda esta sección.
    #canShowTXT = False
    # canShowInvoices = False
    # cantShowTitle = ""
    # cantShowMessage = ""

    # if current_date.month == 1 or current_date.month == 4 or current_date.month == 7 or current_date.month == 10:
    #     if current_date.day <= 10:
    #         canShowNow = True
    #     elif current_date.day >= 27 and current_date.month != 1:
    #         canShowNow = True
    #     else:
    #         canShowNow = False
    # else:
    #     canShowNow = True

    # # Caso 1: seller tiene contabilidad contratada
    # if canShowInvoices == False and seller.contracted_accounting_date is not None:
    #     if seller.contracted_accounting_end_date is None:
    #         if (seller.contracted_accounting_date <= qend):
    #             canShowTXT = True
    #             canShowInvoices = True
    #             cantShowTitle = ""
    #             cantShowMessage = ""
    #         else:
    #             canShowTXT = False
    #             canShowInvoices = False
    #             cantShowTitle = "Sin Acceso"
    #             cantShowMessage = "No tienes acceso a la presentación de facturas porque no cumples los requisitos."
    #     elif seller.contracted_accounting_end_date is not None:
    #         if (seller.contracted_accounting_date <= qend and seller.contracted_accounting_end_date >= qstart):
    #             canShowTXT = True
    #             canShowInvoices = True
    #             cantShowTitle = ""
    #             cantShowMessage = ""
    #         else:
    #             canShowTXT = False
    #             canShowInvoices = False
    #             cantShowTitle = "Sin Acceso"
    #             cantShowMessage = "No tienes acceso a la presentación de facturas porque no cumples los requisitos."

    # # Caso 2: Seller tiene contratado mantenimiento LLC
    # if canShowInvoices == False and seller.contracted_maintenance_llc_date is not None:
    #     if seller.contracted_maintenance_llc_end_date is None:
    #         if (seller.contracted_maintenance_llc_date <= qend):
    #             canShowTXT = True
    #             canShowInvoices = True
    #             cantShowTitle = ""
    #             cantShowMessage = ""
    #         else:
    #             canShowTXT = False
    #             canShowInvoices = False
    #             cantShowTitle = "Sin Acceso"
    #             cantShowMessage = "No tienes acceso a la presentación de facturas porque no cumples los requisitos."
    #     elif seller.contracted_maintenance_llc_end_date is not None:
    #         if (seller.contracted_maintenance_llc_date <= qend and seller.contracted_maintenance_llc_end_date >= qstart):
    #             canShowTXT = True
    #             canShowInvoices = True
    #             cantShowTitle = ""
    #             cantShowMessage = ""
    #         else:
    #             canShowTXT = False
    #             canShowInvoices = False
    #             cantShowTitle = "Sin Acceso"
    #             cantShowMessage = "No tienes acceso a la presentación de facturas porque no cumples los requisitos."

    # # Caso 3: El seller tiene algún país IVA contratado
    # if canShowInvoices == False and sellervats is not None and len(sellervats) > 0:
    #     canShowTXT = False
    #     canShowInvoices = False
    #     cantShowTitle = "Sin Acceso"
    #     cantShowMessage = "No tienes acceso a la presentación de facturas porque no cumples los requisitos."
    #     for vat in sellervats:
    #         if vat.contracting_date is not None:
    #             if vat.end_contracting_date is None:
    #                 if (vat.contracting_date <= qend or (vat.contracting_date is None and vat.is_contracted == True)):
    #                     canShowTXT = True
    #                     canShowInvoices = True
    #                     cantShowTitle = ""
    #                     cantShowMessage = ""
    #                     break
    #             elif vat.end_contracting_date is not None:
    #                 if (vat.contracting_date <= qend and vat.end_contracting_date >= qstart):
    #                     canShowTXT = True
    #                     canShowInvoices = True
    #                     cantShowTitle = ""
    #                     cantShowMessage = ""
    #                     break

    # if canShowInvoices == False:
    #     cantShowTitle = "Sin Acceso"
    #     cantShowMessage = "No tienes acceso a la presentación de facturas porque no cumples los requisitos."
    #     # CASE: Seller Have OSS => Can Show TXT
    #     if seller.oss_date is not None and seller.oss_end_date is None:
    #         if (seller.oss_date <= qend):
    #             canShowTXT = True
    #             canShowInvoices = False
    #     elif seller.oss_date is not None and seller.oss_end_date is not None:
    #         if (seller.oss_date <= qend and seller.oss_end_date >= qstart):
    #             canShowTXT = True
    #             canShowInvoices = False
    #     # CASE: Seller Have Accounting TXT => Can Show TXT
    #     if seller.contracted_accounting_txt_date is not None and seller.contracted_accounting_txt_end_date is None:
    #         if (seller.contracted_accounting_txt_date <= qend):
    #             canShowTXT = True
    #             canShowInvoices = False
    #     elif seller.contracted_accounting_txt_date is not None and seller.contracted_accounting_txt_end_date is not None:
    #         if (seller.contracted_accounting_txt_date <= qend and seller.contracted_accounting_txt_end_date >= qstart):
    #             canShowTXT = True
    #             canShowInvoices = False

    # # Disable UPLOAD FOR USERS ###################################################################
    # ahorita = datetime.now().date()
    # year = ahorita.year
    # if (
    #     ( ahorita > datetime.strptime(f'{year}-04-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-04-27', '%Y-%m-%d').date() ) or
    #     ( ahorita > datetime.strptime(f'{year}-07-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-07-27', '%Y-%m-%d').date() ) or
    #     ( ahorita > datetime.strptime(f'{year}-10-10', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-10-27', '%Y-%m-%d').date() ) or
    #     ( ahorita > datetime.strptime(f'{year}-01-13', '%Y-%m-%d').date() and ahorita < datetime.strptime(f'{year}-02-01', '%Y-%m-%d').date() )
    # ):
    #     canShowInvoices = False
    #     canShowTXT = False
    #     cantShowTitle = "Sin Acceso"
    #     cantShowMessage = "Se ha cerrado el periodo de presentacion de documentos."
    # ##############################################################################################


    # return canShowNow, canShowTXT, canShowInvoices, cantShowTitle, cantShowMessage

def calc_date_vat_contracting_discontinue_show(current_date: datetime, vat_discontinue_date: datetime) -> bool:
    """
    Función para calcular si la bandera de país iva con fecha de baja debe seguir mostrandose o no en función de su fecha y en qué momento del periodo estamos.
    Si se dió de baja durante un trimestre la bandera debe seguir apareciendo + 1 mes adicional al finalizar el trimestre (ya que es el mes de presentación)
    """

    current_period = int(get_current_period(current_date.month).replace("Q", ""))
    vat_discontinue_period = int(get_current_period(vat_discontinue_date.month).replace("Q", ""))
    if (current_period == vat_discontinue_period) or (vat_discontinue_period == current_period - 1 and current_date.month in [1, 4, 7, 10]):
        return True
    else:
        return False

# Utilidades para la amortización de facturas
def calculate_amortization_schedule(total_amount, coefficient):
    """
    Calcula las cuotas de amortización para una factura.
    """
    coef_value = float(coefficient.max_coefficient)
    annual_quota = round(total_amount * (coef_value / 100), 2)
    # Usamos truncamiento en lugar de redondeo para evitar exceso
    monthly_quota = math.trunc((annual_quota / 12) * 100) / 100
    # Otras variantes
    # monthly_quota = round(annual_quota / 12, 2) # Cuotas equilibradas pero la anual se supera por centimos

    total_months = int(total_amount // monthly_quota)
    total_monto_calculado = monthly_quota * total_months
    final_monthly_quota = round(total_amount - total_monto_calculado, 2)

    # Ajustar si la última cuota es menor o igual a cero
    if final_monthly_quota <= 0:
        final_monthly_quota = monthly_quota  # La última cuota se mantiene como la cuota mensual
    else:
        total_months += 1  # Si hay diferencia, se agrega un mes más

    total_years = total_months // 12 + (1 if total_months % 12 != 0 else 0)

    return {
        "annual_quota": annual_quota,
        "monthly_quota": monthly_quota,
        "final_monthly_quota": final_monthly_quota,
        "total_months": total_months,
        "total_years": total_years,
    }

def apply_amortization_data(amortization_entry, invoice, coefficient, amortization_data):
    """
    Aplica los valores de amortización a la instancia proporcionada.
    """
    amortization_entry.start_date = invoice.accounting_date
    amortization_entry.total_years = amortization_data["total_years"]
    amortization_entry.total_months = amortization_data["total_months"]
    amortization_entry.annual_quota = amortization_data["annual_quota"]
    amortization_entry.monthly_quota = amortization_data["monthly_quota"]
    amortization_entry.final_monthly_quota = amortization_data["final_monthly_quota"]
    amortization_entry.total_amount = invoice.total_amount_euros
    amortization_entry.coefficient_reference = coefficient
    return amortization_entry

def create_amortization(invoice, coefficient):
    """
    Crea una nueva entrada de amortización para la factura gasto.
    """
    amortization_data = calculate_amortization_schedule(invoice.total_amount_euros, coefficient)

    amortization_entry = AmortizationEntry(
        invoice=invoice,
        start_date=invoice.accounting_date,
    )
    amortization_entry = apply_amortization_data(amortization_entry, invoice, coefficient, amortization_data)
    amortization_entry.save()

    print(f"[AMORTIZATION] Amortización creada para la factura {invoice.pk}.")

def update_amortization(invoice, coefficient, amortization_entry):
    """
    Actualiza la amortización existente para la factura gasto.
    """
    amortization_data = calculate_amortization_schedule(invoice.total_amount_euros, coefficient)
    amortization_entry = apply_amortization_data(amortization_entry, invoice, coefficient, amortization_data)

    # Guardar cambios en la misma instancia
    amortization_entry.save(update_fields=[
        'start_date',
        'total_years',
        'total_months',
        'annual_quota',
        'monthly_quota',
        'final_monthly_quota',
        'total_amount',
        'coefficient_reference'
    ])

    print(f"[AMORTIZATION] Amortización actualizada para la factura {invoice.pk}.")

def delete_amortization(invoice):
    """
    Elimina la amortización asociada a la factura solo si existe.
    """
    amortization_entry = AmortizationEntry.objects.filter(invoice=invoice).first()

    if amortization_entry:
        amortization_entry.delete()
        print(f"[AMORTIZATION] Amortización eliminada para la factura {invoice.pk}.")
    else:
        print(f"[AMORTIZATION] No se encontró amortización para eliminar en la factura {invoice.pk}.")

