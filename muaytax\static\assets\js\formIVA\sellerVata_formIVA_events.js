// sellerVata_formIVA_events.js

// Objeto global que gestiona los eventos del formulario IVA
window.FormIVAEvents = {

    init: function () {
        this.attachTabEvents();
        this.attachSaveEvents();
        this.attachSubmitEvent();
        this.attachTutorialEvent();
        DebugLogger.log("Eventos del formulario IVA inicializados.");
    },

    // Cambia entre pestañas del formulario
    attachTabEvents: function () {
        const tabButtons = document.querySelectorAll('.nav-link');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const tabId = button.id;
                FormIVAFunctions.updateProgressBar(tabId);
                DebugLogger.log(`Tab cambiado a ${tabId}`);
            });
        });
    },

    // Botones de guardar y avanzar
    attachSaveEvents: function () {
        const saveButtons = document.querySelectorAll(".partial_saving");
        saveButtons.forEach(btn => {
            btn.addEventListener("click", async (e) => {
                e.preventDefault();
                DebugLogger.log("Click en guardar y seguir editando.");
                await FormIVARequests.saveForm();
            });
        });
    },

    // Botón de finalizar formulario
    attachSubmitEvent: function () {
        const submitBtn = document.getElementById("enviarformIVA");
        if (submitBtn) {
            submitBtn.addEventListener("click", async (e) => {
                e.preventDefault();
                DebugLogger.log("Click en enviar formulario.");
                await FormIVARequests.processedFormularioIVA();
            });
        }
    },

    // Botón del tutorial
    attachTutorialEvent: function () {
        const tutorialBtn = document.getElementById("openVideoTutorial");
        const iframe = document.getElementById("tutorial");

        if (tutorialBtn && iframe) {
            tutorialBtn.addEventListener("click", () => {
                iframe.style.visibility = iframe.style.visibility === "visible" ? "hidden" : "visible";
                DebugLogger.log("Toggled video tutorial.");
            });
        }
    }
};

// Ejecutar al cargar el DOM
document.addEventListener("DOMContentLoaded", function () {
    FormIVAEvents.init();
});
