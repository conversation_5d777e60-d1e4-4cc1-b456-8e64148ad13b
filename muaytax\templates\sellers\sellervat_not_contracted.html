{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}
{% block title %}Pais IVA{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:vat_list' seller.shortname %}">Paises IVA</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos generales país IVA no contratado</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" id="form" enctype="multipart/form-data" action=".">
        {% csrf_token %}
        <div class="row">
          <div class="d-flex align-items-center justify-content-between">
            <h3 id="title_process">País IVA No Contratado</h3>
          </div>
          {% if form.non_field_errors %}
            <div class="alert alert-danger" role="alert">
              {{ form.non_field_errors }}
            </div>
          {% endif %}
        </div>
        <hr>
        <br>
        <div class="row">
          <div class="col-6 mb-3">
            <label for="id_vat_country" class="form-label requiredField">
              País del IVA:
            </label>
            <select id="id_vat_country" name="vat_country" class="select form-select" onchange="onChangeCountry()" required="">
              <option value="" selected>---------</option>
              {% for country in countries %}
                {% if country.iso_code == object.vat_country.iso_code %}
                  <option value="{{ country.iso_code }}" selected>{{ country.name }}</option>
                {% else %}
                  <option value="{{ country.iso_code }}">{{ country.name }}</option>
                {% endif %}
              {% endfor %}
            </select>
          </div>
          <br>
          <div class="col-6 mb-3">
            <label for="id_vat_number" class="form-label requiredField">
              Número de IVA:
            </label>
            <input type="text" name="vat_number" value="{{ object.vat_number }}" class="textinput textInput form-control"
                  required="" id="id_vat_number">
          </div>

          <div class="col-6 mb-3 d-none" id="div_it_representation_type">
            <label for="id_it_representation_type" class="form-label">
              Tipo de representación:
            </label>
            <select id="id_it_representation_type" name="it_representation_type" class="select form-select" onchange="onChangeItRepresentationType()">
              <option value=""  {% if object.it_representation_type == None %} selected {% endif %}>---------</option>
              <option value="1" {% if object.it_representation_type == '1' %}  selected {% endif %}>Directa</option>
              <option value="2" {% if object.it_representation_type == '2' %}  selected {% endif %}>Indirecta</option>
            </select>
          </div>

          <div class="col-6 mb-3 d-none" id="div_vat_representative">
            <label for="id_vat_representative" class="form-label">
              Representante:
            </label>
            <select id="id_vat_representative" name="vat_representative"  class="select form-select">
              <option value="">---------</option>
              {% for repre in representative %}
                {% if repre.pk == object.vat_representative.pk %}
                  <option value="{{ repre.pk }}" selected>{{ repre.first_name }} {{ repre.last_name }}</option>
                {% else %}
                  <option value="{{ repre.pk }}">{{ repre.first_name }} {{ repre.last_name }}</option>
                {% endif %}
              {% endfor %}
            </select>
          </div>
        </div>


        {% if object.pk is not None %}
          <div class="col mb-3">
            <div class="form-check">
              <input
                type="checkbox"
                id="id_is_contracted"
                name="is_contracted"
                class="checkboxinput form-check-input"
                {% if object.is_contracted == True %} checked="true" {% endif %}
              />
              <label for="id_is_contracted" class="form-check-label">
                Contratado
              </label>
              <div id="hint_id_is_contracted" class="form-text">¿Esta el Nº IVA contratado? Si:Activo | No:Inactivo
              </div>
            </div>
          </div>
        {% endif %}
        <div class=" mt-2 mb-3 d-flex justify-content-center align-items-center">
          {% if object.pk is not None %}
            <button type="submit" id="submit_button" class="btn btn-primary">Actualizar</button>
          {% else %}
            <button type="submit" id="new_submit_button" class="btn btn-primary">Guardar</button>
          {% endif %}
        </div>
      </form>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
<script>
  const onChangeCountry = () => {
    const country = document.getElementById('id_vat_country').value;
    console.log("Country: ", country);
    const divItRepresentationType = document.getElementById('div_it_representation_type');
    const divVatRepresentative = document.getElementById('div_vat_representative');
    const itRepresentationType = document.getElementById('id_it_representation_type');
    const vatRepresentative = document.getElementById('id_vat_representative');
    if (country === 'IT') {
      divItRepresentationType.classList.remove('d-none');
      itRepresentationType.required = true;
      itRepresentationType.disabled = false;
      divVatRepresentative.classList.remove('d-none');
      vatRepresentative.disabled = false;
    } else {
      divItRepresentationType.classList.add('d-none');
      itRepresentationType.required = false;
      itRepresentationType.disabled = true;
      divVatRepresentative.classList.add('d-none');
      vatRepresentative.required = false;
      vatRepresentative.disabled = true;
    }
  }

  const onChangeItRepresentationType = () => {
    const itRepresentationType = document.getElementById('id_it_representation_type').value;
    const vatRepresentative = document.getElementById('id_vat_representative');
    if (itRepresentationType === '2' || itRepresentationType === '' || itRepresentationType == null ||  itRepresentationType === undefined ) {
      vatRepresentative.value = '';
      vatRepresentative.required = false;
      vatRepresentative.disabled = true;
    } else {
      vatRepresentative.required = true;
      vatRepresentative.disabled = false;
    }
  }

  window.onload = function() {
    onChangeCountry();
    onChangeItRepresentationType();
  };

</script>
{% endblock javascripts %}