def get_contact_message_for_product(platform: str, product_id: str, is_rap_or_lucid=None) -> str:
    """
    Esta funcion devuelve un mensaje generico para el cuerpo del email de contacto, dependiendo de la plataforma y el producto.
    Ojo: Al llamarse de dos sitios diferentes del serializer de creación de compra, product_id puede ser un string o una lista de strings.

    :is_lucid: booleano que indica si el producto es del los productos de Lucid o rap.
    """
    amzvat_ids = [
        '10317', '10318', '10321', '10322', '10323', '10324', '10325', '10326', '10327', 
        '10328', '11410', '12812', '14002', '15674', '15685', '15696', '15697', '15698', 
        '15699', '15700', '15701', '15702', '15703', '15704', '15705', '15706', '19852', 
        '19853', '19854', '19855', '19856', '19857', '19858', '19860', '19861', '19862', 
        '19863', '19799', '19800'
    ]
    if product_id in ['6987', '7529', '17164', '17167']: # Para los ids de Asesoramiento Fiscal no se pondrá este texto genérico en el cuerpo del email. Solicitado por Arturo.
        return ""

    if product_id in amzvat_ids or is_rap_or_lucid:
        platform = 'amzvat'

    # Mensajes por plataforma
    platform_messages = {
        "muaytax": {
            "message": """
                <div style="font-family: Arial, sans-serif; font-size: 14px; color: #23496d;">
                    <p style="margin: 0;">Te confirmo que hemos recibido tu pedido correctamente.</p>
                    <p style="margin-top: 10px;">A partir de ahora el departamento de <u>Gestoría USA</u>, que serán tus nuevos gestores fiscales, te enviarán un correo con la información requerida para continuar con el proceso.</p>
                    <p>Para futuras comunicaciones podrás contactar mediante:</p>
                    <ul style="list-style-type: disc; padding-left: 20px; margin-top: 10px;">
                        <li style="margin-bottom: 10px;">
                            <b>Correo electrónico a</b> <a href="mailto:<EMAIL>" style="color: #00a4bd; text-decoration: none;"><EMAIL></a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>WhatsApp al</b> <a href="https://wa.me/19295603143" style="color: #00a4bd; text-decoration: none;">+1 (929) 560-3143</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>Agendar una llamada a través de</b> <a href="https://app.muaytax.com" style="color: #00a4bd; text-decoration: none;">app.muaytax.com</a> en el apartado de Soporte > Solicitar una llamada.
                        </li>
                    </ul>
                    <p>Saludos,</p>
                </div>
            """
        },
        "gestoria": {
            "message": """
                <div style="font-family: Arial, sans-serif; font-size: 14px; color: #23496d;">
                    <p style="margin: 0;">Te confirmo que hemos recibido tu pedido correctamente.</p>
                    <p style="margin-top: 10px;">A partir de ahora el departamento de <u>Gestoría España</u>, que serán tus nuevos gestores fiscales, te enviarán un correo con la información requerida para continuar con el proceso.</p>
                    <p>Para futuras comunicaciones podrás contactar mediante:</p>
                    <ul style="list-style-type: disc; padding-left: 20px; margin-top: 10px;">
                        <li style="margin-bottom: 10px;">
                            <b>Correo electrónico a</b> <a href="mailto:<EMAIL><" style="color: #00a4bd; text-decoration: none;"><EMAIL></a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>WhatsApp al</b> <a href="https://wa.me/34960730122" style="color: #00a4bd; text-decoration: none;">+34 ***********</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>Agendar una llamada a través de</b> <a href="https://app.muaytax.com" style="color: #00a4bd; text-decoration: none;">app.muaytax.com</a> en el apartado de Soporte > Solicitar una llamada.
                        </li>
                    </ul>
                    <p>Saludos,</p>
                </div>
            """
        },
        "amzvat": {
            "message": """
                <div style="font-family: Arial, sans-serif; font-size: 14px; color: #23496d;">
                    <p style="margin: 0;">Te confirmo que hemos recibido tu pedido correctamente.</p>
                    <p style="margin-top: 10px;">A partir de ahora el departamento de <u>Gestoría IVA</u>, que serán tus nuevos gestores fiscales, te enviarán un correo con la información requerida para continuar con el proceso.</p>
                    <p>Para futuras comunicaciones podrás contactar mediante:</p>
                    <ul style="list-style-type: disc; padding-left: 20px; margin-top: 10px;">
                        <li style="margin-bottom: 10px;">
                            <b>Correo electrónico a</b> <a href="mailto:<EMAIL>" style="color: #00a4bd; text-decoration: none;"><EMAIL></a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>WhatsApp al</b> <a href="https://wa.me/34960730008" style="color: #00a4bd; text-decoration: none;">+34 ***********</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>Agendar una llamada a través de</b> <a href="https://app.muaytax.com" style="color: #00a4bd; text-decoration: none;">app.muaytax.com</a> en el apartado de Soporte > Solicitar una llamada.
                        </li>
                    </ul>
                    <p>Saludos,</p>
                </div>
            """
        },
        "consultoria": {
            "message": """
                <div style="font-family: Arial, sans-serif; font-size: 14px; color: #23496d;">
                    <p style="margin: 0;">Te confirmo que hemos recibido tu pedido correctamente.</p>
                    <p style="margin-top: 10px;">A partir de ahora el departamento de <u>Consultoría de Servicios</u>, que serán tus nuevos gestores fiscales, te enviarán un correo con la información requerida para continuar con el proceso.</p>
                    <p>Para futuras comunicaciones podrás contactar mediante:</p>
                    <ul style="list-style-type: disc; padding-left: 20px; margin-top: 10px;">
                        <li style="margin-bottom: 10px;">
                            <b>Correo electrónico a</b> <a href="mailto:<EMAIL>" style="color: #00a4bd; text-decoration: none;"><EMAIL></a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>WhatsApp al</b> <a href="https://wa.me/34936940806" style="color: #00a4bd; text-decoration: none;">+34 ***********</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <b>Agendar una llamada a través de</b> <a href="https://app.muaytax.com" style="color: #00a4bd; text-decoration: none;">app.muaytax.com</a> en el apartado de Soporte > Solicitar una llamada.
                        </li>
                    </ul>
                    <p>Saludos,</p>
                </div>
            """
        }
    }

    # Excepciones por producto
    product_exceptions = {
        "consultoria": ["14042", "11488", "16878", "10501", "7598"],
        "amzvat": ["15903", "15673", "12881", "7033", "15953", "12992", "7714"],
        "gestoria": ["9510", "7360", "7334", "15685"]
    }

    # Revisar excepciones
    for platform_name, product_ids in product_exceptions.items():
        if str(product_id) in product_ids:
            return platform_messages.get(platform_name)["message"]

    # Si no es una excepción => msg = platform_messages | mensage generico por plataforma
    return platform_messages.get(platform, {}).get("message", "")
