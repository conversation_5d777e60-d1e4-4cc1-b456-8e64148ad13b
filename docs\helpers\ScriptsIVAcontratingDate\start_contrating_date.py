{
  "Cuba": {
    "IVA": "8803101444",
    "DNI": "8803101444",
    "Pasaporte": "R123456"
  },
  "España": {
    "IVA": "ESX1234567L",
    "DNI": "12345678Z",
    "Pasaporte": "AA1234567"
  },
  "Estados Unidos": {
    "IVA": "12-3456789",
    "DNI": "123-45-6789",
    "Pasaporte": "*********"
  },
  "Italia": {
    "IVA": "*************",
    "DNI": "RSSMRA85T10A562S",
    "Pasaporte": "YA1234567"
  },
  "Francia": {
    "IVA": "FR*********01",
    "DNI": "193072A012345",
    "Pasaporte": "12CV34567"
  },
  "Alemania": {
    "IVA": "DE*********",
    "DNI": "T22000129",
    "Pasaporte": "C01X00T45"
  },
  "Reino Unido": {
    "IVA": "GB*********",
    "DNI": "QQ123456C",
    "Pasaporte": "*********"
  },
  "Emiratos Árabes": {
    "IVA": "100123456700003",
    "DNI": "784-1990-1234567-1",
    "Pasaporte": "A1234567"
  },
  "Países Bajos": {
    "IVA": "NL*********B01",
    "DNI": "*********",
    "Pasaporte": "XN1234567"
  }
}


from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat

# Obtener el seller por shortname
seller = Seller.objects.get(shortname="2mventerprisellc")

# Obtener los SellerVat contratados con fecha de contratación
contracts = SellerVat.objects.filter(
    seller=seller,
    is_contracted=True,
    contracting_date__isnull=False
).values_list('vat_country__name', 'vat_number', 'contracting_date')

# Mostrar resultados
for country, vat_number, contracting_date in contracts:
    print(f"{country}: {vat_number} - Fecha de contratación: {contracting_date}")

# In: Cuba: 88031014444 - Fecha de contratación: 2023-03-10

# Actualizar las fechas 


from datetime import date
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat

seller = Seller.objects.get(shortname="2mventerprisellc")

# Fechas aleatorias posteriores a 2023-03-10 pero anteriores a hoy
fechas = [
    date(2023, 4, 1),
    date(2023, 5, 15),
    date(2023, 6, 20),
    date(2023, 7, 10),
    date(2023, 8, 25),
    date(2023, 9, 5),
    date(2023, 10, 12),
    date(2023, 11, 7)
]

# Obtener SellerVat activos sin contracting_date
contracted_vats = SellerVat.objects.filter(
    seller=seller,
    is_contracted=True
).exclude(vat_country__name="Cuba")

for i, sv in enumerate(contracted_vats):
    if i < len(fechas):
        sv.contracting_date = fechas[i]
        sv.save(update_fields=["contracting_date"])
        print(f"✅ {sv.vat_country.name} actualizado a contracting_date={fechas[i]}")
    else:
        print(f"⚠️ No hay suficientes fechas para {sv.vat_country.name}")

# add filtro del tipo de mantenimiento

from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat

# Obtener el seller
seller = Seller.objects.get(shortname="2mventerprisellc")

# Filtrar los registros contratados con fecha y tipo de mantenimiento asignados
contracts = SellerVat.objects.filter(
    seller=seller,
    is_contracted=True,
    contracting_date__isnull=False,
    maintenance_type__isnull=False
).values_list(
    'vat_country__name',
    'vat_number',
    'contracting_date',
    'maintenance_type__code'  # o maintenance_type__code si lo prefieres
)

# Mostrar resultados
for country, vat, date, maintenance_type in contracts:
    print(f"{country}: {vat} - Fecha: {date} - Tipo de mantenimiento: {maintenance_type}")

# add tipo a los paises aleatoreamente 
