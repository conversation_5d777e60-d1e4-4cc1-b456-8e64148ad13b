from datetime import datetime, timedelta
from django import forms
from django.forms.widgets import DateInput, TimeInput
from django.db.models import Q
from django.contrib.auth import get_user_model

from muaytax.app_bookings.models.absence import Absence


User = get_user_model()

class AbsenceCreateForm(forms.ModelForm):
    min_date = datetime.now() + timedelta(days=1)

    date = forms.DateField(
        widget=forms.TextInput(attrs={
            'type': 'date',
            'class': 'date-input',
            'required': 'required',
            'min': min_date.strftime('%Y-%m-%d'),
        }),
        label="Fecha de la ausencia",
    )
    start_time = forms.TimeField(
        widget=TimeInput(attrs={'type': 'time', 'class': 'time-input', 'step': '1800'}),
        label="Hora inicial de la ausencia",
        help_text="Solo se permiten intervalos de 30 minutos. Formato: 24h",
        required=False,
    )
    end_time = forms.TimeField(
        widget=TimeInput(attrs={'type': 'time', 'class': 'time-input','step': '1800'}),
        label="Hora final de la ausencia",
        help_text="Solo se permiten intervalos de 30 minutos. Formato: 24h",
        required=False,
    )
    absence_type = forms.ChoiceField(
        label="Selecciona el tipo de ausencia",
        help_text="Si deseas tener la posibilidad de crear una llamada para un vendedor, selecciona Ausencia general",
        choices= [
            ('', '------------'),
            ('general', 'Ausencia general'),
            ('bloq_schedule', 'Bloquear horario')
        ],
    )

    class Meta:
        model = Absence
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        manager_instance = kwargs.pop("manager_instance", None)
        super(AbsenceCreateForm, self).__init__(*args, **kwargs)

        if manager_instance:
            self.fields['manager'].initial = manager_instance
            self.fields['manager'].widget = forms.HiddenInput()