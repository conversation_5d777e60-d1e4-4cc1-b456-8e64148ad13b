from django.db import models
from django.db.models import F, Sum

class Movement(models.Model):

    # id -> AutoGen

    movement_number = models.CharField(
        max_length=14,
        verbose_name="Número (YYYY-MM-XXXXXX)",
    )

    status = models.ForeignKey(
        "dictionaries.MovementStatus",
        related_name="movement_status",
        on_delete=models.PROTECT,
        verbose_name="Estado",
    )

    concept = models.CharField(
        max_length=500,
        verbose_name="Concepto",
    )

    observation = models.CharField(
        blank=True,
        null=True,
        max_length=500,
        verbose_name="Observación",
    )

    amount = models.DecimalField(
        default=0,
        max_digits=15,
        decimal_places=2,
        verbose_name="Importe",
    )

    amount_euros = models.DecimalField(
        default=0,
        max_digits=15,
        decimal_places=2,
        verbose_name="Importe en Euros",
    )

    currency = models.ForeignKey(
        "dictionaries.Currency",
        default='EUR',
        related_name="movement_currency",
        on_delete=models.PROTECT,
        verbose_name="<PERSON>eda",
    )

    movement_date = models.DateField(
        verbose_name="Fe<PERSON>",
    )

    bank = models.ForeignKey(
        "banks.Bank",
        related_name="movement_bank",
        on_delete=models.PROTECT,
        verbose_name="Banco",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    @property
    def used_in_entry(self):
        used = False
        recs = self.reconciliation_movement.all()
        if (recs and recs.count() > 0):
            for rec in recs:
                if rec.used_in_entry:
                    used = True
                    break
        return used

    @property
    def reconciliations_amount(self):
        amount = 0
        try:
            amount = self.reconciliation_movement.all().aggregate(total=Sum(F('amount')))['total'] or 0
        except Exception as e:
            print(f"Error al obtener importe de reconciliaciones: {e}")
            pass
        return amount
    
    @property
    def reconciliations_quantity(self):
        qty = 0
        try:
            qty = self.reconciliation_movement.all().count() or 0
        except Exception as e:
            print(f"Error al obtener cantidad de reconciliaciones: {e}")
            pass
        return qty
    
    @property
    def reconciliations(self):
        reconciliations = []
        try:
            recs = self.reconciliation_movement.all().filter(movement = self.pk)
            recs = recs.annotate(
                invoice__reference=F('invoice__reference'),
                invoice__invoice_category=F('invoice__invoice_category'),
                invoice__is_rectifying=F('invoice__is_rectifying') or False,
            ).distinct()
            reconciliations = list(recs.values())
            # reconciliations = json.loads(recs.values().to_json(orient='records'))
        except Exception as e:
            print(f"Error al obtener reconciliaciones: {e}")
            pass
        return reconciliations
    
    class Meta:
        verbose_name = "Movimiento"
        verbose_name_plural = "Movimientos"
    
    def __str__(self):
        return self.concept


