import os
import json

from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives

from muaytax.utils.env_resources import logo_url_head_muaytax

def send_error_emailVeriFactu_to_IT(invoice, error, response=None):
    
    env_url = ''

    # Get Enviroment
    environment = os.environ.get('ENVIRONMENT', 'local')

    # Set DJANGO_SETTINGS_MODULE
    if environment == 'local':
        env_url = 'http://localhost:8000'
    elif environment == 'dev':
        env_url = 'https://dev.muaytax.com'
    else:
        env_url = 'https://app.muaytax.com'

    if isinstance(error, str):
        try:
            error = json.loads(error)
        except json.JSONDecodeError:
            print("El error no es un JSON válido, se imprimirá como string")
    
    #Parsear el response a string
    response = response.decode('utf-8') if  hasattr(response, 'decode') else response 

    try:
    
        to_email = '<EMAIL>'
        message = render_to_string("emails/verifactu/error_email_verifactu.html", {
            "error": error,
            "invoice": invoice,
            "invoice_url": f"{env_url}/sellers/{invoice.seller.shortname}/invoice/{invoice.pk}/",
            "logo_head_muaytax": logo_url_head_muaytax(),
            "xml": f"{response}"
        })

        subject = 'MUAYTAX - Error en el envío de factura a VeriFactu'
        text_content = 'Error en el envío de factura a VeriFactu'
        to_email = [to_email]
        bcc_email = ['<EMAIL>']
        reply_to = [to_email]
        from_email = to_email
        html_content = message
        tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to,
                                            bcc=bcc_email)
        tracked_email.attach_alternative(html_content, "text/html")
        resp = tracked_email.send()
    except Exception as e:
        print(f"Error sending email: {e}")
        return False

def send_tracing_emailVeriFactu_to_IT(invoice, action=None, response=None):
    
    env_url = ''

    # Get Enviroment
    environment = os.environ.get('ENVIRONMENT', 'local')

    # Set DJANGO_SETTINGS_MODULE
    if environment == 'local':
        env_url = 'http://localhost:8000'
    elif environment == 'dev':
        env_url = 'https://dev.muaytax.com'
    else:
        env_url = 'https://app.muaytax.com'

    #Parsear el response a string
    response = response.decode('utf-8') if  hasattr(response, 'decode') else response 

    try:
    
        to_email = '<EMAIL>'
        message = render_to_string("emails/verifactu/tracing_email_verifactu.html", {
            "invoice": invoice,
            "invoice_url": f"{env_url}/sellers/{invoice.seller.shortname}/invoice/{invoice.pk}/",
            "logo_head_muaytax": logo_url_head_muaytax(),
            "action": action,
            "xml": f"{response}" if response else None
        })

        subject = 'MUAYTAX - Seguimiento de factura a VeriFactu'
        text_content = 'Seguimiento de factura a VeriFactu'
        to_email = [to_email]
        bcc_email = ['<EMAIL>']
        reply_to = [to_email]
        from_email = to_email
        html_content = message
        tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to,
                                            bcc=bcc_email)
        tracked_email.attach_alternative(html_content, "text/html")
        resp = tracked_email.send()
    except Exception as e:
        print(f"Error sending email: {e}")
        return False