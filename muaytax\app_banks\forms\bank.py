import re

from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import MinLengthValidator, MinValueValidator, RegexValidator

from muaytax.app_banks.models.bank import Bank


class BankChangeForm(forms.ModelForm):
    # Validador para caracteres alfanuméricos, espacios y algunos símbolos comunes
    # Permitimos cualquier cantidad de espacios, luego haremos trim
    alphanumeric_validator = RegexValidator(
        regex=r'^[\w\s\.\,\-\(\)\/]+$',
        message="Solo se permiten caracteres alfanuméricos, espacios y algunos símbolos (.,-()/)"
    )

    bank_name = forms.CharField(
        max_length=100,
        strip=True,  # Aplicamos trim automáticamente
        validators=[
            MinLengthValidator(1, message="El nombre del banco no puede estar vacío."),
            # alphanumeric_validator se valida en clean_bank_name para asegurar que se aplica al valor correcto
        ]
    )
    bank_accounting_account = forms.CharField(
        max_length=100,
        strip=True,  # Aplicamos trim automáticamente
        validators=[
            MinLengthValidator(1, message="La cuenta contable no puede estar vacía."),
            # alphanumeric_validator se valida en clean_bank_accounting_account
        ]
    )
    bank_initial_amount = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(0, message="El monto inicial no puede ser negativo.")],
        required=False  # Manteniendo la lógica del modelo (blank=True, null=True, default=0)
    )
    bank_iban = forms.CharField(
        max_length=34,
        strip=True,  # Quita espacios al inicio/final
        required=False
        # MinLengthValidator se aplicará en clean_bank_iban después de quitar espacios internos
    )
    bank_credit_card = forms.CharField(
        max_length=18, # Longitud máxima con espacios/guiones, la validación de dígitos es 13-19
        strip=True,
        required=False,
        validators=[ # Validador básico para caracteres permitidos
            RegexValidator(
                regex=r'^[\d\s\-]+$',
                message="El número de tarjeta solo debe contener dígitos, espacios o guiones."
            )
        ]
    )

    class Meta:
        model = Bank
        exclude = ['bank_seller']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Establecer el valor inicial para bank_initial_amount si es None para evitar errores con MinValueValidator
        if self.fields['bank_initial_amount'].initial is None and not self.data.get('bank_initial_amount'):
            self.initial['bank_initial_amount'] = 0

        # Añadir placeholders informativos
        self.fields['bank_name'].widget.attrs.update({'placeholder': 'Nombre del banco'})
        self.fields['bank_accounting_account'].widget.attrs.update({'placeholder': 'Cuenta contable'})
        self.fields['bank_iban'].widget.attrs.update({'placeholder': 'IBAN'})
        self.fields['bank_credit_card'].widget.attrs.update({'placeholder': 'Número de tarjeta'})
        # No modificar self.initial si el formulario está vinculado (kwargs['data'] está presente)

    def clean_bank_name(self):
        name_from_post = self.data.get('bank_name', '')
        name_for_validation = name_from_post.strip()

        if not name_for_validation:
            raise ValidationError("El nombre del banco no puede estar vacío.")

        if not re.match(self.alphanumeric_validator.regex, name_for_validation):
            raise ValidationError(self.alphanumeric_validator.message)

        return name_for_validation

    def clean_bank_accounting_account(self):
        account_from_post = self.data.get('bank_accounting_account', '')
        account_for_validation = account_from_post.strip()

        if not account_for_validation:
            raise ValidationError("La cuenta contable no puede estar vacía.")

        if not re.match(self.alphanumeric_validator.regex, account_for_validation):
            raise ValidationError(self.alphanumeric_validator.message)

        return account_for_validation

    def clean_bank_iban(self):
        # strip=True en el campo ya quitó espacios al inicio/final.
        # Es más seguro hacer el strip explícitamente aquí.
        iban_raw = self.data.get('bank_iban', '')
        bank_account_type = self.cleaned_data.get('bank_account_type') # Asumimos que esto ya está validado

        # Primero, strip para quitar leading/trailing whitespace
        iban_externally_stripped = iban_raw.strip()
        # Quitar espacios internos para la validación y para guardar en BBDD
        iban_processed = iban_externally_stripped.replace(' ', '') if iban_externally_stripped else ''

        is_bank_account_type = bank_account_type and getattr(bank_account_type, 'code', '') == 'bankaccount'

        if is_bank_account_type:
            if not iban_processed:
                raise ValidationError("El IBAN es obligatorio para las cuentas bancarias.")

            if len(iban_processed) < 15:
                raise ValidationError("El IBAN debe tener al menos 15 caracteres (sin espacios).")

            if not (iban_processed[:2].isalpha() and len(iban_processed[:2]) == 2 and iban_processed[2:4].isdigit() and len(iban_processed[2:4]) == 2 ):
                raise ValidationError("El formato del IBAN no es válido. Debe comenzar con 2 letras de país seguidas de 2 dígitos de control.")

            if not all(c.isalnum() for c in iban_processed[4:]):
                raise ValidationError("El IBAN debe contener solo caracteres alfanuméricos después del código de país y dígitos de control.")

            return iban_processed # Devolver IBAN sin espacios internos

        # Si no es cuenta bancaria pero se proporcionó un IBAN, validarlo también
        if iban_processed: # Si el usuario escribió algo
            if len(iban_processed) < 15:
                raise ValidationError("El IBAN debe tener al menos 15 caracteres (sin espacios).")
            if not (iban_processed[:2].isalpha() and len(iban_processed[:2]) == 2 and iban_processed[2:4].isdigit() and len(iban_processed[2:4]) == 2 ):
                raise ValidationError("El formato del IBAN no es válido. Debe comenzar con 2 letras de país seguidas de 2 dígitos de control.")
            if not all(c.isalnum() for c in iban_processed[4:]):
                raise ValidationError("El IBAN debe contener solo caracteres alfanuméricos después del código de país y dígitos de control.")
            return iban_processed

        # Si es opcional, no es tipo 'bankaccount', y está vacío (después de strip y replace)
        return iban_processed # Devolverá '' si estaba vacío

    def clean_bank_credit_card(self):
        card_raw = self.data.get('bank_credit_card', '')
        bank_account_type = self.cleaned_data.get('bank_account_type')

        # Primero, strip para quitar leading/trailing whitespace
        card_externally_stripped = card_raw.strip()
        # RegexValidator en el campo ya verificó caracteres permitidos (dígitos, espacios, guiones)
        # Quitar espacios y guiones para validación de longitud y Luhn
        card_processed = card_externally_stripped.replace(' ', '').replace('-', '') if card_externally_stripped else ''

        is_credit_card_type = bank_account_type and getattr(bank_account_type, 'code', '') == 'creditcard'

        if is_credit_card_type:
            if not card_processed:
                raise ValidationError("El número de tarjeta de crédito es obligatorio para las tarjetas de crédito.")

            if not card_processed.isdigit(): # Aunque el RegexValidator de campo ayuda, esta es una comprobación final
                raise ValidationError("El número de tarjeta (sin espacios/guiones) debe contener solo dígitos.")

            if not (13 <= len(card_processed) <= 19):
                raise ValidationError("El número de tarjeta debe tener entre 13 y 19 dígitos.")

            if not self._validate_luhn(card_processed):
                raise ValidationError("El número de tarjeta no es válido según el algoritmo de verificación.")

            return card_processed # Devolver número de tarjeta sin espacios/guiones

        # Si no es tarjeta de crédito pero se proporcionó un número, validarlo
        if card_processed:
            if not card_processed.isdigit():
                raise ValidationError("El número de tarjeta (sin espacios/guiones) debe contener solo dígitos.")
            if not (13 <= len(card_processed) <= 19):
                raise ValidationError("El número de tarjeta debe tener entre 13 y 19 dígitos.")
            if not self._validate_luhn(card_processed):
                raise ValidationError("El número de tarjeta no es válido según el algoritmo de verificación.")
            return card_processed

        return card_processed # Devolverá '' si estaba vacío

    def _validate_luhn(self, card_number):
        """
        Implementación del algoritmo de Luhn para validar números de tarjeta de crédito.
        """
        if not card_number.isdigit():
            return False

        digits = [int(d) for d in card_number]
        checksum = 0

        # Recorrer dígitos de derecha a izquierda
        for i, digit in enumerate(reversed(digits)):
            # Para posiciones impares (índice par al revertir), multiplicar por 2
            if i % 2 == 1:
                digit *= 2
                if digit > 9:
                    digit -= 9
            checksum += digit

        # El número es válido si el checksum es divisible por 10
        return checksum % 10 == 0

    def clean(self):
        """
        Validación final del formulario para comprobar coherencia entre campos.
        """
        cleaned_data = super().clean()
        bank_account_type = cleaned_data.get('bank_account_type')
        bank_iban = cleaned_data.get('bank_iban')
        bank_credit_card = cleaned_data.get('bank_credit_card')

        # Verificación adicional de coherencia entre el tipo de cuenta y los datos proporcionados
        if bank_account_type:
            is_bank_account = getattr(bank_account_type, 'code', '') == 'bankaccount'
            is_credit_card = getattr(bank_account_type, 'code', '') == 'creditcard'

            if is_bank_account and bank_credit_card:
                self.add_error('bank_credit_card',
                               "No se debe proporcionar número de tarjeta para una cuenta bancaria.")

            if is_credit_card and bank_iban:
                self.add_error('bank_iban',
                               "No se debe proporcionar IBAN para una tarjeta de crédito.")

        return cleaned_data

class BankDeleteForm(forms.Form):
    class Meta:
        model = Bank
        exclude = []
