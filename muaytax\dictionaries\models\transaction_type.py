from django.db import models

class TransactionType(models.Model):

    code = models.<PERSON>r<PERSON>ield(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.Char<PERSON>ield(
        max_length=50,
        verbose_name="Descripción",
    )

    category = models.Char<PERSON>ield(
        blank=True,
        null=True,
        max_length=10,
        verbose_name="Categoría"
    )

    class Meta:
        verbose_name = "Tipo de transacción"
        verbose_name_plural = "Tipo de transacciones"
    
    def __str__(self):
        return self.description
    
# @admin.register(TransactionType)
# class TransactionTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "category"]
#     search_fields = ["code", "description", "category"]
