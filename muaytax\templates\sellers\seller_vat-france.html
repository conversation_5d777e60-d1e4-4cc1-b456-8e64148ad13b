{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
{{ country.name | title }}
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/all/all.min-v5.15.4.css"> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/loading.css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {     
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }


      .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }

      .dataTables_filter {
        display: none;
      }

      .centerIcon {
        display: flex;
        justify-content: center;
      }  
  
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">IVA {{ country.name | title }}</h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
          </li>
          <li class="breadcrumb-item">
            <a href=".">IVA {{ country.name | title }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
        <div class="dt-responsive">        
          <div class="row d-flex mt-3 mb-4">
            <!-- Search + Pagination -->
            <div class="col-4 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()" />
              </div>
            </div> 
            <!-- Search + Pagination -->

            <div class="col">
              <select class="form-select form-control" name="period-input" id="period" onchange="onChangePeriodYear()">
                <option value="Q1">Trimestre 1 </option>
                <option value="Q2">Trimestre 2 </option>
                <option value="Q3">Trimestre 3 </option>
                <option value="Q4">Trimestre 4 </option>
                <option value="01">Enero</option>
                <option value="02">Febrero</option>
                <option value="03">Marzo</option>
                <option value="04">Abril</option>
                <option value="05">Mayo</option>
                <option value="06">Junio</option>
                <option value="07">Julio</option>
                <option value="08">Agosto</option>
                <option value="09">Septiembre</option>
                <option value="10">Octubre</option>
                <option value="11">Noviembre</option>
                <option value="12">Diciembre</option>
                <option value="0">Todos los meses</option>
              </select>
            </div>

            <div class="col">
              <select class="form-select form-control" name="period-input" id="year" onchange="onChangePeriodYear()">
                <option value="2022">2022</option>
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025">2025</option>
              </select>
            </div>

            <div class="col">
              <select class="form-select form-control"  id="manager_assigned" onchange="manager_assigned();" disabled>
                <option value="">Mostrar Todos</option>
                {% for manager in managers %}
                <option value="{{ manager.pk }}">{{ manager.name }}</option>
                {% endfor %}
              </select>
            </div>
            
            <div class="col-auto d-flex  align-items-center">
                <button id="showcolumns1" class="btn btn-secondary m-0"  data-bs-toggle="tooltip" data-bs-placement="top" title="Ver/Ocultar TXT" onclick="onClickButtonTxT()">
                  <i class="fa-regular fa-xl fa-eye"></i>
                  <b>TXT</b>
                </button>
            </div>

            <div class="col-auto d-flex  align-items-center">
                <button id="downloadExcel" class="btn btn-success m-0"  data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar Excel" onclick="donwloadExcel()">
                  <i class="fa-regular fa-xl fa-file-excel"></i>
                  <b>Reporte Excel</b>
                </button>
            </div>
          </div>

          <!-- Total pending invoices -->
          <div class="row mt-3">
            <div class="col-3">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL FACTURAS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted"><b id="total_invoices_count">&nbsp</b></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div> 
          
          <table id="seller-list-table2" class="table nowrap table-hover">
            <thead class="table-head">
              <tr>
                <th>Nombre</th>
                <th>Pendientes</th>
                <th>Enero</th>
                <th>Febrero</th>
                <th>Marzo</th>
                <th>Abril</th>
                <th>Mayo</th>
                <th>Junio</th>
                <th>Julio</th>
                <th>Agosto</th>
                <th>Septiembre</th>
                <th>Octubre</th>
                <th>Noviembre</th>
                <th>Diciembre</th>
                <th>Último acceso</th>
                <th style="width:5%;">Acciones</th>
                <th>Gestor Asignado</th>
              </tr>
            </thead>
            <tbody>
              
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- modal for showing loading -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header justify-content-center">
            <h4 class="mt-2">Preparando reporte</h4>
          </div>
          <div class="modal-body">
            <div id="spinner-animation"
                class="d-flex justify-content-center align-items-center text-center mb-3 d-none">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <div id="folder-animation" class="folder-animation-wrapper mb-3">
              <div class="file-animation file-logo">
                <div class="page page1">
                  <p class="mt-1" style="font-size: 12px;">Factura 1</p>
                </div>
                <div class="page page2">
                  <p class="mt-1" style="font-size: 12px;">Factura 2</p>
                </div>
                <div class="page page3">
                  <p class="mt-1" style="font-size: 12px;">Factura 3</p>
                </div>
              </div>
            </div>
            <div class="d-flex-column justify-content-center align-items-center text-center">
              <h4 id="textDownloadingAnimation" class="mb-2">Generando Excel...</h4>
              <p style="font-size: 16px;">Por favor, no cierres ni recargues la página</p>
            </div>

          </div>

        </div>
      </div>
    </div>
    <!-- modal for showing loading -->
</div>
{% endblock content %}

{% block javascripts %}

<!-- JQUERY DATATABLES -->
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
<!-- sweet alert Js -->
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

<script src="{% static 'assets/js/loading.js' %}"></script>
<script>
    let showTXT = false;

    let dataTable = null;

    const debug = true;
      const ajaxData=(d)=>{
        if(debug) console.log('ajax ', d);
          let tParams = "";
          let year = document.getElementById("year").value;
          let period = document.getElementById("period").value;
          if(year){
            if (debug) console.log('filterDT | year: ', year);
            d.year = year
            tParams += "&year=" + year;
          }
          if(period){
            if (debug) console.log('filterDT | period: ', period);
            d.period = period
            tParams += "&period=" + period;
          }

        getTotals(tParams);
        return d
      }

    const getTotals = (params) => {
      let p = params;

      if (!p || p == undefined || p == null || p == "") {
        p = "";
      } else if (p.charAt(0) == "&") {
        p[0] = "?";
      }
    }

    $(document).ready(function() {
        let cookie;
        let perm_user;
        if("{{perms.sellers.view_seller}}"=="True"){
          perm_user = true;
        }else{
          perm_user = false;
        }

        dataTable = $("#seller-list-table2").DataTable({
            "serverSide":false,
                "ajax":{
                    "dataSrc":"data",
                    "url":"{% url 'app_sellers:seller_vatcountry_list_dt' country.iso_code %}",
                    "data":function( d ){
                      ajaxData(d);
                      
                      }
                },
                "language": {
                      "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
                      "lengthMenu": "_MENU_",
                      "zeroRecords": "No se han encontrado vendedores.",
                      "info": "_START_ a _END_ de un total de _TOTAL_",
                      "search": "Buscar:",
                      "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                      "infoFiltered": ""
                    },
                "createdRow": function(row, data, dataIndex) {
                },
                "columns":[
                    {"data": "user_name",
                    "className":"head_name",
                    "render": function(data, type, row) {
                      let html = '';
                      html += '<td class="align-middle">';
                    html += '<div class="d-inline-block">';
                    html += '<h6 class="m-b-0"><b>';
                    
                    let name = row.seller_name;  
                    if (typeof name === 'string') {  
                        const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l','sl.']; 
                          const words = row.seller_name.split(' ').map(function(word) {
                              const lowerWord = word.toLowerCase();
                              if (lowerCaseSuffixes.includes(lowerWord)) {
                                  return word.toUpperCase();
                              } else {
                                  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                              }
                        });
                      html += words.join(' ');
                    }
                    html += '</b>';
                    if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                        html += ' - ' + row.user_name.split(' ').map(function(word) {
                            return word.charAt(0).toUpperCase() + word.slice(1);
                        }).join(' ');
                    }  
                    
                    html += '</h6>';
                    html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                    html += '</div>';
                    html += '</td>';
                    
                    return html;}
                    },
                    {"data": "num_pending_invoices",
                      "className":"pendings",
                      "render": function(data, type, row) {
                        if (data && (type === 'display' || type === 'filter')) {
                          let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                          html += row.num_pending_invoices ;
                          html += '</td>';
                          return html;
                        }
                        return data;
                      }
                  }, 
                  {"data": "month_1",
                     "className": "txt Q1 01 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month1 = row.month_1; 
                        if (month1 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'1\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>';
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },            
                  },
                  {"data": "month_2",
                   "className": "txt Q1 02 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month2 = row.month_2; 
                        if (month2 === true) {
                            html += '<span class="centerIcon"  data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'2\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }       

                  },
                  {"data": "month_3",
                   "className": "txt Q1 03 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month3 = row.month_3; 
                        if (month3 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'3\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }
                  },
                  {"data": "month_4",
                    "className": "txt Q2 04 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month4 = row.month_4; 
                        if (month4 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'4\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>'; 
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }      
                  },
                  {"data": "month_5",
                    "className": "txt Q2 05 0",
                      "render": function(data, type, row) {
                            let html = '';
                            const month5 = row.month_5; 
                          if (month5 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'5\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                          } else {
                              html += '&nbsp';
                          }
                              
                              return html;
                        },      
                    },
                  {"data": "month_6",
                    "className": "txt Q2 06 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month6 = row.month_6; 
                        if (month6 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'6\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      } 
                  },
                  {"data": "month_7",
                    "className": "txt Q3 07 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month7 = row.month_7; 
                        if (month7 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'7\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "month_8",
                  "className": "txt Q3 08 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month8 = row.month_8; 
                        if (month8 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'8\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },      
                  },
                  {"data": "month_9",
                    "className": "txt Q3 09 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month9 = row.month_9; 
                        if (month9 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'9\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },      
                  },
                  {"data": "month_10",
                    "className": "txt Q4 10 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month10 = row.month_10; 
                        if (month10 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'10\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "month_11",
                    "className": "txt Q4 11 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month11 = row.month_11; 
                        if (month11 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'11\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "month_12",
                    "className": "txt Q4 12 0",
                    "render": function(data, type, row) {
                          let html = '';
                          const month12 = row.month_12; 
                        if (month12 === true) {
                            html += '<span class="centerIcon" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar TXT Amazon">';
                            html += '<a href="#" onclick="donwloadTXT(' + row.id + ', \'12\' );" ><i class="fa-solid fa-file-arrow-down fa-xl" style="color: #5957b0;"></i></a>'
                            html += '</span>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "last_login",
                    "className":"login",
                    "render": function(data, type, row) {
                      if (data && (type === 'display' || type === 'filter')) {
                        const date = new Date(data);

                        const day = date.getDate().toString().padStart(2, '0');
                        const month = date.toLocaleString('default', { month: 'short' });
                        const year = date.getFullYear();
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');

                        const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;
                        
                        return formattedDate;
                    }
                    return data; // Para otros tipos, como 'sort'
                  }
                  },
                  {"data": null,
                    'visible': perm_user,
                    "className":"actions",
                    "orderable":false,
                    "render":function(data, type, row) {
                      let html = '<td class="align-middle text-center">';
                      html += '<a href="/sellers/' + row.seller_shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                      html += '<i class="feather icon-edit"></i>';
                      html += '</a>';
                      html += '</td>';
                      html = '<div style="text-align: center;">' + html + '</div>';    
                      return html;
                    },
                  
                  },
                  {"data": "manager_assigned",
                    'visible': false,
                  }
                ],
                "order": [[2, "asc"],[3, "asc"],[0, "asc"]],
                "paging": true,
                "searching":true,
                "lengthChange":false,
                "lengthMenu":[[100,150,200,-1], [100,150,200, 'Todos']],
                "createdRow": function(row, data, dataIndex) {
                  if(perm_user){
                    const excludedCells = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
                    const link = `/sellers/${data.seller_shortname}/`;
                    $(row).css('cursor', 'pointer').on('click', 'td', function (event) {
                      const clickedCellIndex = $(this).index();
                      if (!excludedCells.includes(clickedCellIndex)) {
                        window.location.href = link;
                      }
                    });
                  }
                },
                
                "initComplete": function(settings, json) {
                  filterManager();
                 dataTable.settings()[0].nTBody.style.width = "100%";
                 dataTable.settings()[0].nTable.style.width = "100%";
                 dataTable.settings()[0].nTHead.style.width = "100%";
                 total_invoices_count =json.total_invoices;
                 document.getElementById("total_invoices_count").textContent = total_invoices_count;
                 console.log("Tabla:", settings)
                  cookie = getCookie("allsellersvat_show_txt");
                  const period = "{{period}}";
                  if(cookie == true || cookie == "true"){
                    $('th.txt, td.txt').hide();
                    $(`th.txt.${period}, td.txt.${period}`).show();

                  }else{
                      $('th.txt, td.txt').hide();
                  }
            
       
              },
              "drawCallback": function(settings) {
                  $('[data-bs-toggle="tooltip"]').tooltip();
                  console.log("Tabla redibujada:", settings);
                  dataTable.settings()[0].nTable.style.width = "100%";
                  cookie = getCookie("allsellersvat_show_txt");
                  const period = "{{period}}";

                  if(cookie == true || cookie == "true"){
                    $('th.txt, td.txt').hide();
                    $(`th.txt.${period}, td.txt.${period}`).show();

                  }else{
                      $('th.txt, td.txt').hide();
                  }
                  }
        
                
        });

     });

    function search() {
      var tipo = $("#search").val();
      dataTable.column(0).search(tipo).draw();
    }

    function filtrar() {
      var tipo = $("#tipo").val();
      dataTable.column(1).search(tipo).draw();
    }

    function manager_assigned() {
      var tipo = $("#manager_assigned").val();
      dataTable.column(16).search(tipo).draw();
    }

    const onChangePeriodYear =()=>{
      const period = document.getElementById('period');
      const year = document.getElementById('year');
      const urlembed= "{% url 'app_sellers:vat' country.iso_code %}";
      const newUrl= urlembed + '?period=' + period.value + '&year=' + year.value;
      window.location.href = newUrl;
    }

    const onload =()=>{
      const period = document.getElementById('period');
      const year = document.getElementById('year');
      period.value = '{{period}}';
      year.value = '{{year}}';

    }

    const createCookie = (name, value) => {
      document.cookie = name + "=" + value + "; path=/";
    }

    const getCookie = (name) => {
      const cookieValue = document.cookie.match("(^|;)\\s*" + name + "\\s*=\\s*([^;]+)");
      return cookieValue ? cookieValue.pop() : "";
    }

    
    const onClickButtonTxT = () => {
        const period = "{{period}}";
        if (showTXT == false) {
          $('th.txt, td.txt, div.txt').hide();
          $(`th.txt.${period}, td.txt.${period}, div.txt.${period}`).show();
          showTXT = true;
        } else {
          $('th.txt, td.txt, div.txt').hide();
          showTXT = false;
        }
        
        createCookie("allsellersvat_show_txt", showTXT);
    }

    const cookie = getCookie("allsellersvat_show_txt");
        if(cookie == true || cookie == "true"){
          onClickButtonTxT();
        }  

    onload();

    const donwloadTXT = async (id_seller, month)=> {
      let year=$('#year').val();
      let url = `/AmazonTxtEur/download/${id_seller}/${year}/${month}/`

      // Show loading message
      Swal.fire({
        toast: true,
        title: 'Descargando TXT de AMZ...',
        text: 'Por favor, espere un momento.',
        icon: 'info',
        position: 'top-end',
        showConfirmButton: false,
        timerProgressBar: true,
        didOpen: () => {
            Swal.showLoading()
        },
        allowOutsideClick: false, 
        allowEscapeKey: false, 
        allowEnterKey: false 
      });


      const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        // stop timer when hover
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      });

      try {
        const response = await fetch(url, {
          method: 'GET',
        });

        if (response.status == 200) {
          const data = await response.text();
          const link = document.createElement('a');

          link.href = data;
          link.download = data; 
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          Toast.fire({
            icon: 'success',
            title: 'TXT de AMZ descargado correctamente'
          })
        }else if(response.status == 404){
          Toast.fire({
            icon: 'error',
            title: 'No se encuentra el TXT.'
          })
        } else {
          Toast.fire({
            icon: 'error',
            title: 'Error al descargar el TXT. Intenta más tarde'
          })
        }

      } catch (error) {
        Toast.fire({
          icon: 'error',
          title: error
        })
      }
    }

    const donwloadExcel = async  ()=>{
      let year=$('#year').val();
      let period=$('#period').val();
      let manager_assigned=$('#manager_assigned').val();
      let search=$('#search').val();
      let jsonData = JSON.stringify({year: year, period: period, manager_assigned: manager_assigned, search: search});

      const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            didOpen: (toast) => {
              toast.addEventListener('mouseenter', Swal.stopTimer)
              toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
          });

      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static' 
      });
      modalLoading.show();

      try{
        const response = await fetch("{% url 'app_invoices:vat_FR_list_download_cvs' %}", {
          method: 'POST',
          headers: {
          'X-CSRFToken': "{{ csrf_token }}"
        },
        body: jsonData
        });

        if (response.status == 200){
          modalLoading.hide();
          const blob = await response.blob();
          const blobUrl = window.URL.createObjectURL(blob)
          const link = document.createElement('a');
          link.href = blobUrl
          link.setAttribute('download', `reporte_IVA_Francia.xlsx`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          Toast.fire({
          icon: 'success',
          title: response.headers.get('X-Message')
          });
        }else if (response.status == 204){
          Toast.fire({
          icon: 'error',
          title: response.headers.get('X-Message')
          });
        }else{
          Toast.fire({
              icon: 'error',
              title: 'Error al generar el reporte'
          })
        }

      }catch (error) {
        Toast.fire({
          icon: 'error',
          title: error
        })
      }
      

    }

    const filterManager = ()=>{
      const super_user = "{{perms.users.is_superuserAPP}}"
      if (super_user == 'False'){
        $('#manager_assigned').val("{{current_user_id}}")
        manager_assigned();
      }else{
         $('#manager_assigned').attr('disabled', false);
      }
    }


</script>
<!-- JQUERY DATATABLES -->



{% endblock javascripts %}