{% extends "layouts/base.html" %}
{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
{% block stylesheets %}
	<!-- jQ<PERSON>y debe ser cargado aqui para que este disponibe -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>

	<!-- Fuentes e iconos -->
	<link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css" type="text/css"/>
	<link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">

	<!-- Estilos de frameworks -->
	<link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/datatables/datatable/2.0.7/css/dataTables.bootstrap5.min.css" type="text/css"/>
	<link rel="stylesheet" href="{{ STATIC_URL }}assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css"/>
	<link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">

	<!-- Estilos personalizados de la aplicación -->
	<link rel="stylesheet" href="{% static 'assets/css/skeleton.css' %}">

	<!-- Estilos inline -->
	<style>
		.truncate-text-sidebar {
		display: inline-block;
		width: calc(100% - 150px);
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		}
		.truncate-text-sidebar-link{
		width: calc(100% - 25px);
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		position: relative!important;
		}
		.contracted-service-card .card-header::after {
		content: "";
		background-color: #222529;
		position: absolute;
		left: 0;
		top: 20px;
		width: 4px;
		height: 20px;
		}
		.certificated-badge{
		position: absolute;
		bottom: -5px;
		right: -5px;
		border-radius: 50%;
		width: 30px;
		height: 30px;
		background: #fff;
		padding: 5px 3px;
		}
		.badge-success-lighten{
		color: #0acf97;
		background-color: rgba(10, 207, 151, .18);
		}
		.badge-danger-lighten{
		color: #fa5c7c;
		background-color: rgba(250, 92, 124, .18);
		}

		.add-service-button {
		background-color: #38ac6a;
		}
		.add-service-button:hover {
		background-color: #38ac6ab8;
		border: 1px solid #0d5782;
		}
		.shake-invalid {
		animation: shake 0.2s ease-in-out;
		}

		/* Shake animation */
		@keyframes shake {
		0% {
			transform: translateX(0);
		}
		25% {
			transform: translateX(-5px);
		}
		50% {
			transform: translateX(5px);
		}
		75% {
			transform: translateX(-5px);
		}
		100% {
			transform: translateX(0);
		}
		}

		/* hide icons on date and time inputs */
		input[type="date"]::-webkit-calendar-picker-indicator {
		display: none;
		}

		input[type="date"]::-webkit-inner-spin-button {
		display: none;
		}

		input[type="date"]::-webkit-clear-button {
		display: none;
		}
		/* hide icons on date and time inputs */

		.vat-country-card-text-group > *{
		min-width: fit-content;
		}

		/* datatable start */
		.custom-table-head {
		background: #748892;
		color: #fff;
		}
		div.dt-container {
		overflow: auto;
		}
		/* datatable ends */

		/* Spinner personalizado */
		.custom-spinner {
			border: 4px solid rgba(0, 0, 0, 0.1);
			border-left-color: #3498db;
			border-radius: 50%;
			width: 40px;
			height: 40px;
			animation: spin 1s linear infinite;
			margin: 0 auto;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}
			100% {
				transform: rotate(360deg);
			}
		}

		/* Estilo adicional para el texto debajo del spinner */
		#loading-indicator-vat p {
			font-size: 16px;
			color: #3498db;
			margin-top: 10px;
		}
	</style>
{% endblock stylesheets %}
{% block title %}Información Fiscal{% endblock title %}
{% block breadcrumb %}
	<div class="page-header">
		<div class="page-block">
		<div class="row align-items-center">
			<div class="col-md-12">
			<div class="page-header-title d-flex align-items-center">
				<h5>
				<a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
				{{ object.name }}
				( {{ object.user.email }} )
				</h5>
			</div>
			<ul class="breadcrumb mt-3">
				<li class="breadcrumb-item">
				<a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
				</li>
				<li class="breadcrumb-item">
				<a href="{% url 'app_sellers:list' %}">Vendedores</a>
				</li>
				<li class="breadcrumb-item">
				<a href="{% url 'app_sellers:summary' object.shortname %}">
					{{ object.name }}
				</a>
				</li>
				<li class="breadcrumb-item">
				<a href="{% url 'app_sellers:information' object.shortname %}">Información Fiscal</a>
				</li>
			</ul>
			</div>
		</div>
		</div>
	</div>
{% endblock breadcrumb %}
{% block content %}
	<div class="row">
		<div class="col-lg-4">
			<div class="card user-card user-card-1">
				<div class="card-body pb-0">
					<div class="float-end">
						<span 
							role="button"
							id="toggle-status-icon"
							onclick="toggleSellerStatus()" 
							class="d-flex align-items-center"
							data-bs-toggle="tooltip"
							title="Cambiar estado"
						>
							{% if not object.is_inactive %}
							<span class="status-indicator" style="background-color: green; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px;"></span> Alta
							{% else %}
							<span class="status-indicator" style="background-color: red; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px;"></span> Baja
							{% endif %}
						</span>
					</div>
					<div class="media user-about-block align-items-center mt-0 mb-3">
					<div class="position-relative d-inline-block">
						<img 
						class="img-radius img-fluid wid-80"
						{% if object.logo %}
							src="{{ object.logo.url }}"
						{% elif object.gender and object.gender == 'F' %}
							src="{% static 'assets/images/user/avatar-5.jpg' %}"
						{% else %}
							src="{% static 'assets/images/user/avatar-2.jpg' %}"
						{% endif %}
						alt="User image"
						>   
					</div>
					<div class="media-body ms-3">
						<h6 class="mb-1">
							{{ object.name }} 
						</h6>
						<p class="mb-0 text-muted">
							{{ object.user.email }}
						</p>
						<p class="mb-0 text-muted">
							{{ object.get_legal_entity_display }}
						</p>
						<!-- <i class="feather icon-edit text-black" data-bs-toggle="modal" data-bs-target="#exampleModal" style="cursor: pointer;"></i> -->
					</div>
					</div>
				</div>
				<!-- Modal para Confirmación de Baja -->
				<div 
					class="modal fade" 
					id="confirmDeactivateModal" 
					tabindex="-1" 
					aria-labelledby="confirmDeactivateModalLabel" 
					aria-modal="true" 
					role="dialog" 
					data-bs-backdrop="static" 
					data-bs-keyboard="false"
				>
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<div class="modal-header bg-dark">
								<h5 class="modal-title text-white" id="confirmDeactivateModalLabel">Confirmar Baja</h5>
								<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
							</div>
							<div class="modal-body text-center">
								<!-- Spinner de carga personalizado (oculto por defecto) -->
								<div id="loading-indicator-deactivate" class="d-none">
									<div class="custom-spinner"></div>
									<p>Procesando...</p>
								</div>
								<!-- Contenido del mensaje de confirmación -->
								<div id="modal-body-content">
									<p>Va a proceder a dar de baja a <strong>{{ object.shortname }}</strong>. Ten en cuenta que esto pondrá la fecha de baja a día de hoy a todos los servicios que estén contratados y tengan una fecha de alta sin fecha de baja.</p>
								</div>
							</div>
							<div class="modal-footer">
								<button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancel-button">Cancelar</button>
								<button type="button" class="btn btn-danger" onclick="deactivateSeller()" id="confirm-button">Confirmar Baja</button>
							</div>
						</div>
					</div>
				</div>

				<ul class="list-group list-group-flush">
					<li class="list-group-item">
						<span class="f-w-500 truncate-text-sidebar">
							<i class="feather icon-map-pin m-r-10"></i>
							País de registro
						</span>
						<span class="float-end">
							{{ object.country_registration.name|default:"No tiene" }} &nbsp;
							{% if object.country_registration %}
								<img 
									class="wid-20"
									src="{% static 'assets/images/flags/' %}{{ object.country_registration.iso_code|lower }}.svg" 
									alt="{{ object.country_registration.name }}"
								>
							{% endif %}
						</span>
					</li>
					<li class="list-group-item">
						<span class="f-w-500 truncate-text-sidebar" title="Número de IVA en el país de registro">
							<i class="fa-regular fa-id-card m-r-10"></i>Número de IVA en el país de registro
						</span>
						<span class="float-end">
							<div class="d-flex justify-content-end gap-1">
								<span id="info_1">{{ object.nif_registration|default:"" }}</span>
								{% if object.nif_registration %}
									<i
										class="mdi mdi-content-copy text-primary" 
										role="button"
										data-bs-toggle="tooltip" 
										data-bs-placement="top"
										data-original-title="Copiar" 
										data-bs-title="Copiar"
										data-clipboard="true" 
										data-clipboard-target="#info_1"
										data-copied-tip="Copiado!" 
										data-original-tip="Copiar"
									></i>
								{% endif %}
							</div>
						</span>
					</li>
					<li class="list-group-item">
						<span class="f-w-500 truncate-text-sidebar">
							<i class="fas fa-money-check-alt m-r-10"></i>
							EORI
						</span>
						<span class="float-end">
							<div class="d-flex justify-content-end gap-1">
								<span id="info_2">{{ object.eori }}</span>
								{% if object.eori %}
									<i
										class="mdi mdi-content-copy text-primary" 
										role="button"
										data-bs-toggle="tooltip" 
										data-bs-placement="top"
										data-original-title="Copiar" 
										data-bs-title="Copiar"
										data-clipboard="true" 
										data-clipboard-target="#info_2"
										data-copied-tip="Copiado!" 
										data-original-tip="Copiar"
									></i>
								{% endif %}
							</div>
						</span>
					</li>
				</ul>
				<div class="card-body">
					<br>    
				</div>
				<div class="nav flex-column nav-pills list-group list-group-flush list-pills" id="user-set-tab" role="tablist" aria-orientation="vertical">
					<a class="nav-link list-group-item list-group-item-action active" id="user-set-profile-tab" data-bs-toggle="pill" href="#user-set-profile" role="tab" aria-controls="user-set-profile" aria-selected="true">
						<span class="f-w-500"><i class="feather icon-file-text m-r-10 h5"></i>Información Fiscal</span>
						<span class="float-end"><i class="feather icon-chevron-right"></i></span>
					</a>
					<a class="nav-link list-group-item list-group-item-action" id="user-set-activities-tab" data-bs-toggle="pill" href="#user-set-activities" role="tab" aria-controls="user-set-activities" aria-selected="false">
						<span class="f-w-500 truncate-text-sidebar-link"><i class="fas fa-calculator m-r-10 h5 mb-0"></i>Actividades</span>
						<span class="float-end"><i class="feather icon-chevron-right"></i></span>
					</a>
					<a class="nav-link list-group-item list-group-item-action" id="user-set-services-tab" data-bs-toggle="pill" href="#user-set-services" role="tab" aria-controls="user-set-services" aria-selected="false">
						<span class="f-w-500 truncate-text-sidebar-link"><i class="fa-solid fa-file-signature m-r-10 h5 mb-0"></i>Servicios contratados</span>
						<span class="float-end"><i class="feather icon-chevron-right"></i></span>
					</a>
					<a class="nav-link list-group-item list-group-item-action" id="user-set-annot-tab" data-bs-toggle="pill" href="#user-set-annot" role="tab" aria-controls="user-set-annot" aria-selected="false">
						<span class="f-w-500 truncate-text-sidebar-link"><i class="fas fa-pencil-alt m-r-10 h5 mb-0"></i>Anotaciones</span>
						<span class="float-end"><i class="feather icon-chevron-right"></i></span>
					</a>
					<a class="nav-link list-group-item list-group-item-action" id="user-set-vat-tab" data-bs-toggle="pill" href="#user-set-vat" role="tab" aria-controls="user-set-vat" aria-selected="false">
						<span class="f-w-500 truncate-text-sidebar-link"><i class="fas fa-list-ul m-r-10 h5 mb-0"></i>Ver países IVA</span>
						<span class="float-end"><i class="feather icon-chevron-right"></i></span>
					</a>
				</div>
			</div>
		</div>
		<div class="col-lg-8">
			<div class="tab-content bg-transparent p-0 shadow-none" id="user-set-tabContent">
				<div class="tab-pane fade show active" id="user-set-profile" role="tabpanel" aria-labelledby="user-set-profile-tab">
					{% include 'sellers/include/fiscal_information/information.html' %}
				</div>
				<div class="tab-pane fade" id="user-set-activities" role="tabpanel" aria-labelledby="user-set-activities-tab">
					{% include 'sellers/include/fiscal_information/activities.html' %}
				</div>
				<div class="tab-pane fade" id="user-set-services" role="tabpanel" aria-labelledby="user-set-services-tab">
					{% include 'sellers/include/fiscal_information/services.html' %}
				</div>
				<div class="tab-pane fade" id="user-set-vat" role="tabpanel" aria-labelledby="user-set-vat-tab">
					{% include 'sellers/include/fiscal_information/vat-card-list.html' %}
				</div>
				<div class="tab-pane fade" id="user-set-annot" role="tabpanel" aria-labelledby="user-set-annot-tab">
					{% include 'sellers/include/fiscal_information/annotations.html' %}
				</div>
			</div>
		</div>
	</div>
{% endblock content %}
{% block javascripts %}
	<!-- Librerías esenciales -->
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/htmx/htmx.min-v1.6.0.js"></script>

    <!-- Plugins (DataTables, Datepicker, SweetAlert2, ClipboardJS) -->
    <script src="{{ STATIC_URL }}assets/datatables/datatable/2.0.7/js/dataTables.js"></script>
    <script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>
    <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
    <script src="{% static 'assets/js/plugins/clipboard.min.js' %}"></script>
	<!-- start DEBUG -->
	<script type="text/javascript">
		const debug = {{ debug|yesno:"true,false" }};
		// Función para debug (imprime en consola solo si debug está habilitado)
		function debugLog(...args) {
		  if (debug) {
			console.log(...args);
		  }
		}
		debugLog("Debug mode is enabled")
	</script>
	<!-- end DEBUG -->
	<script>
		//   --------------------
		//    Variables Globales 
		//   --------------------

		const Toast = Swal.mixin({
			toast: true,
			position: 'top-end',
			showConfirmButton: false,
			timer: 3000,
			timerProgressBar: true,
			didOpen: (toast) => {
				toast.addEventListener('mouseenter', Swal.stopTimer);
				toast.addEventListener('mouseleave', Swal.resumeTimer);
			}
		});
		const datepickerInstances = new Map();

		//   -------------------------- 
		//    Declaración de funciones 
		//   --------------------------

		// Inicializae todos los datepickers enlos inputs de tipo "date".
		function initializeDatePicker() {
			datepickerInstances.forEach((instance, element) => {
				instance.destroy();
			});
			datepickerInstances.clear();
			const datepickerElements = document.querySelectorAll('input[type="date"]');
			datepickerElements.forEach(element => {
				if (!element || !(element instanceof HTMLElement)) {
					console.warn(`No se pudo inicializar el DatePicker: el elemento no es válido`, element);
					return;
				}
				const datePickerInput = new Datepicker(element, {
					buttonClass: 'btn btn-outline-secondary',
					format: 'yyyy-mm-dd',
					disableTouchKeyboard: true,
					closeOnSelect: true,
					orientation: 'bottom',
					showOnFocus: true,
					autohide: true,
					todayBtn: true,
					todayHighlight: true,
					maxDate: new Date(2100, 11, 31),
					weekStart: 1,
				});
				datepickerInstances.set(element, datePickerInput);
			});
		}

		// Controlar el comportamiento de la tecla enter en los formularios
		function pressEnterBehaviour(form){
			const inputs = form.querySelectorAll('input');
			inputs.forEach((input) => {
				input.addEventListener('keypress', (e) => {
					if (e.key === 'Enter') {
						e.preventDefault();
						let index = Array.from(inputs).indexOf(input);

						let nextIndex = index
						do{
							nextIndex = (nextIndex + 1) % inputs.length;
						} while (inputs[nextIndex].readOnly);

						const nextInput = inputs[nextIndex];
						nextInput.focus();
						if (nextInput.type === 'text') {
							const valueLength = nextInput.value.length;
							nextInput.setSelectionRange(valueLength, valueLength);
						}
					}
				});
			});

		}

		// Alternar entre los estados de "Editar" y "Cancelar" en un boton de edicion
		function toggleButtonInfo(button) {
			const collapse = button.getAttribute('data-bs-target');
			const icon = button.querySelector('i');
			let infoEditTooltip = bootstrap.Tooltip.getInstance(button);
			// infoEditTooltip.hide();

			const isEditMode = icon.classList.contains('icon-edit');
			const newTooltipTitle = isEditMode ? 'Cancelar' : 'Editar';
			const newButtonClass = isEditMode ? 'btn-danger' : 'btn-primary';
			const oldButtonClass = isEditMode ? 'btn-primary' : 'btn-danger';
			const newIconClass = isEditMode ? 'icon-x' : 'icon-edit';
			const oldIconClass = isEditMode ? 'icon-edit' : 'icon-x';

			button.classList.remove(oldButtonClass);
			button.classList.add(newButtonClass);
			icon.classList.remove(oldIconClass);
			icon.classList.add(newIconClass);
		}

		// Habilitar y deshabilitar botones de un modal (enviar y cerrar)
		function toggleModalButtons(isDisabled, modalId, submitButtonId) {
			const modal = document.querySelector(modalId);
			if (!modal) return;
		
			const submitButton = modal.querySelector(submitButtonId);
			const closeButton = modal.querySelector('.btn-close');
		
			if (submitButton) submitButton.disabled = isDisabled;
			if (closeButton) closeButton.disabled = isDisabled;
		}
		
		// Elimina una DataTable si ya está inicializada en un elemento dado.
		function destroyDataTable(tableId){
			if ($.fn.DataTable.isDataTable(tableId)) {
				$(tableId).DataTable().destroy();
			}
		}

		// Inicializa una DataTable con configuraciones de idioma y responsividad.
		function renderDataTable(table){
			if ($.fn.DataTable.isDataTable(table.id)) {
				$('#services-vat-country-list').DataTable().destroy();
			}
			const dataTableOptions = {
				responsive: true,
				language: {
					"info": `Mostrando _START_ a _END_ de _TOTAL_ ${table.type}`,
					"infoEmpty": "No hay registros que mostrar",
					"infoFiltered": "",
					"lengthMenu": "_MENU_ registros por página",
					"emptyTable": `No hay ${table.type} contratados`,
					"paginate": {
						"first": "Primera página",
						"last": "Última página",
					},
					"loadingRecords": "Cargando...",
					"search": "Buscar:",
				},
			};
			const vatCountryDataTable = new DataTable (table.id, dataTableOptions);
		}

		// Alterna el estado de un vendedor entre "Alta" y "Baja", mostrando un modal de confirmación.
		function toggleSellerStatus() {
			const toggleStatusUrl = "{% url 'app_sellers:toggle_status' object.shortname %}";
			
			// Verificar el estado actual basado en el texto del elemento
			const isInactive = document.getElementById("toggle-status-icon").innerText.trim() === "Alta";
			
			if (isInactive) {
				// Mostrar el modal de confirmación de baja
				$('#confirmDeactivateModal').modal('show');
			
				// Cambiar el texto del modal para mostrar el nombre `shortname`
				document.getElementById("modal-body-content").innerHTML = `Va a proceder a dar de baja a <strong>{{ object.shortname }}</strong>. Ten en cuenta que esto pondrá la fecha de baja a día de hoy a todos los servicios que estén contratados y tengan una fecha de alta sin fecha de baja.`;
			
				// Configurar el botón de confirmación para proceder con la baja
				document.getElementById("confirm-button").onclick = function() {
				// Mostrar el spinner y deshabilitar los botones en el modal
				document.getElementById("modal-body-content").innerHTML = '<div class="custom-spinner"></div>';
				document.getElementById("confirm-button").disabled = true;
				document.getElementById("cancel-button").disabled = true;
			
				// Enviar la solicitud de baja
				fetch(toggleStatusUrl, {
					method: 'POST',
					headers: {
					'X-CSRFToken': '{{ csrf_token }}',
					'Content-Type': 'application/json'
					}
				})
				.then(response => {
					if (!response.ok) {
					throw new Error('Error en la solicitud');
					}
					return response.json();
				})
				.then(data => {
					const isInactive = data.is_inactive;
			
					// Actualizar el estado en el frontend sin recargar
					const toggleStatusIcon = document.getElementById("toggle-status-icon");
					if (toggleStatusIcon) {
						toggleStatusIcon.innerHTML = isInactive 
							? '<span class="status-indicator" style="background-color: red; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px;"></span> Baja'
							: '<span class="status-indicator" style="background-color: green; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px;"></span> Alta';
					
						// Actualiza el evento onclick
						toggleStatusIcon.onclick = toggleSellerStatus;
					}
			
					// Cerrar el modal, mostrar el toast de éxito y recargar la página
					setTimeout(() => {
						$('#confirmDeactivateModal').modal('hide');
						showToast('Estado cambiado correctamente', 'success');
						location.reload();
					}, 500);  // Pequeño retardo de 500ms antes de cerrar el modal
					
				})
				.catch(error => {
					console.error("Error al cambiar el estado:", error);
					showToast('Error al cambiar el estado. Inténtalo de nuevo.', 'error');
				})
				.finally(() => {
					// Restaurar el contenido del modal y habilitar los botones después de la respuesta
					document.getElementById("modal-body-content").innerHTML = `Va a proceder a dar de baja a <strong>{{ object.shortname }}</strong>. Ten en cuenta que esto pondrá la fecha de baja a día de hoy a todos los servicios que estén contratados y tengan una fecha de alta sin fecha de baja.`;
					document.getElementById("confirm-button").disabled = false;
					document.getElementById("cancel-button").disabled = false;
				});
				};
			} else {
				// Si el estado es "Baja" y se intenta cambiar a "Alta", mostrar solo el toast de advertencia
				showToast('Solo se puede modificar el estado de baja desde el panel de administrador.\n contacta con support!!', 'warning');
			}
		}

		// Muestra una notificación tipo Toast con un mensaje y tipo de alerta.
		function showToast(message, type) {
			Toast.fire({
				icon: type,
				title: message
			});
		}

		function resetValidationFeedback() {
			debugLog(`[HTMXCardCountryHandler] Ejecutando -> resetValidationFeedback`);
		
			document.querySelectorAll('.is-invalid').forEach(input => {
				HTMXServiceHandler.toggleEndDateValidationFeedback(input, null);
			});
		}
		
		// Muestra errores de validación en los inputs con animación
		function showValidationErrors(errors) {
			if (!Object.keys(errors).length) {
				resetValidationFeedback();
			}
		
			let fieldIndex = 1;
		
			for (const [field, messages] of Object.entries(errors)) {
				let input = document.getElementById(`id_${field}`);
		
				if (input) {
					if (!input.hasAttribute("data-gtm-form-interact-field-id")) {
						input.setAttribute("data-gtm-form-interact-field-id", fieldIndex);
						fieldIndex++;
					}
		
					const uniqueMessages = [...new Set(messages)];
					HTMXServiceHandler.toggleEndDateValidationFeedback(input, { message: uniqueMessages.join(', ') });
					debugLog(`Campo con error: ${field}`, input, `Mensaje: ${uniqueMessages.join(', ')}`);
				} else {
					console.warn(`No se encontró el input con id="id_${field}"`);
				}
			}
		}

		async function validateAndToggleCardFeedback(response, fieldType) {
			debugLog(`[HTMXServiceHandler] Ejecutando -> validateAndToggleCardFeedback para ${fieldType}`);
		
			try {
				if (response.status === 400) {
					let errorData;
		
					// Intentar parsear la respuesta JSON
					try {
						errorData = JSON.parse(response.responseText);
					} catch (e) {
						debugLog(`[HTMXServiceHandler] No se pudo parsear la respuesta como JSON.`);
						return false;
					}
		
					// Si hay errores en la respuesta, mostrarlos
					if (errorData.errors) {
						debugLog(`[HTMXServiceHandler] Errores recibidos:`, errorData.errors);
						showValidationErrors(errorData.errors);
					}
		
					return false; // Validación fallida
				}
		
				if (response.status === 200) {
					debugLog(`[HTMXServiceHandler] Validación exitosa para ${fieldType}, limpiando errores.`);
					resetValidationFeedback();
					return true; // Validación exitosa
				}
		
				// Caso inesperado
				debugLog(`[HTMXServiceHandler] Respuesta inesperada del servidor.`);
				showToast(`Error inesperado al validar ${fieldType}.`, "error");
				return false;
			} catch (error) {
				debugLog(`[HTMXServiceHandler] Error procesando la respuesta del servidor:`, error);
				showToast("Error en la conexión con el servidor.", "error");
				return false;
			}
		}

		// Función genérica para alternar el indicador de carga en un modal
		function toggleModalLoading(isLoading, loadingIndicatorId, modalContentId) {
			debugLog(`[HTMXHandler] Ejecutando -> toggleModalLoading con isLoading=${isLoading}`);

			const loadingIndicator = document.getElementById(loadingIndicatorId);
			const modalContent = document.getElementById(modalContentId);

			if (loadingIndicator && modalContent) {
				if (isLoading) {
					loadingIndicator.classList.remove('d-none');
					modalContent.classList.add('d-none');
				} else {
					loadingIndicator.classList.add('d-none');
					modalContent.classList.remove('d-none');
				}
			} else {
				console.warn(`[HTMXHandler] No se encontraron los elementos con IDs: ${loadingIndicatorId}, ${modalContentId}`);
			}
		}

		//   ------------------------ 
		//    Manejadores de eventos
		//   ------------------------

		// ---> Eventos de HTMX

		// Se dispara antes de que HTMX reemplace el contenido del DOM con una nueva respuesta del servidor..
		document.body.addEventListener('htmx:beforeSwap', function(evt) {
			if (evt.detail.xhr.status === 400) {
				evt.detail.shouldSwap = false; // cancela el swap en caso de error error (status 400)
				if (!debug) {
					event.preventDefault(); // Error controlado,se oculta en produccion
				}
			}
		});

		// Se dispara antes de enviar los formularios
		document.body.addEventListener('htmx:beforeRequest', async function(event) {
			const formId = event.detail.elt.id;
			debugLog(`[HTMX Event] Before Request - Form ID: ${formId}`);

			// Validación del formulario de Países (VAT) desde paises IVA
			if (formId === 'addUpdateVatActivationDateForm') {
				// Limpiar errores previos
				resetValidationFeedback(); // Limpiar errores previos
				toggleModalButtons(true, '#addUpdateVatActivationDateModal', '#vatDateSubmitBtn') // Deshabilitar los botones del modal
				toggleModalLoading(true, 'loading-indicator-vat', 'modal-content-vat'); // Mostrar indicador de carga
			}
			// Validación del formulario de Países (VAT) desde Servicios
			if (formId === 'addUpdateCountryContractedForm') {
				resetValidationFeedback(); // Limpiar errores previos
				toggleModalButtons(true, '#addUpdateCountryContractedModal', '#countrySubmitBtn') // Deshabilitar los botones del modal
				toggleModalLoading(true, 'loading-indicator', 'modal-content'); // Mostrar indicador de carga
			}
			// Validación del formulario de Servicios
			if (formId === 'addUpdateServiceForm') {
				// Limpiar errores previos
				resetValidationFeedback(); // Limpiar errores previos
				toggleModalLoading(true, 'loading-indicator-service', 'modal-content-service'); // Mostrar indicador de carga
				toggleModalButtons(true, '#addUpdateServiceModalService', '#submitButton') // Deshabilitar los botones del modal
			}
		});

		// Se dispara después de enviar los formularios
		document.body.addEventListener('htmx:afterRequest', async function(event) {
			const formId = event.detail.elt.id;
			debugLog(`[HTMX Event] After Request - Form ID: ${formId}`);

			if (formId === 'addUpdateVatActivationDateForm') {
				
				// Validar la respuesta del servidor usando la función genérica
				const isValid = await validateAndToggleCardFeedback(event.detail.xhr, "activación de fecha VAT");
				toggleModalButtons(false, '#addUpdateVatActivationDateModal', '#vatDateSubmitBtn'); // Habilitar botones
			
				if (!isValid) {
					toggleModalLoading(false, 'loading-indicator-vat', 'modal-content-vat');
					showToast('Error al actualizar, revisa los campos.', 'error');
					return;
				}
			
				// Éxito: cerrar modal, actualizar tarjeta y tabla
				toggleModalLoading(false, 'loading-indicator-vat', 'modal-content-vat');
				const modal = document.getElementById('addUpdateVatActivationDateModal');
				const modalInstance = bootstrap.Modal.getInstance(modal);
				modalInstance.hide();
				showToast('Datos actualizados correctamente', 'success');
			
				const pk = document.getElementById('id_seller_vat_pk').value;
				await HTMXCardCountryHandler.updateCardCountry(pk);
				HTMXServiceHandler.updateTableData();
			}
			
			if (formId === 'addUpdateCountryContractedForm') {
				
				// Validar la respuesta del servidor usando la función genérica
				const isValid = await validateAndToggleCardFeedback(event.detail.xhr, "país contratado");
				toggleModalButtons(false, '#addUpdateCountryContractedModal', '#countrySubmitBtn'); // Habilitar botones
			
				if (!isValid) {
					toggleModalLoading(false, 'loading-indicator', 'modal-content');
					showToast('Error al actualizar, revisa los campos.', 'error');
					return;
				}
			
				// Éxito: cerrar modal, actualizar tarjeta y tabla
				toggleModalLoading(false, 'loading-indicator', 'modal-content');
				const modal = document.getElementById('addUpdateCountryContractedModal');
				const modalInstance = bootstrap.Modal.getInstance(modal);
				modalInstance.hide();
				showToast('Datos actualizados correctamente', 'success');
			
				const pk = document.getElementById('id_contracted_country_seller_pk').value;
				await HTMXCardCountryHandler.updateCardCountry(pk);
				HTMXServiceHandler.updateTableData();
			}
			
			if (formId === 'addUpdateServiceForm') {
				
				// Validar la respuesta del servidor
				const isValidService = await validateAndToggleCardFeedback(event.detail.xhr, "servicio contratado");
				toggleModalButtons(false, '#addUpdateServiceModalService', '#serviceSubmitBtn'); // Habilitar los botones del modal
			
				if (!isValidService) {
					toggleModalLoading(false, 'loading-indicator-service', 'modal-content-service');
					showToast('Error al actualizar el servicio contratado, revisa los campos.', 'error');
					return;
				}
			
				// Éxito: cerrar modal, resetear formulario y actualizar tabla
				toggleModalLoading(false, 'loading-indicator-service', 'modal-content-service');
				const modal = document.getElementById('addUpdateServiceModalService');
				const modalInstance = bootstrap.Modal.getInstance(modal);
				modalInstance.hide();
			
				showToast('Servicio actualizado correctamente', 'success');
				HTMXServiceHandler.updateTableData();
			}

			if (formId === 'deleteServiceForm' && event.detail.xhr.status === 200) {
				const jsonResponse = event.detail.xhr.responseText;
				try {
						const response = JSON.parse(jsonResponse);
						const serviceCard = document.getElementById(response.service);
						if (serviceCard) {
								serviceCard.remove(); //Se elimina la tarjeta del servicio
								}
						} catch (error) {
								console.log("La respuesta no es un JSON válido:", jsonResponse);
						}
				const modal = document.getElementById('confirmDeleteServiceModal');
				const modalInstance = bootstrap.Modal.getInstance(modal);
				modalInstance.hide();
				showToast('Fechas de servicio eliminadas correctamente', 'success');
			}
		});

		// Se dispara después de que HTMX ha reemplazado el contenido del DOM con la respuesta del servidor.
		document.body.addEventListener('htmx:afterSwap', function (event) {
			$('[data-bs-toggle="tooltip"]').tooltip(); // reinicializar los tooltips luego de guardados los formularios
			initializeDatePicker();
		});

		// ---> Eventos del navegador (JavaScript nativo)

		// Ejecutar código para inicializar la tabla inmediatamente que el DOM esté listo
		$(document).ready(function() {
			const table = {     
				id: '#services-vat-country-list',
				type: 'países',
			}
			debugLog(`tabla iniciada con ${table}`)
			renderDataTable(table);
			debugLog(`ejecutada la funcionrenderDataTable(table) con table = {${table}}`)
		});

		// Ejecutar código solo cuando toda la página haya cargado completamente
		window.addEventListener('load', () => {
			// Obtener elementos del formulario
			const name = document.getElementById('id_name');
			const trade_name = document.getElementById('id_trade_name');
			const first_name = document.getElementById('id_first_name');
			const last_name = document.getElementById('id_last_name');
		
			// Verificar si hay un hash en la URL para abrir la pestaña correspondiente
			const hash = window.location.hash;
			if (hash) {
				const tabTrigger = document.querySelector(`.nav-link[href="${hash}"]`);
				if (tabTrigger) {
					const tab = new bootstrap.Tab(tabTrigger);
					tab.show();
					window.history.replaceState(null, null, window.location.pathname);
				}
			}
		
			// Inicializar Datepicker en todos los inputs de fecha
			initializeDatePicker();
		
			// Actualizar campo "name" automáticamente cuando el usuario ingrese nombre o apellido
			first_name.addEventListener('input', () => {
				name.value = `${first_name.value} ${last_name.value}`;
			});
		
			last_name.addEventListener('input', () => {
				name.value = `${first_name.value} ${last_name.value}`;
			});
		
			// Si el usuario no es autónomo, permitir modificar trade_name basado en name (Actualmente comentado)
			if ('{{ object.legal_entity }}' !== 'self-employed') {
				name.addEventListener('input', () => {
					{% comment %} trade_name.value = name.value; {% endcomment %}
				});
			}
		
			// Habilitar copiado al portapapeles con ClipboardJS
			new ClipboardJS('[data-clipboard=true]').on('success', function(e) {
				const trigger = e.trigger;
				trigger.setAttribute('data-bs-original-title', trigger.getAttribute('data-copied-tip'));
				const tooltip = bootstrap.Tooltip.getInstance(trigger);
				tooltip.show();
		
				setTimeout(() => {
					trigger.setAttribute('data-bs-original-title', trigger.getAttribute('data-original-tip'));
					tooltip.hide();
				}, 2000);
		
				e.clearSelection();
			});

			// Lista de formularios donde se aplica el comportamiento del "Enter"
			const formsWithEnterBehaviour = [
				'#generalInfoForm', 
				'#aditionalInfoESForm', 
				'#addressForm', 
				'#addUpdateServiceForm', 
				'#addUpdateActivityForm', 
				'#addUpdateVatActivationDateModal',
				'#addUpdateCountryContractedForm'
			];

			// Aplicar la función a todos los formularios de la lista
			formsWithEnterBehaviour.forEach(selector => {
				const form = document.querySelector(selector);
				if (form) pressEnterBehaviour(form);
			});
		});
	</script>
{% endblock javascripts %}
