{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Representantes del Vendedor{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
                <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                Representantes del vendedor
            </h5>
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
           <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_representatives:representative_list' seller.shortname %}">Representantes</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos del Representante</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
<style>

</style>


<div class="card">
    <div class="card-body">
        <div class="row">
        <div class="d-flex align-items-center justify-content-between">
            <h3 id="title_process">Datos del Representante:</h3>
            
        </div>
        <hr>
        </div><br>
        <form class="form-horizontal" method="post" id="form" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="row">
            <div class="col mb-3">
                <label for="first_name" class="form-label">
                    Nombre: 
                </label>
                <input 
                    type="text" 
                    maxlength="150"
                    id="first_name" 
                    name="first_name" 
                    class="textinput textInput form-control"
                    value="{{object.first_name| default:''}}"
                    required
                />            
            </div>
            <div class="col mb-3">
                <label for="last_name" class="form-label">
                    Apellidos: 
                </label>
                <input 
                    type="text" 
                    maxlength="150"
                    id="last_name" 
                    name="last_name" 
                    class="textinput textInput form-control"
                    value="{{object.last_name| default:''}}"
                    required
                />
            </div>
            <div class="col mb-3">
                <label for="representative_id" class="form-label">
                    NIF:
                </label>
                <input 
                    type="text" 
                    maxlength="150"
                    id="representative_id" 
                    name="representative_id" 
                    class="textinput textInput form-control"
                    value="{{object.representative_id| default:''}}"
                    required
                />            
            </div>
            <div class="col mb-3">
                <label for="representative_id" class="form-label">
                    COD.FISCALE (Italia):
                </label>
                <input 
                    type="text" 
                    maxlength="150"
                    id="codice_fiscale" 
                    name="codice_fiscale" 
                    class="textinput textInput form-control"
                    value="{{object.codice_fiscale| default:''}}"
                    
                />            
            </div>
        </div>
        <div class="row mt-3">
            <div class="col mb-3">
                <label for="birthdate" class="form-label">
                    Fecha de Nacimiento:
                </label>
                <input
                    type="date"
                    id="birthdate"
                    name="birthdate"
                    class="dateinput form-control"
                    {% if object.birthdate %} value="{{ object.birthdate|date:"Y-m-d" }}" {% endif %}
                  />            
            </div>
            <div class="col mb-3">
                <label for="catastral_reference" class="form-label">
                    Género del Representante:
                </label>
                <select id="gender" name="gender" class="select form-select">
                  {% for choice in form.gender.field.choices %}
                    {% if choice.0 == object.gender %}
                      <option value="{{ choice.0 }}" selected>{{ choice.1 }}</option>
                    {% else %}
                      <option value="{{ choice.0 }}">{{ choice.1 }}</option>
                    {% endif %}
                  {% endfor %}
                </select>         
            </div>
            <div class="col mb-3">
                <label for="birth_country" class="form-label">
                    País de nacimiento del Representante:
                </label>
                <select id="birth_country" name="birth_country" class="select form-select"
                    required="">
                <option value="">---------</option>
                {% for country in countries %}
                    {% if country.iso_code == object.birth_country.iso_code %}
                    <option value="{{ country.iso_code }}" selected>{{ country.name }}</option>
                    {% else %}
                    <option value="{{ country.iso_code }}">{{ country.name }}</option>
                    {% endif %}
                {% endfor %}
                </select>         
            </div>
            <div class="col mb-3">
                <label for="birth_country" class="form-label">
                    Tipo de representación:
                </label>
                <select id="type_representation" name="type_representation" class="select form-select">
                  {% for choice in form.type_representation.field.choices %}
                    {% if choice.0 == object.type_representation %}
                      <option value="{{ choice.0 }}" selected>{{ choice.1 }}</option>
                    {% else %}
                      <option value="{{ choice.0 }}">{{ choice.1 }}</option>
                    {% endif %}
                  {% endfor %}
                </select> 
                <div class="info-text mt-2">
                    <p style = "font-size: smaller;"><b>Para el IVA Italiano, el tipo de representación debe ser "representante legal".</b></p>
                </div>       
            </div>
        </div>
        <div class="mt-3 d-flex justify-content-center align-items-center">
            <button type="submit" class="btn btn-primary">Guardar</button>
        </div>
        </form>
        
        
    </div>
</div>

{% endblock content %}

{% block javascripts %}

<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
<script>

</script>
{% endblock javascripts %}