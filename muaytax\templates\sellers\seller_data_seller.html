{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Datos Empresa
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Datos Empresa</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="#">Administración</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Datos Empresa</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-12">
      <div class="card ">
        <div class="card-body">
          <div class="row">
            <div class="col">
              <h5 class="mt-3 mb-3"><b>Datos del Usuario</b></h5>
              <table class="table table-borderless">
                <tbody>
                  <tr>
                    <td class="">Nombre</td>
                    <td class="">{{ request.user.name }}</td>
                  </tr>
                  <tr>
                    <td class="">Nombre Corto</td>
                    <td class="">{{ request.user.username }}</td>
                  </tr>
                  <tr>
                    <td class="">Email</td>
                    <td class="">{{ request.user.email }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="col">
              <h5 class="mt-3 mb-3"><b>Datos de la Empresa</b></h5>
              <table class="table table-borderless">
                <tbody>
                  <tr>
                    <td class="">Nombre</td>
                    <td class="">{{ request.user.seller.name|title }}</td>
                  </tr>
                  <tr>
                    <td class="">Nombre Corto</td>
                    <td class="">{{ request.user.seller.shortname|lower }}</td>
                  </tr>
                  <tr>
                    <td class="">Entidad Jurídica</td>
                    <td class="">{{ request.user.seller.legal_entity|upper }}</td>
                  </tr>
                  <tr>
                    <td class="">Telefono</td>
                    <td class="">{{ request.user.seller.phone|default:"" }}</td>
                  </tr>
                  <tr>
                    <td class="">Dirección Fiscal</td>
                    <td class="">{{ request.user.seller.adress }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <div class="col">
              <h5 class="mt-3 mb-4 pb-3 border-bottom"><b>Contactos</b></h5>
              <div class="row align-items-center mb-3">
                <div class="col-2 border-start">
                  <h5>Clientes</span>
            </div>
            <div class="col-1">
              <h6>{{ customers_count }}</h6>
            </div>
            
            <div class="col-2 border-start">
              <h5>Proveedores</span>
            </div>
            <div class="col-1">
              <h6>{{ providers_count }}</h6>
            </div>

            <div class="col-2 border-start">
              <h5>Socios</span>
            </div>
            <div class="col-1">
              <h6>{{ partners_count }}</h6>
            </div>

            <div class="col-2 border-start">
              <h5>Empleados</span>
            </div>
            <div class="col-1 border-end">
              <h6>{{ workers_count }}</h6>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  <script>
     
  </script>
{% endblock javascripts %}
