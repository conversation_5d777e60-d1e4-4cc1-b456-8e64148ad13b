<div class="modal fade " id="modalVerifactuManualOptions" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header bg-dark">
        <h5 class="modal-title text-white" id="modalLabel"> Panel de control de opciones manuales VeriFActu </h5>
      </div> 
      <div class="modal-body">
        <div class="container">
          <div style="display: flex; justify-content: center;">
            <div class="row">
              <!--DIRECT SENDING AEAT OPTIONS CARD-->
              <div class= "card" style="width: 100%; ">
                <div class="card-body">
                  <h5 class="card-title" style="border-bottom: 1px solid; padding-bottom: 5px;"><i class="fa-solid fa-share-from-square"></i> &nbsp; Envío directo al servicio Verifactu de la AEAT</h5>
                  <div>
                    <p class="card-text">A continuación se listan las diferentes acciones que se pueden llevar a cabo desde este panel:</p>
                    
                    <ul style="display: flex; gap: 15px; flex-direction: column;">
                      <li><b>"Enviar a la AEAT": </b>
                        Da de alta la factura de forma directa a la AEAT a través del sistema Verifactu. 
                      </li>
                      <li><b>"Comprobar Factura": </b>
                        Manda una petición a la AEAT con los datos de la factura para comprobar si ya está registrada y su estado actual. 
                      </li>
                      <li><b>"Cancelar Factura": </b> 
                        Solicita la cancelación de la factura en el sistema Verifactu. El registro de la factura queda guardado en el sistema, pero con estado cancelado y no se toma en cuenta 
                      </li>
                    </ul>
                  </div>
                  {% if lastVeriFactuInv.operation_type == 'Alta' or lastVeriFactuInv.operation_type == 'Anulacion' %}
                    <div style="display: flex; justify-content: center; ">
                      <div class="card rounded border mb-0" style= "border-radius: 5px !important; width: 90%">
                        <div class="card-block">
                          <div class="d-flex gap-3 align-items-center mb-3">
                            <svg width="40" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.10011 38.3275L22.5 9.99997C23.7 7.92247 26.4726 8.22998 27.6701 10.3075L43.8476 38.3275C45.0476 40.405 43.5476 43 41.1501 43H8.79761C6.40011 43 4.90011 40.405 6.10011 38.3275Z" stroke="#E0364C" stroke-width="3" stroke-miterlimit="10"/>
                            <path d="M25 33V17.5" stroke="#E0364C" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M25 38.5C25.8284 38.5 26.5 37.8284 26.5 37C26.5 36.1716 25.8284 35.5 25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5Z" fill="#E0364C"/>
                            </svg>

                            <h4 class="mb-0 text-danger">
                            {% if lastVeriFactuInv.operation_type == 'Alta' %}
                              <b> Atención Factura ya registrada en la AEAT </b>
                            </h4>
                          </div>
                          <div class="row">
                            <div class="col-12">
                              <p>
                                Esta factura ya ha sido incorporada al sistema VeriFactu de la AEAT y <b>se encuentra actualmente registrada</b>.
                                Si vuelves a hacer clic en el botón "Enviar a la AEAT", se reenviará y se considerará una corrección marcándose como "subsanación".
                              </p>
                            {% else %}
                              <b> Atención Factura anulada en la AEAT </b>
                            </h4>
                          </div>
                          <div class="row">
                            <div class="col-12">
                              <p>
                                Esta factura ya ha sido incorporada al sistema VeriFactu de la AEAT y <b>se encuentra actualmente ANULADA</b>.
                                Si vuelves a hacer clic en el botón "Enviar a la AEAT", se reenviará volviéndose a dar de alta y se considerará una corrección marcándose como "subsanación".
                              </p>
                            {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endif %}

                  <div style="display: flex; justify-content: center; margin-top: 35px; gap: 30px;">
                    <button class="btn btn-success" id="send_xmlVerifactu" onclick="sendInvoiceVeriFactuToAEAT('{{seller.shortname}}', '{{invoice.pk}}')" >
                      <i class="fa-solid fa-share"></i>
                      Enviar a la AEAT
                    </button> 
                    <button class="btn btn-light" id="send_check_xml_verifactu" onclick="sendInvoiceVeriFactuToAEAT('{{seller.shortname}}', '{{invoice.pk}}', 'check_record_aeat')" >
                      <i class="fa-solid fa-magnifying-glass"></i>
                      Comprobar Factura
                    </button> 
                    <button class="btn btn-danger" id="send_cancel_xml_verifactu" onclick="sendInvoiceVeriFactuToAEAT('{{seller.shortname}}', '{{invoice.pk}}', 'cancel_record_aeat')" >
                      <i class="fa-solid fa-ban"></i>
                      Cancelar Factura
                    </button> 
                  </div>
                </div>
              </div>

            <!--DOWNLOAD OPTIONS CARD ONLY VISIBLE FOR WEBMASTER-->
            {% if auth_user == "webmaster" %}
              <div class= "card" style="width: 100%;">
                <div class="card-body">
                  <h5 class="card-title" style="border-bottom: 1px solid; padding-bottom: 5px;"><i class="fa-solid fa-file-arrow-down"></i> &nbsp; Descargas de XML servicio VeriFactu </h5>
                  <div>
                    <p class="card-text">Descripción de las distintas descargas en formato XML para envío manual al sistema VeriFactu de la AEAT:</p>
                    <ul style="display: flex; gap: 15px; flex-direction: column;">
                      <li><b>"XML de alta"</b>
                        Descarga la factura en formato XML para ser dada de alta. 
                      </li>
                      <li><b>"XML Comprobación"</b>
                        Descarga el archivo XML que para solicitar la comprobación del estado actual de la factura en VeriFactu. 
                      </li>
                      <li><b>"XML Cancelación"</b> 
                        Descarga el archivo XML que para solicitar la cancelación de la factura en VeriFactu. 
                      </li>
                    </ul>
                  </div>
                  <div style="display: flex; justify-content: center; margin-top: 35px; gap: 30px;">
                    <button class="btn btn-success" id="send_xmlVerifactu" onclick="generateInvoiceVeriFactuXML('{{seller.shortname}}', '{{invoice.pk}}')">
                      <i class="fa-solid fa-download"></i>
                      XML de alta
                    </button>
                    <button class="btn btn-light" id="send_check_xml_verifactu" onclick="generateInvoiceVeriFactuXML('{{seller.shortname}}', '{{invoice.pk}}', 'check_record_aeat')">
                      <i class="fa-solid fa-download"></i>
                      XML Comprobación
                    </button>
                    <button class="btn btn-danger" id="send_cancel_xml_verifactu" onclick="generateInvoiceVeriFactuXML('{{seller.shortname}}', '{{invoice.pk}}', 'cancel_record_aeat')">
                      <i class="fa-solid fa-download"></i>
                      XML Cancelación
                    </button>
                  </div>
                </div>
              </div>
            {% endif %}
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer d-flex justify-content-center">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modalVerifactuManualOptions" aria-label="Close">Cerrar
        </button>
      </div>
    </div>
  </div>
</div>