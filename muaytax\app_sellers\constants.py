IT_REPRESENTATION_TYPE = [
    ('1', 'Directa'),
    ('2', 'Indirecta'),
]

PRODUCTS_SERVICES_CHOICES = [
    ('products', 'Productos'),
    ('services', 'Servicios'),
]

SERVICES = {
    # Servicios del modelo base: Seller
    'Seller': {
        'is_base_model': True,  # Identificador de modelo base
        'services_dates': [
            {
                'name': 'contracted_accounting',
                'display_name': 'Contabilidad ES',
                'start_date': 'contracted_accounting_date',
                'end_date': 'contracted_accounting_end_date'
            },
            {
                'name': 'contracted_maintenance_llc',
                'display_name': 'Mantenimiento LLC',
                'start_date': 'contracted_maintenance_llc_date',
                'end_date': 'contracted_maintenance_llc_end_date'
            },
            {
                'name': 'contracted_labor_payroll',
                'display_name': 'Nóminas Laborales',
                'start_date': 'contracted_labor_payroll_date',
                'end_date': 'contracted_labor_payroll_end_date'
            },
            {
                'name': 'contracted_corporate_payroll',
                'display_name': 'Nóminas Societarias',
                'start_date': 'contracted_corporate_payroll_date',
                'end_date': 'contracted_corporate_payroll_end_date'
            },
            {
                'name': 'oss',
                'display_name': 'OSS',
                'start_date': 'oss_date',
                'end_date': 'oss_end_date'
            },
            {
                'name': 'contracted_accounting_txt',
                'display_name': 'Traducción TXT',
                'start_date': 'contracted_accounting_txt_date',
                'end_date': 'contracted_accounting_txt_end_date'
            },
            {
                'name': 'contracted_model_presentation',
                'display_name': 'Presentación Modelos',
                'start_date': 'contracted_model_presentation_date',
                'end_date': 'contracted_model_presentation_end_date'
            },
            {
                'name': 'withholdings_payments_account',
                'display_name': 'Retenciones e ingresos a cuenta',
                'start_date': 'withholdings_payments_account_date',
                'end_date': 'withholdings_payments_account_end_date'
            },
            {
                'name': 'contracted_accounting_usa',
                'display_name': 'Contabilidad USA (Premium)',
                'start_date': 'contracted_accounting_usa_date',
                'end_date': 'contracted_accounting_usa_end_date'
            },
            {
                'name': 'contracted_accounting_usa_basic',
                'display_name': 'Contabilidad USA (Basic)',
                'start_date': 'contracted_accounting_usa_basic_date',
                'end_date': 'contracted_accounting_usa_basic_end_date'
            },
            {
                'name': 'tax_agency_accounting',
                'display_name': 'Hacienda (Contabilidad ES)',
                'start_date': 'tax_agency_accounting_date',
                'end_date': 'tax_agency_accounting_end_date'
            }
        ]
    },

    # Servicios del modelo externo: SellerVat
    'vat_seller': {
        'is_base_model': False,  # Identificador de modelo externo
        'services_dates': [
            {
                'name': 'vat_service',
                'display_name': 'IVA en Hacienda',
                'start_date': 'activation_date',
                'end_date': 'deactivation_date'
            },
            {
                'name': 'contracting_seller_vat',
                'display_name': 'Contratación',
                'start_date': 'contracting_date',
                'end_date': 'contracting_discontinue'
            },
            {
                'name': 'service_start_seller_vat',
                'display_name': 'Servicio',
                'start_date': 'start_contracting_date',
                'end_date': 'end_contracting_date'
            }
        ]
    },

    # Servicios del modelo externo: Service
    'service_seller': {
        'is_base_model': False,  # Identificador de modelo externo
        'services_dates': [
            {
                'name': 'contracting_service',
                'display_name': 'Contratación',
                'start_date': 'contracting_date',
                'end_date': 'contracting_discontinue'
            },
            {
                'name': 'service_start_service',
                'display_name': 'Inicio de Servicio',
                'start_date': 'start_contracting_date',
                'end_date': 'end_contracting_date'
            },
            {
                'name': 'hacienda_service',
                'display_name': 'Hacienda',
                'start_date': 'activation_date',
                'end_date': 'deactivation_date'
            }
        ]
    }
}
