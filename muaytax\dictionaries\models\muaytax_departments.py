from django.db import models

class MuaytaxDepartment(models.Model):
    code = models.Cha<PERSON><PERSON><PERSON>(
        primary_key=True,
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )
    name = models.Char<PERSON>ield(
        null=True,
        blank=True,
        max_length=250,
        verbose_name="Nombre del departamento"
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="Descripción"
    )
    email = models.CharField(
        null=True,
        blank=True,
        max_length=250,
        verbose_name="Email"
    )
    phone = models.CharField(
        null=True,
        blank=True,
        max_length=250,
        verbose_name="Teléfono"
    )
    whatsapp_number_id = models.Char<PERSON>ield(
        null=True,
        blank=True,
        max_length=250,
        verbose_name="ID del número de WhatsApp",
        help_text="Este identificador se utiliza para enviar mensajes de WhatsApp a través de la API de META."
    )
    
    class Meta:
        verbose_name = "Departamento"
        verbose_name_plural = "Departamentos"

    def __str__(self):
        return str(self.name)

# @admin.register(MuaytaxDepartment)
# class MuaytaxDepartmentAdmin(admin.ModelAdmin):
#     list_display = ["name", "code", "description", "email",  "phone"]
#     search_fields = ["name", "code", "description", "email", "phone"]