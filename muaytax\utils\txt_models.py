import unicodedata
import re
import json

from django.db.models import Q
from datetime import datetime

from muaytax.dictionaries.models import Country
from muaytax.dictionaries.models import VatRates
from muaytax.app_sellers.models import Seller
from muaytax.app_sellers.models import SellerVat
from muaytax.app_invoices.models import Invoice
from muaytax.app_sellers.models.seller_184 import PresentedM184
from muaytax.dictionaries.models import EconomicActivity
from muaytax.app_documents.utils.utils import still_in_limit_date_direct_debit
from muaytax.app_sellers.vat_validation_utils import is_valid_spanish_vat
from muaytax.utils.general import get_period_details

#TODO Comprobar al menos una vez al año las URL de la presentación manual, ya que pueden cambiar en el tiempo (Sobre todo en los anuales)
#TODO Comprobar que el 111, 115, 130,202, 303 se generan bien tras implementar el tipo de declaración con domiciliación 


def urlManualToAEAT():
    
    url_dict = {
        'ES-111': 'https://www1.agenciatributaria.gob.es/wlpl/OV17-M111/index.zul',
        'ES-115': 'https://www1.agenciatributaria.gob.es/wlpl/OV17-M115/index.zul',
        'ES-130': 'https://www1.agenciatributaria.gob.es/wlpl/PAMW-M130/E{year}/CONT/index.zul',
        'ES-180': 'https://www1.agenciatributaria.gob.es/wlpl/PAIN-M180/E{year}/index.zul',
        'ES-184': 'https://www1.agenciatributaria.gob.es/wlpl/PAIN-M184/E{year}/index.zul',
        'ES-190': 'https://www1.agenciatributaria.gob.es/wlpl/PAIN-M190/E{year}/index.zul',
        'ES-202': 'https://www1.agenciatributaria.gob.es/wlpl/OVME-SOCI/202/V1/index.zul',
        'ES-303': 'https://www1.agenciatributaria.gob.es/wlpl/BUGC-JDIT/VentanaCensalIva',
        'ES-309': 'https://www1.agenciatributaria.gob.es/wlpl/FWME-FIVA/309/V1/CONT/index.zul',
        'ES-347': 'https://www1.agenciatributaria.gob.es/wlpl/PAIN-M347/E{year}/index.zul',
        'ES-349': 'https://www1.agenciatributaria.gob.es/wlpl/PAIN-M349/E{year}/index.zul',
        'ES-369': 'https://www1.agenciatributaria.gob.es/wlpl/VOSS-M369/index.zul?DEC=FH',
        'ES-390': 'https://www1.agenciatributaria.gob.es/wlpl/PAIV-M390/E{year}/CONT/index.zul',
    }
    
    # Reemplazar el año  dinámicamente en las URLs que lo requieren
    for key, url in url_dict.items():
        if '{year}' in url:
            url_dict[key] = url.format(year=datetime.now().year)

    url_model_json = json.dumps(url_dict)
    
    return url_model_json

def txtToAEAT(presented_model, seller):
    txt = None
    model_dict = {
        'ES-111': lambda: generate_TXT_MODEL_111(presented_model, seller),
        'ES-115': lambda: generate_TXT_MODEL_115(presented_model, seller),
        'ES-130': lambda: generate_TXT_MODEL_130(presented_model, seller),
        'ES-180': lambda: generate_TXT_MODEL_180(presented_model, seller),
        'ES-184': lambda: generate_TXT_MODEL_184(presented_model, seller),
        'ES-190': lambda: generate_TXT_MODEL_190(presented_model, seller),
        'ES-202': lambda: generate_TXT_MODEL_202(presented_model, seller),
        'ES-303': lambda: generate_TXT_MODEL_303(presented_model, seller),
        'ES-309': lambda: generate_TXT_MODEL_309(presented_model, seller),
        'ES-347': lambda: generate_TXT_MODEL_347(presented_model, seller),
        'ES-349': lambda: generate_TXT_MODEL_349(presented_model, seller),
        'ES-369': lambda: generate_TXT_MODEL_369(presented_model, seller),
        'ES-390': lambda: generate_TXT_MODEL_390(presented_model, seller),
    }
    
    func_txt = model_dict.get(presented_model.model.code)
    if func_txt:
        txt = func_txt()
    return txt

def special_char_eliminate(string, *args):
    """ 
    Elimina caracteres especiales de una cadena, con opciones para convertir a minúsculas, 
    eliminar espacios o mantener solo alfanuméricos (con ñ → n).
    """
    
    # Verificar y aplicar preprocesamientos si están en args
    if 'lower' in args:
        string = string.lower()
    if 'remove_spaces' in args:
        string = string.replace(" ", "")

    # Normalizar a Unicode NFD (caracteres acentuados)
    normalized = unicodedata.normalize('NFD', string)
    
    # Eliminar caracteres especiales (no ASCII)
    clean_string = normalized.encode('ascii', 'ignore').decode('utf-8')

    # Opción alphanumeric: Solo letras y números
    if 'alphanumeric' in args:
        clean_string = re.sub(r'[^a-zA-Z0-9]', '', clean_string)

    return clean_string

def format_numbers(string, long, space):
    string = string.replace('%', '')
    string = '0' if string == '' else string

    string = "{:.2f}".format(float(string.replace(',', '.'))).replace('.', '').zfill(long)
    
    # CASOS ESPECIALES DONDE DEBEMOS PARAMETRIZAR UN ESPACIO DELANTE DEL VALOR
    if space is True:
        string = 'N' + string[1:] if string.startswith('-') else ' ' + string[1:]
    else:
        string = string.replace('-', 'N')

    return string

def format_numbers_it(string, field):
    if field != '':
        if field == "vatannuale":
            string = string.rjust(16)
    return string

def check_nif_country(seller, country, nif, nom_op, error_messages):
        nif_all = nif
        country = country[:2]
        nif = nif[2:]
        name_country= ''
        if country is not None and country != '' and country != 'None' and country != 'No' and country != 'NONE' and country != 'NO':
            name_country = Country.objects.filter(iso_code = country).first()
            name_country = name_country.name if name_country is not None else ''

        url = f'<a href="/sellers/{seller.shortname}/providers/">Ir a Proveedores</a> / <a href="/sellers/{seller.shortname}/customers/">Ir a Clientes</a>'
        explain = f'es necesario que accedas al proveedor/cliente de este vendedor y corrijas sus datos.{url}'

        char_dict = {
                    '8': ['DK', 'SI', 'FI', 'HU', 'LU', 'MT'],
                    '9': ['DE', 'AT', 'CY', 'EE', 'EL','PT','GR'],
                    '10': ['BE', 'PL', 'SK'],
                    '11': ['HR', 'FR', 'IT', 'LV'],
                    '12': ['NL', 'SE'],
                    }

        special_cases ={
            'IE' : [8, 9],
            'BG' : [9, 10],
            'GB' : [5, 9, 12],
            'LT' : [9, 12],
            'CZ' : [8, 9, 10],
            'RO' : [2, 10]
        }


        if country is None or country == '' or country == 'None'  or country == 'No':
            error_messages.append({'error': f'El <b>código de país</b> del operador <b>"{nom_op}"</b> no es correcto, este campo está vacío, {explain}'})

        if nif_all is None or nif_all == '' or nif_all == 'None':
            error_messages.append({'error': f'El <b>NIF</b> del operador <b>"{nom_op}"</b> no es correcto, este campo está vacío, {explain}'})

        if sum(not caracter.isdigit() for caracter in nif) > 4:
            error_messages.append({'error': f'El <b>NIF "{nif}"</b> del operador <b>"{nom_op}"</b> es incorrecto, {explain}'})

        for key, country_list in char_dict.items():
            if country in country_list:
                max_length = int(key)
                if len(nif) != max_length:
                    error_messages.append({'error': f'El <b>NIF "{nif}"</b> del operador <b>"{nom_op}"</b> es erróneo, <u> el país {country} ({name_country}) debe contener {max_length} carácteres, </u> {explain}'})
                break

        for country_list, chars in special_cases.items():
            if country in country_list:
                if country == 'RO':
                    if len(nif) not in range(2,11):
                        error_messages.append({'error': f'El <b>NIF "{nif}"</b> del operador "{nom_op}" es erróneo, <u> el país {country} ({name_country}) debe contener de 2 a 10 carácteres,</u> {explain}'})
                else:
                    if len(nif) not in chars:
                        other_chars = f"{chars}"
                        other_chars = other_chars.replace('[','').replace(']','').replace("'","").replace(",","/")
                        error_messages.append({'error': f'El <b>NIF "{nif}"</b> del operador "{nom_op}" es erróneo, <u> el país {country} ({name_country}) debe contener la cantidad exacta de alguno de estos carácteres {other_chars},</u> {explain}'})

        return error_messages

def check_nif_seller(seller, error_messages):
        nif_registration = Seller.objects.filter(id=seller.id).first().nif_registration
        nif_sellervat = SellerVat.objects.filter(seller_id=seller.id, vat_country='ES').first()
        nif = nif_registration
        url_vat = f'<a href="/sellers/{seller.shortname}/vat">Ir a listado de países IVA</a>'
        url = f'<a href="/sellers/{seller.shortname}/information">Ir a Información fiscal</a>'
        explain = f'Por favor completa la información que falta siguiendo el siguiente enlace. '

        if seller.legal_entity in ['llc', 'other']:
            nif = nif_sellervat.vat_number if nif_sellervat is not None else None
            if nif is None or nif == '':
                error_messages.append({'error': f'El vendedor no tiene NIF/CIF en su País IVA España. {explain} {url_vat}'})
                return error_messages
            else:
                nif = nif
            if nif is not None and nif != '':
                if nif.startswith('ES'):
                    nif = nif[2:]
                # if is_valid_spanish_vat(nif) is False:
                #     error_messages.append({
                #         'error': f'El NIF/CIF del vendedor no es válido. <br>NIF/CIF encontrado: {nif}.<br>{explain} {url_vat}'})
                #     return error_messages
                # else:
                return nif
        else:
            if nif_registration is None or nif_registration == '' or nif_registration == 'None':
                nif = nif_sellervat.vat_number if nif_sellervat is not None else None
                if nif is None or nif == '' or nif == 'None':
                    error_messages.append({'error': f'El vendedor no tiene NIF/CIF en su información fiscal. {explain} {url}'})
                    return error_messages
                else:
                    nif = nif
                    if nif is None or nif == '' or nif == 'None':
                        error_messages.append({'error': f'El vendedor no tiene NIF/CIF en información fiscal,  ni en su país iva España. {explain} {url}'})
                        return error_messages
            if nif is not None and nif != '':
                if nif.startswith('ES'):
                    nif = nif[2:]
                # if is_valid_spanish_vat(nif) is False:
                #     error_messages.append({
                #         'error': f'El NIF/CIF del vendedor no es válido. <br>NIF/CIF encontrado: {nif}.<br><br>Recuerda que el NIF se toma primero en el campo NIF/CIF del vendedor y si no existe se toma el NIF/CIF del País IVA España. {explain} {url}'})
                #     return error_messages
                # else:
                return nif

def set_name_seller( seller, nom_emp, model, error_messages ):
        url = f'<a href="/sellers/{seller.shortname}/information">Ir a Información fiscal</a>'
        explain = f'Por favor completa la información que falta siguiendo el siguiente enlace. '
        models_40_chars = ['190', '180', '347']
        models_80_chars = ['303']
        models_125_chars = ['369']
        models_80_chars_separate = ['130','111','115','202','309', '390']

        if seller.legal_entity == 'self-employed':
            if (seller.first_name is None or seller.first_name == '') and (
                seller.last_name is None or seller.last_name == ''):
                error_messages.append({'error': f'El vendedor no tiene nombre y apellidos en su registro (campo Nombre y Apellidos del seller). {explain} {url}'})
                return error_messages
            else:
                if model in models_80_chars:
                    nom_emp = f"{seller.last_name.upper()}{' '}{seller.first_name.upper()}"
                    nom_emp = f"{nom_emp[:80]:<80}"
                elif model in models_40_chars:
                    nom_emp = f"{seller.last_name.upper()}{' '}{seller.first_name.upper()}"
                    nom_emp = f"{nom_emp[:40]:<40}"
                elif model in models_80_chars_separate:
                    nom_emp = f"{seller.last_name.upper()[:60]:<60}{seller.first_name.upper()[:20]:<20}"
                elif model in models_125_chars:
                    nom_emp = f"{seller.last_name.upper()}{' '}{seller.first_name.upper()}"
                    nom_emp = f"{nom_emp[:125]:<125}"
                return nom_emp
        else:
            if model in models_40_chars:
                nom_emp = f"{seller.name[:40]:<40}"
            elif model in models_125_chars:
                nom_emp = f"{seller.name[:125]:<125}"
            else:
                nom_emp = f"{seller.name[:80]:<80}"
            return nom_emp

def set_transactions_349(seller, year, nif, dict_json, error_messages):
    transactions = []

    last_page = dict_json.get('LAST_PAGE')
    if last_page is None or last_page == '':
        array_lastPage = [int(key.replace('_nombre_op', '').replace('doc_', '')[:-1]) for key in dict_json if "_nombre_op" in key]
        array_lastPage = sorted(array_lastPage, reverse=True)
        last_page = array_lastPage[0] + 1
        
    operations = 0
    for x in range(1, last_page + 1):
        for ix in range(1, 9):
            conjunto_actual = {}
            keys = [f"doc_{x}_cod_pais_op{ix}", f"doc_{x}_nif_op{ix}", f"doc_{x}_nombre_op{ix}", f"doc_{x}_clave_op{ix}", f"doc_{x}_base_imponible_op{ix}"]
            
            if keys[0] in dict_json: #Verificamos que exista al menos la 1a clave en el diccionario
                operations += 1
                valueid = dict_json.get(keys[0]) 
                conjunto_actual["cod_pais_op"] = str(valueid)
                
                valueid = dict_json.get(keys[1])
                conjunto_actual["nif_op"] = str(valueid) 

                valueid = special_char_eliminate(dict_json.get(keys[2], "")).upper() 
                conjunto_actual["nombre_op"] = str(valueid) 

                valueid = dict_json.get(keys[3]) 
                conjunto_actual["clave_op"] = str(valueid) 

                valueid = dict_json.get(keys[4])
                if valueid:
                    valueid = "{:.2f}".format(float(valueid.replace(',', '.'))).replace('.', '')
                    conjunto_actual["base_op"]  = valueid

                if any(value != '' for value in conjunto_actual.values()):
                    transactions.append(conjunto_actual)
    content_transactions = []

    for pais in transactions:

        if pais['nif_op'][:2] != pais['cod_pais_op']:
            if (pais['nif_op'][:1].isdigit() or pais['nif_op'][1:2].isdigit()):
                nif_op = f"{pais['cod_pais_op']}{pais['nif_op']}"
            else:
                nif_op = pais['nif_op']
        else:
            nif_op = pais['nif_op']
        if nif_op is not None:
            nif_op = nif_op.replace(' ', '')
            if len(nif_op) > 17:
                nif_op = nif_op[:17]

        if len(pais['nombre_op']) > 40:
            pais_op = pais['nombre_op'][:40]
        else:
            pais_op = pais['nombre_op']

        check_nif_country(seller, nif_op, nif_op, pais_op, error_messages)

        detalle = f"2349{year}{nif:<9}{' ' * 58}{nif_op:<17}{pais_op:<40}{pais['clave_op']}{pais['base_op']:0>13}{' ' * 32}{' ' * 17}{' ' * 40}{' ' * 265}"
        content_transactions.append(detalle)

    transactions_txt = ''.join(content_transactions)

    return transactions_txt

def set_transactions_369(seller , dict_json,info_page4_369, info_page6_369, num_negatives, error_messages):
    
    def format369(value):
        return "{:.2f}".format(float(value.replace(',', '.'))).replace('.', '')

    # JSON tabla1
    for x in range(1, 29):

        conjunto_actual = {}
        table1_keys = [f"doc_1_EM4_{x}", f"doc_1_IVA4_{x}", f"doc_1_BI4_{x}", f"doc_1_CIVA4_{x}"]

        if table1_keys[2] in dict_json and dict_json.get(table1_keys[2], "") != '':  # Verificamos que exista la clave de la base imponible en el diccionario y tenga un valor.

            valueid = dict_json.get(table1_keys[0], '').replace("GR", "EL").replace("UK", "GB")
            conjunto_actual["codPais"] = valueid

            valueid = dict_json.get(table1_keys[1], '').replace(',', '.')
            valueid = float(valueid) if valueid != '' else 0
            conjunto_actual["iva"] = "{:.2f}".format(valueid).replace('.', '')

            vat_query =VatRates.objects.get(country_code=conjunto_actual["codPais"])
            type_rate1 = 'S' if float(vat_query.vat_default) == valueid else 'R'
            conjunto_actual["tipoIVA"] = type_rate1

            valueid = dict_json.get(table1_keys[2], '')
            conjunto_actual["base"] = format369(valueid)

            valueid = dict_json.get(table1_keys[3], '')
            conjunto_actual["cuota"] = format369(valueid)


            if any(value != '' for value in conjunto_actual.values()):
                if float(conjunto_actual['cuota']) < 0:
                    num_negatives.append(conjunto_actual)
                elif float(conjunto_actual['cuota']) > 0:
                    info_page4_369.append(conjunto_actual)

    # JSON tabla2
    for x in range(2, 6):
        for ix in range(1, 29):
            conjunto_actual = {}
            table2_keys = [f"doc_{x}_CP_envio_{ix:02d}", f"doc_{x}_EM_consumo_{ix:02d}", f"doc_{x}_NIVA_if_{ix:02d}", f"doc_{x}_IVA_pag4_{ix:02d}", f"doc_{x}_BI_pag4_{ix:02d}", f"doc_{x}_Cuota_pag4_{ix:02d}" ]

            if table2_keys[4] in dict_json and dict_json.get(table2_keys[4], "") != '': # Verificamos que exista la clave de la base imponible en el diccionario y tenga un valor.
                
                valueid = dict_json.get(table2_keys[0], '').replace("GR", "EL").replace("UK", "GB")
                conjunto_actual["codPais_env"] = valueid

                valueid = dict_json.get(table2_keys[1], '').replace("GR", "EL").replace("UK", "GB")
                conjunto_actual["codPais"] = valueid

                valueid = dict_json.get(table2_keys[2], '')
                conjunto_actual["otroCod"] = valueid

                valueid = dict_json.get(table2_keys[3], '').replace(',', '.')
                valueid = float(valueid) if valueid != '' else 0
                conjunto_actual["iva"] = "{:.2f}".format(valueid).replace('.', '')

                vat_query =VatRates.objects.filter(country_code=conjunto_actual["codPais"]).first()
                type_rate2 = 'S' if float(vat_query.vat_default) == valueid else 'R'
                conjunto_actual["tipoIVA"] = type_rate2

                valueid = dict_json.get(table2_keys[4], '')
                conjunto_actual["base"] = format369(valueid)

                valueid = dict_json.get(table2_keys[5], '')
                conjunto_actual["cuota"] = format369(valueid)

                if conjunto_actual != {}:
                    if 'otroCod' not in conjunto_actual:
                        valueid = SellerVat.objects.filter(seller_id=seller.pk,
                                                            vat_country=conjunto_actual['codPais_env']).first()
                        if valueid:
                            conjunto_actual["otroCod"] = valueid.vat_country
                    if any(value != '' for value in conjunto_actual.values()):
                        if float(conjunto_actual['cuota']) < 0:
                            num_negatives.append(conjunto_actual)
                        elif float(conjunto_actual['cuota']) > 0:
                            info_page6_369.append(conjunto_actual)
    

    # -----------VALIDACIÓN DE DATOS --------------------------------

    cont = 0
    cont_otroCod = 0
    url = f'<a href="/sellers/{seller.shortname}/vat">Ir a Paises IVA</a>'
    for i in info_page6_369:
        cont += 1
        if 'otroCod' in i:
            if i['otroCod'] != '' and i['otroCod'] != 'None' and i['otroCod'] != 'NONE' and i['otroCod'] is not None: 
                cont_otroCod += 1

    if cont != cont_otroCod:
        error_messages.append({
            'error': f'Faltan algunos NIF/CIF en la tabla "Entregas de bienes expedidos desde otros EM distintos de España", por favor completa los NIF en su ficha de país IVA, {url}'})

def generate_TXT_MODEL_111(presented_model, seller):
    dict_json = json.loads(presented_model.json_pdf)
    # print(json.dumps(dict_json, indent=4))
    year = dict_json['ejercicio']
    period = dict_json['periodo']
    nom_emp = dict_json['nombre'].upper().replace(',', '').replace('.', '')
    nif = ''
    nif_muay = 'B67659607'
    version = '1.0'
    error_messages = []

    # NIF SELLER
    result = check_nif_seller(seller, error_messages)
    nif = result if not isinstance(result, list) else ''

    # NOMBRE DEL SELLER
    result = set_name_seller(seller, nom_emp, '111', error_messages)
    nom_emp = result if not isinstance(result, list) else ''

    nom_emp = special_char_eliminate(nom_emp)


    total_result = float(dict_json['casilla_30'].replace(',', '.'))

    iban = ''
    if total_result > 0:
        type_dec = 'I'
    else:
        type_dec = 'N'
    
    # Comprobamos si el modelo está en el rango de fechas para la domiciliación
    if still_in_limit_date_direct_debit(presented_model, 'aeat'):
        if presented_model.is_direct_debit is True:
            type_dec = 'U'
            iban = presented_model.iban

    # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
    cas_01 = f"{int(float(dict_json['casilla_01'].replace(',', '.')))}" if dict_json['casilla_01'] != '' else '0'
    cas_02 = format_numbers(dict_json['casilla_02'], 17, False)
    cas_03 = format_numbers(dict_json['casilla_03'], 17, False)
    cas_04 = f"{int(float(dict_json['casilla_04'].replace(',', '.')))}" if dict_json['casilla_04'] != '' else '0'
    cas_05 = format_numbers(dict_json['casilla_05'], 17, False)
    cas_06 = format_numbers(dict_json['casilla_06'], 17, False)
    cas_07 = f"{int(float(dict_json['casilla_07'].replace(',', '.')))}" if dict_json['casilla_07'] != '' else '0'
    cas_08 = format_numbers(dict_json['casilla_08'], 17, False)
    cas_09 = format_numbers(dict_json['casilla_09'], 17, False)
    total_liq_sum = format_numbers(dict_json['casilla_28'], 17, False)
    total_liq_ant = format_numbers(dict_json['casilla_29'], 17, False)
    total_liq_result = format_numbers(dict_json['casilla_30'],17, False)
    

    if error_messages:
        return error_messages

    template = (
        f"<T1110{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay}{' ' * 213}</AUX>"
        f"<T11101000>{' '}{type_dec}{nif:<9}{nom_emp}{year}{period}"
        f"{cas_01:0>8}{cas_02}{cas_03}{cas_04:0>8}{cas_05}{cas_06}"
        f"{cas_07:0>8}{cas_08}{cas_09}{'0' * 8}{'0' * 17}{'0' * 17}"
        f"{'0' * 8}{'0' * 17}{'0' * 17}{'0' * 8}{'0' * 17}{'0' * 17}"
        f"{'0' * 8}{'0' * 17}{'0' * 17}{'0' * 8}{'0' * 17}{'0' * 17}"
        f"{'0' * 8}{'0' * 17}{'0' * 17}"
        f"{total_liq_sum}{total_liq_ant}{total_liq_result}{' '}{' ' * 13}{' '}{iban:<34}{' ' * 389}{' ' * 13}"
        f"</T11101000>"
        f"</T1110{year}{period}0000>"
    )

    return template

def generate_TXT_MODEL_115(presented_model, seller):
        dict_json = json.loads(presented_model.json_pdf)
        # print(json.dumps(dict_json, indent=4))

        year = dict_json['page1_field2']
        period = dict_json['page1_field3']
        nom_emp = dict_json['page1_field7'].upper().replace(',', '').replace('.', '')
        nif = ''
        nif_muay = 'B67659607'
        version = '1.0'
        error_messages = []

        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''

        # NOMBRE DEL SELLER
        result = set_name_seller(seller, nom_emp, '115', error_messages)
        nom_emp = result if not isinstance(result, list) else ''

        nom_emp = special_char_eliminate(nom_emp)

        total_result = float(dict_json['page1_field15'].replace(',', '.'))

        if total_result > 0:
            type_dec = 'I'
        else:
            type_dec = 'N'
        
        iban = ''
        # Comprobamos si el modelo está en el rango de fechas para la domiciliación
        if still_in_limit_date_direct_debit(presented_model, 'aeat'):
            if presented_model.is_direct_debit is True:
                type_dec = 'U'
                iban = presented_model.iban

        cas_01 = f"{int(float(dict_json['page1_field10'].replace(',', '.')))}" if dict_json['page1_field10'] != '' else '0'
        cas_02 = format_numbers(dict_json['page1_field11'], 17, False)
        cas_03 = format_numbers(dict_json['page1_field12'], 17, False)
        cas_04 = format_numbers(dict_json['page1_field13'], 17, False)
        cas_05 = format_numbers(dict_json['page1_field15'], 17, False)

        if error_messages:
            return error_messages

        template = (
            f"<T1150{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay}{' ' * 213}</AUX>"
            f"<T11501000>{' '}{type_dec}{nif}{nom_emp}{year}{period}{cas_01:0>15}{cas_02}{cas_03}{cas_04}{cas_05}"
            f"{' '}{' ' * 13}{iban:<34}{' ' * 236}{' ' * 13}</T11501000>"
            f"</T1150{year}{period}0000>"
        )
        return template

def generate_TXT_MODEL_130(presented_model, seller):
    dict_json = json.loads(presented_model.json_pdf)
    # print(json.dumps(dict_json, indent=4))

    year = dict_json['ejercicio']
    period = dict_json['periodo']
    nom_emp = dict_json['nombre'].upper().replace(',', '').replace('.', '')
    nif = ''
    error_messages = []

    # NIF SELLER
    result = check_nif_seller(seller, error_messages)
    nif = result if not isinstance(result, list) else ''

    # NOMBRE DEL SELLER
    result = set_name_seller(seller, nom_emp, '130', error_messages)
    nom_emp = result if not isinstance(result, list) else ''

    nom_emp = special_char_eliminate(nom_emp)

    result = float(dict_json['campo_19'].replace(',', '.'))

    cas_1 = format_numbers(dict_json['campo_01'], 17, False)
    cas_2 = format_numbers(dict_json['campo_02'], 17, False)
    cas_3 = format_numbers(dict_json['campo_03'], 17, False)
    cas_4 = format_numbers(dict_json['campo_04'], 17, False)
    cas_5 = format_numbers(dict_json['campo_05'], 17, False)
    cas_6 = format_numbers(dict_json['campo_06'], 17, False)
    cas_7 = format_numbers(dict_json['campo_07'], 17, False)
    cas_12 = format_numbers(dict_json['campo_12'], 17, False)
    cas_14 = format_numbers(dict_json['campo_14'], 17, False)
    cas_15 = format_numbers(dict_json['campo_15'], 17, False)
    cas_17 = format_numbers(dict_json['campo_17'], 17, False)
    cas_19 = format_numbers(dict_json['campo_19'], 17, False)

    iban = ''

    if result <= 0:
        type_declaration = 'N'
    if result > 0:
        type_declaration = 'I'

    # Comprobamos si el modelo está en el rango de fechas para la domiciliación
    if still_in_limit_date_direct_debit(presented_model, 'aeat'):
        if presented_model.is_direct_debit is True:
            type_declaration = 'U'
            iban = presented_model.iban

    version = '1.0'
    nif_muay = 'B67659607'

    if error_messages:
        return error_messages

    # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{zfill(INT)}(rellenar con ceros a la izquierda)
    template = (
        f"<T1300{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay:<9}{' ' * 213}</AUX>"
        f"<T13001000>{' '}{type_declaration}{nif:<9}{nom_emp}{year}{period}{cas_1}{cas_2}{cas_3}{cas_4}{cas_5}{cas_6}{cas_7}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{cas_12}{'0' * 17}{cas_14}{cas_15}{'0' * 17}{cas_17}{'0' * 17}{cas_19}{' '}{' ' * 13}{iban:<34}{' ' * 96}{' ' * 13}</T13001000>"
        f"</T1300{dict_json['ejercicio']}{dict_json['periodo']}0000>"
    )
    return template

def generate_TXT_MODEL_180(presented_model, seller):
    dict_json = json.loads(presented_model.json_pdf)
    # print(json.dumps(dict_json, indent=4))
    year = dict_json['ejercicio_pag1']
    telephone = '*********'
    relation = 'MUAYTAX ADVISORS SL'
    nom_emp = dict_json['Nombre_seller_pag1'].upper().replace(',', '').replace('.', '')
    nif = ''
    error_messages = []

    # NIF SELLER
    result = check_nif_seller(seller, error_messages)
    nif = result if not isinstance(result, list) else ''

    # NOMBRE DEL SELLER
    result = set_name_seller(seller, nom_emp, '180', error_messages)
    nom_emp = result if not isinstance(result, list) else ''

    nom_emp = special_char_eliminate(nom_emp)

    total_op = int(dict_json['total_perceptores_pag1'])

    total_percp = format_numbers(dict_json['base_total_retenciones_pag1'], 16, True)
    total_ret = format_numbers(dict_json['IVA_total_modelo_pag1'], 15, False)

    transactions = []
    for x in range(2, 10):
        for ix in range(1, 5):
            conjunto_actual = {}
            keys = [f"NIF_perceptor_{ix}_pag{x}", f"nombre_seller_{ix}_pag{x}", f"provincia_codigo_{ix}_pag{x}", f"Modalidad_{ix}_pag{x}",
                    f"base_retenciones_{ix}_pag{x}", f"porcentaje_retencion_{ix}_pag{x}", f"retenciones_a_cuenta_{ix}_pag{x}", f"situacion_codigo_{ix}_pag{x}",
                    f"referencia_catastral_{ix}_pag{x}",f"tipo_de_via_{ix}_pag{x}", f"nombre_via_{ix}_pag{x}", f"tipo_num_{ix}_pag{x}",
                    f"num_casa_{ix}_pag{x}", f"calif_num_{ix}_pag{x}", f"bloque_{ix}_pag{x}", f"portal_{ix}_pag{x}", f"escalera_{ix}_pag{x}",
                    f"planta_{ix}_pag{x}", f"puerta_{ix}_pag{x}", f"complemento_domicilio_{ix}_pag{x}", f"localidad_{ix}_pag{x}", f"nombre_municipio_{ix}_pag{x}",
                    f"cod_municipio_{ix}_pag{x}", f"cod_provincia_{ix}_pag{x}", f"cod_postal_{ix}_pag{x}"]


            if keys[4] in dict_json and dict_json.get(keys[4], "") != '': #Verificamos que exista al menos la clave de la base retenciones en el diccionario y que además tenga valor

                valueid = dict_json.get(keys[0], '')
                conjunto_actual["nif_op"] = valueid[2:] if valueid.startswith('ES') else valueid

                valueid = special_char_eliminate(dict_json.get(keys[1], "")).upper()
                conjunto_actual["nombre_op"] = valueid[:40] if len(valueid) > 40 else valueid

                valueid = dict_json.get(keys[2], '')
                conjunto_actual["cod_prov"] = valueid

                valueid = dict_json.get(keys[3], '')
                conjunto_actual["modalidad"] = valueid

                valueid = dict_json.get(keys[4], '')
                conjunto_actual["base_op"] = valueid

                valueid = dict_json.get(keys[5], '')
                conjunto_actual["porcentaje_op"] = valueid

                valueid = dict_json.get(keys[6], '')
                conjunto_actual["reten_op"] = valueid

                valueid = dict_json.get(keys[7], '')
                conjunto_actual["situacion"] = valueid

                valueid = dict_json.get(keys[8], '')
                conjunto_actual["catastral"] = valueid

                valueid = dict_json.get(keys[9], '')
                conjunto_actual["tipo_via"] = valueid

                valueid = dict_json.get(keys[10], '')
                conjunto_actual["nombre_via"] = valueid

                valueid = dict_json.get(keys[11], '')
                conjunto_actual["tipo_num"] = valueid

                valueid = dict_json.get(keys[12], '')
                conjunto_actual["num_casa"] = valueid

                valueid = dict_json.get(keys[13], '')
                conjunto_actual["calif_num"] = valueid

                valueid = dict_json.get(keys[14], '')
                conjunto_actual["bloque"] = valueid

                valueid = dict_json.get(keys[15], '')
                conjunto_actual["portal"] = valueid

                valueid = dict_json.get(keys[16], '')
                conjunto_actual["esc"] = valueid

                valueid = dict_json.get(keys[17], '')
                conjunto_actual["planta"] = valueid

                valueid = dict_json.get(keys[18], '')
                conjunto_actual["puerta"] = valueid

                valueid = dict_json.get(keys[19], '')
                conjunto_actual["complemento"] = valueid

                valueid = dict_json.get(keys[20], '')
                conjunto_actual["localidad"] = valueid
                
                valueid = dict_json.get(keys[21], '')
                conjunto_actual["nom_mun"] = valueid

                valueid = dict_json.get(keys[22], '')
                conjunto_actual["cod_mun"] = valueid

                valueid = dict_json.get(keys[23], '')
                conjunto_actual["cod_prov"] = valueid

                valueid = dict_json.get(keys[24], '')
                conjunto_actual["cod_postal"] = valueid


                if any(value != '' for value in conjunto_actual.values()):
                    transactions.append(conjunto_actual)


    # print(json.dumps(transactions, indent=4))
    content_transactions = []
    for op in transactions:
        nif_op = op.get('nif_op', '')
        nombre_op = op.get('nombre_op', '')
        cod_prov = op.get('cod_prov', '')
        modalidad = op.get('modalidad', '')
        base_op = format_numbers(op.get('base_op', '0'), 14, True)
        porcentaje_op = format_numbers(op.get('porcentaje_op', '0'), 4, False)
        reten_op = format_numbers(op.get('reten_op', '0'), 13, False )
        situacion = op.get('situacion', '')
        catastral = op.get('catastral', '')
        tipo_via = op.get('tipo_via', '')
        nombre_via = op.get('nombre_via', '')
        tipo_num = op.get('tipo_num', '')
        num_casa = op.get('num_casa', '')
        calif_num = op.get('calif_num', '')
        bloque = op.get('bloque', '')
        portal = op.get('portal', '')
        esc = op.get('esc', '')
        planta = op.get('planta', '')
        puerta = op.get('puerta', '')
        complemento = op.get('complemento', '')
        localidad = op.get('localidad', '')
        nom_mun = op.get('nom_mun', '')
        cod_mun = op.get('cod_mun', '')
        cod_prov = op.get('cod_prov', '')
        cod_postal = op.get('cod_postal', '')

        # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
        detalle = (
            f"2180{year}{nif:<9}{nif_op:<9}{' ' * 9}{nombre_op:<40}{cod_prov}{modalidad}{base_op}{porcentaje_op}{reten_op}{'0' * 4}{situacion}{catastral:<20}"
            f"{tipo_via:<5}{nombre_via:<50}{tipo_num:<3}{num_casa.zfill(5)}{calif_num:<3}{bloque:<3}{portal:<3}{esc:<3}{planta:<3}{puerta:<3}{complemento:<40}{localidad:<30}"
            f"{nom_mun:<30}{cod_prov}{cod_mun}{cod_prov:<2}{cod_postal:<5}{' ' * 173}"
        )
        content_transactions.append(detalle)
    transactions_txt = ''.join(content_transactions)

    if error_messages:
        return error_messages

    template = (
        f"1180{year}{nif:<9}{nom_emp}T{telephone}{relation:<40}{'0' * 13}{' ' * 2}{'0' * 13}{total_op:0>9}{total_percp}{total_ret}{' ' * 62}{' ' * 263}"
        f"{transactions_txt}"
    )
    return template

    # print(template)

def generate_TXT_MODEL_184(presented_model, seller):
        dict_json = json.loads(presented_model.json_pdf)

        def format_field(field, is_numeric, length):
            if is_numeric:
                formatted_field = str(field)
                if any(c in formatted_field for c in ['.', ',']):
                    formatted_field = str("{:.2f}".format(float(formatted_field)))
                    formatted_field = formatted_field.replace(',', '').replace('.', '').replace('-', '')
                formatted_field = formatted_field.zfill(length)
            else:
                formatted_field = field.upper()  # Convertir a mayúsculas
                formatted_field = unicodedata.normalize('NFD', formatted_field)
                formatted_field = formatted_field.encode('ascii', 'ignore').decode('utf-8')
                formatted_field = formatted_field.ljust(length)

                # Truncar si el campo excede la longitud especificada
            return formatted_field[:length]

        def format_percent(field, length):
            formatted_field = str(field)
            if any(c in formatted_field for c in ['.', ',']):
                formatted_field = str("{:.4f}".format(float(formatted_field)))
                formatted_field = formatted_field.replace(',', '').replace('.', '').replace('-', '')
            formatted_field = formatted_field.zfill(length)

            return formatted_field[:length]

        # registro 1 de declarante
        year = format_field(dict_json.get('EJERCICIO_PAG1', ''), True, 4)
        nif = format_field(dict_json.get('NIF1_PAG1', '').replace('ES', ''), False, 9)
        entity = format_field(dict_json.get('DENOMINACION_ENTIDAD_PAG1', ''), False, 40)
        phone = '*********'
        full_name = format_field(dict_json.get('SELLER1_PAG1', ''), False, 40)
        sequencial = '1840000000000'
        compl_sustit = '  '
        prev_declare = '0' * 13
        total_partners = format_field(dict_json.get('TOTAL_REGISTROS_PAG1', ''), True, 9)
        rent_out = '1AUS' + format_percent(dict_json.get('PORCENTAJE_PAG1', ''), 5)
        total_import = format_field(dict_json.get('IMPORTE_PAG1', ''), True, 15)
        representative_nif = format_field(dict_json.get('NIF2_PAG1', ''), False, 9)
        full_name_representative = format_field(dict_json.get('SELLER2_PAG1', ''), False, 40)
        blank_space = ' ' * 280

        template = (
            f"1184{year}{nif}{entity}T{phone}{full_name}{sequencial}{compl_sustit}{prev_declare}{total_partners}00"
            f"{rent_out} {total_import}{representative_nif}{full_name_representative}{blank_space}"
        )

        # registro 2 de declarado
        model184 = PresentedM184.objects.filter(
            seller=seller,
            year=year,
            is_processed=True
        ).first()

        if model184:
            rents = model184.accounting_information.all()

            def generate_rents_template(dict_json, page_num, prefix, entity, year, nif):
                key = format_field(dict_json.get(f'CLAVE{prefix}_PAG{page_num}', ''), False, 1)
                subkey = format_field(dict_json.get(f'SUBCLAVE{prefix}_PAG{page_num}', ''), False, 2)
                country = format_field(dict_json.get(f'PAIS{prefix}_PAG{page_num}', ''), False,
                                       2) if subkey == '02' else '  '
                reg = format_field(dict_json.get(f'REGIMEN{prefix}_PAG{page_num}', ''), True, 1)
                activ = format_field(dict_json.get(f'TIPO_ACTIVIDAD{prefix}_PAG{page_num}', ''), True, 1)
                iae = format_field(dict_json.get(f'EPIGRAFE{prefix}_PAG{page_num}', ''), True, 4)

                sign = 'N' if float(dict_json.get(f'INGRESOS{prefix}_PAG{page_num}', '')) < 0 else ' '
                ingreso = format_field(
                    dict_json.get(f'INGRESOS{prefix}_PAG{page_num}', ''), True, 13)

                sign_rent = 'N' if float(dict_json.get(f'RENTA_ATRIBUIBLE{prefix}_PAG{page_num}', '')) < 0 else ' '
                atrib_rent = format_field(dict_json.get(f'RENTA_ATRIBUIBLE{prefix}_PAG{page_num}', ''), True, 13)

                gasto_personal = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE1_PAG{page_num}', ''), True, 12)
                consumo_explot = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE2_PAG{page_num}', ''), True, 12)
                tributo_deducible = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE3_PAG2{page_num}', ''), True, 12)
                arrendamiento = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE4_PAG{page_num}', ''), True, 12)
                reparacion = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE5_PAG{page_num}', ''), True, 12)
                servic_prof = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE6_PAG{page_num}', ''), True, 12)
                sumin = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE7_PAG{page_num}', ''), True, 12)
                gasto_financ = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE8_PAG{page_num}', ''), True, 12)
                amort = '0' * 12
                provision = '0' * 12
                otros_gastos = format_field(
                    dict_json.get(f'DETALLES{prefix}_AE11_PAG{page_num}', ''), True, 10)

                return (
                    f"2184{year}{nif}{nif}{' ' * 9}{entity}E{key}{subkey}{country}{reg}{activ}{iae}{' ' * 29}{'0' * 34}"
                    f"{sign}{ingreso}{'0' * 12}{sign_rent}{atrib_rent}{'0' * 5} {'0' * 49}{' ' * 20}"
                    f"{gasto_personal}{consumo_explot}{tributo_deducible}{arrendamiento}{reparacion}{servic_prof}"
                    f"{sumin}{gasto_financ}{amort}{provision}{otros_gastos} "
                    f"{'0' * 104}"
                )

            pairs_of_rents = [rents[i:i + 2] for i in range(0, rents.count(), 2)]

            total_pages = 9
            for page_num in range(2, total_pages + 1):
                pair_index = page_num - 2
                if pair_index < len(pairs_of_rents):
                    pair = pairs_of_rents[pair_index]
                    if len(pair) > 0:
                        template += generate_rents_template(dict_json, page_num, '1', entity, year, nif)
                    if len(pair) > 1:
                        template += generate_rents_template(dict_json, page_num, '2', entity, year, nif)

            page = 10
            x = 1
            partners = model184.partners.all()
            for i, partner in enumerate(partners, start=1):
                nif_miembro = format_field(dict_json.get(f"NIF{x}_PAG{page}", ''), False, 9)
                nif_represent = format_field(dict_json.get(f"NIF_REPRESENTANTE{x}_PAG{page}", ''), False, 9)
                name = format_field(dict_json.get(f"SELLER{x}_PAG{page}", ''), False, 40)
                province = format_field(dict_json.get(f"PROVINCIA{x}_PAG{page}", ''), False, 2)
                clave = format_field(dict_json.get(f"CLAVE_TIPO{x}_PAG{page}", ''), True, 1)
                is_member = format_field(dict_json.get(f"X{x}_PAG{page}", ''), False, 1)
                day_numer = format_field(dict_json.get(f"DIAS{x}_PAG{page}", ''), True, 3)
                percent_part = format_percent(dict_json.get(f"PARTICIPACION{x}_PAG{page}", ''), 7)
                clav = format_field(dict_json.get(f"CLAVE{x}_PAG{page}", ''), False, 1)
                subclav = '0' * 2
                sign = 'N' if float(dict_json.get(f"IMPORTE{x}_PAG{page}", '')) < 0 else ' '
                import_partner = format_field(dict_json.get(f"IMPORTE{x}_PAG{page}", ''), True, 12)
                reduc = '0' * 11
                domicilio = format_field(dict_json.get(f"DOMICILIO{x}_PAG{page}", ''), False, 40)
                provis_reduc = '0' * 12

                template += (
                    f"2184{year}{nif}{nif_miembro}{nif_represent}{name}S{province}{' ' * 2}{clave}"
                    f"{is_member}{day_numer}{percent_part}{clav}{subclav}{sign}{import_partner}{reduc}"
                    f"{domicilio}{provis_reduc}00{' ' * 21}{'0' * 21} {'0' * 13}{' ' * 271}"
                )
                x += 1
                if x == 5:
                    x = 1
                    page += 1

        return template

def generate_TXT_MODEL_190(presented_model, seller):
        dict_json = json.loads(presented_model.json_pdf)
        # print(json.dumps(dict_json, indent=4))
        year = dict_json['doc_1_ejercicio']
        telephone = '*********'
        relation = 'MUAYTAX ADVISORS SL'
        mail = '<EMAIL>'
        error_messages = []
        nif = ''

        nom_emp = dict_json['doc_1_nombre'].upper().replace(',', '').replace('.', '')

        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''

        # NOMBRE DEL SELLER
        result = set_name_seller(seller, nom_emp, '190', error_messages)
        nom_emp = result if not isinstance(result, list) else ''

        nom_emp = special_char_eliminate(nom_emp)
        if len(nom_emp) > 40:
            nom_emp = nom_emp[:40]

        total_op = int(dict_json['doc_1_total_perceptores'])

        total_percp = format_numbers(dict_json['doc_1_importe_percepciones'], 16, True)
        total_ret = format_numbers(dict_json['doc_1_importe_retenciones'], 15, False)

        transactions = []
        for x in range(2, 10):
            for ix in range(1, 3):
                conjunto_actual = {}
                keys = [f"doc_{x}_nif_op_{ix}", f"doc_{x}_nombre_op_{ix}", f"doc_{x}_provincia_op_{ix}",f"doc_{x}_clave_op_{ix}", f"doc_{x}_subclave_op_{ix}",
                        f"doc_{x}_base_op_{ix}", f"doc_{x}_irpf_op_{ix}", f"doc_{x}_ejercicio_op_{ix}", f"doc_{x}_nacimiento_op_{ix}", f"doc_{x}_sit_familiar_op_{ix}",
                        f"doc_{x}_nif_conyuge_op_{ix}", f"doc_{x}_titularidad_op_{ix}",f"doc_{x}_discapacidad_op_{ix}", f"doc_{x}_movilidad_op_{ix}", f"doc_{x}_contrato_op_{ix}" ]
                
                if keys[5] in dict_json and dict_json.get(keys[5], "") != '': #Verificamos que exista al menos la clave de la base retenciones en el diccionario y tenga valor


                    valueid = dict_json.get(keys[0], '')
                    conjunto_actual["nif_op"] = valueid[2:] if valueid.startswith('ES') else valueid

                    valueid = special_char_eliminate(dict_json.get(keys[1], '')).upper()
                    conjunto_actual["nombre_op"] = valueid[:40] 

                    valueid = dict_json.get(keys[2], '')
                    conjunto_actual["provincia"] = valueid

                    valueid = dict_json.get(keys[3], '')
                    conjunto_actual["clave"] = valueid

                    valueid = dict_json.get(keys[4], '')
                    conjunto_actual["subclave"] = valueid if valueid != '' else '00'

                    valueid = dict_json.get(keys[5], '')
                    conjunto_actual["percp_int_op"] = valueid

                    valueid = dict_json.get(keys[6], '')
                    conjunto_actual["rent_prac_op"] = valueid

                    valueid = dict_json.get(keys[7], '')
                    conjunto_actual["ejercicio"] = valueid

                    valueid = dict_json.get(keys[8], '')
                    conjunto_actual["nacimiento"] = valueid if valueid != '' else '0000'

                    valueid = dict_json.get(keys[9], '')
                    conjunto_actual["sit_familiar"] = valueid if valueid != '' else '0'

                    valueid = dict_json.get(keys[10], '')
                    conjunto_actual["nif_conyuge"] = valueid

                    valueid = dict_json.get(keys[11], '')
                    conjunto_actual["titularidad"] = valueid if valueid != '' else '0'

                    valueid = dict_json.get(keys[12], '')
                    conjunto_actual["discapacidad"] = valueid if valueid != '' else '0'

                    valueid = dict_json.get(keys[13], '')
                    conjunto_actual["movilidad"] = valueid if valueid != '' else '0'

                    valueid = dict_json.get(keys[14], '')
                    conjunto_actual["contrato"] = valueid if valueid != '' else '0'

                    if any(value != '' for value in conjunto_actual.values()):
                        transactions.append(conjunto_actual)
        

        # print(json.dumps(transactions, indent=4))
        content_transactions = []
        for op in transactions:
            nombre_op = op.get('nombre_op', '')
            nif_op = op.get('nif_op', '')
            provincia = op.get('provincia', '')
            clave = op.get('clave', '')
            subclave = op.get('subclave', '')
            percp = format_numbers(op.get('percp_int_op', '0'), 14, True)
            rent = format_numbers(op.get('rent_prac_op', '0'), 13, False)
            nacimiento = op.get('nacimiento', '')
            sit_familiar = op.get('sit_familiar', '')
            nif_conyuge = op.get('nif_conyuge', '')
            discapacidad = op.get('discapacidad', '')
            titularidad = op.get('titularidad', '')
            movilidad = op.get('movilidad', '')
            contrato = op.get('contrato', '')

            exc_entr = '0'
            if float(op.get('percp_int_op', '0')) > 50000:
                exc_entr = '1'
            
            detalle = f"2190{year}{nif:<9}{nif_op:<9}{' ' * 9}{nombre_op:<40}{provincia}{clave}{subclave}{percp}{rent}{' '}{'0' * 39}{'0' * 4}{'0'}{nacimiento}{sit_familiar}{nif_conyuge:<9}{discapacidad}{contrato}{titularidad}{movilidad}{'0' * 84}{' '}{'0' * 26}{' '}{'0' * 39}{'0'}{'0' * 65}{exc_entr}{' ' * 112}"
            content_transactions.append(detalle)
        transactions_txt = ''.join(content_transactions)

        if error_messages:
            return error_messages

        # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) ||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
        template = (
            f"1190{year}{nif:<9}{nom_emp}T{telephone}{relation:<40}{'0' * 13}{' ' * 2}{'0' * 13}{total_op:0>9}{total_percp}{total_ret}{mail:<50}{' ' * 262}{' ' * 13}"
            f"{transactions_txt}"
        )
        # print(template)
        return template

def generate_TXT_MODEL_202(presented_model, seller):
    dict_json = json.loads(presented_model.json_pdf)
    # print(json.dumps(dict_json, indent=4))

    year = dict_json['pag0_year']
    period = dict_json['pag0_period']
    nom_emp = dict_json['pag0_name'].upper().replace(',', '').replace('.', '')
    nif = ''
    iban = ''
    version ='1.0'
    nif_muay = 'B67659607'
    error_messages = []

    # NIF SELLER
    result = check_nif_seller(seller, error_messages)
    nif = result if not isinstance(result, list) else ''

    #NOMBRE DEL SELLER
    result = set_name_seller (seller, nom_emp, '202', error_messages)
    nom_emp = result if not isinstance(result, list) else ''

    nom_emp = special_char_eliminate(nom_emp)

    total_result = float(dict_json['pag1_total'].replace(',', '.'))

    if total_result > 0:
        type_dec = 'I'
    else:
        type_dec = 'N'

    #Comprobamos si el modelo está en el rango de fechas para la domiciliación
    if still_in_limit_date_direct_debit(presented_model, 'aeat'):
        if presented_model.is_direct_debit is True:
            type_dec = 'U'
            iban = presented_model.iban
        
    # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
    cnae = dict_json['pag0_cnae']
    per_imp = dict_json['pag0_per_imp'].replace('/', '')
    type_grav = dict_json['pag0_dat_ad_10'].replace (",00%", "")
    type_grav = type_grav if len(type_grav) > 1 else f"0{type_grav}"
    cas_01 = format_numbers(dict_json['pag0_ca01'], 17, False)
    cas_03 = format_numbers(dict_json['pag0_ca03'], 17, False)

    if error_messages:
        return error_messages
    
    template = (
        f"<T2020{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay:<9}{' ' * 213}</AUX>"
        f"<T20201000>{' '}{type_dec}{nif:<9}{nom_emp}{year}{period}{per_imp}{cnae:0>4}{' '}{' '}{' '}{' '}{' '}{'0'}"
        f"{type_grav:<5}{'0'}{' '}{cas_01}{'0' * 17}{cas_03}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}"
        f"{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}"
        f"{'0' * 17}{'0' * 17}{'0' * 17}{'A'}{' '}{' '}{'X'}{' ' * 172}</T20201000>"
        f"<T20202000>{' '}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}"
        f"{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{' '}{' ' * 22}"
        f"{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{'0' * 17}{' '}"
        f"{' ' * 13}{iban:<34}{' ' * 101}{' ' * 13}</T20202000>"
        f"</T2020{year}{period}0000>"
    )

    return template
    
def generate_TXT_MODEL_303(presented_model, seller):
    dict_json = json.loads(presented_model.json_pdf)
    # print(json.dumps(dict_json, indent=4))

    year = dict_json['ejercicio']
    period = dict_json['periodo']
    nom_emp = dict_json['nombre'].upper().replace(',', '').replace('.', '')
    nif = ''
    error_messages = []

    # NIF SELLER
    result = check_nif_seller(seller, error_messages)
    nif = result if not isinstance(result, list) else ''

    # NOMBRE DEL SELLER
    result = set_name_seller(seller, nom_emp, '303', error_messages)
    nom_emp = result if not isinstance(result, list) else ''

    nom_emp = special_char_eliminate(nom_emp)

    swift = ''
    iban = ''
    sepa = '0'
    type_dec = ''

    total_result = float(dict_json['campo_71'].replace(',', '.'))

    if total_result > 0:
        type_dec = 'I'
    if total_result == 0:
        type_dec = 'N'
    if total_result < 0:
        if period != '4T':
            type_dec = 'C'
        else:
            if presented_model.choice_seller == 'devolution':
                type_dec = 'D'
                swift = presented_model.swift
                iban = presented_model.iban
                country = iban[:2].upper()
                if country == 'ES':
                    sepa = '1'
                    swift = ''
                else:
                    country_value = Country.objects.filter(iso_code=country).first()
                    if country_value is not None:
                        if country_value.is_european_union == True:
                            sepa = '2'
                        else:
                            sepa = '3'

            elif presented_model.choice_seller == 'compensate':
                type_dec = 'C'

    # Comprobamos si el modelo está en el rango de fechas para la domiciliación
    if still_in_limit_date_direct_debit(presented_model, 'aeat'):
        if presented_model.is_direct_debit is True:
            type_dec = 'U'
            iban = presented_model.iban
            
    json_formated = {}

    for fields in dict_json:
        value = dict_json[fields]
        if fields.startswith('campo_'):
            value = format_numbers(value, 17, False)
            json_formated[fields] = value

    cas_150 = json_formated['campo_150']
    cas_151 = '00000'
    cas_152 = json_formated['campo_152']
    cas_01 = json_formated['campo_1']
    cas_02 = '00400'
    cas_03 = json_formated['campo_3']
    cas_153 = json_formated['campo_153']
    cas_154 = '00500'
    cas_155 = json_formated['campo_155']
    cas_04 = json_formated['campo_4']
    cas_05 = '01000'
    cas_06 = json_formated['campo_6']
    cas_07 = json_formated['campo_7']
    cas_08 = '02100'
    cas_09 = json_formated['campo_9']
    cas_10 = json_formated['campo_10']
    cas_11 = json_formated['campo_11']
    cas_12 = json_formated['campo_12']
    cas_13 = json_formated['campo_13']
    cas_14 = json_formated['campo_14']
    cas_15 = json_formated['campo_15']
    cas_156 = json_formated['campo_156']
    cas_157 = '00175'
    cas_158 = json_formated['campo_158']
    cas_16 = json_formated['campo_16']
    cas_17 = '00500'
    cas_18 = json_formated['campo_18']
    cas_168 = '' # casillas nuevas 
    cas_169 = '' # casillas nuevas 
    cas_170 = '' # casillas nuevas
    cas_165 = '' # casillas nuevas
    cas_166 = '' # casillas nuevas
    cas_167 = '' # casillas nuevas
    cas_19 = json_formated['campo_19']
    cas_20 = '00140'
    cas_21 = json_formated['campo_21']
    cas_22 = json_formated['campo_22']
    cas_23 = '00520'
    cas_24 = json_formated['campo_24']
    cas_25 = json_formated['campo_25']
    cas_26 = json_formated['campo_26']
    cas_27 = json_formated['campo_27']
    cas_28 = json_formated['campo_28']
    cas_29 = json_formated['campo_29']
    cas_30 = json_formated['campo_30']
    cas_31 = json_formated['campo_31']
    cas_32 = json_formated['campo_32']
    cas_33 = json_formated['campo_33']
    cas_34 = json_formated['campo_34']
    cas_35 = json_formated['campo_35']
    cas_36 = json_formated['campo_36']
    cas_37 = json_formated['campo_37']
    cas_38 = json_formated['campo_38']
    cas_39 = json_formated['campo_39']
    cas_40 = json_formated['campo_40']
    cas_41 = json_formated['campo_41']
    cas_42 = json_formated['campo_42']
    cas_43 = json_formated['campo_43']
    cas_44 = json_formated['campo_44']
    cas_45 = json_formated['campo_45']
    cas_46 = json_formated['campo_46']
    cas_59 = json_formated['campo_59']
    cas_60 = json_formated['campo_60']
    cas_120 = json_formated['campo_120']
    cas_122 = json_formated['campo_122']
    cas_123 = json_formated['campo_123']
    cas_124 = json_formated['campo_124']
    cas_62 = json_formated['campo_62']
    cas_63 = json_formated['campo_63']
    cas_74 = json_formated['campo_74']
    cas_75 = f"{'0' * 17}"
    cas_76 = json_formated['campo_76']
    cas_64 = json_formated['campo_64']
    cas_65 = '10000'
    cas_66 = json_formated['campo_66']
    cas_77 = json_formated['campo_77']
    cas_110 = json_formated['campo_110']
    cas_78 = json_formated['campo_78']
    cas_87 = json_formated['campo_87']
    cas_68 = json_formated['campo_68']
    cas_69 = json_formated['campo_69']
    cas_70 = json_formated['campo_70']
    cas_109 = json_formated['campo_109']
    cas_71 = json_formated['campo_71']
    version = '1.0'
    nif_muay = 'B67659607'
    exonerado = '0' if period != '4T' else '2'

    if error_messages:
        return error_messages

    # print(json.dumps(json_formated, indent=4))

    # NOTE :REVISAR EL TEMA DE LOS 2 "0" PORQUE VARÍA SU VALOR CUANDO EL TRIENIO ES 4T
    # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
    if int(year) < 2024 or (int(year)== 2024 and period not in ['4T', '3T']):
        # ---->> VERSION DEL TXT 1.05 <<----
        template = (
            f"<T3030{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay:<9}{' ' * 213}</AUX>"
            f"<T30301000>{' '}{type_dec}{nif:<9}{nom_emp}{year}{period}{'2' * 2}3{'2' * 6}{' ' * 9}2{exonerado}{'0'}{cas_150}{cas_151}{cas_152}{cas_01}{cas_02}{cas_03}{cas_153}{cas_154}"
            f"{cas_155}{cas_04}{cas_05}{cas_06}{cas_07}{cas_08}{cas_09}{cas_10}{cas_11}{cas_12}{cas_13}{cas_14}{cas_15}{cas_156}{cas_157}{cas_158}{cas_16}{cas_17}{cas_18}"
            f"{cas_19}{cas_20}{cas_21}{cas_22}{cas_23}{cas_24}{cas_25}{cas_26}{cas_27}{cas_28}{cas_29}{cas_30}{cas_31}{cas_32}{cas_33}{cas_34}{cas_35}{cas_36}{cas_37}{cas_38}"
            f"{cas_39}{cas_40}{cas_41}{cas_42}{cas_43}{cas_44}{cas_45}{cas_46}"
            f"{' ' * 600}{' ' * 13}</T30301000>"
            f"<T30303000>{cas_59}{cas_60}{cas_120}{cas_122}{cas_123}{cas_124}{cas_62}{cas_63}{cas_74}{cas_75}{cas_76}{cas_64}{cas_65}{cas_66}{cas_77}{cas_110}{cas_78}{cas_87}"
            f"{cas_68}{cas_69}{cas_70}{cas_109}{cas_71}"
            f"{' '}{' '}{' ' * 13}{' ' * 35}{' ' * 52}{' ' * 513}</T30303000>"
            f"<T303DID00>{swift:<11}{iban:<34}{' ' * 70}{' ' * 35}{' ' * 30}{' ' * 2}{sepa}{' ' * 617}</T303DID00>"
            f"</T3030{year}{period}0000>"
        )
        # ---->> VERSION DEL TXT 1.05 <<----

    else:
        # TODO : CAMBIAR LA VERSIÓN DEL TEMAPLTE A 2.01 EN OCTUBRE Y HACER PRUEBAS EN LA AEAT

        #CAMBIOS EN CASILLAS A PARTIR DE 4T 2024
        if (period == '4T' and int(year) == 2024) or int(year) > 2024:
            cas_168 = cas_16
            cas_169 = cas_17
            cas_170 = cas_18
            
            cas_16 = f"{'0' * 17}"
            cas_17 = '00100'
            cas_18 = f"{'0' * 17}"

            cas_165 = f"{'0' * 17}"
            cas_166 = '00200'
            cas_167 = f"{'0' * 17}"

            cas_153 = f"{'0' * 17}"
            cas_154 = '00750'
            cas_155 = f"{'0' * 17}"

            if int(year) >= 2025: #CAMBIOS INTRODUCIDOS EN ENERO DE 2025 
                cas_17 = '00000'
                cas_166 = '00000'
                cas_154 = '00000'

        # ---->> VERSION DEL TXT 2.01 <<---- 
        # Todos los campos referenciados con la Nota 10, sólo se podrán cumplimentar a partir de los periodos 10 y 4T de 2024 y ejercicios posteriores
        template = (
            f"<T3030{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay:<9}{' ' * 213}</AUX>"
            f"<T30301000>{' '}{type_dec}{nif:<9}{nom_emp}{year}{period}{'2' * 2}3{'2' * 6}{' ' * 9}2{exonerado}{'0'}{cas_150}{cas_151}{cas_152}{cas_01}{cas_02}{cas_03}{cas_153}{cas_154}"
            f"{cas_155}{cas_04}{cas_05}{cas_06}{cas_07}{cas_08}{cas_09}{cas_10}{cas_11}{cas_12}{cas_13}{cas_14}{cas_15}{cas_156}{cas_157}{cas_158}{cas_16}{cas_17}{cas_18}"
            f"{cas_19}{cas_20}{cas_21}{cas_22}{cas_23}{cas_24}{cas_25}{cas_26}{cas_27}{cas_28}{cas_29}{cas_30}{cas_31}{cas_32}{cas_33}{cas_34}{cas_35}{cas_36}{cas_37}{cas_38}"
            f"{cas_39}{cas_40}{cas_41}{cas_42}{cas_43}{cas_44}{cas_45}{cas_46}{cas_165}{cas_166}{cas_167}{'0' * 17}{'00050'}{'0' * 17}"
            f"{' ' * 522}{' ' * 13}</T30301000>"
            f"<T30303000>{cas_59}{cas_60}{cas_120}{cas_122}{cas_123}{cas_124}{cas_62}{cas_63}{cas_74}{cas_75}{cas_76}{cas_64}{cas_65}{cas_66}{cas_77}{cas_110}{cas_78}{cas_87}"
            f"{cas_68}{cas_69}{cas_70}{cas_109}{cas_71}"
            f"{' '}{' '}{' ' * 13}{' '}{'0' * 17}{'0' * 17}{' ' * 120}{' '}{' '}{' ' * 443}</T30303000>"
            f"<T303DID00>{swift:<11}{iban:<34}{' ' * 70}{' ' * 35}{' ' * 30}{' ' * 2}{sepa}{' ' * 617}</T303DID00>"
            f"</T3030{year}{period}0000>"
        )
        # ---->> VERSION DEL TXT 2.01 <<----

    return template

def generate_TXT_MODEL_309(presented_model, seller):
        dict_json = json.loads(presented_model.json_pdf)
        # print(json.dumps(dict_json, indent=4))

        year = dict_json['ejercicio']
        period = dict_json['periodo']
        nif = ''
        nif_muay = 'B67659607'
        version = '1.0'
        error_messages = []
        nom_emp = dict_json['nombre_1'].upper().replace(',', '').replace('.', '')
        total_result = float(dict_json['casilla24'].replace(',', '.'))
        seccion_5 = '2'
        seccion_6 = '6'

        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''

        # NOMBRE DEL SELLER
        result = set_name_seller(seller, nom_emp, '309', error_messages)
        nom_emp = result if not isinstance(result, list) else ''

        nom_emp = special_char_eliminate(nom_emp)

        if total_result > 0:
            type_dec = 'I'
        else:
            type_dec = ' '

        # Seccion 6
        if dict_json.get('seccion6_punto1') and dict_json.get('seccion6_punto1') == 'X':
            seccion_6 = '1'
        elif dict_json.get('seccion6_punto2') and dict_json.get('seccion6_punto2') == 'X':
            seccion_6 = '2'
        elif dict_json.get('seccion6_punto3') and dict_json.get('seccion6_punto3') == 'X':
            seccion_6 = '3'
        elif dict_json.get('seccion6_punto4') and dict_json.get('seccion6_punto4') == 'X':
            seccion_6 = '4'
        elif dict_json.get('seccion6_punto5') and dict_json.get('seccion6_punto5') == 'X':
            seccion_6 = '5'
        elif dict_json.get('seccion6_punto6') and dict_json.get('seccion6_punto6') == 'X':
            seccion_6 = '6'
        else:
            seccion_6 = '6'

        # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)


        cas_01 = format_numbers(dict_json['casilla01'], 17, False)
        cas_02 = format_numbers(dict_json['casilla02'], 5, False)
        cas_03 = format_numbers(dict_json['casilla03'], 17, False)
        cas_10 = format_numbers(dict_json['casilla10'], 17, False)
        cas_11 = format_numbers(dict_json['casilla11'], 5, False)
        cas_12 = format_numbers(dict_json['casilla12'], 17, False)
        cas_22 = format_numbers(dict_json['casilla22'], 17, False)
        cas_24 = format_numbers(dict_json['casilla24'], 17, False)

        if error_messages:
            return error_messages

        template = (
            f"<T3090{year}{period}0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay}{' ' * 213}</AUX>"
            f"<T30901000>{' '}{type_dec}{nif:<9}{nom_emp}{year}{period}{' ' * 9}{' ' * 20}{' ' * 60}{' ' * 2}{seccion_5}{seccion_6}"
            f"{' ' * 40}{' ' * 40}{' ' * 40}{' ' * 22}{' ' * 6}{' ' * 40}{' ' * 80}{' ' * 22}{'0' * 5}{' ' * 40}{' ' * 80}{' ' * 22}{'0' * 4}"
            f"{'0' * 10}{cas_01}{cas_02}{cas_03}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}"
            f"{cas_10}{cas_11}{cas_12}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{'0' * 17}{'0' * 5}{'0' * 17}{cas_22}"
            f"{'0' * 17}{cas_24}{' '}{' ' * 13}{' ' * 34}{' ' * 15}{' ' * 9}{' ' * 60}{' ' * 20}{' ' * 308}{' ' * 13}</T30901000>"
            f"</T3090{year}{period}0000>"
        )
        return template

def generate_TXT_MODEL_347(presented_model, seller):
        dict_json = json.loads(presented_model.json_pdf)
        # print(json.dumps(dict_json, indent=4))
        year = dict_json['EJERCICIO_PAG1']
        telephone = '*********'
        relation = 'MUAYTAX ADVISORS SL'
        nif = ''
        error_messages = []

        # Nombre, razón social denominación del declarante
        nom_emp = dict_json['SELLER_PAG1'].replace(',', '').replace('.', '')

        # NOMBRE DEL SELLER
        result = set_name_seller(seller, nom_emp, '347', error_messages)
        nom_emp = result if not isinstance(result, list) else ''

        nom_emp = special_char_eliminate(nom_emp)

        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''

        # Transacciones e importes
        total_op = int(dict_json['C01_PAG1'])
        total_percp = format_numbers(dict_json['C02_PAG1'], 16, True)

        transactions = []
        for x in range(2, 11):
            for ix in range(1, 4):
                conjunto_actual = {}
                keys = [f"NIF_DECLARADO{ix}_PAG{x}", f"NIF_IVA{ix}_PAG{x}", f"SELLER{ix}_PAG{x}",f"PROVINCIA{ix}_PAG{x}", f"PAIS{ix}_PAG{x}",
                        f"CLAVE{ix}_PAG{x}", f"IMPORTE_ANUAL{ix}_PAG{x}", f"T1_{ix}_PAG{x}", f"T2_{ix}_PAG{x}", f"T3_{ix}_PAG{x}",
                        f"T4_{ix}_PAG{x}"]

                if keys[6] in dict_json and dict_json.get(keys[6], "") != '': #Verificamos que exista al menos la clave del importe anual de las operaciones en el diccionario y tenga valor

                    valueid = dict_json.get(keys[0], '')
                    conjunto_actual["nif_op"] = valueid[2:] if valueid[:2].isalpha() else valueid

                    valueid = dict_json.get(keys[1], '')
                    conjunto_actual["nif_op_iva"] = valueid[2:] if valueid[:2].isalpha() else valueid

                    valueid = dict_json.get(keys[2], '')
                    conjunto_actual["nom_op"] = valueid[:40] 

                    valueid = dict_json.get(keys[3], '')
                    conjunto_actual["provin_op"] = valueid

                    valueid = dict_json.get(keys[4], '')
                    conjunto_actual["cod_pais_op"] = valueid

                    valueid = dict_json.get(keys[5], '')
                    conjunto_actual["clave_op"] = valueid

                    valueid = dict_json.get(keys[6], '')
                    conjunto_actual["imp_anual_op"] = valueid

                    valueid = dict_json.get(keys[7], '')
                    conjunto_actual["imp_t1_op"] = valueid

                    valueid = dict_json.get(keys[8], '')
                    conjunto_actual["imp_t2_op"] = valueid

                    valueid = dict_json.get(keys[9], '')
                    conjunto_actual["imp_t3_op"] = valueid

                    valueid = dict_json.get(keys[10], '')
                    conjunto_actual["imp_t4_op"] = valueid

                    if any(value != '' for value in conjunto_actual.values()):
                        transactions.append(conjunto_actual)
                        

        # print(json.dumps(transactions, indent=4))
        content_transactions = []
        for op in transactions:
            nif_op = op.get('nif_op', '')
            nif_op_iva = op.get('nif_op_iva', '')
            cod_pais_op = op.get('cod_pais_op', '')
            nom_op = op.get('nom_op', '')
            provin_op = op.get('provin_op', '')
            clave_op = op.get('clave_op', '')
            imp_anual_op = format_numbers(op.get('imp_anual_op', ''), 16, True)
            imp_t1_op = format_numbers(op.get('imp_t1_op', ''), 16, True)
            imp_t2_op = format_numbers(op.get('imp_t2_op', ''), 16, True)
            imp_t3_op = format_numbers(op.get('imp_t3_op', ''), 16, True)
            imp_t4_op = format_numbers(op.get('imp_t4_op', ''), 16, True)
            cod_pais_2 = ''

            country = Country.objects.filter(iso_code=cod_pais_op).first()

            if country is not None and country.is_european_union == True:
                cod_pais_2 = cod_pais_op
            else:
                cod_pais_2 = ''

            # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
            detalle = (
                f"2347{year}{nif:<9}{nif_op:<9}{' ' * 9}{nom_op:<40}D{provin_op:<2}{cod_pais_op:<2}{' '}{clave_op}{imp_anual_op}{' ' * 2}{'0' * 15}{' '}{'0' * 15}"
                f"{'0' * 4}{imp_t1_op}{' '}{'0' * 15}{imp_t2_op}{' '}{'0' * 15}{imp_t3_op}{' '}{'0' * 15}{imp_t4_op}{' '}{'0' * 15}{cod_pais_2:<2}{nif_op_iva:<15}"
                f"{' ' * 3}{' '}{'0' * 15}{' ' * 201}"
            )
            content_transactions.append(detalle)
        transactions_txt = ''.join(content_transactions)

        if error_messages:
            return error_messages

        # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) ||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
        template = (
            f"1347{year}{nif:<9}{nom_emp}T{telephone}{relation:<40}{'0' * 13}{' ' * 2}{'0' * 13}{total_op:0>9}{total_percp}{'0' * 9}{' '}{'0' * 15}{' ' * 205}{' ' * 9}{' ' * 88}{' ' * 13}"
            f"{transactions_txt}"
        )
        return template
        # print(template)

def generate_TXT_MODEL_349(presented_model, seller):
        dict_json = json.loads(presented_model.json_pdf)
        # print(json.dumps(dict_json, indent=4))
        muaytax = "MUAY TAX ADVISORS, SL"
        year = dict_json['doc_0_ejercicio']
        period = dict_json['doc_0_periodo']
        nom_emp = dict_json['doc_0_nombre'].upper().replace(',', '').replace('.', '')
        nom_emp = special_char_eliminate(nom_emp)
        nif = ''
        error_messages = []

        if len(nom_emp) > 40:
            nom_emp = nom_emp[:40]

        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''

        total_op = int(float(dict_json['doc_0_resumen_01'].replace(',', '.')))
        total_import = format_numbers(dict_json['doc_0_resumen_02'], 15, False)


        result = set_transactions_349(seller, year, nif, dict_json, error_messages)
        transactions_txt = result if not isinstance(result, list) else None

        if error_messages:
            return error_messages

        template = (
            f"1349{year}{nif:<9}{nom_emp:<40}T*********{muaytax:<40}{'0' * 13}{' ' * 2}{'0' * 13}{period}{total_op:0>9}{total_import}{'0' * 9}{'0' * 15}{' '}{' ' * 204}{' ' * 9}{' ' * 101}"
            f"{transactions_txt}"

        )
        # print(template)
        return template

def generate_TXT_MODEL_369(presented_model, seller):
        # -----------OBTENCIÓN DE DATOS A PARTIR DEL JSON--------------------------------

        dict_json = json.loads(presented_model.json_pdf)
        year = dict_json['doc_0_ejercicio']
        period = dict_json['doc_0_periodo']
        nom_emp = dict_json['doc_0_nombre'].upper().replace(',', '').replace('.', '')
        nif = ''

        info_seller_369 = {}
        error_messages = []


        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''
        
        # NOMBRE DEL SELLER
        result = set_name_seller(seller, nom_emp, '369', error_messages)
        nom_emp = result if not isinstance(result, list) else ''

        nom_emp = special_char_eliminate(nom_emp)
        
        first_month, latest_month, period = get_period_details(f'Q{period.replace("T", "")}')

        invoices = Invoice.objects.all().filter(seller_id=seller.pk, status='revised').exclude(
            transaction_type__code__icontains='-transfer').filter(accounting_date__year=year).filter(
            accounting_date__month__gte=first_month).filter(accounting_date__month__lte=latest_month).exclude(
            is_generated_amz=True)
        tax_country = invoices.filter(status__code="revised",
                                        transaction_type__code__in=["oss", "oss-refund"], ).filter(
            Q(departure_country__iso_code="ES") | Q(departure_country=None)).values('tax_country').distinct()

        if tax_country:
            activity = '0'
        else:
            activity = '1'

        info_seller_369 = {'nom_emp': nom_emp, 'nif': nif, 'year': year, 'period': period, 'activity': activity}

        info_page4_369 = []
        info_page6_369 = []
        num_negatives = []

        set_transactions_369(seller , dict_json, info_page4_369, info_page6_369, num_negatives, error_messages)

        # print("INFO TABLA 2")
        # print(json.dumps(info_page6_369, indent=4))

        # print("INFO TABLA 1")
        # print(json.dumps(info_page4_369, indent=4))
        # print("INFO SELLER")
        # print(json.dumps(info_seller_369, indent=4))

        # -----------GENERACIÓN DE TEMPLATE DEL TXT--------------------------------

        # Para valores (An) -->{:<INT} |||||| Para valores (Num) -->{:>INT}
        detalles_consumo = []
        cont_1 = 0
        page = 0
        content_T36905 = []

        for pais in info_page4_369:

            if pais['codPais'] == 'GR':
                pais['codPais'] = 'EL'
            elif pais['codPais'] == 'UK':
                pais['codPais'] = 'GB'

            if page == 0 and cont_1 < 1:

                detalle = f"{' '}{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"
                # codPais = 2, iva = 5, tipoIVA = 1, base = 17, cuota = 17 ---> TOTAL : 42 carácteres por transacción

            elif page == 0 and cont_1 > 0 and cont_1 < 29:

                detalle = f"{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"

            elif page > 0 and cont_1 < 1:

                detalle = f"{'C'}{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"

            elif page > 0 and cont_1 > 0 and cont_1 < 29:

                detalle = f"{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"


            detalles_consumo.append(detalle)
            cont_1 += 1

            if len(detalles_consumo) >= 28:
                detalles_consumo_text = ''.join(detalles_consumo)
                content_T36905.append(f'<T36905>{detalles_consumo_text:<1194}</T36905>')
                detalles_consumo = []
                page += 1
                cont_1 = 0

        if len(detalles_consumo) > 0:
            detalles_consumo_text = ''.join(detalles_consumo)
            content_T36905.append(f'<T36905>{detalles_consumo_text:<1194}</T36905>')
        T36905_txt = ''.join(content_T36905)

        detalles_consumo_2 = []
        cont_2 = 0
        page2 = 0
        content_T36907 = []

        for pais in info_page6_369:

            if pais['codPais'] == 'GR':
                pais['codPais'] = 'EL'
            elif pais['codPais'] == 'UK':
                pais['codPais'] = 'GB'

            if pais['otroCod'][:2] != pais['codPais_env']:
                if (pais['otroCod'][:1].isdigit() or pais['otroCod'][1:2].isdigit()):
                    pais['otroCod'] = f"{pais['codPais_env']}{pais['otroCod']}"

            if page2 == 0 and cont_2 < 1:
                detalle = f"{' '}{pais['otroCod']:<17}{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"
                # codPais = 17, codPais = 2, iva = 5, tipoIVA = 1, base = 17, cuota = 17 ---> TOTAL : 59 carácteres por transacción

            elif page2 == 0 and cont_2 > 0 and cont_2 < 29:
                detalle = f"{pais['otroCod']:<17}{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"

            elif page2 > 0 and cont_2 < 1:
                detalle = f"{'C'}{pais['otroCod']:<17}{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"

            elif page2 > 0 and cont_2 > 0 and cont_2 < 29:
                detalle = f"{pais['otroCod']:<17}{pais['codPais']:<2}{pais['iva']:>5}{pais['tipoIVA']:<1}{pais['base']:>17}{pais['cuota']:>17}"


            detalles_consumo_2.append(detalle)
            cont_2 += 1

            if len(detalles_consumo_2) >= 28:
                detalles_consumo_text_2 = ''.join(detalles_consumo_2)
                content_T36907.append(f"<T36907>{detalles_consumo_text_2:<1670}</T36907>")
                detalles_consumo_2 = []
                page2 += 1
                cont_2 = 0

        if len(detalles_consumo_2) > 0:
            detalles_consumo_text_2 = ''.join(detalles_consumo_2)
            content_T36907.append(f"<T36907>{detalles_consumo_text_2:<1670}</T36907>")
        T36907_txt = ''.join(content_T36907)

        if num_negatives != []:
            
            # Inicializar un diccionario para almacenar los resultados agrupados por codPais
            grouped_data = {}

            for item in num_negatives:
                cod_pais = item["codPais"]
                cuota = float(item["cuota"])
                
                if cod_pais in grouped_data:
                    grouped_data[cod_pais] += cuota
                else:
                    grouped_data[cod_pais] = cuota

            # Convertir el diccionario a una lista de diccionarios
            result = [{"codPais": cod_pais, "cuota": cuota} for cod_pais, cuota in grouped_data.items()]

            num_negatives = result
            content_T36908 = []
            detalles_consumo_neg = []
            for neg in num_negatives:

                if neg['codPais'] == 'GR':
                    neg['codPais'] = 'EL'
                elif neg['codPais'] == 'UK':
                    neg['codPais'] = 'GB'

                new_period = period.replace('T', '').replace('Q', '').replace(' ', '')
                new_year = year
                if int(new_period) > 1:
                    new_period = int(new_period) - 1
                else:
                    new_period = 4
                    new_year = int(year) - 1

                detalle = f"{neg['codPais']:<2}{new_year}T{' '}{new_period}{neg['cuota']:>17}"
                detalles_consumo_neg.append(detalle)

            detalles_consumo_neg_text = ''.join(detalles_consumo_neg)
            content_T36908.append(f"<T36908>{' '}{detalles_consumo_neg_text:<728}{' ' * 17}</T36908>")

            T36908_txt = ''.join(content_T36908)

        else:
            T36908_txt = ''

        template = (
            f"<T3690{year}{period}0000>{' ' * 92}{info_seller_369['nif']:<9}{' ' * 210}"
            f"<T36900>{' ' * 93}</T36900>"
            f"<T36904>MOSS D{' ' * 40}{' '}{'ES'}{info_seller_369['nif']:<15}{info_seller_369['nom_emp']}{year}T{info_seller_369['period'].replace('T', ''):>2}{' ' * 16}{info_seller_369['activity']}{' ' * 1176}{' ' * 17}</T36904>"
            f"{T36905_txt}"
            f"{T36907_txt}"
            f"{T36908_txt}"
            f"<T36909>{' '}{' ' * 5785}</T36909>"
            f"</T3690{year}{period}0000>"
        )
        return template

def generate_TXT_MODEL_390(presented_model, seller):

        dict_json = json.loads(presented_model.json_pdf)
        # print(json.dumps(dict_json, indent=4))
        year = dict_json['EJERCICIO']
        nif = ''
        error_messages = []

        # Nombre, razón social denominación del declarante
        nom_emp = dict_json['nombre_seller_pag1'].replace(',', '').replace('.', '')
        nom_rep = f"{special_char_eliminate(dict_json['POR_PODER_D_1'])}" if 'None' not in str(dict_json['POR_PODER_D_1']) else ''
        nif_rep = dict_json['POR_PODER_NIF_1'] if 'None' not in str(dict_json['POR_PODER_NIF_1']) else ''

        if seller.legal_entity == 'sl':
            nom_emp = f"{seller.first_name if seller.first_name else ''} {seller.last_name if seller.last_name else ''}",
            nif_rep = seller.nif_registration if seller.nif_registration else ''

        # NIF SELLER
        result = check_nif_seller(seller, error_messages)
        nif = result if not isinstance(result, list) else ''

        # NOMBRE DEL SELLER
        result = set_name_seller(seller, nom_emp, '390', error_messages)
        nom_emp = result if not isinstance(result, list) else ''

        nom_emp = special_char_eliminate(nom_emp)

        # Actividades económicas IAE
        act_prin = special_char_eliminate(dict_json['actividad_principal_nombre'].upper().split(' (')[0][:40]) if dict_json['actividad_principal_nombre'] != 'None' else ''
        actividad_principal_codigo = EconomicActivity.objects.filter(code__icontains=f"ES-{dict_json['actividad_principal_epigrafe']}").first().activity_code
        actividad_principal_epigrafe = dict_json['actividad_principal_epigrafe'].replace(".", "") if dict_json['actividad_principal_epigrafe'] != 'None' else ''

        # ---OTRAS IAE--
        otras_act1 = special_char_eliminate(dict_json['actividad_otras1_nombre'].upper().split(' (')[0][:40]) if dict_json['actividad_otras1_nombre'] != 'None' else ''
        # otras_cod1 = dict_json['actividad_otras1_codigo'] if dict_json['actividad_otras1_codigo'] != 'None' else ''
        otras_cod1 = EconomicActivity.objects.filter(code__icontains=f"ES-{dict_json['actividad_otras1_epigrafe']}").first().activity_code if dict_json['actividad_otras1_codigo'] != 'None' else ''
        otras_epi1 = dict_json['actividad_otras1_epigrafe'].replace(".", "") if dict_json['actividad_otras1_epigrafe'] != 'None' else ''

        otras_act2 = special_char_eliminate(dict_json['actividad_otras2_nombre'].upper().split(' (')[0][:40]) if dict_json['actividad_otras2_nombre'] != 'None' else ''
        # otras_cod2 = dict_json['actividad_otras2_codigo'] if dict_json['actividad_otras2_codigo'] != 'None' else ''
        otras_cod2 = EconomicActivity.objects.filter(code__icontains=f"ES-{dict_json['actividad_otras2_epigrafe']}").first().activity_code if dict_json['actividad_otras2_codigo'] != 'None' else ''
        otras_epi2 = dict_json['actividad_otras2_epigrafe'].replace(".", "") if dict_json['actividad_otras2_epigrafe'] != 'None' else ''

        otras_act3 = special_char_eliminate(dict_json['actividad_otras3_nombre'].upper().split(' (')[0][:40]) if dict_json['actividad_otras3_nombre'] != 'None' else ''
        # otras_cod3 = dict_json['actividad_otras3_codigo'] if dict_json['actividad_otras3_codigo'] != 'None' else ''
        otras_cod3 = EconomicActivity.objects.filter(code__icontains=f"ES-{dict_json['actividad_otras3_epigrafe']}").first().activity_code if dict_json['actividad_otras3_codigo'] != 'None' else ''
        otras_epi3 = dict_json['actividad_otras3_epigrafe'].replace(".", "") if dict_json['actividad_otras3_epigrafe'] != 'None' else ''

        otras_act4 = special_char_eliminate(dict_json['actividad_otras4_nombre'].upper().split(' (')[0][:40]) if dict_json['actividad_otras4_nombre'] != 'None' else ''
        # otras_cod4 = dict_json['actividad_otras4_codigo'] if dict_json['actividad_otras4_codigo'] != 'None' else ''
        otras_cod4 = EconomicActivity.objects.filter(code__icontains=f"ES-{dict_json['actividad_otras4_epigrafe']}").first().activity_code if dict_json['actividad_otras4_codigo'] != 'None' else ''
        otras_epi4 = dict_json['actividad_otras4_epigrafe'].replace(".", "") if dict_json['actividad_otras4_epigrafe'] != 'None' else ''

        otras_act5 = special_char_eliminate(dict_json['actividad_otras5_nombre'].upper().split(' (')[0][:40]) if dict_json['actividad_otras5_nombre'] != 'None' else ''
        # otras_cod5 = dict_json['actividad_otras5_codigo'] if dict_json['actividad_otras5_codigo'] != 'None' else ''
        otras_cod5 = EconomicActivity.objects.filter(code__icontains=f"ES-{dict_json['actividad_otras5_epigrafe']}").first().activity_code if dict_json['actividad_otras5_codigo'] != 'None' else ''
        
        otras_epi5 = dict_json['actividad_otras5_epigrafe'].replace(".", "") if dict_json['actividad_otras5_epigrafe'] != 'None' else ''

        # Info para devs
        version = '1.0'
        nif_muay = 'B67659607'

        # CASILLAS NUMÉRICAS

        cas_01 = format_numbers(dict_json['casilla_01'], 17, False)
        cas_02 = format_numbers(dict_json['casilla_02'], 17, False)
        cas_03 = format_numbers(dict_json['casilla_03'], 17, False)
        cas_04 = format_numbers(dict_json['casilla_04'], 17, False)
        cas_05 = format_numbers(dict_json['casilla_05'], 17, False)
        cas_06 = format_numbers(dict_json['casilla_06'], 17, False)
        cas_25 = format_numbers(dict_json['casilla_25'], 17, False)
        cas_26 = format_numbers(dict_json['casilla_26'], 17, False)
        cas_551 = format_numbers(dict_json['casilla_551'], 17, False)
        cas_552 = format_numbers(dict_json['casilla_552'], 17, False)
        cas_27 = format_numbers(dict_json['casilla_27'], 17, False)
        cas_28 = format_numbers(dict_json['casilla_28'], 17, False)
        cas_29 = format_numbers(dict_json['casilla_29'], 17, False)
        cas_30 = format_numbers(dict_json['casilla_30'], 17, False)
        cas_33 = format_numbers(dict_json['casilla_33'], 17, False)
        cas_34 = format_numbers(dict_json['casilla_34'], 17, False)
        cas_35 = format_numbers(dict_json['casilla_35'], 17, False)
        cas_36 = format_numbers(dict_json['casilla_36'], 17, False)
        cas_599 = format_numbers(dict_json['casilla_599'], 17, False)
        cas_600 = format_numbers(dict_json['casilla_600'], 17, False)
        cas_601 = format_numbers(dict_json['casilla_601'], 17, False)
        cas_602 = format_numbers(dict_json['casilla_602'], 17, False)
        cas_43 = format_numbers(dict_json['casilla_43'], 17, False)
        cas_44 = format_numbers(dict_json['casilla_44'], 17, False)
        cas_47 = format_numbers(dict_json['casilla_47'], 17, False)

        # ---- 5. Operaciones en Régimen General Pag 2 ----
        cas_190 = format_numbers(dict_json['casilla_190'], 17, False)
        cas_191 = format_numbers(dict_json['casilla_191'], 17, False)
        cas_603 = format_numbers(dict_json['casilla_603'], 17, False)
        cas_604 = format_numbers(dict_json['casilla_604'], 17, False)
        cas_605 = format_numbers(dict_json['casilla_605'], 17, False)
        cas_606 = format_numbers(dict_json['casilla_606'], 17, False)
        cas_48 = format_numbers(dict_json['casilla_48'], 17, False)
        cas_49 = format_numbers(dict_json['casilla_49'], 17, False)
        cas_202 = format_numbers(dict_json['casilla_202'], 17, False)
        cas_203 = format_numbers(dict_json['casilla_203'], 17, False)
        cas_619 = format_numbers(dict_json['casilla_619'], 17, False)
        cas_620 = format_numbers(dict_json['casilla_620'], 17, False)
        cas_621 = format_numbers(dict_json['casilla_621'], 17, False)
        cas_622 = format_numbers(dict_json['casilla_622'], 17, False)
        cas_52 = format_numbers(dict_json['casilla_52'], 17, False)
        cas_53 = format_numbers(dict_json['casilla_53'], 17, False)
        cas_629 = format_numbers(dict_json['casilla_629'], 17, False)
        cas_630 = format_numbers(dict_json['casilla_630'], 17, False)
        cas_56 = format_numbers(dict_json['casilla_56'], 17, False)
        cas_57 = format_numbers(dict_json['casilla_57'], 17, False)
        cas_637 = format_numbers(dict_json['casilla_637'], 17, False)
        cas_638 = format_numbers(dict_json['casilla_638'], 17, False)
        cas_597 = format_numbers(dict_json['casilla_597'], 17, False)
        cas_598 = format_numbers(dict_json['casilla_598'], 17, False)

        # ---- 5. Operaciones en Régimen General Pag 3 ----
        cas_639 = format_numbers(dict_json['casilla_639'], 17, False)
        cas_62 = format_numbers(dict_json['casilla_62'], 17, False)
        cas_64 = format_numbers(dict_json['casilla_64'], 17, False)
        cas_65 = format_numbers(dict_json['casilla_65'], 17, False)

        # ---- 7. Resultado Liquidación Anual ----
        cas_84 = format_numbers(dict_json['casilla_84'], 17, False)
        cas_85 = format_numbers(dict_json['casilla_85'], 17, False)
        cas_86 = format_numbers(dict_json['casilla_86'], 17, False)

        # ---- 9. Resultado de las Liquidaciones ----
        cas_95 = format_numbers(dict_json['casilla_95'], 17, False)
        cas_97 = format_numbers(dict_json['casilla_97'], 17, False)
        cas_98 = format_numbers(dict_json['casilla_98'], 17, False)
        cas_662 = format_numbers(dict_json['casilla_662'], 17, False)

        # ---- 10. Volumen de Operaciones ----
        cas_99 = format_numbers(dict_json['casilla_99'], 17, False)
        cas_103 = format_numbers(dict_json['casilla_103'], 17, False)
        cas_104 = format_numbers(dict_json['casilla_104'], 17, False)
        cas_110 = format_numbers(dict_json['casilla_110'], 17, False)
        cas_125 = format_numbers(dict_json['casilla_125'], 17, False)
        cas_126 = format_numbers(dict_json['casilla_126'], 17, False)
        cas_127 = format_numbers(dict_json['casilla_127'], 17, False)
        cas_108 = format_numbers(dict_json['casilla_108'], 17, False)

        #  ---- 11. Operaciones Específicas ----
        cas_230 = format_numbers(dict_json['casilla_230'], 17, False)
        cas_231 = format_numbers(dict_json['casilla_231'], 17, False)

        # ---- 12. Prorratas ----
        cas_114_1 = dict_json['casilla_114_1'][:-1]
        cas_115_1 = format_numbers(dict_json['casilla_115_1'], 17, False)
        cas_117_1 = dict_json['casilla_117_1']
        cas_118_1 = format_numbers(dict_json['casilla_118_1'], 5, False)

        cas_114_2 = dict_json['casilla_114_2'][:-1]
        cas_115_2 = format_numbers(dict_json['casilla_115_2'], 17, False)
        cas_117_2 = dict_json['casilla_117_2']
        cas_118_2 = format_numbers(dict_json['casilla_118_2'], 5, False)

        cas_114_3 = dict_json['casilla_114_3'][:-1]
        cas_115_3 = format_numbers(dict_json['casilla_115_3'], 17, False)
        cas_117_3 = dict_json['casilla_117_3']
        cas_118_3 = format_numbers(dict_json['casilla_118_3'], 5, False)

        cas_114_4 = dict_json['casilla_114_4'][:-1]
        cas_115_4 = format_numbers(dict_json['casilla_115_4'], 17, False)
        cas_117_4 = dict_json['casilla_117_4']
        cas_118_4 = format_numbers(dict_json['casilla_118_4'], 5, False)

        cas_114_5 = dict_json['casilla_114_5'][:-1]
        cas_115_5 = format_numbers(dict_json['casilla_115_5'], 17, False)
        cas_117_5 = dict_json['casilla_117_5']
        cas_118_5 = format_numbers(dict_json['casilla_118_5'], 5, False)

        if error_messages:
            return error_messages
        
        page_2 =''
        page_3 = ''
        page_4 = ''

        # ACTUALIZACIÓN DICIEMBRE 2024 INSERCCIÓN NUEVA PÁGINA
        if int(year) < 2024:
            page_2 = (
                f"<T39002000>{' '}{'0' * 34}{cas_01}{cas_02}{'0' * 34}{cas_03}{cas_04}{cas_05}{cas_06}{'0' * 680}{cas_25}{cas_26}{'0' * 136}{cas_551}{cas_552}{cas_27}{cas_28}{cas_29}{cas_30}{'0' * 68}"
                f"{cas_33}{cas_34}{'0' * 34}{cas_35}{cas_36}{'0' * 34}{cas_599}{cas_600}{cas_601}{cas_602}{'0' * 34}{cas_43}{cas_44}{'0' * 34}{cas_47}{' ' * 150}</T39002000>"
            )
            page_3 =(
                f"<T39003000>{' '}{cas_190}{cas_191}{'0' * 34}{cas_603}{cas_604}{cas_605}{cas_606}{cas_48}{cas_49}{'0' * 510}{cas_202}{cas_203}{'0' * 34}{cas_619}{cas_620}{cas_621}{cas_622}"
                f"{cas_52}{cas_53}{'0' * 272}{cas_629}{cas_630}{cas_56}{cas_57}{'0' * 272}{cas_637}{cas_638}{cas_597}{cas_598}{' ' * 150}</T39003000>"
            )
            page_4= (
                f"<T39004000>{' '}{'0' * 17 * 4}{cas_639}{cas_62}{'0' * 17 * 4}{cas_64}{cas_65}{' ' * 150}</T39004000>"
            )
        else:
            page_2 = (
                f"<T39002000>{' '}{'0' * 68}{cas_01}{cas_02}{'0' * 68}{cas_03}{cas_04}{cas_05}{cas_06}{'0' * 952}{cas_25}{cas_26}{'0' * 204}{cas_551}{cas_552}{cas_27}{cas_28}{cas_29}{cas_30}{'0' * 68}"
                f"{cas_33}{cas_34}{' ' * 150}</T39002000>"
                f"<T39002B00>{' '}{'0' * 68}{cas_35}{cas_36}{'0' * 68}{cas_599}{cas_600}{cas_601}{cas_602}{'0' * 34}{cas_43}{cas_44}{'0' * 34}{cas_47}{' ' * 150}</T39002B00>"
            )
            page_3 =(
                f"<T39003000>{' '}{'0' * 34}{cas_190}{cas_191}{'0' * 68}{cas_603}{cas_604}{cas_605}{cas_606}{cas_48}{cas_49}{'0' * 748}{cas_202}{cas_203}{'0' * 68}{cas_619}{cas_620}{cas_621}{cas_622}"
                f"{cas_52}{cas_53}{'0' * 408}{cas_629}{cas_630}{cas_56}{cas_57}{' ' * 150}</T39003000>"
            )
            page_4 = (
                f"<T39004000>{' '}{'0' * 408}{cas_637}{cas_638}{cas_597}{cas_598}{'0' * 68}{cas_639}{cas_62}{'0' * 68}{cas_64}{cas_65}{' ' * 150}</T39004000>"
            )

        # Para valores (An) -->{:<INT} (rellenar con espacios en blanco a la derecha) |||||| Para valores (Num) -->{:0>INT}(rellenar con ceros a la izquierda)
        template = (
            f"<T3900{year}0A0000><AUX>{' ' * 70}{version:<4}{' ' * 4}{nif_muay:<9}{' ' * 213}</AUX>"
            f"<T39001000>{' ' * 2}{nif:<9}{nom_emp:<80}{year}{' ' * 2}00{' ' * 7}000{' ' * 9}22200{' ' * 13}"
            f"{act_prin:<40}{actividad_principal_codigo:<3}{actividad_principal_epigrafe:<4}{otras_act1:<40}{otras_cod1:<3}{otras_epi1:<4}"
            f"{otras_act2:<40}{otras_cod2:<3}{otras_epi2:<4}{otras_act3:<40}{otras_cod3:<3}{otras_epi3:<4}{otras_act4:<40}{otras_cod4:<3}{otras_epi4:<4}{otras_act5:<40}{otras_cod5:<3}{otras_epi5:<4}"
            f"0{' ' * 214}{nom_rep:<80}{nif_rep:<9}{'0' * 8}{' ' * 101}{'0' * 8}{' ' * 101}{'0' * 8}{' ' * 216}</T39001000>"
            f"{page_2}"
            f"{page_3}"
            f"{page_4}"
            f"<T39006000>{' '}{'0' * 17}{cas_84}{'0' * 17}{cas_85}{cas_86}{'0' * 25}{'0' * 102}{cas_95}{'0' * 34}{cas_97}{cas_98}{cas_662}{'0' * 34}{cas_99}{'0' * 17}{cas_103}{cas_104}{'0' * 17}{cas_110}"
            f"{cas_125}{cas_126}{cas_127}{'0' * 136}{cas_108}{' ' * 150}</T39006000>"
            f"<T39007000>{' '}{cas_230}{'0' * 17}{cas_231}{'0' * 136}{otras_act1:<40}{cas_114_1:<3}{cas_115_1}{'0' * 17}{cas_117_1:<1}{cas_118_1}{otras_act2:<40}{cas_114_2:<3}{cas_115_2}{'0' * 17}{cas_117_2:<1}{cas_118_2}"
            f"{otras_act3:<40}{cas_114_3:<3}{cas_115_3}{'0' * 17}{cas_117_3:<1}{cas_118_3}{otras_act4:<40}{cas_114_4:<3}{cas_115_4}{'0' * 17}{cas_117_4:<1}{cas_118_4}"
            f"{otras_act5:<40}{cas_114_5:<3}{cas_115_5}{'0' * 17}{cas_117_5:<1}{cas_118_5}{' ' * 150}</T39007000>"
            f"</T3900{year}0A0000>"
        )

        return template
        # print(template)

def generate_TXT_MODEL_ANNUALE_IT(presented_model):
        # print("txt model annuale")
        dict_json = json.loads(presented_model.json_pdf)
        model_year = str(presented_model.year + 1)
        # print(json.dumps(dict_json, indent=4))

        # DATOS PERSONALES/EMPRESA/REPRESENTANTE
        codice_fiscale = dict_json.get('page0_codice_fiscale', '')
        codice_fiscale_fornitore = '13007250965'
        cognome = special_char_eliminate(dict_json.get('page0_cognome', ''))
        nome = special_char_eliminate(dict_json.get('page0_nome', ''))
        denominazione = special_char_eliminate(dict_json.get('page0_denominazione', ''))
        partita_iva = dict_json.get('page0_partita_iva', '')
        comune_di_nascita = dict_json.get('page0_comune_di_nascita', '')
        provincia_di_nascita = dict_json.get('page0_provincia', '')
        giorno ='00' if dict_json.get('page0_giorno', '') == '' else dict_json.get('page0_giorno', '')
        messe = '00' if dict_json.get('page0_mese', '') == '' else dict_json.get('page0_mese', '')
        anno = '0000' if dict_json.get('page0_anno', '') == '' else dict_json.get('page0_anno', '')
        sesso_f = dict_json.get('page0_sesso_f', '')
        sesso_m = dict_json.get('page0_sesso_m', '')
        sesso = 'F' if sesso_f != '' else 'M' if sesso_m != '' else ''
        natura_giuridica = dict_json.get('page0_natura_giuridica', '00')
        moduli = dict_json.get('page0_numero_di_moduli', '00')
        soggeto = f"{' ' * 16}00{' ' * 16}00{' ' * 16}00{' ' * 16}00{' ' * 16}0"
        codice_fiscale_sottoscrittore = dict_json.get('page0_codice_fiscale_del_sottoscrittore', '')
        codice_fiscale_societa_dichiarante = dict_json.get('page0_Codice fiscale_societa_dichiarante', '')
        codice_carica = dict_json.get('page0_codice_carica', '')
        giorno_sottoscrittore = '00' if dict_json.get('page0_giorno_diacharante_diverso', '00') == '' else dict_json.get('page0_giorno_diacharante_diverso', '00')
        messe_sottoscrittore = '00' if dict_json.get('page0_mese_diacharante_diverso', '00') == '' else dict_json.get('page0_mese_diacharante_diverso', '00')
        anno_sottoscrittore = '0000' if dict_json.get('page0_anno_diacharante_diverso', '0000') == '' else dict_json.get('page0_anno_diacharante_diverso', '0000')
        cognome_sottoscrittore = special_char_eliminate(dict_json.get('page0_cognome_diacharante_diverso', ''))
        nome_sottoscrittore = special_char_eliminate(dict_json.get('page0_nome_diacharente_diverso', ''))
        sesso_f_sottoscrittore = dict_json.get('page0_sesso_f_diacharante_diverso', '')
        sesso_m_sottoscrittore = dict_json.get('page0_sesso_m_diacharante_diverso', '')
        sesso_sottoscrittore = 'F' if sesso_f_sottoscrittore != '' else 'M' if sesso_m_sottoscrittore != '' else ''
        comune_di_nascita_sottoscrittore = special_char_eliminate(dict_json.get('page0_comune_di_nascita_diacharante_diverso', ''))
        comune_di_nascita_sottoscrittore = comune_di_nascita_sottoscrittore.upper() if comune_di_nascita_sottoscrittore is not None or comune_di_nascita_sottoscrittore != '' else ''
        codice_fiscale_incaricato = dict_json.get('page0_codice_fiscale_dell_incaricato', '')
        predisposto_la_dichiarazione = dict_json.get('page0_soggetto_che_ha_predisposto_la_dichiarazione', '')
        giorno_impegno = '00' if dict_json.get('page0_data_dell_impegno_giorno', '00') == '' else dict_json.get('page0_data_dell_impegno_giorno', '00')
        messe_impegno = '00' if dict_json.get('page0_data_dell_impegno_mese', '00') == '' else dict_json.get('page0_data_dell_impegno_mese', '00')
        anno_impegno = '0000' if dict_json.get('page0_data_dell_impegno_anno', '0000') == '' else dict_json.get('page0_data_dell_impegno_anno', '0000')
        firma_incaricato =  dict_json.get('page0_firma_dell_incaricato', '')
        firma_intermediario = '1' if firma_incaricato is not None or firma_incaricato != '' else '0'

        # QUADRI COMPILATI (check de hojas que se presentan)
        va = 'VA'
        ve = 'VE' if dict_json.get('page9_VE', '') == 'X' else ''
        vf = 'VF' if dict_json.get('page9_VF', '') == 'X' else ''
        vl = 'VL' if dict_json.get('page9_VL', '') == 'X' else ''
        vt = 'VT' if dict_json.get('page9_VT', '') == 'X' else ''
        vx = 'VX' if dict_json.get('page9_VX', '') == 'X' else ''
        quadri = f"{va}{ve}{vf}{vl}{vt}{vx}"

        # CASILLAS NUMÉRICAS
        ve020001 = f"VE020001{format_numbers_it(dict_json.get('page3_VE20_1', ''),'vatannuale')}" if dict_json.get('page3_VE20_1', '') != '0' else ''
        ve020002 = f"VE020002{format_numbers_it(dict_json.get('page3_VE20_2', ''),'vatannuale')}" if dict_json.get('page3_VE20_2', '') != '0' else ''
        ve021001 = f"VE021001{format_numbers_it(dict_json.get('page3_VE21_1', ''),'vatannuale')}" if dict_json.get('page3_VE21_1', '') != '0' else ''
        ve021002 = f"VE021002{format_numbers_it(dict_json.get('page3_VE21_2', ''),'vatannuale')}" if dict_json.get('page3_VE21_2', '') != '0' else ''
        ve022001 = f"VE022001{format_numbers_it(dict_json.get('page3_VE22_1', ''),'vatannuale')}" if dict_json.get('page3_VE22_1', '') != '0' else ''
        ve022002 = f"VE022002{format_numbers_it(dict_json.get('page3_VE22_2', ''),'vatannuale')}" if dict_json.get('page3_VE22_2', '') != '0' else ''
        ve023001 = f"VE023001{format_numbers_it(dict_json.get('page3_VE23_1', ''),'vatannuale')}" if dict_json.get('page3_VE23_1', '') != '0' else ''
        ve023002 = f"VE023002{format_numbers_it(dict_json.get('page3_VE23_2', ''),'vatannuale')}" if dict_json.get('page3_VE23_2', '') != '0' else ''
        ve024001 = f"VE024001{format_numbers_it(dict_json.get('page3_VE24_1', ''),'vatannuale')}" if dict_json.get('page3_VE24_1', '') != '0' else ''
        ve024002 = f"VE024002{format_numbers_it(dict_json.get('page3_VE24_2', ''),'vatannuale')}" if dict_json.get('page3_VE24_2', '') != '0' else ''
        ve026002 = f"VE026002{format_numbers_it(dict_json.get('page3_VE26', ''),'vatannuale')}" if dict_json.get('page3_VE26', '') != '0' else ''
        ve033001 = f"VE033001{format_numbers_it(dict_json.get('page3_VE33', ''),'vatannuale')}" if dict_json.get('page3_VE33', '') != '0' else ''
        ve050001 = f"VE050001{format_numbers_it(dict_json.get('page3_VE50', ''),'vatannuale')}" if dict_json.get('page3_VE50', '') != '0' else ''
        vf013001 = f"VF013001{format_numbers_it(dict_json.get('page4_VF13_1', ''),'vatannuale')}" if dict_json.get('page4_VF13_1', '') != '0' else ''
        vf013002 = f"VF013002{format_numbers_it(dict_json.get('page4_VF13_2', ''),'vatannuale')}" if dict_json.get('page4_VF13_2', '') != '0' else ''
        vf025001 = f"VF025001{format_numbers_it(dict_json.get('page4_VF25_1', ''),'vatannuale')}" if dict_json.get('page4_VF25_1', '') != '0' else ''
        vf025002 = f"VF025002{format_numbers_it(dict_json.get('page4_VF25_2', ''),'vatannuale')}" if dict_json.get('page4_VF25_2', '') != '0' else ''
        vf027002 = f"VF027002{format_numbers_it(dict_json.get('page4_VF27', ''),'vatannuale')}" if dict_json.get('page4_VF27', '') != '0' else ''
        vf029003 = f"VF029003{format_numbers_it(dict_json.get('page4_VF29_3', ''),'vatannuale')}" if dict_json.get('page4_VF29_3', '') != '0' else ''
        vf060001 = f"VF060001{dict_json.get('page5_VF60_1', '').replace('X','1').rjust(16)}"
        vf071002 = f"VF071002{format_numbers_it(dict_json.get('page5_VF71', ''),'vatannuale')}" if dict_json.get('page5_VF71', '') != '0' else ''
        vl001001 = f"VL001001{format_numbers_it(dict_json.get('page9_VL1', ''),'vatannuale')}" if dict_json.get('page9_VL1', '') != '0' else ''
        vl002001 = f"VL002001{format_numbers_it(dict_json.get('page9_VL2', ''),'vatannuale')}" if dict_json.get('page9_VL2', '') != '0' else ''
        vl003001 = f"VL003001{format_numbers_it(dict_json.get('page9_VL3', ''),'vatannuale')}" if dict_json.get('page9_VL3', '') != '0' else ''
        vl004001 = f"VL004001{format_numbers_it(dict_json.get('page9_VL4', ''),'vatannuale')}" if dict_json.get('page9_VL4', '') != '0' else ''
        vl008001 = f"VL008001{format_numbers_it(dict_json.get('page9_VL8_1', ''),'vatannuale')}" if dict_json.get('page9_VL8_1', '') != '0' else ''
        vl023001 = f"VL023001{format_numbers_it(dict_json.get('page9_VL23', ''),'vatannuale')}" if dict_json.get('page9_VL23', '') != '0' else ''
        vl025001 = f"VL025001{format_numbers_it(dict_json.get('page9_VL25', ''),'vatannuale')}" if dict_json.get('page9_VL25', '') != '0' else ''
        vl030001 = f"VL030001{format_numbers_it(dict_json.get('page9_VL30_1', ''),'vatannuale')}" if dict_json.get('page9_VL30_1', '') != '0' else ''
        vl030002 = f"VL030002{format_numbers_it(dict_json.get('page9_VL30_2', ''),'vatannuale')}" if dict_json.get('page9_VL30_2', '') != '0' else ''
        vl030003 = f"VL030003{format_numbers_it(dict_json.get('page9_VL30_3', ''),'vatannuale')}" if dict_json.get('page9_VL30_3', '') != '0' else ''
        vl032001 = f"VL032001{format_numbers_it(dict_json.get('page9_VL32', ''),'vatannuale')}" if dict_json.get('page9_VL32', '') != '0' else ''
        vl033001 = f"VL033001{format_numbers_it(dict_json.get('page9_VL33', ''),'vatannuale')}" if dict_json.get('page9_VL33', '') != '0' else ''
        vl036001 = f"VL036001{format_numbers_it(dict_json.get('page9_VL36', ''),'vatannuale')}" if dict_json.get('page9_VL36', '') != '0' else ''
        vl038001 = f"VL038001{format_numbers_it(dict_json.get('page9_VL38', ''),'vatannuale')}" if dict_json.get('page9_VL38', '') != '0' else ''
        vl039001 = f"VL039001{format_numbers_it(dict_json.get('page9_VL39', ''),'vatannuale')}" if dict_json.get('page9_VL39', '') != '0' else ''
        vt001001 = f"VT001001{format_numbers_it(dict_json.get('page11_VT1_1', ''),'vatannuale')}" if dict_json.get('page11_VT1_1', '') != '0' else ''
        vt001002 = f"VT001002{format_numbers_it(dict_json.get('page11_VT1_2', ''),'vatannuale')}" if dict_json.get('page11_VT1_2', '') != '0' else ''
        vt001003 = f"VT001003{format_numbers_it(dict_json.get('page11_VT1_3', ''),'vatannuale')}" if dict_json.get('page11_VT1_3', '') != '0' else ''
        vt001004 = f"VT001004{format_numbers_it(dict_json.get('page11_VT1_4', ''),'vatannuale')}" if dict_json.get('page11_VT1_4', '') != '0' else ''
        vt002001 = f"VT002001{format_numbers_it(dict_json.get('page11_VT2_1', ''),'vatannuale')}" if dict_json.get('page11_VT2_1', '') != '0' else ''
        vt002002 = f"VT002002{format_numbers_it(dict_json.get('page11_VT2_2', ''),'vatannuale')}" if dict_json.get('page11_VT2_2', '') != '0' else ''
        vt011001 = f"VT011001{format_numbers_it(dict_json.get('page11_VT11_1', ''),'vatannuale')}" if dict_json.get('page11_VT11_1', '') != '0' else ''
        vt011002 = f"VT011002{format_numbers_it(dict_json.get('page11_VT11_2', ''),'vatannuale')}" if dict_json.get('page11_VT11_2', '') != '0' else ''
        vx001001 = f"VX001001{format_numbers_it(dict_json.get('page12_VX1', ''),'vatannuale')}" if dict_json.get('page12_VX1', '') != '0' else ''
        vx002001 = f"VX002001{format_numbers_it(dict_json.get('page12_VX2_1', ''),'vatannuale')}" if dict_json.get('page12_VX2_1', '') != '0' else ''
        vx005001 = f"VX005001{format_numbers_it(dict_json.get('page12_VX5', ''),'vatannuale')}" if dict_json.get('page12_VX5', '') != '0' else ''

        casillas = (
            f"{ve020001}{ve020002}{ve022001}{ve022002}{ve021001}{ve021002}"
            f"{ve023001}{ve023002}{ve024001}{ve024002}{ve026002}{ve033001}{ve050001}{vf013001}{vf013002}{vf025001}{vf025002}{vf027002}"
            f"{vf029003}{vf060001}{vf071002}{vl001001}{vl002001}{vl003001}{vl004001}{vl008001}{vl023001}{vl025001}{vl030001}{vl030002}"
            f"{vl030003}{vl032001}{vl033001}{vl036001}{vl038001}{vl039001}{vt001001}{vt001002}{vt001003}{vt001004}{vt002001}{vt002002}"
            f"{vt011001}{vt011002}{vx001001}{vx002001}{vx005001}"
        )

        template = (
            f"A{' ' * 14}IVA{model_year[2:]}01{codice_fiscale_fornitore:<16}{' ' * 483}{'0' * 8}{' ' * 100}{' ' * 1068}{' ' * 200}A\n"
            f"B{codice_fiscale:<16}00000001{' '* 3}{' ' * 25}{' ' * 20}{' ' * 16}00{' '}0{cognome:<24}{nome:<20}{denominazione:<60}"
            f"{partita_iva:0>11}00{' ' * 100}{' ' * 12}{' ' * 12}{comune_di_nascita:<40}{provincia_di_nascita:<2}{giorno}{messe}{anno}{sesso:<1}{natura_giuridica}"
            f"{' ' * 24}{' ' * 3}{' ' * 24}{moduli:0>8}10{' '}0{soggeto}0{' ' * 525}{' ' * 60}{' ' * 24}{' ' * 20}{' ' * 40}00{' ' * 4}{' ' * 5}{' ' * 20}{' ' * 35}"
            f"{' ' * 10}{' ' * 35}{' ' * 24}{' ' * 3}{' ' * 24}{' ' * 24}{' ' * 35}{codice_fiscale_sottoscrittore:<16}{codice_carica:0>2}{'0' * 8}"
            f"{codice_fiscale_societa_dichiarante:0>11}{cognome_sottoscrittore:<24}{nome_sottoscrittore:<20}{sesso_sottoscrittore:<1}{giorno_sottoscrittore}{messe_sottoscrittore}{anno_sottoscrittore}"
            f"{comune_di_nascita_sottoscrittore:<40}{' ' * 2}{' ' * 3}{' ' * 24}{' ' * 24}{' ' * 35}{' ' * 12}{'0' * 8}0{'0' * 8}0{' ' * 40}11{' ' * 3}{codice_fiscale_incaricato:<16}"
            f"{' ' * 5}{predisposto_la_dichiarazione:<1}{giorno_impegno}{messe_impegno}{anno_impegno}{firma_intermediario}{' ' * 16}{'0' * 11}{' ' * 16}000{' ' * 43}{' ' * 20}{' ' * 34}A\n"
            f"D{codice_fiscale:<16}00000001{' ' * 3}{' ' * 25}{' ' * 20}{' ' * 16}V1000001{quadri:<12}{' ' * 4}VA002001479110{' ' * 10}"
            f"{casillas:<1760}A\nZ{' ' * 14}000000001000000001{' ' * 1864}A\n"
        )
        return template
        # print(template)