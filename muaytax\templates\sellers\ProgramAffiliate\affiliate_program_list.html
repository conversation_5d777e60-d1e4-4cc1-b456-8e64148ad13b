{% extends "layouts/base.html" %}
{% load static %}
{% block styles %}
    <style>
        .product-card {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .product-card .card-body {
            overflow-y: auto; /* Permite desplazamiento si hay demasiados datos */
            padding: 10px 15px;
        }

        .product-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .product-list li {
            font-size: 0.9em;
            padding: 5px 0;
        }

        .product-list li span {
            font-weight: bold;
            color: black !important; /* Color negro para las cantidades */
        }

        .product-card .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            text-align: center;
        }

        #product-data-details {
            display: none; /* Oculta el contenedor si no hay datos */
        }
    </style>
{% endblock styles %}

{% block content %}
    <div>
        <h3 class="card-title">Informe general del Programa Afiliados de {{ seller.shortname }} para el Año  
            <span class="fw-bold" id="selected-year"></span> : Mes de <span class="fw-bold" id="selected-month"></span>
        </h3>
    </div>
    <div class="row mt-5">
        <form id="filter-form" class="row g-3">
            <div class="col-md-6">
                <label for="year" class="form-label">Año</label>
                <select id="year" name="year" class="form-select"></select>
            </div>
            <div class="col-md-6">
                <label for="month" class="form-label">Mes</label>
                <select id="month" name="month" class="form-select">
                    <option value="">Todos los meses con registro</option>
                    <option value="1">Enero</option>
                    <option value="2">Febrero</option>
                    <option value="3">Marzo</option>
                    <option value="4">Abril</option>
                    <option value="5">Mayo</option>
                    <option value="6">Junio</option>
                    <option value="7">Julio</option>
                    <option value="8">Agosto</option>
                    <option value="9">Septiembre</option>
                    <option value="10">Octubre</option>
                    <option value="11">Noviembre</option>
                    <option value="12">Diciembre</option>
                </select>
            </div>
        </form>

        <div id="affiliate-programs" class="row mt-4" style="width: 100%;"></div>
        <div id="charts-container" class="row mt-4">
            <div class="chart-wrapper" style="background-color: #ffffff; padding: 20px; border-radius: 10px;">
                <div id="chart-container" style="width: 100%;"></div>
            </div>
            <p id="width-display"></p>
        </div>

        <!-- Tarjetas info. mes -->
        <div id="month-details" class="mt-4" style="display: none;">
            <div class="col-12 mb-4"></div>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">Información de clientes</h5>
                        </div> 
                        <div class="card-body">
                            <p class="card-text">Clientes Mes: <span class="fw-bold text-black" id="clients"></span></p>
                            <p class="card-text">Clientes Captados: <span class="fw-bold text-black" id="new-clients"></span></p>
                            <p class="card-text">Clientes Totales: <span class="fw-bold text-black" id="total-clients"></span></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">Información de pedidos</h5>
                        </div> 
                        <div class="card-body">
                            <p class="card-text">Pedidos: <span class="fw-bold text-black" id="original-order"></span></p>
                            <p class="card-text">Pedido Renovación: <span class="fw-bold text-black" id="renewal-order"></span></p>
                            <p class="card-text">Pedido Único: <span class="fw-bold text-black" id="unique-order"></span></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">Facturación</h5>
                        </div> 
                        <div class="card-body">
                            <p class="card-text">Facturación en Euros: <span class="fw-bold text-black" id="billing-euros"></span><span class="fw-bold text-black"> &euro;</span></p>
                            <p class="card-text">Facturación en Dólares: <span class="fw-bold text-black">&#36; </span> <span class="fw-bold text-black" id="billing-dollars"></span></p>
                            <p class="card-text">Facturación total mensual: <span class="fw-bold text-black">&#36; </span><span class="fw-bold text-black" id="total-billing"></span></p></p>
                            <p class="card-text">Facturación total ultimos 12 meses: <span class="fw-bold text-black">&#36; </span><span class="fw-bold text-black" id="total-billing-Last12Months"></span></p></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">Beneficios</h5>
                        </div> 
                        <div class="card-body">
                            <p class="card-text">Beneficio total del mes: <span class="fw-bold text-black">&#36; </span><span class="fw-bold text-black" id="total-benefit"></span></p></p>
                            <p class="card-text">Beneficio acumulado: <span class="fw-bold text-black">&#36; </span><span class="fw-bold text-black" id="accumulated-benefit"></span></p></p>
                            <p class="card-text">El mes de <span class="fw-bold text-black" id="withdrawal-month"></span> retiró: <span class="fw-bold text-black">&#36; </span><span class="fw-bold text-black" id="withdrawal-amount"></span></p></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="product-data-details" class="row mt-4">
            <div class="col-12">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title text-center">Reporte de Ventas de Productos (por tiendas)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row d-flex justify-content-center align-items-stretch">
                            <!-- Subtarjeta para AMZVAT -->
                            <div class="col-md-4 d-flex align-items-stretch">
                                <div class="card product-card flex-fill">
                                    <div class="card-header text-center">
                                        <h6 class="card-title">AMZVAT</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul id="amzvat-products" class="product-list"></ul>
                                    </div>
                                </div>
                            </div>
                            <!-- Subtarjeta para MuayTax -->
                            <div class="col-md-4 d-flex align-items-stretch">
                                <div class="card product-card flex-fill">
                                    <div class="card-header text-center">
                                        <h6 class="card-title">MuayTax</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul id="muaytax-products" class="product-list"></ul>
                                    </div>
                                </div>
                            </div>
                            <!-- Subtarjeta para Gestoría -->
                            <div class="col-md-4 d-flex align-items-stretch">
                                <div class="card product-card flex-fill">
                                    <div class="card-header text-center">
                                        <h6 class="card-title">Gestoría</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul id="gestoria-products" class="product-list"></ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block javascripts %}
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/highcharts/highcharts.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/exporting/exporting.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/export/export-data.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/accessibility/accessibility.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/pattern/pattern-fill.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const shortname = "{{ seller.shortname }}";
            const yearSelect = document.getElementById('year');
            const monthSelect = document.getElementById('month');
            const affiliateProgramsDiv = document.getElementById('affiliate-programs');
            const chartsContainer = document.getElementById('charts-container');
            const monthDetails = document.getElementById('month-details');
            const widthDisplay = document.getElementById('width-display');
            const filterForm = document.getElementById('filter-form');
            
            // Para Actualizar con la data
            const selectedYear = document.getElementById('selected-year');
            const selectedMonth = document.getElementById('selected-month');
            const clients = document.getElementById('clients');
            const newClients = document.getElementById('new-clients');
            const totalClients = document.getElementById('total-clients');
            const originalOrder = document.getElementById('original-order');
            const renewalOrder = document.getElementById('renewal-order');
            const uniqueOrder = document.getElementById('unique-order');
            const billingEuros = document.getElementById('billing-euros');
            const billingDollars = document.getElementById('billing-dollars');
            const totalBilling = document.getElementById('total-billing');
            const totalBillingLast12Months = document.getElementById('total-billing-Last12Months');
            
            const withdrawalMonth = document.getElementById('withdrawal-month');
            const withdrawalAmount = document.getElementById('withdrawal-amount');
            const accumulatedBenefit = document.getElementById('accumulated-benefit');
            const totalBenefit = document.getElementById('total-benefit');

            let programs = [];

            function displayDivWidth() {
                const width = chartsContainer.offsetWidth;
                widthDisplay.textContent = 'Ancho del div: ' + width + 'px';
            }

            function displayProductDataDetails(month, year) {
            
                const amzvatList = document.getElementById('amzvat-products');
                const muaytaxList = document.getElementById('muaytax-products');
                const gestoriaList = document.getElementById('gestoria-products');
            
                // Limpiar las listas de productos
                amzvatList.innerHTML = '';
                muaytaxList.innerHTML = '';
                gestoriaList.innerHTML = '';
            
                if (!year || !month) {
                    const productDetailsContainer = document.getElementById('product-data-details');
                    productDetailsContainer.style.display = 'none'; // Oculta el bloque completo
                    console.log("Mes o año no seleccionado. Ocultando bloque completo de reporte de ventas.");
                    return;
                }
            
                const filteredProgram = programs.find(program => program.year == year && program.month == month);
            
                if (filteredProgram && filteredProgram.product_data) {
                    const productData = filteredProgram.product_data;
            
                    function populateProductList(store, listElement) {
                        if (productData[store] && productData[store].length > 0) {
                            productData[store].forEach(product => {
                                const listItem = document.createElement('li');
                                listItem.textContent = `${product.name}: `;
                                const sales = document.createElement('span');
                                sales.textContent = product.ventas;
                                sales.style.color = 'black'; // Aplica el color negro directamente
                                sales.style.fontWeight = 'bold'; // O cualquier otro estilo que necesites
                                listItem.appendChild(sales);
                                listElement.appendChild(listItem);
                            });
                        } else {
                            const emptyMessage = document.createElement('li');
                            emptyMessage.textContent = 'No hay datos registrados para esta tienda.';
                            emptyMessage.classList.add('no-data'); // Clase específica para estilos
                            emptyMessage.style.listStyleType = 'none'; // Elimina viñetas
                            listElement.appendChild(emptyMessage);
                        }
                    }
            
                    populateProductList('amzvat', amzvatList);
                    populateProductList('muaytax', muaytaxList);
                    populateProductList('gestoria', gestoriaList);
            
                    document.getElementById('product-data-details').style.display = 'block';
                } else {
                    document.getElementById('product-data-details').style.display = 'none';
                }
            }

            // Obtener los datos
            fetch(`/sellers/${shortname}/affiliate_program/json/`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                programs = data.affiliate_programs;
                const date = new Date();
                const monthCurrent = date.getMonth() + 1; 
                const yearCurrent = date.getFullYear();
                const categories = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];
                    
                // Menu desplegable del año
                const years = [...new Set(programs.map(program => program.year))].sort((a, b) => b - a);
                years.forEach(year => {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    yearSelect.appendChild(option);
                });

                yearSelect.addEventListener('change', function () {
                    const year = this.value;
                    monthSelect.disabled = !year;
                    const filteredMonths = programs.filter(program => program.year == year).map(program => parseInt(program.month));
                    monthSelect.querySelectorAll('option').forEach(option => {
                        const month = parseInt(option.value);
                        option.hidden = !filteredMonths.includes(month);
                    });

                    // Reiniciar el valor del mes al cambiar de año
                    monthSelect.value = ""; // Restablece el mes a la opción predeterminada
                    monthSelect.dispatchEvent(new Event('change')); // Forzar evento de cambio en el mes para actualizar

                    // Limpiar las tarjetas
                    affiliateProgramsDiv.innerHTML = '';
                    chartsContainer.innerHTML = '';
                    // Limpiar la selección del mes
                    monthSelect.value = '';
                    monthDetails.style.display = 'none'
                    
                    displayPrograms();
                    displayProductDataDetails(month, year)
                    //displayMonthDetails();
                });

                monthSelect.addEventListener('change', function () {
                    displayMonthDetails();
                });

                function displayPrograms() {
                    //console.log("Displaying programs...");
                    const year = yearSelect.value;
                    const filteredPrograms = programs.filter(program => program.year == year);
                    if (filteredPrograms.length === 0) {
                        chartsContainer.innerHTML = '';
                        return;
                    }
            
                    affiliateProgramsDiv.innerHTML = '';
                    chartsContainer.innerHTML = '';
            
                    const programsByYear = filteredPrograms.reduce((acc, program) => {
                        const { month, benefit, withdrawal_amount } = program;
                        if (!acc[year]) acc[year] = { benefits: Array(12).fill(0), withdrawals: Array(12).fill(0) };
                        acc[year].benefits[month - 1] = parseFloat(benefit) || 0;
                        acc[year].withdrawals[month - 1] = parseFloat(withdrawal_amount) || 0;
                        return acc;
                    }, {});
        
                    // Encontrar el primer y último mes con registro
                    const firstMonthWithData = Math.min(...filteredPrograms.map(program => program.month));
                    const lastMonthWithData = Math.max(...filteredPrograms.map(program => program.month));
            
                    Object.keys(programsByYear).forEach(year => {
                        const benefits = programsByYear[year].benefits;
                        const withdrawals = programsByYear[year].withdrawals.map(w => w);
                        const accumulatedBenefits = benefits.map((_, index) => {
                            return calculateAccumulatedBenefit(year, index + 1);
                        });
        
                        // Ajustar beneficios acumulados para no mostrar antes del primer mes ni después del último mes
                        const adjustedAccumulatedBenefits = accumulatedBenefits.map((value, index) => {
                            if (index + 1 < firstMonthWithData || index + 1 > lastMonthWithData) {
                                return { y: 0, color: 'rgba(0,0,0,0)' };
                            }
                            return { y: value, color: '#00AD65' };
                        });
            
                        const chartContainer = document.createElement('div');
                        chartContainer.className = 'col-12';
                        chartContainer.id = `chart-${year}`;
                        chartsContainer.appendChild(chartContainer);
                        
                        // Las barras con valor cero serán transparentes para que no ocupen espacio visible
                        const seriesDataBenefits = benefits.map((value, index) => ({
                            y: value,
                            color: value === 0 ? 'rgba(0,0,0,0)' : '#36E093',
                        }));
                        
                        const seriesDataAccumulated = adjustedAccumulatedBenefits;
                        
                        //console.log(`withdrawals es: ${withdrawals}`)
                        const chart = Highcharts.chart(`chart-${year}`, {
                            chart: {
                                type: 'column',
                                backgroundColor: '#ffffff',
                                spacing: [20, 20, 20, 20],
                                spacingTop: 35,
                                events: {
                                    load: displayDivWidth,
                                    redraw: displayDivWidth // Para actualizar el ancho cuando la gráfica se redimensiona
                                }
                            },
                            title: {
                                text: `Beneficios por mes en el año ${year}`,
                                style: {
                                    fontSize: '16px',
                                    fontWeight: '400',
                                    color: '#031549'
                                },
                                margin: 45
                            },
                            xAxis: {
                                categories: categories,
                                labels: {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: '400',
                                        color: '#031549'
                                    }
                                }
                            },
                            yAxis: {
                                visible: false
                            },
                            legend: {
                                layout: 'horizontal',
                                align: 'center',
                                verticalAlign: 'bottom',
                                floating: false,
                                itemStyle: {
                                    fontSize: '11px',
                                    fontWeight: '400',
                                    color: '#031549'
                                },
                                symbolRadius: 4,
                                symbolHeight: 10,
                                symbolWidth: 10,
                                symbolPadding: 10,
                                itemMarginBottom: 25,
                                itemDistance: 35,
                                margin: 35
                            },
                            credits: {
                                enabled: false
                            },
                            tooltip: {
                                shared: true,
                                pointFormat: '{series.name}: <b>$ {point.y:.2f}</b><br/>'
                            },
                            plotOptions: {
                                series: {
                                    cursor: 'pointer',
                                    events: {
                                        legendItemClick: function () {
                                            updateInputs();
                                        }
                                    }
                                },
                                column: {
                                    pointPadding: 0.5, // Espacio entre las barras dentro del mismo grupo
                                    groupPadding: 0.11, // Ajusta el espacio entre los grupos de barras
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    dataLabels: {
                                        enabled: true,
                                        style: {
                                            fontSize: '12px',
                                            fontWeight: '600',
                                            color: '#031549'
                                        },
                                        formatter: function () {
                                            return this.y !== 0 ? '$ ' + this.y.toFixed(2) : '';
                                        }
                                    }
                                }
                            },
                            series: [{
                                name: 'Beneficio',
                                data: seriesDataBenefits, // Datos de beneficios, incluyendo barras transparentes
                                color: '#36E093',
                                pointWidth: 30,
                                legendIndex: 0 // Esto posiciona esta serie en el primer lugar en la leyenda
                            }, {
                                name: 'Beneficio Acumulado',
                                data: seriesDataAccumulated, // Datos de beneficios acumulados, incluyendo barras transparentes
                                color: '#00AD65',
                                pointWidth: 30,
                                legendIndex: 1 // Esto posiciona esta serie en el segundo lugar en la leyenda
                            }, {
                                name: 'Retiros',
                                data: withdrawals, // Datos de retiros, incluyendo barras transparentes
                                color: '#E0364C',
                                pointWidth: 30,
                                legendIndex: 2 // Esto posiciona esta serie en el tercer lugar en la leyenda
                            }],
                            exporting: {
                                sourceWidth: 800,
                                sourceHeight: 400,
                                scale: 10,
                                chartOptions: {
                                    chart: {
                                        width: 1366,
                                        height: 768
                                    }
                                },
                                buttons: {
                                    contextButton: {
                                        menuItems: ['downloadPNG', 'downloadJPEG']
                                    }
                                },
                                lang: {
                                    contextButtonTitle: 'Menú contextual',
                                    downloadJPEG: 'Descargar JPEG',
                                    downloadPNG: 'Descargar PNG',
                                    printChart: 'Imprimir gráfico'
                                }
                            }
                        });
                    });
                    //console.log("Chart container:", chartsContainer);
                }

                function calculateAccumulatedBenefit(year, month) {
                    const selectedDate = new Date(year, month - 1);
                    const previousMonth = new Date(selectedDate);
                    previousMonth.setMonth(selectedDate.getMonth() - 1);

                    const filteredPrograms = programs.filter(program => {
                        const programDate = new Date(program.year, program.month - 1);
                        return programDate <= selectedDate;
                    });

                    const totalBenefits = filteredPrograms.reduce((total, program) => total + (parseFloat(program.benefit) || 0), 0);
                    const totalWithdrawals = filteredPrograms.filter(program => {
                        const programDate = new Date(program.year, program.month - 1);
                        return programDate <= previousMonth;
                    }).reduce((total, program) => total + (parseFloat(program.withdrawal_amount) || 0), 0);

                    return totalBenefits - totalWithdrawals;
                }

                function calculateTotalBillingLast12Months(year, month) {
                    const selectedDate = new Date(year, month);
                    const startDate = new Date(selectedDate);
                    startDate.setMonth(selectedDate.getMonth() - 12); // Retroceder 12 meses
                
                    const filteredPrograms = programs.filter(program => {
                        const programDate = new Date(program.year, program.month - 1);
                        return programDate >= startDate && programDate < selectedDate;
                    });
                    const totalBilling = filteredPrograms.reduce((total, program) => total + (parseFloat(program.total_billing) || 0), 0);
                
                    return totalBilling;
                }

                // Función para imprimir los total_billing de los últimos 12 meses
                function printTotalBillingLast12Months(year, month) {
                    const selectedDate = new Date(year, month);
                    const startDate = new Date(selectedDate);
                    startDate.setMonth(selectedDate.getMonth() - 12); // Retroceder 12 meses

                    const filteredPrograms = programs.filter(program => {
                        const programDate = new Date(program.year, program.month - 1);
                        return programDate >= startDate && programDate < selectedDate;
                    });

                    filteredPrograms.forEach(program => {
                    });
                }
                

                function displayMonthDetails() {
                    const year = yearSelect.value;
                    const month = monthSelect.value;
                    const yearInt = parseInt(year)
                    const monthInt = month !== '' ? parseInt(month) : 0
                    const filteredProgram = programs.find(program => program.year == year && program.month == month);
                    if ((!year || monthSelect.value === '') || (yearInt === yearCurrent && monthInt > monthCurrent)) {
                        monthDetails.style.display = 'none';
                        return;
                    } 
                    
                    if (month !== "") {
                        const categories = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];
                        selectedYear.textContent = year;
                        selectedMonth.textContent = categories[month - 1];
                        clients.textContent = filteredProgram ? filteredProgram.clients : 0;
                        newClients.textContent = filteredProgram ? filteredProgram.new_clients : 0;
                        totalClients.textContent = filteredProgram ? filteredProgram.total_clients : 0;
                        originalOrder.textContent = filteredProgram ? filteredProgram.original_order : 0;
                        renewalOrder.textContent = filteredProgram ? filteredProgram.renewal_order : 0;
                        uniqueOrder.textContent = filteredProgram ? filteredProgram.unique_order : 0;
                        billingEuros.textContent = filteredProgram ? parseFloat(filteredProgram.billing_euros).toFixed(2) : '0.00';
                        billingDollars.textContent = filteredProgram ? parseFloat(filteredProgram.billing_dollars).toFixed(2) : '0.00';
                        totalBilling.textContent = filteredProgram ? parseFloat(filteredProgram.total_billing).toFixed(2) : '0.00';
                        withdrawalMonth.textContent = categories[month - 1];
                        withdrawalAmount.textContent = filteredProgram ? parseFloat(filteredProgram.withdrawal_amount).toFixed(2) : '0.00';
                        // beneficio Total
                        totalBenefit.textContent = filteredProgram ? parseFloat(filteredProgram.benefit).toFixed(2) : '0.00';
                        // Calcular y mostrar el beneficio acumulado
                        const accumulated = calculateAccumulatedBenefit(year, month);
                        accumulatedBenefit.textContent = accumulated.toFixed(2);
                        
                        monthDetails.style.display = 'block';
                        // Generar las opciones usando un bucle for
                        
                        //printTotalBillingLast12Months(year, month)
                        totalBillingLast12Months.textContent = calculateTotalBillingLast12Months(year, month).toFixed(2);
                        const a = calculateTotalBillingLast12Months(year, month)  
                        //console.log('Total de facturación últimos 12 meses:', a);
                        displayProductDataDetails(month, year)
                    }
                }

                // Mostrar el año mas reciente de forma predeterminada
                const mostRecentYear = years[0];
                yearSelect.value = mostRecentYear;
                monthSelect.disabled = false;
                
                // Filtrar los meses disponibles
                if (mostRecentYear) {
                    const filteredProgramsByMostRecentYear = programs.filter(program => program.year == mostRecentYear);
                    const monthsWithData = filteredProgramsByMostRecentYear.map(program => parseInt(program.month));
                    monthSelect.querySelectorAll('option').forEach(option => {
                        const month = parseInt(option.value);
                        option.hidden = !monthsWithData.includes(month);
                    });

                    // Encontrar el último mes con registros
                    const lastMonthWithData = Math.max(...monthsWithData);

                    // Establecer el último mes con registros como seleccionado por defecto
                    monthSelect.value = lastMonthWithData.toString(); // Convertir a cadena antes de asignar
                }
                //console.log(month)
                // Mostrar las tarjetas solo cuando se selecciona un año y un mes
                if (mostRecentYear && month !== "") {
                    displayMonthDetails();
                } else {
                    affiliateProgramsDiv.innerHTML = ''; // Limpiar las tarjetas
                    monthDetails.style.display = 'none'; // Ocultar los detalles del mes
                }
                displayPrograms();

                filterForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    displayPrograms();
                    displayMonthDetails();
                });

                // Actualizar el ancho del div cuando la ventana se redimensiona
                window.addEventListener('resize', displayDivWidth);
                displayDivWidth(); // Mostrar el ancho inicial
            });
        });
    </script>
{% endblock javascripts %}
