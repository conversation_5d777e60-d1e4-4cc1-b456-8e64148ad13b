{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="fas fa-pencil-alt text-c-blue wid-20"></i>
            <span class="p-l-5">Listado de anotaciones</span>
        </h5>
        <button type="button"
            class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="tooltip" data-bs-placement="top" 
            title="Añadir anotación" id="addAnnotationBtn">
            <i class="fas fa-plus"></i>
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive text-nowrap" id="">
            <table
                id="annotations-list-table"
                class="table table-bordered table-hover nowrap dataTable-table"
                style="width: 100%;">
                <thead class="custom-table-head">
                    <tr>
                        <th style="width: 5%;">Categoria</th>
                        <th style="width: 10%;">Código/Registro</th>
                        <th>Anotación o comentario</th>
                        <th style="width: 5%;">Acciones</th>
                    </tr>
                </thead>
                <tbody id="">
                    {% for annot in annotations %}
                    <tr data-bs-id="{{ annot.pk }}">
                        <td class="align-middle">
                            {% if not annot.dictionary_content_type %}
                                <!-- Caso para General -->
                                <span class="badge rounded small" style="background-color: rgba(0, 123, 255, 0.2); color: #007bff;">
                                    General
                                </span>
                            {% elif annot.dictionary_content_type.model == 'accountexpenses' %}
                                <!-- Caso para Cuenta contable de gastos -->
                                <span class="badge badge-danger-lighten rounded small">
                                    Cuenta contable de gastos
                                </span>
                            {% elif annot.dictionary_content_type.model == 'accountsales' %}
                                <!-- Caso para Cuenta contable de ingresos -->
                                <span class="badge badge-success-lighten rounded small">
                                    Cuenta contable de ingresos
                                </span>
                            {% endif %}
                        </td>
                        <td class="align-middle">
                            <span>
                                {% if annot.dictionary_content_type %}
                                    {{ annot.dictionary_object_id }} - {{ annot.dictionary_object_selected }}
                                {% else %}
                                    {{ annot.id }} - Anotaciones generales para el vendedor
                                {% endif %}
                            </span>
                        </td>
                        <td class="align-middle">
                            <span>{{ annot.comment }}</span>
                        </td>
                        <td class="text-center">
                            <a
                                onClick="triggerUpdateAnnotation('{{ annot.id }}')"
                                class="text-warning f-14 me-2"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Editar"
                                role="button">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a
                                onClick="confirmDelete('{{ annot.id }}')"
                                class="text-danger f-14"
                                data-bs-id="{{ annot.id }}"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Eliminar"
                                role="button">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" 
    id="addAnnotationModal" tabindex="-1" 
    aria-labelledby="animateModalLabel" aria-modal="true" 
    role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="addAnnotationForm">
                {% csrf_token %}
                <div class="modal-header bg-dark">
                    <h5 id="" class="modal-title text-white">Añadir una anotación</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-2">
                        <div class="col-12">
                            <label for="id_model_name" class="col-form-label pb-0">Selecciona un diccionario</label>
                            <select class="form-select form-control" id="id_model_name" name="model_name" required>
                                <option value="" selected disabled>Selecciona una opción</option>
                                {% if not general_exists %}
                                    <option value="general">General</option>
                                {% endif %}
                                <option value="accountexpenses">Cuenta contable de gastos</option>
                                <option value="accountsales">Cuenta contable de ingresos</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-2" id="dictionary-object-row">
                        <div class="col-12">
                            <label for="id_dictionary_object_id" class="col-form-label pb-0">Selecciona la cuenta</label>
                            <select class="form-select form-control" id="id_dictionary_object_id" name="dictionary_object_id" required>
                                <option value="" selected disabled>Selecciona un diccionario primero</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-12">
                            <label for="comment" class="col-form-label pb-0">Anotación o comentario</label>
                            <textarea class="form-control" id="comment" placeholder="Escribe aquí la anotación" name="comment" rows="5" required></textarea>
                        </div>
                    </div>
                    <input type="hidden" name="seller" value="{{ seller.id }}">
                </div>
                <div class="modal-footer">
                    <button id="submitAnnotationButton" type="submit" class="btn btn-primary">Añadir</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" 
    id="updateAnnotationModal" tabindex="-1" 
    aria-labelledby="animateModalLabel" aria-modal="true" 
    role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="updateAnnotationForm">
                {% csrf_token %}
                <div class="modal-header bg-dark">
                    <h5 class="modal-title text-white">Modificar una anotación</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-2">
                        <div class="col-12">
                            <!-- Etiqueta dinámica para el tipo de anotación -->
                            <label id="type_label" for="id_model_name_update" class="col-form-label pb-0">Tipo de anotación</label>
                            <select class="form-select form-control" id="id_model_name_update" name="model_name_update">
                                <!-- Opciones dinámicas generadas por JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="row mb-2" id="dictionary-object-update-row">
                        <div class="col-12">
                            <!-- Etiqueta dinámica para el código/registro -->
                            <label id="code_label" for="id_dictionary_object_id_update" class="col-form-label pb-0">Código/Registro de la cuenta contable</label>
                            <select class="form-select form-control" id="id_dictionary_object_id_update" name="dictionary_object_id_update">
                                <!-- Opciones dinámicas generadas por JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-12">
                            <label for="comment_update" class="col-form-label pb-0">Anotación o comentario</label>
                            <textarea class="form-control" id="comment_update" placeholder="Escribe aquí la anotación" name="comment" rows="5" required></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="submitUpdateAnnotationButton" type="button" class="btn btn-primary">Actualizar</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>

    $(document).ready( function() {
        renderDataTable({id: '#annotations-list-table', type: 'anotaciones'});

        // Llamar una primera vez tras inicializar la tabla:
        checkIfGeneralExistsInTable();
    });

    document.querySelector('#addAnnotationBtn').addEventListener('click', function() {
        $('#addAnnotationModal').modal('show');
    });

    document.getElementById('addAnnotationForm').addEventListener('submit', async (e) => {
        e.preventDefault();
    
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
    
        const modelName = data.model_name;
    
        // Validar el modelo antes de enviar la solicitud
        if (!modelName) {
            showToast('Por favor selecciona un diccionario', 'error');
            return;
        }
    
        // Para General, eliminar dictionary_object_id del payload
        if (modelName === 'general') {
            delete data.dictionary_object_id;
        }
    
        document.getElementById('submitAnnotationButton').disabled = true;
    
        // Enviar la solicitud
        await createAnnotation(form, data);
    });
    

    document.getElementById('id_model_name').addEventListener('change', async (e) => {
        const modelName = e.target.value;

        const dictionaryRow = document.getElementById('dictionary-object-row');
        const dictionarySelect = document.getElementById('id_dictionary_object_id');

        if (modelName === 'general') {
            // Ocultar la fila del diccionario y deshabilitar su requisito
            dictionaryRow.style.display = 'none';
            dictionarySelect.removeAttribute('required');
        } else {
            // Mostrar la fila del diccionario y habilitar su requisito
            dictionaryRow.style.display = 'block';
            dictionarySelect.setAttribute('required', 'true');

        
            const urlMap = {
                'accountsales': "{% url 'dictionaries:list_account_sales' %}",
                'accountexpenses': "{% url 'dictionaries:list_account_expenses' %}"
            };

            const url = urlMap[modelName];
            if (!url) return;

            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                const select = document.getElementById('id_dictionary_object_id');
                select.innerHTML = '<option value="" selected disabled>Selecciona una opción</option>';

                data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.code;
                    option.textContent = `${item.code} - ${item.description}`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }
    });

    function checkIfGeneralExistsInTable() {
        const table = document.getElementById('annotations-list-table');
        if (!table) return;
    
        // Verifica si ya existe una anotación "General" en la tabla
        const hasGeneral = [...table.querySelectorAll('tbody tr')].some(tr => {
            const categoriaCell = tr.querySelector('td:nth-child(1)');
            return categoriaCell && categoriaCell.innerText.includes('General');
        });
    
        const select = document.getElementById('id_model_name');
        if (!select) return;
    
        // Quita la opción "General" si ya existe una anotación de este tipo
        const optionGeneral = select.querySelector('option[value="general"]');
        if (hasGeneral && optionGeneral) {
            optionGeneral.remove();
        } else if (!hasGeneral && !optionGeneral) {
            // Agregar la opción "General" si no existe en la tabla ni en el select
            const newOption = document.createElement('option');
            newOption.value = 'general';
            newOption.textContent = 'General';
            // Insertar justo después de "Selecciona una opción"
            const firstOption = select.querySelector('option[value=""]');
            if (firstOption) {
                select.insertBefore(newOption, firstOption.nextSibling);
            } else {
                // Si no hay "Selecciona una opción", agregar como primera opción
                select.insertBefore(newOption, select.firstChild);
            }
        }
    }
    

    function triggerUpdateAnnotation(annotationId) {
        const $modal = $('#updateAnnotationModal');
        const $form = $modal.find('form');
        const $modelSelect = $form.find('#id_model_name_update');
        const $dictionarySelect = $form.find('#id_dictionary_object_id_update');
        const $comment = $form.find('#comment_update');
    
        const $annotationRow = $(`#annotations-list-table tr[data-bs-id="${annotationId}"]`);
        const [dictionary, dictionaryObject, comment] = $annotationRow.find('td').map((_, td) => $(td).text().trim()).get();
    
        const isGeneral = dictionary.includes('General'); // Verificar si es general
    
        // Dinámicamente cambiar los títulos
        const $typeLabel = $form.find('#type_label');
        const $codeLabel = $form.find('#code_label');
    
        if (isGeneral) {
            $typeLabel.text('Tipo de anotación');
            $codeLabel.text('Código/Registro de la anotación');
        } else {
            const modelText = dictionary === 'accountexpenses' ? 'Cuenta contable de gastos' : 'Cuenta contable de ingresos';
            $typeLabel.text('Tipo de anotación');
            $codeLabel.text('Código/Registro de la cuenta contable');
        }
    
        // Actualizar los campos
        function createAndAppendOption($select, value, text) {
            const option = new Option(text, value);
            $select.append(option).val(value).prop('disabled', true);
        }
    
        $modelSelect.empty();
        const modelText = isGeneral ? 'General' : (dictionary === 'accountexpenses' ? 'Cuenta contable de gastos' : 'Cuenta contable de ingresos');
        createAndAppendOption($modelSelect, dictionary, modelText);
    
        $dictionarySelect.empty();
        createAndAppendOption($dictionarySelect, dictionaryObject, dictionaryObject);
    
        $comment.val(comment);
        $modal.modal('show');
    
        // Asegurar que el campo de comentario sea requerido
        $comment.prop('required', true);
    
        setTimeout(() => $comment.focus(), 500);
    
        $('#submitUpdateAnnotationButton').attr('onClick', `updateAnnotation('${annotationId}')`);
    }
    

    async function createAnnotation(form, formData) {
        const url = "{% url 'app_annotations:create_annotation' %}";
        let responseData = null;

        // Datos enviados al backend (para depuración)
        console.log({
            model_name: formData.model_name,  // Aquí se asegura que "general" sea enviado correctamente
            comment: formData.comment,
            seller: formData.seller,
        });
    
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}',
                },
                body: JSON.stringify(formData),
            });
    
            responseData = await response.json();
    
            if (response.ok) {
                showToast('Anotación creada con éxito', 'success');
                $('#submitAnnotationButton').prop('disabled', false);
                $('#addAnnotationModal').modal('hide');
                addAnnotationToTable(responseData);
            } else {
                handleInputErrors(form, responseData);
            }
        } catch (error) {
            console.error('Error al crear anotación:', error);
            showToast('Error inesperado al crear la anotación', 'error');
        }
    
        $('#submitAnnotationButton').prop('disabled', false);
    }
    

    async function updateAnnotation(annotationId) {
        const url = `{% url 'app_annotations:edit_annotation' 'ID_PLACEHOLDER' %}`.replace('ID_PLACEHOLDER', annotationId);
        const form = document.getElementById('updateAnnotationForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        $('#submitUpdateAnnotationButton').prop('disabled', true);

        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const data = await response.json();
            showToast('Anotación actualizada con éxito', 'success');
            updateAnnotationInTable(annotationId, data);
            $('#updateAnnotationModal').modal('hide');
        } else {
            const errorData = await response.json();
            handleInputErrors(form, errorData);
        }
        $('#submitUpdateAnnotationButton').prop('disabled', false);
    }

    function handleInputErrors(form, errorData) {
        $(form).find('.is-invalid').removeClass('is-invalid');
        $(form).find('.error-message').remove();
    
        for (const [field, errorMessages] of Object.entries(errorData.errors)) {
            const $field = $(form).find(`[name="${field}"]`);
            $field.addClass('is-invalid');
    
            // Verificar si errorMessages es un array o no
            const errorText = Array.isArray(errorMessages)
                ? errorMessages.join(', ') // Unir mensajes si es un array
                : String(errorMessages); // Convertir a string si no lo es
    
            $field.after(`<div class="invalid-feedback error-message">${errorText}</div>`);
        }
    }

    function handleServerErrors(form, errorData) {
        const $modalBody = $(form).find('.modal-body');
        $modalBody.prepend(`
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <strong>Error!</strong> ${errorData.message}. Revise los datos e intente nuevamente.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `);
        
    }

    async function deleteAnnotation(annotationId) {
        let url = `{% url 'app_annotations:delete_annotation' 'ID_PLACEHOLDER' %}`
        .replace('ID_PLACEHOLDER', annotationId);

        const response = await fetch(url, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}',
            },
        });

        if (response.ok) {
            showToast('Anotación eliminada', 'success');
            removeAnnotationFromTable(annotationId);
        } else {
            showToast('Error al eliminar la anotación', 'error');
        }
    }

    function confirmDelete(annotationId) {
        Swal.fire({
            title: '¿Estás seguro?',
            text: "No podrás revertir esta acción",
            icon: 'warning',
            cancelButtonText: 'Cancelar',
            showCancelButton: true,
            showDenyButton: true,
            showConfirmButton: false,
            denyButtonText: `Eliminar`,
        }).then((result) => {
            if (result.isDenied) {
                deleteAnnotation(annotationId);
            }
        });
    }

    function addAnnotationToTable(responseData) {
        const annotation = responseData.data;
        const table = $('#annotations-list-table').DataTable();
    
        let badgeHtml = '';
        let secondCol = '';
    
        // Lógica para el color y texto del badge
        if (annotation.content_type === 'accountexpenses') {
            // Rojo
            badgeHtml = `
                <span class="badge badge-danger-lighten rounded small">
                    Cuenta contable de gastos
                </span>`;
            secondCol = `${annotation.dictionary_object_id} - ${annotation.dictionary_object_selected}`;
        } else if (annotation.content_type === 'accountsales') {
            // Verde
            badgeHtml = `
                <span class="badge badge-success-lighten rounded small">
                    Cuenta contable de ingresos
                </span>`;
            secondCol = `${annotation.dictionary_object_id} - ${annotation.dictionary_object_selected}`;
        } else {
            // 'general'
            badgeHtml = `
                <span class="badge rounded small" 
                      style="background-color: rgba(0, 123, 255, 0.2); color: #007bff;">
                    General
                </span>`;
            // Columna de código => "General"
            secondCol = `${ annotation.id } - Anotaciones generales para el vendedor`;
        }
    
        // Links de edición/eliminación con colores correctos
        const editBtn = `
            <a onClick="triggerUpdateAnnotation('${annotation.id}')"
               class="text-warning f-14 me-2"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               title="Editar"
               role="button">
                <i class="fas fa-edit"></i>
            </a>
        `;
        const deleteBtn = `
            <a onClick="confirmDelete('${annotation.id}')"
               class="text-danger f-14"
               data-bs-id="${annotation.id}"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               title="Eliminar"
               role="button">
                <i class="fas fa-trash"></i>
            </a>
        `;
    
        // Añadimos la nueva fila
        const newRow = table.row.add([
            badgeHtml,
            secondCol, 
            annotation.comment,
            editBtn + deleteBtn
        ]).draw().node();
    
        // Para tener coherencia con el resto de la tabla
        $(newRow).attr('data-bs-id', annotation.id);
        $(newRow).find('td').eq(3).addClass('text-center');
    
        // Re-inicializar tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();

        // **Aquí llamamos a la verificación**:
        checkIfGeneralExistsInTable();
    }
    

    function updateAnnotationInTable(annotationId, data) {
        const annotation = data.data;
        const table = $('#annotations-list-table').DataTable();
        const row = table.row(`[data-bs-id="${annotationId}"]`);

        if (row) {
            const rowData = row.data();
            rowData[2] = annotation.comment;
            
            row.data(rowData).draw();
        }
    }


    function removeAnnotationFromTable(annotationId) {
        const table = $('#annotations-list-table').DataTable();
        const row = table.row(`[data-bs-id="${annotationId}"]`);

        if (row) {
            row.remove().draw();
        }

        // **Verificar si sigue existiendo una "General" en la tabla**:
        checkIfGeneralExistsInTable();
    }

    $('#addAnnotationModal').on('hidden.bs.modal', function (e) {
        $('#addAnnotationForm').find('.is-invalid').removeClass('is-invalid');
        $('#addAnnotationForm').find('.error-message').remove();
        $('#addAnnotationForm').find('.alert').remove();
        $('#addAnnotationForm').trigger('reset');
        const select = document.getElementById('id_dictionary_object_id');
        select.innerHTML = '<option value="" selected disabled>Selecciona un diccionario primero</option>';
    });

    $('#updateAnnotationModal').on('hidden.bs.modal', function (e) {
        $('#updateAnnotationForm').find('.is-invalid').removeClass('is-invalid');
        $('#updateAnnotationForm').find('.error-message').remove();
        $('#updateAnnotationForm').find('.alert').remove();
        $('#updateAnnotationForm').trigger('reset');
    });

</script>