from django.db import models

class InvoiceDiscardReason(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=100,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Motivo de descarte"
        verbose_name_plural = "Motivos de descarte"
    
    def __str__(self):
        return self.description
    

# @admin.register(InvoiceDiscardReason)
# class InvoiceDiscardReasonAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]