from django.db import models

class Marketplaces(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    class Meta:
        verbose_name = "Marketplace"
        verbose_name_plural = "Marketplaces"
    
    def __str__(self):
        return self.description
    
# @admin.register(Marketplaces)
# class MarketplacesAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
