{% extends "layouts/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Eliminar Cliente{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">{{ object.name }}</h5>
          </div>
          <ul class="breadcrumb">
           <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name|title}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_customers:list' seller.shortname  %}">Clientes</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_customers:detail' seller.shortname object.pk %}"> {{ object.name | title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Eliminar</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        <div class="text-center">  
          <h1>Eliminar Cliente</h1>
          <p>¿Está seguro de que desea eliminar el cliente "{{ object.name }}"?</p>
          <p><b>Nombre:</b> {{ object.name }}</p>
          <p><b>NIF/CIF/IVA:</b> {{ object.nif_cif_iva }}</p>
          <div class="control-group">
            <div class="controls">  
              {% if invoice_count == 0 %}
                <button type="submit" class="btn btn-danger">Eliminar</button>
              {% endif %}
            </div>
          </div>
        </div>
      </form>
        <div class="text-center">
        {% if not customer_list.exists  and invoice_count > 0 %}
                 <p class="col-form-label ml-0 mt-3"><b>Este cliente tiene {{ invoice_count }} facturas asociadas. Si desea eliminarlo, debe crear antes un nuevo cliente y asociarle sus facturas</b></p>
        {% elif invoice_count > 0 %}
          <label class="col-form-label ml-0 mt-3"><b>Este cliente tiene {{ invoice_count }} facturas asociadas. Si desea eliminarlo, debe asignar estas facturas a otro cliente.</b></label>
          <form class="form-horizontal" method="post" enctype="multipart/form-data" action="{% url 'app_customers:delete' object.seller.shortname object.pk %}">
              {% csrf_token %}             
                <div class="col-6  mx-auto mt-2">
                  <select class="form-control form-select"  name="customer" id="customer">
                    {% for customer in customer_list %}
                      <option value="{{ customer.pk }}">{{ customer.name }}, {{customer.country}}</option>
                    {% endfor %}
                  </select>
                </div>     
                <div class="col text-center mt-3">
                  <button type="submit" class="btn btn-danger"><b>Asignar y Eliminar</b></button>
                </div>         
            </form>
        </div>
      {% endif %}
    </div>
  </div>
{% endblock content %}
