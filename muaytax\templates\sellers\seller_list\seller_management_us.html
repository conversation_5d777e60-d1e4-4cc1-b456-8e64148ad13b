{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Gestoría Estados Unidos
{% endblock title %}

{% block stylesheets %}
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.bootstrap5.min.css' %}" type="text/css"/>
    <link rel="stylesheet" href="{% static 'assets/datatables/datatable/2.0.7/css/dataTables.dataTables.css' %}" type="text/css"/>
    <link rel="stylesheet" href="{% static 'assets/datatables/fixedcolumns/5.0.1/css/fixedColumns.dataTables.css' %}" type="text/css"/>
    <!-- FontAwesome -->
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}" type="text/css" />
    <!-- DataTables Buttons -->
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/dataTables/buttons.dataTables.min-v3.0.1.css' %}" type="text/css" />
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/uicons/css/uicons-bold-rounded.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/swal/custom-swal-buttons.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/fonts/themify/themify.css' %}">

    <style>
        .no-wrap-with-fit-content {
            min-width: fit-content;
            text-wrap: nowrap;
        }
        .switch-small{
            transform: scale(0.7);
        }
        .fs-custom{
            font-size: 0.75rem;
        }
        .fs-custom-md{
            font-size: 0.9em;
        }
        /* .table{
            table-layout: fixed;
        } */
        .custom-table-head {
            background-color: #f2f2f2!important;
        }
        .header_name {
            width: 25%!important;
            word-wrap: break-word;
            white-space: normal;
        }
        .accounting,
        .login {
            width: 150px!important;
        }
        .truncate-text{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;    
        }
        .pendings,
        tbody tr td.login {
            font-size: .9em;
        }
        .actions{
            text-align: center!important;
        }

        .modal-size {
            max-width: 80%;
        }

        .list-causes-warning {
            list-style-type: circle;
        }

        .fix-borders{
            border-top-right-radius: .25rem!important;
            border-bottom-right-radius: .25rem!important;
        }
    </style>
{% endblock stylesheets %}

{% block breadcrumb %}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h5 class="m-b-10">Gestoría Estados Unidos</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href=".">Gestoría Estados Unidos</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
{% endblock breadcrumb %}
{% block content %}
    <div class="row">
        <!-- cartas con valores totales -->
        <div class="col-12">
            <div class="row">
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS REQ. <br> NO INICIADO</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="required-notstarted-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-solid fa-file-contract" style="color:#7c7d7e;"></i>&nbsp;&nbsp;
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS REQ. <br> NO COMPLETADO</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="required-notcompleted-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-solid fa-pencil" style="color:#7c7d7e;"></i>&nbsp;&nbsp;
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS REQ. <br> COMPLETADO</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="required-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-solid fa-file-contract " style="color: #FF0000;"></i>&nbsp;&nbsp;
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS <br>PENDIENTES</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="pending-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-regular fa-clock fa-sm" style="color: #7c7d7e;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS <br>RECHAZADOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="disagreed-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-solid fa-xmark" style="color: #FE8330;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS <br>ACEPTADOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="agreed-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-solid fa-check" style="color: #ffd700;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS ENVIADOS POR FAX</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="fax-send-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fa-solid fa-envelope-circle-check" style="color: #427ddb;"></i>&nbsp;&nbsp;
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6 class="width-fit-content"><b>MODELOS <br>PRESENTADOS</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="presented-model-count">&nbsp;</b>&nbsp;&nbsp;
                                        <i class="fas fa-check-double" style="color: #02c018;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- search input -->
        <div class="col-12 mb-3">
            <div class="row">
                <div class="col-12 col-lg-6 d-flex justify-content-start filters-row">
                    <div class="col-12 col-lg-8 me-3">
                        <div class="input-group">
                            <input class="form-control fix-borders" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()">
                            <span class="mdi mdi-magnify search-icon"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- card with filters and table -->
        <div class="col-12">
            <div class="card rounded">
                <div class="card-body">
                    <div class="row">
                        <div class="d-flex flex-column-reverse flex-lg-row">
                            <!-- Filter Section starts -->
                            <div class="gap-3 col-sm-12 col-lg-6 d-flex flex-column flex-lg-row justify-content-center justify-content-lg-start filters-row mb-2 min-width-fit-content flex-fill">
                                <div class="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label class="btn btn-light border align-content-center" title="Anual">
                                        <input type="radio" name="periods" class="period-filter" autocomplete="off" value="0A">
                                        <span class="d-none d-lg-none d-xl-block no-wrap-with-fit-content">Anual</span>
                                        <span class="d-none d-md-block d-xl-none">Anual</span>
                                        <span class="d-sm-block d-md-none d-xl-none">0A</span>
                                    </label>
                                </div>
                                <div class="d-flex flex-nowrap">
                                    <select role="button" class="form-select form-control btn-light border" name="year-input" id="year" onchange="onChangePeriodYear()">
                                        <option value="2022">2022 &nbsp;&nbsp;&nbsp;</option>
                                        <option value="2023">2023 &nbsp;&nbsp;&nbsp;</option>
                                        <option value="2024">2024 &nbsp;&nbsp;&nbsp;</option>
                                        <option value="2025">2025 &nbsp;&nbsp;&nbsp;</option>
                                    </select>
                                </div>
                            </div>
                            <!-- Filter Section ends -->
                            <!-- starts table action buttons -->
                            <div class="col-sm-12 col-lg-6 d-flex justify-content-center justify-content-lg-end filters-row mb-2 min-width-fit-content flex-basis-0">
                                <div class="table-action-button-container">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 d-flex flex-nowrap gap-1 align-items-center" onclick="openInfoModal()">
                                        <i class="fa-regular fa-circle-question fa-lg me-0"></i>
                                        &nbsp;Leyenda
                                    </button>
                                </div>
                                <div class="table-action-button-container position-relative">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 justify-content-center d-flex flex-nowrap gap-1 align-items-center" id="columnsDropdownButton">
                                        <i class="fas fa-columns fa-lg me-0"></i>
                                        &nbsp;Columnas
                                    </button>
                                    <div id="columnsDropdownArea" class="dropdown-form-columns card rounded">
                                        <div class="card-header p-3">
                                            <h6 class="mb-0"><b>Configuración de columnas</b></h6>
                                        </div>
                                        <div class="py-3">
                                            <div class="px-3">
                                                <p class="mb-1">Modelos</p>
                                            </div>
                                            <div id="monthsContainer">
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="column1">5472-1120</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="column1" class="column-switch" data-column="model_5472">
                                                            <label for="column1" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="column2">7004</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="column2" class="column-switch" data-column="model_7004">
                                                            <label for="column2" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="column3">BE-15</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="column3" class="column-switch" data-column="model_be_15">
                                                            <label for="column3" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="px-3">
                                                    <div class="d-flex justify-content-between align-items-center border-bottom">
                                                        <label class="form-check-label text-dark fs-custom" for="column4">ES-184</label>
                                                        <div class="switch switch-primary d-inline switch-small">
                                                            <input type="checkbox" id="column4" class="column-switch" data-column="model_es_184">
                                                            <label for="column4" class="cr"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="p-3">
                                            <div class="d-grid">
                                                <button id="selectAllModelColumnsButton" class="btn btn-dark border-0 me-0" onclick="toggleSelectAllModelColumns(this)">
                                                    Seleccionar todos
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-action-button-container">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 d-flex flex-nowrap gap-1 align-items-center" onclick="confirmUpdateTable()">
                                        <i class="fi fi-br-refresh me-0"></i>
                                        &nbsp;Actualizar
                                    </button>
                                </div>
                                <div class="dropdown-area-rtl table-action-button-container">
                                    <button class="btn btn-outline-light text-dark border-0 mb-0 me-0 d-flex flex-nowrap gap-1 align-items-center"
                                        type="button"
                                        id="actionsDropdownMenuButton"
                                        data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="fa-solid fa-ellipsis-v fa-lg me-0"></i>
                                        &nbsp;Acciones
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="actionsDropdownMenuButton">
                                    <li>
                                        <a class="dropdown-item" href="#" id="exportExcelBtn">
                                            <i class="fa-solid fa-download fa-lg me-1"></i> Descargar Excel
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="javascript:void(0)" onclick="goToCreateNotification('accounting-es-department')">
                                            <i class="fa-solid fa-bell fa-lg me-0"></i>
                                            &nbsp;Crear notificación
                                        </a>
                                    </li>
                                    </ul>
                                </div>
                            </div>
                            <!--ends table action buttons -->
                        </div>
                        <div class="table-responsive">
                            <table id="management-usa-table" class="table table-hover stripe nowrap border" style="width: 100%;">
                                <thead class="custom-table-head">
                                    <tr>
                                        <th>Nombre</th>
                                        <th>Email</th>
                                        <th>Contabilidad</th>
                                        <th>Priority min</th>
                                        <th>Priority avg</th>
                                        <th>Premium LLC</th>
                                        <th>Mantenimiento</th>
                                        <th>Form 5472-1120</th>
                                        <th>Modelo 7004</th>
                                        <th>Modelo BE-15</th>
                                        <th>Modelo ES-184</th>
                                        <th>Último acceso</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Info -->
        {% include 'gestoria/include/modal_info.html'  with hidden=False %}
    </div>
{% endblock content %}

{% block javascripts %}

<!-- jQuery (requerido por DataTables 2.x) -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/jquery/jquery-3.7.0.min.js"></script>
<!-- DataTables 2.0.7 -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables-v2.0.7.js"></script>
<!-- SweetAlert2 -->
<script src="{{ STATIC_URL }}assets/js/plugins/sweetalert2.all.min.js"></script>
<!-- Plugins de DataTables 2.0.7-->
<script src="{{ STATIC_URL }}assets/datatables/fixedcolumns/5.0.1/js/dataTables.fixedColumns.js"></script>
<script src="{{ STATIC_URL }}assets/datatables/fixedcolumns/5.0.1/js/fixedColumns.dataTables.js"></script>
<!-- Buttons 3.0.1 JS -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/buttons/dataTables.buttons.min.-v3.0.1.js"></script>
<!-- Buttons HTML5 3.0.1 JS -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/buttons/buttons.html5.min-v3.0.1.js"></script>
<!-- JSZip (para exportación a Excel) -->
<script src="{{ STATIC_URL }}assets/cdns_locals/js/cloudflare/jszip/jszip.min-v3.10.1.js"></script>

<!-- start DEBUG -->
<script type="text/javascript">
    const debug = {{ debug|yesno:"true,false" }};
    // Función para debug (imprime en consola solo si debug está habilitado)
    function debugLog(...args) {
        if (debug) {
        console.log(...args);
        }
    }
    debugLog("Debug mode is enabled")
</script>
<!-- end DEBUG -->
<script>
    let table = null;
    let periodTxt = '0A';
    let yearFilter = '{{year}}'

    $(document).ready(function () {
        const $periodFilter = $('.period-filter');

        $periodFilter.each(function() {
            const $this = $(this);
            if ($this.val() === periodTxt) {
                $this.prop('checked', true)
                    .parent().addClass('active');
            }
        });
        document.getElementById('year').value = '{{year}}';

        $periodFilter.on('change', function() {
            onChangePeriodYear();
        });

        createDT();
        
        $('.search-icon').click(function(){
            $(this).closest('.input-group').find('input[type="search"]').focus();
        });
    });

    const createDT = async () => {
        table = $('#management-usa-table').DataTable({
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Exportar a Excel',
                    exportOptions: {
                        columns: function (idx, data, node) {
                            // Exportar todas las columnas excepto aquellas con clase .actions,
                            // pero incluir explícitamente la columna de email
                            const visibleColumns = [1];  // Índice de la columna 'email' a exportar
                            return $(node).hasClass('actions') ? false : (visibleColumns.includes(idx) || table.column(idx).visible());
                        },
                        rows: null,  // Exportar todas las filas
                        format: {
                            body: function (data, row, column, node) {
                                debugLog(
                                    "=== Exportando fila ===\n" +
                                    `Datos: ${JSON.stringify(data)}\n` +
                                    `Índice de fila (Real en DataTables): ${row}\n` +           
                                    `Índice de columna (Real en DataTables): ${column}\n`
                                );
                                debugLog("\n ======= START =======\n");
                    
                                let visibleIndexes = table.rows({ search: 'applied' }).indexes().toArray();
                                debugLog("Index - filas visibles:", visibleIndexes);
                                // let rowDataSeg = table.row(visibleIndexes[row]).data();
                                let rowData = table.row(row).data();

                                if (!rowData) {
                                    console.error(`No se encontró la fila ${visibleIndexes[row]} en la Tabla: `);
                                    return "Error de datos";
                                }
                    
                                debugLog("Datos reales de la fila:", rowData);
                    
                                let columnData = table.settings()[0].aoColumns[column];
                                if (!columnData) {
                                    console.warn(`ERROR: Índice de columna ${column} fuera de rango.`);
                                    return "Error de columna";
                                }
                    
                                debugLog(`Procesando columna ${columnData.data} ...`);

                                // Procesar columna `user_name`
                                if (columnData.data === "user_name") {
                                    debugLog("Procesando columna user_name...");
                                    let name = rowData.user_name || "Desconocido";
                                    return `${name}`.trim();
                                }

                                // Procesar columna `email`
                                if (columnData.data === "email") {
                                    debugLog("Procesando columna email...");
                                    let email = rowData.email || "";
                                    return `${email}`.trim();
                                }
                                
                                // Lista para columnas que se mapean en funcion de las fechas de servicios 
                                const columnsToProcess = [
                                    "contracted_accounting_usa_all_date",
                                    "is_llc_premium_direction",
                                    "contracted_maintenance_llc_date",
                                ];

                                // Procesar columnas personalizadas
                                if (columnsToProcess.includes(columnData.data)) {
                                    debugLog(`Procesando columna ${columnData.data}...`);

                                    if (columnData.data === "contracted_accounting_usa_all_date") {
                                        let date = rowData.contracted_accounting_usa_all_date;
                                        if (date) {
                                            let invoices = rowData.num_pending_invoices ?? "Sin datos";
                                            let percentage = rowData.percentage_pending_invoices ?? "Sin datos";
                                            percentage = percentage !== "Sin datos" ? `(${percentage}%)` : "";
                                            return `${invoices} ${percentage}`.trim();
                                        }
                                        return "No contratado";
                                    }

                                    if (columnData.data === "is_llc_premium_direction") {
                                        return rowData.is_llc_premium_direction ? "Contratado" : "No contratado";
                                    }
                                    
                                    if (columnData.data === "contracted_maintenance_llc_date") {
                                        let startDate = rowData.contracted_maintenance_llc_date;
                                        let endDate = rowData.contracted_maintenance_llc_end_date;
                                    
                                        if (!startDate) return "No contratado";
                                    
                                        let formattedStartDate = `Alta: ${formatDate(startDate, false)}`;
                                        let formattedEndDate = endDate ? `/ Baja: ${formatDate(endDate, false)}` : '';
                                        
                                        return `${formattedStartDate} ${formattedEndDate}`.trim()
                                    }
                                    
                                }

                                // Mapear valores de modelos 5472, 7004, BE-15
                                if (columnData.data.startsWith("model_")) {
                                    let statusMap = {
                                        "processed": "Listo para generar",
                                        "pending": "Pendiente de aprobación",
                                        "agreed": "Aceptado",
                                        "disagreed": "Rechazado",
                                        "presented": "Presentado",
                                        "fax_send": "Fax enviado",
                                        "not-required": "No requerido",
                                        "required": "Requerido",
                                        "not-processed": "Formulario Incompleto",
                                        "not-started": "No ha iniciado",
                                        
                                        // Códigos de errores o Advertencias
                                        "warning16": "Transacciones sin conversión o valor=0",
                                        "warning15": "Falta información en el formulario"
                                    };
                                    return statusMap[rowData[columnData.data]] || "Desconocido";
                                }

                                // Procesar `last_login`
                                if (columnData.data === "last_login") {
                                    debugLog("Procesando columna last_login...");
                                    return formatDate(rowData.last_login, true);
                                }

                                // Función reutilizable para formatear fechas
                                function formatDate(dateStr, includeTime = true) {
                                    if (!dateStr) return "Sin datos"; // Si no hay fecha, retorna "Sin datos"
            
                                    let dateObj = new Date(dateStr.replace(" ", "T"));
            
                                    if (isNaN(dateObj.getTime())) {
                                        debugLog("Fecha inválida:", dateStr);
                                        return "Sin datos";
                                    }
            
                                    let options = {
                                        day: "2-digit",
                                        month: "short",
                                        year: "numeric",
                                        ...(includeTime && { hour: "2-digit", minute: "2-digit", hour12: false })
                                    };
            
                                    let formattedDate = dateObj.toLocaleDateString("es-ES", options);
                                    if (includeTime) {
                                        formattedDate = formattedDate.replace(",", " -");
                                    }
            
                                    debugLog(`Fecha transformada: ${dateStr} → ${formattedDate}`);
                                    return formattedDate;
                                }
                                
                                // Retornar texto limpio por defecto
                                return $('<div>').html(data).text().trim() || "Sin datos";
                            }
                        }
                    },
                    customize: function (xlsx) {
                        debugLog("=== Depuración: Iniciando actualización del formato en Excel ===");
                    
                        let sheet = xlsx.xl.worksheets['sheet1.xml'];
                        let stylesXml = xlsx.xl['styles.xml'];
                    
                        debugLog("Depuración: Contenido inicial del XML de la hoja de cálculo:");
                    
                        // Modificar el título en la celda A1
                        debugLog("Actualizando el título...");
                        let titleCell = $('row:first c[r="A1"] is t', sheet);
                        if (titleCell.length) {
                          titleCell.text("Tabla listado Gestoría USA");
                        } else {
                          console.error("No se encontró la celda A1 para modificar el título.");
                        }
                    
                        debugLog("Aplicando estilos a la tabla...");
                    
                        // Verificar si `cellXfs` existe en `styles.xml`
                        let cellXfs = $('cellXfs', stylesXml);
                        if (cellXfs.length === 0) {
                          console.error("No se encontró la sección cellXfs en styles.xml.");
                          return;
                        }
                    
                        let fills = $('fills', stylesXml);
                        let borders = $('borders', stylesXml);
                        let newStyleIndex = $('xf', cellXfs).length;
                        let boldCenterIndex = newStyleIndex;
                        let centerIndex = newStyleIndex + 1;
                        let firstColumnStyleIndex = newStyleIndex + 2;
                        let secondRowStyleIndex = newStyleIndex + 3;
                        let altaFontIndex = $('font', stylesXml).length;
                        let bajaFontIndex = altaFontIndex + 1;
                    
                        debugLog(`Agregando nuevos estilos...`);
                    
                        // Agregar fondo verde claro para la primera columna (excepto A1 y A2)
                        let greenFillIndex = $('fill', fills).length;
                        fills.append(`
                            <fill>
                                <patternFill patternType="solid">
                                    <fgColor rgb="C6EFCE"/> <!-- Verde claro -->
                                    <bgColor indexed="64"/>
                                </patternFill>
                            </fill>
                        `);
                    
                        // Agregar fondo gris para la segunda fila
                        let grayFillIndex = $('fill', fills).length;
                        fills.append(`
                            <fill>
                                <patternFill patternType="solid">
                                    <fgColor rgb="E0E0E0"/> <!-- Gris claro -->
                                    <bgColor indexed="64"/>
                                </patternFill>
                            </fill>
                        `);
                    
                        // Agregar borde a la lista de estilos de bordes
                        let borderIndex = $('border', borders).length;
                        borders.append(`
                            <border>
                                <left style="thin"><color auto="1"/></left>
                                <right style="thin"><color auto="1"/></right>
                                <top style="thin"><color auto="1"/></top>
                                <bottom style="thin"><color auto="1"/></bottom>
                            </border>
                        `);
                    
                        // Estilo de negrita y centrado horizontal y vertical (para el título y primera columna)
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="2" fillId="0" borderId="${borderIndex}" applyFont="1" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center"/>
                            </xf>
                        `);
                    
                        // Estilo de solo centrado horizontal y vertical (para el resto de la tabla)
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="0" fillId="0" borderId="${borderIndex}" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center"/>
                            </xf>
                        `);
                    
                        // Estilo de la primera columna con fondo verde claro y bordes (excepto las dos primeras celdas)
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="2" fillId="${greenFillIndex}" borderId="${borderIndex}" applyFont="1" applyFill="1" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center" wrapText="1"/>
                            </xf>
                        `);
                    
                        // Estilo especial para la segunda fila en gris con bordes
                        cellXfs.append(`
                            <xf numFmtId="0" fontId="2" fillId="${grayFillIndex}" borderId="${borderIndex}" applyFont="1" applyFill="1" applyAlignment="1" applyBorder="1">
                                <alignment horizontal="center" vertical="center"/>
                            </xf>
                        `);
                    
                        debugLog("Aplicando estilos a las celdas...");

                        // Definir estilos de color en fonts.xml
                            $('fonts', stylesXml).append(`
                            <font><color rgb="008000"/></font> <!-- Verde para "Alta:" -->
                            <font><color rgb="FF0000"/></font> <!-- Rojo para "Baja:" -->
                        `);

                        $('row c[r]', sheet).each(function () {
                            let cell = $(this);
                            let cellText = cell.text();
                        
                            if (cellText.includes('\n')) {
                                let [startDate, endDate] = cellText.split('\n').map(text => text.trim());
                                let altaTexto = "";
                                let bajaTexto = "";
                        
                                if (startDate) {
                                    altaTexto = `
                                        <r>
                                            <rPr><rFont val="Calibri"/><color rgb="008000"/></rPr>
                                            <t>Alta: ${startDate}</t>
                                        </r>
                                        <r>
                                            <t xml:space="preserve">&#10;</t>  <!-- Salto de línea -->
                                        </r>
                                    `;
                                }
                        
                                if (endDate) {
                                    bajaTexto = `
                                        <r>
                                            <rPr><rFont val="Calibri"/><color rgb="FF0000"/></rPr>
                                            <t>Baja: ${endDate}</t>
                                        </r>
                                    `;
                                }
                        
                                let newXml = `${altaTexto}${bajaTexto}`;
                                cell.html(`<is>${newXml}</is>`);
                                cell.attr("t", "inlineStr");
                        
                                // Aplicar wrapText en la celda
                                if (!$('alignment', cell).length) {
                                    cell.append('<alignment wrapText="1"/>');
                                }
                            }
                        });
                        
                        // Ajustar altura de todas las filas (excepto las dos primeras)
                        $('row:not(:lt(2))', sheet).attr('customHeight', '1').attr('ht', 'auto');
                    
                        // Aplicar negrita y centrado al título
                        $('row:first c[r="A1"]', sheet).attr('s', boldCenterIndex);
                    
                        // Aplicar centrado a toda la tabla
                        $('row:not(:first) c', sheet).attr('s', centerIndex);
                    
                        // Aplicar fondo verde claro a la primera columna (excepto las dos primeras celdas)
                        $('row:not(:lt(2)) c[r^="A"]', sheet).attr('s', firstColumnStyleIndex);
                    
                        // Aplicar fondo gris a la segunda fila
                        $('row:eq(1) c', sheet).attr('s', secondRowStyleIndex);
                    
                        debugLog("Formato de la tabla actualizado.");
                    
                        // Ajustar altura de fila (excepto para las dos primeras filas)
                        debugLog("Ajustando altura de filas...");
                        $('row:not(:lt(2))', sheet).attr('ht', '40').attr('customHeight', '1');
                    
                        debugLog("Altura de filas ajustada y ajuste de texto aplicado.");
                    
                        debugLog("Depuración: XML modificado de la hoja de cálculo:");
                        debugLog(new XMLSerializer().serializeToString(sheet));
                    
                        debugLog("=== Depuración finalizada ===");
                    }
                }
            ],
            "serverSide": false,
            "scrollX": true,
            "scrollY": "calc(100vh - 250px)",
            "scrollCollapse": true,
            "ajax": {
                "dataSrc": "data",
                "url": "{% url 'app_lists:seller_management_us_dt_list' %}",
                "data": await function (d) {
                    ajaxData(d);
                }
            },
            "language": {
                "url": "{% static 'assets/datatables/plug-ins/1.11.3/i18n/es_es.json' %}",
                "lengthMenu": "_MENU_",
                "zeroRecords": "No se han encontrado vendedores.",
                "info": "_START_ a _END_ de un total de _TOTAL_ registros",
                "search": "Buscar:",
                "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                "infoFiltered": ""
            },
            "columns": [
                {   // Columna Nombre
                    "data": "user_name",
                    "className": "header_name",
                    "render": function (data, type, row) {
                        let html = '';
                        html += '<td class="align-middle">';
                        html += '<div class="d-inline-block">';
                        html += '<h6 class="m-b-0 fs-custom-md truncate-tex"><b>';

                        let name = row.seller_name;
                        if (typeof name === 'string') {
                        const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
                        const words = row.seller_name.split(' ').map(function (word) {
                            const lowerWord = word.toLowerCase();
                            if (lowerCaseSuffixes.includes(lowerWord)) {
                            return word.toUpperCase();
                            } else {
                            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                            }
                        });
                        html += words.join(' ');
                        }
                        html += '</b>';
                        if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                        html += ' - ' + row.user_name.split(' ').map(function (word) {
                            return word.charAt(0).toUpperCase() + word.slice(1);
                        }).join(' ');
                        }

                        html += '</h6>';
                        html += '<p class="m-b-0 fs-custom-md">' + row.email.toLowerCase() + '</p>';
                        html += '</div>';
                        html += '</td>';

                        return html;
                    }
                },
                {   // Columna Email
                    "data": "email",
                    "className": "email-column",
                    "visible": false,  // No visible en la tabla
                    "exportable": true, // Exportable a Excel
                    "render": function (data, type, row) {
                        return data || row.email;
                    }
                },
                {   // Columna Contabilidad
                    "data": "contracted_accounting_usa_all_date",
                    "className": "accounting",
                    "render": function (data, type, row) {
                        let html = '';
                        let accountingUsaDate = row.contracted_accounting_usa_all_date;
                        let pendingInvoices = row.num_pending_invoices;
                        if (type === 'display' || type === 'filter') {
                            if (accountingUsaDate) {
                                html+= '<span class="pendings">';
                                html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                                html += '</span>';
                                html += '<div class="progress" style="height: 15px;">';
                                html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                                html += '</div>';
                                html += '</td>';
                            } else {
                                html += '<span class="badge badge-danger-lighten rounded small">no contratado</span>';
                            }
                            return html;
                        }
                        return data;
                    }
                },
                {   // Columna No visible
                    "data": null,
                    "visible": false,
                    "render": function (data, type, row) {
                        let html = '';
                        html += '<div>' + row.model_min + '</div>';
                        return html;
                    }
                },
                {   // Columna No visible
                    "data": null,
                    "visible": false,
                    "render": function (data, type, row) {
                        let html = '';
                        html += '<div>' + row.model_avg + '</div>';
                        return html;
                    }
                },
                {   // Columna Premium LLC
                    "data": "is_llc_premium_direction",
                    "className": "premium-llc text-center",
                    "render": function (data, type, row) {
                        if (type === "display") {
                            // Mostrar etiquetas visuales sin el valor numérico
                            return data === true 
                                ? '<span class="badge badge-success-lighten rounded small">contratado</span>' 
                                : '<span class="badge badge-danger-lighten rounded small">no contratado</span>';
                        } else {
                            // Mantener los valores 1 y 0 para ordenación interna de DataTables
                            return data === true ? 1 : 0;
                        }
                    }

                },
                {   // Columna Mantenimiento
                    "data": "contracted_maintenance_llc_date",
                    "className": "maintenance text-start",
                    "render": function (data, type, row) {
                        let html = '';
                        let maintStartDate = row.contracted_maintenance_llc_date;
                        let maintEndDate = row.contracted_maintenance_llc_end_date;

                        if (type === 'display' || type === 'filter') {
                            if (!maintStartDate) {
                                html += '<span class="d-none">0001-01-01<br></span>';
                                html += '<span class="badge badge-danger-lighten rounded small">no contratado</span>';
                            } else {
                                const fechaAlta = renderDateString(maintStartDate);
                                const fechaBaja = maintEndDate ? renderDateString(maintEndDate) : '-----';
                                html += '<div class="d-flex flex-column align-items-start">';
                                html += '<span class="d-none">'+maintStartDate+'</span>';
                                html += '<span class="small text-success-lighten"><i class="mdi mdi-calendar-check text-success-lighten fa-lg"></i> Alta: ' + fechaAlta + '</span>';
                                if (maintEndDate) {
                                    html += '<span class="small text-danger-lighten"><i class="mdi mdi-calendar-remove text-danger-lighten fa-lg"></i> Baja: ' + fechaBaja + '</span>';
                                }
                                html += '</div>';
                            }
                            return html;
                        }
                        return maintStartDate || '0000-01-01';

                    }
                },
                {   // Columna Modelo5472
                    "data": "model_5472",
                    "className": "model model_5472 model-year text-center",
                    "render": function (data, type, row) {
                        console.log(row);
                        let html = ' ';
                        let m_5472_1120 = row.model_5472;
                        if (m_5472_1120 == 'processed') {
                            html += '<span class="d-none">' + 0 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Listo para generar" href="/sellers/' + row.shortname + '/model/5472/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                        } else if (m_5472_1120 == 'not-processed') {
                            html += '<span class="d-none">' + 1 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Formulario Incompleto/Inacabado " href="/sellers/' + row.shortname + '/model/5472/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-pencil fa-xl" style="color:#7c7d7e;"></i></a>';
                        } else if (m_5472_1120 == 'not-started') {
                            html += '<span class="d-none">' + 6 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No ha iniciado" href="/sellers/' + row.shortname + '/model/5472/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-file-contract fa-xl" style="color:#7c7d7e;"></i></a>';
                        } else if (m_5472_1120 == "pending") {
                            html += '<span class="d-none">' + 4 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Generado y pendiente de aprobación" href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                        } else if (m_5472_1120 == 'agreed') {
                            html += '<span class="d-none">' + 3 + '</span>';
                            html += '<i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                        } else if (m_5472_1120 == 'disagreed') {
                            html += '<span class="d-none">' + 2 + '</span>';
                            html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                        } else if (m_5472_1120 == "presented") {
                            html += '<span class="d-none">' + 5 + '</span>';
                            html += '<a href="/' + row.shortname + '/model/5472/pdf/?period=' + '0A' + '&year=' + yearFilter + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                        } else if (m_5472_1120 == 'fax_send') {
                            html += '<span class="d-none">' + 0 + '</span>';
                            html += '<i class="fa-solid fa-envelope-circle-check fa-xl" style="color: #427ddb;"></i>';
                        } else if (m_5472_1120 == 'not-required') {
                            html += '<span class="d-none">' + 7 + '</span>';
                            html += '<i data-bs-toggle="tooltip" data-bs-placement="top" title="No requerido" class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                        } else if (m_5472_1120 == 'warning16'){
                            html += '<span class="d-none">' + 8 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Se puede generar con posibles errores" href="/sellers/' + row.shortname + '/model/5472/?period=' + '0A' + '&year=' + yearFilter + '"><i class="fa-solid fa-xl " style="color: #FF0000;">16</i>';
                        } else {
                            html += '<span class="d-none">' + 0 + '</span>';
                            html += m_5472_1120;
                        }
                        return html;
                    }
                },
                {   // Columna Modelo7004
                    "data": "model_7004",
                    "className": "model model_7004 model-year text-center",
                    "render": function (data, type, row) {
                        let html = '';
                        let m_7004 = row.model_7004;
                        if (m_7004 == 'fax_send') {
                            html += '<span class="d-none">' + 0 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Fax enviado" href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-envelope-circle-check fa-xl" style="color: #427ddb;"></i>';
                        } else if (m_7004 == 'agreed') {
                            html += '<span class="d-none">' + 2 + '</span>';
                            // html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Conforme" href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                            html += '<i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                        } else if (m_7004 == 'required') {
                            html += '<span class="d-none">' + 1 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Requerido" href="/sellers/' + row.shortname + '/model/7004/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                        } else if (m_7004 == 'not-required') {
                            html += '<span class="d-none">' + 5 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No requerido" <i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i></a>';
                        } else if (m_7004 == 'warning15') {
                            html += '<span class="d-none">' + 3 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No se puede generar"><i class="fa-solid fa-xl " style="color: #FF0000;">15</i>';
                        } else if (m_7004 == "presented") {
                            html += '<span class="d-none">' + 4 + '</span>';
                            html += '<a href="/' + row.shortname + '/model/7004/pdf/?period=' + '0A' + '&year=' + yearFilter + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                        }
                        return html;
                    }
                },
                {   // Columna ModeloBE15
                    "data": "model_be_15",
                    "className": "model model_be_15 model-year text-center",
                    "render": function (data, type, row) {
                        let html = ' ';
                        let m_b15 = row.model_be_15;
                        if (m_b15 == 'processed') {
                            html += '<span class="d-none">' + 0 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Listo para generar" href="/sellers/' + row.shortname + '/model/BE15/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                        } else if (m_b15 == 'not-processed') {
                            html += '<span class="d-none">' + 1 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Formulario Incompleto/Inacabado " href="/sellers/' + row.shortname + '/model/BE15/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-pencil fa-xl" style="color:#7c7d7e;"></i></a>';
                        } else if (m_b15 == 'not-started') {
                            html += '<span class="d-none">' + 6 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No ha iniciado" href="/sellers/' + row.shortname + '/model/BE15/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-file-contract fa-xl" style="color:#7c7d7e;"></i></a>';
                        } else if (m_b15 == 'fax_send') {
                            html += '<span class="d-none">' + 0 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Fax enviado" href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-envelope-circle-check fa-xl" style="color: #427ddb;"></i>';
                        } else if (m_b15 == 'agreed') {
                            html += '<span class="d-none">' + 2 + '</span>';
                            html += '<i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i>';
                        } else if (m_b15 == 'required') {
                            html += '<span class="d-none">' + 1 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Requerido" href="/sellers/' + row.shortname + '/model/7004/?period=' + '0A' + '&year=' + yearFilter + '" ><i class="fa-solid fa-file-contract fa-xl" style="color:#FF0000;"></i></a>';
                        } else if (m_b15 == 'not-required') {
                            html += '<span class="d-none">' + 5 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No requerido " <i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i></a>';
                        } else if (m_b15 == "presented") {
                            html += '<span class="d-none">' + 4 + '</span>';
                            html += '<a href="/' + row.shortname + '/model/BE15/pdf/?period=' + '0A' + '&year=' + yearFilter + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                        } else if (m_b15 == "not-wanted") {
                            html += '<span class="d-none">' + 4 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Solo actualizó sus datos" <a href="/' + row.shortname + '/model/BE15/pdf/?period=' + '0A' + '&year=' + yearFilter + '"><i class="fa-solid fa-times fa-xl" style="color: #02c018;"></i></a>';
                        } else if (m_b15 == "pending") {
                            html += '<span class="d-none">' + 4 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Generado y pendiente de aprobación" href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                        } else {
                            html += '<span class="d-none">' + 4 + '</span>';
                            html += m_b15;
                        }
                        return html;
                            }
                },
                {   // Columna Modelo184
                    "data": "model_es_184",
                    "className": "model model_es_184 model-year text-center",
                    "render": function (data, type, row) {
                        console.log('Data en model_es_184 es', data);
                        let html = ' ';
                        let m184 = row.model_es_184;
                        let letter184 = getLetter(row.model_json_result, 'ES-184');
                        if (m184 == "presented") {
                        html += '<span class="d-none">' + 5 + '</span>';
                        html += '<a href="/' + row.shortname + '/model/184/pdf/?period=' + periodTxt + '&year=' + yearFilter + '"><i class="fas fa-check-double fa-xl" style="color: #02c018;"></i></a>';
                        if (letter184) {
                            html += `<a href="/${row.shortname}/model/184/pdf/?period=${periodTxt}&year=${yearFilter}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter184}</i></a>`;
                        }
                        } else if (m184 == "pending") {
                        html += '<span class="d-none">' + 4 + '</span>';
                        html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-regular fa-clock fa-xl" style="color: #7c7d7e;"></i></a>';
                        } else if (m184 == 'disagreed') {
                        html += '<span class="d-none">' + 2 + '</span>';
                        html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-xmark fa-xl" style="color: #FFA500;"></i></a>';
                        } else if (m184 == 'agreed') {
                        html += '<span class="d-none">' + 3 + '</span>';
                        html += '<a href="/sellers/' + row.shortname + '/docs/presented_models/pending/"><i class="fa-solid fa-check fa-xl" style="color: #ffd700;"></i></a>';
                        if (letter184) {
                            html += `<a href="/${row.shortname}/model/184/pdf/?period=${periodTxt}&year=${yearFilter}"><i class="fa-solid " style="color: #000000;">&nbsp;${letter184}</i></a>`;
                        }
                        } else if (m184 == 'not-required') {
                        html += '<span class="d-none">' + 6 + '</span>';
                        html += '<i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i>';
                        } else if (m184 == 'required' || m184 == 'processed') {
                        html += '<span class="d-none">' + 0 + '</span>';
                        html += '<a href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-file-contract fa-xl" style="color: #FF0000;"></i></a>';
                        } else if (m184 == 'not-processed') {
                            html += '<span class="d-none">' + 1 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="Formulario Incompleto/Inacabado " href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-pencil fa-xl" style="color: #7c7d7e;"></i></a>';                
                        } else if (m184 == 'not-started') {
                            html += '<span class="d-none">' + 6 + '</span>';
                            html += '<a data-bs-toggle="tooltip" data-bs-placement="top" title="No ha iniciado" href="/sellers/' + row.shortname + '/model/184/"><i class="fa-solid fa-file-contract fa-xl" style="color: #7c7d7e;"></i></a>';
                        }
                        return html;
                    }
                },
                {   // Columna de Ultimo acceso
                    "data": "last_login",
                    "className": "login truncate-text",
                    "render": function (data, type, row) {
                        if (data && (type === 'display' || type === 'filter')) {
                        const date = new Date(data);

                        const day = date.getDate().toString().padStart(2, '0');
                        const month = date.toLocaleString('default', { month: 'short' });
                        const year = date.getFullYear();
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');

                        const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

                        return formattedDate;
                        }
                        return data;
                    }
                },
                {   // Columna de acciones
                    "data": null,
                    "className": "actions",
                    "orderable": false,
                    "render": function (data, type, row) {
                        let html = '<td class="align-middle text-center">';
                        html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success mb-0 border-0" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                        html += '<i class="feather icon-edit"></i>';
                        html += '</a>';
                        html += '</td>';
                        html = '<div style="text-align: center;">' + html + '</div>';
                        return html;
                    },
                }
            ],
            "paging": true,
            "searching": true,
            "ordering": true,
            "lengthChange": false,
            "lengthMenu": [[100, 150, 200, -1], [100, 150, 200, 'Todos']],
            "layout": {
                topEnd: null,
            },
            "lengthChange": false,
            "createdRow": function (row, data, dataIndex) {
                const shortname = data.shortname;
                const link = '/sellers/' + shortname + '/';
                $(row).attr('style', 'cursor: pointer; vertical-align: middle;');
                $(row).attr('onclick', "window.location.href = '" + link + "';");
            },
            "initComplete": function (settings, json) {
                // Debug para ver los datos recibidos por la tabla
                console.log("Datos recibidos:", json.data);
                table.settings()[0].nTBody.style.width = "100%";
                table.settings()[0].nTable.style.width = "100%";
                table.settings()[0].nTHead.style.width = "100%";
                document.getElementById("pending-model-count").textContent = json.pending_model_count;
                document.getElementById("required-notstarted-model-count").textContent = json.required_notstarted_model_count;
                document.getElementById("required-notcompleted-model-count").textContent = json.required_notcompleted_model_count;
                document.getElementById("required-model-count").textContent = json.required_model_count;
                document.getElementById("disagreed-model-count").textContent = json.disagreed_model_count;
                document.getElementById("agreed-model-count").textContent = json.agreed_model_count;
                document.getElementById("fax-send-model-count").textContent = json.fax_send_model_count;
                document.getElementById("presented-model-count").textContent = json.presented_model_count;
                const period = "{{period}}";
                const entity = "{{entity}}";
                if (period == '0A') {
                    $columns = table.columns(`.model-year`);
                    $columns.visible(true);
                } else {
                    $columns = table.columns(`.model-quarter`);
                    $txtColumns = table.columns(`.txt.${period}`);
                    $columns.visible(true);
                    $txtColumns.visible(true);
                }
                $('.column-switch').each(function () {
                    const columnClass = $(this).data('column');
                    const column = table.column(`.${columnClass}`);
                    this.checked = column.visible();
                    $(this).trigger('change');
                });

                const columnsDropdownButton = document.getElementById('columnsDropdownButton');
                columnsDropdownButton.addEventListener('click', function (event) {
                    // event.stopPropagation();
                    const columnsDropdown = document.getElementById('columnsDropdownArea');
                    columnsDropdown.classList.toggle('active');
                });

                document.addEventListener('click', function (event) {
                    const columnsDropdown = document.getElementById('columnsDropdownArea');
                    if (!columnsDropdown.contains(event.target) && event.target !== columnsDropdownButton) {
                        columnsDropdown.classList.remove('active');

                    }
                });

                const truncatedCells = document.querySelectorAll('.truncate-text');
                    truncatedCells.forEach(cell => {
                    cell.setAttribute('title', cell.textContent);
                });

            },
            "drawCallback": function (settings) {
                // table.settings()[0].nTable.style.width = "100%";
                // const period = "{{period}}";
                // const entity = "{{entity}}";
                // if (period == '0A') {
                //     $columns = table.columns(`.model-year`);
                //     $columns.visible(true);
                // } else {
                //     $columns = table.columns(`.model-quarter`);
                //     $columns.visible(true);
                // }
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });

        $('.column-switch').change(function () {
            const columnClass = $(this).data('column');
            const column = table.column(`.${columnClass}`);
            const isVisible = this.checked;
            column.visible(isVisible);

            // Crear diccionario con el estado de todas las columnas
            const col_excludes = [];
            $('.column-switch').each(function () {
                const col = $(this).data('column');
                if (!$(this).is(':checked')) {
                    col_excludes.push(col);
                }
            });
            refreshResults(col_excludes);


            const allChecked = checkAllColumnSwitch();
            const selectAllButton = document.getElementById('selectAllModelColumnsButton');
            if (allChecked) {
                selectAllButton.textContent = 'Deseleccionar todos';
            } else {
                selectAllButton.textContent = 'Seleccionar todos';
            }
        });

        // 3) Vincular el enlace del dropdown con el botón "excelHtml5" de DataTables
        const exportExcelBtn = document.getElementById('exportExcelBtn');
        if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', function () {
            // Disparamos el botón “excelHtml5”
            table.button('.buttons-excel').trigger();
        });
        }

        const getLetter = (row_json, model)=>{
            let modelJsonResult = row_json ? JSON.parse(row_json) : {};
            let value = modelJsonResult[model]||null;
            return value;
        }

    }

    let refreshResultsToken = null;
    const refreshResults = (excludes) => {
         // generar token para cada petición
        refreshResultsToken = Date.now() + Math.random().toString(36).substring(2, 15);
        const requestToken = refreshResultsToken;  // token local para esta petición
        // Todo
        console.log('Excluidas:', excludes);
        const url = "{% url 'app_lists:seller_management_us_dt_list' %}";
        // Ajax call to get the updated data
        $.ajax({
            url: url,
            type: 'GET',
            data: {
                excludes: excludes.join(','),
                year: document.getElementById("year").value,
                period: $('input[name="periods"]:checked').val()
            },
            success: function (response) {
                if (requestToken === refreshResultsToken) {
                    console.log(response);
                    json = response;
                    document.getElementById("pending-model-count").textContent = json.pending_model_count;
                    document.getElementById("required-notstarted-model-count").textContent = json.required_notstarted_model_count;
                    document.getElementById("required-notcompleted-model-count").textContent = json.required_notcompleted_model_count;
                    document.getElementById("required-model-count").textContent = json.required_model_count;
                    document.getElementById("disagreed-model-count").textContent = json.disagreed_model_count;
                    document.getElementById("agreed-model-count").textContent = json.agreed_model_count;
                    document.getElementById("fax-send-model-count").textContent = json.fax_send_model_count;
                    document.getElementById("presented-model-count").textContent = json.presented_model_count;
                }
            },
            error: function (error) {
                console.error('Error al obtener los datos:', error);
            }
        });
    }

    const ajaxData = (d) => {
        let tParams = "";
        let year = document.getElementById("year").value;
        let  period = $('input[name="periods"]:checked').val();
        if (year) {
            d.year = year
            tParams += "&year=" + year;
        }
        if (period) {
            d.period = period
            tParams += "&period=" + period;
        }
        // getTotals(tParams);
        return d
    }

    function renderAmazonStatus(status, monthValue, row) {
        let html = '';
        let hrefLink = '';
    
        if (status === "no_require") {
        hrefLink = '/sellers/' + row.shortname + '/';
        html += '<span class="d-none">' + 3 + '</span>';
        html += '<a href="' + hrefLink + '"><i class="fa-solid fa-ban fa-xl" style="color: #02c018;"></i></a>';
        } else {
        if (monthValue === true) {
            hrefLink = '/sellers/' + row.shortname + '/AmazonTxtEur/';
            html += '<span class="d-none">' + 2 + '</span>';
            html += '<a href="' + hrefLink + '"><i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i></a>';
        } else {
            hrefLink = '/sellers/' + row.shortname + '/AmazonTxtEur/';
            html += '<span class="d-none">' + 1 + '</span>';
            html += '<a href="' + hrefLink + '"><i class="fa-solid fa-circle fa-xl" style="color: #7c7d7e;"></i></a>';
        }
        }
        return html;
    }

    function search() {
        const tipo = $('#search').val();
        table.search(tipo).draw();
    }

    const onChangePeriodYear = () => {
        const period = $('input[name="periods"]:checked').val();
        const year = document.getElementById('year');
        const urlembed = "{% url 'app_lists:new-management' 'us' %}";
        const newUrl = urlembed + '?period=' + period + '&year=' + year.value;
        window.location.href = newUrl;
    }

    function openInfoModal() {
        const modal = new bootstrap.Modal(document.getElementById('info-modal'));
        modal.show();
    }
    
    function toggleSelectAllModelColumns(button) {
        const allChecked = checkAllColumnSwitch();
        if (allChecked) {
            document.querySelectorAll('.column-switch').forEach(month => month.checked = false);
            button.textContent = 'Seleccionar todos';
        } else {
            document.querySelectorAll('.column-switch').forEach(month => month.checked = true);
            button.textContent = 'Deseleccionar todos';
        }

        document.querySelectorAll('.column-switch').forEach(month => month.dispatchEvent(new Event('change')));

    }

    function checkAllColumnSwitch() {
        const columns = document.querySelectorAll('.column-switch');
        return Array.from(columns).every(column => column.checked);

    }

    function confirmUpdateTable() {
        Swal.fire({
            title: '¿Quieres actualizar la tabla?',
            text: "Se recalcularán los datos para el periodo y año seleccionados.",
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, actualizar <i class="fas fa-sync-alt"></i>',
            cancelButtonText: 'Cancelar',
            customClass: {
                confirmButton: 'swal2-btn-full-width btn-dark',
                cancelButton: 'btn-link',
                actions: 'swal2-action-div-full-width',
            },
        }).then((result) => {
            if (result.isConfirmed) {
                updateTable();
            }
            if (result.isDismissed) {
                setTimeout(function() {
                    document.activeElement.blur();
                }, 300);
            }
        });
    }

    async function updateTable() {

        const year = document.getElementById("year").value;
        const period = $('input[name="periods"]:checked').val();

        Swal.fire({
            icon: 'warning',
            title: 'Actualizando',
            text: `Se están recalculando los datos para el periodo y año seleccionados.`,
            timerProgressBar: true,
            didOpen: () => Swal.showLoading(),
            showCancelButton: false,
            showConfirmButton: false,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false
        });

        const url = "{% url 'app_lists:update_all_seller_list' %}";
        const data = new URLSearchParams({
            list: 'management_us',
            year: year,
            period: period
        });

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: data.toString()
        });

        if (response.ok) {
            const result = await response.json();

            Swal.fire({
                icon: 'success',
                title: 'Actualizado',
                text: `Se han actualizado los datos para el periodo y año seleccionados.`,
                timer: 2000,
                timerProgressBar: true,
                didOpen: () => Swal.showLoading(),
                showCancelButton: false,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false
            });

            table.ajax.reload();
        }

    }

    const renderDateString = (data) => {
        if (data == null || data == undefined || data == "") {
        return null;
        }
        months = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
        const date = new Date(data);
        const day = date.getDate().toString().padStart(2, '0');
        const month = months[date.getMonth()];
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    }

    function goToCreateNotification(department) {
        const period = $('input[name="periods"]:checked').val();
        const year = document.getElementById('year').value;
        const url = "{% url 'app_notifications:create_pending_model_notif_from_cached_list' 'DEPARTMENT_PLACEHOLDER' %}"
            .replace('DEPARTMENT_PLACEHOLDER', department);
        window.location.href = url + `?period=${period}&year=${year}`;
    }
    
</script>
{% endblock javascripts %}