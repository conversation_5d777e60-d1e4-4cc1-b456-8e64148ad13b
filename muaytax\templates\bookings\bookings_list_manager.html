{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Soporte MuayTax
{% endblock title %}

{% block stylesheets %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {     
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    } 

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    .card-text{
			text-align:center;
		}
		.card-title{
      margin-bottom: 0;
			text-align:center;
			font-size:18px;
			color:black;
		}
    .logged-in {
      font-size: large;
      margin-right: 10px;
      color: #03ad65;
      animation: pulse 1s ease-in-out infinite;
    }
    .logged-out {
      font-size: large;
      margin-right: 10px;
      color: #ff0000;
    }

    @keyframes pulse {
      0% {
        opacity: 0.5;
      }
      50% {
        opacity: 1;
      }
      100% {
        opacity: 0.5;
      }
    }

    .is_available{
      font-size: 12px;
      margin-bottom: 0;
      color: #03ad65;
    }
    .not_available{
      font-size: 12px;
      margin-bottom: 0;
      color: #ff0000;
    }
    .schedule-time{
      display: inline-flex;
    }
    #cardButton:hover{
      box-shadow: 0 0 11px rgb(77 181 122 / 46%);
      transform: scale(1.03);
    }

    .user-detail{
      height: 60px;
      width: 60px;
      border-radius: 50%;
      background: linear-gradient(-135deg, #1de9b6 0%, #03ad65 100%);;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              Soporte
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="">Mis llamadas</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}

{% if schedule %}
<div class="row d-flex mt-0">
  <!-- Todos los Sellers -->
  <div class="col-1 card rounded m-2" style="min-width: 17rem;" id="cardButton">
    <a href="{% url 'app_bookings:manager_schedule_update' user.username  %}" class="card-link">
      <div class="card-body">
        <div class="card-title">Editar mi horario</div>
        <div class="card-text">
          <p id="status-text" class="is_available"><span id="logging-status" class="logged-in">●</span>Disponible</p>
          <div class="schedule-time">
            <h3 style="font-weight: bolder;">{{ schedule.start_time }} - {{ schedule.end_time }}</h3>
            {% if absences %}
            <a href="{% url 'app_bookings:manager_absence_list' user.username %}">
              <span style="padding-left: 5px;" data-bs-toggle="tooltip" data-bs-placement="top" title="Tienes ausencias el día de hoy"><i class="bi bi-info-circle"></i></span>
            </a>
            {% endif %}
          </div>
        </div>
      </div>
    </a>
  </div>
  {% if schedule.start_break_time %}
  <div class="col-1 card rounded m-2" style="min-width: 17rem;" id="cardButton">
    <a href="{% url 'app_bookings:manager_schedule_update' user.username %}" class="card-link">
      <div class="card-body">
        <div class="card-title">Hora de comida <i class="fa-solid fa-utensils"></i></div>
        <div class="card-text">
          <p class="is_available" style="opacity: 0;"><span id="logging-status" class="logged-in">●</span>Disponible</p>
          <div class="schedule-time">
            <h3 style="font-weight: bolder;">{{ schedule.start_break_time }} - {{ schedule.end_break_time }}</h3>
          </div>
        </div>
      </div>
    </a>
  </div>
  {% endif %}
  <div class="col-1 card rounded m-2" style="min-width: 17rem;" id="cardButton">
    <a href="{% url 'app_bookings:manager_absence_list' user.username %}" class="card-link">
      <div class="card-body">
        <div class="card-title">Mis Ausencias</div>
        <div class="card-text">
          <h1><i class="fa-solid fa-user-slash"></i></h1>
        </div>
      </div>
    </a>
  </div>
  <div class="col-1 card rounded m-2" style="min-width: 17rem;" id="cardButton">
    <a href="{% url 'app_bookings:new_booking_manager' user.username %}" class="card-link">
      <div class="card-body">
        <div class="card-title">Nueva llamada</div>
        <div class="card-text">
          <h1><i class="bi bi-telephone-plus-fill"></i></h1>
        </div>
      </div>
    </a>
  </div>
</div>
{% endif %}

{% if success_deleted %}
    <div id="successDeleted" data-messages="{{ success_deleted }}" style="display: none;"></div>
{% endif %}
{% if success_created %}
    <div id="successCreated" data-messages="{{ success_created }}" style="display: none;"></div>
{% endif %}

<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
      <div class="row mb-4">
          <div class="col-12 d-flex justify-content-center align-items-start">
            <div class="input-group">
              <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."  />
            </div>
          </div>
        </div>
        <div class="dt-responsive table-responsive">
          <table id="list-table" class="table nowrap">
            <thead class="table-head">
              <tr>
                <th>Fecha y hora de la llamada</th>
                <th>Duración</th>
                <th>Nombre de usuario</th>
                <th>Vendedor</th>
                <th>Asunto</th>
                <th>Temas</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {% for object in object_list %}
              <tr>
                <td class="align-middle">
                  <span style="display: none;">{{ object.date|date:"Y-m-d\\TH:i:s" }}</span>
                  <span>{{ object.date|date:"d/M/Y - H:i"|lower  }}</span>
                </td>
                <td class="align-middle">
                  <span>{{ object.get_duration_text }}</span>
                </td>
                <td class="align-middle">
                  <span>{{ object.get_user }}</span>
                </td>
                <td class="align-middle">
                  <span>{{  object.seller.shortname|default:'No existe en APP' }}</span>
                </td>
                <td class="align-middle">
                  <span>{{ object.subject.description }}</span>
                </td>
                <td class="align-middle">
                  <span>{{ object.topics|default:'' }}</span>
                </td>
                
                <td class="align-middle text-center">
                  <a
                  id="detailButton"
                  data-shortname="{{ object.seller.shortname|default:'' }}"
                  data-name="{{ object.get_user }}"
                  data-date="{{ object.date|date:'d-m-Y' }}"
                  data-email="{% firstof object.seller.user.email object.guest_user.email %}"
                  data-phone="{% firstof object.seller.contact_phone object.guest_user.phone '' %}"
                  data-time="{{ object.date|time:'H:i' }}"
                  data-subject="{{ object.subject.description }}"
                  data-topics="{{ object.topics }}"
                  data-comments="{{ object.comments }}"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  class="btn btn-icon btn-success"
                  title="Ver detalles"
                  >
                  <i class="feather icon-eye"></i>
                  </a>

                  <a
                  id="deleteButton"
                  data-id="{{ object.id }}"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  class="btn btn-icon btn-danger"
                  title="Eliminar llamada"
                  >
                    <i class="feather icon-trash-2"></i>
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

  <div class="modal fade" id="confirmDelete" tabindex="-1" aria-labelledby="animateModalLabel" aria-modal="true" role="dialog">
    <form method="post" enctype="multipart/form-data" action="">
      {% csrf_token %}
      <input type="hidden" id="bookingId" name="bookingIdInput" value="">
      <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white">Eliminar llamada</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
              <div class="swal2-icon swal2-warning swal2-icon-show" style="display: flex; margin-top: 0;">
                <div class="swal2-icon-content">!</div>
              </div>
                <h5>¿Estás seguro que quieres eliminar esta llamada?</h5>
                <p>Ten en cuenta que esta acción no se puede revertir.</p>
                <!-- <p>Se enviará una notificación por correo electrónico tanto a ti como al vendedor.</p> -->

                <div class="alert alert-warning" role="alert">
                  <i class="feather icon-calendar mr-1 align-middle"></i>
                  <span>La llamada se eliminará de tu calendario de google</span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Atras</button>
                <button type="submit" class="btn btn-outline-danger">Eliminar</button>
            </div>
        </div>
      </div>
    </form>
  </div>

  <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="animateModalLabel" aria-modal="true" role="dialog">
    <div class="modal-dialog">
      <div class="modal-content">
          <div class="modal-header" style="background-color: #3f4d67;">
              <h5 class="modal-title text-white">Detalles de la llamada</h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-header bg-body">
            <div class="row align-items-center">
              <div class="col">
                  <div class="row align-items-center">
                      <div class="col-auto col pr-0">
                        <div class="user-detail">
                          <i class="feather icon-user f-30 text-white"></i>
                        </div>
                          <!-- <img class="img-radius img-fluid wid-60" src="https://appsrv1-147a1.kxcdn.com/django-datta-able-enh/images/user/avatar-2.jpg" alt="User image"> -->
                      </div>
                      <div class="col float-end">
                        <h6 id="detailShortname" class="mb-0" style="color: #888;"></h6>
                        <h5 id="detailName" class="mb-0"></h5>
                        <h6 
                          id="detailEmail"
                          class="mb-0"
                          style="color: #888;">
                        </h6>
                        <h6 
                          id="detailPhone"
                          class="mb-0"
                          style="color: #888;">
                        </h6>
                      </div>
                  </div>
              </div>
            </div>
          </div>
          <div class="modal-body">

            <div class="row">
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-calendar-day f-20 text-info mb-2"></i>
                <span id="detailDate"></span>
              </div>
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-clock f-20 text-info mb-2"></i>
                <span id="detailTime"></span>
              </div>
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-question-circle f-20 text-info mb-2"></i>
                <span id="detailSubject"></span>
              </div>
              <div class="col-12 d-flex flex-column align-items-center mt-3">
                <i class="fas fa-tag f-20 text-info mb-2"></i>
                <span id="detailTopics"></span>
              </div>
              <div id="commentsDiv" class="col-12 mt-4 d-none">
                <div class="alert alert-info" role="alert">
                  <span id="detailComments"></span>
                </div>
              </div>
            </div>

          </div>
          
          <div class="modal-footer">
              <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Atras</button>
          </div>
      </div>
    </div>
  </div>
  
{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">

  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>

  <script>
      $(document).ready(function(){
        const dataTableOptions = {
          paging: false,
          searching: true, 
          ordering: true,
          truncation: true,
          info: true,
          footer: true,
          language: {
            lengthMenu: "_MENU_",
            zeroRecords: "No se han encontrado productos.",
            info: "_TOTAL_ resultados. ",
            search: "Buscar:",
            infoEmpty: "No hay resultados que coincidan con su búsqueda.",
            infoFiltered: ""
          },
          dom: 'lrtip',
          fixedHeader: true,
          columnDefs: [
            { orderable: false, targets: -1 }
          ],
        };
        
        const dataTable =$("#list-table").DataTable(dataTableOptions);
        $("#search").on("input", function(){
            const filtro =$(this).val();
            console.log(filtro)
            dataTable.search(filtro).draw();
        });

      });
  </script>

  <script>
    // declare sweetalert
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    const managerStartTime = "{{ schedule.start_time }}";
    const managerEndTime = "{{ schedule.end_time }}";
    const today = new Date();
    const statusText = document.getElementById("status-text");

    const startTimeObject = new Date();
    startTimeObject.setHours(...managerStartTime.split(":"));
    startTimeObject.setSeconds(0);

    const endTimeObject = new Date();
    endTimeObject.setHours(...managerEndTime.split(":"));
    endTimeObject.setSeconds(0);

    if (today >= startTimeObject && today <= endTimeObject) {
      statusText.classList.add("is_available");
      statusText.classList.remove("not_available");
      statusText.innerHTML = "<span id='logging-status' class='logged-in'>●</span>Disponible";

    } else {
      statusText.classList.add("not_available");
      statusText.classList.remove("is_available");
      statusText.innerHTML = "<span id='logging-status' class='logged-out'>●</span>No disponible";
    }

    document.addEventListener("DOMContentLoaded", function () {
      var deleteModal = new bootstrap.Modal(document.getElementById('confirmDelete'), {
        keyboard: false,
        backdrop: 'static'
      });

      var deleteButton = document.querySelectorAll("#deleteButton");
      deleteButton.forEach(function (btn) {
        btn.addEventListener("click", function () {
          var bookingId = btn.getAttribute("data-id");
          document.getElementById("bookingId").value = bookingId;
          deleteModal.show();
        });
      });

      var successDeleted = document.getElementById("successDeleted");
      if (successDeleted) {
        var messages = successDeleted.getAttribute("data-messages");
        Toast.fire({
          icon: "success",
          title: messages,
        });
      }

      var successCreated = document.getElementById("successCreated");
      if (successCreated) {
        var messages = successCreated.getAttribute("data-messages");
        Toast.fire({
          icon: "success",
          title: messages,
        });
      }

      /////////****************////////////////

      // trigger detail modal
      var detailModal = new bootstrap.Modal(document.getElementById('detailModal'), {
        keyboard: false,
        backdrop: 'static'
      });

      var detailButton = document.querySelectorAll("#detailButton");
      detailButton.forEach(function (btn) {
        btn.addEventListener("click", function () {
          var bookingShortname = btn.getAttribute("data-shortname");
          var bookingName = btn.getAttribute("data-name");
          var bookingEmail = btn.getAttribute("data-email");
          var bookingPhone = btn.getAttribute("data-phone");
          var bookingDate = btn.getAttribute("data-date");
          var bookingTime = btn.getAttribute("data-time");
          var bookingSubject = btn.getAttribute("data-subject");
          var bookingTopics = btn.getAttribute("data-topics");
          var bookingComments = btn.getAttribute("data-comments");
          var bookingCommentsDiv = document.getElementById("commentsDiv");

          if (bookingShortname) {
            document.getElementById("detailShortname").classList.remove("text-danger");
            document.getElementById("detailShortname").innerHTML = '@' + bookingShortname;
          } else {
            document.getElementById("detailShortname").classList.add("text-danger");
            document.getElementById("detailShortname").innerHTML = 'No es usuario de la APP';
          }
          document.getElementById("detailName").innerHTML = bookingName;
          document.getElementById("detailEmail").innerHTML = '<i class="fas fa-envelope me-1"></i> ' + bookingEmail;
          document.getElementById("detailPhone").innerHTML = '<i class="fas fa-phone me-1"></i> ' + bookingPhone;
          document.getElementById("detailDate").innerHTML = "<b>Fecha:</b> " + bookingDate;
          document.getElementById("detailTime").innerHTML = "<b>Hora:</b> " + bookingTime;
          document.getElementById("detailSubject").innerHTML = "<b>Asunto:</b> " + bookingSubject;
          document.getElementById("detailTopics").innerHTML = "<b>Temas:</b> " + bookingTopics;
          if (bookingComments) {
            bookingCommentsDiv.classList.remove("d-none");
            document.getElementById("detailComments").innerHTML = "<b>Comentarios:</b> " + bookingComments;
          } else {
            bookingCommentsDiv.classList.add("d-none");
          }

          $('#detailModal').modal('show');
        });
      });

      
    });

  </script>
{% endblock javascripts %}