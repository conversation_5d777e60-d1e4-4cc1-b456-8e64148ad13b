from django.urls import reverse
from django.http import JsonResponse
from django.views.generic import ListView, CreateView
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import HttpResponseRedirect
from django.db.models import F
from django.utils import timezone
from datetime import timedelta, datetime
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_protect
from muaytax.users.permissions import IsSellerShortnamePermission, IsSellerRolePermission, IsManagerRolePermission, IsAnyRolePermission
from muaytax.app_tasks.models.task_pending import TaskPending
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_tasks.forms.task_pending import TaskPendingForm
from django.contrib.auth.decorators import login_required


class TaskPendingListSellerView(LoginRequiredMixin, IsSellerShortnamePermission, IsSellerRolePermission, ListView):
    model = TaskPending
    template_name = 'tasks/task_pending_list_seller.html'
    context_object_name = 'tasks'

    def get_queryset(self):
        return TaskPending.objects.filter(user=self.request.user)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["username"]]))
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        context['pending_tasks_count'] = TaskPending.objects.filter(user=user, completed=False).count()
        context['seen_tasks_count'] = TaskPending.objects.filter(user=user, seen=True).count()
        context['unseen_tasks_count'] = TaskPending.objects.filter(user=user, seen=False).count()
        context['completed_tasks_count'] = TaskPending.objects.filter(user=user, completed=True).count()
        context['uncompleted_tasks_count'] = TaskPending.objects.filter(user=user, completed=False).count()
        
        # Obtener shortname del usuario
        seller = Seller.objects.filter(user=user).first()
        context['user_shortname'] = seller.shortname if seller else None

        # Verificar si hay notificaciones mínimas
        min_notification_tasks = TaskPending.objects.filter(
            user=user, 
            notification_type='minimal', 
            completed=False,
        )
        
        # Filtrar en Python
        min_notification_tasks = [
            task for task in min_notification_tasks
            # if timezone.make_aware(datetime.combine(task.due_date, datetime.min.time())) <= timezone.now() + timedelta(days=task.notification_days_before_deadline)
            if task.due_date and timezone.make_aware(datetime.combine(task.due_date, datetime.min.time())) <= timezone.now() + timedelta(days=task.notification_days_before_deadline)
        ]
        
        context['min_notification_tasks'] = bool(min_notification_tasks)
        context['total_pending_tasks'] = TaskPending.objects.filter(user=user).count()
        
        print(f'total_pending_tasks es --------- {TaskPending.objects.filter(user=user).count()}')
        
        return context

class TaskPendingListManagerView(LoginRequiredMixin, IsManagerRolePermission, ListView):
    model = TaskPending
    template_name = 'tasks/task_pending_list_manager.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        context['pending_tasks_count'] = TaskPending.objects.filter(user=user, completed=False).count()
        context['seen_tasks_count'] = TaskPending.objects.filter(user=user, seen=True).count()
        context['unseen_tasks_count'] = TaskPending.objects.filter(user=user, seen=False).count()
        context['completed_tasks_count'] = TaskPending.objects.filter(user=user, completed=True).count()
        context['uncompleted_tasks_count'] = TaskPending.objects.filter(user=user, completed=False).count()

        # Obtener shortname del usuario
        if user.role == 'manager':
            context['user_shortname'] = user.name
        else:
            seller = Seller.objects.filter(user=user).first()
            context['user_shortname'] = seller.shortname if seller else None
        
        # Verificar si hay notificaciones mínimas
        min_notification_tasks = TaskPending.objects.filter(
            user=user, 
            notification_type='minimal', 
            completed=False,
        )
        
        # Filtrar en Python
        min_notification_tasks = [
            task for task in min_notification_tasks
            if task.due_date and timezone.make_aware(datetime.combine(task.due_date, datetime.min.time())) <= timezone.now() + timedelta(days=task.notification_days_before_deadline)
        ]
        
        context['min_notification_tasks'] = bool(min_notification_tasks)
        print(f'bool(min_notification_tasks) es --------- {bool(min_notification_tasks)}')
        
        # Añadir mensajes al contexto desde la sesión
        task_created_message = self.request.session.pop('task_created_message', None) # Elimina
        if task_created_message:
            context['django_message'] = task_created_message

        return context
    
class TaskPendingListSellerJsonView(LoginRequiredMixin, IsSellerShortnamePermission, IsSellerRolePermission, View):
    def get(self, request, *args, **kwargs):
        shortname = self.kwargs.get('shortname')
        
        # Asegurarse de que se está obteniendo el shortname correctamente
        if not shortname:
            return JsonResponse([], safe=False)

        # Filtrar las tareas por el shortname del vendedor
        try:
            tasks = TaskPending.objects.filter(user__seller__shortname=shortname).annotate(
                task_type_name=F('task_type__name')
            ).values(
                'id', 'created_at', 'task_type_name', 'due_date', 
                'seen', 'completed', 'modified_at', 'notification_type', 'description'
            ).order_by('completed', 'seen', 'due_date')

            task_list = list(tasks)
            
            if not task_list:
                return JsonResponse([], safe=False)
            
            for task in task_list:
                task_instance = TaskPending.objects.get(id=task['id'])
                task['days_before_due_date'] = task_instance.days_before_due_date
            
            # Diferenciar el propósito de la solicitud con un parámetro de consulta
            if request.GET.get('purpose') == 'notification':
                task_counts = {
                    'blocking': sum(1 for task in task_list if task['notification_type'] == 'blocking' and not task['seen']),
                    'invasive': sum(1 for task in task_list if task['notification_type'] == 'invasive' and not task['seen']),
                    'minimal': sum(1 for task in task_list if task['notification_type'] == 'minimal' and not task['seen']),
                    'not_completed': sum(1 for task in task_list if not task['completed']),
                    'not_seen': sum(1 for task in task_list if not task['seen']),
                }
                return JsonResponse(task_counts)
            
            return JsonResponse(task_list, safe=False)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

class TaskPendingListManagerJsonView(LoginRequiredMixin, IsManagerRolePermission, View):
    def get(self, request, *args, **kwargs):
        user = request.user
        
        tasks = TaskPending.objects.filter(user=user).annotate(
            task_type_name=F('task_type__name')
        ).values(
            'id', 'created_at', 'task_type_name', 'due_date', 
            'seen', 'completed', 'modified_at', 'notification_type', 'description'
        ).order_by('completed', 'seen', 'due_date')  # Ordenar por completadas, vistas y fecha límite

        task_list = list(tasks)
        for task in task_list:
            task_instance = TaskPending.objects.get(id=task['id'])
            task['days_before_due_date'] = task_instance.days_before_due_date
        
        return JsonResponse(task_list, safe=False)


class UpdateTaskView(LoginRequiredMixin, IsAnyRolePermission, View):
    @method_decorator(csrf_protect)
    def post(self, request, *args, **kwargs):
        try:
            user = request.user
            task_id = kwargs['task_id']

            if user.role == 'manager':
                task = TaskPending.objects.get(id=task_id, user=user)
            else:
                task = TaskPending.objects.get(id=task_id, user__seller__user=user)

            action = request.POST.get('action')
            task.seen = True

            if action == 'complete':
                task.completed = True
            elif action != 'seen':
                return JsonResponse({'status': 'error', 'message': 'Invalid action'}, status=400)

            task.save()
            return JsonResponse({'status': 'success', 'task_id': task.id})
        except TaskPending.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Task not found'}, status=404)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
        
class TaskPendingCreateView(LoginRequiredMixin, IsManagerRolePermission, CreateView):
    model = TaskPending
    form_class = TaskPendingForm
    template_name = 'tasks/task_pending_create.html'

    def get_success_url(self):
        return reverse_lazy('app_tasks:list_task_pending_manager', kwargs={'username': self.request.user.username})

    def form_valid(self, form):
        response = super().form_valid(form)
        task = form.instance
        self.request.session['task_created_message'] = f'Tarea para {task.user.seller.shortname} fue creada con éxito'
        return response
