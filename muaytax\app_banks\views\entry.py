import datetime
import json
import os
import traceback
import pandas as pd
import zipfile
import time

from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse
from django.shortcuts import get_object_or_404, redirect
from django.http import HttpResponseRedirect, HttpResponse , HttpResponseBadRequest, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView, DeleteView, ListView, UpdateView, View
from django.db.models import Q, Case, When, IntegerField
from django.db import connection
from django_datatables_view.base_datatable_view import BaseDatatableView

from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from muaytax.tasks_scheduled import generate_entry_task # NO ELIMINAR NUNCA O CELERY FALLA
from muaytax.app_banks.models.entry import Entry
from muaytax.app_banks.models.bank import Bank
from muaytax.app_banks.models.movement import Movement
from muaytax.app_banks.models.reconciliation import Reconciliation
from muaytax.app_banks.models.entry_files import EntryFile
from muaytax.app_banks.controllers import EntryController

from muaytax.app_customers.models.customer import Customer
from muaytax.app_invoices.models.concept import Concept
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.invoice_category import InvoiceCategory
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType
from muaytax.dictionaries.models.movement_status import MovementStatus
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.bank_type import BankType
from muaytax.utils.general import get_first_and_last_date

class EntryListView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Entry
    template_name_suffix = "_list"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        entry = Entry.objects.filter(entry_seller = seller)
        return entry

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
 
        qty_invoices_amz_not_reconciled = Invoice.objects.filter(seller=seller, status="revised").exclude(
            Q( used_in_entry= True) |
            Q( is_txt_amz = False) |
            Q(transaction_type="inbound-transfer") | 
            Q(transaction_type="outgoing-transfer") |
            Q(invoice_category__code__icontains='_copy')
        ).count()
        reconciliations_pending = Reconciliation.objects.filter(movement__status="conciliated", movement__bank__bank_seller=seller).exclude(
            Q( used_in_entry= True) 
        ).count()
        qty_reconciliation_not_used = Reconciliation.objects.filter(movement__status__pk="conciliated", movement__bank__bank_seller=seller, used_in_entry=False).count()
        
        invoices_pending=Invoice.objects.filter(
            Q(seller=seller) &
            Q (status="revised")
        ).exclude(
            Q( used_in_entry= True) |
            Q(transaction_type="inbound-transfer") |
            Q(transaction_type="outgoing-transfer") | 
            Q(invoice_category__code__icontains='_copy')
        ).count()

        qty_inv_per_second = 26
        time_s = int(round(invoices_pending / qty_inv_per_second))
        time_s = time_s if time_s > 0 else 1
        time_m = 0
        time_h = 0
        if time_s >= 60:
            time_m = int(time_s / 60)
            time_s = time_s % 60
            if time_m >= 60:
                time_h = int(time_m / 60)
                time_m = time_m % 60
        time_pending = f"{time_h}h {time_m}m {time_s}s aprox. ({qty_inv_per_second} facturas/seg)"

        context = super().get_context_data(**kwargs)     
        context["seller"] = seller
        context["qty_invoices_amz_not_reconciled"] = qty_invoices_amz_not_reconciled
        context["qty_reconciliation_not_used"] = qty_reconciliation_not_used
        context["reconciliations_pending"] = reconciliations_pending
        context["invoices_pending"] = invoices_pending
        context["limit_entries"] = invoices_pending if invoices_pending < 15000 else 15000
        context["time_pending"] = time_pending
        return context
    
    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

class EntryDeleteAllView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    
        def get(self, request, *args, **kwargs):
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            year = request.GET.get('year') if request.GET.get('year') else str(datetime.datetime.now().year - 1)
            entry = Entry.objects.filter(entry_seller = seller, entry_date__year=year)
            for entry in entry:
                try:
                    if entry.entry_reconciliation is not None and entry.entry_reconciliation.invoice is not None:
                        entry.entry_reconciliation.invoice.used_in_entry = False
                        entry.entry_reconciliation.invoice.save()
                    # print("entry.entry_reconciliation "+ str(entry.entry_reconciliation))
                    if entry.entry_reconciliation is not None:
                        # print("entry.entry_reconciliation.movement.bank.bank_name: "+ str(entry.entry_reconciliation.movement.bank.bank_name))
                        if entry.entry_reconciliation.movement.bank.bank_name.upper() == "AMAZON":
                            mv = entry.entry_reconciliation.movement
                            entry.entry_reconciliation.delete() 
                            mv.delete() 
                        else:
                            entry.entry_reconciliation.used_in_entry = False
                            entry.entry_reconciliation.save()
                    elif entry.entry_invoice is not None:
                        entry.entry_invoice.used_in_entry = False
                        entry.entry_invoice.save()
                except Exception as e:
                    print("Error :", e)
                    pass
                entry.delete()
            return redirect(self.get_success_url())
    
        def get_success_url(self):
            return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])
    
        def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))


class EntryExportView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        return kwargs

    def post(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        print(f"EntryExportView: {str(now)}")
        response = JsonResponse({"error": "Sin Asientos para exportar."})

        # GET DATA FROM FILTERS
        json_data = json.loads(request.body)
        year = json_data['year']
        month = json_data['month']
        search = json_data['search']

        first_date, last_date = get_first_and_last_date(year, month)

        print(f"first_date: {first_date}, last_date: {last_date}, search: {search}")

        shortname = self.kwargs['shortname']
        seller=get_object_or_404(Seller, shortname=shortname)
        
        sql = f"""
            SELECT func_entrylist_csv_json(
                '{seller.id}',
                '{first_date}',
                '{last_date}',
                '{search}'
            )
        """

        try:
            print("sql: "+ str(sql))

            row_limit = 30000 # Try to fix to 2mb or lower
            with connection.cursor() as cursor:

                cursor.execute(sql)
                result_json = cursor.fetchone()[0]  # Obtener el resultado JSON como una cadena

                if result_json != None:

                    result_data = json.loads(result_json)
                    num_records = len(result_data)
                    listados = []
                    listados.append(result_data)
                    for i in range(0, len(result_data), row_limit):
                        lista = result_data[i:i + row_limit]
                        listados.append(lista)
                    print(f"Result_data: {str(len(result_data))} registros")
                    print(f"Hay {str(len(listados))} listados de {str(row_limit)} registros. Total: {str(num_records)}")

                    files_to_zip = []
                    for (i, lista) in enumerate(listados):
                        if i == 0:
                            filePath = f"muaytax/media/xls_entry_list/{shortname}_asientos_{now.year:04d}_{now.month:02d}_{now.day:02d}T{now.hour:02d}{now.minute:02d}{now.second:02d}.xlsx"
                        else:
                            filePath = f"muaytax/media/xls_entry_list/{shortname}_asientos_{now.year:04d}_{now.month:02d}_{now.day:02d}T{now.hour:02d}{now.minute:02d}{now.second:02d}_F{i:04d}.xlsx"
                        print(f"filePath: {filePath}")
                        files_to_zip.append(filePath)
                        if os.path.exists(filePath):
                            os.remove(filePath)

                        df = pd.DataFrame(lista)

                        df['Prefijo Cuenta'] = df['Cuenta'].astype(str).str[:3]

                        prioridad_cuenta =['600', '700', '472','477', '475', '430', '400','410','553']
                        prioridad_mapeo = {k: i for i, k in enumerate(prioridad_cuenta)}

                        df['Prioridad Cuenta'] = df['Prefijo Cuenta'].map(prioridad_mapeo)

                        # Los valores de cuentas que no están en prioridad_cuenta se les asigna un valor muy alto para que queden al final
                        df['Prioridad Cuenta'] = df['Prioridad Cuenta'].fillna(len(prioridad_cuenta))

                        df = df.sort_values(by=['Número asiento', 'Prioridad Cuenta'], ascending=True)

                        # Exporta el DataFrame a un archivo Excel
                        column_order = [
                            'Número asiento',
                            'Fecha asiento',
                            'Documento',
                            'Concepto',
                            'Cuenta',
                            'Importe debe',
                            'Importe haber',
                            'Descripción cuenta',
                            'ID'
                        ]
                        df = df[column_order] 
                        with pd.ExcelWriter(filePath, engine='xlsxwriter') as writer:
                            df.to_excel(writer, sheet_name='Hoja1', index=False)

                            # Obtén el objeto de la hoja
                            worksheet = writer.sheets['Hoja1']

                            # Configura el auto ajuste de ancho de columna para cada columna
                            for i, column in enumerate(df.columns):
                                column_len = max(df[column].astype(str).map(len).max(), len(column) + 2)
                                worksheet.set_column(i, i, column_len)

                    zip_filePath = f"muaytax/media/xls_entry_list/{shortname}_asientos_{now.year:04d}_{now.month:02d}_{now.day:02d}T{now.hour:02d}{now.minute:02d}{now.second:02d}.zip"
                    print(f"zip_filePath: {zip_filePath}")
                    with zipfile.ZipFile(zip_filePath, "w") as zipf:
                        for file in files_to_zip:
                            pdf_filename = os.path.basename(file)
                            zipf.write(file, arcname=pdf_filename)
                                
                    zip_filename = os.path.basename(zip_filePath)
                            

                    for file in files_to_zip:
                        if os.path.exists(file):
                            os.remove(file)
                                        
                    # Create record in entry_file table from zip
                    file_instance = EntryFile(seller = seller)
                    file_instance.file.name = zip_filename
                    file_instance.save()                   

                    total = len(result_data)
                    one_per_cent = int(total / 100) if total > 100 else 1
                    count = 0
                    # marcar como exportados las entry que vienen en el result_json
                    # print("result_data: "+ str(result_data))

                    print(f"Empezamos a reccorer result_data: {len(result_data)} registros")
                    for entry in result_data:
                        entry_id=entry['ID']
                        # print("entry_id: "+ str(entry_id))
                        entry=Entry.objects.get(pk=entry_id)
                        entry.used_in_export=True
                        entry.entry_export=file_instance
                        # print("entry: "+ str(entry.used_in_export))
                        entry.save()
                        count += 1
                        if (count % one_per_cent) == 0:
                            print(f"Progreso: {count} de {total} ( {count / total:.2%} )")
                    print(f"Terminamos de reccorer result_data: {len(result_data)} registros")

                    response = JsonResponse({
                        "zip_filePath": zip_filePath.replace("muaytax", ""),
                        "message": f"Se han exportado {total} asientos contables correctamente"
                        })
                else:
                    response = JsonResponse({"error": "No se han encontrado asientos nuevos para exportar"})

        except Exception as e:
            print("Error:", e)
            response = JsonResponse({"error": "Error al exportar asientos"})

        return response

class EntryDeleteView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    
        def get(self, request, *args, **kwargs):
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            entry = get_object_or_404(Entry, pk=self.kwargs["pk"])
            if entry.entry_reconciliation:
                entry.entry_reconciliation.used_in_entry = False
                entry.entry_reconciliation.save()
            elif entry.entry_invoice:
                entry.entry_invoice.used_in_entry = False
                entry.entry_invoice.save()
            entry.delete()
            return redirect(self.get_success_url())
    
        def get_success_url(self):
            return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])
    
        def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

class EntryNewReconciliationView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        limit = request.GET.get('limit') if request.GET.get('limit') else 1000
        yeargen = request.GET.get('yeargen') if request.GET.get('yeargen') else 'all'
        processed = self.generate_entries_reconciliation(seller, limit, yeargen)
        print(f"processed: {processed}")
        return redirect(self.get_success_url())
        
    def generate_entries_reconciliation(self, seller, limit, yeargen='all'):
        MOVEMENT_TYPE_PRIORITY = {
            "invoice":  1,
            "transfer": 2,
            "account":  3
        }

        reconciliations = Reconciliation.objects.filter(movement__status="conciliated", movement__bank__bank_seller=seller).annotate(
            type_priority=Case(
                When(type__pk='invoice', then=1),
                When(type__pk='transfer', then=2),
                When(type__pk='account', then=3),
                default=4,
                output_field=IntegerField()
            )
        )

        debit = None
        credit = None

        if (yeargen != 'all' and yeargen != '' and yeargen != None and yeargen.isnumeric()):
            reconciliations = reconciliations.filter(movement__movement_date__year=yeargen)

        reconciliations = reconciliations.exclude( used_in_entry= True )
        reconciliations = reconciliations.exclude( movement__bank__bank_name__iexact="AMAZON" )
        reconciliations = reconciliations.order_by("movement__movement_date", "movement__movement_number", "type_priority")
        print("reconciliations:"+ str(reconciliations.count()))

        entry_nums = []
        cont_processed = 0
        for reconciliation in reconciliations:
            # print("conciliación: "+ str(reconciliation.id))
            # Realizar cálculos y crear asientos contables

            
            if reconciliation is not None and  reconciliation.movement is not None and  reconciliation.movement.bank is not None and reconciliation.movement.bank.bank_name is not None:
                if str(reconciliation.movement.bank.bank_name).upper() != "AMAZON":
                    # print("conciliacion distinta de amazon")
                    # print("tipo: "+ str(reconciliation.type))

                    # caso 1 conciliación contra factura de ventas
                    if reconciliation.type.code == "invoice" and reconciliation.invoice is not None and reconciliation.invoice.invoice_category.code=="sales":
                        print("caso 1")
                        cat = reconciliation.invoice.invoice_category.code

                        ####################################################################################################################################
                        # ASIENTO DEL MOVIMIENTO ###########################################################################################################
                        ####################################################################################################################################
                        amount = round(reconciliation.movement.amount_euros, 2) if reconciliation.movement.amount_euros else 0
                        total_entry = 0
                        concept_entry = ""
                        total_entry_negativa = 0
                        concept_entry_negativa = ""

                        # Sales
                        if cat == "sales":
                            if amount >= 0:
                                total_entry = amount
                                total_entry_negativa = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"
                            else:
                                total_entry = 0
                                total_entry_negativa = amount * -1
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"

                        # Limit concept_entry / concept_entry_negativa to 150 characters
                        concept_entry = concept_entry[:150] if concept_entry else ""
                        concept_entry_negativa = concept_entry_negativa[:150] if concept_entry_negativa else ""


                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = concept_entry[:150] if concept_entry else ""
                        concept_entry = aux
                            
                        entry_document = reconciliation.invoice.reference if reconciliation.invoice is not None else "- No tiene factura -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry_accounting_account_description = reconciliation.movement.bank.bank_name if reconciliation.movement.bank is not None else "- No tiene banco -"
                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        entry_accounting_account = reconciliation.movement.bank.bank_accounting_account if reconciliation.movement.bank is not None else 0
                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux

                        if total_entry is None:
                            total_entry = 0
                        if total_entry_negativa is None:
                            total_entry_negativa = 0

                        if num_is_new and (total_entry != 0 or total_entry_negativa != 0):
                            entry=Entry.objects.create(
                                entry_num=num,
                                entry_seller=seller,
                                entry_reconciliation=reconciliation,
                                entry_document=entry_document,
                                entry_date=reconciliation.movement.movement_date,
                                entry_concept=concept_entry,
                                entry_accounting_account=entry_accounting_account,
                                entry_accounting_account_description=entry_accounting_account_description,
                                entry_debit=total_entry,
                                entry_credit=total_entry_negativa,
                            )
                            # Entry List
                            if entry.entry_num not in entry_nums:
                                entry_nums.append(entry.entry_num)
                        ####################################################################################################################################

                        ####################################################################################################################################
                        # ASIENTO DE LA FACTURA ############################################################################################################
                        ####################################################################################################################################
                        # total_invoice=0
                        # concepts=Concept.objects.filter(invoice=reconciliation.invoice)
                        # if concepts.count() > 0:
                        #     for concept in concepts:
                        #         total_invoice += concept.amount_euros 
                        #         total_invoice += concept.vat_euros
                        # else:
                        #     total_invoice=reconciliation.invoice.total_euros

                        # amount = round(total_invoice, 2)
                        amount = round(reconciliation.amount * -1, 2)

                        # Sales
                        if cat == "sales":
                            if amount >= 0:
                                total_entry = amount
                                total_entry_negativa = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"
                            else:
                                total_entry=0
                                total_entry_negativa = amount * - 1
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"


                        entry_date = None
                        entry_accounting_account=0
                        entry_accounting_account_description="- No tiene cliente -"
                        entry_concept = "- No tiene factura -"
                        entry_document = "- No tiene factura -"
                        if reconciliation:
                            if reconciliation.movement and reconciliation.movement.movement_date:
                                entry_date=reconciliation.movement.movement_date
                            if reconciliation.invoice:
                                if  reconciliation.invoice.reference:
                                    entry_concept=concept_entry_negativa
                                    entry_document=reconciliation.invoice.reference
                                if reconciliation.invoice.customer:
                                    if reconciliation.invoice.customer.customer_accounting_account:
                                        entry_accounting_account=reconciliation.invoice.customer.customer_accounting_account
                                    if reconciliation.invoice.customer.name:
                                        entry_accounting_account_description=f"Cuenta Cliente {reconciliation.invoice.customer.name}"
                            else:
                                entry_concept = "- No tiene factura -"
                                entry_document = "- No tiene factura -"
                                entry_accounting_account = 0
                                entry_accounting_account_description = "- No tiene Cuenta -"
                        
                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = entry_concept[:150] if entry_concept else ""
                        entry_concept = aux

                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux

                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=entry_document,
                            entry_date=entry_date,
                            entry_concept=entry_concept,
                            entry_accounting_account=entry_accounting_account,
                            entry_accounting_account_description=entry_accounting_account_description,
                            entry_debit=total_entry_negativa,
                            entry_credit=total_entry,
                        )

                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)

                        invoice_id=reconciliation.invoice.id if reconciliation.invoice is not None else None
                        if invoice_id is not None:
                            ENI = EntryNewInvoicesView().generate_entries(seller, 1, invoice_id, yeargen)
                            if ENI is not None and ENI.get("entry_nums") is not None:
                                # Add every num in ENI to entry_nums
                                for eni_num in ENI.get("entry_nums"):
                                    if eni_num not in entry_nums:
                                        entry_nums.append(eni_num)

                        ####################################################################################################################################
                        
                        # print("Asiento 2 (cliente): "+ str(entry.id))
                        reconciliation.used_in_entry = True
                        reconciliation.save()

                    # caso 2 conciliación contra factura de gastos
                    elif reconciliation.type.code == "invoice" and reconciliation.invoice is not None and reconciliation.invoice.invoice_category.code=="expenses":
                        print("caso 2")
                        cat = reconciliation.invoice.invoice_category.code

                        ####################################################################################################################################
                        # ASIENTO DEL MOVIMIENTO ###########################################################################################################
                        ####################################################################################################################################
                        amount = round(reconciliation.movement.amount_euros * -1, 2) if reconciliation.movement.amount_euros else 0
                        total_entry = 0
                        concept_entry = ""
                        total_entry_negativa = 0
                        concept_entry_negativa = ""

                        # Expenses
                        if cat == "expenses":
                            if amount >= 0:
                                debit = 0
                                credit = amount
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                            else:
                                debit = amount * -1
                                credit = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"

                        # Limit concept_entry / concept_entry_negativa to 150 characters
                        concept_entry = concept_entry[:150] if concept_entry else ""
                        concept_entry_negativa = concept_entry_negativa[:150] if concept_entry_negativa else ""


                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = concept_entry[:150] if concept_entry else ""
                        concept_entry = aux

                        aux = concept_entry_negativa[:150] if concept_entry_negativa else ""
                        concept_entry_negativa = aux

                        entry_document = reconciliation.invoice.reference if reconciliation.invoice is not None else "- No tiene factura -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry_accounting_account_description = reconciliation.movement.bank.bank_name if reconciliation.movement.bank is not None else "- No tiene banco -"
                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        entry_accounting_account = reconciliation.movement.bank.bank_accounting_account if reconciliation.movement.bank is not None else 0
                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux
                            
                        if debit is None :
                            debit = 0
                        if credit is None :
                            credit = 0

                        if num_is_new and (debit != 0 or credit != 0):
                            entry=Entry.objects.create(
                                entry_num=num,
                                entry_seller=seller,
                                entry_reconciliation=reconciliation,
                                entry_document=entry_document,
                                entry_date=reconciliation.movement.movement_date,
                                entry_concept=concept_entry,
                                entry_accounting_account=entry_accounting_account,
                                entry_accounting_account_description=entry_accounting_account_description,
                                entry_debit=debit,
                                entry_credit=credit,
                            )
                            # Entry List
                            if entry.entry_num not in entry_nums:
                                entry_nums.append(entry.entry_num)
                        ####################################################################################################################################

                        ####################################################################################################################################
                        # ASIENTO DE LA FACTURA ############################################################################################################
                        ####################################################################################################################################
                        # total_invoice=0
                        # concepts=Concept.objects.filter(invoice=reconciliation.invoice)
                        # if concepts.count() > 0:
                        #     for concept in concepts:
                        #         total_invoice += concept.amount_euros 
                        #         total_invoice += concept.vat_euros
                        # else:
                        #     total_invoice=reconciliation.invoice.total_euros

                        # amount_invoice = round(total_invoice, 2)
                        amount_invoice = round(reconciliation.amount,2)

                        # Expenses
                        if cat == "expenses":
                            if amount_invoice >= 0:
                                debit = amount_invoice
                                credit = 0
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                            else:
                                debit = 0
                                credit = amount_invoice * -1
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"

                        entry_date = None
                        entry_accounting_account=0
                        entry_accounting_account_description="- No tiene cliente -"
                        entry_concept = "- No tiene factura -"
                        entry_document = "- No tiene factura -"
                        if reconciliation:
                            if reconciliation.movement and reconciliation.movement.movement_date:
                                entry_date=reconciliation.movement.movement_date
                            if reconciliation.invoice:
                                if  reconciliation.invoice.reference:
                                    entry_concept=concept_entry_negativa
                                    entry_document=reconciliation.invoice.reference
                                if reconciliation.invoice.provider:
                                    if reconciliation.invoice.provider.provider_accounting_account:
                                        entry_accounting_account=reconciliation.invoice.provider.provider_accounting_account
                                    if reconciliation.invoice.provider.name:
                                        entry_accounting_account_description=f"Cuenta Proveedor {reconciliation.invoice.provider.name}"
                            else:
                                entry_concept = "- No tiene factura -"
                                entry_document = "- No tiene factura -"
                                entry_accounting_account = 0
                                entry_accounting_account_description = "- No tiene Cuenta -"

                        
                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = entry_concept[:150] if entry_concept else ""
                        entry_concept = aux

                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux

                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=entry_document,
                            entry_date=entry_date,
                            entry_concept=entry_concept,
                            entry_accounting_account=entry_accounting_account,
                            entry_accounting_account_description=entry_accounting_account_description,
                            entry_debit=debit,
                            entry_credit=credit,
                        )

                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)

                        invoice_id=reconciliation.invoice.id if reconciliation.invoice is not None else None
                        if invoice_id is not None:
                            ENI = EntryNewInvoicesView().generate_entries(seller, 1, invoice_id, yeargen)
                            if ENI is not None and ENI.get("entry_nums") is not None:
                                # Add every num in ENI to entry_nums
                                for eni_num in ENI.get("entry_nums"):
                                    if eni_num not in entry_nums:
                                        entry_nums.append(eni_num)

                        ####################################################################################################################################
                        
                        # print("Asiento 2 (cliente): "+ str(entry.id))
                        reconciliation.used_in_entry = True
                        reconciliation.save()

                    # caso 3  conciliación contra la cuenta 555, movimiento positivo
                    elif (
                        reconciliation.type.code == "account" and 
                        reconciliation.movement is not None and 
                        (
                            (reconciliation.accounting_account and reconciliation.accounting_account.pk == "*********") or 
                            (reconciliation.accounting_account_detail and len(reconciliation.accounting_account_detail) > 1 and reconciliation.accounting_account_detail == "*********")
                        )
                    ):
                        print("caso 3")
                        ####################################################################################################################################
                        # ASIENTO DEL MOVIMIENTO ###########################################################################################################
                        ####################################################################################################################################
                        total_entry_negativa=0
                        total_entry=round(reconciliation.movement.amount_euros, 2)
                        concept_entry_negativa = f"Pago de {reconciliation.movement.concept}",
                        concept_entry = f"Cobro de {reconciliation.movement.concept}",
                        if total_entry < 0:
                            total_entry_negativa=total_entry*-1
                            total_entry=0
                            concept_entry_negativa = f"Cobro de {reconciliation.movement.concept}",
                            concept_entry = f"Pago de {reconciliation.movement.concept}",
                        
                        # Limit concept_entry / concept_entry_negativa to 150 characters
                        concept_entry = str(concept_entry[:150]).replace("('", "").replace("')", "").replace("',)", "")
                        concept_entry_negativa = str(concept_entry_negativa[:150]).replace("('", "").replace("')", "").replace("',)", "")

                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = concept_entry[:150] if concept_entry else ""
                        concept_entry = aux

                        aux = concept_entry_negativa[:150] if concept_entry_negativa else ""
                        concept_entry_negativa = aux

                        entry_document = reconciliation.movement.concept if reconciliation.movement is not None else "- No tiene movimiento -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry_accounting_account_description = reconciliation.movement.bank.bank_name if reconciliation.movement.bank is not None else "- No tiene banco -"
                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        entry_accounting_account = reconciliation.movement.bank.bank_accounting_account if reconciliation.movement.bank is not None else 0
                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux

                        if debit is None :
                            debit = 0
                        if credit is None :
                            credit = 0

                        if num_is_new and (debit != 0 or credit != 0):
                            entry=Entry.objects.create(
                                entry_num=num,
                                entry_seller=seller,
                                entry_reconciliation=reconciliation,
                                entry_document=entry_document,
                                entry_date=reconciliation.movement.movement_date,
                                entry_concept=concept_entry,
                                entry_accounting_account=entry_accounting_account,
                                entry_accounting_account_description=entry_accounting_account_description,
                                entry_debit=total_entry ,
                                entry_credit=total_entry_negativa,
                            )
                            # Entry List
                            if entry.entry_num not in entry_nums:
                                entry_nums.append(entry.entry_num)
                        ####################################################################################################################################

                        ####################################################################################################################################
                        # ASIENTO DE LA CUENTA #############################################################################################################
                        ####################################################################################################################################
                        total_entry_negativa=0
                        total_entry=round(reconciliation.amount * -1, 2)
                        if total_entry < 0:
                            total_entry_negativa=total_entry*-1
                            total_entry=0

                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = concept_entry[:150] if concept_entry else ""
                        concept_entry = aux

                        aux = concept_entry_negativa[:150] if concept_entry_negativa else ""
                        concept_entry_negativa = aux

                        entry_document = reconciliation.movement.concept if reconciliation.movement is not None else "- No tiene movimiento -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=entry_document,
                            entry_date=reconciliation.movement.movement_date,
                            entry_concept=concept_entry_negativa,
                            entry_accounting_account="*********",
                            entry_accounting_account_description="Partidas Pendientes de Aplicación",
                            entry_debit=total_entry_negativa,
                            entry_credit=total_entry,
                        )
                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)
                        ####################################################################################################################################

                        # print("Asiento 2: "+ str(entry.id))
                        reconciliation.used_in_entry = True
                        reconciliation.save()

                    # caso 5 conciliación contra la cuenta  NO 555, movimiento positivo
                    elif (
                        reconciliation.type.code == "account" and 
                        reconciliation.movement is not None and 
                        (
                            (reconciliation.accounting_account and reconciliation.accounting_account.pk != "*********") or 
                            (reconciliation.accounting_account_detail and len(reconciliation.accounting_account_detail) > 1 and reconciliation.accounting_account_detail != "*********")
                        )
                    ):
                        print("caso 5")

                        ####################################################################################################################################
                        # ASIENTO DEL MOVIMIENTO ###########################################################################################################
                        ####################################################################################################################################
                        concept_entry_negativa = f"Pago de {reconciliation.movement.concept}"
                        concept_entry = f"Cobro de {reconciliation.movement.concept}"

                        amount_entry = round(reconciliation.movement.amount_euros, 2)
                        credit=0
                        debit = amount_entry
                        if amount_entry < 0:
                            credit = amount_entry * -1
                            debit=0
                            concept_entry_negativa = f"Cobro de {reconciliation.movement.concept}",
                            concept_entry = f"Pago de {reconciliation.movement.concept}",
                        
                        # Limit concept_entry / concept_entry_negativa to 150 characters
                        concept_entry = str(concept_entry[:150]).replace("('", "").replace("')", "").replace("',)", "")
                        concept_entry_negativa = str(concept_entry_negativa[:150]).replace("('", "").replace("')", "").replace("',)", "")

                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        aux = concept_entry[:150] if concept_entry else ""
                        concept_entry = aux

                        aux = concept_entry_negativa[:150] if concept_entry_negativa else ""
                        concept_entry_negativa = aux

                        entry_document = str(reconciliation.movement.concept) if reconciliation.movement is not None else "- No tiene movimiento -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry_accounting_account_description = str(reconciliation.movement.bank.bank_name) if reconciliation.movement.bank is not None else "- No tiene banco -"
                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        entry_accounting_account = str(reconciliation.movement.bank.bank_accounting_account) if reconciliation.movement.bank is not None else 0
                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux

                        if debit is None :
                            debit = 0
                        if credit is None :
                            credit = 0

                        if num_is_new and (debit != 0 or credit != 0):
                            entry=Entry.objects.create(
                                entry_num=num,
                                entry_seller=seller,
                                entry_reconciliation=reconciliation,
                                entry_document=entry_document,
                                entry_date=reconciliation.movement.movement_date,
                                entry_concept=concept_entry,
                                entry_accounting_account=entry_accounting_account,
                                entry_accounting_account_description=entry_accounting_account_description,
                                entry_debit=debit,
                                entry_credit=credit,
                            )
                            # Entry List
                            if entry.entry_num not in entry_nums:
                                entry_nums.append(entry.entry_num)

                        ####################################################################################################################################

                        ####################################################################################################################################
                        # ASIENTO DE LA CUENTA #############################################################################################################
                        ####################################################################################################################################
                        amount_account = round(reconciliation.amount, 2)
                        credit=0
                        debit=amount_account
                        if amount_account < 0:
                            credit = amount_account*-1
                            debit = 0

                        account_number = reconciliation.accounting_account.pk if reconciliation.accounting_account is not None else None
                        if account_number is None:
                            account_number = reconciliation.accounting_account_detail if reconciliation.accounting_account_detail is not None else '0'
                        account_number = str(account_number)

                        # concept_entry = None
                        # concept_entry_negativa = None

                        ac_description = None
                        if account_number == "*********":
                            ac_description="Capital social"
                        elif account_number == "*********":
                            ac_description="Aportación de socios"
                        elif account_number == "*********":
                            ac_description= "Préstamo " + str(reconciliation.movement.bank.bank_name)
                        elif account_number == "*********":
                            ac_description="Devolución IVA"
                        elif account_number == "*********":
                            ac_description="Pagos a cuenta IS"
                        elif account_number == "*********":
                            ac_description="Pago IVA"
                        elif account_number == "*********":
                            ac_description="Pago Seguridad Social"
                        elif account_number == "*********":
                            ac_description="Cuenta corriente con socios"
                        elif account_number == "*********":
                            ac_description="Ingreso en efectivo"
                        elif account_number == "*********":
                            ac_description="Intereses de deuda"
                        elif account_number == "*********":
                            ac_description="Otros gastos"
                        elif account_number == "*********":
                            ac_description="Gastos bancarios"
                        elif account_number == "*********":
                            ac_description="Gastos no deducibles"
                        else:
                            aa = AccountingAccount.objects.filter(pk=account_number).first()
                            if aa is not None:
                                ac_description=f"{aa.description}"
                            else:
                                ac_description=f"Cuenta Contable {account_number}"

                        # if concepto is not None:
                        #     concept_entry = concepto
                        #     concept_entry = concept_entry[:150] if concept_entry else ""
                        #     concept_entry_negativa = concepto
                        #     concept_entry_negativa = concept_entry_negativa[:150] if concept_entry_negativa else ""

                        aux = concept_entry[:150] if concept_entry else ""
                        concept_entry = aux

                        aux = concept_entry_negativa[:150] if concept_entry_negativa else ""
                        concept_entry_negativa = aux

                        entry_document = str(reconciliation.movement.concept) if reconciliation.movement is not None else "- No tiene movimiento -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry_accounting_account_description = ac_description if ac_description is not None else "- No tiene Cuenta Contable -"
                        aux = entry_accounting_account_description[:150] if entry_accounting_account_description else ""
                        entry_accounting_account_description = aux

                        entry_accounting_account = account_number if account_number is not None else 0
                        aux = str(entry_accounting_account)[:150] if entry_accounting_account else ""
                        entry_accounting_account = aux

                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=entry_document,
                            entry_date=reconciliation.movement.movement_date,
                            entry_concept=concept_entry_negativa,
                            entry_accounting_account=entry_accounting_account,
                            entry_accounting_account_description=entry_accounting_account_description,
                            entry_debit=debit,
                            entry_credit=credit,
                        )
                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)
                        
                        ####################################################################################################################################

                        reconciliation.used_in_entry = True
                        reconciliation.save()
            
                    # caso 7 conciliacion contra otro movimiento de otro banco (Transfer)
                    elif reconciliation.type.code == "transfer" and reconciliation.movement is not None and reconciliation.movement_transfer is not None:
                        print("caso 7")
                        rec_used = Reconciliation.objects.filter(movement__pk = reconciliation.movement.pk, type__pk = 'transfer').filter(used_in_entry=True).first()
                        if rec_used is not None and rec_used.used_in_entry:
                            continue

                        amount_movement = round(reconciliation.movement.amount_euros, 2)
                        debit = amount_movement
                        credit = 0
                        if amount_movement < 0:
                            debit = 0
                            credit = amount_movement * -1

                        num=1
                        num_is_new = True
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                            num_is_new = False
                        else:
                            last_entry=Entry.objects.filter(entry_reconciliation__movement=reconciliation.movement,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                                num_is_new = False
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                                    num_is_new = True

                        accounting = 0
                        accounting_desc = "Transferencia entre cuentas"
                        if reconciliation.movement.bank:
                            accounting = f"{reconciliation.movement.bank.bank_accounting_account}"
                            aux = accounting[:150] if accounting else ""
                            accounting = aux
                            accounting_desc = f"Cuenta Banco {reconciliation.movement.bank.bank_name}"
                            aux = accounting_desc[:150] if accounting_desc else ""
                            accounting_desc = aux

                        concepto=f"Cobro de {reconciliation.movement.concept} (Transferencia entre cuentas mov)"
                        aux = concepto[:150] if concepto else ""
                        concepto = aux

                        entry_document = reconciliation.movement.concept if reconciliation.movement is not None else "- No tiene movimiento -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        print("Nun is new: ", num_is_new)
                        if num_is_new:
                            entry=Entry.objects.create(
                                entry_num=num,
                                entry_seller=seller,
                                entry_reconciliation=reconciliation,
                                entry_document=entry_document,
                                entry_date=reconciliation.movement.movement_date,
                                entry_concept=concepto,
                                entry_accounting_account=accounting,
                                entry_accounting_account_description=accounting_desc,
                                entry_debit=debit ,
                                entry_credit=credit,
                            )

                            # Entry List
                            if entry.entry_num not in entry_nums:
                                entry_nums.append(entry.entry_num)


                        amount_transfer = round(reconciliation.amount, 2)
                        debit = amount_transfer
                        credit=0
                        if amount_transfer < 0:
                            debit=0
                            credit= amount_transfer * -1

                        accounting = 0
                        accounting_desc = "Transferencia entre cuentas"
                        if reconciliation.movement_transfer.bank:
                            accounting = f"{reconciliation.movement_transfer.bank.bank_accounting_account}"
                            aux = accounting[:150] if accounting else ""
                            accounting = aux
                            accounting_desc = f"Cuenta Banco {reconciliation.movement_transfer.bank.bank_name}"
                            aux = accounting_desc[:150] if accounting_desc else ""
                            accounting_desc = aux

                        # print("Asiento 1: "+ str(entry.id))
                        concepto=f"Pago de {reconciliation.movement.concept} (Transferencia entre cuentas)"
                        aux = concepto[:150] if concepto else ""
                        concepto = aux

                        entry_document = reconciliation.movement.concept if reconciliation.movement is not None else "- No tiene movimiento -"
                        aux = entry_document[:150] if entry_document else ""
                        entry_document = aux

                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=entry_document,
                            entry_date=reconciliation.movement.movement_date,
                            entry_concept=concepto,
                            entry_accounting_account=accounting,
                            entry_accounting_account_description=accounting_desc,
                            entry_debit=debit,
                            entry_credit=credit,
                        )

                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)

                        # print("Asiento 2: "+ str(entry.id))
                        reconciliation.used_in_entry = True
                        reconciliation.save()

                        try:
                            print(f"rec: {reconciliation.movement.pk} - rec_transfer: {reconciliation.movement_transfer.pk} - amount: {reconciliation.movement.amount_euros}")
                            rec_transfer = Reconciliation.objects.filter(movement__pk=reconciliation.movement_transfer.pk).first()
                            rec_transfer.used_in_entry = True
                            rec_transfer.save()
                        except Exception as e:
                            print("Error en rec_transfer:", e)

                    cont_processed += 1
                    if cont_processed >= limit:
                        break                

        return { "cont_processed": cont_processed , "entry_nums": entry_nums }

    def get_success_url(self):
        return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class EntryNewReconciliationAmzView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        limit = request.GET.get('limit') if request.GET.get('limit') else 1000
        yeargen = request.GET.get('yeargen') if request.GET.get('yeargen') else 'all'
        processed = self.generate_entries_reconciliation_amz(seller, limit, yeargen)
        print(f"processed: {processed}")
        return redirect(self.get_success_url())
        
    def generate_entries_reconciliation_amz(self, seller, limit, yeargen='all'):
        print(f"generate_entries_reconciliation_amz: {yeargen}")
        bank_amazon=Bank.objects.filter(bank_seller=seller, bank_name__iexact="AMAZON").first()     
        # Invoices AMZ-TXT not used => Exclude Transfers
        first_invoice = None
        cont_inv_processed = 0
        cont_rec_processed = 0
        invoices_txt=Invoice.objects.filter(seller=seller, status="revised", is_txt_amz = True).exclude(
            Q( used_in_entry= True) |
            # Q( is_txt_amz = False) |
            # Q( is_txt_amz = None) |
            Q(transaction_type="inbound-transfer") | 
            Q(transaction_type="outgoing-transfer") |
            Q(invoice_category__code__icontains='_copy')
        )
        if (yeargen != 'all' and yeargen != '' and yeargen != None and yeargen.isnumeric()):
            invoices_txt = invoices_txt.filter(accounting_date__year=yeargen)
        invoices_txt = invoices_txt.order_by("accounting_date", "reference")
        print("Total facturas txt:"+ str(invoices_txt.count()))       


        movement_number_nuevo_var = None
        entry_nums = []

        for invoice_txt in invoices_txt:
                # print("factura txt: "+ str(invoice_txt.id))
                # Realizar cálculos y crear asientos contables
                total_invoice=0
                concepts=Concept.objects.filter(invoice=invoice_txt)
                if concepts.count() > 0:
                    for concept in concepts:
                        total_invoice += concept.amount_euros 
                        total_invoice += concept.vat_euros
                else:
                    total_invoice=invoice_txt.total_euros


                # si no tiene cliente se crea para ese seller, el cliente "Clientes Particulares" seguido del nombre del marketplace de la invoice,  por ej, "Clientes Particulares Amazon.es"
                nuevo_cliente=None
                cliente_exite=None
                client_name="Clientes Particulares"
                if invoice_txt.customer is None:
                    if invoice_txt.marketplace is not None:
                        client_name+=" "+invoice_txt.marketplace.description

                    cliente_exite=Customer.objects.filter(name=client_name, seller=seller).first()
                    if cliente_exite is not None:
                        invoice_txt.customer=cliente_exite
                        invoice_txt.save()
                    else:   
                        nuevo_cliente=Customer.objects.create(
                            name=client_name,
                            seller=seller,
                        )
                        invoice_txt.customer=nuevo_cliente
                        invoice_txt.save()
                # if cliente_exite is not None:
                #     print("cliente existe: "+ str(cliente_exite.name)+ " "+ str(cliente_exite.customer_accounting_account))
                # if nuevo_cliente is not None:
                #     print("cliente nuevo: "+ str(nuevo_cliente.name)+ " "+ str(nuevo_cliente.customer_accounting_account))

                #  crea movimiento de banco AMAZON
                bank_amazon=Bank.objects.filter(bank_seller=seller, bank_name__iexact="AMAZON").first()
                if bank_amazon is None:
                    bank_amazon=Bank.objects.create(
                        bank_seller=seller,
                        bank_name="AMAZON",
                        bank_accounting_account="*********",
                        bank_account_type=BankType.objects.get(code="bankaccount"),
                    )
                # print("banco amazon: "+ str(bank_amazon.id))
                # movement_number_nuevo=str(invoice_txt.accounting_date.year)+"-"+str(invoice_txt.accounting_date.month).zfill(2)+"-000000"
                # movement_month_actual=Movement.objects.filter(bank=bank_amazon, movement_date__month=invoice_txt.accounting_date.month, movement_date__year=invoice_txt.accounting_date.year).order_by("-movement_date").first()
                # if movement_month_actual is not None:
                #     movement_number_nuevo=str(invoice_txt.accounting_date.year)+"-"+str(invoice_txt.accounting_date.month).zfill(2)+"-"+str(int(movement_month_actual.movement_number[-6:])+1).zfill(6)
                
                concept= "Cobro de la factura "+ invoice_txt.reference
                if total_invoice < 0:
                    concept= "Devolución de la factura "+ invoice_txt.reference

                if invoice_txt.accounting_date is None:
                    if invoice_txt.expedition_date is not None:
                        invoice_txt.accounting_date=invoice_txt.expedition_date
                    elif invoice_txt.invoice_date is not None:
                        invoice_txt.accounting_date=invoice_txt.invoice_date

                if invoice_txt.accounting_date is not None:
                    inv_year_month = f"{str(invoice_txt.accounting_date.year).zfill(4)}-{str(invoice_txt.accounting_date.month).zfill(2)}-"                    
                    condition1 = True if movement_number_nuevo_var is None else False
                    condition2 = True if movement_number_nuevo_var is not None and movement_number_nuevo_var == "" else False
                    condition3 = True if movement_number_nuevo_var is not None and inv_year_month not in movement_number_nuevo_var else False
                    if condition1 or condition2 or condition3:
                        movement_month_actual_var=Movement.objects.filter(bank=bank_amazon, movement_date__month=invoice_txt.accounting_date.month, movement_date__year=invoice_txt.accounting_date.year).order_by("-movement_date").first()
                        if movement_month_actual_var is not None:
                            movement_number_nuevo_var=inv_year_month + str(int(movement_month_actual_var.movement_number[-6:])+1).zfill(6)
                        else:
                            movement_number_nuevo_var=inv_year_month + "000000"
                    else:
                        incremental_number=str(int(movement_number_nuevo_var[-6:])+1).zfill(6)
                        movement_number_nuevo_var=inv_year_month + incremental_number
                else:
                    incremental_number=str(int(movement_number_nuevo_var[-6:])+1).zfill(6)
                    movement_number_nuevo_var=inv_year_month + incremental_number

                movement=Movement.objects.create(
                    bank=bank_amazon,
                    movement_date=invoice_txt.accounting_date,
                    movement_number=movement_number_nuevo_var,
                    concept=concept,
                    amount=total_invoice,
                    amount_euros=total_invoice,
                    status=MovementStatus.objects.get(code="conciliated"),
                )
                # print("movimiento amazon: "+ str(movement.id))
                # concilia movimiento contra factura AMAZON
                reconciliation=Reconciliation.objects.create(
                    movement=movement,
                    amount=-total_invoice,
                    invoice=invoice_txt,
                    type=ReconciliationType.objects.get(code="invoice"),
                    used_in_entry=True,
                )
                # print("conciliacion amazon: "+ str(reconciliation.id))
                        


                #  crea conciliacion contra cuenta AMAZON

                
                # print("Asiento 1 (amazon): "+ str(entry.id))                    

                cat = reconciliation.invoice.invoice_category.code if reconciliation and reconciliation.invoice and reconciliation.invoice.invoice_category else 'sales'
                amount = round(total_invoice, 2)
                total_entry = 0
                concept_entry = ""
                total_entry_negativa = 0
                concept_entry_negativa = ""

                # Sales
                if cat == "sales":
                    if amount >= 0:
                        total_entry = amount
                        total_entry_negativa = 0
                        concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                        concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"
                    else:
                        total_entry = 0
                        total_entry_negativa = amount * -1
                        concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                        concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                # Expenses
                elif cat == "expenses":
                    if amount >= 0:
                        total_entry = 0
                        total_entry_negativa = amount
                        concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                        concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                    else:
                        total_entry = amount * -1
                        total_entry_negativa = 0
                        concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                        concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"
                        
                concept_entry = concept_entry[:150] if concept_entry else ""
                concept_entry_negativa = concept_entry_negativa[:150] if concept_entry_negativa else ""

                customer_account="No tiene cliente"
                if invoice_txt.customer is not None:
                    customer_account=invoice_txt.customer.customer_accounting_account
                
                customer_name="No tiene cliente"
                if invoice_txt.customer is not None:
                    customer_name=invoice_txt.customer.name

                num=1
                last_entry=Entry.objects.filter(entry_invoice=invoice_txt,entry_seller=seller).order_by("-entry_num").first()
                if last_entry is not None and last_entry.entry_num is not None:
                    num = last_entry.entry_num
                else:
                    last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    if last_entry is not None and last_entry.entry_num is not None:
                        num = int(last_entry.entry_num) + 1


                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    # entry_invoice=invoice_txt,
                    entry_reconciliation=reconciliation,
                    entry_document=invoice_txt.reference,
                    entry_date=invoice_txt.accounting_date,
                    entry_concept=concept_entry,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="AMAZON",
                    entry_debit=total_entry ,
                    entry_credit=total_entry_negativa,
                )
                # Entry List
                if entry.entry_num not in entry_nums:
                    entry_nums.append(entry.entry_num)

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice_txt,
                    entry_document=invoice_txt.reference,
                    entry_date=invoice_txt.accounting_date,
                    entry_concept=concept_entry_negativa,
                    entry_accounting_account=customer_account,
                    entry_accounting_account_description="Cuenta del cliente "+ customer_name,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                
                # Entry List
                if entry.entry_num not in entry_nums:
                    entry_nums.append(entry.entry_num)

                # print("Asiento 2 (cliente): "+ str(entry.id))
                # invoice_txt.used_in_entry = True
                # invoice_txt.save()
                ENI = EntryNewInvoicesView().generate_entries(seller, 1, invoice_txt.id)
                entry_nums += ENI.get("entry_nums")

                # movement_number_nuevo_var +1
                # movement_number_nuevo_var=str(int(movement_number_nuevo_var[-6:])+1).zfill(6)

                cont_inv_processed += 1
                # print("cont_inv_processed: "+ str(cont_inv_processed))
                if cont_inv_processed >= limit:
                    break

        # Reconcilations AMAZON not used
        reconciliations = Reconciliation.objects.filter( 
            movement__status="conciliated", 
            movement__bank__bank_seller=seller, 
        ).exclude(
            Q( used_in_entry= True) 
        )
        if (yeargen != 'all' and yeargen != '' and yeargen != None and yeargen.isnumeric()):
            reconciliations = reconciliations.filter(movement__movement_date__year=yeargen)
        reconciliations = reconciliations.order_by("movement__movement_number")
        print("reconciliations:"+ str(reconciliations.count()))

        for reconciliation in reconciliations:
            print(f"REC {reconciliation.id} - MV: {reconciliation.movement} - REC.TYPE: {reconciliation.type} - REC.INV: {reconciliation.invoice}")
            # print("conciliación: "+ str(reconciliation.id))
            # Realizar cálculos y crear asientos contables

            total_entry_negativa=0
            total_entry = 0
            if reconciliation is not None and  reconciliation.movement is not None and  reconciliation.movement.bank is not None and reconciliation.movement.bank.bank_name is not None:
                if str(reconciliation.movement.bank.bank_name).upper() == "AMAZON":
                    # print("conciliacion distinta de amazon")
                    # print("tipo: "+ str(reconciliation.type))

                    # caso 1) Factura Ventas Amazon
                    if reconciliation.type.code == "invoice" and reconciliation.invoice is not None and reconciliation.invoice.invoice_category.code=="sales":
                        # print("caso 1")  

                        cat = reconciliation.invoice.invoice_category.code
                        amount = round(reconciliation.movement.amount_euros, 2) if reconciliation.movement.amount_euros else 0
                        total_entry = 0
                        concept_entry = ""
                        total_entry_negativa = 0
                        concept_entry_negativa = ""

                        # Sales
                        if cat == "sales":
                            if amount >= 0:
                                total_entry = amount
                                total_entry_negativa = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"
                            else:
                                total_entry = 0
                                total_entry_negativa = amount * -1
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                        # Expenses
                        elif cat == "expenses":
                            if amount >= 0:
                                total_entry = 0
                                total_entry_negativa = amount
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                            else:
                                total_entry = amount * -1
                                total_entry_negativa = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"

                        # Limit concept_entry / concept_entry_negativa to 150 characters
                        concept_entry = concept_entry[:150] if concept_entry else ""
                        concept_entry_negativa = concept_entry_negativa[:150] if concept_entry_negativa else ""

                        num=1
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                        else:
                            last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = int(last_entry.entry_num) + 1                     
                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=reconciliation.invoice.reference,
                            entry_date=reconciliation.movement.movement_date,
                            entry_concept=concept_entry,
                            entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                            entry_accounting_account_description="Cuenta Banco "+reconciliation.movement.bank.bank_name,
                            entry_debit=total_entry,
                            entry_credit=total_entry_negativa,
                        )
                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)


                        entry_date = None
                        entry_accounting_account=0
                        entry_accounting_account_description="- No tiene cliente -"
                        entry_concept = "- No tiene factura -"
                        entry_document = "- No tiene factura -"
                        if reconciliation:
                            if reconciliation.movement and reconciliation.movement.movement_date:
                                entry_date=reconciliation.movement.movement_date
                            if reconciliation.invoice:
                                if  reconciliation.invoice.reference:
                                    entry_concept=concept_entry_negativa
                                    entry_document=reconciliation.invoice.reference
                                if reconciliation.invoice.customer:
                                    if reconciliation.invoice.customer.customer_accounting_account:
                                        entry_accounting_account=reconciliation.invoice.customer.customer_accounting_account
                                    if reconciliation.invoice.customer.name:
                                        entry_accounting_account_description=f"Cuenta Cliente {reconciliation.invoice.customer.name}"

                            num=1
                            last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = last_entry.entry_num
                            else:
                                last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                if last_entry is not None and last_entry.entry_num is not None:
                                    num = int(last_entry.entry_num) + 1
                            entry=Entry.objects.create(
                                entry_num=num,
                                entry_seller=seller,
                                entry_reconciliation=reconciliation,
                                entry_document=entry_document,
                                entry_date=entry_date,
                                entry_concept=entry_concept,
                                entry_accounting_account=entry_accounting_account,
                                entry_accounting_account_description=entry_accounting_account_description,
                                entry_debit= total_entry_negativa,
                                entry_credit=total_entry,
                            )
                            # Entry List
                            if entry.entry_num not in entry_nums:
                                entry_nums.append(entry.entry_num)

                            print("Asiento 2 (cliente): "+ str(entry.id))
                            reconciliation.used_in_entry = True
                            reconciliation.save()

                            invoice_id=reconciliation.invoice.id if reconciliation.invoice is not None else None
                            if invoice_id is not None:
                                ENI = EntryNewInvoicesView().generate_entries(seller, 1, invoice_id)
                                entry_nums += ENI.get("entry_nums")

                    # caso 2) Factura Gastos Amazon
                    elif reconciliation.type.code == "invoice" and reconciliation.invoice is not None and reconciliation.invoice.invoice_category.code=="expenses":
                        # print("caso 2")

                        cat = reconciliation.invoice.invoice_category.code
                        amount = round(reconciliation.movement.amount_euros, 2) if reconciliation.movement.amount_euros else 0
                        total_entry = 0
                        concept_entry = ""
                        total_entry_negativa = 0
                        concept_entry_negativa = ""

                        # Sales
                        if cat == "sales":
                            if amount >= 0:
                                total_entry = amount
                                total_entry_negativa = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"
                            else:
                                total_entry = 0
                                total_entry_negativa = amount * -1
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                        # Expenses
                        elif cat == "expenses":
                            if amount >= 0:
                                total_entry = 0
                                total_entry_negativa = amount
                                concept_entry = f"Pago de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Cobro de la factura {reconciliation.invoice.reference}"
                            else:
                                total_entry = amount * -1
                                total_entry_negativa = 0
                                concept_entry = f"Cobro de la factura {reconciliation.invoice.reference}"
                                concept_entry_negativa = f"Pago de la factura {reconciliation.invoice.reference}"

                        # Limit concept_entry / concept_entry_negativa to 150 characters
                        concept_entry = concept_entry[:150] if concept_entry else ""
                        concept_entry_negativa = concept_entry_negativa[:150] if concept_entry_negativa else ""

                        num=1
                        last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                        if last_entry is not None and last_entry.entry_num is not None:
                            num = last_entry.entry_num
                        else:
                            last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                            if last_entry is not None and last_entry.entry_num is not None:
                                num = int(last_entry.entry_num) + 1

                        prov_account = 0
                        prov_account_desc = "- No tiene proveedor -"
                        if reconciliation.invoice.provider:
                            prov_account = reconciliation.invoice.provider.provider_accounting_account
                            prov_account_desc = "Cuenta Proveedor " + reconciliation.invoice.provider.name
                        elif reconciliation.invoice.invoice_type_id == "payroll":
                            prov_account = "*********"
                            prov_account_desc = "Cuenta de Remuneraciones Pendientes de Pago"

                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=reconciliation.invoice.reference,
                            entry_date=reconciliation.movement.movement_date,
                            entry_concept=concept_entry,
                            entry_accounting_account = prov_account,
                            entry_accounting_account_description = prov_account_desc,
                            entry_debit=total_entry,
                            entry_credit=total_entry_negativa,
                        )
                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)

                        # print("Asiento 1 (proveedor): "+ str(entry.id))
                        entry=Entry.objects.create(
                            entry_num=num,
                            entry_seller=seller,
                            entry_reconciliation=reconciliation,
                            entry_document=reconciliation.invoice.reference,
                            entry_date=reconciliation.movement.movement_date,
                            entry_concept=concept_entry_negativa,
                            entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                            entry_accounting_account_description="Cuenta Banco "+reconciliation.movement.bank.bank_name,
                            entry_debit=total_entry_negativa,
                            entry_credit=total_entry,
                        )
                        # Entry List
                        if entry.entry_num not in entry_nums:
                            entry_nums.append(entry.entry_num)

                        print("Asiento 2 (bancos): "+ str(entry.id))
                        reconciliation.used_in_entry = True
                        reconciliation.save()

                        invoice_id=reconciliation.invoice.id if reconciliation.invoice is not None else None
                        if invoice_id is not None:
                            ENI = EntryNewInvoicesView().generate_entries(seller, 1, invoice_id)
                            entry_nums += ENI.get("entry_nums")

            cont_rec_processed += 1
            # print("cont_rec_processed: "+ str(cont_rec_processed))
            if cont_rec_processed >= limit:
                break
        
        cont_processed = 0
        if cont_rec_processed > cont_inv_processed:
            cont_processed = cont_rec_processed
        else:
            cont_processed = cont_inv_processed
        
        return { "cont_processed": cont_processed , "entry_nums": entry_nums }

    def get_success_url(self):

        return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class EntryNewInvoicesView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        invoice_id = request.GET.get("invoice_id") if request.GET.get("invoice_id") else None
        limit = request.GET.get('limit') if request.GET.get('limit') else 1000
        yeargen = request.GET.get('yeargen') if request.GET.get('yeargen') else 'all'
        processed = self.generate_entries(seller, limit, invoice_id, yeargen)
        print("processed: "+ str(processed))
        return redirect(self.get_success_url())

    def generate_entries(self, seller, limit, invoice_id=None, yeargen='all'):
        # print("generate_entries")

        # print("GENERA ASIENTOS DE FACTURAS del seller id: "+ str(seller.id))
        invoices=Invoice.objects.filter(
            # Q(accounting_date__month__lte=6) &
            Q(seller=seller) &
            Q (status="revised")
        ).exclude(
            Q( used_in_entry= True) |
            Q(transaction_type="inbound-transfer") |
            Q(transaction_type="outgoing-transfer") | 
            Q(invoice_category__code__icontains='_copy')
        )
        if (yeargen != 'all' and yeargen != '' and yeargen != None and yeargen.isnumeric()):
            invoices = invoices.filter(accounting_date__year=yeargen)
        invoices = invoices.order_by("invoice_date", "reference")
        
        if invoice_id is not None:
            invoices = invoices.filter(id=invoice_id)
            limit = invoices.count()
        # print("Total facturas:"+ str(invoices.count()))

        cont_processed = 0
        entry_nums = []
        used = False
        for invoice in invoices:
            # print("factura: "+ str(invoice.id))
            # Realizar cálculos y crear asientos contables
            used = False

            # FIX DEV 
            customer_account=0
            if invoice.customer:
                customer_account=invoice.customer.customer_accounting_account
            customer_name="- No tiene cliente -"
            if invoice.customer:
                customer_name=invoice.customer.name

            provider_account = 0
            provider_account_description = "- No tiene proveedor -"
            provider_name = "- No tiene proveedor -"
            if invoice.provider:
                provider_account=invoice.provider.provider_accounting_account
                provider_account_description = f"Cuenta Proveedor {invoice.provider.name}"
                provider_name = invoice.provider.name
            elif invoice.invoice_type_id == "payroll":
                provider_account = "*********"
                provider_account_description = "Cuenta de Remuneraciones Pendientes de Pago"
                provider_name = "Remuneraciones Pendientes de Pago"
                

            account_number=AccountingAccount.objects.filter(code="*********").first()
            if invoice.account_number:
                account_number=invoice.account_number.code

            account_expenses=AccountingAccount.objects.filter(code="*********").first()
            if invoice.account_expenses:
                ac_ex = AccountingAccount.objects.filter(code=invoice.account_expenses.code+"000000").first()
                if (ac_ex is not None):
                    account_expenses = ac_ex

            account_sales=AccountingAccount.objects.get(code="*********")
            if invoice.account_sales:
                ac_sa = AccountingAccount.objects.filter(code=invoice.account_sales.code+"000000").first()
                if (ac_sa is not None):
                    account_sales = ac_sa


            if invoice.invoice_category is None:
                invoicecategory=InvoiceCategory.objects.get(code="expenses")
                invoice.invoice_category=invoicecategory
                invoice.save()

            if invoice.tax_country is None:
                invoice.tax_country=Country.objects.get(iso_code="ES")
                invoice.save()

            # FIN FIX DEV

            # si existe la cuenta contable para el pais se asigna y si no va a la general
            cuenta_contable_general=AccountingAccount.objects.get(code="*********")
            cuenta_especifica=AccountingAccount.objects.filter(code__startswith="470",country=invoice.tax_country.iso_code).first()
            if cuenta_especifica is not None:
                cuenta_contable_general=cuenta_especifica

            invoice_total_euros=0
            invoice_amount_euros=0
            invoice_iva_euros=0
            invoice_irpf_euros=0
            invoice_supplies_euros=0
            concepts=Concept.objects.filter(invoice=invoice)
            if concepts.count() > 0:
                for concept in concepts:
                    qty = concept.quantity if concept.quantity else 1
                    if concept.is_supplied and concept.is_supplied == True:
                        invoice_total_euros += concept.amount_euros * qty
                        invoice_supplies_euros += concept.amount_euros * qty
                    else:
                        invoice_total_euros += concept.amount_euros  * qty
                        invoice_total_euros += concept.vat_euros * qty
                        if concept.irpf_euros:
                            invoice_total_euros -= concept.irpf_euros * qty

                        invoice_amount_euros += concept.amount_euros * qty
                        invoice_iva_euros += concept.vat_euros * qty
                        invoice_irpf_euros += concept.irpf_euros * qty
            else:
                invoice_total_euros=invoice.total_euros
                invoice_amount_euros=invoice.total_amount_euros
                invoice_iva_euros=invoice.total_vat_euros
                invoice_irpf_euros=invoice.total_irpf_euros

            # GET last_entry and entry_num
            num = 1
            last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
            if last_entry is not None and last_entry.entry_num is not None:
                num = int(last_entry.entry_num) + 1
            
            # caso 5
            if invoice.is_oss and invoice.invoice_category.code=="sales":
                # print("caso 5")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_total_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                entry_date = None
                entry_accounting_account=0
                entry_accounting_account_description="- No tiene cliente -"
                entry_concept = "- No tiene factura -"
                entry_document = "- No tiene factura -"
                if invoice:
                    if  invoice.reference:
                        entry_concept=f"Nuestra factura número {invoice.reference}"
                        entry_document=invoice.reference
                    if invoice.customer:
                        if invoice.customer.customer_accounting_account:
                            entry_accounting_account=invoice.customer.customer_accounting_account
                            entry_accounting_account_description = f"Cuenta Cliente {customer_name}"
                    if invoice.accounting_date:
                        entry_date=invoice.accounting_date

                entry_concept = entry_concept[:150] if entry_concept else ""

                # num=1                
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1


                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=entry_document,
                    entry_date=entry_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=entry_accounting_account,
                    entry_accounting_account_description=entry_accounting_account_description,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                # print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                entry_date = None
                entry_accounting_account="*********"
                entry_accounting_account_description="Ventas OSS (incluido ES)"
                entry_concept = account_sales.description + " a "+ customer_name
                entry_concept = entry_concept[:150] if entry_concept else ""
                entry_document = "- No tiene factura -"
                if invoice:
                    if  invoice.reference:
                        entry_document=invoice.reference
                    if invoice.accounting_date:
                        entry_date=invoice.accounting_date
                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=entry_document,
                    entry_date=entry_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=entry_accounting_account,
                    entry_accounting_account_description=entry_accounting_account_description,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                # print("Asiento 2: "+ str(entry.id))
                invoice.used_in_entry = True
                invoice.save()
                used = True

                if invoice_iva_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_iva_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_date = None
                    entry_accounting_account="*********"
                    entry_accounting_account_description="Hacienda Pública (OSS)"
                    entry_concept = "IVA OSS "+ invoice.tax_country.iso_code +" repercutido a "+ customer_name
                    entry_concept = entry_concept[:150] if entry_concept else ""
                    entry_document = "- No tiene factura -"
                    if invoice:
                        if  invoice.reference:
                            entry_document=invoice.reference
                        if invoice.accounting_date:
                            entry_date=invoice.accounting_date

                    # num=1
                    # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                    # if last_entry is not None and last_entry.entry_num is not None:
                    #     num = last_entry.entry_num
                    # else:
                    #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    #     if last_entry is not None and last_entry.entry_num is not None:
                    #         num = int(last_entry.entry_num) + 1
                                
                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=entry_document,
                        entry_date=entry_date,
                        entry_concept=entry_concept,
                        entry_accounting_account=entry_accounting_account,
                        entry_accounting_account_description=entry_accounting_account_description,
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry,
                    )
                    # print("Asiento 3 (IVA): "+ str(entry.id))

                if invoice_supplies_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_supplies_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_concept = "Suplido"
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Suplidos",
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry,
                    )

            # caso 1
            elif invoice.tax_country.iso_code == "ES" and invoice.invoice_category.code=="expenses":
                # print("caso 1")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_total_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                entry_date = None
                entry_accounting_account=0
                entry_accounting_account_description="- No tiene cliente -"
                entry_concept = "- No tiene factura -"
                entry_document = "- No tiene factura -"
                if invoice:
                    if  invoice.reference:
                        entry_concept=f"Su factura número {invoice.reference}"
                        entry_document=invoice.reference
                    if provider_account:
                        entry_accounting_account=provider_account
                    if invoice.accounting_date:
                        entry_date=invoice.accounting_date
                entry_concept = entry_concept[:150] if entry_concept else ""

                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=entry_document,
                    entry_date=entry_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=provider_account,
                    entry_accounting_account_description=provider_account_description,
                    entry_debit=total_entry_negativa, 
                    entry_credit=total_entry,
                )
                # print("Asiento 1: "+ str(entry.id))
                if invoice_irpf_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_irpf_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    # num=1
                    # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                    # if last_entry is not None and last_entry.entry_num is not None:
                    #     num = last_entry.entry_num
                    # else:
                    #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    #     if last_entry is not None and last_entry.entry_num is not None:
                    #         num = int(last_entry.entry_num) + 1
                    entry_concept = "Retención practicada de "+ provider_name
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Hacienda Pública, acreedora por retenciones practicadas",
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry ,
                    )
                    # print("Asiento 4 (IRPF): "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry_concept = account_expenses.description+ " a "+ provider_name
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=account_expenses.code,
                    entry_accounting_account_description=account_expenses.description,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                # print("Asiento 2: "+ str(entry.id))
                invoice.used_in_entry = True
                invoice.save()
                used = True

                if invoice_iva_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_iva_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_concept = "IVA soportado de "+ provider_name
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Hacienda Pública IVA soportado",
                        entry_debit=total_entry ,
                        entry_credit=total_entry_negativa,
                    )
                
                if invoice_supplies_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_supplies_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_concept = "Suplido"
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Suplidos",
                        entry_debit=total_entry ,
                        entry_credit=total_entry_negativa,
                    )

            # caso 2
            elif invoice.tax_country.iso_code != "ES" and invoice.invoice_category.code=="expenses":
                # print("caso 2")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_total_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry_concept = "Su factura número "+ invoice.reference
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=provider_account,
                    entry_accounting_account_description=provider_account_description,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                # print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                
                entry_concept = account_expenses.description+ " a "+ provider_name
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=account_expenses.code,
                    entry_accounting_account_description=account_expenses.description,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                # print("Asiento 2: "+ str(entry.id))
                invoice.used_in_entry = True
                invoice.save()
                used = True

                if invoice_iva_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_iva_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0
                    # num=1
                    # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                    # if last_entry is not None and last_entry.entry_num is not None:
                    #     num = last_entry.entry_num
                    # else:
                    #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    #     if last_entry is not None and last_entry.entry_num is not None:
                    #         num = int(last_entry.entry_num) + 1

                    entry_concept = "IVA soportado de "+ provider_name
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account=cuenta_contable_general.code,
                        # entry_accounting_account_description="Hacienda Pública IVA soportado",
                        entry_accounting_account_description=cuenta_contable_general.description,
                        # entry_debit=0,
                        # entry_credit=round(invoice_iva_euros, 2),
                        entry_debit=total_entry,
                        entry_credit=total_entry_negativa,
                    )
                    # print("Asiento 3 (IVA): "+ str(entry.id))
            
                if invoice_supplies_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_supplies_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_concept = "Suplido"
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Suplidos",
                        entry_debit=total_entry ,
                        entry_credit=total_entry_negativa,
                    )
            
            # caso 3
            elif invoice.tax_country.iso_code == "ES" and invoice.invoice_category.code=="sales":
                # print("caso 3")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_total_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry_concept = "Nuestra factura número "+ invoice.reference
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=customer_account,
                    entry_accounting_account_description="Cuenta Cliente "+ customer_name,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                # print("Asiento 1: "+ str(entry.id))

                if invoice_irpf_euros != 0:
                    if concepts.count() > 0:

                        # si hay varios conceptos se calcula el total de irpf y se crea un asiento por cada pordentaje de irpf diferente
                        irpf_porcentajes=Concept.objects.filter(invoice=invoice).values('irpf').distinct()
                        for irpf_porcentaje in irpf_porcentajes:
                            if str(irpf_porcentaje['irpf']) != '0' and str(irpf_porcentaje['irpf']) != '0.0':
                                print("irpf_porcentaje: "+ str(irpf_porcentaje['irpf']))
                                invoice_irpf_euros=0
                                for concept in concepts:
                                    if concept.irpf == irpf_porcentaje['irpf']:
                                        invoice_irpf_euros += concept.irpf_euros

                                irpf_percentage_formatted = str(irpf_porcentaje['irpf']).rstrip('0').rstrip('.')
                                
                                cuenta_contable_retencion = "47300" + irpf_percentage_formatted.zfill(2).ljust(4, "0")


                                entry_description = f"Retenciones Y Pagos A Cuenta ({irpf_percentage_formatted}%)" 
                                # si el total es negativo, se invierte entre las columnas de débito y crédito
                                total_entry_negativa=0
                                total_entry=round(invoice_irpf_euros, 2)
                                if total_entry < 0:
                                    total_entry_negativa=total_entry*-1
                                    total_entry=0

                                # num=1
                                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                                # if last_entry is not None and last_entry.entry_num is not None:
                                #     num = last_entry.entry_num
                                # else:
                                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                                #     if last_entry is not None and last_entry.entry_num is not None:
                                #         num = int(last_entry.entry_num) + 1

                                entry_concept = f"Retención practicada de {customer_name}"
                                entry_concept = entry_concept[:150] if entry_concept else ""

                                entry = Entry.objects.create(
                                    entry_num=num,
                                    entry_seller=seller,
                                    entry_invoice=invoice,
                                    entry_document=invoice.reference,
                                    entry_date=invoice.invoice_date,
                                    entry_concept=entry_concept,
                                    entry_accounting_account=cuenta_contable_retencion,
                                    entry_accounting_account_description=entry_description,
                                    entry_debit=total_entry,
                                    entry_credit=total_entry_negativa,
                                )
                                # print("Asiento 4 (IRPF): " + str(entry.id))

                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry_concept = account_sales.description+ " a "+ customer_name
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=account_sales.code,
                    entry_accounting_account_description=account_sales.description,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                # print("Asiento 2: "+ str(entry.id))
                invoice.used_in_entry = True
                invoice.save()
                used = True

                if invoice_iva_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_iva_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    # num=1
                    # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                    # if last_entry is not None and last_entry.entry_num is not None:
                    #     num = last_entry.entry_num
                    # else:
                    #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    #     if last_entry is not None and last_entry.entry_num is not None:
                    #         num = int(last_entry.entry_num) + 1

                    entry_concept = "IVA repercutido a "+ customer_name
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Hacienda Pública IVA repercutido",
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry,
                    )
                    # print("Asiento 3 (IVA): "+ str(entry.id))

                if invoice_supplies_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_supplies_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_concept = "Suplido"
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Suplidos",
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry,
                    )

            # caso 4
            elif invoice.tax_country.iso_code != "ES" and invoice.invoice_category.code=="sales":
                # print("caso 4")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_total_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry_concept = "Nuestra factura número "+ invoice.reference
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=customer_account,
                    entry_accounting_account_description="Cuenta Cliente "+ customer_name,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                # print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                # num=1
                # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                # if last_entry is not None and last_entry.entry_num is not None:
                #     num = last_entry.entry_num
                # else:
                #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                #     if last_entry is not None and last_entry.entry_num is not None:
                #         num = int(last_entry.entry_num) + 1

                entry_concept = account_sales.description+ " a "+ customer_name
                entry_concept = entry_concept[:150] if entry_concept else ""

                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept=entry_concept,
                    entry_accounting_account=account_sales.code,
                    entry_accounting_account_description=account_sales.description,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                # print("Asiento 2: "+ str(entry.id))
                invoice.used_in_entry = True
                invoice.save()
                used = True

                if invoice_iva_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_iva_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    # num=1
                    # last_entry=Entry.objects.filter(entry_invoice=invoice,entry_seller=seller).order_by("-entry_num").first()
                    # if last_entry is not None and last_entry.entry_num is not None:
                    #     num = last_entry.entry_num
                    # else:
                    #     last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    #     if last_entry is not None and last_entry.entry_num is not None:
                    #         num = int(last_entry.entry_num) + 1

                    entry_concept = "IVA repercutido a "+ customer_name
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account=cuenta_contable_general.code,
                        # entry_accounting_account_description="Hacienda Pública IVA repercutido",
                        entry_accounting_account_description=cuenta_contable_general.description,
                        # entry_debit=round(invoice_iva_euros, 2),
                        # entry_credit=0,
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry,
                    )
                    # print("Asiento 3 (IVA): "+ str(entry.id))

                if invoice_supplies_euros != 0:
                    # si el total es negativo, se invierte entre las columnas de débito y crédito
                    total_entry_negativa=0
                    total_entry=round(invoice_supplies_euros, 2)
                    if total_entry < 0:
                        total_entry_negativa=total_entry*-1
                        total_entry=0

                    entry_concept = "Suplido"
                    entry_concept = entry_concept[:150] if entry_concept else ""

                    entry=Entry.objects.create(
                        entry_num=num,
                        entry_seller=seller,
                        entry_invoice=invoice,
                        entry_document=invoice.reference,
                        entry_date=invoice.accounting_date,
                        entry_concept=entry_concept,
                        entry_accounting_account="*********",
                        entry_accounting_account_description="Suplidos",
                        entry_debit=total_entry_negativa,
                        entry_credit=total_entry,
                    )
            
            if used == True:
                # Entry List
                if num not in entry_nums:
                    entry_nums.append(num)

                cont_processed += 1
                if cont_processed >= limit:
                    break

        return { "cont_processed": cont_processed , "entry_nums": entry_nums }
    
    def get_success_url(self):

        return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class EntryNewAllView (LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    
    def get(self, request, *args, **kwargs):
        
        start_time = time.time()
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])

        limit = self.kwargs["limit"]
        print(f"Limit: {limit}")

        yeargen = self.kwargs["yeargen"]
        print(f"YearGen: {yeargen}")

        gen_entries = self.generate_entries(seller, limit, yeargen)
        print(f"Gen Entries: {gen_entries}")

        end_time = time.time()  # End timer
        execution_time = end_time - start_time
        print(f"GenerateEntryView Execution Time: {execution_time:.4f} seconds")

        return redirect(self.get_success_url())
        # return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])

    def generate_entries(self, seller, limit, yeargen):
        result = { 'limit': limit, 'total_generated': 0, 'invoices_pending_qty': None }
        total_generated = 0
        
        print("--------------------EntryNewAllView----------------------")
        print("GENERA ASIENTOS con funcion asincrona del seller id: "+ str(seller.id))

        try:
            print("---------------------------------------------------------------------------------------- limit: "+ str(limit)) 
            
            processed_reconci = 0
            processed_reconci_amz = 0
            processed_inv = 0
            entry_nums = []
            if total_generated < limit:
                ENR = EntryNewReconciliationView().generate_entries_reconciliation(seller,limit,yeargen)
                entry_nums += ENR.get("entry_nums")
                processed_reconci = ENR.get("cont_processed")
                print(f"processed_reconci: {processed_reconci}")
                total_generated += processed_reconci
                newlimit = limit - total_generated
                if total_generated < limit:
                    ENRA = EntryNewReconciliationAmzView().generate_entries_reconciliation_amz(seller,newlimit,yeargen)
                    entry_nums += ENRA.get("entry_nums")
                    processed_reconci_amz = ENRA.get("cont_processed")
                    print(f"processed_reconci_amz: {processed_reconci_amz}")
                    total_generated += processed_reconci_amz
                    newlimit = limit - total_generated
                    if total_generated < limit:
                        ENI = EntryNewInvoicesView().generate_entries(seller, newlimit, None, yeargen)
                        entry_nums += ENI.get("entry_nums")
                        processed_inv = ENI.get("cont_processed")
                        print(f"processed_inv: {processed_inv}")
                        total_generated += processed_inv
                        newlimit = limit - total_generated

            print("Total processed_reconci: "+ str(processed_reconci))
            print("Total processed_reconci_amz: "+ str(processed_reconci_amz))
            print("Total processed_inv: "+ str(processed_inv))
            print(f"Entry Nums: {entry_nums}")

            for num in entry_nums:
                entri = Entry.objects.filter(entry_num=num, entry_seller=seller).first()
                if entri:
                    entri.is_entry_balanced()

            
            invoices_pending_qty=Invoice.objects.filter(
                Q(seller=seller) &
                Q (status="revised")
            ).exclude(
                Q( used_in_entry= True) |
                Q(transaction_type="inbound-transfer") |
                Q(transaction_type="outgoing-transfer") | 
                Q(invoice_category__code__icontains='_copy')
            )
            # if (yeargen != 'all' and yeargen != '' and yeargen != None and yeargen.isnumeric()):
            #     invoices_pending_qty = invoices_pending_qty.filter(
            #         accounting_date__year = yeargen
            #     )
            invoices_pending_qty = invoices_pending_qty.count()

            if invoices_pending_qty > 0:
                print(f"Total Generated: {str(total_generated)} | Limit: {str(limit)} | {str(invoices_pending_qty)} Invoices Pending...")
                result = { 'limit': limit, 'total_generated': total_generated, 'invoices_pending_qty': invoices_pending_qty }
            else:
                print(f"Total Generated: {str(total_generated)} | Limit: {str(limit)} | All Processed")
                result = { 'limit': limit, 'total_generated': total_generated, 'invoices_pending_qty': 0 }

        except Exception as e:

            print("Error al ejecutar funcion asincrona Asientos Contables:", e)
            
            print("Error en la línea: " +traceback.format_exc().splitlines()[-2])

        return result

    def get_success_url(self):
        return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class GetEntrysDT(LoginRequiredMixin, IsSellerShortnamePermission, BaseDatatableView):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    model = Entry

    columns = [
        "is_entry_balanced_result",
        "pk",
        "entry_seller.pk",
        "entry_date",
        "entry_document",
        "entry_concept",
        "entry_accounting_account",
        "entry_reconciliation.pk",
        "entry_invoice.pk",
        "entry_accounting_account_description",
        "entry_debit",
        "entry_credit",
        "used_in_export",
        "created_at",
        "modified_at",
    ]

    order_columns = columns

    max_display_length = 9999999

    def filter_queryset(self, qs):
        # print("GetEntrysDT filter_queryset")
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        qs = qs.filter(entry_seller=seller)
        

        # Exported
        used_in_export = self.request.GET.get('used_in_export')
        if used_in_export is not None:
            if used_in_export == 'true':
                qs = qs.filter(used_in_export=True)
            elif used_in_export == 'false':
                qs = qs.filter(used_in_export=False)

        # Balanced
        is_entry_balanced = self.request.GET.get('is_entry_balanced')
        if is_entry_balanced is not None:
            if is_entry_balanced == 'true':
                qs = qs.filter(is_entry_balanced_result=True)
            elif is_entry_balanced == 'false':
                qs = qs.filter(is_entry_balanced_result=False)

        # entry_date
        entry_year = self.request.GET.get('year')
        entry_period = self.request.GET.get('period')
        if entry_year and entry_year.isnumeric():
            if entry_period:
                year = int(entry_year)
                month = int(entry_period)
                if month >= 1 and month <=12:
                    qs = qs.filter(
                        entry_date__isnull = False,
                        entry_date__year = entry_year,
                        entry_date__month = entry_period
                    )
                if month <= -1 and month >= -4:
                    quarter = month * -1
                    if quarter == 1:
                        qrange = [str(year) + "-01-01", str(year) + "-03-31"]
                    if quarter == 2:
                        qrange = [str(year) + "-04-01", str(year) + "-06-30"]
                    if quarter == 3:
                        qrange = [str(year) + "-07-01", str(year) + "-09-30"]
                    if quarter == 4:
                        qrange = [str(year) + "-10-01", str(year) + "-12-31"]
                    if qrange:
                        qs = qs.filter(
                            entry_date__isnull = False,
                            entry_date__range =qrange
                        )
            else:
                qs = qs.filter(
                    entry_date__isnull = False,
                    entry_date__year = entry_year
                )


        # Multiple Search
        search = self.request.GET.get('search')
        if search:
            qs = qs.filter(
                Q(entry_document__icontains = search) |
                Q(entry_concept__icontains = search) |
                Q(entry_accounting_account__icontains = search) |
                Q(entry_accounting_account_description__icontains = search) |
                Q(entry_debit__icontains = search) |
                Q(entry_credit__icontains = search)
            )

        order_by = []
        orderJson = self.request.GET.get('order')
        if orderJson:
            orderArray = json.loads(orderJson)
            if orderArray and len(orderArray) > 0:
                for order in orderArray:
                    if order['name'] and order['dir']:
                        if order['dir'] == 'asc':
                            order_by.append(str(order['name']))
                        elif order['dir'] == 'desc':
                            order_by.append('-' + str(order['name']))
                if (len(order_by) > 0):
                    qs = qs.order_by(*order_by)

       
          
        start=0
        length=25
        start=self.request.GET.get('start')
        length=self.request.GET.get('length')
        
        # recorrer el bucle empezando en start y terminando en start+length
        # for entri in qs [int(start):int(start)+int(length)]:
        #     entri.is_entry_balanced()

        return qs
    

    def handle_no_permission(self):
        response_data = {'error': 'No tienes permiso para realizar la peticion.'}
        return JsonResponse(response_data, status=403)

class EntryFileList(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = EntryFile
    template_name = "banks/entry_file_list.html"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        queryset = EntryFile.objects.filter(seller=seller)

        return queryset

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)     
        context["seller"] = seller
        return context

    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))
    
class EntryFileDeleteView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    model = EntryFile
    template_name = "banks/entry_file_delete.html"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        entry_file = get_object_or_404(EntryFile, pk=self.kwargs["pk"])
        entries_related = Entry.objects.filter(entry_seller=seller, entry_export=entry_file)
        for entry in entries_related:
            entry.used_in_export = False
            entry.entry_export = None
            entry.save()
        entry_file.delete()
        return redirect(self.get_success_url())

    def get_success_url(self):
        return reverse("app_banks:entry_filezip_list", args=[self.kwargs["shortname"]])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class GenerateEntryView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def get(self, request, *args, **kwargs):
        start_time = time.time()
        
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        yeargen = self.kwargs["year"]
        max_entries = int(request.GET.get("max_entries"))

        self.generate_entries(seller, yeargen, max_entries)

        end_time = time.time()  # End timer
        execution_time = end_time - start_time
        print(f"GenerateEntryView Execution Time: {execution_time:.4f} seconds")

        return redirect(self.get_success_url())
    
    def generate_entries(self, seller, yeargen, max_entries: int):
        entry_generator = EntryController(seller=seller, year_generator=yeargen, max_entries=max_entries)
        entry_generator.process_all_entries()

    def get_success_url(self):
        return reverse("app_banks:entry_list", args=[self.kwargs["shortname"]])

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))