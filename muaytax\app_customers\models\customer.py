from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.urls import reverse
from muaytax.signals import disable_for_load_data


class Customer(models.Model):
    # id -> AutoGen
    name = models.CharField(
        max_length=100,
        verbose_name="Nombre",
        help_text="Nombre"
    )

    nif_cif_iva = models.CharField(
        blank=True, null=True,
        max_length=50,
        verbose_name="NIF/CIF/IVA",
        help_text="NIF/CIF/IVA (si disponemos de él)",
    )

    zip = models.CharField(
        max_length=50,
        verbose_name="Código Postal",
        blank=True, null=True,
    )

    customer_address = models.ForeignKey(
        "address.Address",
        related_name="customer_address",
        on_delete=models.PROTECT,
        verbose_name="Dirección del Cliente",
        blank=True, null=True,
    )

    country = models.ForeignKey(
        "dictionaries.Country",
        related_name="customer_country",
        on_delete=models.PROTECT,
        verbose_name="País",
        help_text="País",
        blank=True, null=True,
    )

    vies = models.CharField(
        max_length=50,
        verbose_name="VIES",
        help_text="VIES: Sistema de Intercambio de Información sobre el IVA",
        blank=True, null=True,
    )

    customer_type = models.ForeignKey(
        "dictionaries.CustomerType",
        on_delete=models.PROTECT,
        verbose_name="Tipo de cliente",
        help_text="Tipo de cliente",
        blank=True, null=True,
    )

    customer_number = models.CharField(
        max_length=6,
        verbose_name="Número de cliente",
        help_text="Número de cliente",
        blank=True, null=True,
    )

    account_sales = models.ForeignKey(
        "dictionaries.AccountSales",
        on_delete=models.PROTECT,
        verbose_name="Cuenta de ingreso por defecto",
        help_text="Cuenta de ingreso por defecto",
        blank=True, null=True,
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="customer_seller",
        verbose_name="Vendedor",
    )

    @property
    def customer_accounting_account(self):
        if self.customer_number:
            return f'430{self.customer_number}'
        else:
            return None

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Cliente"
        verbose_name_plural = "Clientes"
        constraints = [
            models.UniqueConstraint(fields=['seller', 'nif_cif_iva'], name='customer_unique_seller_nif_cif_iva'),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse("app_providers:detail", kwargs={"pk": self.pk})

    # set customer_number to +1 of the last customer_number when creating a new customer
    def save(self, *args, **kwargs):
        if self.customer_number is None:
            last_customer = Customer.objects.filter(seller=self.seller, customer_number__isnull=False).order_by(
                '-customer_number').first()
            if last_customer is None:
                self.customer_number = "000001"
            else:
                next_number = int(last_customer.customer_number) + 1
                self.customer_number = f"{next_number:06}"
        super(Customer, self).save(*args, **kwargs)

    # def clean(self):
    #     if self.customer_number is not None:
    #         if Customer.objects.filter(seller=self.seller, customer_number=self.customer_number).exists():
    #             raise ValidationError("Ya existe un cliente con este número para este vendedor")


# @receiver(post_save, sender=Customer)
# @disable_for_load_data
# def after_sellervat_save(sender, instance, created, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)

# @receiver(post_delete, sender=Customer)
# @disable_for_load_data
# def after_sellervat_delete(sender, instance, **kwargs):
#     seller = instance.seller
#     if seller is not None and seller.pk is not None:
#         from muaytax.app_lists.utils import update_and_create_seller_cached_lists
#         update_and_create_seller_cached_lists(seller)