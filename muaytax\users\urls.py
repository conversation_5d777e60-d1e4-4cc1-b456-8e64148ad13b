from django.urls import path, re_path
from muaytax.users import views
from muaytax.users.api import serializers

# from muaytax.users.api.serializers import MyOpenTrackingView
#from muaytax.users.api.views import CreateUserViewSet
#from muaytax.users.api.views import MyObtainTokenPairView, RegisterView



app_name = "users"
urlpatterns = [

    # path(
    #     "test-emails/<plataform>/<product>/",
    #     serializers.TestMail.as_view(),
    #     name="email_testing",
    # ),

    path(
        "dash/<country>/<iae>/<year>/<month>/",
        view=views.UserDashFilter.as_view(),
        name="user_dash_filter",
    ),
    path(
        "old-dash/<country>/<iae>/<year>/<month>/",
        view=views.UserDashFilterOld.as_view(),
        name="user_dash_filter_old"
    ),

    path("users/~redirect/", view=views.UserRedirectView.as_view(), name="redirect"),
    path("users/profile/", view=views.UserRedirectView.as_view(), name="profile"),
    path("users/~update/", view=views.UserUpdateView.as_view(), name="update"),
    path("users/<str:username>/", view=views.UserProfileView.as_view(), name="profile"),
    
    re_path(r"^open-tracking/mail/(?P<user>[0-9]+)/(?P<process>[0-9]+)", views.PixelView.as_view(), name="pixel_view"),
    re_path(r"^open-tracking/welcome/(?P<user>[0-9]+)/(?P<process>[0-9]+)", views.WelcomePixelView.as_view(), name="welcome_pixel_view"),
    re_path(r"^open-tracking/welcome/(?P<user>[0-9]+)/", views.WelcomePixelView.as_view(), name="welcome_pixel_view"),
]
