import json
import os
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse
from django.shortcuts import get_object_or_404, redirect
from django.http import HttpResponseRedirect, HttpResponse , HttpResponseBadRequest
from django.utils.translation import gettext_lazy as _
from django.core.serializers import serialize
from django.views.generic import CreateView, DeleteView, ListView, UpdateView, View
from muaytax.app_banks.models.bank import Bank
from muaytax.app_banks.models.movement import Movement
from muaytax.app_banks.models.reconciliation import Reconciliation
from muaytax.app_customers.models.customer import Customer
from muaytax.app_invoices.models.concept import Concept
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.dictionaries.models.invoice_category import InvoiceCategory
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType
from muaytax.dictionaries.models.movement_status import MovementStatus
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.countries import Country

from django.db.models import Q
from django.db import connection
from muaytax.dictionaries.models.currencies import Currency
from muaytax.dictionaries.models.bank_type import BankType

from muaytax.users.permissions import IsSellerShortnamePermission

from muaytax.app_banks.models.entry import Entry
from muaytax.app_sellers.models.seller import Seller

import pandas as pd

from django.utils import timezone


def generate_entry(seller_id):
    print("Generando asientos...")
    today = timezone.localtime(timezone.now()).date()
    print("today:" +str(today))
    print("seller_id:" +str(seller_id))

    seller = get_object_or_404(Seller, id=seller_id)

    invoices=Invoice.objects.filter(seller=seller, used_in_entry=False, status="revised").exclude(Q(transaction_type="inbound-transfer") | Q(transaction_type="outgoing-transfer") | Q(invoice_category__code__icontains='_copy')
).order_by("invoice_date", "reference")
    print("Total facturas:"+ str(invoices.count()))
    for invoice in invoices:
        print("factura: "+ str(invoice.id))
        # Realizar cálculos y crear asientos contables

        # FIX DEV 
        customer_account="No tiene cliente"
        if invoice.customer:
            customer_account=invoice.customer.customer_accounting_account
        customer_name="No tiene cliente"
        if invoice.customer:
            customer_name=invoice.customer.name

        provider_account="No tiene proveedor"
        if invoice.provider:
            provider_account=invoice.provider.provider_accounting_account
        provider_name="No tiene proveedor"
        if invoice.provider:
            provider_name=invoice.provider.name

        # account_number=AccountingAccount.objects.get(code="*********")
        # if invoice.account_number:
        #     account_number=AccountingAccount.objects.get(code=invoice.account_number.code+"000000")

        account_expenses=AccountingAccount.objects.get(code="*********")
        if invoice.account_expenses:
            account_expenses=AccountingAccount.objects.get(code=invoice.account_expenses.code+"000000")

        account_sales=AccountingAccount.objects.get(code="*********")
        if invoice.account_sales:
            account_sales=AccountingAccount.objects.get(code=invoice.account_sales.code+"000000")

        if invoice.invoice_category is None:
            invoicecategory=InvoiceCategory.objects.get(code="expenses")
            invoice.invoice_category=invoicecategory
            invoice.save()

        if invoice.tax_country is None:
            invoice.tax_country=Country.objects.get(iso_code="ES")
            invoice.save()

        # FIN FIX DEV

        # si existe la cuenta contable para el pais se asigna y si no va a la general
        cuenta_contable_general=AccountingAccount.objects.get(code="*********")
        cuenta_especifica=AccountingAccount.objects.filter(code__startswith="470",country=invoice.tax_country.iso_code).first()
        if cuenta_especifica is not None:
            cuenta_contable_general=cuenta_especifica

        invoice_total_euros=0
        invoice_amount_euros=0
        invoice_iva_euros=0
        invoice_irpf_euros=0
        concepts=Concept.objects.filter(invoice=invoice)
        if concepts.count() > 0:
            for concept in concepts:
                invoice_total_euros += concept.total_euros
                invoice_amount_euros += concept.amount_euros
                invoice_iva_euros += concept.vat_euros
                invoice_irpf_euros += concept.irpf_euros
        else:
            invoice_total_euros=invoice.total_euros
            invoice_amount_euros=invoice.total_amount_euros
            invoice_iva_euros=invoice.total_vat_euros
            invoice_irpf_euros=invoice.total_irpf_euros


        # caso 5
        if invoice.is_oss and invoice.invoice_category.code=="sales":
            print("caso 5")
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_total_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0

            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept="Nuestra factura número "+ invoice.reference,
                entry_accounting_account=invoice.customer.customer_accounting_account,
                entry_accounting_account_description="Cuenta Cliente "+ customer_name,
                entry_debit=total_entry,
                entry_credit=total_entry_negativa,
            )
            print("Asiento 1: "+ str(entry.id))
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_amount_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept=account_sales.description+ " a "+ invoice.reference,
                entry_accounting_account="********",
                entry_accounting_account_description="Ventas OSS (incluido ES)",
                entry_debit=total_entry_negativa,
                entry_credit=total_entry,
            )
            print("Asiento 2: "+ str(entry.id))
            invoice.used_in_entry = True
            invoice.save()

            if invoice_iva_euros > 0:
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_iva_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept="IVA OSS "+ invoice.tax_country.iso_code +" repercutido a "+ customer_name,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="Hacienda Pública (OSS)",
                    # entry_debit=round(invoice_iva_euros, 2),
                    # entry_credit=0,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 3 (IVA): "+ str(entry.id))

        # caso 1
        elif invoice.tax_country.iso_code == "ES" and invoice.invoice_category.code=="expenses":
            print("caso 1")
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_total_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept="Su factura número "+ invoice.reference,
                entry_accounting_account=provider_account,
                entry_accounting_account_description="Cuenta Proveedor "+provider_name,
                entry_debit=total_entry_negativa ,
                entry_credit=total_entry,
            )
            print("Asiento 1: "+ str(entry.id))
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_amount_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept=account_expenses.description+ " a "+ invoice.reference,
                entry_accounting_account=account_expenses.code,
                entry_accounting_account_description=account_expenses.description,
                entry_debit=total_entry,
                entry_credit=total_entry_negativa,
            )
            print("Asiento 2: "+ str(entry.id))
            invoice.used_in_entry = True
            invoice.save()

            if invoice_iva_euros > 0:
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_iva_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept="IVA soportado de "+ provider_name,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="Hacienda Pública IVA soportado",
                    entry_debit=total_entry ,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 3 (IVA): "+ str(entry.id))
            
            if invoice_irpf_euros > 0:
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_irpf_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept="Retención practicada de "+ provider_name,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="Hacienda Pública, acreedora por retenciones practicadas",
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry ,
                )
                print("Asiento 4 (IRPF): "+ str(entry.id))

        # caso 2
        elif invoice.tax_country.iso_code != "ES" and invoice.invoice_category.code=="expenses":
            print("caso 2")
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_total_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept="Su factura número "+ invoice.reference,
                entry_accounting_account=provider_account,
                entry_accounting_account_description="Cuenta Proveedor "+provider_name,
                entry_debit=total_entry_negativa,
                entry_credit=total_entry,
            )
            print("Asiento 1: "+ str(entry.id))
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_amount_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept=account_expenses.description+ " a "+ invoice.reference,
                entry_accounting_account=account_expenses.code,
                entry_accounting_account_description=account_expenses.description,
                entry_debit=total_entry,
                entry_credit=total_entry_negativa,
            )
            print("Asiento 2: "+ str(entry.id))
            invoice.used_in_entry = True
            invoice.save()

            if invoice_iva_euros > 0:
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_iva_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept="IVA soportado de "+ provider_name,
                    entry_accounting_account=cuenta_contable_general.code,
                    # entry_accounting_account_description="Hacienda Pública IVA soportado",
                    entry_accounting_account_description=cuenta_contable_general.description,
                    # entry_debit=0,
                    # entry_credit=round(invoice_iva_euros, 2),
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 3 (IVA): "+ str(entry.id))
        

        # caso 3
        elif invoice.tax_country.iso_code == "ES" and invoice.invoice_category.code=="sales":
            print("caso 3")
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_total_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept="Nuestra factura número "+ invoice.reference,
                entry_accounting_account=customer_account,
                entry_accounting_account_description="Cuenta Cliente "+ customer_name,
                entry_debit=total_entry,
                entry_credit=total_entry_negativa,
            )
            print("Asiento 1: "+ str(entry.id))

            if invoice_irpf_euros > 0:

                if concepts.count() > 0:

                    # si hay varios conceptos se calcula el total de irpf y se crea un asiento por cada pordentaje de irpf diferente
                    irpf_porcentajes=Concept.objects.filter(invoice=invoice).values('irpf').distinct()
                    for irpf_porcentaje in irpf_porcentajes:
                        print("irpf_porcentaje: "+ str(irpf_porcentaje['irpf']))
                        invoice_irpf_euros=0
                        for concept in concepts:
                            if concept.irpf == irpf_porcentaje['irpf']:
                                invoice_irpf_euros += concept.irpf_euros

                        irpf_percentage_formatted = str(irpf_porcentaje['irpf']).rstrip('0').rstrip('.')
                        
                        cuenta_contable_retencion = "47300" + irpf_percentage_formatted.zfill(2).ljust(4, "0")


                        entry_description = f"Retenciones Y Pagos A Cuenta ({irpf_percentage_formatted}%)"
                        # si el total es negativo, se invierte entre las columnas de débito y crédito
                        total_entry_negativa=0
                        total_entry=round(invoice_irpf_euros, 2)
                        if total_entry < 0:
                            total_entry_negativa=total_entry*-1
                            total_entry=0

                        entry = Entry.objects.create(
                            entry_seller=seller,
                            entry_invoice=invoice,
                            entry_document=invoice.reference,
                            entry_date=invoice.invoice_date,
                            entry_concept=f"Retención practicada de {customer_name}",
                            entry_accounting_account=cuenta_contable_retencion,
                            entry_accounting_account_description=entry_description,
                            entry_debit=total_entry,
                            entry_credit=total_entry_negativa,
                        )
                        print("Asiento 4 (IRPF): " + str(entry.id))
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_amount_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0            
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept=account_sales.description+ " a "+ invoice.reference,
                entry_accounting_account=account_sales.code,
                entry_accounting_account_description=account_sales.description,
                entry_debit=total_entry_negativa,
                entry_credit=total_entry,
            )
            print("Asiento 2: "+ str(entry.id))
            invoice.used_in_entry = True
            invoice.save()

            if invoice_iva_euros > 0:
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_iva_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0  
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept="IVA repercutido a "+ customer_name,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="Hacienda Pública IVA repercutido",
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 3 (IVA): "+ str(entry.id))
      


        # caso 4
        elif invoice.tax_country.iso_code != "ES" and invoice.invoice_category.code=="sales":
            print("caso 4")
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_total_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept="Nuestra factura número "+ invoice.reference,
                entry_accounting_account=customer_account,
                entry_accounting_account_description="Cuenta Cliente "+ customer_name,
                entry_debit=total_entry,
                entry_credit=total_entry_negativa,
            )
            print("Asiento 1: "+ str(entry.id))
            # si el total es negativo, se invierte entre las columnas de débito y crédito
            total_entry_negativa=0
            total_entry=round(invoice_amount_euros, 2)
            if total_entry < 0:
                total_entry_negativa=total_entry*-1
                total_entry=0
            entry=Entry.objects.create(
                entry_seller=seller,
                entry_invoice=invoice,
                entry_document=invoice.reference,
                entry_date=invoice.accounting_date,
                entry_concept=account_sales.description + " a "+ invoice.reference,
                entry_accounting_account=account_sales.code,
                entry_accounting_account_description=account_sales.description,
                entry_debit=total_entry_negativa,
                entry_credit=total_entry,
            )
            print("Asiento 2: "+ str(entry.id))
            invoice.used_in_entry = True
            invoice.save()

            if invoice_iva_euros > 0:
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(invoice_iva_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_invoice=invoice,
                    entry_document=invoice.reference,
                    entry_date=invoice.accounting_date,
                    entry_concept="IVA repercutido a "+ customer_name,
                    entry_accounting_account=cuenta_contable_general.code,
                    # entry_accounting_account_description="Hacienda Pública IVA repercutido",
                    entry_accounting_account_description=cuenta_contable_general.description,
                    # entry_debit=round(invoice_iva_euros, 2),
                    # entry_credit=0,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 3 (IVA): "+ str(entry.id))
        
       

  
    # caso 7
    #  excluir transfer de amazon 
    invoices_txt=Invoice.objects.filter(seller=seller, used_in_entry=False, is_txt_amz=True).exclude(Q(transaction_type="inbound-transfer") | Q(transaction_type="outgoing-transfer") | Q(invoice_category__code__icontains='_copy')
).order_by("accounting_date", "reference")
    print("Total facturas txt:"+ str(invoices_txt.count()))
    for invoice_txt in invoices_txt:
        print("factura txt: "+ str(invoice_txt.id))
        # Realizar cálculos y crear asientos contables
        total_invoice=0
        concepts=Concept.objects.filter(invoice=invoice_txt)
        if concepts.count() > 0:
            for concept in concepts:
                total_invoice += concept.total_euros
        else:
            total_invoice=invoice_txt.total_euros


        # si no tiene cliente se crea para ese seller, el cliente "Clientes Particulares" seguido del nombre del marketplace de la invoice,  por ej, "Clientes Particulares Amazon.es"
        nuevo_cliente=None
        cliente_exite=None
        client_name="Clientes Particulares"
        if invoice_txt.customer is None:
            if invoice_txt.marketplace is not None:
                client_name+=" "+invoice_txt.marketplace.description

            cliente_exite=Customer.objects.filter(name=client_name, seller=seller).first()
            if cliente_exite is not None:
                invoice_txt.customer=cliente_exite
                invoice_txt.save()
            else:   
                nuevo_cliente=Customer.objects.create(
                    name=client_name,
                    seller=seller,
                )
                invoice_txt.customer=nuevo_cliente
                invoice_txt.save()
        if cliente_exite is not None:
            print("cliente existe: "+ str(cliente_exite.name)+ " "+ str(cliente_exite.customer_accounting_account))
        if nuevo_cliente is not None:
            print("cliente nuevo: "+ str(nuevo_cliente.name)+ " "+ str(nuevo_cliente.customer_accounting_account))

        #  crea movimiento de banco AMAZON
        bank_amazon=Bank.objects.filter(bank_seller=seller, bank_name__iexact="AMAZON").first()
        if bank_amazon is None:
            bank_amazon=Bank.objects.create(
                bank_seller=seller,
                bank_name="AMAZON",
                bank_accounting_account="*********",
                bank_account_type=BankType.objects.get(code="bankaccount"),
            )
        print("banco amazon: "+ str(bank_amazon.id))
        movement_number_nuevo=str(invoice_txt.accounting_date.year)+"-"+str(invoice_txt.accounting_date.month).zfill(2)+"-000000"
        movement_month_actual=Movement.objects.filter(bank=bank_amazon, movement_date__month=invoice_txt.accounting_date.month, movement_date__year=invoice_txt.accounting_date.year).order_by("-movement_date").first()
        if movement_month_actual is not None:
            movement_number_nuevo=str(invoice_txt.accounting_date.year)+"-"+str(invoice_txt.accounting_date.month).zfill(2)+"-"+str(int(movement_month_actual.movement_number[-6:])+1).zfill(6)
            
        movement=Movement.objects.create(
            bank=bank_amazon,
            movement_date=invoice_txt.accounting_date,
            movement_number=movement_number_nuevo,
            concept="Cobro de la factura "+ invoice_txt.reference,
            amount_euros=total_invoice,
            status=MovementStatus.objects.get(code="conciliated"),
        )
        print("movimiento amazon: "+ str(movement.id))
        # concilia movimiento contra factura AMAZON
        reconciliation=Reconciliation.objects.create(
            movement=movement,
            bank=bank_amazon,
            type=ReconciliationType.objects.get(code="invoice"),
            amount=total_invoice,
            used_in_entry=True,
        )
        print("conciliacion amazon: "+ str(reconciliation.id))
                


        #  crea conciliacion contra cuenta AMAZON

        # si el total es negativo, se invierte entre las columnas de débito y crédito
        total_entry_negativa=0
        total_entry=round(total_invoice, 2)
        if total_entry < 0:
            total_entry_negativa=total_entry*-1
            total_entry=0
        entry=Entry.objects.create(
            entry_seller=seller,
            entry_invoice=invoice_txt,
            entry_document=invoice_txt.reference,
            entry_date=invoice_txt.accounting_date,
            entry_concept="Cobro de la factura "+ invoice_txt.reference,
            entry_accounting_account="*********",
            entry_accounting_account_description="AMAZON",
            entry_debit=total_entry,
            entry_credit=total_entry_negativa,
        )
        print("Asiento 1 (amazon): "+ str(entry.id))
        customer_account="No tiene cliente"
        if invoice_txt.customer:
            customer_account=invoice_txt.customer.customer_accounting_account
        customer_name="No tiene cliente"
        if invoice_txt.customer:
            customer_name=invoice_txt.customer.name
        # si el total es negativo, se invierte entre las columnas de débito y crédito
        total_entry_negativa=0
        total_entry=round(total_invoice, 2)
        if total_entry < 0:
            total_entry_negativa=total_entry*-1
            total_entry=0
        entry=Entry.objects.create(
            entry_seller=seller,
            entry_invoice=invoice_txt,
            entry_document=invoice_txt.reference,
            entry_date=invoice_txt.accounting_date,
            entry_concept="Cobro de la factura "+ invoice_txt.reference,
            entry_accounting_account=customer_account,
            entry_accounting_account_description="Cuenta del cliente "+ customer_name,
            entry_debit=total_entry_negativa,
            entry_credit=total_entry,
        )
        print("Asiento 2 (cliente): "+ str(entry.id))
        invoice_txt.used_in_entry = True
        invoice_txt.save()

       

    reconciliations = Reconciliation.objects.filter(movement__status="conciliated", movement__bank__bank_seller=seller, used_in_entry=False).order_by("movement__movement_date", "movement__movement_number")
    print("reconciliations:"+ str(reconciliations.count()))
    for reconciliation in reconciliations:
        print("conciliación: "+ str(reconciliation.id))
        # Realizar cálculos y crear asientos contables

        # caso 1
        if reconciliation.movement.bank.bank_name.lower() != "amazon":
            print("conciliacion distinta de amazon")
            print("tipo: "+ str(reconciliation.type))

            if reconciliation.type.code == "invoice" and reconciliation.invoice is not None and reconciliation.invoice.invoice_category.code=="sales":
                print("caso 1")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.invoice.reference,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de la factura "+ reconciliation.invoice.reference,
                    entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                    entry_accounting_account_description=reconciliation.movement.bank.bank_name,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 1 (bancos): "+ str(entry.id))
                # total de importe de la factura, sumando el amount_euros de todos sus conceptos
                total_invoice=0
                concepts=Concept.objects.filter(invoice=reconciliation.invoice)
                if concepts.count() > 0:
                    for concept in concepts:
                        total_invoice += concept.total_euros
                else:
                    total_invoice=reconciliation.invoice.total_euros

                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(total_invoice, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.invoice.reference,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de la factura "+ reconciliation.invoice.reference,
                    entry_accounting_account=reconciliation.invoice.customer.customer_accounting_account,
                    entry_accounting_account_description="Cuenta Cliente "+ reconciliation.invoice.customer.name,
                    entry_debit= total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 2 (cliente): "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()

            # caso 2
            elif reconciliation.type.code == "invoice" and reconciliation.invoice is not None and reconciliation.invoice.invoice_category.code=="expenses":
                print("caso 2")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                prov_account = 0
                prov_account_desc = "- No tiene proveedor -"
                if reconciliation.invoice.provider:
                    prov_account = reconciliation.invoice.provider.provider_accounting_account
                    prov_account_desc = "Cuenta Proveedor " + reconciliation.invoice.provider.name
                elif reconciliation.invoice.invoice_type_id == "payroll":
                    prov_account = "*********"
                    prov_account_desc = "Cuenta de Remuneraciones Pendientes de Pago"

                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.invoice.reference,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Pago de la factura "+ reconciliation.invoice.reference,
                    entry_accounting_account=prov_account,
                    entry_accounting_account_description=prov_account_desc,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 1 (proveedor): "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.invoice.reference,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Pago de la factura "+ reconciliation.invoice.reference,
                    entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                    entry_accounting_account_description="Cuenta Banco "+reconciliation.movement.bank.bank_name,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 2 (bancos): "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()

            # caso 3
            elif reconciliation.type.code == "account" and reconciliation.movement is not None and reconciliation.movement.bank.bank_accounting_account == "*********" and reconciliation.amount > 0:
                print("caso 3")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                    entry_accounting_account_description=reconciliation.movement.bank.bank_name,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="Partidas Pendientes de Aplicación",
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 2: "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()
            
            # caso 4
            elif reconciliation.type.code == "account" and reconciliation.movement is not None and reconciliation.movement.bank.bank_accounting_account == "*********" and reconciliation.amount < 0:
                print("caso 4")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account="*********",
                    entry_accounting_account_description="Partidas Pendientes de Aplicación",
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                    entry_accounting_account_description=reconciliation.movement.bank.bank_name,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 2: "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()
            
            # caso 5
            elif reconciliation.type.code == "account" and reconciliation.movement is not None and reconciliation.movement.bank.bank_accounting_account != "*********" and reconciliation.amount > 0:
                print("caso 5")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                    entry_accounting_account_description=reconciliation.movement.bank.bank_name,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account=reconciliation.accounting_account.code,
                    entry_accounting_account_description=reconciliation.accounting_account.description,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 2: "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()
            
            # caso 6
            elif reconciliation.type.code == "account" and reconciliation.movement is not None and reconciliation.movement.bank.bank_accounting_account != "*********" and reconciliation.amount < 0:
                print("caso 6")
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account=reconciliation.accounting_account.code,
                    entry_accounting_account_description=reconciliation.accounting_account.description,
                    entry_debit=total_entry,
                    entry_credit=total_entry_negativa,
                )
                print("Asiento 1: "+ str(entry.id))
                # si el total es negativo, se invierte entre las columnas de débito y crédito
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0
                entry=Entry.objects.create(
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept="Cobro de "+ reconciliation.movement.concept,
                    entry_accounting_account=reconciliation.movement.bank.bank_accounting_account,
                    entry_accounting_account_description=reconciliation.movement.bank.bank_name,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )
                print("Asiento 2: "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()

            # caso 7 conciliacion contra otro movimiento de otro banco (Transfer)
            elif reconciliation.type.code == "transfer" and reconciliation.movement is not None and reconciliation.movement_transfer is not None:
                rec_used = Reconciliation.objects.filter(movement__pk = reconciliation.movement.pk).filter(used_in_entry=True).first()
                if rec_used is not None and rec_used.used_in_entry:
                    continue

                # print("caso 7")
                total_entry_negativa=0
                total_entry=round(reconciliation.movement.amount_euros, 2)
                if total_entry < 0:
                    total_entry_negativa=total_entry*-1
                    total_entry=0

                num=1
                last_entry=Entry.objects.filter(entry_reconciliation=reconciliation,entry_seller=seller).order_by("-entry_num").first()
                if last_entry is not None and last_entry.entry_num is not None:
                    num = last_entry.entry_num
                else:
                    last_entry=Entry.objects.filter(entry_num__isnull=False,entry_seller=seller).order_by("-entry_num").first()
                    if last_entry is not None and last_entry.entry_num is not None:
                        num = int(last_entry.entry_num) + 1

                accounting = 0
                accounting_desc = "Transferencia entre cuentas"
                if reconciliation.movement.bank:
                    accounting = f"{reconciliation.movement.bank.bank_accounting_account}"
                    accounting_desc = f"Cuenta Banco {reconciliation.movement.bank.bank_name}"

                concepto=f"Cobro de {reconciliation.movement.concept} (Transferencia entre cuentas)"
                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept=concepto,
                    entry_accounting_account=accounting,
                    entry_accounting_account_description=accounting_desc,
                    entry_debit=total_entry ,
                    entry_credit=total_entry_negativa,
                )

                # # Entry List
                # if entry.entry_num not in entry_nums:
                #     entry_nums.append(entry.entry_num)

                accounting = 0
                accounting_desc = "Transferencia entre cuentas"
                if reconciliation.movement_transfer.bank:
                    accounting = f"{reconciliation.movement_transfer.bank.bank_accounting_account}"
                    accounting_desc = f"Cuenta Banco {reconciliation.movement_transfer.bank.bank_name}"

                # print("Asiento 1: "+ str(entry.id))
                concepto=f"Pago de {reconciliation.movement.concept} (Transferencia entre cuentas)"
                entry=Entry.objects.create(
                    entry_num=num,
                    entry_seller=seller,
                    entry_reconciliation=reconciliation,
                    entry_document=reconciliation.movement.concept,
                    entry_date=reconciliation.movement.movement_date,
                    entry_concept=concepto,
                    entry_accounting_account=accounting,
                    entry_accounting_account_description=accounting_desc,
                    entry_debit=total_entry_negativa,
                    entry_credit=total_entry,
                )

                # # Entry List
                # if entry.entry_num not in entry_nums:
                #     entry_nums.append(entry.entry_num)

                # print("Asiento 2: "+ str(entry.id))
                reconciliation.used_in_entry = True
                reconciliation.save()

                try:
                    print(f"rec: {reconciliation.movement.pk} - rec_transfer: {reconciliation.movement_transfer.pk} - amount: {reconciliation.movement.amount_euros}")
                    rec_transfer = Reconciliation.objects.filter(movement__pk=reconciliation.movement_transfer.pk).first()
                    rec_transfer.used_in_entry = True
                    rec_transfer.save()
                except Exception as e:
                    print("Error en rec_transfer:", e)
