from django.db import models

class ContractType(models.Model):

    code = models.CharField(
        max_length=10,
        verbose_name="Código del tipo de contrato o relación",
    )

    name = models.CharField(
        max_length=100,
        verbose_name="Nombre del tipo de contrato o relación",
    )

    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="Descripción del tipo de contrato o relación"
    )

    class Meta:
        verbose_name = "Tipo de contrato o relación"
        verbose_name_plural = "Tipos de contrato o relación"

    def __str__(self):
        return self.code + " - " + self.description
        
# @admin.register(ContractType)
# class ContractTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "name", "description"]
#     search_fields = ["code", "name", "description"]