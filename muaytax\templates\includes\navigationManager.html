{% load static %}
{% load gravatar %}

<!-- [ Header ] start -->
<header class="navbar pcoded-header fullbgManager navbar-expand-lg navbar-light ">

    <div class="collapse navbar-collapse">
        <ul class="navbar-nav me-auto2">
            <li>
                <div class=" header-logo">
                    <a href="{% url 'home' %}" class="b-brand">
                        <div class="b-bg">
                            {% if user.seller.logo %}
                                <img style="width:50px;" src="{{ request.user.seller.logo.url }}" alt="Seller Logo">
                            {% elif user.name %}
                                {% gravatar user.email 40 %}
                            {% else %}
                                <img style="width:50px;" src="{% static 'assets/images/logo.png' %}"/>    
                            {% endif %}  
                        </div>
                        <span class="b-title" style="color:#111;font-weight:500;">
                            {% if user.seller and user.seller.trade_name %}
                                {{ user.seller.trade_name }}
                            {% elif  user.seller and user.seller.name %}
                                {{ user.seller.name }}
                            {% elif user.name %}
                                {{ user.name }}
                            {% else %}
                                Muaytax
                            {% endif %}

                            {% if user.role == 'manager' %}
                                &nbsp; &nbsp; 
                                [ <b>Gestoría</b> ]
                            {% endif %}
                        </span>
                    </a>
                
                </div>
            </li>
        </ul>
        <ul class="navbar-nav ms-auto">
           
            <!--<li><a target="_blank" href="https://web.whatsapp.com/send?phone=34936940806" class="displayChatbox"><i class="feather icon-message-circle"></i></a></li>-->
            <li>
                <div class="dropdown drp-user">
                    <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="icon feather icon-settings"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end profile-notification">
                        <!--
                           <div class="pro-head">
                            <img src="{% if user.image %}{{ user.image }}{% else %}
                                        {% static 'assets/images/user/avatar-5.jpg' %}
                                        {% endif %}" class="img-radius hei-40" alt="User-Profile-Image">
                            <span>
                                {{ request.user.username }}
                            </span>
                            <a href="{% url 'account_logout' %}" class="dud-logout" title="Cerrar sesión">
                                <i class="feather icon-log-out"></i>
                            </a>
                        </div>
                    -->
                        <ul class="pro-body">
                            {% if not request.user.has_permission_manager %}
                            <li>
                                <a href="{% url 'users:profile' %}" class="dropdown-item">
                                    <i class="feather icon-user"></i>
                                    Perfil
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a href="{% url 'account_logout' %}" class="dropdown-item">
                                    <i class="feather icon-log-out"></i>
                                    Cerrar sesión
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</header>
<!-- [ Header ] end -->
