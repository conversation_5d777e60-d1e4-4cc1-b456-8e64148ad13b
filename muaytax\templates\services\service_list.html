{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}
  Servicios Contratados
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <style>
    #list-table {
      width:  100% !important;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }

    .dt-paging.paging_full_numbers, .dt-info {
        display: flex;
        justify-content: flex-end;
    }
  </style>

{% endblock stylesheets %}
{% block breadcrumb %}
    <div class="page-header">
      <div class="page-block">
        <div class="row align-items-center">
          <div class="col">
            <div class="page-header-title">
              <h5 class="m-b-10">Servicios Contratados</h5>
            </div>
            <ul class="breadcrumb">
              <li class="breadcrumb-item">
                <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:list' %}">Vendedores</a>
              </li>
              <li class="breadcrumb-item">
                <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name }} </a>
              </li>
              <li class="breadcrumb-item">
                <a href=".">Servicios Contratados</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-12 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input  class="form-control" 
                        type="search" 
                        id="search" 
                        name="search"
                        placeholder="Buscar..."/>
              </div>
            </div>
          </div>
            <div class="dt-responsive table-responsive">
              <table id="list-table" class="table nowrap table-hover">
                <thead class="table-head">
                <tr>
                  <th >ID</th>
                  <th style= "width: 50%;">Nombre del Servicio</th>
                  <th>F.Contratación</th>
                  <th style=  "width:5%;">Cantidad</th>
                  <th style=  "width:5%;">Acciones</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
              </table>
            </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <!-- DEBUG  -->
  <script type="text/javascript">
    const debug = true;
  </script>
  <!-- DEBUG  -->

  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.min-v2.0.8.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.2.1.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables.min-v2.0.8.css">
  <link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/fixedHeader/fixedHeader.dataTables.min-v3.2.1.css">
  <script>
  $(document).ready(function () {
    let timer = null;
    const searchInput = $("#search");
      searchInput.on("keydown", (e) => {
      if (e.key === "Enter") {
          clearTimeout(timer);
          filter();
            }
        });

      searchInput.on("input", () => {
          clearTimeout(timer);
          timer = setTimeout(filter, 500);
      });

    const ajaxData = (d) =>{
      if (debug) console.log('ajaxData | d: ', d);

      let input = searchInput.val();
      if(input){
        if(debug) console.log('filterDT | input ', input);
        d.search = input;
      }

      return d;
    }

    const dataTableOptions = {
      serverSide: true,
      autoWidth: true,
      truncation: true,
      paging: true,
      searching: true,
      lengthChange: false,
      fixedHeader: true,
      language: {
          lengthMenu: "_MENU_",
          zeroRecords: "No se han encontrado servicios.",
          info: "_TOTAL_ resultados. ",
          search: "Buscar:",
          infoEmpty: "No hay resultados que coincidan con su búsqueda.",
          infoFiltered: ""
      },
      dom: 'lrtip',
      ajax: {
          url: "{% url 'app_services:service_list_DT' seller.shortname %}",
          type: "GET",
          dataSrc: 'data',
            data: function (d) {
              return ajaxData(d);
            }
      },
      columns: [
          {"data": "pk"},
          {"data": "service_name"},
          {"data": "contracting_date"},
          {"data": "quantity"},
          {
            "data": 'buttons',
            render: function (data, type, row) {
                return `<a data-bs-toggle="tooltip" data-bs-placement="top" title="Editar"
                          class="btn btn-icon btn-success"
                          href="./detail/${row.pk}">
                          <i class="feather icon-edit"></i>
                      </a>`;
            }
        }
      ],
      drawCallback: function (settings) {
        $('[data-bs-toggle="tooltip"]').tooltip(); //inicializar los tooltips
      }
    };
    const dataTable = $("#list-table").DataTable(dataTableOptions);

    const filter = () =>{
      dataTable.draw();
    }
  });
  </script>
{% endblock javascripts %}
