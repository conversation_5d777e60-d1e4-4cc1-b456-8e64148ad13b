{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
{{ country.name | title }}
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/all/all.min-v5.15.4.css"> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {     
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }


     .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }

    .dataTables_filter {
      display: none;
    }
  
  
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">IVA {{ country.name | title }}</h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
          </li>
          <li class="breadcrumb-item">
            <a href=".">IVA {{ country.name | title }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
        <div class="dt-responsive">        
          <div class="row d-flex mt-3">
            <!-- Search + Pagination -->
            <div class="col-4 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()" />
              </div>
            </div> 
            <!-- Search + Pagination -->

            <div class="col">
              <select class="form-select form-control" name="period-input" id="period" onchange="onChangePeriodYear()">
                <option value="Q1">Trimestre 1 </option>
                <option value="Q2">Trimestre 2 </option>
                <option value="Q3">Trimestre 3 </option>
                <option value="Q4">Trimestre 4 </option>
              </select>
            </div>

            <div class="col">
              <select class="form-select form-control" name="period-input" id="year" onchange="onChangePeriodYear()">
                <option value="2022">2022</option>
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025">2025</option>
              </select>
            </div>

            <div class="col">
              <select class="form-select form-control"  id="manager_assigned" onchange="manager_assigned();">
                <option value="">Mostrar Todos</option>
                {% for manager in managers %}
                <option value="{{ manager.pk }}">{{ manager.name }}</option>
                {% endfor %}
              </select>
            </div>
            
            <div class="col-2 d-flex  align-items-center">
              {% comment %}
              <p class="mx-3">
                <button id="showcolumns1" class="btn btn-secondary m-0"  onclick="onClickButtonModels()">
                  <i class="fa-regular fa-eye"></i>
                  <b>Modelos</b>
                </button>
              </p>
              {% endcomment %}
              <p class="mx-3">
                <button id="showcolumns1" class="btn btn-secondary m-0"  onclick="onClickButtonTxT()">
                  <i class="fa-regular fa-eye"></i>
                  <b>TXT</b>
                </button>
              </p>
            </div>
            
          </div>

          <!-- Total pending invoices -->
          <div class="row">
            <div class="col-3">
              <div class="card">
                <div class="card-block">
                  <div class="row d-flex align-items-center">
                    <div class="col">
                      <h6><b>TOTAL FACTURAS PENDIENTES</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted"><b id="total_invoices_count">&nbsp</b></h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <table id="seller-list-table2" class="table nowrap">
            <thead class="table-head">
              <tr>
                <th>Nombre</th>
                <th>Pendientes</th>
                <th >Enero</th>
                <th >Febrero</th>
                <th >Marzo</th>
                <th >Abril</th>
                <th >Mayo</th>
                <th >Junio</th>
                <th >Julio</th>
                <th >Agosto</th>
                <th >Septiembre</th>
                <th >Octubre</th>
                <th >Noviembre</th>
                <th >Diciembre</th>
                <th>Último acceso</th>
                <th style="width:5%;">Acciones</th>
              </tr>
            </thead>
            <tbody>
              
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}

<!-- JQUERY DATATABLES -->
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
<script>

    let showTXT = false;

    let dataTable = null;

    const debug = true;
      const ajaxData=(d)=>{
        if(debug) console.log('ajax ', d);
          let tParams = "";
          let year = document.getElementById("year").value;
          let period = document.getElementById("period").value;
          if(year){
            if (debug) console.log('filterDT | year: ', year);
            d.year = year
            tParams += "&year=" + year;
          }
          if(period){
            if (debug) console.log('filterDT | period: ', period);
            d.period = period
            tParams += "&period=" + period;
          }

        getTotals(tParams);
        return d
      }

    const getTotals = (params) => {
      let p = params;

      if (!p || p == undefined || p == null || p == "") {
        p = "";
      } else if (p.charAt(0) == "&") {
        p[0] = "?";
      }
    }

    $(document).ready(function() {
        let cookie;
        dataTable = $("#seller-list-table2").DataTable({
            "serverSide":false,
                "ajax":{
                    "dataSrc":"data",
                    "url":"{% url 'app_sellers:seller_vatcountry_list_dt' country.iso_code %}",
                    "data":function( d ){
                      ajaxData(d);
                      
                      }
                },
                "language": {
                      "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
                      "lengthMenu": "_MENU_",
                      "zeroRecords": "No se han encontrado vendedores.",
                      "info": "_START_ a _END_ de un total de _TOTAL_",
                      "search": "Buscar:",
                      "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                      "infoFiltered": ""
                    },
                "createdRow": function(row, data, dataIndex) {

                  const shortname = data.shortname; 
                  const link = '/sellers/' + shortname + '/';
                  $(row).attr('style', 'cursor: pointer;');
                  $(row).attr('onclick', "window.location.href = '" + link + "';");
                },
                "columns":[
                  {"data": "user_name",
                    "className":"head_name",
                    "render": function(data, type, row) {
                      let html = '';
                      html += '<td class="align-middle">';
                    html += '<div class="d-inline-block">';
                    html += '<h6 class="m-b-0"><b>';
                    
                    let name = row.seller_name;  
                    if (typeof name === 'string') {  
                        const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l','sl.']; 
                          const words = row.seller_name.split(' ').map(function(word) {
                              const lowerWord = word.toLowerCase();
                              if (lowerCaseSuffixes.includes(lowerWord)) {
                                  return word.toUpperCase();
                              } else {
                                  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                              }
                        });
                      html += words.join(' ');
                    }
                    html += '</b>';
                    if (row.seller_name && row.seller_name.toLowerCase() !== row.user_name.toLowerCase()) {
                        html += ' - ' + row.user_name.split(' ').map(function(word) {
                            return word.charAt(0).toUpperCase() + word.slice(1);
                        }).join(' ');
                    }  
                    
                    html += '</h6>';
                    html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                    html += '</div>';
                    html += '</td>';
                    
                    return html;}
                  },
                  {"data": "num_pending_invoices",
                      "className":"pendings",
                      "render": function(data, type, row) {
                        if (data && (type === 'display' || type === 'filter')) {
                          let html = '<td data-order="' + row.num_pending_invoices + '" class="align-middle">';
                          html += row.num_pending_invoices + ' (' + row.percentage_pending_invoices + '%)';
                          html += '<div class="progress" style="height: 15px;">';
                          html += '<div class="progress-bar bg-warning" role="progressbar" style="width: ' + row.percentage_pending_invoices + '%"></div>';
                          html += '</div>';
                          html += '</td>';
                          return html;
                        }
                        return data;
                      }
                  },
                  {"data": "month1",
                     "className": "txt Q1",
                    "render": function(data, type, row) {
                          let html = '';
                          const month1 = row.month1; 
                        if (month1 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },            
                  },
                  {"data": "month2",
                   "className": "txt Q1",
                    "render": function(data, type, row) {
                          let html = '';
                          const month2 = row.month2; 
                        if (month2 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }       

                  },
                  {"data": "month3",
                   "className": "txt Q1",
                    "render": function(data, type, row) {
                          let html = '';
                          const month3 = row.month3; 
                        if (month3 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }
                  },
                  {"data": "month4",
                    "className": "txt Q2",
                    "render": function(data, type, row) {
                          let html = '';
                          const month4 = row.month4; 
                        if (month4 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }      
                  },
                  {"data": "month5",
                    "className": "txt Q2",
                      "render": function(data, type, row) {
                            let html = '';
                            const month5 = row.month5; 
                          if (month5 === true) {
                              html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                              html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                              html += '</a>';
                          } else {
                              html += '&nbsp';
                          }
                              
                              return html;
                        },      
                    },
                  {"data": "month6",
                    "className": "txt Q2",
                    "render": function(data, type, row) {
                          let html = '';
                          const month6 = row.month6; 
                        if (month6 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      } 
                  },
                  {"data": "month7",
                    "className": "txt Q3",
                    "render": function(data, type, row) {
                          let html = '';
                          const month7 = row.month7; 
                        if (month7 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "month8",
                    "className": "txt Q3",
                    "render": function(data, type, row) {
                          let html = '';
                          const month8 = row.month8; 
                        if (month8 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },      
                  },
                  {"data": "month9",
                    "className": "txt Q3",
                    "render": function(data, type, row) {
                          let html = '';
                          const month9 = row.month9; 
                        if (month9 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      },      
                  },
                  {"data": "month10",
                    "className": "txt Q4",
                    "render": function(data, type, row) {
                          let html = '';
                          const month10 = row.month10; 
                        if (month10 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "month11",
                    "className": "txt Q4",
                    "render": function(data, type, row) {
                          let html = '';
                          const month11 = row.month11; 
                        if (month11 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "month12",
                    "className": "txt Q4",
                    "render": function(data, type, row) {
                          let html = '';
                          const month12 = row.month12; 
                        if (month12 === true) {
                            html += '<a href="/sellers/' + row.shortname + '/AmazonTxtEur/">';
                            html += '<i class="fa-solid fa-circle fa-xl" style="color: #02c018;"></i>';
                            html += '</a>';
                        } else {
                            html += '&nbsp';
                        }
                            
                            return html;
                      }     
                  },
                  {"data": "last_login",
                    "className":"login",
                    "render": function(data, type, row) {
                          if (data && (type === 'display' || type === 'filter')) {
                            const date = new Date(data);

                            const day = date.getDate().toString().padStart(2, '0');
                            const month = date.toLocaleString('default', { month: 'short' });
                            const year = date.getFullYear();
                            const hours = date.getHours().toString().padStart(2, '0');
                            const minutes = date.getMinutes().toString().padStart(2, '0');

                            const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;
                            
                            return formattedDate;
                        }
                        return data; // Para otros tipos, como 'sort'
                      }
                  },
                  {"data": null,
                    "className":"actions",
                    "orderable": false,
                    "render":function(data, type, row) {
                      let html = '<td class="align-middle text-center">';
                      html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                      html += '<i class="feather icon-edit"></i>';
                      html += '</a>';
                      html += '</td>';
                      html = '<div style="text-align: center;">' + html + '</div>';    
                      return html;
                    },
                  
                  },
                  {"data": "manager_assigned",
                    'visible': false,
                  }
                ],
                "order": [[2, "asc"],[3, "asc"],[0, "asc"]],
                "paging": true,
                "searching":true,
                "lengthChange":false,
                "lengthMenu":[[100,150,200,-1], [100,150,200, 'Todos']],
                "initComplete": function(settings, json) {
                 dataTable.settings()[0].nTBody.style.width = "100%";
                 dataTable.settings()[0].nTable.style.width = "100%";
                 dataTable.settings()[0].nTHead.style.width = "100%";
                 console.log("Tabla:", settings)
                 total_invoices_count =json.total_invoices;
                 document.getElementById("total_invoices_count").textContent = total_invoices_count;
                  cookie = getCookie("allsellersvat_show_txt");
                  const period = "{{period}}";
                  if(cookie == true || cookie == "true"){
                    $('th.txt, td.txt').hide();
                    $(`th.txt.${period}, td.txt.${period}`).show();

                  }else{
                      $('th.txt, td.txt').hide();
                  }
                 
       
              },
              "drawCallback": function(settings) {
                  console.log("Tabla redibujada:", settings);
                  dataTable.settings()[0].nTable.style.width = "100%";
                  cookie = getCookie("allsellersvat_show_txt");
                  const period = "{{period}}";
                  if(cookie == true || cookie == "true"){
                    $('th.txt, td.txt').hide();
                    $(`th.txt.${period}, td.txt.${period}`).show();

                  }else{
                      $('th.txt, td.txt').hide();
                  }
                  }
        
                
        });

     });

    function search() {
      var tipo = $("#search").val();
      dataTable.column(0).search(tipo).draw();
    }

    function filtrar() {
      var tipo = $("#tipo").val();
      dataTable.column(1).search(tipo).draw();
    }

    function manager_assigned() {
      var tipo = $("#manager_assigned").val();
      dataTable.column(16).search(tipo).draw();
    }

    const onChangePeriodYear =()=>{
      const period = document.getElementById('period');
      const year = document.getElementById('year');
      const urlembed= "{% url 'app_sellers:vat' country.iso_code %}";
      const newUrl= urlembed + '?period=' + period.value + '&year=' + year.value;
      window.location.href = newUrl;
      console.log(newUrl);
    }

    const onload =()=>{
      const period = document.getElementById('period');
      const year = document.getElementById('year');
      period.value = '{{period}}';
      year.value = '{{year}}';

    }

    const createCookie = (name, value) => {
      document.cookie = name + "=" + value + "; path=/";
    }

    const getCookie = (name) => {
      const cookieValue = document.cookie.match("(^|;)\\s*" + name + "\\s*=\\s*([^;]+)");
      return cookieValue ? cookieValue.pop() : "";
    }

    
     const onClickButtonTxT = () => {
        const period = "{{period}}";
        if (showTXT == false) {
          $('th.txt, td.txt, div.txt').hide();
          $(`th.txt.${period}, td.txt.${period}, div.txt.${period}`).show();
          showTXT = true;
        } else {
          $('th.txt, td.txt, div.txt').hide();
          showTXT = false;
        }
        
        createCookie("allsellersvat_show_txt", showTXT);
    }

    const cookie = getCookie("allsellersvat_show_txt");
        if(cookie == true || cookie == "true"){
          onClickButtonTxT();
        }  

    onload();
    

</script>
<!-- JQUERY DATATABLES -->



{% endblock javascripts %}