{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}
{% block stylesheets %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/loading.css"/>
  <script type="module" src="{{ STATIC_URL }}assets/js/plugins/multi-checkbox.js"></script>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v1.13.4.css"
        type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.10.25.css"
        type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css"
        type="text/css"/>
  <style>
    thead tr th:after, thead tr th:before {
      display: none !important;
    }

    .dropdown-menu.show:before {
      display: none;

    }

    .column-width-limit {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 13vw;
    }

    .user-profile-list table tbody tr:hover {
      box-shadow: 0 0 6px 0 #04a9f5;
    }

    .user-profile-list table tbody tr:hover td {
      /* cursor: pointer; */
      color: #fff;
      background: #04a9f5;
    }

    .user-profile-list table tbody tr:hover td h6 {
      color: #fff;
    }

    .user-profile-list table tbody tr:hover td .overlay-edit {
      opacity: 1;
    }

    table.dataTable tbody tr td.select-checkbox:before {
    {% comment %} content: "";
    margin-top: 5px;
    margin-left: 1px;
    text-align: center;
    border: 1px solid black;
    border-radius: 3px; {% endcomment %} display: none;
    }

    table.dataTable tbody tr.selected td.select-checkbox:after {
    {% comment %} content: "✓";
    font-size: 18px;
    margin-top: -11px;
    margin-left: 1px;
    text-align: center;
    text-shadow: 1px 1px #b0bed9, -1px -1px #b0bed9, 1px -1px #b0bed9, -1px 1px #b0bed9; {% endcomment %} display: none;
    }

    input#miId {
      margin-left: 8px;
      width: 14px;
      height: 14px;
    }

    #select_all {
      width: 14px;
      height: 14px;
    }

    .tooltip-wrapper {
      position: relative;
    }

    .tooltip-wrapper::after {
      content: attr(data-bs-original-title);
      position: absolute;
      bottom: 115%;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px;
      background-color: black;
      color: white;
      border-radius: 20px;
      font-size: 14px;
      font-weight: normal;
      width: 200px;
      white-space: pre-line;
      visibility: hidden;
      opacity: 0;
      transition: opacity 0.3s;
      z-index: 9999 !important;
    }

    .tooltip-wrapper:hover::after {
      visibility: visible;
      opacity: 1;
    }

    .tooltip-button {
      white-space: nowrap;
      width: 150px;
    }

    .tooltip-button:hover::after {
      white-space: nowrap;
      width: auto;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }

    multi-transaction, multi-departurecountry, multi-taxcountry {
      /* Element */
      --mc-z-index: 10 !important;
      --mc-border: 1px solid #ced4da;;
      --mc-display: inline-block;
      --mc-font: 14px;
      --mc-margin: 0;
      --mc-vertical-align: middle;

      /* Dropdown */
      --mc-dropdown-background: #fff;
      --mc-dropdown-box-shadow: 0 1px 2px 0 rgba(57, 70, 92, .05);
      --mc-dropdown-max-height: 350px;
      --mc-dropdown-text-align: left;
      --mc-dropdown-width: 100%;

      /* Input */
      --mc-target-background: #fff;
      --mc-target-color: #000000;
      --mc-target-cursor: pointer;
      --mc-target-margin: 0;
      --mc-target-padding: 0px 0px 0px 3px;
      --mc-target-outline: none;
      --mc-vertical-align: middle;
      --mc-target-width: 268px;

      /* Dropdown Line Items */
      --mc-dropdown-line-height: 2em;
      --mc-ul-margin: 0;
      --mc-ul-padding: 0;
      --mc-checkbox-height: 30px;
      --mc-checkbox-width: 20px;
      --mc-li-cursor: pointer;

      /* Toggle Button */
      --mc-toggle-button-background: #ffffff;
      --mc-toggle-button-color: #000000;
      --mc-toggle-button-cursor: pointer;
      --mc-toggle-button-height: 46px;
      --mc-toggle-button-outline: none;
      --mc-toggle-button-width: 23px;

      /* Close Button */
      --mc-close-button-background: #efefef;
      --mc-close-button-border: none;
      --mc-close-button-border-radius: default;
      --mc-close-button-color: #000000;
      --mc-close-button-cursor: pointer;
      --mc-close-button-display: block;
      --mc-close-button-height: 22px;
      --mc-close-button-margin: 5px auto;
      --mc-close-button-outline: none;
      --mc-close-button-width: 22px;
    }

    #multi-value:hover {
      background-color: #0d6efd;
      --mc-dropdown-color: #fff;
    }

    #multi-value:hover div {
      color: #fff;
    }

    /* new styles */
    .theme-bg {
      background: linear-gradient(-135deg, #1de9b6 0%, #03ad65 100%);
    }

    .theme-bg2 {
      background: linear-gradient(-135deg, #899fd4 0, #a389d4 100%);
    }

    .theme-bg3 {
      background: linear-gradient(-135deg, #04a9f5 0, #085f88 100%);
    }

    .def-position {
      position: unset !important;
      top: 50% !important;
      transform: unset !important;
      opacity: unset !important;
    }

  </style>
{% endblock stylesheets %}
{% block title %}
  Facturas
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas: Listado Facturas
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:summary' seller.shortname %}">
                    {% if seller.name is not None %}
                      {{ seller.name.capitalize }}
                    {% else %}
                      Resumen
                    {% endif %}
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Facturas</a>
                </li>
                {% if transfer %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_invoices:seller_invoices_transfers' seller.shortname %}">Transfers</a>
                  </li>
                {% endif %}
                {% if category %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname category.pk %}">Facturas
                      de {{ category }}</a>
                  </li>
                {% endif %}
              </ul>
            </div>
            {% if perms.users.is_superuserAPP or perms.invoices.change_invoice or perms.invoices.add_invoice %}
            <div class="col col-auto" style="display: none;">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}uploadtxt">Subir TXT Amazon</a>
                </li>
              </ul>
            </div>
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}upload">Subir Facturas</a>
                </li>
              </ul>
            </div>
            {% endif %}
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'expenses' %}">
                    Facturas de Gasto
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'sales' %}">
                    Facturas de Venta
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-auto">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Todas las Facturas</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <!-- Filtros -->
    <div class="col-12">
      <div class="row">
        {% if economic_activity|length > 1 %}
          <div class="col-xl col-lg-4">
            <select class="form-control form-select" name="economic_activity" id="economic_activity"
                    onchange="filter()">
              <option value="">Todos los IAE</option>
              {% for iae in economic_activity %}
                <option value="{{ iae.code }}">{{ iae.description }}</option>
              {% endfor %}
            </select>
          </div>
        {% endif %}
        <div class="col-xl col-lg-4">
          <select class="form-control form-select" name="status" id="status" onchange="filter()">
            <option value="">Todos los estados</option>
            {% for status in invoice_statuses %}
              <option value="{{ status.pk }}">{{ status.description }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-xl col-lg-4">
          <select class="form-control form-select" name="year" id="year" onchange="filter(); onChangeYear();">
            <option value="">Todos los años</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
            <option value="2021">2021</option>
            <option value="2020">2020</option>
          </select>
        </div>
        <div class="col-xl col-lg-4">
          <select class="form-control form-select" name="month" id="month" onchange="filter()" disabled>
            <option value="">Todos los meses</option>
            <option value="-1">Trimestre 1</option>
            <option value="-2">Trimestre 2</option>
            <option value="-3">Trimestre 3</option>
            <option value="-4">Trimestre 4</option>
            <option value="01">Enero</option>
            <option value="02">Febrero</option>
            <option value="03">Marzo</option>
            <option value="04">Abril</option>
            <option value="05">Mayo</option>
            <option value="06">Junio</option>
            <option value="07">Julio</option>
            <option value="08">Agosto</option>
            <option value="09">Septiembre</option>
            <option value="10">Octubre</option>
            <option value="11">Noviembre</option>
            <option value="12">Diciembre</option>
          </select>
        </div>
        <div class="col-xl col-lg-4">
          <multi-taxcountry separator=", " value="" id="multiple-taxcountries">
            <ul slot="check-values">
              {% for country in invoices_tax_country %}
                <li class="cursor-default" id="multi-value" value="{{ country.pk }}">{{ country.name }} ( {{ country.pk }} )</li>
              {% endfor %}
            </ul>
          </multi-taxcountry>
        </div>
        <div class="col-xl col-lg-4">
          <multi-departurecountry separator=", " value="" id="multiple-departurecountries">
            <ul slot="check-values">
              {% for country in invoices_departure_country %}
                <li class="cursor-default" id="multi-value" value="{{ country.pk }}">{{ country.name }} ( {{ country.pk }} )</li>
              {% endfor %}
            </ul>
          </multi-departurecountry>
        </div>
        <div class="col-xl col-lg-4">
          <select class="form-control form-select" name="invoice_type" id="invoice_type"
                  onchange="filter()">
            <option value="">Todos los tipos de facturas</option>
            {% for invoice in invoice_type %}
              <option value="{{ invoice.code }}">{{ invoice.description }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-xl col-lg-4">
          <multi-transaction separator=", " value="" id="multiple-transactions">
            <ul slot="check-values">
              {% for transaction in transaction_types %}
                <li class="cursor-default" id="multi-value"
                    value="{{ transaction.code }}">{{ transaction.description }}</li>
              {% endfor %}
            </ul>
          </multi-transaction>
        </div>

      </div>
    </div>
    <!-- Filtros -->

    <!-- Search + download + export excel -->
    <div class="col-12">
      <div class="row mt-3">
        <div class="col">
          <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."/>
        </div>

        <div class="col-auto">
          <div class="btn-group">
            <button class="btn btn-info dropdown-toggle" style="font-size: 14px;" type="button"
                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
              <i class="fa-solid fa-xl fa-download me-0"></i>
              Descargar
            </button>
            <div class="dropdown-menu"
                 style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(0px, 45px);"
                 data-popper-placement="bottom-start">
              <a class="dropdown-item" role="button" onclick="downloadInvoices()">Descargar facturas
                <!-- <a class="dropdown-item" data-bs-toggle="tooltip" data-bs-placement="top" title="Solo se descargarán las facturas que estén cargadas en la aplicación" role="button" onclick="downloadInvoices()" >Descargar facturas -->
                <span class="badge" style="background-color: #6c757d;">.zip</span>
              </a>
              <a class="dropdown-item" role="button" onclick="generateCSV()">Exportar excel
                <span class="badge float-end bg-success">.xlsx</span>
              </a>
            </div>
          </div>
        </div>

        <!-- <div class="col-auto">
          <div class="text-end">
            <div id="export_excel">
              <button class="btn btn-primary w-100" type="button" id="export-csv-btn" onclick="generateCSV()">
                <i class="fa-solid fa-xl fa-file-excel"></i><b>Excel</b>
              </button>
            </div>
            <div id="export_excel_spinner" style="display:none;">
              <button class="btn btn-primary w-100" type="button" disabled>
                <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                <b>Generando...</b>
              </button>
            </div>
          </div>
        </div> -->

      </div>
    </div>
    <!-- Search -->

    <!-- Totals | START -->
    {% if category %}
      {% if category.pk == "sales" or category.pk == "expenses" %}
        <div class="vue">
          <div class="row mt-3 mb-0" style="display: none;" v-show="true">
            <div class="col-md-12 col-lg-6 col-xl-4">
              <div class="card theme-bg bitcoin-wallet" style="min-height: 234.38px;">
                <div class="card-block">
                  <div class="row d-flex align-items-center mb-0">
                    <div class="col col-12 mb-0">
                      <div class="d-flex justify-content-between">
                        <h6 class="text-white"><b>BASE</b></h6>
                        {% if is_self_employed %}
                          <h4 class="text-white"><b>[[ invoiceDataJSON.regime ]]</b></h4>
                        {% endif %}
                      </div>
                      <h2 class="f-w-300 d-flex align-items-center mb-4 text-white">
                        {% if category.pk == 'sales' %}
                          <b>[[ invoiceDataJSON.total_sales_amount ]] €</b>
                        {% elif category.pk == 'expenses' %}
                          <b>[[ invoiceDataJSON.total_expenses_amount ]] €</b>
                        {% endif %}
                      </h2>
                      <span class="text-white d-block">Importe total bruto</span>
                    </div>
                  </div>
                  <i class="fas fa-euro-sign f-70 text-white"></i>
                </div>
              </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xl-4">
              <div class="card theme-bg2 bitcoin-wallet">
                <div class="card-block">
                  <div class="row d-flex align-items-center mb-0">
                    <div class="col col-12 mb-0">
                      <div class="d-flex justify-content-between">
                        <h6 class="text-white"><b>IVA</b></h6>
                        {% if is_self_employed %}
                          <h4 class="text-white"><b>[[ invoiceDataJSON.regime ]]</b></h4>
                        {% endif %}
                      </div>
                      <h2 class="f-w-300 d-flex align-items-center mb-4 text-white">
                        {% if category.pk == 'sales' %}
                          <b>[[ invoiceDataJSON.total_sales_vat ]] €</b>
                        {% elif category.pk == 'expenses' %}
                          <b>[[ invoiceDataJSON.total_expenses_vat ]] €</b>
                        {% endif %}
                      </h2>
                    </div>
                    <div class="col mb-0">
                      <h6 class="text-white"><b>IRPF</b></h6>
                      <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                        {% if category.pk == 'sales' %}
                          [[ invoiceDataJSON.total_sales_irpf ]] €
                        {% elif category.pk == 'expenses' %}
                          [[ invoiceDataJSON.total_expenses_irpf ]] €
                        {% endif %}
                      </h4>
                    </div>
                    {% if category.pk == 'expenses' and seller.eqtax == True %}
                      <div class="col mb-0">
                        <h6 class="text-white"><b>RE.EQ</b></h6>
                        <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                          [[ invoiceDataJSON.total_expenses_eqtax ]] €
                        </h4>
                      </div>
                    {% endif %}
                    {% if is_self_employed and seller.is_direct_estimation %}
                      {% if category.pk == 'expenses' %}
                        <div class="col mb-0 ">
                          <h6 class="text-white"><b>OTROS</b></h6>
                          <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                            [[ invoiceDataJSON.total_sevent_porcent]] €
                          </h4>
                        </div>
                      {% endif %}
                    {% endif %}
                  </div>
                  <i class="fas fa-euro-sign f-70 text-white"></i>
                </div>
              </div>
            </div>
            <div class="col-md-12 col-lg-12 col-xl-4">
              <div class="card theme-bg3 bitcoin-wallet" style="min-height: 234.38px;">
                <div class="card-block">
                  <div class="row d-flex align-items-center mb-0">
                    <div class="col col-12 mb-0">
                      <div class="d-flex justify-content-between">
                        <h6 class="text-white"><b>TOTAL</b></h6>
                        {% if is_self_employed %}
                          <h4 class="text-white"><b>[[ invoiceDataJSON.regime ]]</b></h4>
                        {% endif %}
                      </div>
                      <h2 class="f-w-300 d-flex align-items-center mb-4 text-white">
                        {% if category.pk == 'sales' %}
                          <b>[[ invoiceDataJSON.total_sales ]] €</b>
                        {% elif category.pk == 'expenses' %}
                          <b>[[ invoiceDataJSON.total_expenses ]] €</b>
                        {% endif %}
                      </h2>
                      <span class="text-white d-block">Importe total neto</span>
                    </div>
                  </div>
                  <i class="fas fa-euro-sign f-70 text-white"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    {% else %}
      <div class="vue">
        <div class="row mt-3" style="display: none;" v-show="true">
          <div class="col-md-12 col-lg-6 col-xl-4">
            <div class="card theme-bg bitcoin-wallet">
              <div class="card-block">
                <div class="row d-flex align-items-center mb-0">
                  <div class="col col-12 mb-0">
                    <div class="d-flex justify-content-between">
                      <h6 class="text-white"><b>Ingresos</b></h6>
                      {% if is_self_employed %}
                        <h4 class="text-white"><b>[[ invoiceDataJSON.regime ]]</b></h4>
                      {% endif %}
                    </div>
                    <h3 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      <b>[[ invoiceDataJSON.total_sales_amount ]] € &nbsp;</b>
                      <i class="def-position fas fa-caret-up f-26 m-l-8" style="color: #18764e;"></i>
                    </h3>
                  </div>
                  <div class="col mb-0">
                    <h6 class="text-white"><b>IVA</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_sales_vat ]] €
                    </h4>
                  </div>
                  <div class="col mb-0">
                    <h6 class="text-white"><b>IRPF</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_sales_irpf ]] €
                    </h4>
                  </div>
                </div>
                <i class="fas fa-euro-sign f-70 text-white"></i>
              </div>
            </div>
          </div>
          <!-- <div class="col-4 mb-0">
            <div class="card mb-0">
              <div class="card-block mb-0">
                <div class="row d-flex align-items-center mb-0">
                  <div class="col mb-0">
                    <h6><b>Ingresos</b></h6>
                    <h3 class="f-w-300 d-flex align-items-center mb-4 text-success">
                        <b>[[ invoiceDataJSON.total_sales_amount ]] €</b>
                    </h3>
                  </div>
                  <div class="col mb-0">
                    <h6><b>IVA</b></h6>
                    <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                      [[ invoiceDataJSON.total_sales_vat ]] €
                    </h3>
                  </div>
                  <div class="col mb-0">
                    <h6><b>IRPF</b></h6>
                    <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                        [[ invoiceDataJSON.total_sales_irpf ]] €
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
          <div class="col-md-12 col-lg-6 col-xl-4">
            <div class="card theme-bg2 bitcoin-wallet">
              <div class="card-block">
                <div class="row d-flex align-items-center mb-0">
                  <div class="col col-12 mb-0">
                    <div class="d-flex justify-content-between">
                      <h6 class="text-white"><b>Gastos</b></h6>
                      {% if is_self_employed %}
                        <h4 class="text-white"><b>[[ invoiceDataJSON.regime ]]</b></h4>
                      {% endif %}
                    </div>
                    <h3 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      <b>[[ invoiceDataJSON.total_expenses_amount ]] € &nbsp;</b>
                      <i class="def-position fas fa-caret-down text-c-red f-26 m-l-8"></i>
                    </h3>
                  </div>
                  <div class="col mb-0">
                    <h6 class="text-white"><b>IVA</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_expenses_vat ]] €
                    </h4>
                  </div>
                  <div class="col mb-0">
                    <h6 class="text-white"><b>IRPF</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_expenses_irpf ]] €
                    </h4>
                  </div>
                  {% if seller.eqtax == True %}
                    <div class="col mb-0">
                      <h6 class="text-white"><b>RE.EQ</b></h6>
                      <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                        [[ invoiceDataJSON.total_expenses_eqtax ]] €
                      </h4>
                    </div>
                  {% endif %}
                  {% if is_self_employed and seller.is_direct_estimation %}
                  <div class="col mb-0 ">
                    <h6 class="text-white"><b>OTROS</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_sevent_porcent]] €
                    </h4>
                  </div>
                  {% endif %}
                </div>
                <i class="fas fa-euro-sign f-70 text-white"></i>
              </div>
            </div>
          </div>
          <!-- <div class="col-4 mb-0">
                <div class="card mb-0">
                  <div class="card-block mb-0">
                    <div class="row d-flex align-items-center mb-0">
                      <div class="col mb-0">
                        <h6><b>Gastos</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-4 text-danger">
                            <b>[[ invoiceDataJSON.total_expenses_amount ]] €</b>
                        </h3>
                      </div>
                      <div class="col mb-0">
                        <h6><b>IVA</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                          [[ invoiceDataJSON.total_expenses_vat ]] €
                        </h3>
                      </div>
                      <div class="col mb-0">
                        <h6><b>IRPF</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                            [[ invoiceDataJSON.total_expenses_irpf ]] €
                        </h3>
                      </div>
                      {% if seller.eqtax == True %}
                        <div class="col mb-0">
                          <h6><b>RE.EQ</b></h6>
                          <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                            [[ invoiceDataJSON.total_expenses_eqtax ]] €
                          </h3>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div> -->
          <div class="col-md-12 col-lg-12 col-xl-4">
            <div class="card theme-bg3 bitcoin-wallet">
              <div class="card-block">
                <div class="row d-flex align-items-center mb-0">
                  <div class="col col-12 mb-0">
                    <div class="d-flex justify-content-between">
                      <h6 class="text-white"><b>Beneficios</b></h6>
                      {% if is_self_employed %}
                        <h4 class="text-white"><b>[[ invoiceDataJSON.regime ]]</b></h4>
                      {% endif %}
                    </div>
                    <h3 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      <b>[[ invoiceDataJSON.total_profit_amount ]] € &nbsp;</b>
                      <i :class="['def-position', iconClassCheck, 'f-26', 'm-l-8']"></i>
                    </h3>
                  </div>
                  <div class="col mb-0">
                    <h6 class="text-white"><b>IVA</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_profit_vat ]] €
                    </h4>
                  </div>
                  <div class="col mb-0">
                    <h6 class="text-white"><b>IRPF</b></h6>
                    <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                      [[ invoiceDataJSON.total_profit_irpf ]] €
                    </h4>
                  </div>
                  {% if seller.eqtax == True %}
                    <div class="col mb-0">
                      <h6 class="text-white"><b>RE.EQ</b></h6>
                      <h4 class="f-w-300 d-flex align-items-center mb-4 text-white">
                        [[ invoiceDataJSON.total_profit_eqtax ]] €
                      </h4>
                    </div>
                  {% endif %}
                </div>
                <i class="fas fa-euro-sign f-70 text-white"></i>
              </div>
            </div>
          </div>
          <!-- <div class="col-4 mb-0">
                <div class="card mb-0">
                  <div class="card-block mb-0">
                    <div class="row d-flex align-items-center mb-0">
                      <div class="col mb-0">
                        <h6><b>Beneficios</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-4">
                            <b>[[ invoiceDataJSON.total_profit_amount ]] €</b>
                        </h3>
                      </div>
                      <div class="col mb-0">
                        <h6><b>IVA</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                          [[ invoiceDataJSON.total_profit_vat ]] €
                        </h3>
                      </div>
                      <div class="col mb-0">
                        <h6><b>IRPF</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                            [[ invoiceDataJSON.total_profit_irpf ]] €
                        </h3>
                      </div>
                      {% if seller.eqtax == True %}
                        <div class="col mb-0">
                          <h6><b>RE.EQ</b></h6>
                          <h3 class="f-w-300 d-flex align-items-center mb-4 text-muted">
                            [[ invoiceDataJSON.total_profit_eqtax ]] €
                          </h3>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div> -->
        </div>
      </div>
    {% endif %}
    <!-- Totals | END -->

    {% comment %}
        <!-- NIF Info | START  -->
        <div class="col-12 mt-2 mb-2">
          <div class="card mb-0">
            <div class="card-block mb-0">
              <div class="row d-flex align-items-center mb-0">
                <div class="col">
                  <h6><b>NIF SELLER</b></h6>
                  <h3 class="f-w-300 d-flex align-items-center mb-1 text-muted">
                    {% if seller and seller.nif_registration %}
                      <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Copiar" data-text="{{ seller.nif_registration }}">
                        {{ seller.nif_registration }}
                      </span>
                    {% else %}
                      - Sin NIF -
                    {% endif %}
                  </h3>
                  <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                    {% if seller and seller.contracted_accounting == True %}
                      Contabilidad: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                    {% else %}
                      Contabilidad: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                    {% endif %}
                  </h5>
                  <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                    {% if seller and seller.oss == True %}
                      OSS: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                    {% else %}
                      OSS: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                    {% endif %}
                    &nbsp;  &nbsp;
                    {% if seller and seller.eqtax == True %}
                      R.EQ: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                    {% else %}
                      R.EQ: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                    {% endif %}
                  </h5>
                </div>
                {% if seller_vat %}
                  {% for sv in seller_vat %}
                    <div class="col" style="border-left: 4px solid #F5F7FA; height: auto;">
                      <h6><b>NIF {{sv.vat_country}}</b></h6>
                      <h3 class="f-w-300 d-flex align-items-center mb-1 text-muted">
                        {% if sv and sv.vat_number %}
                          <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Copiar" data-text="{{ object.nrc }}">
                            {{ sv.vat_number }}
                          </span>
                        {% else %}
                          - Sin NIF -
                        {% endif %}
                      </h3>
                      <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                        {% if sv and sv.is_contracted == True %}
                          Contabilidad: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                        {% else %}
                          Contabilidad: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                        {% endif %}
                      </h5>
                    </div>
                  {% endfor %}
                {% endif%}
              </div>
            </div>
          </div>
        </div>
        <!-- NIF Info | END  -->
        {% endcomment %}

    <!-- Checkbox Selection and pagination -->
    <div class="col-12 mb-2">
      <div class="row justify-content-between">
        <div class="col-xl-4 col-lg-5 col-md-6 col-sm-12">
          <form method="post" id="formSelect" enctype="multipart/form-data"
                action="{% url 'app_invoices:seller_invoices_massive' seller.shortname %}">
            {% csrf_token %}
            <div class="col-12" id="massiveAction" style="display:none;">
              <div class="col-12 d-flex">
                <select class="form-control form-select" id="selectValue" onchange="urlForm()">
                  <option value="empty">Selecciona una acción múltiple</option>
                  <option value="delete">Eliminar facturas selecionadas</option>
                  <option value="pending">Cambiar estado a Pendiente</option>
                  <option value="revision-pending">Cambiar estado a Revisión Pendiente</option>
                  <option value="discard">Cambiar estado a Descartada</option>
                  <option value="revised">Cambiar estado a Revisada</option>
                  <option value="download">Descargar facturas</option>
                </select>
                <button class="btn btn-primary mx-2 my-auto disabled " style="display:block; width: 120px;"
                        id="enabledButton" type="submit">
                  <b>Confirmar</b>
                </button>
                <!-- Modal Trigger-->
                <button type="button" class="btn btn-danger mx-2 my-auto" style="display:none; width: 120px;"
                        id="deleteButton" data-bs-toggle="modal" data-bs-target="#modal">
                  <b>Eliminar</b>
                </button>
                <input type="hidden" name="selectAction" id="selectAction">
                <input type="hidden" name="selectedPk" id="selectedPk">
              </div>
              <footer id="footer" style="color:red; display:none;">
                <b>* No está permitido eliminar facturas revisadas</b>
              </footer>
            </div>
            <!-- Modal Disagreed-->
            <div class="modal fade " id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
                 aria-hidden="true">
              <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">Eliminar</h5>
                  </div>
                  <div class="modal-body">
                    <div class="col form-group form-check p-3">
                      <p><b><h4 style="text-align: center;">¿Está seguro que desea eliminar las facturas
                        seleccionadas?</h4></b></p>
                    </div>
                  </div>
                  <div class="modal-footer d-flex justify-content-center">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal"
                            aria-label="Close">Cancelar
                    </button>
                    <button type="submit" id="submitButton" class="btn btn-danger"><b>Eliminar</b></button>
                  </div>
                </div>
              </div>
            </div>
          </form>
          {% comment %}
              <div class="col">
                <div id="export" class="text-end">
                  <div style="display: none;" v-show="true">
                    <div v-if="inputExportCSV == false">
                      <button class="btn btn-primary" type="button" id="export-csv-btn" onclick="onClickExportCSV()">
                        Exportar a CSV
                      </button>
                    </div>
                    <div v-if="inputExportCSV == true">
                      <button class="btn btn-primary" type="button" disabled>
                        <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                        Generando CSV...
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {% endcomment %}
        </div>

        <div class="col-auto">
          <div class="d-inline-flex">
            <h6 style="padding: 10px 16px;">Elementos por página: </h6>
            <div class="col-auto">
              <select class="form-control form-select" name="show" id="show" onchange="filter()">
                <option value="50" default>50</option>
                <option value="100">100</option>
                <option value="200">200</option>
                <option value="-1">Todos</option>
              </select>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Tabla Listado Facturas | START -->
    <div class="col-12 user-profile-list">
      <table id="invoices-table" class="table table-striped table-bordered dt-responsive nowrap mt-2 border"
             style="width:100%">
        <thead class="table-head">
        <tr>
          <th><input type="checkbox" name="select_all" id="select_all"></th>
          <th>ID</th>
          <th>Estado</th>
          <th>Número</th>
          <th>Cliente</th>
          <th>Cuenta Ingresos</th>
          <th>Proveedor</th>
          <th>Cuenta Gastos</th>
          <th>Cliente/Proveedor</th>
          <th>Cuenta Contable</th>
          <th>Fecha Contabilización</th>
          <th>Fecha Factura</th>
          <th>Fecha Expedición</th>
          <th>Fecha</th>
          <th>País IVA</th>
          <th>Pais IVA</th>
          <th>País Salida</th>
          <th>Categoría</th>
          <th>Tipo</th>
          <th>Tipo de Transacción</th>
          <th>IAE</th>
          <th>IRPF (€)</th>
          <th style="width:5%;">Re.EQ (€)</th>
          <th>Generada/Subida</th>
          <th>JSON VATS</th>
          <th>IVA 2,1%(€)</th>
          <th>IVA 3%(€)</th>
          <th>IVA 4%(€)</th>
          <th>IVA 4,8%(€)</th>
          <th>IVA 5%(€)</th>
          <th>IVA 5,5%(€)</th>
          <th>IVA 6%(€)</th>
          <th>IVA 7%(€)</th>
          <th>IVA 8%(€)</th>
          <th>IVA 9%(€)</th>
          <th>IVA 9,5%(€)</th>
          <th>IVA 10%(€)</th>
          <th>IVA 12%(€)</th>
          <th>IVA 13%(€)</th>
          <th>IVA 13,5%(€)</th>
          <th>IVA 14%(€)</th>
          <th>IVA 15%(€)</th>
          <th>IVA 17%(€)</th>
          <th>IVA 18%(€)</th>
          <th>IVA 19%(€)</th>
          <th>IVA 20%(€)</th>
          <th>IVA 21%(€)</th>
          <th>IVA 22%(€)</th>
          <th>IVA 23%(€)</th>
          <th>IVA 24%(€)</th>
          <th>IVA 25%(€)</th>
          <th>IVA 27%(€)</th>
          {% comment %}
              <th>IVA  4%(€)</th>
              <th>IVA 10%(€)</th>
              <th>IVA 21%(€)</th>
              {% endcomment %}
          <th style="width:5%;">IVA (€)</th>
          <th style="width:5%;">Base (€)</th>
          <th style="width:5%;">Total (€)</th>
          <th style="width:5%;">Tags</th>
          <th style="width:5%;">Notes</th>
          <th style="width:5%;">Discard Reason</th>
          <th style="width:5%;">Discard Reason Notes</th>
          <th style="width:5%;">Info</th>
          <th style="width:5%;">Acciones</th>
        </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
    <!-- Tabla Listado Facturas | END -->

    <!-- Modal to show info notes and others -->
    <div class="modal fade" id="infoModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header justify-content-center">
            <h4 class="mt-2">INFORMACIÓN DE LA FACTURA</h4>
          </div>
          <div class="modal-body">
            <div class="info-content">
              <p style="font-size: 16px;" id="tags"></p>
              <p style="font-size: 16px;" id="notes"></p>
              <p style="font-size: 16px;" id="notes_private"></p>
              <p style="font-size: 16px;" id="discard"></p>
              <p style="font-size: 16px;" id="discard_notes"></p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary mt-2" data-bs-dismiss="modal">Cerrar</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal to show info notes and others -->

    <!-- modal for showing loading -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header justify-content-center">
            <h4 class="mt-2">Descargando</h4>
          </div>
          <div class="modal-body">

            <!-- <div class="d-flex flex-column align-items-center">
              <svg version="1.1" viewBox="-1 -1 102 102" enable-background="new 0 0 200 200" xml:space="preserve">

                <path id="circle" class="circle"
                      d="M0 50
                         A50 50 0 1 1 100 50
                         A50 50 0 1 1 0   50"  />

               <path id="file" class="file"
                     d="M21 2  H5 v28 h22 l0 -22 z
                        M25 28 H7 V4  h12 v6     h6    z"/>

               <?
               <polygon id="folder-top" class="folder-top"
                     points="25,8 25,6 14,6 14,4 5,4 5,8"/>
               ?>
               <?
               <path id="folder-square" class="folder-square"
                     d="M3,10v18h26V10L3,10z M27,26H5V12h22V26z"/>
               ?>
               <?<path class="folder-top" d="M5 4 h8 v2 h12 v2 h-20 z"/>?>
             </svg>

             <h4 id="textDownloadingAnimation" class="mt-2">Preparando archivos...</h4>
            </div> -->


            <div id="spinner-animation"
                 class="d-flex justify-content-center align-items-center text-center mb-3 d-none">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <div id="folder-animation" class="folder-animation-wrapper mb-3">
              <div class="file-animation file-logo">
                <div class="page page1">
                  <p class="mt-1" style="font-size: 12px;">Factura 1</p>
                </div>
                <div class="page page2">
                  <p class="mt-1" style="font-size: 12px;">Factura 2</p>
                </div>
                <div class="page page3">
                  <p class="mt-1" style="font-size: 12px;">Factura 3</p>
                </div>
              </div>
            </div>
            <div class="d-flex-column justify-content-center align-items-center text-center">
              <h4 id="textDownloadingAnimation" class="mb-2">Preparando archivos...</h4>
              <p style="font-size: 16px;">Por favor, no cierres ni recargues la página</p>
            </div>

          </div>

        </div>
      </div>
    </div>
    <!-- modal for showing loading -->
  </div>
{% endblock content %}
{% block javascripts %}
  <!-- DEBUG  -->
  <script type="text/javascript">
    const debug = true;
  </script>
  <!-- DEBUG  -->

  <!-- JQUERY DATA TABLE  -->.
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{% static 'assets/js/loading.js' %}"></script>

  <!-- https://datatables.net/extensions/select/examples/initialisation/checkbox.html -->
  <script type="text/javascript">

    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    let table = null;
    let retryAttempts = 0;
    let timer;
    // let exportCSV = false; // Use inputExportCSV from VUE

    const multiCheckbox = document.getElementById('multiple-transactions');
    multiCheckbox.addEventListener('change', () => filter());

    const multipledepartureCountries = document.getElementById('multiple-departurecountries');
    multipledepartureCountries.addEventListener('change', () => filter());

    const multipleTaxCountries = document.getElementById('multiple-taxcountries');
    multipleTaxCountries.addEventListener('change', () => filter());

    const search_invoice = document.getElementById('search');

    search_invoice.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        clearTimeout(timer);
        filter();
      }
    });

    search_invoice.addEventListener("input", () => {
      clearTimeout(timer);
      timer = setTimeout(filter, 500);
    });

    const ajaxData = (d) => {
      if (debug) console.log('ajaxData | d: ', d);

      let economic_activity = null;
      if (document.getElementById("economic_activity")) {
        economic_activity = document.getElementById("economic_activity").value;
      }
      let status = document.getElementById("status").value;
      let year = document.getElementById("year").value;
      let month = document.getElementById("month").value;
      let country = document.getElementById("multiple-taxcountries").dataset.value;
      let departure_country = document.getElementById("multiple-departurecountries").dataset.value;
      let invoice_type = document.getElementById("invoice_type").value;
      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;

      let search = search_invoice.value;
      let show = document.getElementById("show").value;
      let fc_transfer = false;
      let category = "";
      let tParams = "";

      {% if category and category.pk %}
        category = "{{category.pk}}";
      {% endif %}

      {% if transfer and transfer == True %}
        fc_transfer = true;
      {% endif %}

      if (economic_activity) {
        if (debug) console.log('filterDT | economic_activity: ', economic_activity);
        d.iae = economic_activity;
        tParams += "&economic_activity=" + economic_activity;
      }

      if (status) {
        if (debug) console.log('filterDT | estado: ', status);
        d.status_id = status;
        tParams += "&status=" + status;
      }

      if (year) {
        if (debug) console.log('filterDT | year: ', year);
        d.expedition_date_year = year;
        tParams += "&year=" + year;
      }

      if (month) {
        if (debug) console.log('filterDT | month: ', month);
        d.expedition_date_month = month;
        tParams += "&month=" + month;
      }

      if (country) {
        country = JSON.stringify(country.split(", "));
        d.tax_country_id = country;
        tParams += "&country=" + country;
      }

      if (departure_country) {
        departure_country = JSON.stringify(departure_country.split(", "));
        d.departure_country_id = departure_country;
        tParams += "&departure_country=" + departure_country;
      }

      if (invoice_type) {
        if (debug) console.log('filterDT | invoice_type: ', invoice_type);
        d.invoice_type = invoice_type;
        tParams += "&invoice_type=" + invoice_type;
      }

      // if (transaction) {
      //   const selectedValues = Array.from(transactionElement.selectedOptions).map(option => option.value);

      //     if (debug) {
      //       console.log('filterDT | transaction: ', transaction);
      //       console.log('filterDT | transaction multiple: ', selectedValues);
      //        console.log('filterDT | tParams: ', tParams);
      //     }
      //     d.transaction_type_id = JSON.stringify(selectedValues);
      //     tParams += '&transaction=' + JSON.stringify(selectedValues);
      // }

      if (multiple_transactions) {
        const selectedValues = multiple_transactions.split(', ');
        d.transaction_type_id = JSON.stringify(selectedValues);
        tParams += '&transaction=' + JSON.stringify(selectedValues);
      }

      if (search) {
        if (debug) console.log('filterDT | search: ', search);
        d.search = search.trim();
        tParams += "&search=" + search.trim();
      }

      if (category) {
        if (debug) console.log('filterDT | category: ', category);
        d.invoice_category_id = category;
      }

      if (fc_transfer) {
        if (debug) console.log('filterDT | fc_transfer: ', fc_transfer);
        d.fc_transfer = fc_transfer;
        tParams += "&transfer=" + fc_transfer;
      }

      {% comment %}
      if (show) {
          if (debug) console.log('filterDT | show: ', show);
          d.length = show;
      }
      {% endcomment %}

      if (d.order.length > 0) {
        orderby = [];
        for (const o of d.order) {
          name = d.columns[o.column].data;
          if (name == 'contact') {
            orderby.push({"dir": o.dir, "name": "customer"});
            orderby.push({"dir": o.dir, "name": "provider"});
          } else if (name == 'account_sales_expenses') {
            orderby.push({"dir": o.dir, "name": "account_sales"});
            orderby.push({"dir": o.dir, "name": "account_expenses"});
          } else if (name == 'date') {
            orderby.push({"dir": o.dir, "name": "expedition_date"});
            orderby.push({"dir": o.dir, "name": "invoice_date"});
            orderby.push({"dir": o.dir, "name": "accounting_date"});
          } else if (name == 'status') {
            orderby.push({"dir": o.dir, "name": "status__order"});
          } else {
            orderby.push({"dir": o.dir, "name": name});
          }
          // orderby.push({ "dir": o.dir, "name": "status__order"});
          orderby.push({"dir": 'desc', "name": "pk"});
        }
        if (debug) console.log('filterDT | orderBy: ', orderby);
        d.order = JSON.stringify(orderby);
      }

      getTotals(tParams);
      return d;

    }

    const renderNumber = (data, type = null, row = null) => {
      if (data == null || data == undefined || data == "" || data == 0) {
        data = 0.0;
      } else {
        data = parseInt(parseFloat(data) * 100) / 100;
      }
      return data;
    }

    const renderVAT = (vatNumber, vats) => {
      let r = 0;

      vatint = parseInt(vatNumber.toString()).toString();
      vat1dec = parseFloat(vatNumber.toString()).toFixed(1).toString();
      vat2dec = parseFloat(vatNumber.toString()).toFixed(2).toString();

      if (vats) {
        if (vats[vatint]) {
          r = vats[vatint];
        } else if (vats[vat1dec]) {
          r = vats[vat1dec];
        } else if (vats[vat2dec]) {
          r = vats[vat2dec];
        }
      }

      {% comment %} console.log("vatint: ", vatint, " | vat1dec: ", vat1dec, " | vat2dec: ", vat2dec, " | r: ", r); {% endcomment %}
      return r;
    }

    const createDT = () => {
      const seller = dj.value.seller;
      const irpf_visibility = seller && seller.contracted_accounting == true ? true : false;
      const eqtax_visibility = seller && seller.contracted_accounting == true && seller.eqtax == true ? true : false;
      let jsonvat = null;
      let vats = {};
      table = $('#invoices-table').DataTable({
        "serverSide": true,
        "ajax": {
          "url": "{% url 'app_invoices:seller_invoices_dt' seller.shortname %}",
          "data": function (d) {
            ajaxData(d);
          },
          "error": function(xhr, error, thrown) {
            if(xhr.status === 500 && retryAttempts < 3) {
                // retryAttempts + 1
                retryAttempts++;
                // Reload DT
                setTimeout(function() { 
                  table.ajax.reload(null, false) 
                }, 1000 ); // Retry after 1 seconds

            } else {
                // Load Toast
                const Toast = Swal.mixin({
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 10000,
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });
                // Show Toast Error
                Toast.fire({
                  icon: 'error',
                  title: 'Ha Ocurrido un error en la carga de datos, por favor recargue la página.'
                });
            }
          },
        },
        "language": {
          "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
        },
        "searching": false,
        "lengthChange": false,
        "lengthMenu": [[50, 100, 200, -1], [50, 100, 200, 'Todos']],
        "order": [[2, 'asc'], [1, 'desc'],],
        "select": {"style": 'multi', "selector": 'custom-checkbox'},
        "columns": [
          {
            "data": null,
            "className": 'select-checkbox',
            "visible": true,
            "orderable": false,
            "render": function (data, type, full, meta) {

              if (type === 'display') {
                return '<input type="checkbox" class="custom-checkbox" data-select="true" id="miId">';
              }

              return data;
            }
          },
          {"data": "pk", "visible": true},
          {
            "data": "status", "render": function (data, type, row) {
              // if (debug) console.log(row);
              let html = "";
              if (row.status) {
                let bg = "";
                if (row.status == "Pendiente" || row.status == "Revision Pendiente") {
                  bg = "bg-warning";
                } else if (row.status == "Revisada") {
                  bg = "bg-success";
                } else if (row.status == "Descartada") {
                  bg = "bg-danger";
                }
                html = `<span class="rounded ${bg} text-white p-1">
                            <b> &nbsp; ${row.status} &nbsp; </b>
                          </span>`;
              }
              return html;
            }
          },
          {"data": "reference", className: "column-width-limit"},
          {"data": "customer", "visible": false},
          {"data": "account_sales", "visible": false},
          {"data": "provider", "visible": false},
          {"data": "account_expenses", "visible": false},
          {
            "data": "contact", className: "column-width-limit", "render": function (data, type, row) {
              let contact = "";
              if (row.customer) {
                contact = row.customer;
              } else if (row.provider) {
                contact = row.provider;
              }
              return contact;
            }
          },
          {
            "data": "account_sales_expenses", className: "column-width-limit", "render": function (data, type, row) {
              let account = "";
              if (row.account_sales) {
                account = row.account_sales;
              } else if (row.account_expenses) {
                account = row.account_expenses;
              }
              if (account == null || account == undefined || account == "") {
                account = "";
              }
              return account;
            }
          },
          {"data": "accounting_date", "orderable": false, "visible": false},
          {"data": "invoice_date", "orderable": false, "visible": false},
          {"data": "expedition_date", "orderable": false, "visible": false},
          {
            "data": "date", "type": "date", "render": function (data, type, row) {
              let date = "";
              if (row.accounting_date) {
                date = row.accounting_date;
              } else if (row.expedition_date) {
                date = row.expedition_date;
              } else if (row.invoice_date) {
                date = row.invoice_date;
              }
              // cambia el formato de la fecha de 2023-07-01 a 01/jul/2023
              if (date) {
                const [year, month, day] = date.split('-');
                const monthNames = [
                  "ene", "feb", "mar",
                  "abr", "may", "jun", "jul",
                  "ago", "sep", "oct",
                  "nov", "dic"
                ];
                date = day + '/' + monthNames[parseInt(month) - 1] + '/' + year;
              }
              {% comment %}
              if (date && date != "" && date.length >= 8) {
                const newDate = new Date(date);
                const day = newDate.getDate().toString().padStart(2, '0');
                const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
                const year = newDate.getFullYear().toString();
                date = day + '/' + month + '/' + year;
              }
              {% endcomment %}
              return date;
            }
          },
          {"data": "tax_country_id"},
          {"data": "tax_country", "visible": false},
          {"data": "departure_country_id"},
          {"data": "invoice_category", "visible": false},
          {"data": "invoice_type"},
          {
            "data": "transaction_type", render: function (data, type, row) {
              let transaction = "(Sin categorizar)";
              if (row.transaction_type) {
                transaction = row.transaction_type;
              }
              return transaction;
            }
          },
          {"data": "iae", "visible": false},
          {"data": "total_irpf_euros", "visible": irpf_visibility, "render": renderNumber},
          {"data": "total_eqtax_euros", "visible": eqtax_visibility, "render": renderNumber},
          {"data": "is_generated", "visible": false},
          {
            "data": "json_vat", "visible": false, "render": function (data, type, row) {
              vats = {};
              jsonvat = data.replaceAll("&quot;", '"');
              if (jsonvat && jsonvat != "" && jsonvat != "null") {
                vats = JSON.parse(jsonvat);
              }
              return data;
            }
          },
          {
            "data": "total_vat_euros_2_1", "visible": false, "render": function (data, type, row) {
              return renderVAT("2.1", vats);
            }
          },
          {
            "data": "total_vat_euros_3", "visible": false, "render": function (data, type, row) {
              return renderVAT("3", vats);
            }
          },
          {
            "data": "total_vat_euros_4", "visible": false, "render": function (data, type, row) {
              return renderVAT("4", vats);
            }
          },
          {
            "data": "total_vat_euros_4_8", "visible": false, "render": function (data, type, row) {
              return renderVAT("4.8", vats);
            }
          },
          {
            "data": "total_vat_euros_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("5", vats);
            }
          },
          {
            "data": "total_vat_euros_5_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("5.5", vats);
            }
          },
          {
            "data": "total_vat_euros_6", "visible": false, "render": function (data, type, row) {
              return renderVAT("6", vats);
            }
          },
          {
            "data": "total_vat_euros_7", "visible": false, "render": function (data, type, row) {
              return renderVAT("7", vats);
            }
          },
          {
            "data": "total_vat_euros_8", "visible": false, "render": function (data, type, row) {
              return renderVAT("8", vats);
            }
          },
          {
            "data": "total_vat_euros_9", "visible": false, "render": function (data, type, row) {
              return renderVAT("9", vats);
            }
          },
          {
            "data": "total_vat_euros_9_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("9.5", vats);
            }
          },
          {
            "data": "total_vat_euros_10", "visible": false, "render": function (data, type, row) {
              return renderVAT("10", vats);
            }
          },
          {
            "data": "total_vat_euros_12", "visible": false, "render": function (data, type, row) {
              return renderVAT("12", vats);
            }
          },
          {
            "data": "total_vat_euros_13", "visible": false, "render": function (data, type, row) {
              return renderVAT("13", vats);
            }
          },
          {
            "data": "total_vat_euros_13_5", "visible": false, "render": function (data, type, row) {
              return renderVAT("13.5", vats);
            }
          },
          {
            "data": "total_vat_euros_14", "visible": false, "render": function (data, type, row) {
              return renderVAT("14", vats);
            }
          },
          {
            "data": "total_vat_euros_15", "visible": false, "render": function (data, type, row) {
              return renderVAT("15", vats);
            }
          },
          {
            "data": "total_vat_euros_17", "visible": false, "render": function (data, type, row) {
              return renderVAT("17", vats);
            }
          },
          {
            "data": "total_vat_euros_18", "visible": false, "render": function (data, type, row) {
              return renderVAT("18", vats);
            }
          },
          {
            "data": "total_vat_euros_19", "visible": false, "render": function (data, type, row) {
              return renderVAT("19", vats);
            }
          },
          {
            "data": "total_vat_euros_20", "visible": false, "render": function (data, type, row) {
              return renderVAT("20", vats);
            }
          },
          {
            "data": "total_vat_euros_21", "visible": false, "render": function (data, type, row) {
              return renderVAT("21", vats);
            }
          },
          {
            "data": "total_vat_euros_22", "visible": false, "render": function (data, type, row) {
              return renderVAT("22", vats);
            }
          },
          {
            "data": "total_vat_euros_23", "visible": false, "render": function (data, type, row) {
              return renderVAT("23", vats);
            }
          },
          {
            "data": "total_vat_euros_24", "visible": false, "render": function (data, type, row) {
              return renderVAT("24", vats);
            }
          },
          {
            "data": "total_vat_euros_25", "visible": false, "render": function (data, type, row) {
              return renderVAT("25", vats);
            }
          },
          {
            "data": "total_vat_euros_27", "visible": false, "render": function (data, type, row) {
              return renderVAT("27", vats);
            }
          },
          {"data": "vat_euros"},
          {
            "data": "amount_euros", "render": function (data, type, row) {
              let amount_euros = "";
              if (row.transaction_type == "Import DUA") {
                if (row.status != "Pendiente" && row.status != "Revision Pendiente") {
                  amount_euros = "-";
                }
              } else if (row.amount_euros) {
                amount_euros = row.amount_euros
              }
              return amount_euros;
            }
          },
          {
            "data": "total_euros_concp", "render": function (data, type, row) {
              let total_euros_concp = "";
              if (row.transaction_type == "Import DUA") {
                total_euros_concp = row.vat_euros;
              } else if (row.total_euros_concp) {
                total_euros_concp = row.total_euros_concp
              }
              return total_euros_concp;
            }
          },
          {"data": "tags", "orderable": false, "visible": false},
          {"data": "notes", "orderable": false, "visible": false},
          {"data": "discard_reason", "orderable": false, "visible": false},
          {"data": "discard_reason_notes", "orderable": false, "visible": false},
          {
            "data": "notes_private", "orderable": false, "render": function (data, type, row) {
              let info = "";
              const status = row.status
              const tags = row.tags;
              const notes = row.notes;
              let discard_reason = "";
              let discard_reason_notes = "";

              if (status == "Descartada") {
                discard_reason = row.discard_reason;
                if (discard_reason == "Otro") {
                  discard_reason_notes = row.discard_reason_notes;
                }
              }

              const infoData = {
                tags: tags,
                notes: notes,
                notes_private: data,
                discard_reason: discard_reason,
                discard_notes: discard_reason_notes,
              }
              if (data || tags || notes || discard_reason) {
                const infoJsonString = JSON.stringify(infoData).replace(/"/g, '&quot;');
                info = '<i id="triggerModal" class="cursor-pointer fas fa-info fa-xl" data-toggle="modal" data-target="#disagreedModal" onclick="showInfo(\'' + infoJsonString + '\')" data-row-id="' + row.pk + '" data-content="' + data + '"></i>';
              } else {
                info = '<i class="fas fa-info fa-xl" style="color: #999999;"></i>';
              }
              return info;
            }
          },
          {
            "data": "file", "orderable": false, "render": function (data, type, row) {
              let html = "";
              let button1 = "";
              let button2 = "";
              let button3 = "";
              if (row.pk) {
                if('{{ perms.users.is_superuserAPP }}' == 'True'){
                button1 = `
                    <a class="btn btn-success btn-icon tooltip-wrapper tooltip-button" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Categorizar Factura">
                      <i class="fa-solid fa-pen-to-square"></i>
                    </a>
                  `;}else if('{{ perms.invoices.view_invoice }}' == 'True'){
                    button1 = `
                    <a class="btn btn-success btn-icon tooltip-wrapper tooltip-button" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Ver Factura">
                      <i class="fas fa-eye"></i>
                    </a>
                  `;}
              }
              if (row.file) {
                button2 = `
                    <a class="btn btn-info btn-icon tooltip-wrapper tooltip-button " href="/media/${row.file}"
                        target="_blank"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="Descargar Factura"
                        download >
                      <i class="fa-solid fa-download"></i>
                    </a>
                  `;
              } else if (row.is_generated == "True") {
                button2 = `
                    <a class="btn btn-info btn-icon tooltip-wrapper tooltip-button" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}/file/"
                        target="_blank"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="Descargar Factura"
                        download >
                      <i class="fa-solid fa-download"></i>
                    </a>
                  `;
              }
              if (row.pk) {
                if (row.status != "Revisada") {
                  if('{{ perms.users.is_superuserAPP }}' == 'True'){
                  button3 = `
                      <a class="btn btn-danger btn-icon tooltip-wrapper tooltip-button" data-bs-original-title="Eliminar Factura" href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}/delete">
                        <i class="feather icon-trash-2"></i>
                      </a>
                    `;}
                }
              }
              if (row.pk || row.file) {
                html = `
                  <div class="text-center justify-content-center">
                      <button type="button" class="btn btn-secondary border  text-nowrap" data-bs-toggle ="dropdown" aria-expanded="false">
                        <i class="fa-solid fa-xl fa-ellipsis m-0"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end">
                        <li class= mb-1>${button1}</li>
                        <li class= mb-1>${button2}</li>
                        <li>${button3}</li>
                      </ul>
                    </div>
                  `;
                htmlseparated = `
                    ${button1}
                    ${button2}
                    ${button3}
                  `;
              }
              return htmlseparated;
            }
          },
        ],
        "language": {
          "lengthMenu": "_MENU_",
          "zeroRecords": "No se han encontrado facturas.",
          "info": "_START_ a _END_ de un total de _TOTAL_",
          "search": "Buscar:",
          "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
          "infoFiltered": "",
          "emptyTable": "Cargando...",
          "paginate": {
            "first": "Primero",
            "last": "Último",
            "previous": "Anterior",
            "next": "Siguiente"
          },
        },
        "drawCallback": function (settings, json) {
          //  console.log("Opciones completas de DataTables:", settings);
          $('#select_all').prop("checked", false);
          arrayRowData = [];
          arrayStatus = [];
          document.getElementById("massiveAction").style.display = "none";
        }
      });
      {% comment %} filter(); {% endcomment %}

      $('#page-length').change(function () {
        table.page.len($(this).val()).draw();
      });

      $(table.table().node()).on('xhr.dt', function (e, settings, json) {
        if (inputExportCSV.value == true) {
          exportTableToCSV();
        }
      });
    }

    $(document).ready(function () {
      createDT();
      massiveOptionPerm();
    });

    let arrayRowData = [];
    let arrayStatus = [];

    $('#invoices-table').on('click', 'input.custom-checkbox[data-select="true"]', function (e) {
      e.stopPropagation(); // Evita que el clic se propague

      var row = $(this).closest('tr');
      var dataTable = $('#invoices-table').DataTable();

      if (dataTable.row(row).nodes().to$().hasClass('selected')) {
        dataTable.row(row).deselect();
      } else {
        dataTable.row(row).select();
      }
    });

    $('#select_all').on('change', function () {
      var isChecked = $(this).prop('checked');
      var rows = table.rows({page: 'current'}).nodes();

      // Selecciona o deselecciona todas las filas según el estado de la casilla
      $(rows).find('input[type="checkbox"]').prop('checked', isChecked);

      // Dispara el evento 'select.dt' después de seleccionar todas las filas
      if (isChecked) {
        table.rows({page: 'current'}).select();
      } else {
        table.rows({page: 'current'}).deselect();
      }
    });

    // Select rows
    $('#invoices-table').on('select.dt', function (e, dt, type, indexes) {
      let rowData = dt.rows(indexes).data().toArray();
      let status = dt.rows(indexes).data().toArray();

      arrayRowData = arrayRowData.concat(rowData.map(row => row.pk));
      arrayStatus = arrayStatus.concat(status.map(row => row.status));

      document.getElementById("selectedPk").value = JSON.stringify(arrayRowData);
      if (arrayRowData.length > 0) {
        document.getElementById("massiveAction").style.display = "block";
      }
      urlForm();
    });

    // Deselect rows
    $('#invoices-table').on('deselect.dt', function (e, dt, type, indexes) {
      let rowDataDelete = dt.rows(indexes).data().toArray();
      let statusDelete = dt.rows(indexes).data().toArray();
      let pkValue = rowDataDelete[0].pk;
      let index = arrayRowData.indexOf(pkValue);

      arrayStatus.splice(index, 1);
      arrayRowData = arrayRowData.filter(row => !rowDataDelete.some(r => r.pk === row));

      document.getElementById("selectedPk").value = JSON.stringify(arrayRowData);
      if (arrayRowData.length == 0) {
        document.getElementById("massiveAction").style.display = "none";
      }
      urlForm();
    });

    const filter = () => {
      let show = document.getElementById("show").value;
      if (show) {
        table.page.len(show);
      }
      retryAttempts = 0;
      table.draw();
    }

    const getTotals = (params) => {
      let p = params;

      if (!p || p == undefined || p == null || p == "") {
        p = "";
      } else if (p.charAt(0) == "&") {
        p[0] = "?";
      }

      invoiceDataJSON.value = {
        "status": "revised",
        "country": [],
        "year": "all",
        "economic_activity": "all",
        "month": "all",
        "invoices_count": 0,
        "total_sales": 0,
        "total_sales_amount": 0,
        "total_sales_vat": 0,
        "total_sales_eqtax": 0,
        "total_sales_irpf": 0,
        "total_expenses": 0,
        "total_expenses_amount": 0,
        "total_expenses_vat": 0,
        "total_expenses_eqtax": 0,
        "total_expenses_irpf": 0,
        "total_profit_amount": 0,
        "total_profit_vat": 0,
        "total_profit_eqtax": 0,
        "total_profit_irpf": 0
      };

      const url = "{% url 'app_invoices:seller_invoices_json' seller.shortname %}?" + p;
      $.ajax({
        url: url,
        type: "GET",
        dataType: "JSON",
        success: function (data) {
          const s = document.getElementById("status").value.toString().toLowerCase().trim();
          const y = document.getElementById("year").value.toString().toLowerCase().trim();
          const m = document.getElementById("month").value.toString().toLowerCase().trim();
          let c = document.getElementById("multiple-taxcountries");
          c = c && c.dataset.value ? c.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          let dc = document.getElementById("multiple-departurecountries");
          dc = dc && dc.dataset.value ? dc.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const it = document.getElementById("invoice_type").value.toString().toLowerCase().trim();
          let ea = document.getElementById("economic_activity");
          ea = ea ? ea.value.toString().toLowerCase().trim() : null;
          let ms = document.getElementById("multiple-transactions");
          ms = ms && ms.dataset.value ? ms.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const tt = data.transaction_type ? data.transaction_type.join(',').toString().toLowerCase().trim() : null;

          let sameIAE = false;
          let sameStatus = false;
          let sameYear = false;
          let sameMonth = false;
          let sameCountry = false;
          let sameDepartureCountry = false;
          let sameInvoiceType = false;
          let sameMultipleTransactions = false;

          if (ea == data.economic_activity || (data.economic_activity == "all" && !ea)) {
            sameIAE = true;
          }

          if (s == data.status || (data.status == "revised" && !s)) {
            sameStatus = true;
          }

          if (y == data.year || (data.year == "all" && !y)) {
            sameYear = true;
          }

          if (m == data.month || (data.month == "all" && !m)) {
            sameMonth = true;
          }

          if (
            (c && data.country && c === data.country.join(',').toString().toLowerCase().trim()) ||
            (data.country.toString().toLowerCase().trim() == "" && !c)
          ) {
            sameCountry = true;
          }

          if (
            (dc && data.departure_country && dc === data.departure_country.join(',').toString().toLowerCase().trim()) ||
            (data.departure_country.toString().toLowerCase().trim() == "" && !dc)
          ) {
            sameDepartureCountry = true;
          }

          if (it == data.invoice_type || (data.invoice_type == "all" && !it)) {
            sameInvoiceType = true;
          }

          if (
            (ms && data.transaction_type && ms === data.transaction_type.join(',').toString().toLowerCase().trim()) ||
            (data.transaction_type.toString().toLowerCase().trim() == "" && !ms)
          ) {
            sameMultipleTransactions = true;
          } else {
            console.log("ms: ", ms);
            console.log("tt: ", tt);
          }

          if (sameIAE && sameStatus && sameYear && sameMonth && sameCountry && sameDepartureCountry && sameInvoiceType && sameMultipleTransactions) {
            // Aquí se guarda la peticion
            invoiceDataJSON.value = data;
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          // Aquí se ejecuta el código cuando hay un error en la petición
          console.error("Error en la peticion AJAX")
        }
      });

    }

    const onClickExportCSV = () => {
      inputExportCSV.value = true;
      document.getElementById("show").value = -1;
      table.page.len(-1);
      table.draw();
    }

    const exportTableToCSV = async () => {
      // Disable Export CSV
      inputExportCSV.value = false;

      // Obtenemos los datos de la tabla
      let data = table.ajax.json().data;

      // Creamos la variable CSV
      let csv = '\ufeff';

      // Creamos el encabezado de las columnas
      let header = [];

      // Creamos la lista de columnas a excluir
      const exclude = [
        "account_sales_expenses", "Cuenta Contable",
        "contact", "Cliente/Proveedor",
        "invoice_date", "Fecha Factura",
        "date", "Fecha",
        "file", "Acciones",
        "pdf_amazon", "PDF Amazon",
        "json_vat", "JSON VATS",
        "tax_country_id", "País IVA"
      ];

      // Creamos el separador de columnas
      const separator = ";";

      // Obtenemos los country codes y los vat rates
      let countries = [];
      let validVats = [];
      data.forEach(function (row) {
        // console.log(row);
        if (row.tax_country_id && !countries.includes(row.tax_country_id)) {
          countries.push(row.tax_country_id);
        }
      });

      if (countries && countries.length > 0) {
        dj.value.vat_rates.forEach(function (vat) {
          // console.log("vat.country_code: ", vat.country_code);
          // console.log("vat.vat_rates: ", vat.vat_rates);
          if (countries.includes(vat.country_code)) {
            {% comment %} console.log("vat.vat_rates: ", vat.vat_rates); {% endcomment %}
            vat.vat_rates.split(',').forEach(function (vat_rate) {
              if (!validVats.includes(vat_rate.trim())) {
                validVats.push(vat_rate.trim());
              }
            });
          }
        });
      }

      {% comment %} console.log("countries: ", countries); {% endcomment %}
      {% comment %} console.log("validVats: ", validVats); {% endcomment %}
      {% comment %} console.log("exclude: ", exclude); {% endcomment %}

      // Obtenemos los encabezados de las columnas
      table.columns().every(function () {
        let temp_header = [];
        temp_header.push(this.header().innerText);
        for (item of temp_header) {
          if (!exclude.includes(item)) {
            if (item.includes("IVA") && item.includes("%(€)")) {
              validVats.forEach(function (vat_rate) {
                vat_rate = vat_rate.replaceAll(".", ",").replaceAll(" ", "");
                if (item.includes("IVA " + vat_rate + "%(€)")) {
                  header.push(item);
                }
              });
            } else {
              header.push(item);
            }
          }
        }
      });

      // Agregamos los encabezados al string CSV
      csv += header.join(separator) + '\n';

      // Agregamos los datos al string CSV
      data.forEach(function (row) {
        var rowData = [];
        var vats = {};
        // Recorremos cada celda de la fila
        for (var prop in row) {
          if (!exclude.includes(prop)) {
            if (!(prop.includes("€") || prop.includes("euros") || prop.includes("total"))) {
              rowData.push(`"${row[prop]}"`);
            } else if (prop.includes("total_vat_euros_")) {
              let vatNumber = prop.trim().replace("total_vat_euros_", "").trim();
              vatNumber = vatNumber.replace("_", ".").trim();
              if (validVats.includes(vatNumber)) {
                vatValue = renderVAT(vatNumber, vats).toString().replace(',', '').replace('.', ',');
                {% comment %} console.log("Export CSV | vatNumber: ", vatNumber + " | vatValue: ", vatValue); {% endcomment %}
                rowData.push(`"${vatValue}"`);
              }
            } else {
              let data = row[prop];
              data = renderNumber(data);
              let dataEsp = data.toString().replace(',', '').replace('.', ',');
              rowData.push(`"${dataEsp}"`)
            }
          } else if (prop.includes("json_vat")) {
            let jsonvat = row[prop].toString().replaceAll("&quot;", '"');
            if (jsonvat && jsonvat != "" && jsonvat != "null") {
              vats = JSON.parse(jsonvat);
            }
            {% comment %} console.log("Export CSV | vats: ", vats); {% endcomment %}
          }
        }
        csv += rowData.join(separator) + '\n';
      });

      // Descargamos el archivo CSV
      let csvData = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csv.replaceAll("&amp;", "&").replaceAll("&#x27;", "&"));
      //csvData = csvData.replace("&amp;", "&");
      let link = document.createElement('a');
      link.href = csvData;
      {% if seller.shortname %}
        link.download = 'facturas_{{seller.shortname}}.csv';
      {% else %}
        link.download = 'facturas.csv';
      {% endif %}
      link.click();

      // Disable Export CSV
      inputExportCSV.value = false;
    }

    const urlForm = () => {
      let select = document.getElementById("selectValue").value;
      document.getElementById("selectAction").value = select;

      let resultStatus = $.inArray('Revisada', arrayStatus);

      if (select != "empty" && resultStatus == -1) {
        document.getElementById("enabledButton").className = "btn btn-primary mx-2 my-auto";
        document.getElementById("footer").style.display = "none";

        if (select == "delete" && resultStatus != -1) {
          document.getElementById("enabledButton").style.display = "none";
          document.getElementById("deleteButton").style.display = "block";
        } else if (select == "delete" && resultStatus == -1) {
          document.getElementById("footer").style.display = "none";
          document.getElementById("enabledButton").style.display = "none";
          document.getElementById("deleteButton").style.display = "block";
          document.getElementById("enabledButton").className = "btn btn-primary mx-2 my-auto disabled";
        } else {
          document.getElementById("footer").style.display = "none";
          document.getElementById("enabledButton").style.display = "block";
          document.getElementById("deleteButton").style.display = "none";
          document.getElementById("enabledButton").className = "btn btn-primary mx-2 my-auto enabled";
        }

      } else if (select != "empty" && resultStatus != -1 && select != "delete") {
        document.getElementById("enabledButton").className = "btn btn-primary mx-2 my-auto";
        document.getElementById("footer").style.display = "none";
        document.getElementById("deleteButton").style.display = "none";
        document.getElementById("enabledButton").style.display = "block";

      } else {
        document.getElementById("enabledButton").className = "btn btn-primary mx-2 my-auto disabled";
        document.getElementById("footer").style.display = "none";
        document.getElementById("deleteButton").style.display = "none";
        document.getElementById("enabledButton").style.display = "block";
        if (select == "delete") {
          document.getElementById("footer").style.display = "block";
        }

      }
    }

    const formSelect = document.getElementById("formSelect");
    formSelect.addEventListener("submit", function (event) {
      let select = document.getElementById("selectValue").value;
      if (select == "download") {
        const url = formSelect.getAttribute("action");
        const method = formSelect.getAttribute("method");
        const data = new FormData(formSelect);

        fetch(url, {
          method: method,
          body: data,
        }).then(response => {

          const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            didOpen: (toast) => {
              toast.addEventListener('mouseenter', Swal.stopTimer)
              toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
          });

          if (response.status == 200) {
            Toast.fire({
              icon: 'success',
              title: 'Facturas descargadas correctamente'
            })
          } else if (response.status == 204) {
            Toast.fire({
              icon: 'error',
              title: 'Ocurrió un error o no hay facturas para descargar'
            })
          } else {
            Toast.fire({
              icon: 'error',
              title: 'Error al descargar las facturas'
            })
          }
        })
          .catch(error => {
            const Toast = Swal.mixin({
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              timer: 2500,
            });
            Toast.fire({
              icon: 'error',
              title: 'No se ha podido conectar con el servidor. Comunícate con el departamento de IT'
            })
          });
      }
    });

    const showInfo = (infoData) => {
      const parsedInfoData = JSON.parse(infoData.replace(/&quot;/g, '"'));

      const modal = document.getElementById("infoModal");
      const modalBody = modal.querySelector('.modal-body');
      const tagsParagraph = modalBody.querySelector('#tags');
      const notesParagraph = modalBody.querySelector('#notes');
      const notesPrivateParagraph = modalBody.querySelector('#notes_private');
      const discardParagraph = modalBody.querySelector('#discard');
      const discardNotesParagraph = modalBody.querySelector('#discard_notes');

      // Clear the content of all paragraphs
      tagsParagraph.textContent = '';
      notesParagraph.textContent = '';
      notesPrivateParagraph.textContent = '';
      discardParagraph.textContent = '';
      discardNotesParagraph.textContent = '';

      if (parsedInfoData.tags) {
        tagsParagraph.textContent = "ETIQUETAS: " + parsedInfoData.tags;
      }
      if (parsedInfoData.notes) {
        notesParagraph.textContent = "NOTAS: " + parsedInfoData.notes;
      }
      if (parsedInfoData.notes_private) {
        notesPrivateParagraph.textContent = "NOTAS PRIVADAS: " + parsedInfoData.notes_private;
      }
      if (parsedInfoData.discard_reason) {
        discardParagraph.textContent = "RAZÓN DE DESCARTE: " + parsedInfoData.discard_reason;
      }
      if (parsedInfoData.discard_notes) {
        discardNotesParagraph.textContent = "NOTA DE DESCARTE: " + parsedInfoData.discard_notes;
      }

      $('#infoModal').modal('show');
    }

    const generateCSV = async () => {
      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static'
      });
      modalLoading.show();
      await new Promise(r => setTimeout(r, 1000));

      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;
      let selectedTransactionValues = multiple_transactions ? multiple_transactions.split(', ') : [];
      let multiple_departurecountries = document.getElementById("multiple-departurecountries").dataset.value;
      let selectedDepartureCountryValues = multiple_departurecountries ? multiple_departurecountries.split(', ') : [];
      let multiple_taxcountries = document.getElementById("multiple-taxcountries").dataset.value;
      let selectedTaxCountryValues = multiple_taxcountries ? multiple_taxcountries.split(', ') : [];
      let seachInput = document.getElementById("search").value;
      let currentURL = window.location.href;
      let invoice_type;

      if (currentURL.includes("/invoices/sales")) {
        invoice_type = "sales";
      } else if (currentURL.includes("/invoices/expenses")) {
        invoice_type = "expenses";
      } else if (currentURL.includes("/invoices/transfers")) {
        invoice_type = "transfers";
      } else {
        invoice_type = "all";
      }

      let dataFiltered = {
        invoice_status: document.getElementById("status").value,
        year: document.getElementById("year").value,
        month: document.getElementById("month").value,
        tax_country_id: selectedTaxCountryValues,
        departure_country_id: selectedDepartureCountryValues,
        transaction_type: selectedTransactionValues,
        invoice_type: invoice_type,
        search: seachInput
      };

      let jsonData = JSON.stringify(dataFiltered);

      const generateCSVResponse = await fetch("{% url 'app_invoices:generate_csv' seller.shortname %}", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': "{{ csrf_token }}"
        },
        body: jsonData
      });


      if (generateCSVResponse.status == 200) {
        const blob = await generateCSVResponse.blob();
        const blobUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.setAttribute('download', `Listado_facturas_{{seller.shortname}}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Toast.fire({
          icon: 'success',
          title: generateCSVResponse.headers.get('X-Message')
        });

      } else if (generateCSVResponse.status == 204) {
        Toast.fire({
          icon: 'warning',
          title: generateCSVResponse.headers.get('X-Message')
        });
      } else if (generateCSVResponse.status == 500) {
        Toast.fire({
          icon: 'error',
          title: generateCSVResponse.headers.get('X-Message')
        });
      } else {
        Toast.fire({
          icon: 'error',
          title: 'Ha surgido un error al generar el archivo'
        });
      }
      modalLoading.hide();
    }

    const downloadInvoices = async () => {
      const shortname = "{{seller.shortname}}";
      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static'
      });
      modalLoading.show();
      await new Promise(r => setTimeout(r, 1000));

      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;
      let selectedTransactionValues = multiple_transactions ? multiple_transactions.split(', ') : [];
      let multiple_departurecountries = document.getElementById("multiple-departurecountries").dataset.value;
      let selectedDepartureCountryValues = multiple_departurecountries ? multiple_departurecountries.split(', ') : [];
      let multiple_taxcountries = document.getElementById("multiple-taxcountries").dataset.value;
      let selectedTaxCountryValues = multiple_taxcountries ? multiple_taxcountries.split(', ') : [];
      let seachInput = document.getElementById("search").value;
      let currentURL = window.location.href;
      let invoice_type;

      if (currentURL.includes("/invoices/sales")) {
        invoice_type = "sales";
      } else if (currentURL.includes("/invoices/expenses")) {
        invoice_type = "expenses";
      } else if (currentURL.includes("/invoices/transfers")) {
        invoice_type = "transfers";
      } else {
        invoice_type = "all";
      }

      let dataFiltered = {
        invoice_status: document.getElementById("status").value,
        year: document.getElementById("year").value,
        month: document.getElementById("month").value,
        tax_country_id: selectedTaxCountryValues,
        departure_country_id: selectedDepartureCountryValues,
        transaction_type: selectedTransactionValues,
        invoice_type: invoice_type,
        search: seachInput
      };

      try {

        const invoicesResponse = await fetch("{% url 'app_invoices:seller_invoices_massive_download' seller.shortname %}", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': "{{ csrf_token }}"
          },
          body: JSON.stringify(dataFiltered)
        });

        if (invoicesResponse.status == 200) {
          modalLoading.hide();
          const blob = await invoicesResponse.blob();
          const blobUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = blobUrl;
          link.setAttribute('download', `Facturas_${shortname}.zip`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          Toast.fire({
            icon: 'success',
            title: 'Facturas descargadas correctamente'
          });
        } else if (invoicesResponse.status == 204) {
          Toast.fire({
            icon: 'warning',
            title: 'No hay facturas para descargar'
          });
        } else {
          Toast.fire({
            icon: 'error',
            title: 'Ha surgido un error al descargar las facturas'
          });
        }
      } catch (error) {
        console.error("Error in downloadInvoices: ", error);
      }
      modalLoading.hide();
    }

    const massiveOptionPerm = () =>{
      let perms = "{{ perms.users.is_superuserAPP }}";
      if (perms == "False") {
        $('#selectValue').empty().append('<option value="empty">Selecciona una acción múltiple</option><option value="download">Descargar facturas</option>');
      }

    }

  </script>

  <!-- JQUERY DATA TABLE  -->

  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script type="text/javascript">
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputPaisIva = ref(null);
    const inputCategoria = ref(null);
    const inputFile = ref(null);
    const inputFilename = ref(null);
    const inputExportCSV = ref(false);
    const invoiceDataJSON = ref({});
    const dj = ref({});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          // if (debug) console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      if (debug) console.log(dj.value);
    };

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
      // console.log("fileList: ", fileList);
    }

    const onChangeYear = () => {
      const year = document.getElementById("year");
      const period = document.getElementById("month");
      console.log("EL AÑO ES: ", year.value);
      if (year && year.value) {
        period.disabled = false;
      } else {
        period.value = '';
        period.disabled = true;
      }
    }

    // WATCHERS ////////////////////////////////////////////////////////////////////////

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      invoiceDataJSON,

      inputId,
      inputPaisIva,
      inputCategoria,
      inputFile,
      inputFilename,
      inputExportCSV,

      getCountryNameByCode,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";
      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export}
        },
        computed: {
          totalProfitValue() {
            return parseFloat(this.invoiceDataJSON.total_profit_amount);
          },
          iconClassCheck() {
            return this.totalProfitValue >= 0 ? 'fas fa-caret-up text-c-green' : 'fas fa-caret-down text-c-red';
          },
        }
      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('.vue', data_export);
    createVue3('#toast', data_export);
    createVue3('#export', data_export);
    
  </script>
  
  <!-- VUE3 JS  -->
{% endblock javascripts %}
