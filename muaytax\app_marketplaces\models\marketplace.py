from django.db import models

class Marketplace(models.Model):

    # id -> AutoGen


    shopname = models.CharField(
        max_length=150, 
        blank=True, 
        null=True, 
        default=None,
        verbose_name="Nombre de la tienda "
    )

    marketplace = models.ForeignKey(
        "dictionaries.Marketplaces",
        on_delete=models.PROTECT,
        related_name="marketplace",
        verbose_name="Marketplace Name"
        )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="seller_marketplace",
        verbose_name="Vendedor",
    )
    
    sellervat= models.ForeignKey(
        "sellers.Sellervat",
        on_delete=models.PROTECT,
        related_name="sellervat_marketplace",
        verbose_name="IVA Vendedor",
        blank=True,
        null=True
    )

    token = models.CharField(
        blank=True,
        null=True,
        max_length=350, 
        verbose_name="Token Auth"
    )

    actived = models.BooleanField(
        default=False,
        verbose_name="Autorizado"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Marketplaces"
        verbose_name_plural = "Marketplaces"
    
    def __str__(self):
            return "Marketplace N.{}".format(self.pk)
    