import requests
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Div, Field, Submit
from django import forms
from django.shortcuts import get_object_or_404

from muaytax.app_address.models.address import Address
from muaytax.app_providers.models.provider import Provider
from muaytax.dictionaries.models.countries import Country
from muaytax.app_sellers.models.seller import Seller


class ProviderChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, required=False, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")

    def clean_vies(self):
        vies = self.cleaned_data.get('vies')

        if vies and vies != '':
            clean_vies = vies.replace(' ', '').upper()
            if clean_vies == 'LU19647148' or clean_vies == '19647148':
                return vies

        if vies:
            country = self.cleaned_data.get('country').pk
            if country and country == 'GR':
                country='EL'
            rq = requests.get(f"https://ec.europa.eu/taxation_customs/vies/rest-api/ms/{country}/vat/{vies}")
            if rq.status_code != 200:
                raise forms.ValidationError('No se pudo conectar con la página de la comisión europea')
            if not rq.json().get('isValid', False):
                raise forms.ValidationError('El VIES no es válido')
        return vies

    def clean_nif_cif_iva(self):
        nif_cif_iva = self.cleaned_data.get('nif_cif_iva')
        seller = None
        try:
            if self.instance.pk is not None and self.instance.seller is not None:
                seller = self.instance.seller
            else:            
                shortname = self.fields['shortname'].initial
                seller = get_object_or_404(Seller, shortname=shortname)
        except:
            seller = None

        if seller is None:
            print(self.fields)
            raise forms.ValidationError('No se ha podido determinar el seller')
        
        if nif_cif_iva:
            if nif_cif_iva[:2].isalpha():
                provider = Provider.objects.filter(nif_cif_iva=nif_cif_iva, seller=seller).exclude(pk=self.instance.pk).first()
                provider2 = Provider.objects.filter(nif_cif_iva=nif_cif_iva[2:], seller=seller).exclude(pk=self.instance.pk).first()
                if provider or provider2:
                    raise forms.ValidationError('Ya existe un proveedor con este NIF')
            else:
                country_code = self.cleaned_data.get('country').iso_code if self.cleaned_data.get('country') else ''
                provider =  Provider.objects.filter(nif_cif_iva=nif_cif_iva, seller=seller).exclude(pk=self.instance.pk).first()
                provider2 = Provider.objects.filter(nif_cif_iva=f"{country_code}{nif_cif_iva}", seller=seller).exclude(pk=self.instance.pk).first()
                if provider or provider2:
                    raise forms.ValidationError('Ya existe un proveedor con este NIF')

        return nif_cif_iva

    class Meta:
        model = Provider
        fields = [
            "name", 'country', 'nif_cif_iva', 'vies', 'nif_cif_iva_country', 'provider_type', 'provider_number',
            'account_expenses', 'address', 'address_number', 'address_continue', 'address_zip', 'address_city',
            'address_country'
        ]
        widgets = {
            'provider_number': forms.TextInput(attrs={'readonly': 'readonly'}),
        }
        help_texts = {
            'vies': 'Sistema de Intercambio de Información sobre el IVA (El VIES se valida en la página de la comisión europea)',
        }

    def create_dynamic_layout(self):
        d_fields = {
            'account_expenses': 'col-12',
            'address': 'col-9',
            'address_number': 'col-3',
            'address_continue': 'col-12',
            'address_zip': 'col-4',
            'address_city': 'col-4',
            'address_country': 'col-4',
        }
        submit_message = 'Actualizar' if self.instance.pk else 'Guardar'
        layout = Layout(
            Div(*[
                Div(
                    Field(name, css_class='form-control' if self.fields[name].widget.__class__.__name__ in [
                        'Select'] else ''),
                    css_class=d_fields.get(name, 'col-6'))
                for name in self.fields
            ], 
            Div(Div(Submit('submitUpload', submit_message), css_class="controls"),
            css_class="control-group"), css_class='row')
        )
        return layout

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper(self)
        self.helper.layout = self.create_dynamic_layout()

        provider = kwargs.get('instance')
        if provider and provider.provider_address:
            address = provider.provider_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country
        elif provider:
            if provider.zip:
                self.fields['address_zip'].initial = provider.zip
            if provider.nif_cif_iva_country:
                self.fields['address_country'].initial = provider.nif_cif_iva_country

        if self.fields['country'] is not None:
            self.fields['country'].required = True

        if kwargs['initial'] is not None and kwargs['initial'].get('seller') is not None:
            shortname = kwargs['initial']['seller'].shortname
            self.fields['shortname'] = forms.CharField(max_length=150, required=False, label="shortname", initial=shortname)
            
    def save(self, commit=True):
        provider = super().save(commit=False)
        address = provider.provider_address

        if (address is None):
            address = Address()
            provider.provider_address = address

        address.address_name = f"Direccion Proveedor {provider.pk}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.save()

        provider.zip = address.address_zip

        if commit:
            pk = provider.pk
            provider.save()
            if (pk is None):
                pk = provider.pk
                address.address_name = f"Direccion Proveedor {pk}"
                address.save()

        return provider


class ProviderDeleteForm(forms.Form):
    class Meta:
        model = Provider
        exclude = ["seller"]
