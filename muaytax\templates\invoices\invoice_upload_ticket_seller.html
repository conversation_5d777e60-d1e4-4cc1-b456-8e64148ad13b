{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
    {% comment %} <link rel="stylesheet" href="{% static 'assets/css/plugins/dropzone.min.css' %}" /> {% endcomment %}
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css" />
    <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/uppy.min.css" />
    <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" />
    <style>
      .dropzone {
        width: 100%;
        min-height: 50vh !important;
        background-image: url("https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png");
        background-size: 85px;
        background-repeat: no-repeat;
        background-position: center 25%;
      }
      .dropzone .dz-message {
        display: block; 
        position: absolute;
        top: 53%;
        width: 95%;
        text-align: center;
      }
      .dropzone .dz-preview.dz-error .dz-error-message {
        display: none !important; 
      }  
    </style>
{% endblock stylesheets %}

{% block title %}
Lector de Recibos Bancarios
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              Facturas: Lector de Recibos Bancarios
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">
                    Facturas
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices_upload_ticket' seller.shortname %}">
                    Lector de Recibos Bancarios
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <!-- Cargar Documentos | START  -->
    <div class="col-12" id="uploads">
      <div class="card">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>Lector de Recibos Bancarios</h5>
          </div>
        </div>
        <div class="card-body border">
          {% comment %}
          <!-- BLOCK UPLOAD MESSAGE-->
          <div class="alert alert-warning w-100 text-center mx-auto">
            <br />
            <h2> FIN DE PERIODO </h2>
            <h2><i class="fas fa-duotone fa-triangle-exclamation"></i></h2>
            <br />
            <h4> Temporalmente Deshabilitado. </h4>
            <br />
          </div>   
          {% endcomment %}          
          <div class="row">
            <div class="col-4 mt-3">
              <label class="form-label" for="inputCustomerType">
                País IVA Contratado de los Documentos a subir:
              </label>
              <select
                class="form-select form-control"
                id="inputPaisIva"
                v-model="inputPaisIva"
              >
                <option :value="null" selected>Desconocido</option>
                <option v-for="vat in dj.seller_vat" :key="vat.vat_country" :value="vat.vat_country">
                  [[ getCountryNameByCode(vat.vat_country) ]]
                </option>
              </select>
            </div>

            <div class="col-4 mt-3">
              <label class="form-label" for="inputCustomerType">
                Categoría de los Documentos a subir:
              </label>
              <select
                class="form-select form-control"
                id="inputCategoria"
                v-model="inputCategoria"
                disabled
              >
                <option :value="null" selected>Desconocida</option>
                <option v-for="cat in dj.invoice_categories" :key="cat.pk" :value="cat.pk">
                  [[ cat.description ]]
                </option>
              </select>
            </div>

            <div class="col-4 mt-3">
              <label class="form-label" for="inputCustomerType">
                Tipo de los Documentos a subir:
              </label>
              <select
                class="form-select form-control"
                id="inputTipo"
                v-model="inputTipo"
                disabled
              >
                <option :value="null" selected>Desconocida</option>
                <option v-for="type in dj.invoice_types" :key="type.pk" :value="type.pk">
                  [[ type.description ]]
                </option>
              </select>
            </div>
          </div>
          <br>
          <form method="post" enctype="multipart/form-data" action="{% url 'app_invoices:seller_invoice_create_ticket' seller.shortname %}" id="my-dropzone" class="dropzone">
            {% csrf_token %}

            <div class="d-none d-print-none">
              <!-- change d-x-none to d-none -->
              <!-- change type text to hidden -->
              <input
                type="hidden"
                id="id"
                name="{{ form_create.seller.name }}"
                value="{{ seller.pk }}"
              />
              <input
                type="hidden"
                id="tax_country"
                name="tax_country"
                v-model="inputPaisIva"
              />
              <input
                type="hidden"
                id="invoice_category"
                name="invoice_category"
                v-model="inputCategoria"
              />
              <input
                type="hidden"
                id="invoice_type"
                name="invoice_type"
                v-model="inputTipo"
              />
            </div>

            <div class="fallback">
              <input
                type="file"
                id="file"
                name="form_create.file.name"
                multiple
              />
            </div>
          </form> 
          <span id="myListContainer"></span>          
        </div>
      </div>
    </div>
    <!-- Cargar Documentos | END  -->
  </div>

{% endblock content %}

{% block javascripts %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>
  
  <!-- DROPZONE JS  -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dropzone/dropzone.min-v5.js"></script>
  <script>
    Dropzone.options.myDropzone = {
      init: function() {
        var myList = document.createElement("ul");
        myList.classList.add("list-group");
        document.getElementById("myListContainer").appendChild(myList);

        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function(file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function(file) {
          console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }
            
            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });
       
        this.on("success", function(file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          console.log('Success | response: ', response);
          $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
           // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Subido";
          listItem.classList.add("list-group-item", "list-group-item-success");
          myList.insertBefore(listItem, myList.firstChild);
    
        });

        this.on("error", function(file, errorMessage) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
          $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
          var listItem = document.createElement("li");
          listItem.innerHTML = "<b>" + file.name + "</b> - Error: " + errorMessage;
          listItem.classList.add("list-group-item", "list-group-item-danger");
          myList.insertBefore(listItem, myList.firstChild);
        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize:10485760,
      acceptedFiles:'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Arrastre los archivos aquí",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
  </script>
  <!-- DROPZONE JS  -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script >

    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const { ref, watch } = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputPaisIva = ref(null);
    const inputCategoria = ref('expenses');
    const inputTipo = ref('ticket');
    const inputFile = ref(null);
    const inputFilename = ref(null);
    const dj = ref({});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj=null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1 ) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key,value] of Object.entries(djObj) ) {
            dj2[key] = [];
            for (const obj of JSON.parse(value) ) {
              dj2[key].push({ ...obj?.fields , "pk":obj?.pk })
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }        
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      console.log(dj.value);
    };   

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
      console.log("fileList: ", fileList);
    }

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      inputId,
      inputPaisIva,
      inputCategoria,
      inputTipo,
      inputFile,
      inputFilename,     
      getCountryNameByCode,
   
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const { createApp } = VUE3;
      const app = createApp({
          components: { 
            EasyDataTable: window["vue3-easy-data-table"],
          },
          delimiters: ['[[', ']]'],
          el: target,
          data() { return { ...data_export } }
      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('#uploads', data_export);  
  </script>
  <!-- VUE3 JS  -->

 
{% endblock javascripts %}
