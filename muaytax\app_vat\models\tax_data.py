from django.db import models


class TaxData(models.Model):
    # Relaciones
    seller = models.OneToOneField(
        "sellers.seller", on_delete=models.CASCADE, verbose_name="empresa"
    )
    country = models.ForeignKey(
        "dictionaries.Country", on_delete=models.CASCADE, verbose_name="país"
    )

    class Meta:
        verbose_name = "país IVA"
        verbose_name_plural = "paises IVA"

    def __str__(self):
        return self.country.name


class VATNumber(models.Model):
    # Relaciones
    tax_data = models.OneToOneField(
        TaxData, on_delete=models.CASCADE, verbose_name="datos fiscales"
    )

    # Datos
    number = models.CharField("número IVA", max_length=250)
    register_date = models.DateField("fecha registro")
    end_date = models.DateField("fecha fin", null=True)
    permanent_establisment = models.BooleanField(
        "establecimiento permanente",
        help_text=(
            "Es un lugar fijo de negocios que generalmente da lugar a una obligación tributaria "
            "por impuesto sobre la renta o sobre el valor agregado en una jurisdicción en particular. "
            "Por ejemplo: una sucursal, una oficina, una fábrica, un lugar de administración."
        ),
        default=False,
    )
    # valid = models.BooleanField('válido')
    # validation_date = models.DateField('fecha validación', null=True)

    class Meta:
        verbose_name = "número IVA"


class TaxNumber(models.Model):
    # Relaciones
    tax_data = models.OneToOneField(
        TaxData, on_delete=models.CASCADE, verbose_name="datos fiscales"
    )

    # Datos
    tax_number = models.CharField("número de identificación fiscal", max_length=250)
    register_date = models.DateField("fecha registro")
    end_date = models.DateField("fecha fin", null=True)

    class Meta:
        verbose_name = "número identificación fiscal"


class TaxPeriodType(models.Model):
    # Relaciones
    tax_data = models.OneToOneField(
        TaxData, on_delete=models.CASCADE, verbose_name="datos fiscales"
    )

    # Datos
    start_date = models.DateField("fecha inicio")
    tax_period_type = models.CharField(
        "tipo periodo impositivo",
        max_length=50,
        choices=[
            ("monthly", "mensual"),
            ("quarterly", "trimestral"),
            ("annually", "anualmente"),
        ],
    )

    class Meta:
        verbose_name = "tipo periodo impositivo"
