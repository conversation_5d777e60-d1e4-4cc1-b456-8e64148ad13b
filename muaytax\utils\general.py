import requests
import deepl

from typing import <PERSON><PERSON>
from datetime import datetime, timedelta, date
from phonenumbers import PhoneNumber
from django.conf import settings

def get_year(year: int) -> int:
    """
    Función para obtener el año en función de la fecha actual, en caso de no recibirlo por parámetro.
    """
    month = datetime.now().month
    if year is None:
        year = datetime.now().year
        if month == 1:
            year = year - 1
    return year

def get_period(period: str) -> str:
    """
    Función para obtener el periodo actual a partir del mes en caso de no recibirlo por parámetro.
    """
    if period is None:
        month = datetime.now().month
        if 2 <= month <= 4:
            period = "Q1"
        elif 5 <= month <= 7:
            period = "Q2"
        elif 8 <= month <= 10:
            period = "Q3"
        elif 11 <= month <= 12:
            period = "Q4"
        if month == 1:
            period = "Q4"
    return period

def get_period_details(period: str) -> Tuple[int, int, str]:
    """
    Función que recibe un período y devuelve el primer mes, último mes y código de período.
    """

    if period == "Q1":
        return 1, 3, "1T"
    elif period == "Q2":
        return 4, 6, "2T"
    elif period == "Q3":
        return 7, 9, "3T"
    elif period == "Q4":
        return 10, 12, "4T"
    elif period == "0A":
        return 1, 12, "0A"
    else:  # Month period
        return None, None, period

def get_first_and_last_date(year: str, month: str) -> Tuple[str, str]:
    """
    Función que recibe un año y un mes(o código de período) 
    y devuelve el primer y último día del mes o período seleccionado.
    Estos valores se utilizan para filtrar en las consultas SQL.

    month: es un string qque representa el mes o el código de período.
    year: es un string que representa el año.
    """
    first_date = ''
    last_date = ''

    if month == '' and year == '':
        first_date = ''
        last_date = ''
    elif month == '' and year != '':
        first_date = f'{year}-01-01'
        last_date = f'{str(int(year) + 1)}-01-01'
    elif month == '0A':
        first_date = f'{year}-01-01'
        last_date = f'{str(int(year) + 1)}-01-01'
    elif month == '-1' or month == "Q1":
        first_date = f'{year}-01-01'
        last_date = f'{year}-04-01'
    elif month == '-2' or month == "Q2":
        first_date = f'{year}-04-01'
        last_date = f'{year}-07-01'
    elif month == '-3' or month == "Q3":
        first_date = f'{year}-07-01'
        last_date = f'{year}-10-01'
    elif month == '-4' or month == "Q4":
        first_date = f'{year}-10-01'
        last_date = f'{str(int(year) + 1)}-01-01'
    elif month == '01' or month == "M1":
        first_date = f'{year}-01-01'
        last_date = f'{year}-02-01'
    elif month == '02' or month == "M2":
        first_date = f'{year}-02-01'
        last_date = f'{year}-03-01'
    elif month == '03' or month == "M3":
        first_date = f'{year}-03-01'
        last_date = f'{year}-04-01'
    elif month == '04' or month == "M4":
        first_date = f'{year}-04-01'
        last_date = f'{year}-05-01'
    elif month == '05' or month == "M5":
        first_date = f'{year}-05-01'
        last_date = f'{year}-06-01'
    elif month == '06' or month == "M6":
        first_date = f'{year}-06-01'
        last_date = f'{year}-07-01'
    elif month == '07' or month == "M7":
        first_date = f'{year}-07-01'
        last_date = f'{year}-08-01'
    elif month == '08' or month == "M8":
        first_date = f'{year}-08-01'
        last_date = f'{year}-09-01'
    elif month == '09' or month == "M9":
        first_date = f'{year}-09-01'
        last_date = f'{year}-10-01'
    elif month == '10' or month == "M10":
        first_date = f'{year}-10-01'
        last_date = f'{year}-11-01'
    elif month == '11' or month == "M11":
        first_date = f'{year}-11-01'
        last_date = f'{year}-12-01'
    elif month == '12' or month == "M12":
        first_date = f'{year}-12-01'
        last_date = f'{str(int(year) + 1)}-01-01'
    
    return first_date, last_date

def get_actual_first_and_last_date() -> Tuple[date, date]:
    """
    Función que devuelve el primer y último día del período actual.
    """
    # GET FIRST MONTH AND LAST MONTH OF QUARTER
    current_date = datetime.now()
    qstart = f'{current_date.year}-01-01'
    qend = f'{current_date.year}-12-31'
    prev_month = current_date.month - 1 if current_date.month - 1 != 0 else 12

    if current_date.month == 4 or current_date.month == 7 or current_date.month == 10:
        if current_date.day >= 27 :
            prev_month = current_date.month    

    # Q1
    if prev_month >= 1 and prev_month <= 3:
        qstart = f'{current_date.year}-01-01'
        qend = f'{current_date.year}-03-31'
    # Q2
    elif prev_month >= 4 and prev_month <= 6:
        qstart = f'{current_date.year}-04-01'
        qend = f'{current_date.year}-06-30'
    # Q3
    elif prev_month >= 7 and prev_month <= 9:
        qstart = f'{current_date.year}-07-01'
        qend = f'{current_date.year}-09-30'
    # Q4
    elif prev_month >= 10 and prev_month <= 11:
        qstart = f'{current_date.year}-10-01'
        qend = f'{current_date.year}-12-31'
    elif prev_month == 12:
        qstart = f'{current_date.year - 1}-10-01'
        qend = f'{current_date.year - 1}-12-31'
    qstart = datetime.strptime(qstart, '%Y-%m-%d').date()
    qend = datetime.strptime(qend, '%Y-%m-%d').date()

    return qstart, qend

def get_range_dates_gb(year: str, month: str) -> Tuple[str, str]:
    """
    Función que recibe un año y un mes y devuelve el primer y último día del período 
    de declaración para gb.
    Estos valores se utilizan para filtrar en las consultas SQL.

    month: es un string que representa el mes.
    year: es un string que representa el año.

    El mes seleccionado siempre será el último mes del periodo de declaración. Con lo cual
    el primer mes será el segundo mes anterior al mes seleccionado.
    """
    month = int(month[1:])
    year = int(year)
    
    selected_date = datetime(year, month, 1)
    first_date = selected_date - timedelta(days=(selected_date.day - 1) + 50)
    first_date = first_date.replace(day=1)
    
    next_month = selected_date + timedelta(days=32)
    next_month = next_month.replace(day=1)
    
    return first_date.strftime('%Y-%m-%d'), next_month.strftime('%Y-%m-%d')

def get_presentation_type_gb(month: str) -> str:
    """
    Función que recibe un mes y devuelve el tipo de presentación.
    01: Enero - Abril - Julio - Octubre
    02: Febrero - Mayo - Agosto - Noviembre
    03: Marzo - Junio - Septiembre - Diciembre
    """
    month = int(month[1:])
    if month in [1, 4, 7, 10]:
        return '02'
    elif month in [2, 5, 8, 11]:
        return '03'
    elif month in [3, 6, 9, 12]:
        return '01'

def is_current_date_in_presentation_dates() -> bool:
    '''
    Función que determina si estamos en época de presentaciones apartir de la fecha actual
    '''

    period = determine_period(None, [])

    current_date = (datetime.now().month, datetime.now().day)

    presentation_dates = {
        "Q1": [(1,8),(2,9)], #Fecha inicio, fecha de fin, representan el mes y el día
        "Q2": [(4,1),(4,26)],
        "Q3": [(7,1),(7,26)],
        "Q4": [(10,1),(10,25)]
    }

    start_date, end_date = presentation_dates.get(period)
    return start_date <= current_date <= end_date

def determine_period(model_id, anual_models):
    if model_id in anual_models:
        return "0A"
    
    month = datetime.now().month
    if model_id == 'GB-VAT-PROOF':
        month -= 1
        if month <= 0:
            month = 12
        return f"M{month}"
    elif 1 <= month <= 3:
        return "Q1"
    elif 4 <= month <= 6:
        return "Q2"
    elif 7 <= month <= 9:
        return "Q3"
    elif 10 <= month <= 12:
        return "Q4"

def convertCurrencyToDollar(currency: str, amount: float, date) -> float:
    # try:
    #     seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
    # except:
    #     seller = None
    # if seller:
    #     if seller.api_usage is None or seller.api_usage == "":
    #         api_usage = {}
    #     else:
    #         api_usage = json.loads(seller.api_usage)
    #     api = "currency-api"
    #     if api in api_usage:
    #         api_usage[str(api)] = api_usage[str(api)] + 1
    #         # print("ya se encuentra, se actualiza, valor: " + str(api_usage[str(api)]))
    #     else:
    #         api_usage[str(api)] = 1
    #         # print("no se encuentra, lo creamos")
    #     seller.api_usage = json.dumps(api_usage)
    #     seller.save()

    baseUrl = 'https://api.currencyapi.com/v3/'
    apikey = settings.CURRENCY_API_KEY
    taxConversion = 0

    if amount == 0:
        return 0
    
    if currency is not None and currency == 'USD':
        taxConversion = 1
    elif date is not None and currency is not None:

        try:
            url = baseUrl
            currentDate = datetime.now()
            invoiceDate = datetime.strptime(str(date) + ' 23:59:59', '%Y-%m-%d %H:%M:%S')

            if invoiceDate < currentDate:
                url += 'historical'
                url += '?currencies=USD'
                url += '&date=' + str(date)
            else:
                url += 'latest'
                url += '?currencies=USD'
            url += '&base_currency=' + currency
            url += '&apikey=' + apikey
            r = requests.get(url)
            if r is not None and r.status_code == 200:
                data = r.json()['data']
                taxConversion = float(data['USD']['value'])
        except Exception as error:
            print('Error on getTaxConversion: ', repr(error))

    return taxConversion

def form_to_dict_for_translation(form) -> dict:
    """
    Función que recibe un formulario de Django y devuelve un diccionario con los datos del formulario.
    """
    form_data = {}
    for field_name, field in form.fields.items():
        field_value = form.cleaned_data.get(field_name)
        if isinstance(field_value, PhoneNumber):
            continue
        elif hasattr(field_value, 'pk'):
            field_value = field_value.__str__()
        elif hasattr(field_value, 'as_tuple'):
            continue
        if field_value is None:
            continue
        form_data[field_name] = field_value
        
    return form_data

def translate_fields_american_models(data):
    """
    Función que recibe un diccionario con los datos de un modelo y devuelve un diccionario con los datos traducidos.
    """
    result = {}
    translator = deepl.Translator(settings.DEEPL_API_KEY)

    try:
        translated_fields = translator.translate_text(
            list(data.values()),
            source_lang="ES",
            target_lang="EN-US"
        )
    except deepl.DeepLException:
        return None
    
    result.update(dict(zip(data.keys(), [text.text for text in translated_fields])))

    return result