{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="card-body border-top pro-det-edit collapse show" id="pro-det-edit-1">
    <form>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Razón Social</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.name }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Nombre Comercial</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.trade_name }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Nombre del freelance</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.first_name|default:"" }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Apellido del freelance</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.last_name|default:"" }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Fecha de registro SL/Autónomo</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.establishment_date|date:"d/m/Y"|default:"" }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Fecha de Incorporación LLC</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.incorporation_llc_date|date:"d/m/Y"|default:"" }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Número de identificación fiscal</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {{ object.nif_registration }}
            </div>
        </div>
        {% comment %}
        {% if object.legal_entity == 'self-employed' %}
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Estimación directa simplificada</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.is_direct_estimation %}
                    Sí
                {% elif object.is_direct_estimation == False %}
                    No
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% endcomment %}
    </form>
</div>
<div class="card-body border-top pro-det-edit collapse " id="pro-det-edit-2">
    <form id="generalInfoForm" class="form-horizontal" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="form-group row">
            <label for="{{ form.name.id_for_label }}"
                class="col-4 col-form-label text-sm-end">
                {{ form.name.label }}
                {% if form.name.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.name.errors %}
                {% crispy_field form.name 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.name.errors }}</span>
                {% else %}
                {% crispy_field form.name 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="{{ form.trade_name.id_for_label }}"
                class="col-4 col-form-label text-sm-end">
                {{ form.trade_name.label }}
                {% if form.trade_name.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.trade_name.errors %}
                {% crispy_field form.name 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.trade_name.errors }}</span>
                {% else %}
                {% crispy_field form.trade_name 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="{{ form.first_name.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ form.first_name.label }}
                {% if form.first_name.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.first_name.errors %}
                {% crispy_field form.first_name 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.first_name.errors }}</span>
                {% else %}
                {% crispy_field form.first_name 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="{{ form.last_name.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ form.last_name.label }}
                {% if form.last_name.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.last_name.errors %}
                {% crispy_field form.last_name 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.last_name.errors }}</span>
                {% else %}
                {% crispy_field form.last_name 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="{{ form.establishment_date.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ form.establishment_date.label }}
                {% if form.establishment_date.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.establishment_date.errors %}
                {% crispy_field form.establishment_date 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.establishment_date.errors }}</span>
                {% else %}
                {% crispy_field form.establishment_date 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="{{ form.incorporation_llc_date.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ form.incorporation_llc_date.label }}
                {% if form.incorporation_llc_date.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.incorporation_llc_date.errors %}
                {% crispy_field form.incorporation_llc_date 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.incorporation_llc_date.errors }}</span>
                {% else %}
                {% crispy_field form.incorporation_llc_date 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="{{ form.nif_registration.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ form.nif_registration.label }}
                {% if form.nif_registration.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.nif_registration.errors %}
                {% crispy_field form.nif_registration 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.nif_registration.errors }}</span>
                {% else %}
                {% crispy_field form.nif_registration 'class' 'form-control' %}
                {% endif %}
            </div>
        </div>
        
        {% comment %}
        {% if object.legal_entity == 'self-employed' %}
        <div class="form-group row">
            <label for="{{ form.is_direct_estimation.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ form.is_direct_estimation.label }}
                {% if form.is_direct_estimation.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if form.is_direct_estimation.errors %}
                {% crispy_field form.is_direct_estimation 'class' 'form-control is-invalid' %}
                <span class="text-danger">{{ form.is_direct_estimation.errors }}</span>
                {% else %}
                {% for choice in form.is_direct_estimation.field.choices %}
                    <div class="form-check">
                        <input class="form-check-input"
                            type="radio" id="{{ form.is_direct_estimation.id_for_label }}_{{ forloop.counter0 }}"
                            name="{{ form.is_direct_estimation.html_name }}"
                            value="{{ choice.0 }}" {% if form.is_direct_estimation.value == choice.0 %}checked{% endif %}
                            {% if form.is_direct_estimation.field.required %}required{% endif %}>
                        <label class="form-check-label" for="{{ form.is_direct_estimation.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                    </div>
                  {% endfor %}
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% endcomment %}

        <div class="form-group row">
            <label class="col-sm-5 col-form-label"></label>
            <div class="col-sm-7">
                <button type="submit" name="profile_submit" class="btn btn-primary">Actualizar</button>
            </div>
        </div>
    </form>
</div>