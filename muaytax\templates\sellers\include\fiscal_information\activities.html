{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="fas fa-calculator text-c-blue wid-20"></i>
            <span class="p-l-5">Listado de actividades</span>
        </h5>
        <button
            id="addVatActivityButton"
            hx-get="{% url 'app_sellers:add_vat_activities' object.shortname %}"
            hx-target="#updateActivityModal"
            hx-swap="innerHTML"
            type="button"
            class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="tooltip" data-bs-placement="top" 
            title="Añadir actividad" id="addServiceBtn">
            <i class="fas fa-plus"></i>
        </button>
    </div>
</div>
<div class="card">
    <div class="card-body">
        <div class="card-body d-none" id="skeletonBodyActivityTable">
            <div id="" class="dt-container dt-empty-footer">
                <div class="d-flex gap-3">
                    <div class="skeleton skeleton-text">&nbsp;</div>
                    <div class="skeleton skeleton-text">&nbsp;</div>
                </div>
                <div class="dt-layout-row dt-layout-table">
                    <div class="dt-layout-cell ">
                        <table class="display dataTable" style="width: 100%;"
                            aria-describedby="services-vat-country-list_info">
                            <thead class="table-head">
                                <tr role="row">
                                    <th rowspan="1" colspan="1" >
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </th>
                                    <th rowspan="1" colspan="1" >
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </th>
                                    <th rowspan="1" colspan="1" >
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </th>
                                    <th rowspan="1" colspan="1" >
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </th>
                                    <th rowspan="1" colspan="1" >
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                    <td class="overflow-hidden sorting_1">
                                        <div class="skeleton skeleton-text">&nbsp;</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="dt-layout-row">
                    <div class="d-flex gap-3">
                        <div class="skeleton skeleton-text">&nbsp;</div>
                        <div class="skeleton skeleton-text">&nbsp;</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive text-nowrap" id="activityTableBody">
            <table
                id="vat-activities-list-table"
                class="table table-bordered table-hover nowrap dataTable-table"
                style="width: 100%;">
                <thead class="custom-table-head">
                    <tr>
                        <th style="width: 5%;">Pais IVA</th>
                        <th style="width: 10%;">Epigrafe</th>
                        <th style="width: 5%;">Régimen</th>
                        <th>Fecha alta</th>
                        <th>Fecha Baja</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody id="vatActivityRows">
                    {% for activity in seller_vat_activities %}
                    <tr>
                        <td class="align-middle">
                            <span>{{ activity.sellervat.vat_country.name }}</span>
                        </td>
                        <td class="align-middle">
                            <span>{{ activity.sellervat_activity_iae.description }}</span>
                        </td>
                        <td class="align-middle">
                            <span>{{ activity.regime }}</span>
                        </td>
                        <td class="align-middle">
                            <span>{{ activity.date|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                        </td>
                        <td class="align-middle">
                            <span>{{ activity.end_date|date:"d/M/Y"|lower|default:"dd/mm/aaaa" }}</span>
                        </td>
                        <td class="text-center">
                            <a
                                id="updateActivityButton"
                                class="text-warning f-14 me-2"
                                hx-get="{% url 'app_sellers:update_vat_activities' object.shortname  activity.pk %}"
                                hx-target="#updateActivityModal"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Editar"
                                role="button">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a
                                id="deleteActivityButton"
                                class="text-danger f-14"
                                hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                hx-post="{% url 'app_sellers:delete_vat_activity' object.shortname  activity.pk %}"
                                hx-target="#vatActivityRows"
                                hx-swap="innerHTML"
                                hx-trigger='confirmed'
                                onClick="
                                Swal.fire({
                                    title: 'Eliminar actividad de país IVA',
                                    text:'¿Estás seguro de que deseas eliminar esta actividad de país IVA?',
                                    icon: 'warning',
                                    cancelButtonText: 'Cancelar',
                                    showCancelButton: true,
                                    showDenyButton: true,
                                    showConfirmButton: false,
                                    denyButtonText: `Eliminar`,
                                }).then((result)=>{
                                    if(result.isDenied){
                                    htmx.trigger(this, 'confirmed');  
                                    } 
                                })"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Eliminar"
                                role="button">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- modales -->
<div class="modal fade" 
    id="addUpdateVatActivityModal" tabindex="-1" 
    aria-labelledby="animateModalLabel" aria-modal="true" 
    role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" id="updateActivityModal">
            {% include 'sellers/include/fiscal_information/partials/vat-activity-form.html' %}
        </div>
    </div>
</div>

<!-- confirm delete modal -->
<div class="modal fade" id="deleteActivityModal" tabindex="-1" aria-labelledby="animateModalLabel" aria-modal="true" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white">Eliminar actividad de país IVA</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas eliminar esta actividad de país IVA?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="deleteActivitySubmitBtn">Eliminar</button>
            </div>
        </div>
    </div>
</div>

<script>
    const idLegalEntity = "{{ object.legal_entity }}"

    $(document).ready( function() {
        renderDataTable({id: '#vat-activities-list-table', type: 'actividades'});
    });

    document.body.addEventListener('htmx:afterRequest', function (event) {
        if (event.detail.elt.id === 'addVatActivityButton' || event.detail.elt.id === 'updateActivityButton') {
            if (event.detail.xhr.status === 200) {
                const modal = document.getElementById('addUpdateVatActivityModal');
                const modalInstance = new bootstrap.Modal(modal, {backdrop: 'static'});
                modalInstance.show();
                initializeDynamicFields();
                initializeDatePicker();
            }
        }
        else if (event.detail.elt.id === 'addUpdateVatActivityForm') {
            const skeletonBodyActivityTable = document.getElementById('skeletonBodyActivityTable');
            const activityTableBody = document.getElementById('activityTableBody');
            skeletonBodyActivityTable.classList.add('d-none');
            activityTableBody.classList.remove('d-none');

            if (event.detail.xhr.status === 200) {
                const modal = document.getElementById('addUpdateVatActivityModal');
                const modalInstance = bootstrap.Modal.getInstance(modal);

                modalInstance.hide();
                renderDataTable({id: '#vat-activities-list-table', type: 'actividades'});
                showToast('Actividad de país IVA guardada correctamente', 'success');
            }
        }
        else if (event.detail.elt.id === 'vatActivityRows') {
            const skeletonBodyActivityTable = document.getElementById('skeletonBodyActivityTable');
            const activityTableBody = document.getElementById('activityTableBody');
            skeletonBodyActivityTable.classList.add('d-none');
            activityTableBody.classList.remove('d-none');
            if (event.detail.xhr.status === 200) {
                renderDataTable({id: '#vat-activities-list-table', type: 'actividades'});
                showToast('Actividad de país IVA eliminada correctamente', 'success');
            }
        }
    });

    document.body.addEventListener('htmx:beforeRequest', function (event) {
        const formId = event.detail.elt.id;
        if (formId === 'addUpdateVatActivityForm') {
            validateActivityEndDate();
            if (!event.defaultPrevented) {
                const skeletonBodyActivityTable = document.getElementById('skeletonBodyActivityTable');
                const activityTableBody = document.getElementById('activityTableBody');
                skeletonBodyActivityTable.classList.remove('d-none');
                activityTableBody.classList.add('d-none');
                destroyDataTable('#vat-activities-list-table');
            }
        }
        else if (formId === 'deleteActivityButton') {
            const skeletonBodyActivityTable = document.getElementById('skeletonBodyActivityTable');
            const activityTableBody = document.getElementById('activityTableBody');
            skeletonBodyActivityTable.classList.remove('d-none');
            activityTableBody.classList.add('d-none');
            destroyDataTable('#vat-activities-list-table');
        }
    });

    function validateActivityEndDate() {
        const activationDateInput = document.getElementById('id_date');
        const deactivationDateInput = document.getElementById('id_end_date');

        if (deactivationDateInput.value && deactivationDateInput.value <= activationDateInput.value) {
            const error = {
                message: 'La fecha de baja debe ser posterior a la fecha de alta',
                type: 'error'
            
            };
            toggleEndDateValidationFeedback(deactivationDateInput, error);
            event.preventDefault();
        }
        else if (!activationDateInput.value && deactivationDateInput.value) {
            const error = {
                message: 'Debes introducir una fecha de alta antes de introducir una fecha de baja',
                type: 'error'
            };
            toggleEndDateValidationFeedback(activationDateInput, error);
            event.preventDefault();
        }
        else {
            const submitButton = document.querySelector('#vatActivitySubmitBtn');
            submitButton.disabled = true;
            return true;
        }
    }

    function initializeDynamicFields() {
        setInitialFieldsDynamic();
        setDynamicFieldListeners();
    }

    function setDynamicFieldListeners() {
        const activityVatCountry = document.getElementById('id_sellervat');
        activityVatCountry.addEventListener('change', updateDynamicFields);
    }

    function setInitialFieldsDynamic() {
        updateDynamicFields();
    }

    function updateDynamicFields() {
        const activityVatCountry = document.getElementById('id_sellervat');
        const activityVatActivityIae = document.getElementById('id_sellervat_activity_iae');
        const activityRegime = document.getElementById('id_regime');

        const selectedCountryISO = activityVatCountry.options[activityVatCountry.selectedIndex].text.substring(0, 2);
        const selectedCountry = activityVatCountry.options[activityVatCountry.selectedIndex].text.substring(5);

        toggleRegimeOptions(activityRegime, selectedCountry);
        filterActivityOptions(activityVatActivityIae, selectedCountryISO);
    }

    function toggleRegimeOptions(activityRegime, selectedCountry) {
        if (idLegalEntity === 'self-employed' && selectedCountry === 'España') {
            activityRegime.options[2].style.display = 'block';
        } else {
            if (activityRegime.selectedIndex === 2) {
                activityRegime.selectedIndex = 0;
            }
            activityRegime.options[2].style.display = 'none';
        }
    }

    function filterActivityOptions(activityVatActivityIae, selectedCountryISO) {
        for (let i = 0; i < activityVatActivityIae.options.length; i++) {
            const option = activityVatActivityIae.options[i];
            option.style.display = option.value.startsWith(selectedCountryISO) ? 'block' : 'none';
        }
        if (activityVatActivityIae.options[activityVatActivityIae.selectedIndex].style.display === 'none') {
            activityVatActivityIae.selectedIndex = 0;
        }
    }

</script>
