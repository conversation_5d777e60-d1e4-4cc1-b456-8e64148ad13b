from django.db import models
from django.core.validators import FileExtensionValidator

class BankMovementsImporter(models.Model):

    # id -> AutoGen

    file = models.FileField(
        "document", upload_to="uploads/bank_movements/", validators=[FileExtensionValidator(["xlsx","XLSX","xls","XLS","csv","CSV"])]
    )

    status = models.ForeignKey(
        "dictionaries.BankMovementImporterStatus",
        on_delete=models.PROTECT,
        default='pending',
        related_name="bankmovements_importer_status",
        verbose_name="Estado",
    )

    template = models.ForeignKey(
        "dictionaries.BankMovementTemplate",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="bankmovements_importer_template",
        verbose_name="Plantilla",
    )

    bank = models.ForeignKey(
        "banks.Bank",
        on_delete=models.PROTECT,
        related_name="bankmovements_importer_bank",
        verbose_name="Banco",
    )

    error_message = models.CharField(
        max_length=250, 
        blank=True, 
        null=True,
        verbose_name="Mensaje de error"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Importador de Moviemiento Bancario"
        verbose_name_plural = "Importador de Movimientos Bancarios"
    
    def __str__(self):
        if self.file and self.file.name:
            return self.file.name.replace("uploads/", "").replace("bank_movements/", "")
        else:
            return "Bank Movement File N.{}".format(self.pk)
           
    # def get_absolute_url(self):
    #     return reverse("app_importers:amz_txt_eur_detail", kwargs={"pk": self.pk})
    def get_file_name(self):
         return self.file.name.replace("uploads/", "").replace("bank_movements/", "")
    
    def get_file_url(self):    
        filename = self.get_file_name()    
        return f'/media/uploads/bank_movements/{filename}'
