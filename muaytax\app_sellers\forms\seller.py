import datetime
import json
import re
from datetime import timedelta

import numpy as np
import pandas as pd
from crispy_forms.helper import FormHelper
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from slugify import slugify

from muaytax.app_address.models.address import Address
from muaytax.app_documents.models import Document
from muaytax.app_documents.models.model_5472_1120 import TRANSACTION_TYPE_CHOICES
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_184 import ActivityCountry
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.app_sellers.models.seller_yield_record import SellerYieldRecord
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.currencies import Currency
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.dictionaries.models.sellervat_epigraph_regime import (
    SellerVatEpigraphRegime,
)
from muaytax.utils.helpers import localized_choices
from muaytax.utils.mixins import CustomPhoneNumberPrefixWidget

User = get_user_model()


class sellerForm(forms.ModelForm):
    class Meta:
        model = Seller
        exclude = [""]

class sellerChangeForm(forms.ModelForm):
    class Meta:
        model = Seller
        fields = ['logo', 'trade_name', 'name', 'first_name', 'last_name', 'contact_phone', 'valid_emails']
        labels = {
            'name': 'Razón Social',
            'trade_name': 'Nombre Comercial',
            'first_name': 'Nombre ',
            'last_name': 'Apellidos ',
        }


    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)  # Extrae el usuario pasado como argumento
        super().__init__(*args, **kwargs)

        # if  self.instance.legal_entity=='self-employed':
        self.fields['name'].required = False
        self.fields['name'].widget.attrs = {
            'readonly': 'true',
            'style': 'background-color: #e9ecef; opacity: 1;',
            'onmousedown': 'return false;',
            'onfocus': 'this.blur();',
        }
        if  self.instance.legal_entity=='self-employed':
            self.initial['name'] = f"{self.instance.first_name} {self.instance.last_name}"
            self.fields['first_name'].required = True
            self.fields['last_name'].required = True

        # Bloquear edición de Nombre y Apellidos si el usuario no es manager
        if not user or not user.groups.filter(name='manager').exists():
            for field in ['first_name', 'last_name']:
                self.fields[field].widget.attrs.update({
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                })
        # else:
        #     self.fields['trade_name'].required = False
        #     self.fields['trade_name'].widget.attrs = {
        #         'readonly': 'true',
        #         'style': 'background-color: #e9ecef; opacity: 1;',
        #         'onmousedown': 'return false;',
        #         'onfocus': 'this.blur();'
        #     }

class sellerChangeFormManager(forms.ModelForm):
    class Meta:
        model = Seller
        fields = '__all__'
        exclude = [
            "user",
            "shortname",
            "seller_address",
            "seller_bank_address",
            "seller_provider_address",
            "seller_iae",  # todo quitar cuando se elimine el campo
            "api_usage",
            "reg_wizard_steps",
            "reg_wizard_step",
            'limit_invoice'
        ]

    def __init__(self, *args, **kwargs):
        super(sellerChangeFormManager, self).__init__(*args, **kwargs)

        # Make member_address field readonly
        if 'member_address' in self.fields:
            self.fields['member_address'].disabled = True
            # Optional: Add visual styling to indicate it's readonly
            if 'attrs' not in self.fields['member_address'].widget.attrs:
                self.fields['member_address'].widget.attrs = {}
            self.fields['member_address'].widget.attrs.update({
                'readonly': 'readonly',
                'style': 'background-color: #f8f9fa; color: #6c757d;'
            })
            # Optional: Add help text to explain why it's readonly
            self.fields['member_address'].help_text = "Este campo es de solo lectura y no puede ser modificado."

        # Títulos de secciones
        self.section_titles = {
            'personal_info': 'INFORMACIÓN PERSONAL Y DE IDENTIFICACIÓN',
            'fiscal_info': 'INFORMACIÓN FISCAL Y LEGAL',
            'economic_info': 'INFORMACIÓN ECONÓMICA',
            'bank_info': 'INFORMACIÓN BANCARIA',
            'amazon_info': 'AMAZON Y VENTAS',
            'accounting_es': 'CONTABILIDAD ESPAÑA',
            'accounting_usa': 'CONTABILIDAD USA',
            'llc_maintenance': 'MANTENIMIENTO LLC',
            'other_services': 'OTROS SERVICIOS CONTRATADOS',
            'flat_rate': 'TARIFA PLANA INSS',
            'oss': 'OSS',
            'affiliate_programs': 'PROGRAMAS DE AFILIADOS',
            'other': 'OTROS DATOS ADMINISTRATIVOS'
        }

        # Reorganizar todos los campos del formulario en grupos lógicos
        # Primero obtenemos todos los campos disponibles
        available_fields = list(self.fields.keys())
        ordered_fields = []

        # Grupo 1: Información personal y de identificación
        personal_info_fields = [
            'name', 'trade_name', 'first_name', 'last_name', 'birthdate_seller', 'gender',
            'birth_country', 'phone_country', 'phone', 'contact_phone', 'signature_image',
            'member_address', 'logo', 'package_brand_name'
        ]

        # Grupo 2: Información fiscal y legal
        fiscal_info_fields = [
            'legal_entity', 'is_fiscally_transparent', 'is_eu_seller', 'country_registration',
            'state_agency_registration', 'nif_registration', 'establishment_date',
            'incorporation_llc_date', 'vat_no_origin_country', 'eori', 'ein', 'business_id_eeuu',
            'representative_id', 'name_representative', 'last_name_representative',
            'tax_information_start_date', 'tax_information_end_date',
            'share_capital', 'shares_number', 'share_value', 'is_direct_estimation',
            'check_model_es_123', 'check_model_es_131', 'check_model_es_216',
            'check_model_es_111', 'check_model_es_115'
        ]

        # Grupo 3: Información económica
        economic_info_fields = [
            'total_turnover', 'total_benefits', 'total_assests', 'total_liabilities',
            'activity_type', 'products_and_services', 'desc_main_activity',
            'manufacture_products', 'provider_name'
        ]

        # Grupo 4: Información bancaria
        bank_info_fields = [
            'iban', 'swift', 'bank_name'
        ]

        # Grupo 5: Amazon y ventas
        amazon_fields = [
            'amazon_sell', 'amazon_name', 'amazon_merchant_token', 'amazon_account_ids',
            'amazon_address', 'is_amz_report'
        ]

        # Grupo 6: Servicios contratados - Contabilidad ES
        accounting_fields = [
            'contracted_accounting',
            'contracted_accounting_date',
            'contracted_accounting_end_date',
            'tax_agency_accounting_date',
            'tax_agency_accounting_end_date',
            'contracted_accounting_payment_date',
            # Añadido: Servicios de registro/cancelación para España
            'service_registration_purchase_date',
            'service_cancellation_purchase_date'
        ]

        # Grupo 7: Servicios contratados - Contabilidad USA
        accounting_usa_fields = [
            'contracted_accounting_usa_date',
            'contracted_accounting_usa_end_date',
            'contracted_accounting_usa_payment_date',
            'contracted_accounting_usa_basic_date',
            'contracted_accounting_usa_basic_end_date',
            'contracted_accounting_usa_basic_payment_date'
        ]

        # Grupo 8: Servicios contratados - LLC
        llc_fields = [
            'contracted_maintenance_llc',
            'contracted_maintenance_llc_date',
            'contracted_maintenance_llc_end_date',
            'contracted_maintenance_llc_payment_date',
            'maintenance_type',
            # Añadido: Servicios de registro/cancelación para LLC
            'service_llc_registration_purchase_date',
            'service_llc_cancellation_purchase_date'
        ]

        # Grupo 9: Otros servicios contratados
        other_services_fields = [
            'contracted_accounting_txt',
            'contracted_accounting_txt_date',
            'contracted_accounting_txt_end_date',
            'contracted_accounting_txt_payment_date',
            'contracted_model_presentation',
            'contracted_model_presentation_date',
            'contracted_model_presentation_end_date',
            'contracted_model_presentation_payment_date',
            'is_contracted_corporate_payroll',
            'contracted_corporate_payroll_date',
            'contracted_corporate_payroll_end_date',
            'contracted_corporate_payroll_payment_date',
            'is_contracted_labor_payroll',
            'contracted_labor_payroll_date',
            'contracted_labor_payroll_end_date',
            'contracted_labor_payroll_payment_date',
            'withholdings_payments_account_date',
            'withholdings_payments_account_end_date',
            'withholdings_payments_account_payment_date',
            'is_184_contracted',
            'is_b15_contracted',
            'is_5472_1120_contracted',
            'is_5472_1120_inactive_contracted',
            'is_boir_contracted',
            'is_llc_premium_direction'
        ]

        # Grupo 10: Tarifa plana INSS
        flat_rate_fields = [
            'flat_rate_inss_date',
            'flat_rate_inss_extension_count',
            'flat_rate_inss_extension_date',
            'flat_rate_inss_extension_start_date',
            'flat_rate_inss_extension_end_date',
            'flat_rate_inss_next_expiration_send_email'
        ]

        # Grupo 12: OSS
        oss_fields = [
            'oss',
            'oss_date',
            'oss_end_date',
            'oss_payment_date',
            'oss_country',
            'eqtax'
        ]

        # Grupo 13: Programas de afiliados
        affiliate_fields = [
            'affiliate_program',
            'affiliate_start_date',
            'affiliate_end_date',
            'affiliatebpa_program',
            'affiliatebpa_start_date',
            'affiliatebpa_end_date'
        ]

        # Grupo 14: Otros
        other_fields = [
            'is_inactive',
            'valid_emails',
            'limit_invoice_promoted'
        ]

        # Lista de todos los grupos con sus títulos
        field_groups = [
            ('personal_info', personal_info_fields),
            ('fiscal_info', fiscal_info_fields),
            ('economic_info', economic_info_fields),
            ('bank_info', bank_info_fields),
            ('amazon_info', amazon_fields),
            ('accounting_es', accounting_fields),
            ('accounting_usa', accounting_usa_fields),
            ('llc_maintenance', llc_fields),
            ('other_services', other_services_fields),
            ('flat_rate', flat_rate_fields),
            ('oss', oss_fields),
            ('affiliate_programs', affiliate_fields),
            ('other', other_fields)
        ]

        # Creamos una nueva lista ordenada con los campos disponibles
        for section_key, group in field_groups:
            section_fields = []
            for field in group:
                if field in available_fields:
                    section_fields.append(field)

            # Solo añadimos sección si tiene al menos un campo
            if section_fields:
                # Añadimos el separador de sección con el título
                section_title = self.section_titles.get(section_key, 'SECCIÓN')
                self.add_section_header(section_title, ordered_fields)

                # Añadimos los campos de la sección
                ordered_fields.extend(section_fields)

        # Añadimos cualquier campo que haya quedado fuera (por si hay campos nuevos)
        remaining_fields = [f for f in available_fields if f not in ordered_fields]
        if remaining_fields:
            self.add_section_header('CAMPOS ADICIONALES', ordered_fields)
            ordered_fields.extend(remaining_fields)

        # Establecer el nuevo orden de campos
        self.order_fields(ordered_fields)

    def add_section_header(self, title, ordered_fields):
        """Añade un campo de tipo separador con título de sección al formulario"""
        header_field = f"section_header_{len(ordered_fields)}"
        self.fields[header_field] = forms.CharField(
            label='',
            required=False,
            widget=forms.TextInput(attrs={
                'readonly': 'readonly',
                'class': 'section-header',
                'value': title,
                'style': 'font-weight: bold; font-size: 1.2em; margin-top: 20px; margin-bottom: 10px; border: none; background: none; width: 100%; border-bottom: 1px solid #dee2e6; padding-bottom: 5px;'
            })
        )
        ordered_fields.append(header_field)

class SellerChangeInformationForm(forms.ModelForm):

    class Meta:
        model = Seller
        fields = [
                'name',
                'trade_name',
                'first_name',
                'last_name',
                'establishment_date',
                'incorporation_llc_date',
                'nif_registration',
        ]
        widgets = {
            'establishment_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'incorporation_llc_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
        }
        labels = {
            'name': 'Razón Social',
            'trade_name': 'Nombre Comercial',
            'first_name': 'Nombre del freelance',
            'last_name': 'Apellidos del freelance',
            'establishment_date': 'Fecha de registro SL/autónomo',

            'nif_registration': 'Número de identificación fiscal',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance.legal_entity:
            if  self.instance.legal_entity=='self-employed':
                self.fields['name'].required = False
                self.fields['name'].widget.attrs = {
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                }
                # self.fields['is_direct_estimation'].required = True
                self.fields['first_name'].required = True
                self.fields['last_name'].required = True
            else:
                self.fields['name'].required = True
                self.fields['first_name'].required = False
                self.fields['first_name'].widget.attrs = {
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                }
                self.fields['last_name'].required = False
                self.fields['last_name'].widget.attrs = {
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                }
                # self.fields['trade_name'].required = False
                # self.fields['trade_name'].widget.attrs = {
                #     'readonly': 'true',
                #     'style': 'background-color: #e9ecef; opacity: 1;',
                #     'onmousedown': 'return false;',
                #     'onfocus': 'this.blur();'
                # }


        # if self.instance.seller_address and self.instance.seller_address.address_country:
        #     vies = SellerVat.objects.filter(
        #         seller=self.instance,
        #         vat_address__address_country__iso_code=self.instance.seller_address.address_country.iso_code,
        #         is_local=True
        #     ).first()

    def save(self, commit=True):
        seller = super().save()
        # crear seller_vat local si no existe y es sl o self-employed
        if seller.legal_entity in ['sl', 'self-employed'] and seller.country_registration:
            vat = seller.vat_seller.filter(Q(vat_country=seller.country_registration) | Q(is_local=True))
            if vat.exists():
                vat_c = vat.filter(vat_country=seller.country_registration).first()
                vat_l = vat.filter(is_local=True).first()
                if (vat_c or vat_l) and vat_l != vat_c:
                    if vat_l:
                        vat_l.is_local = False
                        vat_l.save()

                    if vat_c:
                        vat_c.is_local = True
                        vat_c.save()
            else:
                SellerVat.objects.create(
                    seller=seller,
                    vat_country=seller.country_registration,
                    vat_number='',
                    is_contracted=seller.contracted_accounting or False,
                    is_local=True,
                )
        return seller

class SellerChangeAditionalInformationESForm(forms.ModelForm):
    is_direct_estimation = forms.ChoiceField(
        required=False,
        label="Estimación directa simplificada",
        choices=[(True, 'Sí'), (False, 'No')],
    )

    check_model_es_123 = check_model_es_131 = check_model_es_216 = forms.ChoiceField(
        required=True,
        choices=[(True, 'Requerido'), (False, 'No Requerido')],
    )

    check_model_es_111 = forms.ChoiceField(
        required=True,
        choices=[(True, 'Requerido'), (False, 'No Requerido')],
        label="Modelo 111"
    )
    check_model_es_115 = forms.ChoiceField(
        required=True,
        choices=[(True, 'Requerido'), (False, 'No Requerido')],
        label="Modelo 115"
    )

    class Meta:
        model = Seller
        fields = [
            'is_direct_estimation',
            'check_model_es_123',
            'check_model_es_131',
            'check_model_es_216',
            'check_model_es_111',
            'check_model_es_115',
        ]
        widgets = {
        }
        labels = {
            'is_direct_estimation': 'Estimación directa simplificada',
            'check_model_es_123': 'Modelo 123',
            'check_model_es_131': 'Modelo 131',
            'check_model_es_216': 'Modelo 216',
        }

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)

    # def save(self, commit=True):
    #     seller = super().save()
    #     return seller

class SellerChangeFormSellPlatform(forms.ModelForm):
    class Meta:
        model = Seller
        fields = ['amazon_sell', 'amazon_name', 'amazon_merchant_token', 'amazon_account_ids']

class SellerFlatRateINSSForm(forms.ModelForm):
    extend_flat_rate = forms.IntegerField(
        required=False,
        label="Extender Tarifa Plana",
        help_text="Ingrese 1 para extender la tarifa plana por un año adicional o 0 para no extenderla.",
        min_value=0,
        max_value=1,
        initial=0
    )

    class Meta:
        model = Seller
        fields = ['flat_rate_inss_date']
        labels = {
            'flat_rate_inss_date': 'Fecha de Inscripción',
        }
        help_texts = {
            'flat_rate_inss_date': 'Seleccione la fecha de inscripción a la tarifa plana.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        seller = self.instance

        # Solo mostrar la opción de extender si el vendedor puede extender su tarifa plana
        if not hasattr(seller, 'can_extend_flat_rate') or not seller.can_extend_flat_rate:
            self.fields['extend_flat_rate'].widget = forms.HiddenInput()

    def clean_flat_rate_inss_date(self):
        flat_rate_inss_date = self.cleaned_data.get('flat_rate_inss_date')

        # Validaciones relacionadas con el modelo
        seller = self.instance
        if flat_rate_inss_date and seller.legal_entity != "self-employed":
            raise ValidationError(
                'La fecha de inscripción a tarifa plana solo se aplica a vendedores autónomos (self-employed).'
            )
         #26-03-25: Delete validation | 0x7xyp01
        """
        # if flat_rate_inss_date and seller.contracted_accounting_date:
        #     if flat_rate_inss_date < seller.contracted_accounting_date:
        #         formatted_date = seller.contracted_accounting_date.strftime("%d/%m/%Y")
        #         raise ValidationError(
        #             f'La fecha de inscripción a tarifa plana no puede ser anterior a la fecha de alta como autónomo {formatted_date}'
        #         )
        """
        return flat_rate_inss_date

    def clean(self):
        cleaned_data = super().clean()
        extend_flat_rate = cleaned_data.get('extend_flat_rate')

        # Si se ingresa 1, verificar que el vendedor pueda extender
        if extend_flat_rate == 1 and not self.instance.can_extend_flat_rate:
            # Determinar el motivo específico por el que no se puede extender
            if self.instance.flat_rate_inss_extension_count >= 1:
                raise ValidationError({
                    'extend_flat_rate': 'No se puede extender la tarifa plana porque ya se ha utilizado la única extensión permitida.'
                })
            elif self.instance.flat_rate_inss_warning_days is not None and self.instance.flat_rate_inss_warning_days <= 0:
                raise ValidationError({
                    'extend_flat_rate': 'No se puede extender la tarifa plana porque ya ha expirado. La extensión solo se puede aplicar antes de que expire la fecha de inscripción a tarifa plana.'
                })
            else:
                raise ValidationError({
                    'extend_flat_rate': 'No se puede extender la tarifa plana en este momento. Solo se puede extender antes de que expire, y solo una vez.'
                })

        return cleaned_data

    def save(self, commit=True):
        seller = super().save(commit=False)
        extend_flat_rate = self.cleaned_data.get('extend_flat_rate')

        # Si se ingresa 1 y el vendedor puede extender
        if extend_flat_rate == 1 and seller.can_extend_flat_rate:
            # Establecer el contador de extensiones a 1
            seller.flat_rate_inss_extension_count = 1
            # Establecer la fecha de extensión como la fecha actual
            seller.flat_rate_inss_extension_date = timezone.now().date()

            # Calcular la fecha de inicio de la prórroga (12 meses después de la fecha de inscripción)
            if seller.flat_rate_inss_date:
                # La fecha de inicio de la prórroga es un año después de la fecha de inscripción
                seller.flat_rate_inss_extension_start_date = seller.flat_rate_inss_date + timedelta(days=365)
                # La fecha de fin de la prórroga es un año después de la fecha de inicio de la prórroga menos 1 día
                # Esto asegura que la prórroga termine el día anterior al aniversario
                seller.flat_rate_inss_extension_end_date = seller.flat_rate_inss_extension_start_date + timedelta(days=364)

        if commit:
            seller.save()
        return seller

class SellerChangeFormContractedServices(forms.ModelForm):
    class Meta:
        model = Seller
        fields = [
            'contracted_accounting',
            'contracted_accounting_date',
            'contracted_accounting_end_date',
            'oss',
            'oss_date',
            'oss_end_date',
            'contracted_maintenance_llc',
            'contracted_maintenance_llc_date',
            'contracted_maintenance_llc_end_date',
            'contracted_model_presentation',
            'contracted_model_presentation_date',
            'contracted_model_presentation_end_date',
            'contracted_accounting_txt',
            'contracted_accounting_txt_date',
            'contracted_accounting_txt_end_date',
            'is_contracted_corporate_payroll',
            'contracted_corporate_payroll_date',
            'contracted_corporate_payroll_end_date',
            'is_contracted_labor_payroll',
            'contracted_labor_payroll_date',
            'contracted_labor_payroll_end_date',
            'withholdings_payments_account_date',
            'withholdings_payments_account_end_date',
        ]
        widgets = {
            'contracted_accounting_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_accounting_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'oss_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'oss_end_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_accounting_txt_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_accounting_txt_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_model_presentation_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_model_presentation_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_maintenance_llc_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_maintenance_llc_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_corporate_payroll_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_corporate_payroll_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_labor_payroll_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_labor_payroll_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'withholdings_payments_account_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'withholdings_payments_account_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
        }
        labels = {
            'contracted_accounting_date': 'Fecha de alta servicio Contabilidad',
            'contracted_accounting_end_date': 'Fecha de baja servicio Contabilidad',
            'oss_date': 'Fecha de alta servicio OSS',
            'oss_end_date': 'Fecha de baja servicio OSS',
            'contracted_accounting_txt': 'Traducción TXT',
            'contracted_accounting_txt_date': 'Fecha de alta servicio Traducción TXT',
            'contracted_accounting_txt_end_date': 'Fecha de baja servicio Traducción TXT',
            'contracted_model_presentation': 'Presentación Modelos',
            'contracted_model_presentation_date': 'Fecha de alta servicio Presentación Modelos',
            'contracted_model_presentation_end_date': 'Fecha de baja servicio Presentación Modelos',
            'contracted_maintenance_llc': 'Mantenimiento LLC',
            'contracted_maintenance_llc_date': 'Fecha de alta servicio Mantenimiento LLC',
            'contracted_maintenance_llc_end_date': 'Fecha de baja servicio Mantenimiento LLC',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # campos booleanos ocultos
        del self.fields['contracted_accounting']
        del self.fields['oss']
        del self.fields['contracted_maintenance_llc']
        del self.fields['contracted_model_presentation']
        del self.fields['contracted_accounting_txt']
        del self.fields['is_contracted_corporate_payroll']
        del self.fields['is_contracted_labor_payroll']

        # verifica de que si no tiene al menos un documento de alta o de baja de pais iva españa,
        # no puede editar las fechas de alta o baja de Retenciones e ingresos a cuenta
        if not Document.objects.filter(
            Q(seller=self.instance),
            Q(documentType__code__in=['ES-036', 'ES-VATSUSPENSION']),
        ).exists():
            self.fields['withholdings_payments_account_date'].widget.attrs.update(readonly='true')
            self.fields['withholdings_payments_account_end_date'].widget.attrs.update(readonly='true')

        if self.instance and self.instance.legal_entity == 'llc':
            # del self.fields['contracted_accounting']
            del self.fields['contracted_accounting_date']
            del self.fields['contracted_accounting_end_date']
            # del self.fields['oss']
            del self.fields['oss_date']
            del self.fields['oss_end_date']
            # del self.fields['contracted_accounting_txt']
            # del self.fields['contracted_accounting_txt_date']
            # del self.fields['contracted_accounting_txt_end_date']

        else:
            # del self.fields['contracted_model_presentation']
            del self.fields['contracted_model_presentation_date']
            del self.fields['contracted_model_presentation_end_date']
            # del self.fields['contracted_maintenance_llc']
            del self.fields['contracted_maintenance_llc_date']
            del self.fields['contracted_maintenance_llc_end_date']

    def clean(self):
        cleaned_data = super().clean()

        contracted_accounting_date = cleaned_data.get('contracted_accounting_date')
        contracted_accounting_end_date = cleaned_data.get('contracted_accounting_end_date')
        oss_date = cleaned_data.get('oss_date')
        oss_end_date = cleaned_data.get('oss_end_date')
        contracted_accounting_txt_date = cleaned_data.get('contracted_accounting_txt_date')
        contracted_accounting_txt_end_date = cleaned_data.get('contracted_accounting_txt_end_date')
        contracted_model_presentation_date = cleaned_data.get('contracted_model_presentation_date')
        contracted_model_presentation_end_date = cleaned_data.get('contracted_model_presentation_end_date')
        contracted_maintenance_llc_date = cleaned_data.get('contracted_maintenance_llc_date')
        contracted_maintenance_llc_end_date = cleaned_data.get('contracted_maintenance_llc_end_date')
        contracted_corporate_payroll_date = cleaned_data.get('contracted_corporate_payroll_date')
        contracted_corporate_payroll_end_date = cleaned_data.get('contracted_corporate_payroll_end_date')
        contracted_labor_payroll_date = cleaned_data.get('contracted_labor_payroll_date')
        contracted_labor_payroll_end_date = cleaned_data.get('contracted_labor_payroll_end_date')
        witholdings_payments_account_date = cleaned_data.get('withholdings_payments_account_date')
        witholdings_payments_account_end_date = cleaned_data.get('withholdings_payments_account_end_date')

        if contracted_accounting_date and contracted_accounting_end_date:
            if contracted_accounting_date > contracted_accounting_end_date:
                self.add_error('contracted_accounting_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if oss_date and oss_end_date:
            if oss_date > oss_end_date:
                self.add_error('oss_date', "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_accounting_txt_date and contracted_accounting_txt_end_date:
            if contracted_accounting_txt_date > contracted_accounting_txt_end_date:
                self.add_error('contracted_accounting_txt_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")

        if contracted_accounting_end_date and not contracted_accounting_date:
            self.add_error('contracted_accounting_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")
        if oss_end_date and not oss_date:
            self.add_error('oss_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")
        if contracted_accounting_txt_end_date and not contracted_accounting_txt_date:
            self.add_error('contracted_accounting_txt_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_model_presentation_date and contracted_model_presentation_end_date:
            if contracted_model_presentation_date > contracted_model_presentation_end_date:
                self.add_error('contracted_model_presentation_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_model_presentation_end_date and not contracted_model_presentation_date:
            self.add_error('contracted_model_presentation_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_maintenance_llc_date and contracted_maintenance_llc_end_date:
            if contracted_maintenance_llc_date > contracted_maintenance_llc_end_date:
                self.add_error('contracted_maintenance_llc_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_maintenance_llc_end_date and not contracted_maintenance_llc_date:
            self.add_error('contracted_maintenance_llc_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_corporate_payroll_date and contracted_corporate_payroll_end_date:
            if contracted_corporate_payroll_date > contracted_corporate_payroll_end_date:
                self.add_error('contracted_corporate_payroll_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_corporate_payroll_end_date and not contracted_corporate_payroll_date:
            self.add_error('contracted_corporate_payroll_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_labor_payroll_date and contracted_labor_payroll_end_date:
            if contracted_labor_payroll_date > contracted_labor_payroll_end_date:
                self.add_error('contracted_labor_payroll_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_labor_payroll_end_date and not contracted_labor_payroll_date:
            self.add_error('contracted_labor_payroll_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if witholdings_payments_account_date and witholdings_payments_account_end_date:
            if witholdings_payments_account_date > witholdings_payments_account_end_date:
                self.add_error('withholdings_payments_account_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if witholdings_payments_account_end_date and not witholdings_payments_account_date:
            self.add_error('withholdings_payments_account_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        return cleaned_data

    def save(self, commit=True):
        seller = super().save()
        vat_local = seller.vat_seller.filter(is_local=True).first()
        if vat_local:
            vat_local.is_contracted = seller.contracted_accounting or False
            if seller.legal_entity in ['sl', 'self-employed']:
                vat_local.activation_date = seller.contracted_accounting_date
                vat_local.start_contracting_date = seller.contracted_accounting_date

                vat_local.deactivation_date = seller.contracted_accounting_end_date
                vat_local.end_contracting_date = seller.contracted_accounting_end_date
            vat_local.save()
            if seller.country_registration:
                econ_act = EconomicActivity.objects.filter(
                    code__startswith=seller.country_registration.iso_code,
                    code__endswith='665'
                ).first() or EconomicActivity.objects.get(
                    code='665'
                )
                SellerVatActivity.objects.get_or_create(
                    sellervat=vat_local,
                    sellervat_activity_iae=econ_act,
                )
        return seller

class SellerChangeAddressInformationForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_state = forms.CharField(max_length=50, required=False, label="Estado / Provincia / Región")
    # address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")

    class Meta:
        model = Seller
        fields = ["address", "address_number", "address_continue", "address_zip", "address_city", "address_state"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        seller = kwargs.get('instance')
        if seller and seller.seller_address:
            address = seller.seller_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_state'].initial = address.address_state

    def save(self, commit=True):
        seller = super().save(commit=False)
        address = seller.seller_address

        if address is None:
            address = Address()
            seller.seller_address = address

        address.address_name = f"Direccion {seller.shortname}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_state = self.cleaned_data['address_state']
        address.save()

        if commit:
            seller.save()

        return seller

class LegalEntityActivityIVACountryForm(forms.Form):
    vat_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País IVA")

    def __init__(self, *args, **kwargs):
        seller = kwargs.pop('seller', None)
        super().__init__(*args, **kwargs)
        if seller:
            query_country = Country.objects.filter(seller_vat_country__in=seller.vat_seller.all())
            vat_countries = SellerVat.objects.filter(seller=seller)
            self.fields['vat_country'].queryset = query_country
            sellervat = seller.vat_seller.filter(is_local=True).first()
            if sellervat:
                self.fields['vat_country'].initial = sellervat.vat_country
            if sellervat:
                self.fields['vat_country'].empty_label = None
            if not vat_countries.exists():
                self.fields['vat_country'].required = False

class LegalEntityActivityChangeForm(forms.ModelForm):
    regime = forms.ModelChoiceField(
        queryset=SellerVatEpigraphRegime.objects.all(),
        empty_label='---------',
        required=True
    )

    def __init__(self, *args, **kwargs):

        instance = kwargs.get('instance')
        iae = kwargs.pop('iae', None)
        super().__init__(*args, **kwargs)
        if iae:
            self.fields['sellervat_activity_iae'].queryset = iae

        if instance:
            self.fields['sellervat_activity_iae'].empty_label = None
            self.fields['regime'].empty_label = None

    class Meta:
        model = SellerVatActivity
        fields = [
            "sellervat_activity_iae",
            "regime",
            "date",
            "end_date"
        ]
        widgets = {
            "date": forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            "end_date": forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
        }

    def clean(self):
        cleaned_data = super().clean()

        date = cleaned_data.get('date')
        end_date = cleaned_data.get('end_date')

        if date and end_date:
            if date > end_date:
                self.add_error('date',
                               "La fecha de alta de actividad no puede ser posterior a la fecha de baja de actividad")

        if end_date and not date:
            self.add_error('date',
                           "Para introducir una fecha de baja de actividad, debe introducir una fecha de alta de actividad")

        return cleaned_data

class BaseActivityFormset(forms.BaseInlineFormSet):
    def clean(self):
        super().clean()

        if any(self.errors):
            return

        # Check if activity doesnt repeat
        activities = set()
        for form in self.forms:
            if form.cleaned_data:
                activity = form.cleaned_data.get('sellervat_activity_iae')
                if activity in activities:
                    raise forms.ValidationError("No puede haber Epígrafes IAE repetidos")
                activities.add(activity)

    def save(self, commit=True, seller=None):
        super().save(commit=commit)
        if seller:
            vat_local = seller.vat_seller.filter(is_local=True).first()
            if vat_local:
                for form in self.forms:
                    if 'sellervat_activity_iae' in form.changed_data:
                        activity = form.cleaned_data.get('sellervat_activity_iae')
                        if activity:
                            code = activity.code.split('-')[-1]
                            iae = EconomicActivity.objects.filter(
                                code__startswith=f'{vat_local.vat_country.iso_code}-',
                                code__endswith=f'{code}'
                            ).first()
                            if not iae:
                                iae = EconomicActivity.objects.filter(code=code).first()

                            SellerVatActivity.objects.get_or_create(
                                sellervat=vat_local,
                                sellervat_activity_iae=iae,
                            )

class sellerAddressChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")
    address_catastral = forms.CharField(max_length=100, required=False, label="Referencia Catastral",
                                        help_text="Solo aplica a España")

    class Meta:
        model = Seller
        fields = ["address", "address_number", "address_continue", "address_zip", "address_city", "address_country",
                  "address_catastral"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        seller = kwargs.get('instance')
        if seller and seller.seller_address:
            address = seller.seller_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country
            self.fields['address_catastral'].initial = address.address_catastral

    def save(self, commit=True):
        seller = super().save(commit=False)
        address = seller.seller_address

        if address is None:
            address = Address()
            seller.seller_address = address

        address.address_name = f"Direccion {seller.shortname}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.address_catastral = self.cleaned_data['address_catastral']
        address.save()

        if commit:
            seller.save()

        return seller

class sellerBankChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")
    address_catastral = forms.CharField(max_length=100, required=False, label="Referencia Catastral",
                                        help_text="Solo aplica a España")

    class Meta:
        model = Seller
        fields = ["iban", "swift", "bank_name", "address", "address_number", "address_continue", "address_zip",
                  "address_city", "address_country"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        seller = kwargs.get('instance')
        if seller and seller.seller_bank_address:
            address = seller.seller_bank_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country

    def save(self, commit=True):
        seller = super().save(commit=False)
        print(seller)
        address = seller.seller_bank_address

        if (address is None):
            address = Address()
            seller.seller_bank_address = address

        address.address_name = f"Direccion Banco {seller.shortname}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.save()

        if commit:
            seller.save()

        return seller

class Sellerintratolocal(forms.Form):
    name = forms.CharField(label='name', max_length=100)

class SellerDeleteTxt(forms.Form):
    name = forms.CharField(label='name', max_length=100)
    month = forms.CharField(label='month', max_length=100)
    year = forms.CharField(label='year', max_length=100)

class SellerSqlExec(forms.Form):
    sql = forms.CharField(label='sql')

class SellerCsvImportForm(forms.Form):
    csv_upload = forms.FileField(validators=[FileExtensionValidator(["csv"])])

class SellerXlsxImportForm(forms.Form):
    xlsx_upload = forms.FileField(validators=[FileExtensionValidator(["xlsx"])])

class SellerTxtImportForm(forms.Form):
    txt_upload = forms.FileField(validators=[FileExtensionValidator(["txt"])])

class SellerTxtCsvImportForm(forms.Form):
    txt_upload = forms.FileField(validators=[FileExtensionValidator(["txt"])])

class SellerListDataCsvUpdateForm(forms.Form):
    file = forms.FileField(validators=[FileExtensionValidator(["csv"])])

class SellerM184Form(forms.ModelForm):
    email = forms.EmailField(
        label="Correo electrónico",
        required=False,
        widget=forms.TextInput(attrs={'readonly': 'true'})
    )
    nif_spanish = forms.CharField(
        label="Nº CIF",
        widget=forms.TextInput(attrs={'maxlength': '9'}),
    )
    contact_first_name = forms.CharField(
        label="Nombre de contacto",
    )
    contact_last_name = forms.CharField(
        label="Apellidos de contacto",
    )
    # TODO: CAMBIAR A REPRESENTANTE DE LA TABLA DE REPRESENTANTES
    representative_id = forms.CharField(
        label="NIF del representante",
        widget=forms.TextInput(attrs={'maxlength': '9'}),
    )

    def __init__(self, *args, **kwargs):
        m_form = kwargs.pop('m_form', None)
        required = kwargs.pop('required', False)
        raw_processed_form = kwargs.pop('processed_form', None)
        processed_form = json.loads(raw_processed_form) if raw_processed_form else raw_processed_form

        super().__init__(*args, **kwargs)

        for field in self.fields.values():
            field.disabled = m_form and m_form.is_processed
            field.required = required
            field.widget.attrs['required'] = 'required'

        self.helper = FormHelper()
        self.helper.form_show_labels = False

        sellervat = self.instance.vat_seller.filter(vat_country="ES").first()
        sel = ''
        if sellervat:
            sel = sellervat.vat_number
            if sel and sel.startswith('ES'):
                sel = sel[2:]

        # ESTABLECER VALORES INICIALES RECUPERANDO LA INFORMACION DEL JSON DEL FORMULARIO PROCESADO SI EXISTE
        self.fields['email'].initial = processed_form.get('form').get('email') if processed_form and processed_form.get('form', None) else self.instance.user.email
        self.fields['nif_spanish'].initial =  processed_form.get('form').get('nif_spanish') if processed_form and processed_form.get('form', None) else sel
        self.fields['name'].initial = processed_form.get('form').get('name') if processed_form and processed_form.get('form', None) else self.instance.name
        self.fields['ein'].initial = processed_form.get('form').get('ein') if processed_form and processed_form.get('form', None) else self.instance.ein
        self.fields['contact_first_name'].initial =  processed_form.get('form').get('contact_first_name') if processed_form and processed_form.get('form', None) else self.instance.user.first_name
        self.fields['contact_last_name'].initial = processed_form.get('form').get('contact_last_name') if processed_form and processed_form.get('form', None) else self.instance.user.last_name
        self.fields['contact_phone'].initial = processed_form.get('form').get('contact_phone') if processed_form and processed_form.get('form', None) else self.instance.contact_phone
        self.fields['representative_id'].initial = processed_form.get('form').get('representative_id') if processed_form and processed_form.get('form', None) else self.instance.representative_id
        self.fields['name_representative'].initial = processed_form.get('form').get('name_representative') if processed_form and processed_form.get('form', None) else self.instance.name_representative
        self.fields['last_name_representative'].initial = processed_form.get('form').get('last_name_representative') if processed_form and processed_form.get('form', None) else self.instance.last_name_representative

    def clean_nif_spanish(self):
        cif = self.cleaned_data['nif_spanish']
        cif.upper() if cif else cif

        regex_cif = re.compile(r'^[ABCDEFGHKLMNPQSX][A-Za-z0-9]{8}$')
        sumPar = 0
        sumImpar = 0
        sumTotal = 0

        if regex_cif.match(cif):
            for i in range(1, len(cif) - 1):
                number = int(cif[i])
                if np.isnan(number):
                    raise forms.ValidationError("CIF no válido.")
                if i % 2 == 0:
                    sumPar += number
                else:
                    double = number * 2
                    double = double // 10 + double % 10 if double > 9 else double
                    sumImpar += double
            sumTotal = sumPar + sumImpar
            unit = sumTotal % 10
            control = (10- unit) % 10

            lastChar = cif[-1]
            if lastChar.isdigit():
                if control == int(lastChar):
                    return cif
            else:
                controlChar = 'JABCDEFGHI'[control]
                if lastChar == controlChar:
                    return cif
        else:
            raise forms.ValidationError("El CIF introducido no es válido.")

        return cif

    def save(self, commit=True):
        seller = super().save(commit=False)
        seller.user.first_name = self.cleaned_data['contact_first_name']
        seller.user.last_name = self.cleaned_data['contact_last_name']
        seller.user.save()

        nif_spanish = self.cleaned_data['nif_spanish']
        if nif_spanish.startswith('ES'):
            nif_spanish = nif_spanish[2:]

        seller.save()

        spa_sellervat = seller.vat_seller.filter(vat_country__iso_code="ES")
        if spa_sellervat.exists():
            spa_sellervat.update(vat_number=nif_spanish)
        else:
            SellerVat.objects.create(
                seller=seller,
                vat_country=Country.objects.get(iso_code="ES"),
                vat_number=nif_spanish,
                is_contracted=False,
            )

        return seller

    class Meta:
        model = Seller
        fields = ['email', 'nif_spanish', 'name', 'ein', 'contact_first_name', 'contact_last_name',
                  'contact_phone', 'representative_id', 'name_representative', 'last_name_representative',
                #   'percent_entity' TODO: AÑADIR ESTE CAMPO Y CALCULARLO
                  ]
        widgets = {
            'name': forms.TextInput(attrs={'readonly': 'true'}),
            'percent_entity': forms.NumberInput(attrs={'min': '1', 'max': '100', 'value': '1', 'readonly': 'true'}),
            'contact_phone': CustomPhoneNumberPrefixWidget(
                country_choices=localized_choices(),
                attrs={'phone_prefix': 'True'}
            ),
        }
        labels = {
            'contact_phone': 'Teléfono de la persona de contacto',
            'ein': 'EIN',
            # 'percent_entity': 'Porcentaje de miembros residentes fiscales en España',
        }

class BaseActivityCountryFormSet(forms.BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        required = kwargs.pop('required', False)
        super().__init__(*args, **kwargs)
        # obtiene el country del primer form y lo pone readonly
        first_country_field = self[0].fields['country']
        first_country_field.widget.attrs = {
            'style': 'background-color: #e9ecef; opacity: 1;',
            'onmousedown': 'return false;',
            'onfocus': 'this.blur();',
        }

    def clean(self):
        super().clean()

        # Check if activity doesnt repeat
        countries = set()
        for form in self.forms:
            if form.cleaned_data:
                country = form.cleaned_data.get('country')
                if country:
                    if country in countries:
                        raise forms.ValidationError("No puede haber países repetidos")
                    countries.add(country)

class CustomNumberInput(forms.NumberInput):
    def __init__(self, *args, **kwargs):
        data_type = kwargs.pop('data_type', 'expense')
        super().__init__(*args, **kwargs)
        self.attrs = {
            'value': '',
            'step': '0.01',
            'min': '0',
            'data-type': data_type,
        }

class ActivityCountryForm(forms.ModelForm):

    def clean_economic_activity_type(self):
        income_origin = self.cleaned_data['income_origin']
        data = self.cleaned_data['economic_activity_type']
        if income_origin == '1' and not data:
            raise forms.ValidationError("Este campo es requerido.")
        return data

    def clean_epigraph_iae(self):
        income_origin = self.cleaned_data['income_origin']
        data = self.cleaned_data['epigraph_iae']
        if income_origin == '1' and not data:
            raise forms.ValidationError("Este campo es requerido.")
        return data

    def __init__(self, *args, **kwargs):
        required = kwargs.pop('required', False)
        m_form = kwargs.pop('m_form', None)
        processed_form = kwargs.pop('processed_form', None)
        super().__init__(*args, **kwargs)

        for field in self.fields.values():
            field.disabled = m_form and m_form.is_processed
            field.required = required
            field.widget.attrs['required'] = 'required'

        self.fields['economic_activity_type'].label += '*'
        self.fields['epigraph_iae'].label += '*'
        self.fields['epigraph_iae'].queryset = self.fields['epigraph_iae'].queryset.exclude(
            code__in=['ES-432', 'ES-665.1', 'ES-763']  # epigrafes que no aparecen en hacienda cuando se presenta el 184
        )

    class Meta:
        model = ActivityCountry
        fields = '__all__'
        exclude = ['account_info_m184']
        widgets = {
            'full_income': CustomNumberInput(data_type='income'),
            'personal_expenses': CustomNumberInput(),
            'operating_expenses': CustomNumberInput(),
            'deductible_taxes': CustomNumberInput(),
            'rental_expenses_fee': CustomNumberInput(),
            'repair_costs': CustomNumberInput(),
            'professional_services': CustomNumberInput(),
            'supplies': CustomNumberInput(),
            'financial_expenses': CustomNumberInput(),
            'other_deductibe_expenses': CustomNumberInput(),
        }

class ProcessExcelForm(forms.Form):
    excel = forms.FileField(
        required=False,
        validators=[FileExtensionValidator(["xlsx", "xls"])],
        widget=forms.FileInput(attrs={'accept': 'xlsx, xls'}),
        label="",
    )

    def is_valid(self):
        return super().is_valid() and self.check_excel_validation()

    def transaction_by_reportable(self):
        return {
            'Reportables 1': ['Compra de productos', 'Compra de servicios', 'Venta de productos', 'Venta de servicios'],
            'Reportables 2': ['Contribución', 'Constitución', 'Disolución', 'Adquisición', 'Distribución'],
        }

    def check_excel_validation(self):
        today = datetime.date.today()
        current_presentation_year = today.year - 1

        try:
            presentation_year = self.data.get('presentation_year')
            if presentation_year and len(str(presentation_year)) > 0 and len(str(presentation_year)) == 4:
                current_presentation_year = int(presentation_year)
        except Exception as e:
            print(f"No se ha podido obtener el presentation_year desde el front: {e}")
            pass

        excel = self.cleaned_data['excel']
        error_list = []
        exc = pd.read_excel(excel, sheet_name=None)
        # obtener las hojas del excel
        sheets = exc.keys()
        # verificar si el excel no tiene exactamente 2 hojas
        if len(sheets) != 2:
            error_list.append(_('El archivo excel debe tener exactamente 2 hojas'))
        # verificar si las hojas no contienen "Reportables 1" ni "Reportables 2"
        elif 'Reportables 1' not in sheets or 'Reportables 2' not in sheets:
            error_list.append(
                _('El archivo excel debe tener dos hojas llamadas "Reportables 1" y "Reportables 2".')
            )
        else:
            for sheet in sheets:
                # slugify sheet
                slug_sh = slugify(sheet)
                # crea una lista vacía en el cleaned_data del formulario con el nombre de la hoja slugificada
                self.cleaned_data[f'{slug_sh}'] = []
                # obtener la hoja, desde la 1ra columna hasta la 5ta, y descarta las filas que estén vacías por completo
                df = exc[sheet].iloc[:, :5].dropna(how='all')
                # verificar si la hoja no tiene los encabezados correctos
                if not df.columns.equals(
                    pd.Index(['Fecha', 'Tipo de transacción', 'Descripción', 'Total', 'Moneda'])
                ):
                    error_list.append(_(f'La hoja {sheet} no tiene los encabezados correctos.'))
                else:
                    for index, row in df.iterrows():
                        row_dict = {}
                        exc_index = index + 2
                        date = row['Fecha']
                        transaction = row['Tipo de transacción']
                        desc = row['Descripción']
                        total = row['Total']
                        currency = row['Moneda']

                        # verificar si la fecha no es válida
                        if pd.isna(date):
                            error_list.append(_(f'La fecha en la fila {exc_index} de la hoja {sheet} está vacía.'))
                        elif pd.isna(pd.to_datetime(date, errors='coerce')):
                            error_list.append(_(f'La fecha en la fila {exc_index} de la hoja {sheet} no es válida.'))
                        elif pd.to_datetime(date).year != current_presentation_year:
                            error_list.append(
                                _(f'La fecha en la fila {exc_index} de la hoja {sheet} no corresponde al año de la presentación de este formulario: {current_presentation_year}')
                            )
                        else:
                            row_dict['date'] = pd.to_datetime(date)

                        # verificar si el tipo de transacción no es válido
                        if pd.isna(transaction):
                            error_list.append(
                                _(f'El tipo de transacción en la fila {exc_index} de la hoja {sheet} está vacío.'))
                        elif transaction not in self.transaction_by_reportable()[sheet]:
                            error_list.append(
                                _(f'El tipo de transacción en la fila {exc_index} de la hoja {sheet} no es válido.')
                            )
                        else:
                            for clave, texto in TRANSACTION_TYPE_CHOICES:
                                if texto == _(transaction):
                                    row_dict['transaction_type'] = clave

                        # verificar si la descripción está vacía
                        if pd.isna(desc):
                            error_list.append(
                                _(f'La descripción en la fila {exc_index} de la hoja {sheet} está vacía.'))
                        else:
                            row_dict['description'] = desc

                        # verificar si el total no es válido
                        if pd.isna(total):
                            error_list.append(_(f'El total en la fila {exc_index} de la hoja {sheet} está vacío.'))
                        elif not isinstance(total, (int, float)):
                            error_list.append(_(f'El total en la fila {exc_index} de la hoja {sheet} no es válido.'))
                        else:
                            row_dict['amount'] = abs(total)

                        # verificar si la moneda no es válida
                        if pd.isna(currency):
                            error_list.append(_(f'La moneda en la fila {exc_index} de la hoja {sheet} está vacía.'))
                        elif not re.match(r'^[A-Z]{3}$', str(currency)) or not Currency.objects.filter(
                            code=currency).exists():
                            error_list.append(_(f'La moneda en la fila {exc_index} de la hoja {sheet} no es válida.'))
                        else:
                            row_dict['currency'] = Currency.objects.get(code=currency)

                        # añade el diccionario al cleaned_data del formulario
                        self.cleaned_data[f'{slug_sh}'].append(row_dict)
        if error_list:
            for error in error_list:
                self.add_error('excel', error)
            return False
        return True

class ExcelUploadDateForm(forms.Form):
    excel_file = forms.FileField(label='Selecciona el archivo Excel')

    def clean_excel_file(self):
        file = self.cleaned_data.get('excel_file')
        if not file.name.endswith(('.xls', '.xlsx')):
            raise ValidationError("El archivo debe ser un archivo Excel con extensión .xls o .xlsx")
        return file

class SellerNetYieldsForm(forms.ModelForm):
    class Meta:
        model = SellerYieldRecord
        fields = ['seller', 'year', 'period', 'model','net_yields', 'assessment_type']
