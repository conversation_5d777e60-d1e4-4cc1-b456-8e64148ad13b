{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load crispy_forms_filters crispy_forms_field %}

{% block title %}
  Soporte MuayTax
{% endblock title %}

{% block stylesheets %}
<!-- css  -->
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">

  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.3.min-v3.6.3.js" crossorigin="anonymous"></script>
  <style>
    .is-focused .choices__inner,
    .is-open .choices__inner {
      border-color: #86b7fe !important; }
    .choices__inner {
      color: #495057 !important;
      font-size: 14px!important;
    }
    .choices__list--dropdown {
      color: #212529 !important;
    }
    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-calendar-picker-indicator {
      display: none;
      -webkit-appearance: none;
    }
    input[type="time"]::-webkit-calendar-picker-indicator {
      background: none;
      display: none;
      -webkit-appearance: none;
    }
    .cursor-pointer {
      cursor: pointer;
    }
    .booking-container{
      max-width: 800px;
    }
    .choices__inner {
      color: #495057 !important;
      font-size: 14px !important;
      display: inline-block;
      vertical-align: top;
      width: 100%;
      background-color: #fff;
      padding: 5.5px 7.5px 3.75px;
      border: 1px solid #ced4da;
      border-radius: 0.375rem;
      min-height: 44px;
      overflow: hidden;
    }
    calendar-icon.icon {
      font-size: 0.5em;
      display: block;
      position: relative;
      width: 7em;
      height: 7em;
      background-color: #fff;
      margin: 2em auto;
      border-radius: 0.6em;
      box-shadow: 0 1px 0 #bdbdbd, 0 2px 0 #fff, 0 3px 0 #bdbdbd, 0 4px 0 #fff, 0 5px 0 #bdbdbd, 0 0 0 1px #bdbdbd;
      overflow: hidden;
      -webkit-backface-visibility: hidden;
      -webkit-transform: rotate(0deg) skewY(0deg);
      -webkit-transform-origin: 50% 10%;
      transform-origin: 50% 10%;
    }
    calendar-icon.icon * {
      display: block;
      width: 100%;
      font-size: 1em;
      font-weight: bold;
      font-style: normal;
      text-align: center;
    }

    calendar-icon.icon strong {
      position: absolute;
      font-size: 1.3em;
      top: 0;
      padding: 0.4em 0;
      color: #fff;
      background-color: #03ad65;
      border-bottom: 1px dashed #f37302;
      box-shadow: 0 2px 0 #03ad65;
    }

    calendar-icon.icon em {
      position: absolute;
      bottom: 0.3em;
      color: #03ad65;
    }

    calendar-icon.icon span {
      width: 100%;
      font-size: 2.8em;
      letter-spacing: -0.05em;
      padding-top: 0.8em;
      color: #2f2f2f;
    }
    .bg-booking-custom{
      background-color: #f4f4f5!important;
    }
    .rounded-10{
      border-radius: 10px!important;
    }
    .nav-link {
      color: #888;
      text-decoration: none;
    }
    .nav-link:hover{
      color: #888;
    }
    .nav-link:focus{
      color: #03ad65;
    }
    .nav-pills .nav-link.active, .nav-pills .show>.nav-link {
      color: #03ad65;
      font-weight: 600;
      background: #fff;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              Soporte
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_bookings:list_bookings_manager' user.username %}">Mis llamadas</a>
            </li>
            <li class="breadcrumb-item">
              <a href="">Crear una llamada telefónica</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-xl-3 col-lg-3 d-none d-lg-block">
    <div class="card task-board-left">
        <div class="card-header">
            <h5>Mi horario</h5>
        </div>
        <div class="card-block">
          <div class="row">
            <div class="col-12">
              <div class="alert alert-success text-center" role="alert" style="font-size: 20px;">
                {{ schedule.start_time|time:"H:i" }} - {{ schedule.end_time|time:"H:i" }}
              </div>
            </div>
          </div>
            <div class="task-right">
              {% if schedule.start_break_time and schedule.end_break_time %}
                <div class="task-right-header-status" data-bs-toggle="collapse" data-bs-target=".taskboard-right-progress">
                    <span class="f-w-400">Tiempo de descanso</span>
                    <i class="fas fa-caret-down float-end m-t-5"></i>
                </div>
                <div class="taskboard-right-progress collapse show">
                  <div class="alert alert-info text-center" role="alert" style="font-size: 20px;">
                    {{ schedule.start_break_time|time:"H:i" }} - {{ schedule.end_break_time|time:"H:i" }}
                  </div>
                </div>
              {% endif %}
              
              {% if appointments %}
              <div class="task-right-header-users" data-bs-toggle="collapse" data-bs-target=".taskboard-right-users">
                  <span class="f-w-400">Próximas llamadas</span>
                  <i class="fas fa-caret-down float-end m-t-5"></i>
              </div>
              <div class="user-box assign-user taskboard-right-users collapse show">
                {% for ap in appointments %}
                <div class="media mb-2">
                  <div class="media-left media-middle me-3">
                    <a href="#" class="pe-none">
                      {% if ap.seller.gender == 'F' %}
                        <img class="media-object img-radius" src="https://appsrv1-147a1.kxcdn.com/django-datta-able-enh/images/user/avatar-1.jpg" alt="Generic placeholder image">
                      {% else %}
                      <img class="media-object img-radius" src="https://appsrv1-147a1.kxcdn.com/django-datta-able-enh/images/user/avatar-2.jpg" alt="Generic placeholder image">
                      {% endif %}
                        <!-- <div class="live-status bg-danger"></div> -->
                    </a>
                  </div>
                  <div class="media-body">
                      <h6>{{ ap.get_user }}</h6>
                      <p style="margin-bottom: 0.3rem;">
                        <i class="feather icon-calendar"></i>
                        {{ ap.date.date|date:"d/M/Y"|lower }}
                      </p>
                      <p>
                        <i class="feather icon-clock"></i>
                        {{ ap.date.time|time:"H:i" }}
                      </p>
                  </div>
                </div>
                {% endfor %}
              </div>
              {% endif %}

              {% if absences %}
              <div class="task-right-header-users" data-bs-toggle="collapse" data-bs-target=".taskboard-right-revision">
                  <span class="f-w-400">Próximas ausencias</span>
                  <i class="fas fa-caret-down float-end m-t-5"></i>
              </div>
              <div class="taskboard-right-revision user-box collapse show">
                {% for ab in absences %}
                <div class="media mb-2">
                  <div class="media-left media-middle me-3">
                    <calendar-icon class="icon">
                      <em>{{ ab.date|date:"l" }}</em>
                      <strong>{{ ab.date|date:"M" }}</strong>
                      <span>{{ ab.date|date:"d" }}</span>
                    </calendar-icon>
                  </div>
                  <div class="media-body">
                    <h6>{{ ab.get_absence_type_display }}</h6>
                    {% if ab.is_all_day %}
                    <p>
                      (Todo el día)
                    </p>
                    {% else %}
                    <p style="margin-bottom: unset;">
                      <i class="feather icon-clock"></i>
                      {{ ab.start_time|time:"H:i" }}
                    </p>
                    <!-- arrow down -->
                    <i class="feather icon-arrow-down"></i>
                    <p>
                      <i class="feather icon-clock"></i>
                      {{ ab.end_time|time:"H:i" }}
                    </p>
                    {% endif %}
                  </div>
                </div>
                {% endfor %}
              </div>
              {% endif %}
            </div>
        </div>
    </div>
  </div>
  <div class="col-xl-6 col-lg-9 col-md-12">
    <div class="card">
      <div class="card-body">

        <div class="col-sm-12">
          <h5 class="mb-3 text-center f-w-500">Agenda una llamada</h5>
          <hr>
          <ul class="nav nav-pills mt-4 shadow-none gap-2 p-1 justify-content-center bg-booking-custom rounded-10" id="pills-tab" role="tablist" style="border: .5px solid #e1e1e121;">
              <li class="nav-item flex-fill text-center">
                  <a  
                    class="{% if block_tab == 'pills-user' %}pe-none{% endif %} nav-link rounded-10 {% if active_tab == 'pills-user' %}active{% endif %}"
                    id="pills-user-tab" data-bs-toggle="pill" href="#pills-user" role="tab"
                    aria-controls="pills-user" aria-selected="true">
                    Usuario aplicación
                  </a>
              </li>
              <li class="nav-item flex-fill text-center has-error">
                  <a
                    class="{% if block_tab == 'pills-guest' %}pe-none{% endif %} nav-link rounded-10 {% if active_tab == 'pills-guest' %}active{% endif %}"
                    id="pills-guest-tab" data-bs-toggle="pill" href="#pills-guest" role="tab"
                    aria-controls="pills-guest" aria-selected="false">
                    Nuevo usuario
                  </a>
              </li>
          </ul>
          <div class="tab-content shadow-none" id="pills-tabContent">
              <div class="tab-pane fade {% if active_tab == 'pills-user' %} active show {% endif %}" id="pills-user" role="tabpanel" aria-labelledby="pills-user-tab">
                <form
                  id="normalSellerForm"
                  class="form-horizontal" method="post"
                  enctype="multipart/form-data" action="">
                  {% csrf_token %}
                  <!-- errors -->
                  <input type="hidden" name="active_tab" value="pills-user">
                  <input type="hidden" name="app_user_form" value="seller">
                  {% if form.non_field_errors %}
                    <div class="alert alert-danger alert-dismissible" role="alert">
                      {{ form.non_field_errors }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                  {% endif %}
                  <div class="container booking-container">
                    <div class="row">
                      <!-- seller -->
                      <div class="col-12 mb-2">
                        <label for="{{ form.seller.id_for_label }}"
                              class="col-form-label{% if form.seller.field.required %} requiredField{% endif %}">
                          {{ form.seller.label }}{% if form.seller.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if form.seller.errors %}
                          {% crispy_field form.seller 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ form.seller.errors }}</span>
                        {% else %}
                          {% crispy_field form.seller 'class' 'form-control form-select' 'required' 'true'  %}
                        {% endif %}
                      </div>
        
                      <!-- subject -->
                      <div class="col-12 mb-2">
                        <label for="{{ form.subject.id_for_label }}"
                              class="col-form-label{% if form.subject.field.required %} requiredField{% endif %}">
                          {{ form.subject.label }}{% if form.subject.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if form.subject.errors %}
                          {% crispy_field form.subject 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ form.subject.errors }}</span>
                        {% else %}
                          {% crispy_field form.subject 'class' 'form-control form-select' 'required' 'true'  %}
                        {% endif %}
                      </div>
        
                      <!-- topics -->
                      <div class="col-12 mb-2">
                        <label for="{{ form.topics.id_for_label }}"
                              class="col-form-label{% if form.topics.field.required %} requiredField{% endif %}">
                          {{ form.topics.label }}{% if form.topics.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if form.topics.errors %}
                          {% crispy_field form.topics 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ form.topics.errors }}</span>
                        {% else %}
                          {% crispy_field form.topics 'class' 'form-control' %}
                        {% endif %}
                      </div>
        
                      <!-- comments -->
                      <div class="col-12 mb-2">
                        <label for="{{ form.comments.id_for_label }}"
                              class="col-form-label{% if form.comments.field.required %} requiredField{% endif %}">
                          {{ form.comments.label }}{% if form.comments.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if form.comments.errors %}
                          {% crispy_field form.comments 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ form.comments.errors }}</span>
                        {% else %}
                          {% crispy_field form.comments 'class' 'form-control' 'rows' '2'  %}
                        {% endif %}
                      </div>
        
                      <!-- date and time -->
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                        <label for="{{ form.date.id_for_label }}"
                              class="col-form-label{% if form.date.field.required %} requiredField{% endif %}">
                          {{ form.date.label }}{% if form.date.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        <div class="input-group">
                          {% if form.date.errors %}
                            {% crispy_field form.date 'class' 'form-control is-invalid' %}
                            <span class="text-danger">{{ form.date.errors }}</span>
                          {% else %}
                            {% crispy_field form.date 'class' 'form-control cursor-pointer' %}
                          {% endif %}
                          <span class="input-group-text bg-transparent"><i class="feather icon-calendar"></i></span>
                        </div>
                      </div>
        
                      <!-- time -->
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                        <label for="{{ form.time.id_for_label }}"
                              class="col-form-label{% if form.time.field.required %} requiredField{% endif %}">
                          {{ form.time.label }}{% if form.time.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        <div class="input-group">
                          {% if form.time.errors %}
                            {% crispy_field form.time 'class' 'form-control is-invalid' %}
                            <span class="text-danger">{{ form.date.errors }}</span>
                          {% else %}
                            {% crispy_field form.time 'class' 'form-control' %}
                          {% endif %}
                          <span class="input-group-text bg-transparent"><i class="feather icon-clock"></i></span>
                        </div>
                        {% if form.time.help_text %}
                          <small class="form-text text-muted">{{ form.time.help_text }}</small>
                        {% endif %}
                      </div>
        
                      <!-- duration as radio -->
                      <div class="col-12 mb-2">
                        <label for="{{ form.duration.id_for_label }}" class="col-form-label{% if form.duration.field.required %} requiredField{% endif %}">
                            {{ form.duration.label }}{% if form.duration.field.required %}
                                <span class="asteriskField">*</span>{% endif %}
                        </label>
                        <div>
                          {% for choice in form.duration.field.choices %}
                            <div class="form-check">
                                <input class="form-check-input" type="radio" id="{{ form.duration.id_for_label }}_{{ forloop.counter0 }}" name="{{ form.duration.html_name }}" value="{{ choice.0 }}" {% if form.duration.value == choice.0 %}checked{% endif %}>
                                <label class="form-check-label" for="{{ form.duration.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                            </div>
                          {% endfor %}
                        </div>
                      </div>
                    
                      <div class="col-12 mb-2">
                        <label for="switch-demo" class="col-form-label">¿Deseas forzar la generación de esta cita telefónica?</label>
                      </div>

                      <div class="switch switch-danger d-inline m-r-10">
                        <input type="checkbox" id="switch-p-1" name="forze_booking">
                        <label for="switch-p-1" class="cr"></label>
                      </div>
        
                    </div>
                  </div>
                  <hr>
                  <div class="control-group">
                    <div class="controls">
                      {% if object.pk is not None %} 
                        <button type="submit" class="btn btn-primary">Actualizar</button>
                      {% else %}
                        <button id="id_seller_submit" type="submit" class="btn btn-primary">Agendar llamada</button>
                      {% endif %}
                    </div>
                  </div>
                </form>
              </div>
              <div class="tab-pane fade {% if active_tab == 'pills-guest' %}show active{% endif %}" id="pills-guest" role="tabpanel" aria-labelledby="pills-guest-tab">
                <form
                  id="newUserForm"
                  class="form-horizontal" method="post"
                  enctype="multipart/form-data" action="">
                  {% csrf_token %}
                  <!-- errors -->
                  <input type="hidden" name="active_tab" value="pills-guest">
                  <input type="hidden" name="new_user_form" value="guest">
                  {% if newSellerForm.non_field_errors %}
                    <div class="alert alert-danger alert-dismissible" role="alert">
                      {{ newSellerForm.non_field_errors }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                  {% endif %}
                  <div class="container booking-container">
                    <div class="row">
                      <!-- new email -->
                      <div class="col-12 mb-2 typeahead">
                        <label for="{{ newSellerForm.new_user_email.id_for_label }}"
                              class="col-form-label{% if newSellerForm.new_user_email.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.new_user_email.label }}{% if newSellerForm.new_user_email.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.new_user_email.errors %}
                          {% crispy_field newSellerForm.new_user_email 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.new_user_email.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.new_user_email 'class' 'form-control' 'required' 'true'  %}
                        {% endif %}
                      </div>
                      <!-- new name -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.new_user_name.id_for_label }}"
                              class="col-form-label{% if form.new_user_name.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.new_user_name.label }}{% if newSellerForm.new_user_name.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.new_user_name.errors %}
                          {% crispy_field newSellerForm.new_user_name 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.new_user_name.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.new_user_name 'class' 'form-control' 'required' 'true'  %}
                        {% endif %}
                      </div>
                      <!-- new last_name -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.new_user_last_name.id_for_label }}"
                              class="col-form-label{% if form.new_user_last_name.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.new_user_last_name.label }}{% if newSellerForm.new_user_last_name.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.new_user_last_name.errors %}
                          {% crispy_field newSellerForm.new_user_last_name 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.new_user_last_name.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.new_user_last_name 'class' 'form-control' 'required' 'true'  %}
                        {% endif %}
                      </div>
                      
                      <!-- new phone -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.new_user_phone.id_for_label }}"
                              class="col-form-label{% if newSellerForm.new_user_phone.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.new_user_phone.label }}{% if newSellerForm.new_user_phone.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.new_user_phone.errors %}
                          {% crispy_field newSellerForm.new_user_phone 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.new_user_phone.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.new_user_phone 'class' 'form-control' 'required' 'true'  %}
                        {% endif %}
                      </div>
                      <!-- subject -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.subject.id_for_label }}"
                              class="col-form-label{% if newSellerForm.subject.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.subject.label }}{% if newSellerForm.subject.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.subject.errors %}
                          {% crispy_field newSellerForm.subject 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.subject.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.subject 'class' 'form-control form-select' 'required' 'true'  %}
                        {% endif %}
                      </div>
                      <!-- topics -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.topics.id_for_label }}"
                              class="col-form-label{% if newSellerForm.topics.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.topics.label }}{% if newSellerForm.topics.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.topics.errors %}
                          {% crispy_field newSellerForm.topics 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.topics.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.topics 'class' 'form-control' %}
                        {% endif %}
                      </div>
                      <!-- comments -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.comments.id_for_label }}"
                              class="col-form-label{% if newSellerForm.comments.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.comments.label }}{% if newSellerForm.comments.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        {% if newSellerForm.comments.errors %}
                          {% crispy_field newSellerForm.comments 'class' 'form-control is-invalid' %}
                          <span class="text-danger">{{ newSellerForm.comments.errors }}</span>
                        {% else %}
                          {% crispy_field newSellerForm.comments 'class' 'form-control' 'rows' '2'  %}
                        {% endif %}
                      </div>
                      <!-- date and time -->
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                        <label for="{{ newSellerForm.date.id_for_label }}"
                              class="col-form-label{% if newSellerForm.date.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.date.label }}{% if newSellerForm.date.field.required %}
                            <span class="asteriskField">*</span>{% endif %}
                        </label>
                        <div class="input-group">
                          {% if newSellerForm.date.errors %}
                            {% crispy_field newSellerForm.date 'class' 'form-control is-invalid' %}
                            <span class="text-danger">{{ newSellerForm.date.errors }}</span>
                          {% else %}
                            {% crispy_field newSellerForm.date 'class' 'form-control cursor-pointer' %}
                          {% endif %}
                          <span class="input-group-text bg-transparent"><i class="feather icon-calendar"></i></span>
                        </div>
                      </div>
                      <!-- time -->
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                        <label
                          for="{{ newSellerForm.time.id_for_label }}"
                          class="col-form-label{% if newSellerForm.time.field.required %} requiredField{% endif %}">
                          {{ newSellerForm.time.label }}
                          {% if newSellerForm.time.field.required %}
                            <span class="asteriskField">*</span>
                          {% endif %}
                        </label>
                        <div class="input-group">
                          {% if newSellerForm.time.errors %}
                            {% crispy_field newSellerForm.time 'class' 'form-control is-invalid' %}
                            <span class="text-danger">{{ newSellerForm.date.errors }}</span>
                          {% else %}
                            {% crispy_field newSellerForm.time 'class' 'form-control' %}
                          {% endif %}
                          <span class="input-group-text bg-transparent"><i class="feather icon-clock"></i></span>
                        </div>
                        {% if newSellerForm.time.help_text %}
                          <small class="form-text text-muted">{{ newSellerForm.time.help_text }}</small>
                        {% endif %}
                      </div>
                      <!-- duration as radio -->
                      <div class="col-12 mb-2">
                        <label for="{{ newSellerForm.duration.id_for_label }}" class="col-form-label{% if newSellerForm.duration.field.required %} requiredField{% endif %}">
                            {{ newSellerForm.duration.label }}{% if newSellerForm.duration.field.required %}
                                <span class="asteriskField">*</span>{% endif %}
                        </label>
                        <div>
                          {% for choice in newSellerForm.duration.field.choices %}
                            <div class="form-check">
                                <input class="form-check-input" type="radio" id="new_{{ newSellerForm.duration.id_for_label }}_{{ forloop.counter0 }}" name="{{ newSellerForm.duration.html_name }}" value="{{ choice.0 }}" {% if newSellerForm.duration.value == choice.0 %}checked{% endif %}>
                                <label class="form-check-label" for="new_{{ newSellerForm.duration.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                            </div>
                          {% endfor %}
                        </div>
                      </div>
                    
                      <div class="col-12 mb-2">
                        <label for="switch-demo_new" class="col-form-label">¿Deseas forzar la generación de esta cita telefónica?</label>
                      </div>

                      <div class="switch switch-danger d-inline m-r-10">
                        <input type="checkbox" id="switch-p-1" name="forze_booking">
                        <label for="switch-p-1" class="cr"></label>
                      </div>
        
                    </div>
                  </div>
                  <hr>
                  <div class="control-group">
                    <div class="controls">
                      <button
                        id="id_new_user_submit"
                        type="submit" 
                        class="btn btn-primary">
                        Agendar llamada
                      </button>
                    </div>
                  </div>
                </form>
              </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- Modal HTML -->
<div class="modal" id="confirmModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title">Confirmación para forzar Llamada Telefónica</h5>
          </div>
          <div class="modal-body">
              <p>¿Estás seguro de que deseas forzar la generación de esta llamada telefónica?</p>
              <div class="alert alert-warning" role="alert">
                  <strong>¡Atención!</strong> Ten en cuenta que esta acción puede provocar superposiciones con otras llamadas.
                  Por favor, asegúrate de revisar tu agenda y el calendario de Google antes de continuar.
              </div>
          </div>
          <div class="modal-footer">
              <button type="button" class="btn btn-primary" id="confirmYes">Sí, Forzar</button>
              <button type="button" class="btn btn-secondary" id="confirmNo" data-dismiss="modal">Cancelar</button>
          </div>
      </div>
  </div>
</div>

{% endblock %}

{% block javascripts %}
<script src="{% static 'assets/js/plugins/choices.min.js' %}"></script>
<script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>
<script src="{% static 'assets/js/plugins/type-ahead.min.js' %}"></script>
<script>

  document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    let confirmForze = false;
    const sellerField = document.getElementById('id_seller');
    const forzeInputs = document.querySelectorAll('input[name="forze_booking"]');
    const confirmModal = $('#confirmModal');

    const newEmailInput = document.getElementById('id_new_user_email');
    const newNameInput = document.getElementById('id_new_user_name');
    const newLastNameInput = document.getElementById('id_new_user_last_name');
    const newPhoneInput = document.getElementById('id_new_user_phone');
    

    new Choices(sellerField, {
      searchPlaceholderValue: 'Selecciona un vendedor',
      noResultsText: 'No se encontraron resultados',
      itemSelectText: 'Presiona para seleccionar',
      shouldSort: false,
      position: 'bottom',
      allowHTML: true,
    });

    $('#switch-p-1').on('change', function() {
      const isChecked = $(this).is(':checked');
      forzeInputs.forEach(input => input.checked = isChecked);

      if (isChecked) {
        confirmModal.modal('show');
      } else {
        confirmForze = false;
      }
    });

    $('#confirmYes').on('click', function() {
      forzeInputs.forEach(input => input.checked = true);
      confirmForze = true;
      confirmModal.modal('hide');
    });

    $('#confirmNo').on('click', function() {
      forzeInputs.forEach(input => input.checked = false);
      confirmForze = false;
      confirmModal.modal('hide');
    });

    confirmModal.on('hidden.bs.modal', function() {
      if (!confirmForze) {
        forzeInputs.forEach(input => input.checked = false);
      }
    });

    document.querySelectorAll('#id_date').forEach(element => {
      new Datepicker(element, {
        buttonClass: 'btn',
        format: 'yyyy-mm-dd',
        closeOnSelect: true,
        orientation: 'bottom',
        showOnFocus: true,
        autohide: true,
        minDate: today,
        weekStart: 1,
        daysOfWeekDisabled: [0, 6],
      });
    });

    newEmailInput.addEventListener('change', async function() {
      const email = this.value;
      if (email && email.includes('@') && email.includes('.')) {
        try {
          const response = await fetch(`/bookings/guest-users/?email=${email}`);
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            if (data.length > 0) {
              newEmailInput.classList.add('is-valid');
              newNameInput.classList.add('is-valid');
              newLastNameInput.classList.add('is-valid');
              newPhoneInput.classList.add('is-valid');
              addValidationMessage(newEmailInput, 'Ya existe registro de este cliente externo');
              addValidationMessage(newNameInput, 'Ya existe registro. Datos autocompletados');
              addValidationMessage(newLastNameInput, 'Ya existe registro. Datos autocompletados');
              addValidationMessage(newPhoneInput, 'Ya existe registro. Datos autocompletados');
              newNameInput.value = data[0].name || '';
              newPhoneInput.value = data[0].phone || '';
            }
        } catch (error) {
          console.error('There was a problem with the fetch operation:', error);
        }
      }
      else {
        removeValidation(newEmailInput);
        removeValidation(newNameInput);
        removeValidation(newPhoneInput);
      }
    });

    function addValidationMessage(inputElement, message) {
      const nextElementSibling = inputElement.nextElementSibling;
      if (!nextElementSibling || !nextElementSibling.classList.contains('text-success')) {
          inputElement.insertAdjacentHTML('afterend', `<span class="text-success"><i class="fas fa-check-circle"></i> ${message}</span>`);
      }
    }

    function removeValidation(inputElement) {
      inputElement.classList.remove('is-valid');
      const successSpan = inputElement.nextElementSibling;
      if (successSpan && successSpan.classList.contains('text-success')) {
          successSpan.remove();
      }
    }

  });

  document.getElementById('newUserForm').addEventListener('submit', function() {
    document.getElementById('id_new_user_submit').disabled = true;
  });

  document.getElementById('normalSellerForm').addEventListener('submit', function() {
    document.getElementById('id_seller_submit').disabled = true;
  });

</script>
{% endblock javascripts %}