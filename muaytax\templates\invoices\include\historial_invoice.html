<p>
<div class="accordion accordion-flush col-4" id="accordionHistorial">
  <div class="accordion-item" style="width: max-content;">
    <h2 class="accordion-header" style="display: flex;">
      <div class="row" style="display: flex; flex-wrap: nowrap;">
        <div  style="display: flex; align-items: center; width: auto;">
          <span style="font-size:14px;">Realizada por:&nbsp;
            {% if last_modified.user_manager.first_name is None %}
              {{last_modified.user_manager.name}}
            {% else %}
              {{last_modified.user_manager.first_name}} {{last_modified.user_manager.last_name}}
            {% endif %}
          </span></div>
        <div  style="display: flex; align-items: center; width: auto;">
          <button id="show_more" class="accordion-button collapsed btn btn-primary text-white {% if not show_detail %} d-none {% endif %}"
          style="padding: 0px 5px; margin: 0; border-radius: 5px; width: auto;"
          type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
            Ver más &nbsp;
          </button>
        </div>
        <div >
      </div>
    </h2>
    <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
      <div class="accordion-body" style="width: max-content;">
        {% for hist in hist_invoice %}

          <b>Fecha:</b>
          {{ hist.created_at|date:"d/m/Y H:i" }} &nbsp;&nbsp;
          <b>Modificado por:</b>
          {% if last_modified.user_manager.first_name is None %}
              {{last_modified.user_manager.name}}
            {% else %}
              {{last_modified.user_manager.first_name}} {{last_modified.user_manager.last_name}}
          {% endif %}
          &nbsp;&nbsp;
          <b>Tiempo de contabilización:</b>
          {{ hist.processing_time }} seg.
          <br>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
</p>