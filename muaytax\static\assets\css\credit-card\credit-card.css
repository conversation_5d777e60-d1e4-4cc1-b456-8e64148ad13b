@import url('https://fonts.cdnfonts.com/css/credit-card');
.form-wrapper{
    min-width: 400px;
}
.credit-card-wrapper{
    min-width: 400px;
}
.credit-card-container{
    perspective: 1000px;
    max-width: 400px;
    margin: 0 auto;
}
.credit-card {
    position: relative;
    width: 100%;
    aspect-ratio: 1.586 / 1;
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}
.credit-card-front {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 1px 5px 6px 0px #000000b5;
}
/* .credit-card-container{
    position: relative;
    width: 100%;
    max-width: 370px;
    max-height: 230px;
    height: 54vw;
    padding: 20px;
    transform-style: preserve-3d;
    box-shadow: 1px 5px 6px 0px black;
    border-radius: 22px;
} */
.credit-card-background{
    position: absolute;
    inset: 0;
    background-size: 400% 400%;
    animation: gradient-xy 15s ease infinite;
}

.bg-card-default{
    background: rgb(189,189,189);
    background: -moz-linear-gradient(180deg, rgba(189,189,189,1) 0%, rgba(97,97,97,1) 61%);
    background: -webkit-linear-gradient(180deg, rgba(189,189,189,1) 0%, rgba(97,97,97,1) 61%);
    background: linear-gradient(180deg, rgba(189,189,189,1) 0%, rgba(97,97,97,1) 61%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#bdbdbd",endColorstr="#616161",GradientType=1);
}
.visa-bg {
    background: rgb(0,53,85);
    background: -moz-linear-gradient(0deg, rgba(0,53,85,1) 0%, rgba(1,39,60,1) 100%);
    background: -webkit-linear-gradient(0deg, rgba(0,53,85,1) 0%, rgba(1,39,60,1) 100%);
    background: linear-gradient(0deg, rgba(0,53,85,1) 0%, rgba(1,39,60,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#003555",endColorstr="#01273c",GradientType=1);
}
.americanexpress-bg{
    background: rgb(0,53,85);
    background: -moz-linear-gradient(0deg, rgba(0,53,85,1) 0%, rgba(50,118,203,1) 100%);
    background: -webkit-linear-gradient(0deg, rgba(0,53,85,1) 0%, rgba(50,118,203,1) 100%);
    background: linear-gradient(0deg, rgba(0,53,85,1) 0%, rgba(50,118,203,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#003555",endColorstr="#3276cb",GradientType=1);
}
.mastercard-bg{
    background: rgb(0,0,0);
    background: -moz-linear-gradient(301deg, rgba(0,0,0,1) 0%, rgba(41,45,43,1) 100%);
    background: -webkit-linear-gradient(301deg, rgba(0,0,0,1) 0%, rgba(41,45,43,1) 100%);
    background: linear-gradient(301deg, rgba(0,0,0,1) 0%, rgba(41,45,43,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#000000",endColorstr="#292d2b",GradientType=1);
}
.discovery-bg{
    background: #93D3E7;
    background: radial-gradient(circle, rgba(147, 211, 231, 1) 35%, rgba(136, 141, 197, 1) 84%);
    background: -moz-linear-gradient(95deg, rgba(136,141,197,1) 0%, rgba(147,211,231,1) 15%, rgba(177,207,206,1) 59%, rgba(136,141,197,1) 100%);
    background: -webkit-linear-gradient(106deg, rgba(136, 141, 197, 1) 0%, rgba(147, 211, 231, 1) 15%, rgb(160 220 218) 59%, rgba(136, 141, 197, 1) 100%);
    /* background: linear-gradient(95deg, #888dc5 0%, #93d3e7 15%, rgba(177,207,206,1) 59%, rgba(136,141,197,1) 100%); */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#888dc5",endColorstr="#888dc5",GradientType=1);
}
.discovery-bg .cc-holder-label,
.discovery-bg .cc-number-label{
    color: #4e4e4e;
}
.jcb-bg {
    background: rgb(204,85,80);
    background: -moz-linear-gradient(95deg, rgba(204,85,80,1) 0%, rgba(180,11,19,1) 100%);
    background: -webkit-linear-gradient(95deg, rgba(204,85,80,1) 0%, rgba(180,11,19,1) 100%);
    background: linear-gradient(95deg, rgba(204,85,80,1) 0%, rgba(180,11,19,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#cc5550",endColorstr="#b40b13",GradientType=1);
}
/* .credit-card-content {
    width: 100%;
    height: 100%;
    color: #fff;
} */

.credit-card-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(2px);
}
.credit-card-content{
    position: absolute;
    inset: 0;
    padding: 1.5rem;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.bank-name-badge{
    display: inline-block;
    /* background-color: rgba(255, 255, 255, 0.1); */
    /* backdrop-filter: blur(4px); */
    /* padding: 0.25rem 0.75rem; */
    font-weight: 500;
    /* height: 50px; */
    /* overflow: hidden; */
    font-size: 1.2rem;
    font-weight: bold;
}

.nfc-icon-color{
    color: #fff;
    filter: brightness(0) invert(1);
}
.credit-card-label{
    /* font-size: 9px; */
    color: #b6b6b6;
    font-size: 0.675rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
    /* margin-bottom: 0.25rem; */
}
.credit-card-number{
    color: #ffff;
    text-shadow: 0 0 2px #ccc;
    /* font-family: OCR-A, monospace; */
    /* font-size: 30px; */
    font-size: 1rem;
    font-weight: bold;
    letter-spacing: 2px;
}
.credit-card-holder-name {
    color: #ffff;
    text-shadow: 0 0 2px #ccc;
    font-size: 1rem;
    font-weight: bold;
}
.credit-card-logo{
    position: absolute;
    right: 0;
    top: 5px
    /* transform: translateY(-100%); */
}
.credit-card-input-icon {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 5;
}
.credit-card-form-container {
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}
.form-floating > label {
    padding-left: 3.5rem;
}

.form-control:not([readonly]):focus {
    border-color: #03ad65;
    box-shadow: 0 0 0 0.25rem rgba(3, 173, 101, 0.25);
}
.form-control[readonly]:focus {
    border-color: #ced4da!important;
}