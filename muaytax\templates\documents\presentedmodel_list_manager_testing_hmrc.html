{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}
  Modelos
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  
  <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css' %}" />
  <link rel="stylesheet" href="{% static 'assets/css/swal/custom-swal-buttons.css' %}" />
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }

    .error-list-item {
      color: black;
      font-weight: bold;
      font-size: 1.2em;
    }

    .btn-icon {
      margin-left: 5px;
      margin-right: 0px;
    }

    .btn-group.dropstart .dropdown-menu::before,
    .btn-group.dropstart .dropdown-menu::after {
      display: none !important;
    }

    .dropstart .dropdown-toggle::before {
      display: none;
    }

    .hovered-row {
      background-color: #FF0000;
    }
    
    .dataTables_scrollBody {
      min-height: 300px;
      background-color: #f2f2f2;
    }

    .table-responsive {
      width: auto; /* El contenedor ocupa el 100% del ancho */
      
    }
  
    #list-table {
      width: 100% !important;
    }

    .modal-content {
      overflow: hidden; /* Para evitar barras de desplazamiento si el contenido es demasiado grande */
    }
  
    #documentPreview {
      width: 100%;
      border: none; /* Quitar borde del iframe */
    }

    .invisible-placeholder {
      visibility: hidden !important;
      pointer-events: none !important;
    }
  </style>
{% endblock stylesheets %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Modelos</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name|title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Modelos</a>
            </li>
          </ul>
        </div>
        <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
          <a href="{% url 'app_documents:presented_model_upload' seller.shortname %}" class="btn btn-primary">
            Cargar Presentacion Nueva
          </a>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body pb-0 mb-0">
          <div class="row mb-4">
            <!-- Search + Pagination -->
            <div class="col-2 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."/>
              </div>
            </div>
            <div class="col-lg-2">
              <select class="form-control form-select" name="countries" id="countries">
                <option value="">Todos los paises</option>
                {% for country in countries %}
                  <option value="{{ country.name }}">{{ country.name }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="col-lg-2">
              <select class="form-control form-select" name="model" id="model">
                <option value="">Selecciona un modelo</option>
                {% for model in modelscode %}
                  <option value="{{ model.description }}">{{ model.description }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-lg-2">
              <select class="form-control form-select" name="period" id="period">
                <option value="">Todos los periodos</option>
                {% for periods in period %}
                  <option value="{{ periods.description }}">{{ periods.description }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-lg-2">
              <select class="form-select form-control" name="year" id="year">
                <option value="">Todos los años</option>
                <option value="2025">2025</option>
                <option value="2024">2024</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
                <option value="2020">2020</option>
                <option value="2019">2019</option>
              </select>
            </div>
            <div class="col-lg-2">
              <select class="form-control form-select" name="status" id="status">
                <option value="">Selecciona un estado</option>
                {% for model_status in models_status %}
                  <option value="{{ model_status.description }}">{{ model_status.description }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <!-- Info -->
          <div class="col-12 mt-3 mb-0 pt-0">
            <div class="card mb-0">
              <div class="card-block mb-0">
                <div class="row d-flex align-items-center mb-0">
                  <div class="col">
                    <h6><b>NIF SELLER</b></h6>
                    <h3 class="f-w-300 d-flex align-items-center mb-1 text-muted">
                      {% if seller and seller.nif_registration %}
                        <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top"
                              data-bs-original-title="Copiar" data-text="{{ seller.nif_registration }}">
                              {{ seller.nif_registration }}
                            </span>
                      {% else %}
                        - Sin NIF -
                      {% endif %}
                    </h3>
                    <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                      {% if seller and seller.contracted_accounting == True %}
                        Contabilidad: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                      {% else %}
                        Contabilidad: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                      {% endif %}
                    </h5>
                  </div>
                  {% if sellervat %}
                    {% for sv in sellervat %}
                      <div class="col" style="border-left: 4px solid #F5F7FA; height: auto;">
                        <h6><b>NIF {{ sv.vat_country }}</b></h6>
                        <h3 class="f-w-300 d-flex align-items-center mb-1 text-muted">
                          {% if sv and sv.vat_number %}
                            <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top"
                                  data-bs-original-title="Copiar" data-text="{{ object.nrc }}">
                                  {{ sv.vat_number }}
                                </span>
                          {% else %}
                            - Sin NIF -
                          {% endif %}
                        </h3>
                        <h5 class="f-w-300 d-flex align-items-center mb-0 text-muted">
                          {% if sv and sv.is_contracted == True %}
                            Contabilidad: &nbsp; <i class="fa-solid fa-md fa-check" style="color: #02c018;"></i>
                          {% else %}
                            Contabilidad: &nbsp; <i class="fa-solid fa-md fa-xmark" style="color: #ff0000;"></i>
                          {% endif %}
                        </h5>
                      </div>
                    {% endfor %}
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
        
  <div class="row">
    <div class="col-12">
      <div class="card user-profile-list">
        <div class="card-body pt-0 mt-0">
          <div class="dt-responsive">
            <table id="list-table" class="table nowrap table-hover m-0 p-0">
              <thead class="table-head">
              <tr>
                <th>Modelo</th>
                <th>País</th>
                <th>Periodo</th>
                <th>Año</th>
                <th>Fecha</th>
                <th>NRC</th>
                <th>IBAN</th>
                <th>SWIFT</th>
                <th>Estado</th>
                <th>Fecha de cita</th>
                <th class="text-center">Resultado</th>
                <th class="text-center">Elección</th>
                <th class="text-center">Importe</th>
                <th class="text-center">Pagado</th>
                <th class="text-center">Frac</th>
                <th>Info</th>
                <th class="dt-fixed-right">Acciones</th>
              </tr>
              </thead>
              <tbody>
              {% for object in models %}
                <tr data-document-url="{{ object.get_file_url }}" data-txt-url="{{ object.get_txt_url }}">
                  <td class="align-middle">
                      <span class="cursor-default" data-bs-toggle="tooltip" data-bs-placement="top"
                            data-bs-original-title="{{ object.model }}" data-model-id="{{object.id}}">{{ object.model|truncatechars:20 }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.country }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.period }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.year }} </span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.created_at|date:"d/M/Y - H:i"|lower }}</span>
                  </td>
                  <td class="align-middle">
                    {% if object.nrc %}
                      <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top"
                            data-bs-original-title="Copiar" data-text="{{ object.nrc }}">{{ object.nrc }}</span><br>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    {% if object.iban8 %}
                      <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top"
                            data-bs-original-title="Copiar" data-text="{{ object.iban8 }}">{{ object.iban8 }}</span><br>
                    {% endif %}
                    {% if  object.iban %}
                      <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top"
                            data-bs-original-title="Copiar" data-text="{{ object.iban }}">{{ object.iban }}
                            {% if object.is_direct_debit %}
                              <a class="btn btn-icon mx-0" data-bs-toggle="tooltip" data-bs-placement="top"
                                title="Pago Domiciliado"
                                style="color: #03ad65;"
                                href="#">
                                <i class="fas fa-university fa-xl"></i>
                              </a>
                            {% endif %}
                      </span><br>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    {% if object.swift %}
                      <span class="copyableText" data-bs-toggle="tooltip" data-bs-placement="top"
                            data-bs-original-title="Copiar" data-text="{{ object.swift }}">{{ object.swift }}</span><br>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    <span>{{ object.status }}</span>
                  </td>
                  <td class="align-middle">
                    <span>{{ object.disagree_appointment.date|date:"d/M/Y - H:i"|lower }}</span>
                  </td>
                  <td class="align-middle text-center">
                    {% if object.result %}
                      <span>{{ object.result }}</span>
                    {% endif %}
                  </td>
                  <td class="align-middle">
                    <span>{{ object.get_choice_seller_display | default:'' }} </span>
                  </td>
                  <td class="align-middle text-center">
                    {% if object.amount %}
                      <span>{{ object.amount }}</span>
                    {% else %}
                      <span></span>
                    {% endif %}
                  </td>
                  <td class="text-center align-middle">
                    {% if object.is_paid == True %}
                      <span><i class="fa-solid fa-check fa-xl" style="color: #02c018;"></i></span>
                    {% endif %}
                    {% if object.is_paid == False %}
                      {% if object.nrc != null %}
                        <span><i class="fa-regular fa-clock fa-xl" style="color: #ffd700;"></i></span>
                      {% else %}
                        <span><i class="fa-solid fa-xmark fa-xl" style="color: #c01802;"></i></span>
                      {% endif %}
                    {% endif %}
                  </td>
                  <td class="align-middle text-center">
                    {% if object.count_split_payments > 1 %}
                      <span>{{ object.count_split_payments }}</span>
                    {% else %}
                      <span><i class="fa-solid fa-xmark fa-xl" style="color: #c01802;"></i></span>
                    {% endif %}
                  </td>
                  {% comment %} info tooltip for disagreed starts {% endcomment %}
                  <td class="aling-middle text-center">
                    {% if object.status.code == "disagreed" and object.comment %}
                      <span class="cursor-pointer tooltip-wrapper" data-bs-toggle="tooltip" data-bs-placement="top"
                            data-bs-original-title="Ver motivo de disconformidad">
                        <i
                          id="triggerModal"
                          class="cursor-pointer fas fa-info fa-xl"
                          data-toggle="modal"
                          data-target="#disagreedModal"
                          data-row-id="{{ object.id }}"
                          data-content="{{ object.comment }}">
                        </i>
                      </span>
                    {% else %}
                      <i class="fas fa-info fa-xl" style="color: #999999;"></i>
                    {% endif %}
                  </td>
                  {% comment %} info tooltip for disagreed ends {% endcomment %}
      
                  <!-- BUTTONS SECTION -->
                  <td class="align-middle dt-fixed-right">
                    <div>
                      <!-- Sending actions -->
                      {% if object.status.code == 'agreed' %}
                        {% if object.country.iso_code == 'ES' and object.count_split_payments is None %}
                          <i class="fa-solid fa-up-right-from-square btn btn-icon btn-warning"
                            id="buttonSendAEAT"
                            data-year="{{ object.year }}"
                            data-period="{{ object.period.code }}"
                            data-period-description="{{ object.period.description }}"
                            data-model="{{ object.model.code }}"
                            data-url="{{ object.get_file_url }}"
                            data-model-id="{{object.id}}"
                            data-result="{{object.result.description}}"
                            data-amount="{{object.amount}}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Presentar en la AEAT"></i>
                        {% elif object.country.iso_code == 'GB' and object.model.code == 'GB-VAT-PROOF' %}
                          {% if seller.hmrc_access_seller.is_access_granted %}
                            <i class="fa-solid fa-up-right-from-square btn btn-icon btn-warning"
                              id="buttonSendHRC"
                              data-model-id="{{object.id}}"
                              data-date-from="{{ object.convert_json_pdf_to_dict.DATE_FROM }}"
                              data-date-to="{{ object.convert_json_pdf_to_dict.DATE_TO }}"
                              data-result="{{ object.result.description }}"
                              data-amount="{{ object.amount }}"
                              data-url="{{ object.get_file_url }}"
                              onclick="vatReturnModal(this)"
                              data-bs-toggle="tooltip"
                              data-bs-placement="top"
                              title="Presentar en la HMRC"></i>
                          {% else %}
                            <i class="fa-solid fa-up-right-from-square btn btn-icon btn-warning"
                                id="buttonSendHRC"
                                onclick="grantAccessModal()"
                                title="Presentar en la HMRC"></i>
                            {% endif %}
                        {% else %}
                          <i class="fa-solid fa-up-right-from-square btn btn-icon btn-warning invisible-placeholder"></i>
                        {% endif %}
                        {% if object.model.code in fax_models and not object.fax_destination_id %}
                          <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                            title="Enviar FAX" style="background-color:#13abca; color:white;" style="color: #427ddb;"
                            href="{% url 'app_fax:fax' seller.shortname object.pk %}">
                            <i class="fa-regular fa-envelope"></i>
                          </a>
                        {% endif %}
                        {% if object.m5472_signed and not object.fax_destination_id %}
                          <a class="btn btn-icon " data-bs-toggle="tooltip" data-bs-placement="top"
                            title="Ver modelo firmado y enviar FAX" style="background-color:#13abca; color:white;"
                            href="{% url 'app_fax:fax' seller.shortname object.pk %}">
                            <i class="fa-regular fa-envelope"></i>
                          </a>
                        {% endif %}
                      {% else %}
                        <i class="fa-solid fa-up-right-from-square btn btn-icon btn-warning invisible-placeholder"></i>
                      {% endif %}
                      <!-- Sending actions -->
                      
                      <!-- Preview button | Vista previa -->
                      {% if object.receipt_file or object.substitute_presentation or object.complementary_presentation or object.resolution_presentation or object.nrc_file or object.confirmation_lipe_signed or object.m5472_signed or object.report_fax %}
                      <div class="btn-group dropstart" data-bs-toggle="tooltip" data-bs-placement="top" title="Vista Previa" style="margin-bottom: 2.5%;">
                        <button class="btn btn-icon btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                          <i class="fa-solid fa-eye"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a href="#" onclick="previewDocument('{{ object.get_file_url }}')">
                              <i class="fa-regular fa-file-pdf"></i>
                              Vista Previa Modelo
                            </a>
                          </li>
                          {% if object.receipt_file %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.receipt_file.url }}')">
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Vista Previa Recibo
                              </a>
                            </li>
                          {% endif %}
                          {% if object.substitute_presentation %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.substitute_presentation.url }}')">
                                <i class="fa-regular fa-file-pdf"></i>
                                Vista Previa Sustitutiva
                              </a>
                            </li>
                          {% endif %}
                          {% if object.complementary_presentation %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.complementary_presentation.url }}')">
                                <i class="fa-regular fa-file-pdf"></i>
                                Vista Previa Complementaria
                              </a>
                            </li>
                          {% endif %}
                          {% if object.resolution_presentation %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.resolution_presentation.url }}')">
                                <i class="fa-regular fa-file-pdf"></i>
                                Vista Previa Resolución
                              </a>
                            </li>
                          {% endif %}
                          {% if object.nrc_file %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.nrc_file.url }}')">
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Vista Previa Captura NRC
                              </a>
                            </li>
                          {% endif %}
                          {% if object.confirmation_lipe_signed %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.confirmation_lipe_signed.url }}')">
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Vista Previa Confirmación Firmada
                              </a>
                            </li>
                          {% endif %}
                          {% if object.m5472_signed %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.m5472_signed.url }}')">
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Vista Previa Modelo Firmado
                              </a>
                            </li>
                          {% endif %}
                          {% if object.report_fax %}
                            <li>
                              <a href="#" onclick="previewDocument('{{ object.report_fax.url }}')">
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Vista Previa Justificante FAX
                              </a>
                            </li>
                          {% endif %}
                        </ul>
                      </div>
                      {% else %}
                      <button class="btn btn-icon btn-success" type="button" data-bs-toggle="tooltip" data-bs-placement="top" title="Vista Previa" onclick="previewDocument('{{ object.get_file_url }}')">
                        <i class="fa-solid fa-eye"></i>
                      </button>
                      {% endif %}

                      <!-- Download actions -->
                      <div class="btn-group dropstart" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargas" style="margin-bottom: 2.5%;">
                        <button class="btn btn-icon btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                          <i class="fa-solid fa-download "></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a href="{{ object.get_file_url }}" target="_blank" download>
                              <i class="fa-regular fa-file-pdf"></i>
                              Descargar modelo
                            </a>
                          </li>
                          {% if object.receipt_file %}
                            <li>
                              <a href="{{ object.receipt_file.url }}" target="_blank" download>
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Descargar recibo
                              </a>
                            </li>
                          {% endif %}
                          {% if object.substitute_presentation %}
                            <li>
                              <a href="{{ object.substitute_presentation.url }}" target="_blank" download>
                                <i class="fa-regular fa-file-pdf"></i>
                                Descargar Sustitutiva
                              </a>
                            </li>
                          {% endif %}
                          {% if object.complementary_presentation %}
                            <li>
                              <a href="{{ object.complementary_presentation.url }}" target="_blank" download>
                                <i class="fa-regular fa-file-pdf"></i>
                                Descargar Complementaria
                              </a>
                            </li>
                          {% endif %}
                          {% if object.resolution_presentation %}
                            <li>
                              <a href="{{ object.resolution_presentation.url }}" target="_blank" download>
                                <i class="fa-regular fa-file-pdf"></i>
                                Descargar Resolución
                              </a>
                            </li>
                          {% endif %}
                          {% if object.country.iso_code == 'ES' or object.model.code == 'IT-VATANNUALE' %}
                            <li>
                              <a href="#" onclick="generate_txt(this)">
                                <i class="fa-regular fa-file-lines"></i>
                                Descargar TXT
                              </a>
                            </li>
                          {% endif %}
                          {% if object.model.code == 'IT-LIPE' %}
                            <li>
                              <a href="#" id="downloadButton" onclick="generate_xml(this)">
                                <i class="fa-regular fa-file-code"></i>
                                Descargar XML
                              </a>
                            </li>
                          {% endif %}
                          {% if object.nrc_file %}
                            <li>
                              <a href="{{ object.nrc_file.url }}" target="_blank" download>
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Descargar captura de NRC
                              </a>
                            </li>
                          {% endif %}
                          {% if object.confirmation_lipe_signed %}
                            <li>
                              <a href="{{ object.confirmation_lipe_signed.url }}" target="_blank" download>
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Descargar confirmación firmada
                              </a>
                            </li>
                          {% endif %}
                          {% if object.m5472_signed %}
                            <li>
                              <a href="{{ object.m5472_signed.url }}" target="_blank" download>
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Descargar modelo firmado
                              </a>
                            </li>
                          {% endif %}
                          {% if object.report_fax %}
                            <li>
                              <a href="{{ object.report_fax.url }}" target="_blank" download>
                                <i class="fa-solid fa-file-arrow-down"></i>
                                Descargar justificante FAX
                              </a>
                            </li>
                          {% endif %}
                        </ul>
                      </div>
                      <!-- Download actions -->

                      <a class="btn btn-icon btn-danger" data-bs-toggle="tooltip" data-bs-placement="top"
                        title="Eliminar"
                        href="{% url 'app_documents:presented_model_delete' seller.shortname object.pk %}">
                        <i class="feather icon-trash-2"></i>
                      </a>
      
                    </div>
                  </td>
                  <!-- BUTTONS SECTION -->
      
                </tr>
      
                {% comment %} disagreed message modal {% endcomment %}
                <div class="modal fade" id="disagreedModal" tabindex="-1" aria-labelledby="successModalLabel"
                    aria-hidden="true">
                  <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h4 class="mt-2">Motivo de disconformidad:</h4>
                      </div>
                      <div class="modal-body">
                        <!-- text comes from js -->
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-primary mt-2" onclick="closeModal()">Cerrar</button>
                      </div>
                    </div>
                  </div>
                </div>
      
                <!-- Modal errores TXT -->
                <div class="modal fade " id="modal_errors" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
                    aria-hidden="true">
                  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title" id="modalLabel">Se han detectado errores al general el TXT</h5>
                      </div>
                      <div class="modal-body">
                        <div class="col form-group form-check ">
                          <h4 class="mt-4 text-center"><b> Los errores encontrados son: </b></h4>
                          <ul id="error_list"></ul>
                        </div>
                      </div>
      
                      <div class="modal-footer d-flex justify-content-center">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal_errors"
                                aria-label="Close">Cancelar
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Modal errores TXT -->
      
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

  </div>      

  <!-- Modal Confirmación de envío a la AEAT -->
  {% include 'documents/include/model_to_AEAT/modal_sending_to_AEAT.html'  with hidden=False %}
  <!-- Modal Confirmación de envío a la AEAT -->

  <!-- Modal borrador devuelto por la AEAT -->
  {% include 'documents/include/model_to_AEAT/modal_draft_from_AEAT.html'  with hidden=False %}
  <!-- Modal borrador devuelto por la AEAT -->

  <!-- Modal vista previa -->
  {% include 'documents/include/modal_preview.html' %}
  <!-- Modal vista previa -->

  <!-- Modal para autorizar accesso en HMRC -->
  {% include 'documents/include/hmrc/modal_authorization_required.html'  with hidden=False %}
  <!-- Modal para autorizar accesso en HMRC -->

  <!-- Modal para presentar en HMRC -->
  {% include 'documents/include/hmrc/modal_send_vat_return.html'  with hidden=False %}
  <!-- Modal para presentar en HMRC -->
  

{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <!-- <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script> -->
  <!-- <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedColumns.min-v4.0.2.js"></script> -->
  <!-- sweet alert Js -->
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/fixedColumns/fixedColumns.dataTables.min-v4.0.2.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/uuid/8.3.2/uuid.min.js"></script>

  <!-- AEAT functions Js -->
  <script src="{% static 'assets/js/AEAT/aeat_functions.js' %}"></script>
  <!-- HMRC js -->
  <script src="{% static 'assets/js/hmrc/main.js' %}"></script>
  <!-- Variables globales -->
  <script>
    const GlobalXCSRFToken = '{{ csrf_token }}';
  </script>


  <script>
    // Variable de depuración
    const debug = false;

    //Variable necesaria para ser usada en el script de AEAT
    let url_model = '{{ url_model|safe }}';

    $(document).ready(function () {

      const dataTableOptions = {
        scrollX: true,
        paging: false,
        searching: true,
        ordering: true,
        truncation: true,
        info: true,
        footer: true,
        columnDefs: [
          {targets: 15, orderable: false},
          {targets: 16, orderable: false}
        ],
        language: {
          lengthMenu: "_MENU_",
          zeroRecords: "No se han encontrado modelos.",
          info: "_START_ a _END_ de un total de _TOTAL_",
          search: "Buscar:",
          infoEmpty: "No hay resultados que coincidan con su búsqueda.",
          infoFiltered: ""
        },
        dom: 'lrtip',
        fixedHeader: true,
        

        // Guarda el estado inicial de la tabla
        stateSave: true,
        stateDuration: -1,

        // Habilita la tabla responsiva
        responsive: true,
        
        createdRow: function (row, data, dataIndex) {
          const excludedCells = [5, 6, 7, 15, 16]; // Exclude cells from the click event
          const modelId = $(row).find('td:first-child span').attr('data-model-id');
          const link = '/sellers/' + '{{seller.shortname}}' + '/docs/presented_models/' + modelId + '/';
      
          $(row).css('cursor', 'pointer').on('click', 'td', function (event) {
            const clickedCellIndex = $(this).index();
            if (!excludedCells.includes(clickedCellIndex)) {
              window.location.href = link;
            }
          });
        },
        initComplete: function (settings, json) {
          const state = $(this).DataTable().state.loaded();
          if (state) {
            if (state.search.search) {
              $('#search').val(state.search.search);
            }
            if (state.columns[1].search.search) {
              $('#countries').val(state.columns[1].search.search);
            }
            if (state.columns[0].search.search) {
              $('#model').val(state.columns[0].search.search);
            }
            if (state.columns[2].search.search) {
              $('#period').val(state.columns[2].search.search);
            }
            if (state.columns[3].search.search) {
              $('#year').val(state.columns[3].search.search);
            }
            if (state.columns[8].search.search) {
              $('#status').val( state.columns[8].search.search.replace('^', '').replace('$', '') );
            }
          }
        }
      };
      
      const dataTable = $("#list-table").DataTable(dataTableOptions);

      $("#search").on("input", function () {
        const filtro = $(this).val();
        debug && console.log(filtro);
        dataTable.search(filtro).draw();
      });

      $("#year").on("change", function () {
        const filtro = $(this).val();
        debug && console.log(filtro);
        dataTable.column(3).search(filtro).draw();
      });

      $("#model").on("change", function () {
        const filtro = $(this).val();
        debug && console.log(filtro);
        dataTable.column(0).search(filtro).draw();
      });

      $("#status").on("change", function () {
        const filtro = $(this).val();
        debug && console.log(filtro);
        const regexFiltro = "^" + filtro + "$";
        if (filtro) {
          dataTable.column(8).search(regexFiltro, true, false).draw();
        } else {
          dataTable.column(8).search(filtro).draw();
        }
      });

      $("#countries").on("change", function () {
        const filtro = $(this).val();
        debug && console.log(filtro)
        dataTable.column(1).search(filtro).draw();
      });

      $("#period").on("change", function () {
        const filtro = $(this).val();
        debug && console.log(filtro)
        dataTable.column(2).search(filtro).draw();
      });
    });

    const getFileName = (file) => {
      const fileName = file.split('\\').pop().split('/').pop();
      return fileName;
    }

    // Function to generate xml file
    async function generate_xml(rowData) {
      const shortname = '{{seller.shortname}}';
      let seller_id = '{{seller.id}}';
      let row = rowData.closest('tr');
      let cells = row.getElementsByTagName('td');
      let period = cells[2].innerText.replace('Trimestre ', '')
      let year = cells[3].innerText;
      let model = cells[0].innerText;

      if (model.includes('Italia')) {
        model = model.replace('Modelo ', '').replace(' (Italia)', '')
      }
      if (period == 'Anual') {
        period = '0A'
      } else {
        period = 'Q' + period
      }

      var url = `/docs/presented_models/generate_xml/${seller_id}/${period}/${year}/${model}/`;

      const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        // stop timer when hover
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      });

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/xml',
            'X-CSRFToken': '{{ csrf_token }}'
          }
        });

        if (response.status == 200) {
          const disposition = response.headers.get('Content-Disposition');
          const file_name = disposition.split('filename=')[1].replace(/"/g, ''); // Remove surrounding quotes
          const blob = await response.blob();
          const blobUrl = URL.createObjectURL(blob);
          const link = document.createElement('a');

          link.href = blobUrl;
          link.download = `${file_name}.xml`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          Toast.fire({
            icon: 'success',
            title: 'XML descargado correctamente'
          })
        } else if (response.status == 204) {
          Toast.fire({
            icon: 'warning',
            title: 'No se puede generar XML, aún no se ha mandado a revisión el modelo.'
          })
        } else {
          Toast.fire({
            icon: 'error',
            title: 'Error al descargar el XML. Intenta más tarde'
          })
        }

      } catch (error) {
        Toast.fire({
          icon: 'error',
          title: error
        })
      }
    }

    // Function to copy text to the clipboard
    function copyTextToClipboard(event) {
      const textToCopy = event.target.getAttribute('data-text');
      const textElement = document.createElement('textarea');
      textElement.value = textToCopy;
      document.body.appendChild(textElement);

      textElement.select();
      textElement.setSelectionRange(0, 99999);
      document.execCommand('copy');
      document.body.removeChild(textElement);

      $("span.copyableText").click(function () {
        $(this).attr("data-bs-original-title", "Copiado!");
        $(this).tooltip('show');
        $(this).tooltip({
          trigger: 'hover'
        })
      });

      // reset tooltip to copiar  after mouse leave
      $("span.copyableText").mouseleave(function () {
        $(this).attr("data-bs-original-title", "Copiar");
        $(this).tooltip('show');
        $(this).tooltip({
          trigger: 'hover'
        })
      });
    }

    const copyableElements = document.querySelectorAll('.copyableText');
    copyableElements.forEach(function (element) {
      element.addEventListener('click', copyTextToClipboard);
    });

    document.querySelectorAll('#triggerModal').forEach(function (element) {
      element.addEventListener('click', function () {
        const content = element.getAttribute('data-content');
        const modal = document.getElementById('disagreedModal');
        const modalBody = modal.querySelector('.modal-body');

        modalBody.innerHTML = content;
        $('#disagreedModal').modal('show');
      });
    });

    function closeModal() {
      $('#disagreedModal').modal('hide');
    }

    function detectBrowser() {
      let userAgent = navigator.userAgent.toLowerCase();
      debug && console.log(`User Agent es: ${userAgent}`);
      
      if (userAgent.indexOf('edg') > -1) {
        return 'edge';
      } else if (userAgent.indexOf('opr') > -1) {
        return 'opera';
      } else if (userAgent.indexOf('chrome') > -1 && userAgent.indexOf('safari') > -1) {
        return 'chrome';
      } else if (userAgent.indexOf('firefox') > -1) {
        return 'firefox';
      } else if (userAgent.indexOf('safari') > -1 && userAgent.indexOf('chrome') === -1) {
        return 'safari';
      } else if (userAgent.indexOf('msie') > -1 || userAgent.indexOf('trident') > -1) {
        return 'ie';
      } else {
        return 'other';
      }
    }

    function previewDocument(url) {
      const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));

      // Obtener la altura disponible del viewport
      const viewportHeight = $(window).height();

      // Ajustar el tamaño máximo del modal al 100% del viewport (para dejar espacio para encabezado y pie de página)
      const modalBodyHeight = viewportHeight * 1; // 1 | es 100% del viewport

      // Establecer la altura del modal-body y el iframe
      $('#previewModal .modal-body').css('max-height', modalBodyHeight + 'px');
      $('#documentPreview').css('height', (modalBodyHeight - 160) + 'px'); // Ajusta según necesidades


      // Detectar el navegador y mostrar el mensaje si no es Chrome
      const browser = detectBrowser();
      debug && console.log('Detected Browser:', browser); 
      const warningMessage = document.getElementById('browser-warning');
      if (browser !== 'chrome') {
        warningMessage.textContent = `Está usando el navegador ${browser}. Podría tener problemas en la visualización. Sugerimos que use Chrome.`;
        warningMessage.style.display = 'block';
      } else {
        warningMessage.style.display = 'none';
      }

      document.getElementById('documentPreview').src = url;
      previewModal.show();
    }
    
    // Listener para limpiar contenido del modal al cerrarse
    $('#previewModal').on('hidden.bs.modal', function () {
      document.getElementById('documentPreview').src = "";
    });

    function vatReturnModal(element){
      $('#sendVatReturnModal').modal('show');

      const modelId = element.getAttribute('data-model-id');
      const dataFrom = element.getAttribute('data-date-from');
      const dataTo = element.getAttribute('data-date-to');
      const result = element.getAttribute('data-result');
      const amout = element.getAttribute('data-amount');
      const url = element.getAttribute('data-url');

      // updates the information in the modal
      $('#vat-return-detail-date-from').text(dataFrom);
      $('#vat-return-detail-date-to').text(dataTo);
      $('#vat-return-detail-amount').text('£ '+ amout);
      $('#vat-return-detail-result').text(result);
      $('#vat-return-detail-model-link').attr('href', url);
      $('#sendVatReturnBtn').attr('data-model-id', modelId);
      
    }

    async function submitVatReturn(button){

      const closeBtn = document.getElementById('close-presentation-hmrc');
      closeBtn.remove();

      const infoSection = document.querySelectorAll('.before-send-section');
      const loadingSection = document.querySelectorAll('.loading-send-section');
      
      infoSection.forEach(function (element) {
        element.classList.add('hide');
      });

      loadingSection.forEach(function (element) {
        element.classList.add('show')
      });

      const modelId = button.getAttribute('data-model-id');
      let url = `{% url 'app_hmrc:hmrc_vat_return_send' 'SHORTNAME_PLACEHOLDER' 'MODEL_PLACEHOLDER' %}`;
      url = url.replace('SHORTNAME_PLACEHOLDER', '{{ seller.shortname }}');
      url = url.replace('MODEL_PLACEHOLDER', modelId);

      const formData = new FormData();
      formData.append('confirmation', $('#vatReturnCheckAgreed').is(':checked'));
      const fraudPrevHeaders = await getHrmcFraudPreventionHeaders();

      // this part is for testing. Delete when finished testing
      const periodKey = getTestingPeriodKey();
      formData.append('period_key', periodKey);
      // this part is for testing. Delete when finished testing

      response = await fetch(url, {
        method: 'POST',
        headers: fraudPrevHeaders,
        body: formData
      });

      data = await response.json();
      $('#sendVatReturnModal').modal('hide');
      let responseHtml = renderResponseHtml(data);

      if (!response.ok){
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          html: responseHtml,
          customClass: {
            confirmButton: 'w-100 btn-dark',
            actions: 'swal2-action-div-full-width'
          }
        })
      }
      else{
        Swal.fire({
            icon: 'success',
            title: 'Enhorabuena!',
            html: responseHtml,
            showCancelButton: true,
            cancelButtonText: 'Ver recibo',
            customClass: {
              confirmButton: 'w-100 btn-dark',
              actions: 'swal2-action-div-full-width',
              cancelButton: 'btn-link'
            }
          }).then((result) => {
              if (result.isConfirmed) {
                window.location.reload();
              } else if (result.isDismissed) {
                window.open(data.file_path, '_blank');
                window.location.reload();
              }
          });

      }

    }

    function renderResponseHtml(data){
      if (data.errors){
        return `
            <p>No se pudo enviar la declaración a la HMRC. Acontinuación se muestran los errores:</p>
            <div class="alert alert-danger text-start rounded" role="alert">
                ${data.errors.map(error => `
                    <div class="error-item">
                        <p class="small mb-1 fw-bold text-danger">${error.title}</p>
                        <p class="text-secondary">${error.message}</p>
                    </div>
                `).join('')}
            </div>
        `;
      }
      return `
        <p>El modelo ha sido enviado a la HMRC correctamente.</p>
        <p>¡Esta página se recargará luego de realizar alguna de las siguientes acciones!</p>
      `;
    }

    $('#sendVatReturnModal').on('hidden.bs.modal', function () {
      // Reset checkbox and button in one line
      $('#vatReturnCheckAgreed').prop('checked', false);
      $('#sendVatReturnBtn').prop('disabled', true);

      // Remove classes from sections using jQuery chaining and optimization
      $('.before-send-section').removeClass('hide');
      $('.loading-send-section').removeClass('show');

      if (!$('#close-presentation-hmrc').length) {
          $('<button>', {
              type: 'button',
              class: 'btn-close',
              'data-bs-dismiss': 'modal',
              'aria-label': 'Close',
              id: 'close-presentation-hmrc'
          }).appendTo('#sendVatReturnModal .modal-header');
      }
    });

    $('#vatReturnCheckAgreed').change(function(){
      $('#sendVatReturnBtn').prop('disabled', !$(this).is(':checked'));
    });

    // this part is for testing. Delete when finished testing
    function getTestingPeriodKey(){
      let periodKey = localStorage.getItem('period_key');

      if (periodKey) {
        let prefix = periodKey.slice(0, -2);
        let letter = periodKey[periodKey.length - 2];
        let number = parseInt(periodKey[periodKey.length - 1], 10);

        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        
        if (number < 9) {
            number++;
        } else {
            number = 1;
            let letterIndex = alphabet.indexOf(letter);
            letter = alphabet[letterIndex + 1];
        }
        periodKey = prefix + letter + number;
        localStorage.setItem('period_key', periodKey);

      }
      else {
        periodKey = '18A1';
        localStorage.setItem('period_key', periodKey);
      }

      return periodKey;

    }

  </script>
{% endblock javascripts %}
