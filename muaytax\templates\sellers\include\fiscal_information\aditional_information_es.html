{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="card-body border-top pro-det2-edit collapse show" id="pro-det2-edit-1">
    <form>
        {% if object.legal_entity == 'self-employed' %}
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Estimación directa simplificada</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.is_direct_estimation %}
                    Sí
                {% elif object.is_direct_estimation == False %}
                    No
                {% else %}
                    -
                {% endif %}
            </div>
        </div>
        {% endif %}
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Modelo ES-123</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.check_model_es_123 == True %}
                    Requerido
                {% elif object.check_model_es_123 == False %}
                    No Requerido
                {% else %}
                    -
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Modelo ES-111</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.check_model_es_111 == True %}
                    Requerido
                {% elif object.check_model_es_111 == False %}
                    No Requerido
                {% else %}
                    -
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Modelo ES-115</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.check_model_es_115 == True %}
                    Requerido
                {% elif object.check_model_es_115 == False %}
                    No Requerido
                {% else %}
                    -
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Modelo ES-131</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.check_model_es_131 == True %}
                    Requerido
                {% elif object.check_model_es_131 == False %}
                    No Requerido
                {% else %}
                    -
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-4 text-sm-end col-form-label font-weight-bolder">Modelo ES-216</label>
            <div class="col-1">
                :
            </div>
            <div class="col-7">
                {% if object.check_model_es_216 == True %}
                    Requerido
                {% elif object.check_model_es_216 == False %}
                    No Requerido
                {% else %}
                    -
                {% endif %}
            </div>
        </div>
    </form>
</div>
<div class="card-body border-top pro-det2-edit collapse " id="pro-det2-edit-2">
    <form id="aditionalInfoESForm" class="form-horizontal" method="post" enctype="multipart/form-data">
        {% csrf_token %}

        {% if object.legal_entity == 'self-employed' %}
        <div class="form-group row">
            <label for="{{ formAditionalInfoES.is_direct_estimation.id_for_label }}" class="col-4 col-form-label text-sm-end">
                {{ formAditionalInfoES.is_direct_estimation.label }}
                {% if formAditionalInfoES.is_direct_estimation.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if formAditionalInfoES.is_direct_estimation.errors %}
                    {% crispy_field formAditionalInfoES.is_direct_estimation 'class' 'form-control is-invalid' %}
                    <span class="text-danger">{{ formAditionalInfoES.is_direct_estimation.errors }}</span>
                    {% else %}
                    {% for choice in formAditionalInfoES.is_direct_estimation.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input"
                                type="radio" id="{{ formAditionalInfoES.is_direct_estimation.id_for_label }}_{{ forloop.counter0 }}"
                                name="{{ formAditionalInfoES.is_direct_estimation.html_name }}"
                                value="{{ choice.0 }}" {% if formAditionalInfoES.is_direct_estimation.value == choice.0 %}checked{% endif %}
                                {% if formAditionalInfoES.is_direct_estimation.field.required %}required{% endif %}>
                            <label class="form-check-label" for="{{ formAditionalInfoES.is_direct_estimation.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        {% endif %}

        <div class="form-group row">
            <label for="{{ formAditionalInfoES.check_model_es_123.id_for_label }}" class="col-4 col-form-label text-sm-end">
                Modelo 123
                {% if formAditionalInfoES.check_model_es_123.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if formAditionalInfoES.check_model_es_123.errors %}
                    {% crispy_field formAditionalInfoES.check_model_es_123 'class' 'form-control is-invalid' %}
                    <span class="text-danger">{{ formAditionalInfoES.check_model_es_123.errors }}</span>
                    {% else %}
                    {% for choice in formAditionalInfoES.check_model_es_123.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input"
                                type="radio" id="{{ formAditionalInfoES.check_model_es_123.id_for_label }}_{{ forloop.counter0 }}"
                                name="{{ formAditionalInfoES.check_model_es_123.html_name }}"
                                value="{{ choice.0 }}" {% if formAditionalInfoES.check_model_es_123.value == choice.0 %}checked{% endif %}
                                {% if formAditionalInfoES.check_model_es_123.field.required %}required{% endif %}>
                                
                            <label class="form-check-label" for="{{ formAditionalInfoES.check_model_es_123.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <div class="form-group row">
            <label for="{{ formAditionalInfoES.check_model_es_111.id_for_label }}" class="col-4 col-form-label text-sm-end">
                Modelo 111
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if formAditionalInfoES.check_model_es_123.errors %}
                    {% crispy_field formAditionalInfoES.check_model_es_123 'class' 'form-control is-invalid' %}
                    <span class="text-danger">{{ formAditionalInfoES.check_model_es_123.errors }}</span>
                    {% else %}
                    {% for choice in formAditionalInfoES.check_model_es_111.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input" type="radio"
                                id="{{ formAditionalInfoES.check_model_es_111.id_for_label }}_{{ forloop.counter0 }}"
                                name="{{ formAditionalInfoES.check_model_es_111.html_name }}"
                                value="{{ choice.0 }}" 
                                {% if formAditionalInfoES.check_model_es_111.value == choice.0 %}checked{% endif %}>

                            <label class="form-check-label" for="{{ formAditionalInfoES.check_model_es_111.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        
        <div class="form-group row">
            <label for="{{ formAditionalInfoES.check_model_es_115.id_for_label }}" class="col-4 col-form-label text-sm-end">
                Modelo 115
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if formAditionalInfoES.check_model_es_123.errors %}
                    {% crispy_field formAditionalInfoES.check_model_es_123 'class' 'form-control is-invalid' %}
                    <span class="text-danger">{{ formAditionalInfoES.check_model_es_123.errors }}</span>
                    {% else %}
                    {% for choice in formAditionalInfoES.check_model_es_115.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input" type="radio"
                                id="{{ formAditionalInfoES.check_model_es_115.id_for_label }}_{{ forloop.counter0 }}"
                                name="{{ formAditionalInfoES.check_model_es_115.html_name }}"
                                value="{{ choice.0 }}" 
                                {% if formAditionalInfoES.check_model_es_115.value == choice.0 %}checked{% endif %}>
                                
                            <label class="form-check-label" for="{{ formAditionalInfoES.check_model_es_115.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <div class="form-group row">
            <label for="{{ formAditionalInfoES.check_model_es_131.id_for_label }}" class="col-4 col-form-label text-sm-end">
                Modelo 131
                {% if formAditionalInfoES.check_model_es_131.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if formAditionalInfoES.check_model_es_31.errors %}
                    {% crispy_field formAditionalInfoES.check_model_es_131 'class' 'form-control is-invalid' %}
                    <span class="text-danger">{{ formAditionalInfoES.check_model_es_131.errors }}</span>
                    {% else %}
                    {% for choice in formAditionalInfoES.check_model_es_131.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input"
                                type="radio" id="{{ formAditionalInfoES.check_model_es_131.id_for_label }}_{{ forloop.counter0 }}"
                                name="{{ formAditionalInfoES.check_model_es_131.html_name }}"
                                value="{{ choice.0 }}" {% if formAditionalInfoES.check_model_es_131.value == choice.0 %}checked{% endif %}
                                {% if formAditionalInfoES.check_model.field.required %}required{% endif %}>
                            <label class="form-check-label" for="{{ formAditionalInfoES.check_model.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <div class="form-group row">
            <label for="{{ formAditionalInfoES.check_model_es_216.id_for_label }}" class="col-4 col-form-label text-sm-end">
                Modelo 216
                {% if formAditionalInfoES.check_model_es_216.field.required %} *{% endif %}
            </label>
            <div class="col-1"></div>
            <div class="col-7">
                {% if formAditionalInfoES.check_model_es_31.errors %}
                    {% crispy_field formAditionalInfoES.check_model_es_216 'class' 'form-control is-invalid' %}
                    <span class="text-danger">{{ formAditionalInfoES.check_model_es_216.errors }}</span>
                    {% else %}
                    {% for choice in formAditionalInfoES.check_model_es_216.field.choices %}
                        <div class="form-check">
                            <input class="form-check-input"
                                type="radio" id="{{ formAditionalInfoES.check_model_es_216.id_for_label }}_{{ forloop.counter0 }}"
                                name="{{ formAditionalInfoES.check_model_es_216.html_name }}"
                                value="{{ choice.0 }}" {% if formAditionalInfoES.check_model_es_216.value == choice.0 %}checked{% endif %}
                                {% if formAditionalInfoES.check_model.field.required %}required{% endif %}>
                            <label class="form-check-label" for="{{ formAditionalInfoES.check_model.id_for_label }}_{{ forloop.counter0 }}">{{ choice.1 }}</label>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-form-label"></label>
            <div class="col-sm-7">
                <button type="submit" name="aditional_info_es_submit" class="btn btn-primary">Actualizar</button>
            </div>
        </div>
    </form>
</div>