// === sellerVata_formIVA_base.js ===

// Objeto global de logs con control de depuración desde Django
window.DebugLogger = {
    log(...args) {
        if (window.debug) {
            console.log('[DEBUG]', ...args);
        }
    },
    warn(...args) {
        if (window.debug) {
            console.warn('[DEBUG]', ...args);
        }
    },
    error(...args) {
        if (window.debug) {
            console.error('[DEBUG]', ...args);
        }
    },
    group(title) {
        if (window.debug) {
            console.group(title);
        }
    },
    groupEnd() {
        if (window.debug) {
            console.groupEnd();
        }
    }
};

// Objeto global base para agrupar toda la lógica del formulario IVA
window.FormIVA = window.FormIVA || {
    settings: {
        isFormProcessed: false,
        submittedCountries: [],
        partialSaved: false,
        isNewForm: false
    },
    initSettings: function ({ isFormProcessed, submittedCountries, partialSaved, isNewForm }) {
        this.settings.isFormProcessed = isFormProcessed;
        this.settings.submittedCountries = submittedCountries;
        this.settings.partialSaved = partialSaved;
        this.settings.isNewForm = isNewForm;

        DebugLogger.log("🧩 FormIVA settings inicializados:", this.settings);
    }
};

// Inicialización diferida (por si es necesario reactivar desde otros módulos)
document.addEventListener('DOMContentLoaded', () => {
    DebugLogger.log("✅ [FormIVA] Base JS cargado y listo");
});
