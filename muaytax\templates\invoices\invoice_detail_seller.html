{% extends "layouts/base.html" %}
{% load static %}

{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/flag/flag-icons.min-v6.6.6.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/vuetify/vuetify.min-v3.1.5.css" />
  <style scoped>

    .custom-icon-small {
      font-size: 12px; /* Tamaño más pequeño para el ícono */
    }

    #swal2-title{
      margin-bottom: 30px;
    }

    .hover-effect {
      transition: transform 0.3s ease; /* Transición suave */
    }

    .hover-effect:hover {
      transform: scale(1.1); /* Aumentar el tamaño al 120% */
    }

    .pdf {
      width: 100%;
      height: 100vh;
      /* height: 1200px; */
    }
    .pdf.sticky{
        position: -webkit-sticky; /* Safari */
        position: sticky;
        top: 0;
      }
    .form-group {
      margin-bottom: 1rem !important;
    }
    .concept {
      background-color: #F4F7FA;
      border: 1px solid #E9ECEF;
      border-radius: 10px;

      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
    }
    .total {
      background-color: #F4F7FA;
      border: 1px solid #E9ECEF;
      border-radius: 10px;

      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
    }
    .totalKey {
      justify-content: flex-start;
      align-items: flex-start;
      text-align: left;
      padding-left: 4rem;
    }
    .totalValue {
      justify-content: flex-end;
      align-items: flex-end;
      text-align: right;
      padding-right: 4rem;
    }
    input {
      direction: ltr !important;
    }
  </style>
{% endblock stylesheets %}

{% block title %}
  Facturas
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
              Facturas: Detalle Factura
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                {% comment %}
                  {% if seller.name is not None %}
                    <li class="breadcrumb-item">
                      {{seller.name.capitalize}}
                    </li>
                  {% endif %}
                {% endcomment %}
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Facturas</a>
                </li>
                <li class="breadcrumb-item">
                    <a href=".">Detalle Factura</a>
                </li>
              </ul>
            </div>            
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
<div class="card row">
  <fieldset disabled>
    <div id="card-header" class="card-header">
      <div class="row" style="display: none;" v-show="true"> 
          <div class="col-9 d-flex justify-content-start align-items-center text-left">    
                <h3><b>Título:</b></h3> &nbsp; &nbsp;            
                <span v-show="editName != true">
                    <h3> [[ inputInvoiceName ]] </h3>
                </span>     
          </div>
          <div class="col-3 d-flex justify-content-end align-items-center text-center">
            <div class="w-100 badge badge-xl rounded-pill"  :style="'background-color: ' + getStatusBg(dj.invoice.status) + ';'" >
              <h3>
                <span id="invoiceStatus" class="badge badge-xl text-white">
                  [[ getStatusByCode(dj.invoice.status) ]]
                </span>
              </h3>
            </div>
          </div>
          <div class="col-12">
            <span>
              Última Modificación: 
              [[ new Date(dj.invoice.modified_at).toLocaleDateString("es-ES") ]]
              [[ new Date(dj.invoice.modified_at).toLocaleTimeString() ]]
            </span>
          </div>
          {% if invoice and invoice.status.pk == "revised" and invoice.is_generated is True %}
          {% comment %} <div class="col-12">
            <span class="d-flex justify-content-end align-items-center text-left">
              <h5><b>DESCARGAR: &nbsp; &nbsp;</b></h5> 
              <a href="#" id="download_xml" onclick="generateInvoiceXML('{{seller.shortname}}', '{{invoice.pk}}')" data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar factura eléctronica">
                <img src="{% static 'assets/images/logos/png/logo-facturae.png' %}" alt="Descargar factura digital" style="height: 40px;" class="hover-effect" />
              </a>
            </span>
          </div> {% endcomment %}
          {% endif %}
      </div>
    </div>
  </fieldset>

  <div class="card-body">
    {% csrf_token %}
    <div class="row">
      <!-- PDF VIEWER  -->
      {% if object.file %} 
        <div class="col-5" >
          {% if ".jpg" in object.file.name or ".JPG" in object.file.name or ".png" in object.file.name or ".PNG" in object.file.name %} 
            <img src="{{ object.file.url }}" width="100%">
          {% else %} 
          <object
            type="application/pdf"
            class="pdf border"
            data="{{ object.file.url }}"
          >
            <embed
              type="application/pdf"
              class="pdf border"
              src="{{ object.file.url }}"
            ></embed>
            <a href="{{ object.file.url }}">
              Descargar
            </a>
          </object>
          {% endif %}
        </div>
      {% elif invoice.is_generated == True %}
        <div class="col-6" > 
            <object
              type="application/pdf"
              class="pdf border"
              data="{% url 'app_invoices:seller_invoice_file' seller.shortname invoice.pk %}"
            >
              <embed
                type="application/pdf"
                class="pdf border"
                src="{% url 'app_invoices:seller_invoice_file' seller.shortname invoice.pk %}"
              ></embed>
              <a href="{% url 'app_invoices:seller_invoice_file' seller.shortname invoice.pk %}">
                Descargar
              </a>
            </object>
        </div>
      {% endif %}
      <!-- PDF VIEWER  -->

      <!-- FORM BODY  -->
      {% if invoice and invoice.status.pk == "revised" %}
      <!-- BODY -->
      <div class="col" id="formBody">
        <div style="display: none;" v-show="true">
          <!-- Fieldset 1 -->
          <form id="form" method="post" action="." class="needs-validation">
          <fieldset id="fieldset1">                
            <!-- Invoice Category & Invoice Type -->
            <div class="row alert alert-warning" role="alert">
              <div class="col-11">
                <span v-if="inputInvoiceCategory">
                  <b>Categoría: </b> &nbsp; [[ getCategoryByCode(inputInvoiceCategory)?.description ]]
                  <span v-if="inputInvoiceType">
                    &nbsp; | &nbsp;
                    <b>Tipo: </b> &nbsp; [[ getTypeByCode(inputInvoiceType)?.description  ]]
                  </span>
                </span>
                <span v-if="!inputInvoiceCategory">
                  <b> Sin Categoría/Tipo Asignado </b>
                </span>
              </div>
              {% comment %}
              <div class="col-1">
                <a href="#" @click="showCategory=!showCategory">
                  <i class="fa-solid fa-xl fa-pen-to-square"></i>
                </a>
              </div>
              {% endcomment %}
            </div>
            <div class="row" v-show="!inputInvoiceCategory || !inputInvoiceType || showCategory == true">
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceCategory">
                  <b>Categoría de Factura * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputInvoiceCategory"
                  name="inputInvoiceCategory"
                  v-model="inputInvoiceCategory"
                  v-on:change="onChangeCategory"
                  required
                >
                  <option :value="null" disabled selected>Seleccione la Categoría de la Factura</option>
                  <option :value="category.pk" v-for="category in dj.categories" :key="category.pk">
                    [[ category.description ]]
                  </option>
                </select>
              </div>
              <div class="col-6 form-group form-check">
                <label class="form-label" for="invoice_type">
                  Tipo de Factura:
                </label>
                <select
                  class="form-select form-control"
                  id="inputInvoiceType"
                  name="inputInvoiceType"
                  v-model="inputInvoiceType"
                  v-on:change="onChangeType"
                  v-on:focusout="onChangeType"
                  :disabled="!inputInvoiceCategory"
                  required
                >
                  <option :value="null" disabled selected>Seleccione el Tipo de la Factura</option>
                  <option :value="type.pk" v-for="type in getTypesForCategory()" :key="type.pk" :disabled="type.pk == 'payroll'">
                    [[ type.description ]]
                  </option>
                </select>
              </div>
            </div>
            <!-- Invoice Category & Invoice Type -->
            
            
            <!-- Invoice Relations -->
            {% if invoice_relations %}
              {% for inv in invoice_relations %}
                <div class="row alert alert-info">
                  <div class="col-2"><b>ID:</b> {{ inv.pk }}</div>
                  <div class="col"><b>Num. Factura:</b> {{ inv.reference }}</div>
                  <div class="col"><b>Tipo:</b> {{ inv.invoice_type }}</div>
                  <div class="col"><b>Estado:</b> {{ inv.status }}</div>
                  <div class="col-1">
                    <a href="{% url 'app_invoices:seller_invoice' seller.shortname inv.pk %}">
                      <i class="fa-solid fa-xl fa-arrow-right"></i>
                    </a>
                  </div>
                </div>
              {% endfor %}
            {% endif %}
            <!-- Invoice Relations -->

            <!-- Invoice Relation -->
            {% comment %} <div class="row alert alert-info" v-if="dj.invoice.related_invoice == null && inputRelatedInvoice">
              <div class="col-2"><b>ID:</b> [[ getRelatedInvoiceById(inputRelatedInvoice).pk ]]</div>
              <div class="col"><b>Num. Factura:</b> [[ getRelatedInvoiceById(inputRelatedInvoice).reference ]]</div>
              <div class="col"><b>Tipo:</b> [[ getTypeByCode(getRelatedInvoiceById(inputRelatedInvoice).invoice_type).description ]]</div>
              <div class="col"><b>Estado:</b> [[ getStatusByCode(getRelatedInvoiceById(inputRelatedInvoice).status) ]]</div>
              <div class="col-1">
                <a :href="'../' + getRelatedInvoiceById(inputRelatedInvoice).pk">
                  <i class="fa-solid fa-xl fa-arrow-right"></i>
                </a>
              </div>
            </div> {% endcomment %}
            <!-- Invoice Relation -->

            <!-- Invoice Not Related && Invoice Type = 'import-dua' -->
            {% comment %} <div class="row mt-4" v-if="dj.invoice.related_invoice == null && dj.seller.contracted_accounting == true && inputInvoiceType == 'import-dua'">
              <div class="col form-group form-check alert alert-info">
                <label class="form-label" for="inputRelatedInvoice">
                  Factura Importación Relacionada:
                </label>
                <select
                  class="form-select form-control"
                  id="inputRelatedInvoice"
                  name="inputRelatedInvoice"
                  v-model="inputRelatedInvoice"
                  required
                >
                  <option :value="null" selected disabled>Seleccione la Factura de Importación:</option>    
                  {% for inv in import_invoices_not_related %}
                    {% if inv.reference %}
                      <option value="{{inv.pk}}">
                        ID: {{ inv.pk }} &nbsp; | &nbsp; 
                        Num.Factura: {{ inv.reference }} &nbsp; |  &nbsp; 
                        Total: {% if inv.total_currency %} {{ inv.total_currency }} {% else %} 0 {% endif %} {{ inv.currency.code }} &nbsp; |  &nbsp; 
                        Proveedor {{ inv.provider }}
                      </option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>
            </div> {% endcomment %}
            <!-- Invoice Not Related && Invoice Type = 'import-dua' -->

            <!-- Invoice Not Related && Invoice Type = 'import-expenses' -->
            {% comment %} <div class="row mt-4" v-if="dj.invoice.related_invoice == null && dj.seller.contracted_accounting == true && inputInvoiceType == 'import-expenses'">
              <div class="col form-group form-check alert alert-info">
                <label class="form-label" for="inputRelatedInvoice">
                  DUA Relacionado:
                </label>
                <select
                  class="form-select form-control"
                  id="inputRelatedInvoice"
                  name="inputRelatedInvoice"
                  v-model="inputRelatedInvoice"
                  required
                >
                  <option :value="null" selected disabled>Seleccione el DUA de Importación:</option>    
                  {% for inv in dua_invoices_not_related %}
                    <option value="{{inv.pk}}">
                        ID: {{ inv.pk }} &nbsp; | &nbsp; 
                        Num.Factura: {{ inv.reference }} &nbsp; |  &nbsp; 
                        Total: {% if inv.total_currency %} {{ inv.total_currency }} {% else %} 0 {% endif %} {{ inv.currency.code }} &nbsp; |  &nbsp; 
                        Proveedor: {{ inv.provider }}
                      </option>
                  {% endfor %}
                </select>
              </div>
            </div> {% endcomment %}
            <!-- Invoice Not Related && Invoice Type = 'import-expenses' -->

            <!-- Form 1: Sales (Common) -->
            <div class="row" id="sales" v-if="inputInvoiceType== 'sales' && inputInvoiceStatus != 'discard'">

              <!-- Is a AMZ invoice Generated ? -->
              <div class="col-12 mx-0 alert alert-warning w-100 rounded" role="alert" v-if="inputIsGeneratedAmz==true">
                <div class="row mx-3">
                  <div class="col-auto m-0 p-0"><i class="fa-solid fa-xl fa-triangle-exclamation" style=" vertical-align: bottom;"></i> &nbsp;  &nbsp;</div>
                  <div class="col">
                    <span class="text-start">
                      <b>Advertencia:</b> Esta factura es de una venta de Amazon (generada manualmente). <br>
                      <b>No se considerada para fines de facturación</b>, ya que su inclusión duplicaría los datos presentes en el informe de ventas de Amazon.
                    </span>
                  </div>
                  <div class="col-auto m-0 p-0">&nbsp;  &nbsp;  <i class="fa-solid fa-xl fa-triangle-exclamation" style=" vertical-align: bottom;"></i></div>
                </div>
              </div>

              <!-- Customer -->
              <div class="col-12">
                <div class="row">
                  <hr class="mt-0 mb-2"/>
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputCustomer">
                      <b>Cliente * </b> 
                    </label>
                    <v-autocomplete
                      placeholder="Seleccione Cliente"
                      variant="solo"
                      item-title="longname2"
                      item-value="pk"
                      :items="dj.customers"
                      v-model="inputCustomer"
                      v-on:focusin="onChangeType"
                      :disabled="inputInvoiceStatus == 'revised'"
                      :required="!inputCustomer"
                    ></v-autocomplete>
                    <div class="invalid-feedback">Seleccione el Cliente</div>
                  </div>
                  <div class="col-6">
                    {% comment %}
                    <div>
                      <span v-show="inputCustomer">
                        <a data-bs-toggle="modal" @click="onclickTagA('edit')" data-bs-target="#newCostumer" href="#" >
                          Editar Cliente
                        </a>
                        &nbsp; | &nbsp;
                      </span>
                      <span>
                        <a data-bs-toggle="modal" @click="onclickTagA('new')"  data-bs-target="#newCostumer" href="#" >
                          Crear Nuevo Cliente
                        </a>
                        </br>
                      </span>
                      
                    </div>
                    {% endcomment %}
                    <div v-show="inputCustomer">
                      <span>
                        <b>[[ getCustomerById(inputCustomer)?.name ]]</b> <br/>
                        NIF: [[ getCustomerById(inputCustomer)?.nif_cif_iva ]] <br/>
                        VIES: [[ getCustomerById(inputCustomer)?.vies ]] <br/>
                        [[ getCustomerById(inputCustomer)?.zip ]] <br/>
                      </span>
                    </div>
                  </div>
                  <hr class="mt-0"/>
                </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxCountry">
                  <b>País Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  v-if="inputIsDistanceSell != true"
                  required
                >
                  <option selected disabled>Seleccione el país</option>
                  <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk"
                          :data-activation-date="vat.activation_date"
                          :data-deactivation-date="vat.deactivation_date">
                    [[ getCountryNameByCode(vat.vat_country) ]]
                  </option>
                </select>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  v-if="inputIsDistanceSell == true"
                  required
                >
                  <option selected disabled>Seleccione el país</option>
                  <option :value="country.pk" v-for="country in getEuropeanCountries()"
                          :key="country.pk">
                    [[ country.name ]] (OSS)
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece
                  esta factura.
                </div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceNumber">
                  <b>Número de la Factura * </b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceNumber"
                  name="inputInvoiceNumber"
                  v-model="inputInvoiceNumber"
                  required
                />
                <div class="invalid-feedback">Introduzca el número/referencia de la factura.
                </div>
              </div>

              <div class="col-6 form-group form-check">
                <div class="row">
                  <!-- Invoice Expedition Date -->
                  <div class="col form-group form-check">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de la Factura * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceDate"
                      name="inputInvoiceDate"
                      v-model="inputInvoiceDate"
                      v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                      @blur="show_accounting_date('{{ end_date }}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                  </div>
                  <!-- Invoice Accounting Date -->
                  <div class="col form-group form-check" v-if="inputOutOfTime">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de Contabilización * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceAccountingDate"
                      name="inputInvoiceAccountingDate"
                      v-model="inputInvoiceAccountingDate"
                      min="{{ end_date }}"
                      @blur="final_date_period('{{ end_date }}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de contaibilización.
                    </div>
                  </div>
                </div>
              </div>

              <!-- Is Rectifying -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxResponsibility">
                  <b>¿Es una factura rectificativa? * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsRectifying"
                  name="inputIsRectifying"
                  v-model="inputIsRectifying"
                  v-on:change="onChangeIsRectifying"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputCurrency">
                  <b>Moneda * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputCurrency"
                  name="inputCurrency"
                  v-model="inputCurrency"
                  required
                >
                  <option selected disabled>Seleccione la moneda</option>
                  <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[
                    cur.description ]] ([[ cur.pk ]])
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>

              <!-- Is Distance Sell (OSS) -->
              <div class="col-6 form-group form-check" v-if="dj.seller.oss == true">
                <label class="form-label" for="inputIsDistanceSell">
                  ¿Es una venta a distancia?
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsDistanceSell"
                  name="inputIsDistanceSell"
                  v-model="inputIsDistanceSell"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>

              <!-- Departure Country -->
              <div class="col-6 form-group form-check" v-if="dj.seller.oss == true && inputIsDistanceSell==true">
                <label class="form-label" for="inputDepartureCountry">
                  País de salida:
                </label>
                <select
                  class="form-select form-control"
                  id="inputDepartureCountry"
                  name="inputDepartureCountry"
                  v-model="inputDepartureCountry"
                  :required="inputIsDistanceSell==true"
                >
                  <option selected disabled> Seleccione el pais</option>
                  <option :value="country.pk" v-for="country in dj.countries"
                          :key="country.pk">
                    [[ country?.name ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País de salida.</div>
              </div>
              <div class="col-6 form-group form-check" v-else></div>

              <!-- Tax Responsibility -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxResponsibility">
                  <b>Responsable de las Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxResponsibility"
                  name="inputTaxResponsibility"
                  v-model="inputTaxResponsibility"
                  required
                >
                  <option selected disabled>Seleccione el responsable</option>
                  <option :value="res.pk" v-for="res in dj.tax_responsability" :key="res.pk">
                    [[ res.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione quien es el responsable de pagar las
                  tasas.
                </div>
              </div>

              <!-- Is Equivalent Tax (eqtax) -->
              <div class="col-6 form-group form-check eqtax" v-if="{{ is_eqtax }}">
                <label class="form-label" for="is_eqtax">
                  ¿Tiene Recargo de Equivalencia?
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsEquivalentTax"
                  name="inputIsEquivalentTax"
                  v-model="inputIsEquivalentTax"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>
              <!-- Operación con inversión del sujeto pasivo -->
              <div class="col-6 form-group form-check" >
                <label class="form-label" for="inputIsReverseCharge">
                  <b> ¿Es operación con inversión del sujeto pasivo? * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsReverseCharge"
                  name="inputIsReverseCharge"
                  v-model="inputIsReverseCharge"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true" :disabled="getTotal().vatCurrency != 0">Si</option>
                </select>
              </div>
            </div>
            <!-- Form 1: Sales (Common) -->

            <!-- Form 2: Expenses (Common) -->
            <div class="row" id="expenses" v-if="inputInvoiceType == 'expenses' && inputInvoiceStatus != 'discard'">
              <!-- Provider Name -->
              <div class="col-12">
                <div class="row">
                  <hr class="mt-0 mb-2" />
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="provider">
                    <b>Proveedor * </b>
                    </label>
                    <v-autocomplete
                      placeholder="Seleccione Proveedor"
                      variant="solo"
                      item-title="longname"
                      item-value="pk"
                      :items="dj.providers"
                      v-model="inputProvider"
                      v-on:focusin="onChangeType"
                      :disabled="inputInvoiceStatus == 'revised'"
                      :required="!inputProvider"
                    ></v-autocomplete>
                    <div class="invalid-feedback">Seleccione el Proveedor.</div>
                  </div>
                  <div class="col-6">
                    {% comment %}
                    <div>
                      <span v-show="inputProvider">
                        <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#" >
                          Editar Proveedor
                        </a>
                        &nbsp; | &nbsp;
                      </span>
                      <span>
                        <a data-bs-toggle="modal" @click="onclickTagA('newProv')"  data-bs-target="#newCostumer" href="#" >
                          Crear Nuevo Proveedor
                        </a>
                        </br>
                      </span>
                    </div>                                           
                    {% endcomment %}
                    <div v-show="inputProvider">
                      <span>
                        <b>[[ getProviderById(inputProvider)?.name ]]</b> <br />
                        NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br />
                        VIES: [[ getProviderById(inputProvider)?.vies ]] <br />
                        [[ getProviderById(inputProvider)?.zip ]] <br />
                      </span>
                    </div>
                  </div>
                  <hr class="mt-0" />
                </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxCountry">
                  <b>País Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  required
                >
                  <option selected disabled>Seleccione el país</option>
                  <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk">
                    [[ getCountryNameByCode(vat.vat_country) ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceNumber">
                  <b>Numero de la Factura * </b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceNumber"
                  name="inputInvoiceNumber"
                  v-model="inputInvoiceNumber"
                  required
                />
                <div class="invalid-feedback">Introduzca el numero/referencia de la factura.</div>
              </div>

              <div class="col-6 form-group form-check">
                <div class="row">
                  <!-- Invoice Expedition Date -->
                  <div class="col form-group form-check">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de la Factura * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceDate"
                      name="inputInvoiceDate"
                      v-model="inputInvoiceDate"
                      v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                      @blur="show_accounting_date('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                  </div>
                  <!-- Invoice Accounting Date -->
                  <div class="col form-group form-check" v-if="inputOutOfTime">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de Contabilización * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceAccountingDate"
                      name="inputInvoiceAccountingDate"
                      v-model="inputInvoiceAccountingDate"
                      min="{{end_date}}"
                      @blur="final_date_period('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                  </div>
                </div>
              </div>                    

              <!-- Is Rectifying -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxResponsibility">
                  <b>¿Es una factura rectificativa? * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsRectifying"
                  name="inputIsRectifying"
                  v-model="inputIsRectifying"
                  v-on:change="onChangeIsRectifying"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputCurrency">
                  <b>Moneda * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputCurrency"
                  name="inputCurrency"
                  v-model="inputCurrency"
                  required
                >
                  <option selected disabled>Seleccione la moneda</option>
                  <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                </select>
                <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>

              <!-- Is Equivalent Tax (eqtax) -->
              <div class="col-6 form-group form-check eqtax" v-if="{{is_eqtax}}">
                <label class="form-label" for="is_eqtax">
                  ¿Tiene Recargo de Equivalencia?
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsEquivalentTax"
                  name="inputIsEquivalentTax"
                  v-model="inputIsEquivalentTax"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>
              <!-- Operación con inversión del sujeto pasivo -->
              <div class="col-6 form-group form-check" >
                <label class="form-label" for="inputIsReverseCharge">
                  <b> ¿Es operación con inversión del sujeto pasivo? * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsReverseCharge"
                  name="inputIsReverseCharge"
                  v-model="inputIsReverseCharge"

                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true" :disabled="getTotal().vatCurrency != 0">Si</option>
                </select>
              </div>
              
            </div>
            <!-- Form 2: Expenses (Common) -->

            <!-- Form 3: Tickets -->
            <div class="row" id="expenses" v-if="inputInvoiceType == 'ticket' && inputInvoiceStatus != 'discard'">
              <!-- Provider -->
              <div class="col-12">
                <div class="row">
                  <hr class="mt-0 mb-2" />
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="provider">
                    <b>Proveedor * </b>
                    </label>
                    <v-autocomplete
                      placeholder="Seleccione Proveedor"
                      variant="solo"
                      item-title="longname"
                      item-value="pk"
                      :items="dj.providers"
                      v-model="inputProvider"
                      v-on:focusin="onChangeType"
                      :disabled="inputInvoiceStatus == 'revised'"
                      :required="!inputProvider"
                    ></v-autocomplete>
                    <div class="invalid-feedback">Seleccione el Proveedor.</div>
                  </div>
                  <div class="col-6">
                    {% comment %}
                    <div>
                      <span v-show="inputProvider">
                        <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#" >
                          Editar Proveedor
                        </a>
                        &nbsp; | &nbsp;
                      </span>
                      <span>
                        <a data-bs-toggle="modal" @click="onclickTagA('newProv')"  data-bs-target="#newCostumer" href="#" >
                          Crear Nuevo Proveedor
                        </a>
                        </br>
                      </span>
                    </div> 
                    {% endcomment %}
                    <div v-show="inputProvider"> 
                      <span>
                        <b>[[ getProviderById(inputProvider)?.name ]]</b> <br />
                        NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br />
                        VIES: [[ getProviderById(inputProvider)?.vies ]] <br />
                        [[ getProviderById(inputProvider)?.zip ]] <br />
                      </span>
                    </div>
                  </div>
                  <hr class="mt-0" />
                </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxCountry">
                  <b>País Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  required
                >
                  <option selected disabled>Seleccione el pais</option>
                  <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk">
                    [[ getCountryNameByCode(vat.vat_country) ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceNumber">
                  <b>Número del Ticket * </b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceNumber"
                  name="inputInvoiceNumber"
                  v-model="inputInvoiceNumber"
                  required
                />
                <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                <div class="error">
                  {{ form.errors }}
                  {% if 'reference' in form.errors %}
                    {{ form.errors.reference }}
                  {% endif %}
                </div>
              </div>

              <div class="col-6 form-group form-check">
                <div class="row">
                  <!-- Invoice Date -->
                  <div class="col form-group form-check">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de la Factura * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceDate"
                      name="inputInvoiceDate"
                      v-model="inputInvoiceDate"
                      v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                      @blur="show_accounting_date('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                  </div>
                  <!-- Invoice Accounting Date -->
                  <div class="col form-group form-check" v-if="inputOutOfTime">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de Contabilización * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceAccountingDate"
                      name="inputInvoiceAccountingDate"
                      v-model="inputInvoiceAccountingDate"
                      min="{{end_date}}"
                      @blur="final_date_period('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                  </div>
                </div>
              </div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputCurrency">
                  <b>Moneda * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputCurrency"
                  name="inputCurrency"
                  v-model="inputCurrency"
                  required
                >
                  <option selected disabled>Seleccione la moneda</option>
                  <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                </select>
                <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>

              <!-- Is Equivalent Tax (eqtax) -->
              <div class="col-6 form-group form-check eqtax" v-if="{{is_eqtax}}">
                <label class="form-label" for="is_eqtax">
                  ¿Tiene Recargo de Equivalencia?
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsEquivalentTax"
                  name="inputIsEquivalentTax"
                  v-model="inputIsEquivalentTax"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>
            </div>
            <!-- Form 3: Tickets -->

            <!-- Form 4: Import Invoice -->
            <div class="row" id="import-invoice" v-if="inputInvoiceType == 'import-invoice' && inputInvoiceStatus != 'discard'">
              <!-- Provider Name -->
              <div class="col-12">
                <div class="row">
                  <hr class="mt-0 mb-2" />
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="provider">
                    <b>Proveedor * </b>
                    </label>
                    <v-autocomplete
                      placeholder="Seleccione Proveedor"
                      variant="solo"
                      item-title="longname"
                      item-value="pk"
                      :items="dj.providers"
                      v-model="inputProvider"
                      v-on:focusin="onChangeType"
                      :disabled="inputInvoiceStatus == 'revised'"
                      :required="!inputProvider"
                    ></v-autocomplete>
                    <div class="invalid-feedback">Seleccione el Proveedor.</div>
                  </div>
                  <div class="col-6">
                    {% comment %}
                    <div>
                      <span v-show="inputProvider">
                        <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#" >
                          Editar Proveedor
                        </a>
                        &nbsp; | &nbsp;
                      </span>
                      <span>
                        <a data-bs-toggle="modal" @click="onclickTagA('newProv')"  data-bs-target="#newCostumer" href="#" >
                          Crear Nuevo Proveedor
                        </a>
                        </br>
                      </span>
                    </div>                                           
                    {% endcomment %}
                    <div v-show="inputProvider">
                      <span>
                        <b>[[ getProviderById(inputProvider)?.name ]]</b> <br />
                        NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br />
                        VIES: [[ getProviderById(inputProvider)?.vies ]] <br />
                        [[ getProviderById(inputProvider)?.zip ]] <br />
                      </span>
                    </div>
                  </div>
                  <hr class="mt-0" />
                </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxCountry">
                  <b>País Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  required
                >
                  <option selected disabled>Seleccione el país</option>
                  <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk">
                    [[ getCountryNameByCode(vat.vat_country) ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceNumber">
                  <b>Número de la Factura * </b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceNumber"
                  name="inputInvoiceNumber"
                  v-model="inputInvoiceNumber"
                  required
                />
                <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
              </div>

              <div class="col-6 form-group form-check">
                <div class="row">
                  <!-- Invoice Expedition Date -->
                  <div class="col form-group form-check">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de la Factura * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceDate"
                      name="inputInvoiceDate"
                      v-model="inputInvoiceDate"
                      v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                      @blur="show_accounting_date('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                  </div>
                  <!-- Invoice Accounting Date -->
                  <div class="col form-group form-check" v-if="inputOutOfTime">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de Contabilización * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceAccountingDate"
                      name="inputInvoiceAccountingDate"
                      v-model="inputInvoiceAccountingDate"
                      min="{{end_date}}"
                      @blur="final_date_period('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                  </div>
                </div>
              </div>                    

              <!-- Is Rectifying -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxResponsibility">
                  <b>¿Es una factura rectificativa? * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsRectifying"
                  name="inputIsRectifying"
                  v-model="inputIsRectifying"
                  v-on:change="onChangeIsRectifying"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputCurrency">
                  <b>Moneda * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputCurrency"
                  name="inputCurrency"
                  v-model="inputCurrency"
                  required
                >
                  <option selected disabled>Seleccione la moneda</option>
                  <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                </select>
                <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>

              <!-- Is Equivalent Tax (eqtax) -->
              <div class="col-6 form-group form-check eqtax" v-if="{{is_eqtax}}">
                <label class="form-label" for="is_eqtax">
                  ¿Tiene Recargo de Equivalencia?
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsEquivalentTax"
                  name="inputIsEquivalentTax"
                  v-model="inputIsEquivalentTax"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>
            </div>
            <!-- Form 4: Import Invoice -->

            <!-- Form 5: Import DUA -->
            <div class="row" id="import-dua" v-if="inputInvoiceType == 'import-dua' && inputInvoiceStatus != 'discard'">
              <!-- Provider Name -->
              <div class="col-12" v-if="dj.seller.contracted_accounting">
                <div class="row">
                  <hr class="mt-0 mb-2" />
                  <div class="col-6 form-group form-check" v-if="dj.seller.contracted_accounting">
                    <label class="form-label" for="provider">
                      <b>Proveedor * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputProvider"
                      name="inputProvider"
                      v-model="inputProvider"
                      required
                    >
                      <option :value="null" selected disabled>Seleccione el proveedor:</option>
                      <option :value="prov.pk" v-for="prov in getRelatedInvoiceProvider()">
                        [[ prov.name ]]
                      </option>
                    </select>
                  </div>
                  <div class="col-6 form-group form-check" v-if="!dj.seller.contracted_accounting">
                    <label class="form-label" for="provider">
                    <b>Proveedor * </b>
                    </label>
                    <v-autocomplete
                      placeholder="Seleccione Proveedor"
                      variant="solo"
                      item-title="longname"
                      item-value="pk"
                      :items="dj.providers"
                      v-model="inputProvider"
                      v-on:focusin="onChangeType"
                      :disabled="inputInvoiceStatus == 'revised'"
                      :required="!inputProvider"
                    ></v-autocomplete>
                    <div class="invalid-feedback">Seleccione el Proveedor.</div>
                  </div>
                  <div class="col-6">
                    {% comment %}
                    <div>
                      <span v-show="inputProvider">
                        <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#" >
                          Editar Proveedor
                        </a>
                      </span>
                    </div>                                           
                    {% endcomment %}
                    <div v-show="inputProvider">
                      <span>
                        <b>[[ getProviderById(inputProvider)?.name ]]</b> <br />
                        NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br />
                        VIES: [[ getProviderById(inputProvider)?.vies ]] <br />
                        [[ getProviderById(inputProvider)?.zip ]] <br />
                      </span>
                    </div>
                  </div>
                  <hr class="mt-0" />
                </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxCountry">
                  <b>País Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  required
                >
                  <option selected disabled>Seleccione el país</option>
                  <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk">
                    [[ getCountryNameByCode(vat.vat_country) ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceNumber">
                  <b>Número del DUA * </b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceNumber"
                  name="inputInvoiceNumber"
                  v-model="inputInvoiceNumber"
                  required
                />
                <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
              </div>

              <div class="col-6 form-group form-check">
                <div class="row">
                  <!-- Invoice Expedition Date -->
                  <div class="col form-group form-check">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de la Factura * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceDate"
                      name="inputInvoiceDate"
                      v-model="inputInvoiceDate"
                      v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                      @blur="show_accounting_date('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                  </div>
                  <!-- Invoice Accounting Date -->
                  <div class="col form-group form-check" v-if="inputOutOfTime">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de Contabilización * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceAccountingDate"
                      name="inputInvoiceAccountingDate"
                      v-model="inputInvoiceAccountingDate"
                      min="{{end_date}}"
                      @blur="final_date_period('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                  </div>
                </div>
              </div>   

              <!-- Is Equivalent Tax (eqtax) -->
              <div class="col-6 form-group form-check eqtax" v-if="{{is_eqtax}}">
                <label class="form-label" for="is_eqtax">
                  ¿Tiene Recargo de Equivalencia?
                </label>
                <select
                  class="form-select form-control"
                  id="inputIsEquivalentTax"
                  name="inputIsEquivalentTax"
                  v-model="inputIsEquivalentTax"
                  required
                >
                  <option selected disabled>Seleccione el valor</option>
                  <option :value="false" selected>No</option>
                  <option :value="true">Si</option>
                </select>
              </div>
              <div class="col-6 form-group form-check" v-else></div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputCurrency">
                  <b>Moneda * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputCurrency"
                  name="inputCurrency"
                  v-model="inputCurrency"
                  required
                >
                  <option selected disabled>Seleccione la moneda</option>
                  <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                </select>
                <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>                         
            </div>
            <!-- Form 5: Import DUA -->

            <!-- Form 6: Import Expenses -->
            <div class="row" id="import-expenses" v-if="inputInvoiceType == 'import-expenses' && inputInvoiceStatus != 'discard'">
              <!-- Provider Name -->
              <div class="col-12" v-if="dj.seller.contracted_accounting">
                <div class="row">
                  <hr class="mt-0 mb-2" />
                  <div class="col-6 form-group form-check" v-if="dj.seller.contracted_accounting">
                    <label class="form-label" for="provider">
                      <b>Proveedor * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputProvider"
                      name="inputProvider"
                      v-model="inputProvider"
                      required
                    >
                      <option :value="null" selected disabled>Seleccione el proveedor:</option>                         
                      <option :value="prov.pk" v-for="prov in getRelatedInvoiceProvider()">
                        [[ prov.name ]]
                      </option>
                    </select>
                  </div>
                  <div class="col-6 form-group form-check" v-if="!dj.seller.contracted_accounting">
                    <label class="form-label" for="provider">
                    <b>Proveedor * </b>
                    </label>
                    <v-autocomplete
                      placeholder="Seleccione Proveedor"
                      variant="solo"
                      item-title="longname"
                      item-value="pk"
                      :items="dj.providers"
                      v-model="inputProvider"
                      v-on:focusin="onChangeType"
                      :disabled="inputInvoiceStatus == 'revised'"
                      :required="!inputProvider"
                    ></v-autocomplete>
                    <div class="invalid-feedback">Seleccione el Proveedor.</div>
                  </div>
                  <div class="col-6">
                    {% comment %}
                    <div>
                      <span v-show="inputProvider">
                        <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#" >
                          Editar Proveedor
                        </a>
                      </span>
                    </div>                                           
                    {% endcomment %}
                    <div v-show="inputProvider">
                      <span>
                        <b>[[ getProviderById(inputProvider)?.name ]]</b> <br />
                        NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br />
                        VIES: [[ getProviderById(inputProvider)?.vies ]] <br />
                        [[ getProviderById(inputProvider)?.zip ]] <br />
                      </span>
                    </div>
                  </div>
                  <hr class="mt-0" />
                </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputTaxCountry">
                  <b>País Tasas * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputTaxCountry"
                  name="inputTaxCountry"
                  v-model="inputTaxCountry"
                  required
                >
                  <option selected disabled>Seleccione el país</option>
                  <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk">
                    [[ getCountryNameByCode(vat.vat_country) ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputInvoiceNumber">
                  <b>Número de la Factura * </b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceNumber"
                  name="inputInvoiceNumber"
                  v-model="inputInvoiceNumber"
                  required
                />
                <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
              </div>

              <div class="col-6 form-group form-check">
                <div class="row">
                  <!-- Invoice Expedition Date -->
                  <div class="col form-group form-check">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de la Factura * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceDate"
                      name="inputInvoiceDate"
                      v-model="inputInvoiceDate"
                      v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                      @blur="show_accounting_date('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                  </div>
                  <!-- Invoice Accounting Date -->
                  <div class="col form-group form-check" v-if="inputOutOfTime">
                    <label class="form-label" for="inputInvoiceDate">
                      <b>Fecha de Contabilización * </b>
                    </label>
                    <input
                      type="date"
                      class="form-control"
                      id="inputInvoiceAccountingDate"
                      name="inputInvoiceAccountingDate"
                      v-model="inputInvoiceAccountingDate"
                      min="{{end_date}}"
                      @blur="final_date_period('{{end_date}}')"
                      required
                    />
                    <div class="invalid-feedback">Introduzca la fecha de contabilización.</div>
                  </div>
                </div>
              </div>  

              <!-- Separator -->
              <div class="col-6 form-group form-check">
              </div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="inputCurrency">
                  <b>Moneda * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputCurrency"
                  name="inputCurrency"
                  v-model="inputCurrency"
                  required
                >
                  <option selected disabled>Seleccione la moneda</option>
                  <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                </select>
                <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>  
            </div>
            <!-- Form 6: Import Expenses -->

            <!-- Form 8: Rent -->
            <div class="row" id="rents" v-if="inputInvoiceType == 'rent' && inputInvoiceStatus != 'discard'">
              <!-- Provider Name -->
              <div class="col-12">
                  <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                          <label class="form-label" for="provider">
                              <b>Alquileres * </b>
                          </label>
                          <v-autocomplete
                                  placeholder="Seleccione Alquiler"
                                  variant="solo"
                                  item-title="longname"
                                  item-value="pk"
                                  :items="dj.rent"
                                  v-model="inputRent"
                                  v-on:focusin="onChangeType"
                                  :disabled="inputInvoiceStatus == 'revised'"
                                  :required="!inputRent"
                          ></v-autocomplete>
                          <div class="invalid-feedback">Seleccione el Alquiler.</div>
                      </div>
                      <div class="col-6">
                          <div>
                            
                          </div>
                          <div v-show="inputRent">
                            <span>
                              <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                              Calle: [[ getRentByID(inputRent)?.type_road ]] [[ getRentByID(inputRent)?.name_road ]] [[ getRentByID(inputRent)?.portal ]]<br/>
                              {% comment %} Provincia: [[ getRentByID(inputRent)?.province ]] <br/>
                              Municipio: [[ getRentByID(inputRent)?.name_municipality ]] <br/> {% endcomment %}
                              Codigo Postal: [[ getRentByID(inputRent)?.postal_code ]] <br/>
                            </span>
                          </div>
                      </div>
                      <hr class="mt-0"/>
                  </div>
              </div>

              <!-- Tax Country (VAT) -->
              <div class="col-6 form-group form-check">
                  <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                  </label>
                  <select
                          class="form-select form-control"
                          id="inputTaxCountry"
                          name="inputTaxCountry"
                          v-model="inputTaxCountry"
                          required
                  >
                      <option selected disabled>Seleccione el país</option>
                      <option :value="vat.vat_country" v-for="vat in dj.seller_vat" :key="vat.pk">
                          [[ getCountryNameByCode(vat.vat_country) ]]
                      </option>
                  </select>
                  <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece
                      esta factura.
                  </div>
              </div>

              <!-- Out of Time -->
              <div class="col-6 form-group form-check">
                <label class="form-label">&nbsp;</label>
                <div class="btn-group w-100" role="group">
                  <input
                    type="checkbox"
                    class="btn-check"
                    id="inputOutOfTime"
                    name="inputOutOfTime"
                    v-model="inputOutOfTime"
                    autocomplete="off"
                    disabled:="inputInvoiceStatus == 'revised'" 
                  />
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                    Factura <b>no emitida</b> en este periodo fiscal
                  </label>
                  <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                    Factura <b>emitida</b> en este periodo fiscal
                  </label>
                </div>
              </div>

              <!-- Invoice Number/Reference -->
              <div class="col-6 form-group form-check">
                  <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de la Factura * </b>
                  </label>
                  <input
                          type="text"
                          class="form-control"
                          id="inputInvoiceNumber"
                          name="inputInvoiceNumber"
                          v-model="inputInvoiceNumber"
                          required
                  />
                  <div class="invalid-feedback">Introduzca el número/referencia de la factura.
                  </div>
              </div>

              <div class="col-6 form-group form-check">
                  <div class="row">
                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                          <label class="form-label" for="inputInvoiceDate">
                              <b>Fecha de la Factura * </b>
                          </label>
                          <input
                                  type="date"
                                  class="form-control"
                                  id="inputInvoiceDate"
                                  name="inputInvoiceDate"
                                  v-model="inputInvoiceDate"
                                  v-on:change="inputInvoiceAccountingDate = inputInvoiceDate"
                                  @blur="show_accounting_date('{{ end_date }}')"
                                  required
                          />
                          <div class="invalid-feedback">Introduzca la fecha de la factura.</div>
                      </div>
                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                          <label class="form-label" for="inputInvoiceDate">
                              <b>Fecha de Contabilización * </b>
                          </label>
                          <input
                                  type="date"
                                  class="form-control"
                                  id="inputInvoiceAccountingDate"
                                  name="inputInvoiceAccountingDate"
                                  v-model="inputInvoiceAccountingDate"
                                  min="{{ end_date }}"
                                  @blur="final_date_period('{{ end_date }}')"
                                  required
                          />
                          <div class="invalid-feedback">Introduzca la fecha de contaibilización.
                          </div>
                      </div>
                  </div>
              </div>

              <!-- Is Rectifying -->
              <div class="col-6 form-group form-check">
                  <label class="form-label" for="inputTaxResponsibility">
                      <b>¿Es una factura rectificativa? * </b>
                  </label>
                  <select
                          class="form-select form-control"
                          id="inputIsRectifying"
                          name="inputIsRectifying"
                          v-model="inputIsRectifying"
                          v-on:change="onChangeIsRectifying"
                          required
                  >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                  </select>
              </div>

              <!-- Currency -->
              <div class="col-6 form-group form-check">
                  <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                  </label>
                  <select
                          class="form-select form-control"
                          id="inputCurrency"
                          name="inputCurrency"
                          v-model="inputCurrency"
                          required
                  >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[
                          cur.description ]] ([[ cur.pk ]])
                      </option>
                  </select>
                  <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
              </div>

              <!-- Is Equivalent Tax (eqtax) -->
              <div class="col-6 form-group form-check eqtax" v-if="{{is_eqtax}}">
                  <label class="form-label" for="is_eqtax">
                      ¿Tiene Recargo de Equivalencia?
                  </label>
                  <select
                          class="form-select form-control"
                          id="inputIsEquivalentTax"
                          name="inputIsEquivalentTax"
                          v-model="inputIsEquivalentTax"
                          required
                  >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                  </select>
              </div>
            </div>
            <!-- Form 8: Rent -->
          </fieldset>
          <!-- Fieldset 1 -->
          
          <!-- Concepts  -->
          <div class="accordion" id="accordionExample">
            <div
              class="accordion-item concept row align-items-center mt-2 mb-2"
              v-if="inputInvoiceType && inputInvoiceStatus != 'discard'"
              v-for="(concept,i) in inputConcepts"
              :key="i"
              :id="'concept' + i"
            >
              <h2 class="accordion-header" :id="'heading'+i">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" :data-bs-target="'#collapse'+i" aria-expanded="false" :aria-controls="'collapse'+i">
                  <span v-if="!concept.is_supplied">Concepto [[i+1]]</span>
                  <span v-else>Concepto [[i+1]] (Suplido)</span>
                </button>
              </h2>
              <div :id="'collapse'+i" class="accordion-collapse collapse" :aria-labelledby="'heading'+i" data-bs-parent="#accordionExample">
                    <!-- Fieldset 2 -->
                    <fieldset id="fieldset2">
                      <!-- Fieldset 2 (Concept Standard)-->
                      <fieldset v-if="!concept.is_supplied">
                        <div class="row">
                          <div class="col">
                            <div class="row">

                              <!-- Concepto -->
                              <div class="col-12 mt-0">
                                <div class="headers row">
                                  <div class="col-12 ml-0 mr-0">
                                    <label class="control-label" for="items__concept">
                                      <b>Concepto *</b>
                                    </label>
                                  </div>
                                </div>

                                <div class="fields row">
                                  <div class="col-12 ml-0 mr-0">
                                    <input
                                      type="text"
                                      class="form-control"
                                      :id="'inputConceptName' + i"
                                      v-model.trim="concept.concept"
                                      @focusin="conceptDefaultValues(concept)"
                                      @focusout="conceptDefaultValues(concept)"
                                      required
                                    />
                                    <div class="invalid-feedback">Introduzca el nombre del concepto.</div>
                                  </div>
                                </div>
                              </div>

                              <!-- Porcentaje y Cantidad -->
                              <div class="col-2 mt-2">
                                <div class="row">
                                  <div class="col-12">
                                    <label class="control-label">
                                      Porcentaje
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptPercentage' + i"
                                        min="0"
                                        max="100"
                                        pattern="[0-9.]+"
                                        v-model.trim="concept.percentage"
                                        @focusin="conceptDefaultValues(concept)"
                                        @focusout="conceptDefaultValues(concept)"
                                        required
                                      />
                                      <span class="input-group-text">%</span>
                                    </div>
                                    <div class="invalid-feedback">Introduzca el porcentaje del concepto.</div>
                                  </div>
                                  <div class="col-12 mt-2">
                                    <label class="control-label">
                                      <b>Cantidad *</b>
                                    </label>
                                    <input
                                      type="text"
                                      class="form-control"
                                      :id="'inputConceptQuantity' + i"
                                      v-model.trim="concept.quantity"
                                      min="1"
                                      pattern="[1-9][0-9]*"
                                      @focusin="conceptDefaultValues(concept)"
                                      @focusout="conceptDefaultValues(concept)"
                                      required
                                    />
                                  </div>
                                </div>
                              </div>

                              <div class="col-10 mt-2">
                                <!-- Fila 1 | IVA, Recargo y IRPF -->
                                <div class="fields-currency">
                                  <div 
                                    class="row"
                                    v-if="!['ticket','self-employment-fee','import-dua','import-expenses','import-invoice'].includes(inputInvoiceType)">
                                    <div class='col-4'>
                                      <label class="control-label">IVA</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptVat' + i"
                                          v-model.trim="concept.vat"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                    <div class='col-4'>
                                      <label class="control-label">IRPF</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptIrpf' + i"
                                          v-model.trim="concept.irpf"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                    <div class="col-4" v-if="inputIsEquivalentTax">
                                      <label class="control-label">Req.EQ</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptReqEq' + i"
                                          v-model.trim="concept.eqtax"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <!-- Fila 2 | Base Original, Base y Total -->
                                <div class="row mt-2">
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Original*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountOriginal' + i"
                                        v-model.trim="concept.amount_original"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Calculada*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountCurrency' + i"
                                        v-model.trim="concept.amount_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                        readonly
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Total *</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptTotalCurrency' + i"
                                        v-model.trim="concept.total_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                </div>
                                <!-- Fila 3 | conversiones a Euros (solo si la moneda no es EUR) -->
                                <div class="col-12" v-if="(concept.currency && concept.currency != 'EUR') || (inputCurrency && inputCurrency != 'EUR')">
                                  <div class="row">
                                    <div class="col-4">
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptAmountOriginalEuros' + i"
                                          v-model="concept.amount_original_euros"
                                          readonly
                                          required
                                        />
                                        <span class="input-group-text">EUR</span>
                                      </div>
                                    </div>
                                    <div class="col-4">
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptAmountEuros' + i"
                                          v-model="concept.amount_euros"
                                          readonly
                                          required
                                        />
                                        <span class="input-group-text">EUR</span>
                                      </div>
                                    </div>
                                    <div class="col-4">
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptTotalEuros' + i"
                                          v-model="concept.total_euros"
                                          readonly
                                          required
                                        />
                                        <span class="input-group-text">EUR</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                            </div>
                          </div>
                          <!-- Botón de eliminar -->
                          <div class="col-1 d-flex justify-content-center align-items-center text-center">
                            <btn type="button" class="btn btn-link" @click="removeConcept(i)">
                              <h3><i class="fa-solid fa-xl fa-xmark text-danger"></i></h3>
                            </btn>
                          </div>
                        </div>
                      </fieldset>
                      <!-- Fieldset 2 (Concept Standard)-->

                      <!-- Fieldset 2 (Concept supplied)-->
                      <fieldset v-if="concept.is_supplied">
                        <div class="row">
                          <div class="col">
                            <div class="row">
                              <!-- Concepto -->
                              <div class="col-12 mt-0">
                                <div class="headers row">
                                  <div class="col-10 ml-0 mr-0">
                                    <label class="control-label" for="items__concept">
                                      <b>Concepto *</b>
                                    </label>
                                  </div>
                                  <div class="col-2 ml-0 mr-0">
                                    <label class="control-label" for="items__concept">
                                      Porcentaje
                                    </label>
                                  </div>
                                </div>
                                <div class="fields row">
                                  <div class="col-10 ml-0 mr-0">
                                    <input
                                      type="text"
                                      class="form-control"
                                      :id="'inputConceptName' + i"
                                      v-model.trim="concept.concept"
                                      @focusin="conceptDefaultValues(concept)"
                                      @focusout="conceptDefaultValues(concept)"
                                      required
                                    />
                                    <div class="invalid-feedback">
                                      Introduzca el nombre del concepto.
                                    </div>
                                  </div>
                                  <div class="col-2">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptPercentage' + i"
                                        min="0"
                                        max="100"
                                        pattern="[0-9.]+"
                                        v-model.trim="concept.percentage"
                                        @focusin="conceptDefaultValues(concept)"
                                        @focusout="conceptDefaultValues(concept)"
                                        required
                                      />
                                      <span class="input-group-text">%</span>
                                    </div>
                                    <div class="invalid-feedback">Introduzca el porcentaje del concepto.</div>
                                  </div>
                                </div>
                              </div>
                              <!-- Cantidad -->
                              <div class="col-2 mt-2">
                                <label class="control-label">
                                  <b>Cantidad *</b>
                                </label>
                                <input
                                  type="text"
                                  class="form-control"
                                  :id="'inputConceptQuantity' + i"
                                  v-model.trim="concept.quantity"
                                  min="1"
                                  pattern="[1-9][0-9]*"
                                  @focusin="conceptDefaultValues(concept)"
                                  @focusout="conceptDefaultValues(concept)"
                                  required
                                />
                              </div>

                              <!-- Amount - Total -->
                              <div class="col-10 mt-2 mx-0">
                                <!-- Fila 2 | Base Original, Base y Total -->
                                <div class="row fields-currency">
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Original*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountOriginal' + i"
                                        v-model.trim="concept.amount_original"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Calculada*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountCurrency' + i"
                                        v-model.trim="concept.amount_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                        readonly
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Total *</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptTotalCurrency' + i"
                                        v-model.trim="concept.total_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        @focusin="conceptDefaultValues(concept)"
                                        @focusout="conceptDefaultValues(concept)"
                                        required
                                      />
                                      <span class="input-group-text">
                                        [[ concept?.currency || inputCurrency ]]
                                      </span>
                                    </div>
                                  </div>
                                </div>

                                <!-- Fila 3 | conversiones a Euros (solo si la moneda no es EUR) -->
                                <div class="row fields-Euros"
                                  v-show="(concept.currency && concept.currency != 'EUR') || (inputCurrency && inputCurrency != 'EUR')"
                                >
                                  <div class="col-4">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountOriginalEuros' + i"
                                        v-model="concept.amount_original_euros"
                                        readonly
                                        required
                                      />
                                      <span class="input-group-text">EUR</span>
                                    </div>
                                  </div>
                                  <div class="col-4 ml-0 mr-0">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountEuros' + i"
                                        v-model="concept.amount_euros"
                                        @input="onChangeAmountEuros(concept)"
                                        readonly
                                        required
                                      />
                                      <span class="input-group-text">
                                        EUR
                                      </span>
                                    </div>
                                  </div>
                                  <div class="col-4 ml-0 mr-0">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptTotalEuros' + i"
                                        v-model="concept.total_euros"
                                        readonly
                                        required
                                      />
                                      <span class="input-group-text">
                                        EUR
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-1 d-flex justify-content-center align-items-center text-center">
                            <btn type="button" class="btn btn-link" @click="removeConcept(i)">
                              <h3><i class="fa-solid fa-xl fa-xmark text-danger"></i></h3>
                            </btn>
                          </div>
                        </div>
                      </fieldset>
                      <!-- Fieldset 2 (Concept supplied)-->
                    </fieldset>
                    <!-- Fieldset 2 -->
              </div>  
            </div>
          </div>
          <!-- Concepts  -->

          <!-- Fieldset 3 -->
          <fieldset id="fieldset3" >
            <!-- Button: Add Concept  -->
            {% comment %}
            <div class="col-12 d-flex justify-content-end align-items-center text-rigth">
              <a class="btn btn-outline-danger btn-plus mt-1 mb-2" @click="deleteAllConcepts()" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                <i class="fa-solid fa-x"></i>
                <span>Eliminar Todos</span>
              </a>
              <a class="btn btn-outline-primary btn-plus mt-1 mb-2" @click="recalcIVA0Concepts()" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                <span>Todos los IVA al 0%</span>
              </a>
              <a class="btn btn-outline-primary btn-plus mt-1 mb-2" @click="recalcIVA21Concepts()" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                <span>Todos los IVA al 21%</span>
              </a>
              <a class="btn btn-outline-primary btn-plus mt-1 mb-2" @click="recalcTotalConcepts()" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                <span>Recalcular Totales (Desde la Base)</span>
              </a>
              <a class="btn btn-outline-primary btn-plus mt-1 mb-2" @click="recalcCurrencyConcepts()" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                <span>Recalcular Cambio Divisas</span>
              </a>
              <a class="btn btn-outline-primary btn-plus mt-1 mb-2" @click="addConcept()" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                <i class="fa-solid fa-plus"></i>
                <span>Añadir Concepto</span>
              </a>
            </div> 
            {% endcomment %}

            <!-- Resume: Total  -->
            <div class="d-flex justify-content-end align-items-end text-rigth">
              <div class="col-6 total border" id="totalResumeCurrency" v-show="inputInvoiceType && inputCurrency && inputCurrency != 'EUR' && inputInvoiceStatus != 'discard'">
                <div class="row">
                  <div class="col-6 totalKey">
                    <span><b>Base Imponible:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().amountCurrencyWithoutSuplied) ]] [[ inputCurrency ]]</b></span>
                  </div>
                </div>
                <div class="row" v-show="inputHaveSupplies">
                  <div class="col-6 totalKey">
                    <span><b>Base Imp. Exenta:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().amountSupliedCurrency) ]] [[ inputCurrency ]]</b></span>
                  </div>
                </div>
                <div class="row mt-1"></div>
                <div class="row" v-for="(value, key, index) in all_vats_currency" >
                  <div class="col-6 totalKey" v-show="key!='0.00'">
                    <span>IVA [[key]]%:</span>
                  </div>
                  <div class="col-6 totalValue" v-show="key!='0.00'">
                    <span>[[ numberPrecision2(value) ]] [[ inputCurrency ]]</span>
                  </div>
                </div>
                <div class="row mt-1">
                  <div class="col-6 totalKey">
                    <span><b>IVA (Total):</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().vatCurrency) ]] [[ inputCurrency ]]</b></span>
                  </div>
                </div>
                <div class="row" v-show="inputIsEquivalentTax == true">
                  <div class="col-6 totalKey">
                    <span><b>Rec. Equivalencia:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().eqtaxCurrency) ]] [[ inputCurrency ]]</b></span>
                  </div>
                </div>
                <div class="row" v-show="parseInt(Number(getTotal().irpfCurrency)* 100, 10) / 100 != 0">
                  <div class="col-6 totalKey">
                    <span><b>IRPF:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().irpfCurrency) * -1 ]] [[ inputCurrency ]]</b></span>
                  </div>
                </div>
                <div class="row mt-1 justify-content-center">
                  <hr class="border border-primary border-3 align-center opacity-75" style="width:90%;">
                </div>
                <div class="row">
                  <div class="col-6 totalKey">
                    <span><b>Total:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().totalCurrency) ]] [[ inputCurrency ]]</b></span>
                  </div>
                </div>
              </div>
              <div class="col-6 total border" id="totalResumeEuros" v-show="inputInvoiceType && inputInvoiceStatus != 'discard'" >
                <div class="row">
                  <div class="col-6 totalKey">
                    <span><b>Base Imponible:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().amountEurosWithoutSuplied) ]] €</b></span>
                  </div>
                </div>
                <div class="row" v-show="inputHaveSupplies">
                  <div class="col-6 totalKey">
                    <span><b>Base Imp. Exenta:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().amountSupliedEuros) ]] €</b></span>
                  </div>
                </div>
                <div class="row mt-1"></div>
                <div class="row" v-for="(value, key, index) in all_vats_euros" >
                  <div class="col-6 totalKey" v-show="key!='0.00'">
                    <span>IVA [[key]] %:</span>
                  </div>
                  <div class="col-6 totalValue" v-show="key!='0.00'">
                    <span>[[ numberPrecision2(value) ]] €</span>
                  </div>
                </div>
                <div class="row mt-1">
                  <div class="col-6 totalKey">
                    <span><b>IVA (Total):</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().vatEuros) ]] €</b></span>
                  </div>
                </div>
                <div class="row" v-show="inputIsEquivalentTax == true">
                  <div class="col-6 totalKey">
                    <span><b>Rec. Equivalencia:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().eqtaxEuros) ]] €</b></span>
                  </div>
                </div>
                <div class="row" v-show="parseInt(Number(getTotal().irpfEuros)* 100, 10) / 100 != 0">
                  <div class="col-6 totalKey">
                    <span><b>IRPF:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().irpfEuros) * -1 ]] €</b></span>
                  </div>
                </div>
                <div class="row mt-1 justify-content-center">
                  <hr class="border border-primary border-3 align-center opacity-75" style="width:90%;">
                </div>
                <div class="row">
                  <div class="col-6 totalKey">
                    <span><b>Total:</b></span>
                  </div>
                  <div class="col-6 totalValue">
                    <span><b>[[ numberPrecision2(getTotal().totalEuros) ]] €</b></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form 1: Sales -->
            <div class="row mt-3" id="sales_bottom" v-if="inputInvoiceType == 'sales' && inputInvoiceStatus != 'discard'">
              <!-- Account -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="account_sales">
                  <b>Cuenta contable de ingreso * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputAccountSales"
                  name="inputAccountSales"
                  v-model="inputAccountSales"
                  :disabled="!inputCustomer"
                  required
                >
                  <option selected disabled> Selecciona la cuenta </option>
                  <option :value="account.pk" v-for="account in dj.account_sales" :key="account.pk">
                    [[ account.description ]]  ( [[ account.pk ]] )
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la Cuenta Contable de Ingreso.</div>
              </div>

              <!-- MarketPlace -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="marketplace">
                  <b>Marketplace * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputMarketplace"
                  name="inputMarketplace"
                  v-model="inputMarketplace"
                  placeholder="Selecciona el marketplace"
                  {% comment %} required {% endcomment %}
                  disabled
                >
                  <option selected disabled> Selecciona el marketplace </option>
                  <option :value="market.pk" v-for="market in dj.marketplaces" :key="market.pk">
                    [[ market.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
              </div>

              <!-- Economic Activity -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="iae">
                  <b>Actividad económica * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputEconomicActivity"
                  name="inputEconomicActivity"
                  v-model="inputEconomicActivity"
                  required
                >
                  <option selected disabled> Selecciona la actividad </option>
                  <option :value="item.pk" v-for="item in dj.economic_activity" :key="item.pk">
                    [[ item.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la actividad económica.</div>
              </div>

              <!-- Tags  -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="tags">
                  Etiquetas:
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceTags"
                  name="inputInvoiceTags"
                  v-model="inputInvoiceTags"
                />
              </div>

              <!-- Hidden Inputs -->
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  class="form-control"
                  id="transaction_type"
                  name="transaction_type"
                  v-model="inputTransactionType"
                  placeholder="Tipo de Transacción"
                  readonly
                />
                <input
                  type="hidden"
                  class="form-control"
                  id="operation_type"
                  name="operation_type"
                  v-model="inputOperationType"
                  placeholder="Tipo de Operacion"
                  readonly
                />
              </div>
            </div>
            <!-- Form 1: Sales -->

            <!-- Form 2: Expenses -->
            <div class="row mt-3" id="expenses_bottom" v-if="inputInvoiceType == 'expenses' && inputInvoiceStatus != 'discard'">
              <!-- Account -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="account_sales">
                  <b>Cuenta contable de gastos *  </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputAccountExpenses"
                  name="inputAccountExpenses"
                  v-model="inputAccountExpenses"
                  :disabled="!inputProvider"
                  required
                >
                  <option selected disabled> Selecciona la cuenta </option>
                  <option :value="account.pk" v-for="account in dj.account_expenses" :key="account.pk">
                    [[ account.description ]]  ( [[ account.pk ]] )
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
              </div>

              <!-- MarketPlace -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="marketplace">
                  <b>Marketplace * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputMarketplace"
                  name="inputMarketplace"
                  v-model="inputMarketplace"
                  {% comment %} required {% endcomment %}
                  disabled
                >
                  <option selected disabled> Selecciona el marketplace </option>
                  <option :value="market.pk" v-for="market in dj.marketplaces" :key="market.pk">
                    [[ market.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
              </div>

              <!-- Economic Activity -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="iae">
                  <b>Actividad económica * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputEconomicActivity"
                  name="inputEconomicActivity"
                  v-model="inputEconomicActivity"
                  required
                >
                  <option selected disabled> Selecciona la actividad </option>
                  <option :value="item.pk" v-for="item in dj.economic_activity" :key="item.pk">
                    [[ item.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la actividad económica.</div>
              </div>

              <!-- Tags  -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="tags">
                  Etiquetas:
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceTags"
                  name="inputInvoiceTags"
                  v-model="inputInvoiceTags"
                />
              </div>

              <!-- Hidden Inputs -->
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  class="form-control"
                  id="transaction_type"
                  name="transaction_type"
                  v-model="inputTransactionType"
                  placeholder="Tipo de Transacción"
                  readonly
                />
                <input
                  type="hidden"
                  class="form-control"
                  id="operation_type"
                  name="operation_type"
                  v-model="inputOperationType"
                  placeholder="Tipo de Operacion"
                  readonly
                />
              </div>
            </div>
            <!-- Form 2: Expenses -->

            <!-- Form 3: Tickets -->
            <div class="row mt-3" id="expenses_bottom" v-if="inputInvoiceType == 'ticket' && inputInvoiceStatus != 'discard'">
              <!-- Account -->
              <div class="col-12 form-group form-check">
                  <label class="form-label" for="account_sales">
                    <b>Cuenta contable de gastos *  </b>
                  </label>
                  <select
                    class="form-select form-control"
                    id="inputAccountExpenses"
                    name="inputAccountExpenses"
                    v-model="inputAccountExpenses"
                    :disabled="!inputProvider"
                    required
                  >
                  <option selected disabled> Selecciona la cuenta </option>
                  <option :value="account.pk" v-for="account in dj.account_expenses" :key="account.pk">
                    [[ account.description ]]  ( [[ account.pk ]] )
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
              </div>

              <!-- MarketPlace -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="marketplace">
                  <b>Marketplace * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputMarketplace"
                  name="inputMarketplace"
                  v-model="inputMarketplace"
                  {% comment %} required {% endcomment %}
                  disabled
                >
                  <option selected disabled> Selecciona el marketplace </option>
                  <option :value="market.pk" v-for="market in dj.marketplaces" :key="market.pk">
                    [[ market.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
              </div>

              <!-- Economic Activity -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="iae">
                  <b>Actividad económica * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputEconomicActivity"
                  name="inputEconomicActivity"
                  v-model="inputEconomicActivity"
                  required
                >
                  <option selected disabled> Selecciona la actividad </option>
                  <option :value="item.pk" v-for="item in dj.economic_activity" :key="item.pk">
                    [[ item.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la actividad económica.</div>
              </div>

              <!-- Tags  -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="tags">
                  Etiquetas:
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceTags"
                  name="inputInvoiceTags"
                  v-model="inputInvoiceTags"
                />
              </div>

              <!-- Hidden Inputs -->
              <div class="d-none d-print-none">
                <input
                  type="hidden"
                  class="form-control"
                  id="transaction_type"
                  name="transaction_type"
                  v-model="inputTransactionType"
                  placeholder="Tipo de Transacción"
                  readonly
                />
                <input
                  type="hidden"
                  class="form-control"
                  id="operation_type"
                  name="operation_type"
                  v-model="inputOperationType"
                  placeholder="Tipo de Operación"
                  readonly
                />
              </div>
            </div>
            <!-- Form 3: Tickets -->

            <!-- Form 4: Import Invoice -->
            <div class="row mt-3" id="import-invoice_bottom" v-if="inputInvoiceType == 'import-invoice' && inputInvoiceStatus != 'discard'">
              <!-- Account -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="account_sales">
                  <b>Cuenta contable de gastos *  </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputAccountExpenses"
                  name="inputAccountExpenses"
                  v-model="inputAccountExpenses"
                  :disabled="!inputProvider"
                  required
                >
                  <option selected disabled> Selecciona la cuenta </option>
                  <option :value="account.pk" v-for="account in dj.account_expenses" :key="account.pk">
                    [[ account.description ]]  ( [[ account.pk ]] )
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
              </div>

              <!-- MarketPlace -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="marketplace">
                  <b>Marketplace * </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputMarketplace"
                  name="inputMarketplace"
                  v-model="inputMarketplace"
                  {% comment %} required {% endcomment %}
                  disabled
                >
                  <option selected disabled> Selecciona el marketplace </option>
                  <option :value="market.pk" v-for="market in dj.marketplaces" :key="market.pk">
                    [[ market.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
              </div>

              <!-- Economic Activity -->
              <div class="col-6 form-group form-check">
                <label class="form-label" for="iae">
                  <b>Actividad económica *</b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputEconomicActivity"
                  name="inputEconomicActivity"
                  v-model="inputEconomicActivity"
                  required
                >
                  <option selected disabled> Selecciona la actividad </option>
                  <option :value="item.pk" v-for="item in dj.economic_activity" :key="item.pk">
                    [[ item.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la actividad económica.</div>
              </div>

              <!-- Tags  -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="tags">
                  Etiquetas:
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="inputInvoiceTags"
                  name="inputInvoiceTags"
                  v-model="inputInvoiceTags"
                />
              </div>
            </div>
            <!-- Form 4: Import Invoice -->

            <!-- Form 6: Import Expenses -->
            <div class="row mt-3" id="import-invoice_bottom" v-if="inputInvoiceType == 'import-expenses' && inputInvoiceStatus != 'discard'">
              <!-- Account -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="account_sales">
                  <b>Cuenta contable de gastos *  </b>
                </label>
                <select
                  class="form-select form-control"
                  id="inputAccountExpenses"
                  name="inputAccountExpenses"
                  v-model="inputAccountExpenses"
                  :disabled="!inputProvider"
                  required
                >
                  <option selected disabled> Selecciona la cuenta </option>
                  <option :value="account.pk" v-for="account in dj.account_expenses" :key="account.pk">
                    [[ account.description ]]  ( [[ account.pk ]] )
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
              </div>
            </div>
            <!-- Form 6: Import Expenses -->

            <!-- Form 8: Rent -->
            <div class="row mt-3" id="rent_bottom"
                                     v-if="inputInvoiceType == 'rent' && inputInvoiceStatus != 'discard'">
                                    <!-- Account -->
                                    <div class="col-12 form-group form-check">
                                        <label class="form-label" for="account_sales">
                                            <b>Cuenta contable de gastos * </b>
                                        </label>
                                        <select
                                                class="form-select form-control"
                                                id="inputAccountExpenses"
                                                name="inputAccountExpenses"
                                                v-model="inputAccountExpenses"
                                                :disabled="!inputProvider"
                                                required
                                        >
                                            <option selected disabled> Selecciona la cuenta</option>
                                            <option :value="account.pk" v-for="account in dj.account_expenses"
                                                    :key="account.pk">
                                                [[ account.description ]] ( [[ account.pk ]] )
                                            </option>
                                        </select>
                                        <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                                    </div>

                                    <!-- MarketPlace -->
                                    <div class="col-6 form-group form-check">
                                        <label class="form-label" for="marketplace">
                                            <b>Marketplace * </b>
                                        </label>
                                        <select
                                                class="form-select form-control"
                                                id="inputMarketplace"
                                                name="inputMarketplace"
                                                v-model="inputMarketplace"
                                                {% comment %} required {% endcomment %}
                                                disabled
                                        >
                                            <option selected disabled> Selecciona el marketplace</option>
                                            <option :value="market.pk" v-for="market in dj.marketplaces"
                                                    :key="market.pk">
                                                [[ market.description ]]
                                            </option>
                                        </select>
                                        <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                                    </div>

                                    <!-- Economic Activity -->
                                    <div class="col-6 form-group form-check">
                                        <label class="form-label" for="iae">
                                            <b>Actividad económica * </b>
                                        </label>
                                        <select
                                                class="form-select form-control"
                                                id="inputEconomicActivity2"
                                                name="inputEconomicActivity2"
                                                v-model="inputEconomicActivity"
                                                required
                                        >
                                            <option selected disabled> Selecciona la actividad</option>
                                            <option :value="item.pk" v-for="item in dj.economic_activity"
                                                    :key="item.pk">
                                                [[ item.description ]]
                                            </option>
                                        </select>
                                        <div class="invalid-feedback">Seleccione la actividad económica.</div>
                                    </div>
                                </div>
            <!-- Form 8: Rent -->

            <!-- Form: Common -->
            <div class="row mt-3" id="common_bottom">
              <!-- Discard Reasons -->
              <div class="col-12 form-group form-check" v-if="inputInvoiceStatus == 'discard'">
                <label class="form-label" for="discard_reason">
                  Motivo de descarte:
                </label>
                <select
                  class="form-select form-control"
                  id="inputDiscardReason"
                  name="inputDiscardReason"
                  v-model="inputDiscardReason"
                >
                  <option selected disabled>Seleccione el motivo de descarte</option>
                  <option :value="res.pk" v-for="res in dj.discard_reason" :key="res.pk">
                    [[ res.description ]]
                  </option>
                </select>
                <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
              </div>
              <!-- Discard Notes -->
              <div class="col form-group form-check p-3" v-if="inputInvoiceStatus == 'discard' && inputDiscardReason == 'other'" >
                <label class="form-label" for="discard_reason_notes">
                  Descripción del motivo de descarte:
                </label>
                <textarea
                  class="form-control"
                  id="inputDiscardReasonNotes"
                  name="inputDiscardReasonNotes"
                  v-model="inputDiscardReasonNotes"
                  rows="3"
                ></textarea>
              </div>
              <!-- Notes  -->
              <div class="col-12 form-group form-check" v-if="inputInvoiceStatus != 'discard'">
                <label class="form-label" for="notes">
                  Notas / Observaciones:
                </label>
                <textarea
                  class="form-control"
                  id="inputInvoiceNotes"
                  name="inputInvoiceNotes"
                  v-model="inputInvoiceNotes"
                  rows="3"
                ></textarea>
              </div>
            </div>
            <!-- Form: Common -->
          </fieldset>
          <!-- Fieldset 3 -->      
          
          <!-- FORM -->
          
            {% csrf_token %}

            <!-- Hidden Inputs -->
            <div class="d-none d-print-none" style="display: none;">
              <!-- Invoice Name -->
              <input type="hidden" id="name" name="name" v-model="inputInvoiceName" readonly />

              <!-- Invoice Status -->
              <input type="hidden" id="status" name="status" v-model="inputInvoiceStatus" readonly />
              
              <!-- Invoice Category -->
              <input type="hidden" id="invoice_category" name="invoice_category" v-model="inputInvoiceCategory" readonly />
              
              <!-- Invoice Type -->
              <input type="hidden" id="invoice_type" name="invoice_type" v-model="inputInvoiceType" readonly />

              <!-- Invoice Relation -->
              <input type="hidden" id="related_invoice" name="related_invoice" v-model="inputRelatedInvoice" readonly />
              
              <!-- Customer -->
              <input type="hidden" id="customer" name="customer" v-model="inputCustomer" v-if="inputInvoiceCategory== 'sales'"/>
              
              <!-- Provider -->
              <input type="hidden" id="provider" name="provider" v-model="inputProvider" v-if="inputInvoiceCategory== 'expenses'"/>
              
              <!-- TaxCountry -->
              <input type="hidden" id="tax_country" name="tax_country" v-model="inputTaxCountry" readonly />
              
              <!-- TaxResponsability -->
              <input type="hidden" id="tax_responsibility" name="tax_responsibility" v-model="inputTaxResponsibility" readonly />
              
              <!-- Invoice Reference -->
              <input type="hidden" id="reference" name="reference" v-model="inputInvoiceNumber" readonly />
              <input type="hidden" id="reference_absolute" name="reference_absolute" v-model="inputInvoiceNumberAbsolute" readonly />
              
              <!-- Invoice Expedition Date -->
              <input type="hidden" id="expedition_date" name="expedition_date" v-model="inputInvoiceDate" readonly />
              <input type="hidden" id="invoice_date" name="invoice_date" v-model="inputInvoiceDate" readonly />
              
              <!-- Is Rectifying --> 
              <div v-if="inputInvoiceType == 'sales' || inputInvoiceType == 'expenses'">
                <input type="hidden" id="is_rectifying" name="is_rectifying" v-model="inputIsRectifying" readonly />
              </div>
              <div v-else>
                <input type="hidden" id="is_rectifying" name="is_rectifying" :value="False" readonly />
              </div>
              
              <!-- Currency -->
              <input type="hidden" id="currency" name="currency" v-model="inputCurrency" readonly />
              
              <!-- IS OSS --> 
              <div v-if="inputInvoiceCategory == 'sales'">
                <input type="hidden" id="is_oss" name="is_oss" v-model="inputIsDistanceSell" readonly />
              </div>
              <div v-else>
                <input type="hidden" id="is_oss" name="is_oss" :value="False" readonly />
              </div>

              <!-- IS EQTAX --> 
              <input type="hidden" id="is_eqtax" name="is_eqtax" v-model="inputIsEquivalentTax" readonly />
              
              <!-- Departure Country -->
              <input type="hidden" id="departure_country" name="departure_country" v-model="inputDepartureCountry" readonly />

              <!-- Out of Time -->
              <input type="hidden" id="out_of_time" name="out_of_time" v-model="inputOutOfTime" readonly/>

              <!-- Invoice Accounting Date -->
              <div v-if="inputOutOfTime">
                <input type="hidden" id="accounting_date" name="accounting_date" v-model="inputInvoiceAccountingDate" readonly/>
              </div>
              <div v-else>
                <input type="hidden" id="accounting_date" name="accounting_date" v-model="inputInvoiceDate" readonly/>
              </div>

              <!-- Account Sales -->
              <div v-if="inputInvoiceCategory == 'sales'">
                <input type="hidden" id="account_sales" name="account_sales" v-model="inputAccountSales" readonly />
              </div>

              <!-- Account Expenses -->
              <div v-if="inputInvoiceCategory == 'expenses'">
                <input type="hidden" id="account_expenses" name="account_expenses" v-model="inputAccountExpenses" readonly />
              </div>
              
              <!-- MarketPlace -->
              <input type="hidden" id="marketplace" name="marketplace" v-model="inputMarketplace" readonly />
              
              <!-- IAE / Economic Activity -->
              <input type="hidden" id="iae" name="iae" v-model="inputEconomicActivity" readonly />
              
              <!-- Tags -->
              <input type="hidden" id="tags" name="tags" v-model="inputInvoiceTags" readonly />

              <!-- Discard Reasons -->
              <input type="hidden" id="discard_reason" name="discard_reason" v-model="inputDiscardReason" readonly>

              <!-- Notes Discard Reasons -->
              <input type="hidden" id="discard_reason_notes" name="discard_reason_notes" v-model="inputDiscardReasonNotes" readonly>
              
              <!-- Invoices Carrusel -->
              <input type="hidden" id="carrusel" name="carrusel"  >

              <!-- Notes -->            
              <input type="hidden" id="notes" name="notes" v-model="inputInvoiceNotes" readonly />
              
              <!-- Transaction Type --> 
              <input type="hidden" id="transaction_type" name="transaction_type" v-model="inputTransactionType" readonly />
              
              <!-- Operation Type --> 
              <input type="hidden" id="operation_type" name="operation_type" v-model="inputOperationType" readonly /> 

              <!-- Concepts -->
              <input type="hidden" id="concepts" name="concepts" v-model="inputConceptsJSON" readonly />

              <!-- JSON VATS -->
              <input type="hidden" id="json_vat" name="json_vat" v-model="inputJsonVats" readonly />

              <!-- Totals -->
              <input type="hidden" id="total_eqtax_currency" name="total_eqtax_currency" v-model="getTotal().eqtaxCurrency" readonly />
              <input type="hidden" id="total_eqtax_euros" name="total_eqtax_euros" v-model="getTotal().eqtaxEuros" readonly />
              <input type="hidden" id="total_vat_currency" name="total_vat_currency" v-model="getTotal().vatCurrency" readonly />
              <input type="hidden" id="total_vat_euros" name="total_vat_euros" v-model="getTotal().vatEuros" readonly />
              <input type="hidden" id="total_irpf_currency" name="total_irpf_currency" v-model="getTotal().irpfCurrency" readonly />
              <input type="hidden" id="total_irpf_euros" name="total_irpf_euros" v-model="getTotal().irpfEuros" readonly />
              <input type="hidden" id="total_amount_currency" name="total_amount_currency" v-model="getTotal().amountCurrency" readonly />
              <input type="hidden" id="total_amount_euros" name="total_amount_euros" v-model="getTotal().amountEuros" readonly />
              <input type="hidden" id="total_currency" name="total_currency" v-model="getTotal().totalCurrency" readonly />
              <input type="hidden" id="total_euros" name="total_euros" v-model="getTotal().totalEuros" readonly />                              
            </div>
            <!-- Hidden Inputs -->

            <!-- Save Buttons -->
            {% comment %}
            <div v-if="inputInvoiceStatusOriginal == 'revised' || inputInvoiceStatusOriginal == 'discard'">
              <div class="row" v-show="inputInvoiceStatus == 'revised' || inputInvoiceStatus == 'discard'">
                <div class="col-4 form-group form-check">
                  <button 
                    type="submit"
                    class="btn btn-warning w-100 text-white"
                    v-show="inputInvoiceType"
                    style="background-color: #FFC107;"
                    @click="onClickSubmit('revision-pending')"
                  >
                    <b>Reabrir: Pendiente Revision</b>
                  </button>
                  <input type="hidden" id="status" name="status" v-model="inputInvoiceStatus" readonly>
                </div>
              </div>
            </div>
            <div v-if="inputInvoiceStatusOriginal != 'revised' && inputInvoiceStatusOriginal != 'discard'">
              <div class="row" v-show="inputInvoiceStatus != 'revised'">
                <div class="col-4 form-group form-check">
                  <!-- Button trigger modal -->
                  <button type="button" class="btn btn-danger w-100 text-white" data-bs-toggle="modal" data-bs-target="#modal">
                    <b> Descartar</b>
                  </button>
                    <!-- Modal -->
                        <div class="modal fade " id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
                          <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="modalLabel">Motivo de descarte</h5>
                              </div>
                              <div class="modal-body">
                                <div class="col form-group form-check p-3">
                                  <label class="form-label" for="inputDiscardReason">
                                    <b>Seleccione un motivo de descarte:</b>
                                  </label>
                                  <select
                                    class="form-select form-control"
                                    id="inputDiscardReason"
                                    name="inputDiscardReason"
                                    v-model="inputDiscardReason" 
                                  >
                                    <option selected disabled>Seleccione el motivo de descarte</option>
                                    <option :value="res.pk" v-for="res in dj.discard_reason" :key="res.pk">
                                      [[ res.description ]]
                                    </option>
                                  </select>
                                </div>

                                <div class="col form-group form-check p-3" v-if="inputDiscardReason == 'other'" >
                                  <label class="form-label" for="discard_reason_notes">
                                          Escriba el motivo de descarte:
                                        </label>
                                        <textarea
                                          class="form-control"
                                          id="inputDiscardReasonNotes"
                                          name="inputDiscardReasonNotes"
                                          v-model="inputDiscardReasonNotes"
                                          rows="3"
                                        ></textarea>
                                </div>
                              </div>
                              
                              <div class="modal-footer d-flex justify-content-center">
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#exampleModal" aria-label="Close">Cancelar</button>
                                <button type="submit" class="btn btn-danger  text-white"                                   
                                    style="background-color: #DC3545;"
                                    @click="onClickSubmit('discard')"
                                  >
                                    <b>Descartar</b>
                                </button>
                                <button type="submit" class="btn btn-danger  text-white"                                   
                                    style="background-color: #DC3545;"
                                    @click="onClickSubmit('discard'); inputCarrusel()"
                                  >
                                    <b>Descartar/Continuar</b>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                </div>
                <div class="col-4 form-group form-check">
                  <button 
                    type="submit"
                    class="btn btn-warning w-100 text-white"
                    v-show="inputInvoiceType"
                    style="background-color: #FFC107;"
                    @click="onClickSubmit('revision-pending')"
                  >
                    <b>Pendiente Revision</b>
                  </button>
                </div>
                <div class="col-4 form-group form-check">
                  <button 
                    type="submit"
                    class="btn btn-success w-100 text-white"
                    v-show="inputInvoiceType"
                    style="background-color: #1A8754;"
                    @click="onClickSubmit('revised')"
                  >
                    <b>Revisado</b>
                  </button>
                </div>
                <div class="col-4 form-group form-check"></div>
                <div class="col-4 form-group form-check">
                  <button 
                    type="submit"
                    class="btn btn-warning w-100 text-white"
                    v-show="inputInvoiceType"
                    style="background-color: #FFC107;"
                    @click="onClickSubmit('revision-pending'); inputCarrusel()"
                  >
                    <b>Pendiente Revision/Continuar</b>
                  </button>
                </div>
                <div class="col-4 form-group form-check">
                  <button 
                    type="submit"
                    class="btn btn-success w-100 text-white"
                    v-show="inputInvoiceType"
                    style="background-color: #1A8754;"
                    @click="onClickSubmit('revised'); inputCarrusel()"
                  >
                    <b>Revisado/Continuar</b>
                  </button>
                </div>
              </div>
            </div>
            {% endcomment %}
            <!-- Save Buttons -->
          </form>
          <!-- FORM -->
        </div>
      </div>
      <!-- BODY -->
      {% elif invoice and invoice.status.pk == "discard" %}
        <div class="col" id="formBody">
          <fieldset disabled>
            <!-- Form: Common -->
            <div class="row mt-3" id="common_bottom">
              <div class="col-12 form-group form-check" v-if="inputInvoiceStatus == 'discard'">
                <label class="form-label" for="discard_reason">
                  Motivo de descarte:
                </label>
                <select
                  class="form-select form-control"
                  id="inputDiscardReason"
                  name="inputDiscardReason"
                  v-model="inputDiscardReason"
                >
                  <option selected disabled>Seleccione el motivo de descarte</option>
                  <option :value="res.pk" v-for="res in dj.discard_reason" :key="res.pk">
                    [[ res.description ]]
                  </option>
                </select>
                </div>
              <!-- Notes  -->
              <div class="col-12 form-group form-check">
                <label class="form-label" for="notes">
                  Descripción del motivo de descarte:
                </label>
                <textarea
                  class="form-control"
                  id="notes"
                  name="notes"
                  v-model="inputDiscardReasonNotes"
                  rows="3"
                ></textarea>
              </div>
            </div>
            <!-- Form: Common -->
          </fieldset>
        </div>     
      {% else %}
      <div class="col alert alert-warning W-100 H-100" id="formBody">
        <div class="row" role="alert">
          <div class="col-12">
            <h4 class="alert-heading"><b>Factura no revisada</b></h4>
            <p>Le informamos que los datos de la factura, recibo o DUA que está intentando visualizar actualmente no son visibles debido a que todavía no ha sido revisada por nuestro equipo. Una vez revisada podrá visualizar aquí los datos contabilizados de este documento. Gracias</p>
          </div>
        </div>
      </div>
      {% endif %}
      <!-- FORM BODY  -->
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}
<script type="text/javascript" src="{{STATIC_URL}}assets/js/plugins/vue/3.2.6/vue.global.prod.js"></script>
<script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>
<script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.3.min-v3.6.3.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/vuetify/vuetify.min-v3.1.5.js"></script>
<!-- JAVASCRIPT FACTURACIÓN DIGITAL -->
<script type="text/javascript" src="{{ STATIC_URL }}assets/js/invoice/invoice_xml.js"></script>
<script type="module">
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const { ref, watch, computed } = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputInvoiceCategory = ref(null);
    const inputInvoiceType = ref(null);
    const inputTaxCountry = ref('ES');
    const inputTaxResponsibility = ref('seller');
    const inputOutOfTime = ref(false);
    const inputIsRectifying = ref(false);
    const inputIsReverseCharge = ref(false);
    const inputInvoiceNumber = ref();
    const inputInvoiceNumberAbsolute = ref();
    const inputInvoiceDate = ref();
    const inputInvoiceAccountingDate = ref();
    const inputCurrency = ref('EUR');
    const inputIsDistanceSell = ref(false);
    const inputIsEquivalentTax = ref(false);
    const inputDepartureCountry = ref(null);
    const inputAccountSales = ref(null);
    const inputAccountExpenses = ref(null);
    const inputCustomer = ref();
    const inputProvider = ref();
    const inputRent = ref();
    const inputMarketplace = ref(null);
    const inputEconomicActivity = ref(null);
    const inputInvoiceTags = ref(null);
    const inputInvoiceNotes = ref(null);
    const inputTransactionType = computed(() => calcTransactionType());
    const inputOperationType = computed(() => calcOperationType());
    const inputInvoiceName = ref(null);
    const inputTotalCurrency = ref(0);
    const inputTotalEuros = ref(0);
    const inputInvoiceStatus = ref(null);
    const inputInvoiceStatusOriginal = ref(null);
    const inputIsGeneratedAmz = ref(false);
    const inputRelatedInvoice = ref();
    const inputDiscardReason = ref(null);
    const inputDiscardReasonNotes = ref(null);
    const inputHaveSupplies = ref(false);
    const inputConcepts = ref([]);
    const inputConceptsOld = ref([]);
    const inputConceptsJSON = computed(() => JSON.stringify(inputConcepts.value));
    const inputJsonVats = ref("");
    const all_vats_currency = ref({});
    const all_vats_euros = ref({});

    const taxConversion = ref(1);
    const dj = ref({});
    const showCategory = ref(false);
    const editName = ref(false);


    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const reloadPage = () =>{
      console.log("reload");
      document.location.reload(true);
    }

    const getDjangoData = () => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1 ) {
          let dj2 = {};
          const djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));

          for (const [key,value] of Object.entries(djObj) ) {
            dj2[key] = [];
            for (const obj of JSON.parse(value) ) {
              dj2[key].push({ ...obj?.fields , "pk":obj?.pk })
            }
          }

          dj2.invoice = dj2?.invoice?.length > 0 ? dj2.invoice[0] : {};
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj2.seller_vat = dj2?.seller_vat?.length > 0 ? dj2.seller_vat : [{
            "pk": dj2.seller.pk,
            "seller": dj2.seller.pk,
            "vat_country": "ES",
            "vat_number":"ES" + dj2.seller.nif_registration,
            "vat_vies": false,
          }];

          dj2.providers.map( (obj) => {
            obj['longname'] = obj.nif_cif_iva ? obj.name + " ( " + obj.nif_cif_iva + " ) " : obj.name;
            return obj;
          });

          dj2.customers.map((obj)=>{
            obj['longname2'] = obj.nif_cif_iva ? obj.name + " ( " + obj.nif_cif_iva + " ) " : obj.name;
            return obj;
          })

          dj2.rent.map((obj) => {
                        dj2.providers.filter((obj2) => {
                            if (obj2.pk == obj.provider) {
                              obj2.nif_cif_iva ? obj2.name + " ( " + obj2.nif_cif_iva + " ) " : obj2.name;
                            }
                        })
                        return obj;
                    })

          dj.value = dj2;
          console.log('DJ.value: ', dj.value);
          loadInputsData();
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      console.log(dj.value);
    };

    const setDefaultData = () => {
      const inv = dj?.value?.invoice ? dj.value.invoice : [];
      const status = inv.status  ? inv.status : inputInvoiceStatus.value;
      if (status && status != 'revised') {
        
      }
    }

    const loadInputsData = () => {
      const seller =  dj?.value?.seller ? dj.value.seller : [];
      const inv = dj?.value?.invoice ? dj.value.invoice : [];
      const concepts = dj?.value?.concepts ? dj.value.concepts : [];

      inputInvoiceCategory.value = inv.invoice_category ? inv.invoice_category : inputInvoiceCategory.value;
      inputInvoiceType.value = inv.invoice_type ? inv.invoice_type : inputInvoiceCategory.value;
      inputCustomer.value = inv.customer ? inv.customer : inputCustomer.value;
      inputProvider.value = inv.provider ? inv.provider : inputProvider.value;
      inputRent.value = inv.seller_rental ? inv.seller_rental : inputRent.value;
      inputTaxCountry.value = inv.tax_country ? inv.tax_country : inputTaxCountry.value;
      inputTaxResponsibility.value = inv.tax_responsibility ? inv.tax_responsibility : inputTaxResponsibility.value;
      inputOutOfTime.value = inv.out_of_time ? inv.out_of_time : inputOutOfTime.value;
      inputInvoiceNumber.value = inv.reference ? inv.reference : inputInvoiceNumber.value;
      inputInvoiceNumberAbsolute.value = seller.pk && inv.reference ? seller.pk + ";" + inv.reference : inputInvoiceNumberAbsolute.value;
      inputInvoiceNumberAbsolute.value = inv.reference_absolute ? inv.reference_absolute : inputInvoiceNumberAbsolute.value;
      inputInvoiceDate.value = inv.invoice_date ? inv.invoice_date : inputInvoiceDate.value;
      inputInvoiceDate.value = inv.expedition_date ? inv.expedition_date : inputInvoiceDate.value;
      inputInvoiceAccountingDate.value = inputInvoiceDate.value ? inputInvoiceDate.value : inputInvoiceAccountingDate.value;
      inputInvoiceAccountingDate.value = inv.accounting_date ? inv.accounting_date : inputInvoiceAccountingDate.value;
      inputIsRectifying.value = inv.is_rectifying ? inv.is_rectifying : inputIsRectifying.value;
      inputCurrency.value = inv.currency ? inv.currency : inputCurrency.value;
      inputIsDistanceSell.value = inv.is_oss ? inv.is_oss : inputIsDistanceSell.value;
      inputIsEquivalentTax.value = inv.is_eqtax? inv.is_eqtax : inputIsEquivalentTax.value;
      inputIsReverseCharge.value = inv.is_reverse_charge ? inv.is_reverse_charge : inputIsReverseCharge.value;
      inputDepartureCountry.value = inv.departure_country ? inv.departure_country : inputDepartureCountry.value;
      inputAccountSales.value = inv.account_sales ? inv.account_sales : inputAccountSales.value;
      inputAccountExpenses.value = inv.account_expenses ? inv.account_expenses : inputAccountExpenses.value;
      inputMarketplace.value = inv.marketplace ? inv.marketplace : inputMarketplace.value;
      inputEconomicActivity.value = inv.iae ? inv.iae : inputEconomicActivity.value;
      inputInvoiceTags.value = inv.tags ? inv.tags : inputInvoiceTags.value;
      inputInvoiceNotes.value = inv.notes ? inv.notes : inputInvoiceNotes.value;
      inputInvoiceName.value = inv.name ? inv.name : ( inv.file ? inv.file.replace("uploads/", "") : inputInvoiceName.value );
      inputInvoiceStatus.value = inv.status  ? inv.status : inputInvoiceStatus.value;
      inputInvoiceStatusOriginal.value = inputInvoiceStatus.value ? inputInvoiceStatus.value : inputInvoiceStatusOriginal.value;
      inputIsGeneratedAmz.value = inv.is_generated_amz ? inv.is_generated_amz : inputIsGeneratedAmz.value;
      inputRelatedInvoice.value = inv.related_invoice ? inv.related_invoice : inputRelatedInvoice.value;
      inputDiscardReason.value = inv.discard_reason ? inv.discard_reason : inputDiscardReason.value;
      inputDiscardReasonNotes.value = inv.discard_reason_notes ? inv.discard_reason_notes : inputDiscardReasonNotes.value;
      // inputTransactionType.value = inv.transaction_type ? inv.transaction_type : inputTransactionType.value;
      // inputOperationType.value = inv.operation_type ? inv.operation_type : inputOperationType.value;
      showCategory.value = inputInvoiceCategory.value ? false : true;
      
      if (inputInvoiceType.value == 'import-dua' && seller.contracted_accounting == true) {
        const invrelprov = getRelatedInvoiceProvider()?.length > 0 ? getRelatedInvoiceProvider()[0] : null;
        inputProvider.value = invrelprov && invrelprov.pk ? invrelprov.pk : inputProvider.value;
      }

      if (inputInvoiceType.value == 'import-expenses' && seller.contracted_accounting == true) {
        const invrel = getRelatedInvoiceById(inputRelatedInvoice.value);
        const invrelrel = invrel.related_invoice ? getRelatedInvoiceById(invrel.related_invoice) : null;
        const invrelprov = getRelatedInvoiceProvider()?.length > 0 ? getRelatedInvoiceProvider()[0] : null;
        inputProvider.value = invrelprov && invrelprov.pk ? invrelprov.pk : inputProvider.value;
        inputInvoiceDate.value = invrel && invrel.expedition_date ? invrel.expedition_date : inputInvoiceDate;
        inputInvoiceNumber.value = invrelrel && invrelrel.reference ? invrelrel.reference + "-PLUS" : inputInvoiceNumber.value;
        inputAccountExpenses.value = '631';
      }

      if (inputInvoiceType.value == 'import-invoice') {
        inputAccountExpenses.value = '600';
      }

      for (const con of concepts ) {
        addConcept({
          concept: con.concept || "",
          percentage: con.percentage || "100",
          quantity: con.quantity || "1",
          vat: con.vat || "0",
          vat_currency: con.vat_currency || "0",
          vat_euros: con.vat_euros || "0",
          irpf: con.irpf || "0",
          irpf_currency: con.irpf_curency || "0",
          irpf_euros: con.irpf_euros || "0",
          eqtax: con.eqtax || "0",
          eqtax_currency: con.eqtax_curency || "0",
          eqtax_euros: con.eqtax_euros || "0", 
          amount_original: con.amount_original || "0",         
          amount_currency: con.amount_currency || "0",
          amount_euros: con.amount_euros || "0",
          total_currency: con.total_currency || "0",
          total_euros: con.total_euros || "0",
          is_supplied: con.is_supplied || false,
        });
      }
      inputConceptsOld.value = [];
      for (const con of inputConcepts.value ) {
        inputConceptsOld.value.push({ ...con });
      }
    }

    const getCountryByCode = (code = "") => {
      const country = dj.value.countries.filter(co => {
        if (code) {
          return co.pk.toUpperCase() == code.toUpperCase();
        } else {
          return false;
        }
      })[0];
      return country;
    }

    const getCountryNameByCode = (code = "") => {
      const country = getCountryByCode(code);
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    };

    const getStatusBg = (code = "") => {
      let bg = "#FFC107";
      if(code == "revised") {
        bg = "#4CAF50";
      } else if (code == "discard") {
        bg = "#B71C1C";
      }
      return bg;
    };

    const getStatusByCode = (code = "") => {
      const stat = dj.value.status.filter(st => st.pk.trim().toUpperCase() == code.trim().toUpperCase())[0];
      const description = stat?.description ? stat?.description : code;
      return description;
    };

    const getCategoryByCode = (code = "") => {
      const category = dj.value.categories.filter(cat => cat?.pk?.trim()?.toUpperCase() == code?.trim()?.toUpperCase())[0];
      // const description = category?.description ? category?.description : code;
      return category;
    };

    const getTypeByCode = (code = "") => {
      const type = dj.value.types.filter(type => type?.pk?.trim()?.toUpperCase() == code?.trim()?.toUpperCase())[0];
      return type;
    };

    const getTypesForCategory = () => {
      let types = [];
      if (inputInvoiceCategory) {
        const category = inputInvoiceCategory?.value?.trim()?.toUpperCase();
        types = dj.value.types.filter(type => type?.category?.trim()?.toUpperCase() == category);
      }
      return types;
    }

    const getCustomerById = (id) => {
      let r = {};
      if (id && id != null) {
        r = dj.value.customers.filter(element => element.pk == id)[0];
      }
      return r;
    };

    const getProviderById = (id) => {
      let r = {};
      if (id && id != null) {
        r = dj.value.providers.filter(element => element.pk == id)[0];
      }
      return r;
    };

    const getRentByID = (id) => {
            let r = {};
            if (id && id != null) {
                r = dj.value.rent.filter(element => element.pk == id)[0];
            }
            return r;
        };

    const getRelatedInvoiceProvider = () => {
      let providers = [];
      const seller = dj.value.seller;
      if (seller.contracted_accounting == true) { 
        console.log("inputRelatedInvoice: ", inputRelatedInvoice.value);
        const relinv = getRelatedInvoiceById(inputRelatedInvoice.value);
        console.log("relinv: ", relinv);
        const relprov = getProviderById(relinv.provider);
        console.log("relprov: ", relprov);
        if (relprov && relprov != null && relprov?.pk != null) {
          providers.push(relprov);
        }
      } else {
        providers = dj.value.providers;
      }
      console.log("getRelatedInvoiceProvider: ", providers);
      return providers;
    };

    const getRelatedInvoiceById = (id) => {
      let r = {};
      if (id && id != null) {
        const imports = dj.value.import_invoices_not_related.filter(element => element.pk == id);
        if (imports && imports.length > 0) {
          r = imports[0];
        } else {
          const duas = dj.value.dua_invoices_not_related.filter(element => element.pk == id);
          if (duas && duas.length > 0) {
            r = duas[0];
          } else {
            const related = dj.value.invoice_relations.filter(element => element.pk == id);
            if (related && related.length > 0) {
              r = related[0];
            }
          }
        }
      }
      return r;
    };

    const setCustomerAccount = () => {
      const customer = getCustomerById(inputCustomer.value);
      if (customer && customer?.account_sales) {
        inputAccountSales.value = customer.account_sales
      }
      calcOperationType();
    }

    const setProviderAccount = () => {
      const provider = getProviderById(inputProvider.value);
      if (provider && provider?.account_expenses) {
        inputAccountExpenses.value = provider.account_expenses;
      }
      console.log("provider: ", provider);
      console.log("inputAccountExpenses: ", inputAccountExpenses.provider);
      calcOperationType();
    }

    const calcTransactionType = () => {
      let transactionType = null;
      const customer = getCustomerById(inputCustomer.value);
      const customerCountry = getCountryByCode(customer?.country);

      const provider = getProviderById(inputProvider.value);
      const providerCountry = (provider && provider?.is_origin_country == false) 
                              ? getCountryByCode(provider?.nif_cif_iva_country) 
                              : getCountryByCode(provider?.country);

      const taxCountry = inputTaxCountry.value?.toString().toUpperCase().trim();
      const sellerTaxCountry = getCountryByCode( taxCountry );

      const vatCurrency = getTotal().vatCurrency || 0;
      const vatEuros = getTotal().vatEuros || 0;

      if (inputInvoiceCategory.value == "sales" && inputInvoiceType.value == "sales") {
        if(sellerTaxCountry?.pk == customerCountry?.pk) {
          if (inputIsRectifying.value == true) {
            transactionType = "local-refund";
          } else {
            transactionType = "local-sale";
          }
        }
  
        if(sellerTaxCountry?.pk != customerCountry?.pk && customerCountry?.is_european_union == true) {
          if (customer.vies && customer.vies.trim().length > 0 && vatCurrency == 0 ) {
            if(customer?.customer_type.toString().toUpperCase() == 'B2B'){ 
              if (inputIsRectifying.value == true) {
                transactionType = "intra-community-refund";
              } else {
                transactionType = "intra-community-sale";
              }
            } else {
              if (inputIsRectifying.value == true) {
                transactionType = "local-refund";
              } else {
                transactionType = "local-sale";
              }
            }
          } else {
            if (inputIsRectifying.value == true) {
              transactionType = "local-refund";
            } else {
              transactionType = "local-sale";
            }
          }
          // if(customer?.customer_type.toString().toUpperCase() == 'B2C'){
          //   if (inputIsRectifying.value == true) {
          //     transactionType = "local-refund";
          //   } else {
          //     transactionType = "local-sale";
          //   }
          // }

          // if(customer?.customer_type.toString().toUpperCase() == 'B2B'){    
          //   if (customer.vies && customer.vies.trim().length > 0 && vatCurrency == 0 ) {
          //     if (inputIsRectifying.value == true) {
          //       transactionType = "intra-community-refund";
          //     } else {
          //       transactionType = "intra-community-sale";
          //     }
          //   } else {
          //     if (inputIsRectifying.value == true) {
          //       transactionType = "local-refund";
          //     } else {
          //       transactionType = "local-sale";
          //     }
          //   }
          // }
        }
  
        if(sellerTaxCountry?.pk != customerCountry?.pk && customerCountry?.is_european_union == false) {
          if (inputIsRectifying.value == true) {
            transactionType = "export-refund";
          } else {
            transactionType = "export-sale";
          }
        }
  
        if (inputIsDistanceSell.value == true) {
          if (inputIsRectifying.value == true) {
            transactionType = "oss-refund";
          } else {
            transactionType = "oss";
          }
        }
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "expenses") {
        if(sellerTaxCountry?.pk == providerCountry?.pk) {
          if (inputIsRectifying.value == true) {
            transactionType = "local-credit";
          } else {
            transactionType = "local-expense";
          }
        }

        if(sellerTaxCountry?.pk != providerCountry?.pk && providerCountry?.is_european_union == true) {
          if (provider.vies && provider.vies.trim().length > 0 && vatCurrency == 0 && providerCountry?.pk != 'GB') {
            if (inputIsRectifying.value == true) {
              transactionType = "intra-community-credit";
            } else {
              transactionType = "intra-community-expense";
            }
          } else if (providerCountry?.pk == 'GB') {
            if (inputIsRectifying.value == true) {
              transactionType = "extra-credit";
            } else {
              transactionType = "extra-expense";
            }
          } else {
            if (inputIsRectifying.value == true) {
              transactionType = "local-credit";
            } else {
              transactionType = "local-expense";
            }
          }
        }

        if(sellerTaxCountry?.pk != providerCountry?.pk && providerCountry?.is_european_union == false) {
          const nif_cif_iva = (provider && provider?.nif_cif_iva) ? provider.nif_cif_iva.trim().toUpperCase() : null;
          if ( nif_cif_iva && ( nif_cif_iva.startsWith('EU') || nif_cif_iva.startsWith('IM') ) &&  providerCountry?.pk != 'GB' ) {
            if (inputIsRectifying.value == true) {
              transactionType = "local-credit";
            } else {
              transactionType = "local-expense";
            }
          } else {
            if (inputIsRectifying.value == true) {
              transactionType = "extra-credit";
            } else {
              transactionType = "extra-expense";
            }
          } 
        }       
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "ticket") {
        transactionType = "local-expense";
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "import-invoice") {
        transactionType = "import-invoice";
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "import-dua") {
        transactionType = "import-dua";
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "import-expenses") {
        transactionType = "import-expenses";
      }

      transactionType = transactionType ? transactionType.toString().trim().toLowerCase() : transactionType;
      return transactionType;
    }

    const calcOperationType = () => {
      let operationType = null;
      const transactionType = inputTransactionType.value?.toString().toUpperCase().trim();
      const taxCountry = inputTaxCountry.value?.toString().toUpperCase().trim();
      const accountExpenses = inputAccountExpenses.value?.toString().toUpperCase().trim();

      if (inputInvoiceCategory.value == "sales" && inputInvoiceType.value == "sales") {
        if (taxCountry && taxCountry == "ES") {
          if(transactionType == "LOCAL-SALE" || transactionType == "LOCAL-REFUND") {
            operationType = 1;
          }
          if(transactionType == "INTRA-COMMUNTY-SALE" || transactionType == "INTRA-COMMUNTY-REFUND") {
            operationType = 3;
          }
          if(transactionType == "EXPORT-SALE" || transactionType == "EXPORT-REFUND") {
            operationType = 6;
          }
        }
  
        if (taxCountry && taxCountry != "ES") {
          operationType = 7;
        }
  
        if (transactionType == "OSS" || transactionType == "OSS-REFUND") {
          operationType = 7;
        }
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "expenses") {
        if (taxCountry && taxCountry == "ES") {
          if(transactionType == "LOCAL-EXPENSE" || transactionType == "LOCAL-CREDIT") {
            operationType = 1;
          }
          if(transactionType == "INTRA-COMMUNTY-EXPENSE" || transactionType == "INTRA-COMMUNTY-CREDIT") {
            if(accountExpenses && accountExpenses=='700') {
              operationType = 3;
            }
            if(accountExpenses && accountExpenses=='705') {
              operationType = 8;
            }
          }
        }
  
        if (taxCountry && taxCountry != "ES") {
          operationType = 7;
        }
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "ticket") {
        operationType = 7;
      }

      return operationType;
    }

    const convertToEuros = (value, currency, date) => {
      let total = 0;

      // Currency => Make Conversion
      if (!isNaN(value) && currency != 'EUR') {
        total = value * taxConversion.value;
      }

      // EUROS => No Conversion
      if (!isNaN(value) && currency == 'EUR') {
        total = value;
      }

      total = numberPrecision2(total);

      return total;
    };

    const getRectifyingAmount = (amount, force = false) => {
      let r = amount;
      if (inputIsRectifying.value == true && amount > 0) {
        r = amount * -1;
      } else if (inputIsRectifying.value == false && amount <= 0) {
        if (force) {
          r = amount * -1;
        }
      }
      return r;
    }

    const getConceptTotal = (amount, vatPercentage, irpfPercentage, conceptPecentage, quantity = 1, eqtaxPercentage = null) => {
      let total = 0;

      // Amount
      if (!isNaN(Number(amount))){
        total += Number(amount);
      }

      // Amount VAT
      if (!isNaN(Number(amount)) && !isNaN(Number(vatPercentage))){
        total += (Number(amount) * Number(vatPercentage) / 100);
      }

      // EqTax
      if (!isNaN(Number(amount)) && !isNaN(Number(vatPercentage))) {
        if (inputIsEquivalentTax.value == true) {
          let concepteqtax = 0;
          if (eqtaxPercentage == null) {
            const conceptvat = vatPercentage;
            if (conceptvat == 21) {
              concepteqtax = 5.2;
            } else if (conceptvat == 10) {
              concepteqtax = 1.4;
            } else if (conceptvat == 4) {
              concepteqtax = 0.5;
            } else {
              concepteqtax = 0;
            }
          } else {
            concepteqtax = eqtaxPercentage;
          }
          total += (Number(amount) * Number(concepteqtax) / 100);
          {% comment %} // const dec_concepteqtax = to_decimal_value(concepteqtax); {% endcomment %}
          {% comment %} //total = total.plus(dec_amount.times(dec_concepteqtax).dividedBy(dec_hundred)); {% endcomment %}
        }
      }

      // Amount IRPF
      if (!isNaN(Number(amount)) && !isNaN(Number(irpfPercentage) && irpfPercentage > 0 && irpfPercentage <= 100)){
        total += (Number(amount) * Number(irpfPercentage) * -1 / 100);
      }

      // Quantity
      if (!isNaN(Number(quantity))){
        total *= Number(quantity);
      } else {
        // total = 0;
      }

      // Round 2 Decimals
      total = numberPrecision2(total);

      return total;
    };

    const getTotal = () => {
      let total = {
        amountCurrency: Number(0),
        amountSupliedCurrency: Number(0),
        amountCurrencyWithoutSuplied: Number(0),
        vatCurrency: Number(0),
        irpfCurrency: Number(0),
        eqtaxCurrency: Number(0),
        totalCurrency: Number(0),

        amountEuros: Number(0),
        amountSupliedEuros: Number(0),
        amountEurosWithoutSuplied: Number(0),
        vatEuros: Number(0),
        irpfEuros: Number(0),
        eqtaxEuros: Number(0),
        totalEuros: Number(0),
      };

      for (const concept of inputConcepts.value) {
        const qty = Number(concept.quantity);

        // CURRENCY

        total.amountCurrency += concept.amount_currency
          ? (Number(concept.amount_currency) * qty) : 0;
        if (concept.is_supplied && concept.is_supplied == true) {
          total.amountSupliedCurrency += concept.amount_currency
            ? (Number(concept.amount_currency) * qty) : 0;
        }

        total.vatCurrency += concept.amount_currency && concept.vat
          ? (Number(concept.amount_currency) * Number(concept.vat) * qty / 100) : 0;
      
        total.irpfCurrency += concept.amount_currency && concept.irpf
          ? (Number(concept.amount_currency) * Number(concept.irpf) * qty / 100) : 0;

        total.eqtaxCurrency += concept.amount_currency && concept.eqtax
          ? (Number(concept.amount_currency) * Number(concept.eqtax) * qty / 100) : 0;

        // EUROS
        
        total.amountEuros += concept.amount_euros
          ? (Number(concept.amount_euros) * qty) : 0;
        if (concept.is_supplied && concept.is_supplied == true) {
          total.amountSupliedEuros += concept.amount_euros
            ? (Number(concept.amount_euros) * qty) : 0;
        }

        total.vatEuros += concept.amount_euros && concept.vat
          ? (Number(concept.amount_euros) * Number(concept.vat) * qty / 100) : 0;
        
        total.irpfEuros += concept.amount_euros && concept.irpf
          ? (Number(concept.amount_euros) * Number(concept.irpf) * qty / 100) : 0;
       
        total.eqtaxEuros += concept.amount_euros && concept.eqtax
          ? (Number(concept.amount_euros) * Number(concept.eqtax) * qty / 100) : 0;
      }
      total.amountCurrencyWithoutSuplied = total.amountCurrency - total.amountSupliedCurrency;
      total.amountEurosWithoutSuplied = total.amountEuros - total.amountSupliedEuros;

      // Calc Total EUROS / CURRENCY
      total.totalCurrency = total.amountCurrency + total.vatCurrency + (total.irpfCurrency * -1) + total.eqtaxCurrency;
      total.totalEuros =  total.amountEuros + total.vatEuros + (total.irpfEuros * -1) + total.eqtaxEuros;

      // Round 2 Decimals
      total.totalCurrency = numberPrecision2(total.totalCurrency);
      total.amountCurrency = numberPrecision2(total.amountCurrency);
      total.vatCurrency = numberPrecision2(total.vatCurrency);
      total.irpfCurrency = numberPrecision2(total.irpfCurrency);
      total.eqtaxCurrency = numberPrecision2(total.eqtaxCurrency);
      total.totalEuros = numberPrecision2(total.totalEuros);
      total.amountEuros = numberPrecision2(total.amountEuros);
      total.vatEuros = numberPrecision2(total.vatEuros);
      total.irpfEuros = numberPrecision2(total.irpfEuros);
      total.eqtaxEuros = numberPrecision2(total.eqtaxEuros);

      return total;
    }

    const conceptDefaultValues = (concept) => {
      // Percentage
      if (concept?.percentage == null || concept?.percentage == undefined || concept?.percentage == "" || isNaN(concept.percentage)) {
        concept.percentage = 100;
      } else if (!isNaN(concept.percentage) && Number(concept.percentage) > 100) {
        concept.percentage = 100;
      } else if (!isNaN(concept.percentage) && Number(concept.percentage) < 0) {
        concept.percentage = Number(concept.percentage) * -1;
      }
      // Quantity
      if (concept?.quantity == null || concept?.quantity == undefined || concept?.quantity == "" || isNaN(concept.quantity)) {
        concept.quantity = 1;
      } else if (!isNaN(concept.quantity) && Number(concept.quantity) < 0) {
        concept.quantity = Number(concept.quantity) * -1;
      }
      // VAT
      if (concept?.vat == null || concept?.vat == undefined || concept?.vat == "" || isNaN(concept.vat)) {
        concept.vat = 0;
      } else if (!isNaN(concept.vat) && Number(concept.vat) < 0) {
        concept.vat = Number(concept.vat) * -1;
      }
      // IRPF
      if (concept?.irpf == null || concept?.irpf == undefined || concept?.irpf == "" || isNaN(concept.irpf)) {
        concept.irpf = 0;
      } else if (!isNaN(concept.irpf) && Number(concept.irpf) < 0) {
        concept.irpf = Number(concept.irpf) * -1;
      }
      // EQTAX
      if (concept?.eqtax == null || concept?.eqtax == undefined || concept?.eqtax == "" || isNaN(concept.eqtax)) {
        concept.eqtax = 0;
      } else if (!isNaN(concept.eqtax) && Number(concept.eqtax) < 0) {
        concept.eqtax = Number(concept.eqtax) * -1;
      }
      // Amount (Base Imponible)
      if (
        concept?.amount_currency == null ||
        concept?.amount_currency == undefined ||
        concept?.amount_currency == "" ||
        isNaN(concept.amount_currency)
      ) {
        concept.amount_currency = 0;
      }

      // Amount Original (Base Imponible)
      if (
        concept?.amount_original == null ||
        concept?.amount_original == undefined ||
        concept?.amount_original == "" ||
        isNaN(concept.amount_original)
      ) {
        concept.amount_original = 0;
      }
    }

    const addConcept = (concept = null) => {
      if (concept == null) {
        concept = {
          concept: "",
          percentage: 100,
          quantity: 1,
          vat: 0,
          vat_currency: 0,
          vat_euros: 0,
          irpf: 0,
          irpf_currency: 0,
          irpf_euros: 0,
          eqtax: 0,
          eqtax_currency: 0,
          eqtax_euros: 0,
          amount_original: 0,
          amount_currency: 0,
          amount_euros: 0,
          total_currency: 0,
          total_euros: 0,
        };
      }
      inputConcepts.value.push( concept );
      onChangeAmountCurrency(concept);
    };

    const addConceptSuplied = (concept = null) => {
      if (concept == null) {
          concept = {
          concept: "",
          percentage: 100,
          quantity: 1,
          vat: 0,
          vat_currency: 0,
          vat_euros: 0,
          irpf: 0,
          irpf_currency: 0,
          irpf_euros: 0,
          eqtax: 0,
          eqtax_currency: 0,
          eqtax_euros: 0,
          amount_original: 0,
          amount_currency: 0,
          amount_euros: 0,
          total_currency: 0,
          total_euros: 0,
          is_supplied: true
        };
      } else {
        addConcept(concept);
      }

      inputConcepts.value.push(concept);
      onChangeAmountCurrency(concept);
    }

    const removeConcept = (index) => {
      inputConcepts.value.splice(index, 1);
    };

    const recalcTotalConcepts = () => {
      for (const concept of inputConcepts.value) {
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);        
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcCurrencyConcepts = () => {
      for (const concept of inputConcepts.value) {
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcIVA0Concepts = () => {
      for (const concept of inputConcepts.value) {
        concept.vat = 0;
        concept.vat_currency = 0;
        concept.vat_euros = 0;
        concept.eqtax = 0;
        concept.eqtax_currency = 0;
        concept.eqtax_euros = 0;
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcIVA21Concepts = () => {
      for (const concept of inputConcepts.value) {
        concept.vat = 21;
        concept.vat_currency = concept.amount_currency * 0.21;
        concept.vat_euros = convertToEuros(concept.vat_currency, inputCurrency.value, inputInvoiceDate.value);
        if (inputIsEquivalentTax.value == true) {
          concept.eqtax = 5.2;
          concept.eqtax_currency = concept.amount_currency * 0.052;
          concept.eqtax_euros = convertToEuros(concept.eqtax_currency, inputCurrency.value, inputInvoiceDate.value);
        } else {
          concept.eqtax = 0;
          concept.eqtax_currency = 0;
          concept.eqtax_euros = 0;
        }
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcJsonIVA = (concepts) => {
      let vats_euros = {};
      let vats_currency = {};
      let quantity = 0;
      if (concepts && concepts.length > 0)
        for (const concept of concepts) {
          if (concept.vat) {
            const vat_float = numberPrecision2(concept.vat);
            if (vat_float != 0.00) {
              if (!vats_euros[vat_float.toString()]) {
                vats_euros[vat_float.toString()] = 0.00;
              }
              if (!vats_currency[vat_float.toString()]) {
                vats_currency[vat_float.toString()] = 0.00;
              }
              if (concept.quantity) {
                quantity = parseFloat(concept.quantity.toString());
                if (isNaN(quantity)) {
                  quantity = 0.0;
                }
              }

              if (concept.vat && concept.amount_euros) {
                vats_euros[vat_float.toString()] += (parseFloat(concept.amount_euros.toString()) * vat_float * quantity / 100.0);
              }

              if (concept.vat && concept.amount_currency) {
                vats_currency[vat_float.toString()] += (parseFloat(concept.amount_currency.toString()) * vat_float * quantity / 100.0);
              }

            }
          }
        }

      // Redondear/Truncar los valores del array
      for (const key in vats_euros) {
        vats_euros[key] = numberPrecision2(vats_euros[key]);
      }
      for (const key in vats_currency) {
        vats_currency[key] = numberPrecision2(vats_currency[key]);
      }

      // Convertir el objeto en un array de pares clave-valor y ordenarlo por clave
      const arr_eur = Object.entries(vats_euros);
      const arr_cur = Object.entries(vats_currency);
      arr_eur.sort((a, b) => a[0] - b[0]);
      arr_cur.sort((a, b) => a[0] - b[0]);

      // Convertir el array ordenado de nuevo en un objeto
      vats_euros = Object.fromEntries(arr_eur);
      vats_currency = Object.fromEntries(arr_cur);

      // Actualizar los campos
      all_vats_euros.value = vats_euros;
      all_vats_currency.value = vats_currency;
      inputJsonVats.value = JSON.stringify(vats_euros);
      return vats_euros;
    }

    const deleteAllConcepts = () => {
      if (inputConcepts.value && inputConcepts.value.length > 0) {
        inputConcepts.value.splice(0, inputConcepts.value.length);
      }
      recalcJsonIVA();
    }

    const onChangeCategory = () => {
      inputInvoiceType.value = inputInvoiceCategory.value;
    }

    const onChangeType = () => {
      if(inputInvoiceCategory && inputInvoiceType) {
        showCategory.value = false;
        if (inputInvoiceType.value == 'ticket') {
          for (let concept of inputConcepts.value) {
            concept.percentage = 100;
            concept.quantity = 1;
            concept.vat = 0;
            concept.irpf = 0;
            concept.eqtax = 0;
            concept.total_currency = concept.amount_currency;
            concept.total_euros = concept.amount_euros;
          }
          inputTotalCurrency.value = getTotal()?.totalCurrency || 0;
          inputTotalEuros.value = getTotal()?.totalEuros || 0;
        }
      }
    }

    const onChangeIsRectifying = () => {
      for (const concept of inputConcepts.value) {
        concept.amount_currency = getRectifyingAmount(concept.amount_currency, true);
        onChangeAmountCurrency(concept);
      }
    }

    const onChangeAmountCurrency = (concept) => {
      if (inputIsRectifying && getRectifyingAmount(concept.amount_currency) == concept.amount_currency)
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
      else {
        concept.amount_currency = getRectifyingAmount(concept.amount_currency);
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    };

    const onChangeAmountEuros = (concept) => {
      concept.amount_euros = getRectifyingAmount(concept.amount_euros);
    }

    const onClickSubmit = (status) => {
      if (status != 'revised') {
        setAllFieldsNotRequired();
        if (!inputInvoiceType.value || inputInvoiceType.value == null) {
          inputConcepts.value = [];
        } else {
          for (const concept of inputConcepts.value) {
            if (concept.concept.trim() == "") {
              concept.concept = "Concepto";
            }
          }
        }
      }

      const form = document.getElementById("form");
      form.classList.add("was-validated");
      const validate = form.checkValidity();
      
      if (validate == true) {
        inputInvoiceStatus.value = status;
        if(status == "discard") {
          const now = new Date();
          const isodate = new Date(now.getTime() - (now.getTimezoneOffset() * 60000)).toISOString();
          let newInvoiceNumber = (isodate.replaceAll("-", "").replaceAll("T", "").replaceAll(":", "")).split(".")[0];
          inputInvoiceNumber.value = newInvoiceNumber + "-DISCARD";
        }
      }
    }

    const setAllFieldsDisabled = () => {
      const fieldset1 = document.getElementById("fieldset1");
      console.log(fieldset1)
      if (fieldset1 != null) {
        fieldset1.disabled = true;
      }
      const fieldset2 = document.getElementById("fieldset2");
      if (fieldset2 != null) {
        fieldset2.disabled = true;
      }
      const fieldset3 = document.getElementById("fieldset3");
      if (fieldset3 != null) {
        fieldset3.disabled = true;
      }
    }

    const setAllFieldsNotRequired = () => {
      const form = document.getElementById("form");
      const elements = form.getElementsByTagName("*");
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].nodeName === "INPUT" || elements[i].nodeName === "SELECT" || elements[i].nodeName === "TEXTAREA") {
          elements[i].required = false;
        }
      }
    }

    const numberPrecision2 = (number) => {
      let m = Number((Math.abs(number) * 100).toPrecision(15));
      return Math.round(m) / 100 * Math.sign(number);
    }

    const onclickTagA = (direction)=>{
      const iframe = document.getElementById("iframeNewClient");
      const popup = "?popup=true";
      console.log(direction);
      
      if(direction=="new"){ 
        iframe.style = "display: none;";
        iframe.src="{% url 'app_customers:new' seller.shortname %}" + popup;
        document.getElementById("iframeloading").style = "display: block;";
        
      }
      else if (direction=="edit"){
        iframe.style = "display: none;";
        iframe.src= "{% url 'app_customers:list' seller.shortname %}" + inputCustomer.value + "/" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      }

      else if (direction=="editProv"){
        iframe.style = "display: none;";
        iframe.src= "{% url 'app_providers:list' seller.shortname %}" + inputProvider.value + "/" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      }
      else if (direction=="newProv"){
        iframe.style = "display: none;";
        iframe.src= "{% url 'app_providers:new' seller.shortname %}" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      }
    }

    const iframe = ()=>{

      console.log("Evento iframe cargado"); 
      console.log(dj.customers)
      const iframe = document.getElementById("iframeNewClient");
      const form = iframe.contentDocument.getElementById("form");
        if (form) {   
          iframe.style = "display: block;";
          document.getElementById("iframeloading").style = "display: none;";
          form.addEventListener("submit", () => {
            console.log("Me cierro"); 
            iframe.style = "display: none;"
            document.getElementById("iframeloading").style = "display: block;";
            setTimeout(reloadPage, 1000);
          }); 
        } 
        else {
          iframe.style = "display: none;";
          document.getElementById("iframeloading").style = "display: block;";

          
          $(document).ready(function(){
            $('#newCostumer').modal('hide');
          });          
        }
    }
    
    const inputCarrusel = ()=>{

      document.getElementById("carrusel").value = "carruselTrue";
    }

    const final_date_period = (final_date) =>{
      let date= inputInvoiceAccountingDate.value;

      if(date < final_date){
        inputInvoiceAccountingDate.value = final_date;
      }
    }

    const show_accounting_date = (final_date)=>{
      let invoice_date = inputInvoiceDate.value;
      if(invoice_date < final_date){
        inputOutOfTime.value = true;
        final_date_period(final_date);
      }
         
    }

    const getEuropeanCountries = () => {
      let eur = dj.value.countries.filter( country => country.is_european_union == true );
      return eur;
    }


    // WATCHERS ////////////////////////////////////////////////////////////////////////
    watch(inputCurrency, async(newValue) => {

      const response = await fetch(`/api/get-tax-conversion/?invoice_date=${inputInvoiceDate.value}&currency=${newValue}`);
      const data = await response.json();
      
      if (data.tax_conversion) {
        taxConversion.value = data.tax_conversion; 
      } else {
        console.error('Error fetching tax conversion:', data.error);
      }

      console.log('inputCurrency watcher');
      for (const concept of inputConcepts.value) {
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch(inputInvoiceDate, async(newValue) => {

      const response = await fetch(`/api/get-tax-conversion/?invoice_date=${newValue}&currency=${inputCurrency.value}`);
      const data = await response.json();
      
      if (data.tax_conversion) {
        taxConversion.value = data.tax_conversion; 
      } else {
        console.error('Error fetching tax conversion:', data.error);
      }

      console.log('inputInvoiceDate watcher');
      for (const concept of inputConcepts.value) {
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch(inputIsRectifying, (newValue) => {
      console.log('inputIsRectifying watcher');
      for (const concept of inputConcepts.value) {
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch(inputIsEquivalentTax, (newValue) => {
      console.log('inputIsEquivalentTax watcher');
      for (const concept of inputConcepts.value) {
        if (inputInvoiceStatus.value != 'revised') {
          if (inputIsEquivalentTax.value == true && concept.vat) {
            if (concept.vat == 21) {
              concept.eqtax = 5.2;
            } else if (concept.vat == 10) {
              concept.eqtax = 1.4;
            } else if (concept.vat == 4) {
              concept.eqtax = 0.5;
            } else {
              concept.eqtax = 0;
            }
          } else {
            concept.eqtax = 0;
          }
        }
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch((inputConcepts, inputConcepts.value), (newValue) => {
      console.log('inputConcepts watcher');
      
      inputHaveSupplies.value = false;
      let i = 0;
      for (const concept of newValue) {
        if (concept.is_supplied == true) {
          inputHaveSupplies.value = true;
        }
        if (i < inputConceptsOld.value.length) {
          const oldConcept = inputConceptsOld.value[i];

          if (
            oldConcept.quantity != concept.quantity ||
            oldConcept.percentage != concept.percentage ||
            oldConcept.vat != concept.vat ||
            oldConcept.irpf != concept.irpf ||
            oldConcept.amount_currency != concept.amount_currency ||
            oldConcept.amount_euros != concept.amount_euros ||
            oldConcept.total_currency != concept.total_currency ||
            oldConcept.total_euros != concept.total_euros ||
            oldConcept.eqtax != concept.eqtax
          ){        
            // STRING TO NUMBER
            concept.quantity = Number(concept.quantity);
            // concept.vat = Number(concept.vat);
            // concept.irpf = Number(concept.irpf);
            // concept.eqtax = Number(concept.eqtax);
            // concept.amount_currency = Number(concept.amount_currency);
            // concept.amount_euros = Number(concept.amount_euros);

            // EQTAX
            if (oldConcept.vat != concept.vat ) {
              if (inputIsEquivalentTax.value == true && concept.vat) {
                if (concept.vat == 21) {
                  concept.eqtax = 5.2;
                } else if (concept.vat == 10) {
                  concept.eqtax = 1.4;
                } else if (concept.vat == 4) {
                  concept.eqtax = 0.5;
                } else {
                  concept.eqtax = 0;
                }
              }
            }

            // CURRENCY
            if(oldConcept.total_currency == concept.total_currency) {
              concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax); 
            }
            concept.vat_currency   = ( concept.amount_currency * concept.vat   / 100.0);
            concept.irpf_currency  = ( concept.amount_currency * concept.irpf  / 100.0);
            concept.eqtax_currency = ( concept.amount_currency * concept.eqtax / 100.0);

            // EUROS
            concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
            concept.vat_euros   = ( concept.amount_euros * concept.vat   / 100.0);
            concept.irpf_euros  = ( concept.amount_euros * concept.irpf  / 100.0);
            concept.eqtax_euros = ( concept.amount_euros * concept.eqtax / 100.0);
          }
        }
        i++;
      }

      inputTotalCurrency.value = getTotal()?.totalCurrency || 0;
      inputTotalEuros.value = getTotal()?.totalEuros || 0;
      recalcJsonIVA(newValue);

      inputConceptsOld.value = [];
      for (const con of inputConcepts.value ) {
        inputConceptsOld.value.push({ ...con });
      } 
    });

    watch(inputInvoiceNumber, (newValue) => {
      console.log('inputInvoiceNumber watcher');
      inputInvoiceNumberAbsolute.value = dj.value.seller.pk + ";" + inputInvoiceNumber.value;
    });

    watch(inputProvider, (newValue) => {
            if (inputInvoiceType.value) {
                if (inputInvoiceType.value == 'rent'){
                    inputAccountExpenses.value = 621;
                } else if ( !dj.value.invoice.account_expenses ) {
                    setProviderAccount();
                } 
            }           
        });

    watch(inputCustomer, (newValue) => {
      if ( !dj.value.invoice.account_sales ) {
        setCustomerAccount();
      }
    });

    watch(inputRent, (newValue) => {
            const filtered = dj.value.rent.filter(rent => rent.pk.toString() == inputRent.value.toString());
            if (filtered && filtered.length > 0) {
                inputProvider.value = filtered[0].provider;
            }
        });

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    if (inputConcepts?.value?.length < 1) {
      addConcept();
    }

    if (inputInvoiceStatus.value == 'revised' || inputInvoiceStatus.value == 'discard') {
      setAllFieldsDisabled();
    }

    recalcTotalConcepts();
    console.log(inputConcepts.value);

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      showCategory,
      editName,
      inputInvoiceCategory,
      inputInvoiceType,
      inputTaxCountry,
      inputTaxResponsibility,
      inputOutOfTime,
      inputIsRectifying,
      inputInvoiceNumber,
      inputInvoiceNumberAbsolute,
      inputInvoiceDate,
      inputInvoiceAccountingDate,
      inputCurrency,
      inputIsDistanceSell,
      inputIsEquivalentTax,
      inputIsReverseCharge,
      inputDepartureCountry,
      inputAccountSales,
      inputAccountExpenses,
      inputCustomer,
      inputProvider,
      inputRent,
      inputMarketplace,
      inputEconomicActivity,
      inputInvoiceTags,
      inputInvoiceNotes,
      inputTransactionType,
      inputOperationType,
      inputInvoiceName,
      inputInvoiceStatus,
      inputInvoiceStatusOriginal,
      inputIsGeneratedAmz,
      inputDiscardReason,
      inputDiscardReasonNotes,
      inputRelatedInvoice,
      inputTotalCurrency,
      inputTotalEuros,
      inputHaveSupplies,
      inputConcepts,
      inputConceptsJSON,
      inputJsonVats,
      all_vats_currency,
      all_vats_euros,

      getRelatedInvoiceById,
      getRelatedInvoiceProvider,
      getProviderById,
      getRentByID,
      setProviderAccount,
      getCustomerById,
      setCustomerAccount,
      getCountryNameByCode,
      getStatusBg,
      getStatusByCode,
      getCategoryByCode,
      getTypeByCode,
      getTypesForCategory,
      getTotal,
      convertToEuros,
      conceptDefaultValues,
      addConcept,
      removeConcept,
      recalcTotalConcepts,
      recalcCurrencyConcepts,
      recalcIVA0Concepts,
      recalcIVA21Concepts,
      deleteAllConcepts,
      onChangeCategory,
      onChangeType,
      onChangeIsRectifying,
      onChangeAmountCurrency,
      onChangeAmountEuros,
      onClickSubmit,
      numberPrecision2,
      iframe,
      onclickTagA,
      reloadPage,
      inputCarrusel,
      final_date_period,
      show_accounting_date,
      getEuropeanCountries,
    };

    // CREATE VUE 3
    const createVue3 = (target, data_export) => {
      const { createApp } = Vue;
      const { createVuetify } = Vuetify;

      const app = createApp({
          delimiters: ['[[', ']]'],
          el: target,
          data() { return { ...data_export } }
      });
      const vuetify = createVuetify();
      app.use(vuetify)
      app.mount(target);
    };
    createVue3('#card-header', data_export);
    createVue3('#formBody', data_export);
    createVue3('.vue', data_export);

    window.addEventListener('scroll', function () {
      const header = document.querySelector('.pdf');
      if (header) {
        header.classList.toggle("sticky", window.scrollY > 0);
      }
    });

    
    let taxCountry = document.getElementById("inputTaxCountry");
    taxCountry.addEventListener("change", function () {
        let eqtaxElements = document.querySelectorAll(".eqtax");

        if (taxCountry.value == "ES") {
            eqtaxElements.forEach(function(element) {
                element.style.display = "block";
            });
        } else {
            eqtaxElements.forEach(function(element) {
                element.style.display = "none";
            });
        }
    });




</script>
{% endblock javascripts %}
