// === sellerVata_formIVA_functions.js ===

// Asegurar el namespace principal
window.FormIVA = window.FormIVA || {};

// ========== MÓDULO DE UTILIDADES ==========
FormIVA.Utils = {
    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    },

    getLabelForInput(input) {
        if (!input) return "(sin nombre)";

        const label = document.querySelector(`label[for="${input.id}"]`);
        if (label) return label.innerText.trim();

        const row = input.closest('.form-group.row');
        if (row) {
            const labelDiv = row.querySelector('.col-form-label');
            if (labelDiv) return labelDiv.innerText.trim();
        }

        return input.name || "(sin label)";
    },

    buildHtmlBlock(title, items) {
        if (!items.length) return "";
        const lines = items.map(item => `<p style="margin: 0 0 6px 0;">${item}</p>`).join("");
        return `
            <h6 style="margin-top: 1rem; margin-bottom: .4rem; font-weight: 600;">${title}</h6>
            <div>${lines}</div>
        `;
    }
};

// ========== MÓDULO DE BLOQUEO ==========
FormIVA.Lockers = {
    lockFileInputField(field, iso = "", suffix = "", baseField = "") {
        if (!field || field.type !== "file") return;

        field.classList.add("d-none");
        const colDiv = field.closest(".col-lg-4");

        if (colDiv) {
            const autoLabel = colDiv.querySelector("label");
            if (autoLabel) autoLabel.classList.add("d-none");

            const fieldWrapper = field.closest(".mb-3");
            if (fieldWrapper) fieldWrapper.classList.remove("mb-3");
        }

        const fileInfoId = `${baseField || field.name}_fileinfo_${iso}${suffix}`;
        const fileInfoP = document.getElementById(fileInfoId);
        if (fileInfoP) {
            fileInfoP.classList.remove("d-none");
            fileInfoP.classList.add("readonly-file-display", "form-control", "col-lg-4");

            const parentRow = fileInfoP.closest(".form-group.row");
            if (parentRow) {
                const existingManualLabel = parentRow.querySelector(".col-lg-6.col-form-label");
                if (!existingManualLabel) {
                    const labelDiv = document.createElement("div");
                    labelDiv.className = "col-lg-6 col-form-label text-lg-end";
                    parentRow.insertBefore(labelDiv, fileInfoP.parentElement);
                }
            }
        }
    },

    lockCompanyInfoFields(allowedFields = []) {
        document.querySelectorAll('#company_info_form input, #company_info_form select, #company_info_form textarea')
            .forEach(field => {
                const name = field.name || field.id?.replace("id_", "");
                const type = field.type;
                const isAllowed = allowedFields.includes(name);

                if (type === "file") {
                    if (!isAllowed) {
                        FormIVA.Lockers.lockFileInputField(field);
                    }
                } else {
                    if (!isAllowed) {
                        field.setAttribute("readonly", "true");
                        field.setAttribute("disabled", "true");
                    }
                }
            });
    },

    lockReadonlyFields(isFormProcessed, submittedCountries = [], partialSaved = false, isNewForm = false) {
        if (isNewForm) return;

        if (isFormProcessed) {
            DebugLogger.log("[Lockers] 🔒 Formulario completo bloqueado");
            FormIVA.Lockers.lockCompanyInfoFields();
            FormIVA.Lockers.lockPartners();
            FormIVA.Lockers.lockAllMigrationForms();
            FormIVA.Lockers.lockAllCountryDocuments();
            FormIVA.Lockers.disableSaveButtons();
            return;
        }

        const allIsoButtons = [...document.querySelectorAll("[data-country-iso]")].map(btn =>
            btn.dataset.countryIso
        );

        allIsoButtons.forEach(iso => {
            if (submittedCountries.includes(iso)) {
                DebugLogger.log(`[Lockers] 🔒 Bloqueando país ya enviado: ${iso}`);
                FormIVA.Lockers.lockMigrationFormByIso(iso);
                FormIVA.Lockers.lockCountryDocumentsByIso(iso);
            }
        });
    },

    disableSaveButtons() {
        document.querySelectorAll(".partial_saving").forEach(btn => {
            btn.setAttribute("disabled", "true");
            btn.classList.add("d-none");
        });

        const finalizarBtn = document.getElementById("finalizarButton");
        if (finalizarBtn) {
            finalizarBtn.setAttribute("disabled", "true");
            finalizarBtn.classList.add("d-none");
        }

        DebugLogger.log("[Lockers] 🧯 Botones de guardar y finalizar ocultos");
    },

    lockAllMigrationForms() {
        const allMigrationForms = document.querySelectorAll("[id^='migration-form-']");
        allMigrationForms.forEach(formDiv => {
            const iso = formDiv.dataset.countryIso;
            FormIVA.Lockers.lockMigrationFormByIso(iso);
        });
    },

    lockAllCountryDocuments() {
        const allDocForms = document.querySelectorAll("[id^='documents-form-']");
        allDocForms.forEach(formDiv => {
            const iso = formDiv.dataset.countryIso;
            FormIVA.Lockers.lockCountryDocumentsByIso(iso);
        });
    },

    lockMigrationFormByIso(iso, allowedFields = []) {
        const migrationForm = document.querySelector(`#migration-form-${iso}`);
        if (!migrationForm) return;

        migrationForm.querySelectorAll("input, select, textarea").forEach(field => {
            const name = field.name;
            const suffixMatch = name.match(/_(\d+)$/);
            const suffix = suffixMatch ? `_${suffixMatch[1]}` : "";
            const baseField = suffix ? name.replace(/_\d+$/, "") : name;
            const isAllowed = allowedFields.includes(name);

            if (field.type === "file") {
                if (!isAllowed) {
                    FormIVA.Lockers.lockFileInputField(field, iso, suffix, baseField);
                }
            } else {
                if (!isAllowed) {
                    field.setAttribute("readonly", "true");
                    field.setAttribute("disabled", "true");
                }
            }
        });
    },

    lockCountryDocumentsByIso(iso, allowedFields = []) {
        const docForm = document.querySelector(`#documents-form-${iso}`);
        if (!docForm) return;

        docForm.querySelectorAll("input, select, textarea").forEach(field => {
            const name = field.name;
            const suffixMatch = name.match(/_(\d+)$/);
            const suffix = suffixMatch ? `_${suffixMatch[1]}` : "";
            const baseField = suffix ? name.replace(/_\d+$/, "") : name;
            const isAllowed = allowedFields.includes(name);

            if (field.type === "file") {
                if (!isAllowed) {
                    FormIVA.Lockers.lockFileInputField(field, iso, suffix, baseField);
                }
            } else {
                if (!isAllowed) {
                    field.setAttribute("readonly", "true");
                    field.setAttribute("disabled", "true");
                }
            }
        });
    },

    lockPartners(validationData = {}) {
        DebugLogger.log("[Lockers] 🔒 Ocultando botones de socios sin errores");

        const addNewBtn = document.querySelector(".new-partner-btn");
        if (addNewBtn) addNewBtn.classList.add("d-none");

        document.querySelectorAll(".edit-partner-btn").forEach(btn => {
            const partnerId = btn.dataset.partnerId;

            const hasPendingError = Object.entries(validationData.partner || {}).some(
                ([key, value]) =>
                    key.startsWith(`${partnerId}-`) &&
                    value.status === "incorrecto" &&
                    value.pending === true
            );

            if (!hasPendingError) {
                btn.classList.add("d-none");
            } else {
                btn.classList.remove("d-none");
                DebugLogger.log(`[Lockers] 🔓 Mostrando botón de edición para socio con error: ${partnerId}`);
            }
        });
    }
};

// ========== MÓDULO DE UI ==========
FormIVA.UI = {
    updateProgressBar(tabId) {
        const progressBar = document.querySelector('.progress-bar');
        let progressValue = 25;

        switch (tabId) {
            case 'general-tab': progressValue = 25; break;
            case 'members-tab': progressValue = 50; break;
            case 'documents-tab': progressValue = 75; break;
            case 'summary-tab': progressValue = 100; break;
        }

        progressBar.style.width = progressValue + '%';
        progressBar.setAttribute('aria-valuenow', progressValue);
    },

    changeTab(tabId) {
        document.querySelectorAll('.tab-pane').forEach(tab => tab.style.display = "none");
        document.querySelectorAll('.tab-pane').forEach(tab => tab.classList.remove('active'));
        document.getElementById(tabId.replace('-tab', '')).style.display = "block";
        document.getElementById(tabId.replace('-tab','')).classList.add('active');
        document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
        document.getElementById(tabId).classList.add('active');
        FormIVA.UI.updateProgressBar(tabId);
    },

    openConfirmationModal() {
        const modal = new bootstrap.Modal(document.getElementById('confirmForm'));
        modal.show();
    }
};
