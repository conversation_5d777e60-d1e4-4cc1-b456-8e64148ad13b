from django.db import models

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from django.utils.translation import gettext as _

User = get_user_model()

class Absence(models.Model):
    manager = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name="absence_manager",
        verbose_name="Gestor",
        limit_choices_to={'role': 'manager'},
    )
    date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Fecha de ausencia",
    )
    is_all_day = models.BooleanField(
        default=False,
        verbose_name="¿La ausencia es todo el día?",
    )
    start_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="Hora inicial de ausencia",
    )
    end_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="Hora final de ausencia",
    )
    absence_type = models.CharField(
        max_length = 15,
        blank = True,
        null = True,
        choices = [
            ('general', 'Ausencia general'),
            ('bloq_schedule', 'Bloquear horario')
        ],
        verbose_name = 'Tipo de ausencia'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Ausencia"
        verbose_name_plural = "Ausencias"

    def __str__(self):
        return self.manager.username + " ausencia número " + str(self.id)
    
    def clean(self):
        if not self.is_all_day:
            if not (self.start_time and self.end_time):
                raise ValidationError(_("Si la ausencia no es todo el día, debe indicar hora de inicio y fin."), code="add_time_absence")
            if self.start_time >= self.end_time:
                raise ValidationError(_("La hora final debe ser mayor que la hora inicial."), code="end_time_greater")
            
            absence_all_day = Absence.objects.filter(manager=self.manager, date=self.date, is_all_day=True).exclude(pk=self.pk)
            absence_not_all_day = Absence.objects.filter(manager=self.manager, date=self.date, is_all_day=False).exclude(pk=self.pk).order_by("start_time")

            if absence_all_day.exists():
                raise ValidationError(_("Ya existe una ausencia para este gestor en esta fecha."), code="absence_exists")
            
            for i, absence in enumerate(absence_not_all_day):
                if self.start_time > absence.start_time:
                    if self.start_time < absence.end_time:
                        raise ValidationError(_("La hora inicial de la nueva ausencia se solapa con otra ausencia"), code="absence_exists_time_start")
                    if self.start_time >= absence.end_time:
                        if i == len(absence_not_all_day)-1:
                            break
                        else:
                            continue
                else:
                    if self.end_time > absence.start_time:
                        raise ValidationError(_("La hora final de la nueva ausencia se solapa con el inicio de otra ausencia"), code="absence_exists_end_time")
                    else:
                        break

        else:
            absence_all_day = Absence.objects.filter(manager=self.manager, date=self.date).exclude(pk=self.pk)
            
            if absence_all_day.exists():
                raise ValidationError(_("Ya existe una ausencia para este gestor en esta fecha."), code="absence_exists")
            
            self.start_time = None
            self.end_time = None
                
