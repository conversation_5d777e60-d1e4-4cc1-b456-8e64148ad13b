from django.db import models

class AccountExpenses(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=10,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=200,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Cuenta de Gastos"
        verbose_name_plural = "Cuentas de Gastos"
    
    def __str__(self):
        return self.description
    
# @admin.register(AccountExpenses)
# class AccountExpensesAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
