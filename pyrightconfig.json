{"include": ["."], "exclude": ["**/node_modules", "**/__pycache__", "**/.*", "**/venv", "**/.venv", "**/env", "**/.env", "**/migrations", "**/static", "**/media", "**/htmlcov", "**/.git", "**/build", "**/dist", "**/.pytest_cache", "**/.coverage", "**/logs", "**/docs"], "ignore": ["**/.history"], "defineConstant": {"DEBUG": true}, "reportMissingImports": true, "reportMissingTypeStubs": false, "pythonVersion": "3.10", "typeCheckingMode": "basic"}