class DropzoneMT {
    constructor(container, dropzoneId, formData = {}, options = {}) {
        this.container = container;
        this.dropzoneId = dropzoneId;
        this.formData = formData;
        this.options = {
            name: options.name || "Documento",
            namePlural: options.namePlural || "Documentos",
            dropzoneImg: options.dropzoneImg || "/static/assets/img/dropzone.svg",
            ...options
        };
        this.files = [];
        this.allowedExtensions = ["application/pdf", "image/jpeg", "image/png", "image/jpg"];

        this.init();
    }

    init() {
        this.render();
        this.attachEventListeners();
        this.initPerfectScrollbar();

        this.Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener("mouseenter", Swal.stopTimer);
                toast.addEventListener("mouseleave", Swal.resumeTimer);
            },
        });
    }

    render() {
        this.container.innerHTML = `
            <div>
                <div class="alert alert-warning alert-dismissible fade show d-none" role="alert" id="limitInvoiceAlertMessage_${this.dropzoneId}">
                    <strong>¡Atención!</strong> Has excedido el límite de documentos que puedes cargar. Por favor, elimina algunos o contacta a soporte para cambiar el límite.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <form id="uploadDropzoneForm_${this.dropzoneId}" action="${this.formData.url}" method="POST">
                    <input type="hidden" name="csrfmiddlewaretoken" value="${this.formData.csrfToken}">
                    <div class="row">
                        <div id="dropzoneContainer_${this.dropzoneId}" class="dropzone-wrapper">
                            <div id="customDropzone_${this.dropzoneId}" class="custom-dropzone upload">
                                <div class="upload-progress"></div>
                                <div class="dropzone-img-container">
                                    <img src="${this.options.dropzoneImg}" alt="Dropzone">
                                </div>
                                <h4 class="mt-2 mb-0 text-dark fw-bold">
                                    Arrastra & suelta 
                                    <span class="text-success">tus ${this.options.namePlural.toLowerCase()}</span>
                                </h4>
                                <p class="small mb-0 text-dark">o haz clic para agregarlas desde tu ordenador</p>
                                <input id="fileInput_${this.dropzoneId}" type="file" accept=".pdf,.jpg,.jpeg,.png" multiple class="d-none">
                                <input type="hidden" id="iae" name="iae" value="${this.formData.iae}"/>
                                <p class="mt-3 text-muted">
                                    <i class="bi bi-exclamation-triangle fw-bold text-warning">&nbsp;</i>
                                    <span class="fw-bolder text-dark">¡ATENCIÓN!</span> Solo puede subirse un/a ${this.options.name} por cada fichero. <span class="fw-bolder">¡No subir ficheros con varias ${this.options.namePlural}!</span></p>
                                <p class="text-success small">*Formatos aceptados: PDF, JPG, PNG</p>
                            </div>
                        </div>
                        
                        <div id="fileListContainer_${this.dropzoneId}" class="mb-3 file-list-wrapper">
                            <div class="file-list-grid">
                                <div id="fileCount_${this.dropzoneId}" class="mb-1 file-count text-start d-none">
                                    <i class="bi bi-file-earmark-text me-2"></i>
                                    <span></span>
                                    <span>|</span>
                                    <a href="#" class="text-danger btn-link" id="deleteAllFiles_${this.dropzoneId}">Eliminar todos</a>
                                </div>
                                <div id="fileList_${this.dropzoneId}" class="file-list scroll-div ps"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="mt-3 col-12 text-center">
                            <button id="submitDropzoneBtn_${this.dropzoneId}" type="submit" class="btn btn-navy lft-hover m-0 col-12 col-md-5 btn-submit-dropzone upload" disabled>
                                <div class="d-flex flex-row justify-content-center align-items-center upload-button-text">
                                    <i class="bi bi-cloud-upload me-2"></i>
                                    Procesar ${this.options.namePlural}
                                </div>
                                <div class="upload-hint">Procesando...</div>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        `;
        this.cacheDOMElements();
    }
    cacheDOMElements() {
        this.dropzoneContainer = this.container.querySelector(`#dropzoneContainer_${this.dropzoneId}`);
        this.dropzone = this.container.querySelector(`#customDropzone_${this.dropzoneId}`);
        this.fileInput = this.container.querySelector(`#fileInput_${this.dropzoneId}`);
        this.fileListContainer = this.container.querySelector(`#fileListContainer_${this.dropzoneId}`);
        this.fileListRows = this.container.querySelector(`#fileList_${this.dropzoneId}`);
        this.fileCount = this.container.querySelector(`#fileCount_${this.dropzoneId}`);
        this.submitDropzoneBtn = this.container.querySelector(`#submitDropzoneBtn_${this.dropzoneId}`);
        this.dropzoneForm = this.container.querySelector(`#uploadDropzoneForm_${this.dropzoneId}`);
        this.limitInvoiceAlertMessage = this.container.querySelector(`#limitInvoiceAlertMessage_${this.dropzoneId}`);
        this.deleteAllFiles = this.container.querySelector(`#deleteAllFiles_${this.dropzoneId}`);
        this.totalInvoices = this.options.totalInvoices;
        this.limitInvoices = this.options.limitInvoices || -1;
    }

    attachEventListeners() {
        this.dropzone.addEventListener('click', this.handleDropzoneClick.bind(this));
        this.dropzone.addEventListener('dragover', this.handleDragOver.bind(this));
        this.dropzone.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.dropzone.addEventListener('drop', this.handleFileDrop.bind(this));
        this.fileInput.addEventListener('change', this.handleFileInputChange.bind(this));
        this.dropzoneForm.addEventListener('submit', this.handleFormSubmit.bind(this));
        this.deleteAllFiles.addEventListener('click', this.removeAllFiles.bind(this));
    }

    updateTotalInvoices(newTotal) {
        this.totalInvoices = newTotal;
    }

    handleDropzoneClick(e) {
        if (e.target !== this.fileInput && !this.dropzone.classList.contains("disabled")) {
            this.fileInput.click();
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        if (this.dropzone.classList.contains("disabled")) return;

        this.dropzone.classList.add('custom-dropzone-dragover');
        this.dropzone.querySelector('.dropzone-img-container').classList.add('enlarge-img');
    }

    handleDragLeave(){
        if (this.dropzone.classList.contains("disabled")) return;

        this.dropzone.classList.remove('custom-dropzone-dragover');
        this.dropzone.querySelector('.dropzone-img-container').classList.remove('enlarge-img');
    }

    async handleFileDrop(e) {
        e.preventDefault();
        if (this.dropzone.classList.contains("disabled")) return;

        this.dropzone.classList.remove('custom-dropzone-dragover');
        this.dropzone.querySelector('.dropzone-img-container').classList.remove('enlarge-img');
        const droppedFiles = await this.filterAllowedFiles(e.dataTransfer.files);
        this.handleFilesUploaded(droppedFiles);
    }

    async handleFileInputChange(e) {
        const selectedFiles = await this.filterAllowedFiles(Array.from(e.target.files));
        this.handleFilesUploaded(selectedFiles); 
    }

    async filterAllowedFiles(files) {
        const validFiles = [];
        for (const file of files) {
            const isValidExtension = this.allowedExtensions.includes(file.type);
            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
            // const {isValidPages, isEncrypted} = await this._isValidPDF(file);

            if (!isValidExtension) {
                notifier.show("Formato no permitido!", `${file.name} no es un formato permitido`, "danger", "", 4000);
                continue;
            }

            if (!isValidSize) {
                notifier.show("Tamaño excedido!", `${file.name} excede el tamaño máximo de 10MB`, "danger", "", 4000);
                continue;
            }

            // if (isEncrypted) {
            //     notifier.show("PDF encriptado!", `${file.name} está encriptado y no puede ser procesado`, "danger", "", 4000);
            //     continue;
            // }

            // if (!isValidPages) {
            //     notifier.show("Páginas excedidas!", `${file.name} excede el número máximo de páginas permitidas`, "danger", "", 4000);
            //     continue;
            // }

            validFiles.push(file);
        }

        return validFiles;
    }

    handleFilesUploaded(newFiles) {
        const newFilesToAdd = newFiles.filter((file) => !this._isDuplicate(file));

        if (newFilesToAdd.length > 0) {
            this.files = [...this.files, ...newFilesToAdd];
        }
        this._updateDropzoneContainer();
        this._updateFileInput();
        this._updateSubmitButton();
        this._checkSellerLimitInvoiceUploader();
        setTimeout(() => {
            this._updateFileList();
            this._updateFileCount();
        }, 310);

    }

    removeFile(index) {
        this.files.splice(index, 1);
        this._updateDropzoneContainer();
        this._updateFileInput();
        this._updateSubmitButton();
        this._updateFileCount();
        this._updateFileList();
        this._checkSellerLimitInvoiceUploader();
    }

    removeAllFiles() {
        this.files = [];
        this._updateDropzoneContainer();
        this._updateFileInput();
        this._updateSubmitButton();
        this._updateFileCount();
        this._updateFileList();
        this._checkSellerLimitInvoiceUploader();
    }
    
    async _isValidPDF(file) {
        if (file.type === "application/pdf") {

            try{
                const arrayBuffer = await file.arrayBuffer();
                const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                const pageCount = pdfDoc.getPageCount();
                return { isValidPages: pageCount <= 10, isEncrypted: false };
            } catch (error) {
                if (error.message.includes("encrypted")) {
                    return { isValidPages: false, isEncrypted: true };
                }
                
            }
        }
        return { isValidPages: true, isEncrypted: false };
    }

    _isDuplicate(file) {
        const _isDuplicateFile = this.files.some(
            (f) => f.name === file.name && f.size === file.size && f.type === file.type
        );
        if (_isDuplicateFile) {
            notifier.show("Duplicado!", `${file.name} ya ha sido cargado`, "warning", "", 4000);
        }
        return _isDuplicateFile;
    }

    _updateFileInput() {
        const dataTransfer = new DataTransfer();

        this.files.forEach((file) => dataTransfer.items.add(file));

        this.fileInput.files = dataTransfer.files;
    }

    _updateFileList() {
        this.fileListRows.innerHTML = this.files
            .map(
                (file, index) => {
                    const fileSizeKB = (file.size / 1024).toFixed(2);
                    return `
                        <div class="file-list-row">
                            <div class="d-flex align-items-center">
                                <div class="file-icon text-primary">
                                    <i class="bi ${this._getFileIcon(file.type)}"></i>
                                </div>
                                <div class=" file-info flex-grow-1 ms-2">
                                    <div class="text-truncate text-dark fw-bold">${file.name}</div>
                                    <div class="text-muted small">${fileSizeKB} kb</div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-icon btn-dlt-file btn-outline-danger" aria-label="Eliminar archivo">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    `;
                }
            )
            .join("");

        this.fileListRows.querySelectorAll(".btn-dlt-file").forEach((btn, index) => {
            btn.addEventListener("click", () => this.removeFile(index));
        });

    }

    _updateFileCount() {
        if (this.files.length > 0) {
            this.fileCount.classList.remove("d-none");
            this.fileCount.querySelector("span").textContent = `${this.files.length} ${this.files.length === 1
                    ? "archivo cargado y listo para procesar"
                    : "archivos cargados y listos para procesar"
                }`;
        } 
        else {
            this.fileCount.classList.add("d-none");
        }
    }

    _updateSubmitButton() {
        if (this.files.length === 0) {
            this.submitDropzoneBtn.disabled = true;
        } else {
            if (this.formData.userRole == "seller") {
                this.submitDropzoneBtn.disabled = this.files.length + this.totalInvoices > this.limitInvoices && this.limitInvoices > 0;
                return;
            } 
            this.submitDropzoneBtn.disabled = false;
        }
    }

    _updateDropzoneContainer() {
        if (this.files.length > 0) {
            this.dropzoneContainer.classList.add("col-12", "col-xl-6");
            this.fileListContainer.classList.add("col-12", "col-xl-6", "files-added");
            // this.fileListContainer.scrollIntoView({ behavior: "smooth" });
        } else {
            this.dropzoneContainer.classList.remove("col-12", "col-xl-6");
            this.fileListContainer.classList.remove("col-12", "col-xl-6", "files-added");
        }
    }

    _getFileIcon(fileType) {
        switch (fileType) {
            case "application/pdf":
                return "bi-filetype-pdf text-danger";
            case "image/jpeg":
                return "bi-filetype-jpg text-success";
            case "image/png":
                return "bi-filetype-png text-warning";
            default:
                return "bi-file-earmark";
        }
    }

    _startUploadAnimation() {
        const dltFileBtns = document.querySelectorAll(".btn-dlt-file");
        dltFileBtns.forEach((btn) => btn.disabled = true);

        this.submitDropzoneBtn.disabled = true;
        this.dropzone.classList.add("disabled");
        this.fileInput.disabled = true;
        this.submitDropzoneBtn.classList.add("uploading");
        this.dropzone.classList.add("uploading");
    }

    _stopUploadAnimation() {
        const dltFileBtns = document.querySelectorAll(".btn-dlt-file");
        dltFileBtns.forEach((btn) => btn.disabled = false);

        this.submitDropzoneBtn.classList.remove("uploading");
        this.dropzone.classList.remove("uploading");
        this.dropzone.classList.remove("disabled");
        this.fileInput.disabled = false;
    }

    _checkSellerLimitInvoiceUploader() {
        if (this.formData.userRole == "seller") {
            if (this.files.length + this.totalInvoices > this.limitInvoices && this.limitInvoices > 0) {
                this.limitInvoiceAlertMessage.classList.remove("d-none");
            } else {
                this.limitInvoiceAlertMessage.classList.add("d-none");
            }
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        this._startUploadAnimation();

        const formData = new FormData(e.target);
        this.files.forEach((file) => formData.append("files", file));

        try {
            await new Promise(resolve => setTimeout(resolve, 500)); // Pause for 0.5 seconds
            const response = await fetch(e.target.action, {
                method: "POST",
                body: formData,
                headers: {
                    "X-CSRFToken": this.formData.csrfToken,
                },
            });

            if (response.ok) {
                const {data} = await response.json();
    
                this._stopUploadAnimation();
                this.files = [];
                this._updateFileInput();
                this._updateDropzoneContainer();
                this._updateFileCount();
                this._updateFileList();

                this.Toast.fire({
                    icon: "success",
                    title: `Se han procesado ${data.invoices_created} ${this.options.name.toLowerCase()} exitosamente.`,
                });

                const successEvent = new CustomEvent("dropzoneMTSuccess", { 
                    detail: {
                        dropzoneId: this.dropzoneId,
                        data: data,
                    },
                });
                this.container.dispatchEvent(successEvent);
            }
            else {
                this._stopUploadAnimation();
                this._updateSubmitButton();

                if (response.status == 413){
                    Swal.fire({
                        icon: "warning",
                        title: "Límite de facturas excedido",
                        text: `No se han cargado las facturas porque has excedido el límite de facturas que puedes cargar.`,
                        confirmButtonText: "Entendido",
                        confirmButtonColor: "#0d6efd",
                    });
                    return;
                }

                this.Toast.fire({
                    icon: "error",
                    title: "Ocurrió un error al procesar las facturas. Por favor, inténtalo de nuevo.",
                });
            }

        } catch (error) {
            console.error("Error:", error);
            
        }
        this._stopUploadAnimation();
        this._updateSubmitButton();
    }

    initPerfectScrollbar() {
        new PerfectScrollbar(this.fileListRows, {
            wheelSpeed: 0.5,
            swipeEasing: true,
            suppressScrollX: true,
            wheelPropagation: true,
            minScrollbarLength: 40,
        });
    }

}