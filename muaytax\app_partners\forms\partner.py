from datetime import datetime, timedelta

from django.core.exceptions import ValidationError
from django.utils import timezone

from django import forms
from django.utils.translation import gettext_lazy as _

from muaytax.app_address.models.address import Address
from muaytax.app_partners.models.partner import Partner
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.province_code import ProvinceCode

# PRESENT_YEAR = datetime.now().year - 1

RADIOSELECT_KWARGS = {
    'attrs': {"required": "true"},
    'choices': ((True, _('Sí')), (False, _('No')))
}

DATEINPUT_KWARGS = {
    'attrs': {"type": "date", 
            #   "min": f"{PRESENT_YEAR}-01-01", "max": f"{PRESENT_YEAR}-12-31", 
            # "readonly": "true"
              },
    'format': "%Y-%m-%d"
}

class PartnerChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label=_("Dirección"))
    address_number = forms.CharField(max_length=10, required=False, label=_("Número"))
    address_continue = forms.CharField(max_length=100, required=False, label=_("Dirección (Continuación)"))
    address_zip = forms.CharField(max_length=10, label=_("Código Postal"))
    address_city = forms.CharField(max_length=50, required=False, label=_("Ciudad"))
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label=_("País"))
    address_catastral = forms.CharField(max_length=100, required=False, label=_("Referencia Catastral"),
                                        help_text=_("Solo aplica a España"))

    class Meta:
        model = Partner
        fields = "__all__"
        exclude = ["seller", "partner_address", "address"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        partner = kwargs.get('instance')
        if partner and partner.address:
            address = partner.address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country
            self.fields['address_catastral'].initial = address.address_catastral
        elif partner:
            if partner.zip:
                self.fields['address_zip'].initial = partner.zip
            if partner.country:
                self.fields['address_country'].initial = partner.country

    def save(self, commit=True):
        partner = super().save(commit=False)
        

        partner.shares_percentage_m184 = self.cleaned_data['shares_percentage']
        address = partner.address

        if (address is None):
            address = Address()
            partner.address = address

        address.address_name = f"Direccion Proveedor {partner.pk}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.address_catastral = self.cleaned_data['address_catastral']
        address.save()

        if commit:
            pk = partner.pk
            partner.save()
            if (pk is None):
                pk = partner.pk
                address.address_name = f"Direccion Socio {pk}"
                address.save()

        return partner

class PartnerDeleteForm(forms.Form):
    class Meta:
        model = Partner
        exclude = ["seller"]

class PartnerM184Form(forms.ModelForm):
    id_number = forms.CharField(
        max_length=9,
        label=_("DNI o NIE"),
        error_messages={"unique": _("Ya existe un miembro con este DNI o NIE")},
    )
    legal_representative = forms.CharField(
        required=False,
        max_length=9,
        label=_("NIF del representante"),
    )
    # Campos de dirección
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label=_("País"))
    address_address = forms.CharField(max_length=100, label=_("Dirección"))
    address_city = forms.CharField(max_length=50, label=_("Ciudad"))
    address_state = forms.CharField(max_length=50, label=_("Estado / Provincia / Región"))
    address_zip = forms.CharField(max_length=10, label=_("Código Postal"))
    fiscal_province = forms.ModelChoiceField(
        queryset=ProvinceCode.objects.all(),
        label=_("Provincia de residencia fiscal")
    )

    def clean_legal_representative(self):
        data = self.cleaned_data['partner_type']
        legal_representative = self.cleaned_data['legal_representative']
        if data.code == 'legal' and legal_representative is None:
            raise forms.ValidationError(_("Este campo es requerido."))
        return legal_representative

    class Meta:
        model = Partner
        fields = [
            "name", "last_name", "id_number", "partner_type", "legal_representative",
            "shares_percentage", "fiscal_province", "address_address", "address_country","address_city", "address_state", "address_zip",
            "days_member", "start_date", "end_date"
        ]
        widgets = {
            "days_member": forms.NumberInput(attrs={"min": "0", "max": "365", "readonly": "true"}),
            "shares_percentage": forms.NumberInput(attrs={"value": "1.00", "min": "1", "max": "100", "step": "0.01"}),
            "start_date": forms.DateInput(attrs={"type": "date"}, format="%Y-%m-%d"),
            "end_date": forms.DateInput(attrs={"type": "date", "readonly": "true"}, format="%Y-%m-%d"),
            "name": forms.TextInput(attrs={"id": "id_partner_name"}),
            # "legal_representative": forms.TextInput(attrs={"readonly": "true"}),
        }
        labels = {
            "shares_percentage": _("Porcentaje de participación"),
            "partner_type": _("Tipo de miembro"),
            "days_member": _("Días que ha sido miembro"),
        }

    def clean_legal_representative(self):
        partner_type = self.cleaned_data['partner_type']
        data = self.cleaned_data['legal_representative']
        if partner_type.code == 'legal' and (data is None or data == ''):
            raise forms.ValidationError(_("Este campo es requerido."))
        return data

    def __init__(self, *args, **kwargs):
        required = kwargs.pop('required', False)
        m_form = kwargs.pop('m_form', None)
        processed_form = kwargs.pop('processed_form', None)
        super().__init__(*args, **kwargs)

        not_required_fields = ['legal_representative', 'start_date', 'end_date']

        for field_name, field in self.fields.items():
            field.disabled = m_form and m_form.is_processed
            field.required = True if field_name not in not_required_fields else False
            if field.required:
                field.widget.attrs['required'] = 'required'
        
        partner = kwargs.get('instance')

        if partner and partner.address:
            address = partner.address
            self.fields['address_country'].initial = address.address_country
            self.fields['address_address'].initial = address.address
            self.fields['address_city'].initial = address.address_city
            self.fields['address_state'].initial = address.address_state
            self.fields['address_zip'].initial = address.address_zip

            
    def save(self, commit=True):
        partner = super().save(commit=False)
        partner.shares_percentage_m184 = self.cleaned_data['shares_percentage'] #Copia el valor del % de participación al % de participación en el modelo 184

        address = partner.address

        if address is None:
            address = Address()
            partner.address = address

        address.address_name = f"Direccion Socio {partner.pk}"
        address.address = self.cleaned_data['address_address']
        address.address_country = self.cleaned_data['address_country']
        address.address_city = self.cleaned_data['address_city']
        address.address_state = self.cleaned_data['address_state']
        address.address_zip = self.cleaned_data['address_zip']
        address.save()

        if commit:
            pk = partner.pk
            partner.save()
            if pk is None:
                pk = partner.pk
                address.address_name = f"Direccion Socio {pk}"
                address.save()

        return partner

class PartnerFlatRateINSSForm(forms.ModelForm):
    class Meta:
        model = Partner
        fields = ['sl_flat_rate_inss_start_date']
        labels = {
            'sl_flat_rate_inss_start_date': 'Fecha de inscripción a tarifa plana',
        }
        widgets = {
            'sl_flat_rate_inss_start_date': forms.DateInput(
                attrs={'type': 'date', 'class': 'form-control'},
                format='%Y-%m-%d' # Formato de fecha esperado
            )
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.sl_flat_rate_inss_start_date:
            self.initial['sl_flat_rate_inss_start_date'] = self.instance.sl_flat_rate_inss_start_date.strftime('%Y-%m-%d')
