#!/bin/bash

# Ruta base del proyecto (aj<PERSON>tala si estás fuera de la raíz)
BASE_DIR="muaytax/templates/sellers/include/service_iva/navTabs"

# Crear directorio si no existe
mkdir -p "$BASE_DIR"

# Crear archivos individuales
cat <<EOF > "$BASE_DIR/navTabs_general.html"
<li class="nav-item">
    <a class="nav-link active" id="general-tab" data-bs-toggle="tab" href="#general" role="tab"
    aria-controls="general" aria-selected="true" onclick="changeTab('general-tab')"
    style="display: flex; align-items: center;">
        <i class="feather icon-alert-circle hidden" style="color: #dc3545; margin-right: 5px;"></i>
        {% include "sellers/include/service_iva/navTabs/navTabs_icons.html" with icon="info" %}
        <span class="d-none d-md-inline td_font_country">&nbsp;Información General</span>
    </a>
</li>
EOF

cat <<EOF > "$BASE_DIR/navTabs_members.html"
<li class="nav-item">
    <a class="nav-link" id="members-tab" data-bs-toggle="tab" href="#members" role="tab"
    aria-controls="members" aria-selected="false" onclick="changeTab('members-tab')"
    style="display: flex; align-items: center;">
        <i class="feather icon-alert-circle hidden" style="color: #dc3545; margin-right: 5px;"></i>
        {% include "sellers/include/service_iva/navTabs/navTabs_icons.html" with icon="members" %}
        <span class="d-none d-md-inline td_font_country">&nbsp;Socio/s</span>
    </a>
</li>
EOF

cat <<EOF > "$BASE_DIR/navTabs_documents.html"
<li class="nav-item">
    <a class="nav-link" id="documents-tab" data-bs-toggle="tab" href="#documents" role="tab"
    aria-controls="documents" aria-selected="false" onclick="changeTab('documents-tab')"
    style="display: flex; align-items: center;">
        <i class="feather icon-alert-circle hidden" style="color: #dc3545; margin-right: 5px;"></i>
        {% include "sellers/include/service_iva/navTabs/navTabs_icons.html" with icon="documents" %}
        <span class="d-none d-md-inline td_font_country">&nbsp;Documentos por País</span>
    </a>
</li>
EOF

cat <<EOF > "$BASE_DIR/navTabs_summary.html"
<li class="nav-item">
    <a class="nav-link" id="summary-tab" data-bs-toggle="tab" href="#summary" role="tab"
    aria-controls="summary" aria-selected="false" onclick="changeTab('summary-tab')"
    style="display: flex; align-items: center;">
        <i class="feather icon-alert-circle hidden" style="color: #dc3545; margin-right: 5px;"></i>
        {% include "sellers/include/service_iva/navTabs/navTabs_icons.html" with icon="summary" %}
        <span class="d-none d-md-inline td_font_country">&nbsp;Resumen y Finalizar</span>
    </a>
</li>
EOF

cat <<EOF > "$BASE_DIR/navTabs_icons.html"
{% comment %} Aquí defines los SVG según el parámetro 'icon' {% endcomment %}
{% if icon == "info" %}
<i class="align_incons">
    <!-- SVG de Información General -->
</i>
{% elif icon == "members" %}
<i class="align_incons">
    <!-- SVG de Miembros -->
</i>
{% elif icon == "documents" %}
<i class="align_incons">
    <!-- SVG de Documentos -->
</i>
{% elif icon == "summary" %}
<i class="align_incons">
    <!-- SVG de Resumen -->
</i>
{% endif %}
EOF

echo "✅ Archivos creados en $BASE_DIR"

# Como ejecutarlo:
# 1. Guarda este script en un archivo docs/helpers/createhtml_shell.sh
# 2. Dale permisos de ejecución: chmod +x docs/helpers/createhtml_shell.sh
# 3. Ejecuta el script: ./docs/helpers/createhtml_shell.sh
