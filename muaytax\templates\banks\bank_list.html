{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %} Bancos {% endblock title %}

{% block stylesheets %}
  <!-- Archivos CSS y scripts estáticos necesarios -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
  <script type="module" src="{{ STATIC_URL }}assets/js/plugins/multi-checkbox.js"></script>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">

  <!-- Estilos específicos de DataTables -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">

  <!-- Estilos de Select2 -->
  <link href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2.min-v4.1.0.css" rel="stylesheet" />
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2-bootstrap-5-theme.min-v5.css" />

  <style>
    .card-carousel {
        overflow: hidden;
        position: relative;
    }

    .card-container {
        display: flex;
        transition: transform 0.3s ease-in-out;
    }

    .card {
        flex: 0 0 calc(390px);
        margin-right: 20px;
        padding: 0px;
    }

    .carousel-controls {
        margin-top: 10px;
        text-align: center;
    }

    .carousel-prev,
    .carousel-next {
        {% comment %}
        background-color: #f4f7fa;
        color: #fff;
        border: none;
        padding: 5px 10px;
        cursor: pointer;
        {% endcomment %}
    }

    .carousel-prev:hover,
    .carousel-next:hover {
        {% comment %}
        background-color: rgb(25, 135, 84);
        {% endcomment %}
    }

    #arrow-left-icon:hover,
    #arrow-right-icon:hover {
      text-shadow: 0 0 10px RGB(0, 200, 0);
    }

    .modal-custom {
      max-width: 55%;
      width: 55%;
      max-height: 100vh;
      margin: auto;
      left: 0;
      right: 0;
      align-items: center;
      justify-content: center;
    }

    /* Estilo para asegurar que SweetAlert aparezca por encima de los modales de Bootstrap */
    .swal-higher-z-index {
      z-index: 2000 !important;
    }

    /* Estilo para el overlay de SweetAlert */
    .swal-overlay-z-index {
      z-index: 2000 !important;
    }

    /* Asegurar que SweetAlert siempre aparezca por encima de los modales de Bootstrap */
    .swal2-container {
      z-index: 2000 !important;
    }

    /* Estilo para los botones de acción en la tabla de reglas */
    #bankRulesTable .btn-sm {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }

    .modal-custom .modal-dialog {
      max-width: 100%; /* Permite que el contenido ocupe el 100% del ancho del modal */
      width: 100%; /* Permite que el contenido ocupe el 100% del ancho del modal */
      margin-top: 0;
      margin-bottom: 0;
      margin-right: 0;
      margin-left: 0;
    }

    .modal-custom .modal-content {
      max-width: 100%;
      width: 100%;
      max-height: 100%;
      height: 100%;
      {% comment %} overflow-y: auto; /* Agrega una barra de desplazamiento vertical si el contenido es más largo que la altura del modal */ {% endcomment %}
    }

    .border-double {
      border-style: double;
    }

    .text-limit-concept {
      display: block;
      width: 18vw;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .search-container {
      display: inline-block;
      position: relative;
      transition: width 0.4s ease-in-out;
    }
    .search-container > input {
      font-size: 12px;
    }

    /* Lupa dentro del input */
    .search-icon {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 1.5em;
      color: #ccc;
      margin-right: 0px;
      pointer-events: none; /* No permite hacer clic en la lupa */
    }

    /* Cuando el input tiene texto, oculta la lupa */
    #search-input:not(:placeholder-shown) + .search-icon {
      display: none; /* Oculta la lupa cuando se escribe */
    }

    /* Asegura que la columna de los checkboxes sea de un ancho fijo */
    .select-checkbox-column {
      width: 20px !important; /* Puedes ajustar este valor según lo necesites */
      text-align: center; /* Centra horizontalmente */
      vertical-align: middle; /* Centra verticalmente */
    }

    /* multiselectors style | START */
    multi-month {
      /* Element */
      --mc-z-index: 8 !important;
      --mc-cont-z-index: 20 !important;
      --mc-border: 1px solid #ced4da;;
      --mc-display: inline-block;
      --mc-font: 14px !important;
      --mc-margin: 0;
      --mc-vertical-align: middle;

      /* Dropdown */
      --mc-dropdown-background: #fff;
      --mc-dropdown-box-shadow: 0 1px 2px 0 rgba(57, 70, 92, .05);
      --mc-dropdown-max-height: 350px;
      --mc-dropdown-text-align: left;
      --mc-dropdown-width: 100%;

      /* Input */
      --mc-target-background: #fff;
      --mc-target-color: #000000;
      --mc-target-cursor: pointer;
      --mc-target-margin: 0;
      --mc-target-padding: 0px 0px 0px 3px;
      --mc-target-outline: none;
      --mc-vertical-align: middle;
      --mc-target-width: 268px;

      /* Dropdown Line Items */
      --mc-dropdown-line-height: 2em;
      --mc-ul-margin: 0;
      --mc-ul-padding: 0;
      --mc-checkbox-height: 30px;
      --mc-checkbox-width: 20px;
      --mc-li-cursor: pointer;

      /* Toggle Button */
      --mc-toggle-button-background: #ffffff;
      --mc-toggle-button-color: #000000;
      --mc-toggle-button-cursor: pointer;
      --mc-toggle-button-height: 46px;
      --mc-toggle-button-outline: none;
      --mc-toggle-button-width: 23px;

      /* Close Button */
      --mc-close-button-background: #efefef;
      --mc-close-button-border: none;
      --mc-close-button-border-radius: default;
      --mc-close-button-color: #000000;
      --mc-close-button-cursor: pointer;
      --mc-close-button-display: block;
      --mc-close-button-height: 22px;
      --mc-close-button-margin: 5px auto;
      --mc-close-button-outline: none;
      --mc-close-button-width: 22px;
    }

    #multi-value:hover {
      background-color: #0d6efd;
      --mc-dropdown-color: #fff;
    }

    #multi-value:hover div {
      color: #fff;
    }
    /* multiselectors style | END */

    .list-action-btn-block{
      position: relative;
    }
    .list-action-btn-block > *{
      font-size: 12px;
      margin-bottom: unset!important;
      margin-right: unset!important;
    }
    .list-action-btn-block button{
      min-width: max-content;
    }
    .dropdown-form {
      display: block;
      min-width: 500px;
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      padding: 10px;
      z-index: 1000;
      transform: translate(-70%, 10px);
      opacity: 0;
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    .btn.dropdown-toggle:after {
      display: none
    }
    .top-right-badge{
      position: absolute!important;
      top: -10px!important;
      right: -5px!important;
      width: 25px!important;
      height: 25px!important;
      align-items: center;
      display: flex;
      justify-content: center;
    }

    .card-balance {
      position: absolute; /* Permite colocar el saldo en una posición específica */
      bottom: 10px; /* Ajusta la distancia desde la parte inferior de la tarjeta */
      left: 10px; /* Ajusta la distancia desde el borde izquierdo */
      background-color: rgba(0, 0, 0, 0.7); /* Fondo semitransparente para mejor visibilidad */
      color: #ffffff; /* Texto blanco para buen contraste */
      padding: 5px 10px; /* Espaciado interno para que el texto no toque los bordes */
      border-radius: 5px; /* Bordes redondeados para un estilo moderno */
      font-size: 14px; /* Tamaño de fuente ajustado */
      font-weight: bold; /* Fuente en negrita para resaltar el mensaje */
      z-index: 10; /* Asegura que el saldo quede visible sobre otros elementos */
      text-align: center; /* Centra el texto dentro del bloque */
    }

    table.dataTable thead th {
      background-color: #f8f9fa; /* Cambia el color según tu diseño */
      color: #212529;
      border-bottom: 1px solid #dee2e6; /* Opcional, para estilo */
      position: sticky; /* Mantiene fijo */
      top: 0; /* Fija la posición superior */
      z-index: 10; /* Por si otros elementos interfieren */
    }

    .btn-danger {
      background-color: #ff4d4f;
      border-color: #ff4d4f;
      color: #fff;
    }

    .btn-danger:hover {
        background-color: #ff7875;
        border-color: #ff7875;
    }

    /* Estilo para el cursor al arrastrar filas */
    .cursor-move {
        cursor: move;
    }

    /* Estilo para la fila que se está arrastrando */
    .sortable-ghost {
        background-color: #f8f9fa;
        opacity: 0.5;
    }
  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col">
                    <div class="page-header-title">
                        <h5 class="m-b-10">Bancos</h5>
                    </div>
                    <ul class="breadcrumb">
                        {% if user.role == 'manager' %}
                            <li class="breadcrumb-item">
                                <a href="{% url 'home' %}">
                                    <i class="feather icon-home"></i>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{% url 'app_sellers:summary' seller.shortname %}">
                                    {{ seller.name }}
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href=".">Bancos</a>
                            </li>
                        {% else %}
                            <li class="breadcrumb-item">
                                <a href="{% url 'home' %}">
                                    <i class="feather icon-home"></i>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href=".">Bancos</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div class="" id="bank_carrusel">
        <div class="row">
            <!-- Arrow Left -->
            <div class="d-flex align-items-center justify-content-center mx-1" style="width:5%">
                <button class="btn btn-link carousel-prev" style="opacity:0.15;" id="arrow-left" onclick="onClickPrev()" disabled>
                <h2> <i class="fa-solid fa-circle-chevron-left text-secondary" id="arrow-left-icon"></i> </h2>
                </button>
            </div>
            <!-- Cards -->
            <div class="col" style="min-width: 0;">
                <div class="card-carousel">
                    <div class="card-container">
                        <!-- BANK CARDS -->
                        {% for bank in banks %}
                            {% if bank.bank_name is not None %}
                                <div class="card" id="bank_{{bank.id}}" onclick="onClickCardBank('{{bank.id}}')"
                                {% if bank.bank_name|lower == 'accounts' and user.role != 'manager' %}
                                    style="display:none;"
                                {% endif %}
                                >
                                <div
                                    {% if bank.bank_name|lower == 'amazon' %}
                                    class="card-body text-white" style="background-color: #ff9900d6;"
                                    {% elif bank.bank_name|lower == 'accounts' %}
                                    class="card-body text-white" style="background-color: #006affcc;"
                                    {% else %}
                                    class="card-body bg-secondary bg-gradient text-white"
                                    {% endif %}
                                >
                                    <!-- BANK INFO -->
                                    <div class="row">
                                    <div class="d-flex px-0 mx-0" style="width:auto; max-width:calc(100% - 130px);">
                                        &nbsp; &nbsp;
                                        <h4 class="card-title">
                                        <span class="text-white" style="display:inline-block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 240px;">
                                            <b> {{bank.bank_name}} </b>
                                        </span>
                                        </h4>
                                    </div>
                                    <div class="d-flex justify-content-start px-0 mx-0" style="width:130px;">
                                        &nbsp;&nbsp;
                                        <h4>
                                        <span class="text-white">
                                            <b>
                                            {% if bank.bank_name is not None and bank.bank_name|lower != 'amazon'%}
                                                {% if bank.bank_account_type.code == 'bankaccount' and bank.bank_iban is not None %}
                                                ({{bank.bank_iban|slice:"-4:"}})
                                                {% elif bank.bank_account_type.code == 'creditcard' and bank_credit_card is not None %}
                                                ({{bank.bank_credit_card|slice:"-4:"}})
                                                {% endif %}
                                            {% endif %}
                                            </b>
                                        </span>
                                        </h4>
                                        &nbsp;&nbsp;
                                        {% if bank.bank_account_type.code == 'bankaccount'%}
                                        <span class="text-white" data-bs-toggle="tooltip" data-bs-placement="top" title="Cuenta Bancaria">
                                            <b> <i class="fa-solid fa-lg fa-building-columns"></i> </b>
                                        </span>
                                        {% elif bank.bank_account_type.code == 'creditcard' %}
                                        <span class="text-white" data-bs-toggle="tooltip" data-bs-placement="top" title="Tarjeta de Credito">
                                            <b> <i class="fa-solid fa-lg fa-credit-card"></i> </b>
                                        </span>
                                        {% endif %}
                                        <div id="bank_{{bank.id}}_edit" style="display:none;">
                                        {% if bank.bank_name is not None and bank.bank_name|lower != 'amazon' and bank.bank_name|lower != 'accounts'%}
                                        &nbsp;
                                        <a href="./{{bank.id}}/edit/">
                                            <span class="text-white" data-bs-toggle="tooltip" data-bs-placement="top" title="Editar">
                                            <b> <i class="fa-solid fa-lg fa-pen-to-square"></i> </b>
                                            </span>
                                        </a>
                                        {% endif %}
                                        </div>
                                    </div>
                                    </div>
                                </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                        <!-- NEW CARD -->
                        <div class="card">
                            <div class="card-body bg-secondary bg-gradient text-white">
                                <!-- TITLE -->
                                <div class="row">
                                    <div class="d-flex px-0 mx-0" style="width:auto; max-width:calc(100% - 100px);">
                                    <h5 class="card-title mb-4">
                                        <span class="text-white" style="display:inline-block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 270px;">
                                        &nbsp; <b>Crear Cuenta Nueva</b>
                                        </span>
                                    </h5>
                                    </div>
                                </div>
                                <!-- BUTTONS -->
                                <div class="" style="height: 70%;">
                                    <button class="btn btn-success w-100 h-50" onclick="onClickNewBank('bankaccount')">Añadir Cuenta Bancaria &nbsp; <i class="fa-solid fa-building-columns"></i> <i class="fa-solid fa-plus"></i></button>
                                    <button class="btn btn-success w-100 h-50" onclick="onClickNewBank('creditcard')">Añadir Tarjeta Credito &nbsp; &nbsp; <i class="fa-solid fa-credit-card"></i> <i class="fa-solid fa-plus"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Arrow Right -->
            <div class="d-flex align-items-center justify-content-center mx-1" style="width:5%">
                <button class="btn btn-link carousel-next" style="opacity:0.15;" id="arrow-right" onclick="onClickNext()" disabled>
                    <h2><i class="fa-solid fa-circle-chevron-right text-secondary" id="arrow-right-icon"></i></h2>
                </button>
            </div>
        </div>
    </div>
    <div class="row align-items-center" id="movements">
        {% for bank in banks %}
        <div class="col-12" style="display:none;" id="move_bank_{{ bank.id }}">
            <div class="row">
            <div class="col">
                <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">

                <!-- Bank Title -->
                <div class="d-flex align-items-center">
                    <h3 class="mb-0">
                    {% if bank.bank_account_type.code == 'bankaccount' %}
                        <span data-bs-toggle="tooltip" data-bs-placement="top" title="Cuenta Bancaria">
                            <i class="fa-solid fa-lg fa-building-columns"></i>
                        </span>
                    {% elif bank.bank_account_type.code == 'creditcard' %}
                        <span data-bs-toggle="tooltip" data-bs-placement="top" title="Tarjeta de Crédito">
                            <i class="fa-solid fa-lg fa-credit-card"></i>
                        </span>
                    {% endif %}
                    <span> {{ bank.bank_name }} </span>
                    {% if bank.bank_iban is not None and bank.bank_iban|length > 4 %}
                        <span class="">( {{ bank.bank_iban|slice:"-4:" }} )</span>
                    {% endif %}
                    <span id="title_table_{{ bank.pk }}"> - Movimientos </span>
                    </h3>
                </div>

                <!-- Search Input -->
                <div class="list-action-btn-block flex-fill d-flex justify-content-end">
                    <div class="search-container">
                    <input class="form-control search-input" type="search" id="search-input-{{ bank.id }}" name="search" placeholder="buscar..." data-bank-id="{{ bank.id }}">
                    <i class="fa fa-search search-icon"></i>
                    </div>
                </div>

                <!-- Filtro de selección múltiple -->
                <div class="list-action-btn-block">
                    <button
                    id="dropdownButton-{{ bank.id }}" class="btn btn-dark"
                    type="button" title="Filtros">
                    <i class="mdi mdi-filter-outline fa-xl me-0"></i>
                    <span class="badge top-right-badge rounded-pill bg-danger d-none" id="id-filter-notification-{{ bank.id }}">2</span>
                    </button>
                    <div id="dropdownFiltersForm-{{ bank.id }}" class="dropdown-form shadow p-3" style="display: none;">
                    <form id="filtersFormID-{{ bank.id }}">
                        <div class="row mb-2">
                        <!-- Filtro por Año -->
                        <div class="col-6 mb-3">
                            <label for="year-{{ bank.id }}" class="mb-1">Año</label>
                            <select class="form-control form-select" name="year" id="year-{{ bank.id }}" onchange="onChangeYear('{{ bank.id }}');">
                            <option value="">Todos los años</option>
                            {% for year in years_list %}
                                <option value="{{ year }}">{{ year }}</option>
                            {% endfor %}
                            </select>
                        </div>
                        <!-- Filtro de meses -->
                        <div class="col-6 mb-3">
                            <label for="multiple-month-{{ bank.id }}" class="mb-1">Meses</label>
                            <multi-month separator=", " value="" id="multiple-month-{{ bank.id }}" disabled>
                            <ul slot="check-values">
                                <li class="cursor-default" id="multi-value" value="1" multi-title="Enero" type="checkbox">Enero</li>
                                <li class="cursor-default" id="multi-value" value="2" multi-title="Febrero">Febrero</li>
                                <li class="cursor-default" id="multi-value" value="3" multi-title="Marzo">Marzo</li>
                                <li class="cursor-default" id="multi-value" value="4" multi-title="Abril">Abril</li>
                                <li class="cursor-default" id="multi-value" value="5" multi-title="Mayo">Mayo</li>
                                <li class="cursor-default" id="multi-value" value="6" multi-title="Junio">Junio</li>
                                <li class="cursor-default" id="multi-value" value="7" multi-title="Julio">Julio</li>
                                <li class="cursor-default" id="multi-value" value="8" multi-title="Agosto">Agosto</li>
                                <li class="cursor-default" id="multi-value" value="9" multi-title="Septiembre">Septiembre</li>
                                <li class="cursor-default" id="multi-value" value="10" multi-title="Octubre">Octubre</li>
                                <li class="cursor-default" id="multi-value" value="11" multi-title="Noviembre">Noviembre</li>
                                <li class="cursor-default" id="multi-value" value="12" multi-title="Diciembre">Diciembre</li>
                            </ul>
                            </multi-month>
                        </div>
                        </div>
                        <hr>
                        <div class="row">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-light" id="clearFiltersButton-{{ bank.id }}" onclick="resetFilters('{{ bank.id }}')" disabled>
                            Limpiar
                            </button>
                            <button type="button" id="applyFiltersButton-{{ bank.id }}" class="btn btn-dark" onclick="applyFilters('{{ bank.id }}')">
                            Aplicar
                            </button>
                        </div>
                        </div>
                    </form>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    <div id="change_conciliate_{{ bank.pk }}" style="display:block;">
                    <button class="btn btn-warning" onclick="onClickChangeMode('conciliate')">
                        Ver Conciliación
                    </button>
                    </div>
                    <div id="change_movements_{{ bank.pk }}" style="display:none;">
                    <button class="btn btn-warning" onclick="onClickChangeMode('movements')">
                        Ver Movimientos
                    </button>
                    </div>
                    {% if user.role == 'manager' %}
                    <button id="btnMassConciliate-{{ bank.pk }}" class="btn btn-success" onclick="onClickMassConciliate('{{ bank.pk }}')" disabled>
                        <i class="fa-solid fa-calculator"></i> Conciliación Masiva
                    </button>
                    <a class="btn btn-primary" href="{% url 'app_banks:bank_movements_upload' seller.shortname bank.id %}">
                        Importar Movimientos
                    </a>
                    <button class="btn btn-info" onclick="onClickBankRules('{{ bank.id }}')">
                        Reglas
                    </button>
                    {% endif %}
                </div>
                </div>
                <!-- Bank Movements Table -->
                <div id="table_movements_{{ bank.pk }}" class="dt-responsive table-responsive mt-4">
                    <table id="movements_bank_{{bank.pk}}" class="table nowrap w-100" >
                        <thead class="table-head">
                        <tr>
                            <th>ID</th>
                            <th>Estado</th>
                            <th>Num Movimiento</th>
                            <th>Concepto</th>
                            <th>Observación</th>
                            <th>Importe</th>
                            <th>Moneda</th>
                            <th>Importe (€)</th>
                            <th>Fecha</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>

                <!-- Bank Conciliate Table -->
                <div id="table_conciliate_{{ bank.pk }}" class="dt-responsive table-responsive mt-4" style="display:none;">
                <table id="conciliate_bank_{{bank.pk}}" class="table nowrap w-100" >
                    <thead class="table-head">
                    <tr>
                        <th class="select-checkbox-column text-center"><input type="checkbox" id="select-all-conciliate-checkbox" class="select-all-checkbox"></th>
                        <th>ID</th>
                        <th>Banco (ID)</th>
                        <th>Estado</th>
                        <th>Concepto</th>
                        <th>Observacion</th>
                        <th>Fecha Movimiento</th>
                        <th>Importe</th>
                        <th>Moneda</th>
                        <th>Importe (€)</th>
                        <th>Movimientos Bancarios</th>
                        <th>Cantidad Conciliaciones</th>
                        <th>Importe  Conciliaciones</th>
                        <th>Conciliación</th>
                        {% if user.role == 'manager' %}
                        <th>Acciones</th>
                        {% endif %}
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                </div>
            </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Modal para Conciliación Masiva de Conciliaciones -->
    <div class="modal fade" id="modalMassConciliate" tabindex="-1" aria-labelledby="modalMassConciliateLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalMassConciliateLabel">Conciliación Masiva de Conciliaciones</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="modalMassConciliateSpinner" class="text-center" style="display:none;">
                        <div class="spinner-border text-primary text-center" role="status">
                            <span class="visually-hidden">Cargando ...</span>
                        </div>
                    </div>
                        <div class="mb-3">
                            <label for="massConciliateAccountSelect" class="form-label">Seleccione Cuenta Contable:</label>
                            <select class="select2" name="massConciliateAccountSelect" id="massConciliateAccountSelect">
                                <option value="0" disabled="disabled" selected> Seleccione Cuenta Contable</option>
                                {% for account in accounting_account %}
                                    <option value="{{account.pk}}"> {{account.pk}} - {{account.description}}</option>
                                {% endfor %}
                            </select>
                        </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnProcessMassConciliate" disabled>Conciliar</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Conciliate Invoice-->
    <div class="modal fade modal-custom" id="modalConciliateInvoice" tabindex="-1" aria-labelledby="modalConciliateInvoiceTitle" aria-hidden="true">
        <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header row">

                <div class="col-12 d-flex align-items-center">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="col-12 mb-2 text-center">
                <h5 class="modal-title text-uppercase">
                    <b><span id="modalConciliateInvoiceTitle">Movimiento</span></b> <br>
                    <b><span id="modalConciliateInvoiceSubTitle"></span></b>
                </h5>
                </div>

            </div>

            <div class="modal-body">
            <!-- Loading Spinner -->
            <div id="modalConciliateInvoiceSpinner" class="text-center">
                <div class="spinner-border text-primary text-center" role="status">
                <span class="visually-hidden">Cargando ...</span>
                </div>
            </div>
            <!-- Content -->
            <div class="row" id="modalConciliateInvoiceContent" style="display:none">
                <div class="col my-0 ms-3 me-1 d-flex align-items-center border-double">
                <h5 class="modal-title" id="modalConciliateInvoiceMovementLabel">
                    <b>Movimiento Bancario</b>
                </h5>
                </div>

                <div class="col my-0 ms-1 me-3 d-flex align-items-center border-double">
                <h5 class="modal-title" id="modalConciliateInvoiceConciliateLabel">
                    <b>Conciliación</b>
                </h5>
                </div>

                <div class="col-12"><!-- Empty --></div>

                <div class="col my-0 ms-3 me-1 p-0 d-flex align-items-center border-double">
                <div class="row m-0 p-0 w-100">
                    <div class="col-9 m-0 d-flex align-items-center justify-content-start">
                    <h6 class="mx-0 my-1"><span id="modalConciliateInvoiceMovementConcept" name="modalConciliateInvoiceMovementConcept"class="text-limit-concept">Concepto Movimiento</span></h6>
                    </div>
                    <div class="col-3 m-0 d-flex align-items-center justify-content-end">
                    <h6 class="mx-0 my-1"><b><span id="modalConciliateInvoiceMovementAmount" name="modalConciliateInvoiceMovementAmount">XX,XX</span>€</b>&nbsp;</h6>
                    </div>
                    <div class="col-12">
                    <h7 class="mx-0 my-1"><span id="modalConciliateInvoiceMovementObservation" name="modalConciliateInvoiceMovementObservation" class="text-limit-concept">Observación Movimiento</span></h7>
                    </div>
                    <div class="col-6 m-0 d-flex align-items-center justify-content-start">
                    <h7 class="mx-0 my-1"><span id="modalConciliateInvoiceMovementDate" name="modalConciliateInvoiceMovementDate">Fecha</span></h7>
                    </div>

                    <div class="col-6 m-0 d-flex align-items-center justify-content-end">
                    <h6 class="mx-0 my-1">
                        <b><span id="modalConciliateInvoiceMovementAmountPending" name="modalConciliateInvoiceMovementAmountPending">XX,XX</span>€</b>
                        &nbsp;<span>por Conciliar</span>&nbsp;
                    </h6>
                    </div>
                </div>
                </div>

                <div class="col">
                <div class="row" id="modalConciliateInvoiceNotConciliated" name="modalConciliateInvoiceNotConciliated" style="display:flex;">
                    <div class="col-12 m-0 d-flex align-items-center justify-content-center" >
                    - No Conciliado -
                    </div>
                </div>
                <div class="row" id="firstconcilation" name="firstconcilation" style="display:none;">
                    <!-- First Concilation Here-->
                </div>
                </div>

                <div class="col-12"><!-- Empty --></div>

                <div class="col-12">
                <div class="row" id="concilations" name="concilations">
                    <!-- Concilations Here-->
                </div>
                </div>

                <div class="col-12"><!-- Empty --></div>

                <div class="col my-3 mx-3 p-0">
                <input class="form-control" type="search" id="searchModalInvoice" name="searchModalInvoice" placeholder="Buscar...">
                </div>

                <div class="col-12"><!-- Empty --></div>

                <div class="col my-3 mx-3 p-0" id="table_invoices_modal">
                </div>
            </div>
            </div>

            <div class="modal-footer"></div>
        </div>
        </div>
    </div>
    <!--Modal Conciliate Invoice Partial Amount -->
    <div class="modal fade" id="modalConciliateInvoicePartialAmount" tabindex="-1" role="dialog" aria-labelledby="modalConciliateInvoicePartialAmountLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title" id="modalConciliateInvoicePartialAmountLabel">Conciliar Parcialmente</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            <!-- Contenido del segundo modal, incluyendo el input para el número -->
            <label for="modalConciliateInvoicePartialAmountInput">Cantidad a Conciliar:</label>
            <input type="number" class="form-control" id="modalConciliateInvoicePartialAmountInput">
            </div>
            <div class="modal-footer">
            <button type="button" class="btn btn-primary" id="modalConciliateInvoicePartialAmountBtn">Conciliar Parcialmente</button>
            </div>
        </div>
        </div>
    </div>
    <!--Modal Conciliate Account -->
    <div class="modal fade" id="modalConciliateAccount" tabindex="-1" role="dialog" aria-labelledby="modalConciliateAccountLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title" id="modalConciliateAccountLabel">Conciliar Contra Cuentas Contables</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            <!-- Contenido del segundo modal, incluyendo el input para el número -->
            <div class="col-12 text-center" id="modalConciliateAccountPrevSpinner" style="display:none;">
                <div class="spinner-border text-primary text-center" role="status">
                <span class="visually-hidden">Cargando ...</span>
                </div>
            </div>

            <div id="modalConciliateAccountPrev" style="display:none;">

                <div class="col-12">
                <div class="row">
                    <div class="col my-0 d-flex align-items-center border-double">
                    <h5 class="modal-title" id="modalConciliateAccountPrevLabel">
                        <b>&nbsp; Conciliaciónes Previas (Cuentas Contables)</b>
                    </h5>
                    </div>
                </div>
                </div>

                <div class="col-12"><!-- Empty --></div>

                <div class="col-12">
                <div class="row" id="modalConciliateAccountPrevDiv">
                    <!-- Concilations Here-->
                </div>
                </div>
                <br>
            </div>

            <div id="modalConciliateAccountContent" style="display:none;">
                <label for="modalConciliateAccountInputBank">Seleccione Cuenta Contable:</label>
                <select class="select2" name="modalConciliateAccountInputBank" id="modalConciliateAccountInputBank">
                <option value="0" disabled="disabled"> Seleccione Cuenta Contable</option>
                {% for account in accounting_account %}
                    {% comment %} <option value="{{account.pk}}"> {{account.pk|slice:":3"}} {{account.country}} - {{account.description}}</option> {% endcomment %}
                    <option value="{{account.pk}}"> {{account.pk}} - {{account.description}}</option>
                {% endfor %}
                </select>
                <br>
                <label for="modalConciliateAccountInputAmount">Cantidad a Conciliar:</label>
                <input type="number" class="form-control" id="modalConciliateAccountInputAmount">
            </div>
            </div>
            <div class="modal-footer">
            <button type="button" class="btn btn-primary" id="modalConciliateAccountBtn">Conciliar</button>
            </div>
        </div>
        </div>
    </div>
    <!-- Confirm Conciliate Modal => ¿ARE YOU SURE? YES / NO -->
    <div class="modal fade" id="modalConciliateAmz" tabindex="-1" aria-labelledby="modalConciliateAmz" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">Conciliar Movimiento contra Banco Amazon <span id='modalConciliateAmzSpanReference1'></span> </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            <p>
                <h6>
                Vas a conciliar el <b>Movimiento <span id='modalConciliateAmzSpanReference2'>X</span></b> con un importe de <b><span id='modalConciliateAmzSpanAmount'>X</span> €.</b>
                </h6>
            </p>
            <p>
                <h6>
                Se va a crear un <b>Movimiento de <span id='modalConciliateAmzSpanAmountMovement'>X</span> €</b> en el <b>Banco <span id='modalConciliateAmzSpanBank' class="text-capitalize">X</span></b>.
                <h6>
            </p>
            <p>
                <h6>
                <b>¿Estás seguro?</b>
                </h6>
            </p>
            </div>
            <div class="modal-footer">
            {% csrf_token %}
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            <button type="button" class="btn btn-primary" id="modalConciliateAmzButton">Conciliar</button>
            </div>
        </div>
        </div>
    </div>

    <!--Modal Bank Rules-->
    <div class="modal fade" id="modalBankRules" tabindex="-1" aria-labelledby="modalBankRulesTitle" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header text-align-center">
                    <h5 class="modal-title" id="modalBankRulesTitle">Reglas para Movimientos Bancarios</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="modalBankRulesSpinner" class="text-center">
                        <div class="spinner-border text-primary text-center" role="status">
                            <span class="visually-hidden">Cargando ...</span>
                        </div>
                    </div>
                    <div id="modalBankRulesContent" style="display:none">
                        <p>Aquí puede configurar reglas para los movimientos bancarios.</p>

                        <!-- Formulario para agregar/editar reglas -->
                        <form id="bankRulesForm" class="mb-4">
                            {% csrf_token %}
                            <input type="hidden" id="bankRulesBankId" name="bank_id" value="">
                            <input type="hidden" id="ruleId" name="rule_id" value="">
                            <input type="hidden" id="ruleType" name="rule_type" value="auto_categorize">
                            <input type="hidden" id="ruleAction" name="rule_action" value="assign_category">
                            <input type="hidden" id="ruleValue" name="rule_value" value="default">

                            <div class="mb-3">
                                <label for="ruleMatchType" class="form-label">Tipo de Parámetro</label>
                                <select class="form-select" id="ruleMatchType" name="rule_match_type" required>
                                    <option value="" selected>Seleccione parámetro</option>
                                    <option value="equals">Igual que</option>
                                    <option value="starts_with">Comienza con</option>
                                    <option value="ends_with">Finaliza con</option>
                                    <option value="contains">Contiene</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="rulePattern" class="form-label">Concepto</label>
                                <input type="text" class="form-control" id="rulePattern" name="rule_pattern" placeholder="Introduzca el concepto" required>
                            </div>
                            <div class="mb-3">
                                <label for="bankRuleAccountSelect" class="form-label">Seleccione Cuenta Contable:</label>
                                <select class="select2" name="bankRuleAccountSelect" id="bankRuleAccountSelect">
                                    <option value="0" disabled="disabled" selected> Seleccione Cuenta Contable</option>
                                    {% for account in accounting_account %}
                                    <option value="{{account.pk}}"> {{account.pk}} - {{account.description}}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="button" id="cancelEditBtn" class="btn btn-secondary me-2" style="display:none;" onclick="cancelEditRule()">Cancelar</button>
                                <button type="button" id="saveBankRuleFormBtn" class="btn btn-primary" onclick="submitRuleForm()">Guardar Regla</button>
                            </div>
                        </form>

                        <!-- Tabla de reglas existentes -->
                        <div class="mt-4">
                            <h5>Reglas Existentes</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="bankRulesTable">
                                    <thead>
                                        <tr>
                                            <th>Tipo de Parámetro</th>
                                            <th>Concepto</th>
                                            <th>Cuenta Contable</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bankRulesTableBody" class="sortable-list">
                                        <!-- Las reglas se cargarán aquí dinámicamente -->
                                        <tr id="noRulesRow">
                                            <td colspan="4" class="text-center">No hay reglas configuradas para este banco.</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="alert alert-info mt-2">
                                    <i class="fas fa-info-circle"></i> Las reglas se aplican en el orden mostrado. Arrastra y suelta para cambiar la prioridad.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="applyRulesBtn" onclick="applyBankRules()">Aplicar Reglas</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}
{% block javascripts %}
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
    <script src="{{ STATIC_URL }}assets/cdns_locals/js/select/select2.min-v4.1.0.js"></script>
    <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
    <!-- start DEBUG -->
    <script type="text/javascript">
        const debug = true; // Habilitado para depuración
        // Función para debug (imprime en consola solo si debug está habilitado)
        function debugLog(...args) {
        if (debug) {
            console.log(...args);
        }
        }
        debugLog("Debug mode is enabled")
    </script>
    <!-- end DEBUG -->
    <script>
        // Check for toast message in URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const toastMessage = urlParams.get('toast_message');
            const toastType = urlParams.get('toast_type') || 'error';

            if (toastMessage && toastMessage.trim() !== "") {
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 20000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer);
                        toast.addEventListener('mouseleave', Swal.resumeTimer);
                    }
                });

                Toast.fire({
                    icon: toastType,
                    title: toastMessage
                });

                // Remove the parameters from URL without reloading the page
                // This prevents the toast from showing again on refresh
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }
        });

        // Variables Globales Generales
        let dj = {}; // Objeto global para almacenar datos provenientes de Django.
        let selectedCard = null; // ID de la tarjeta bancaria seleccionada actualmente.
        let modal = null; // Modal actualmente activo.
        const csrftoken = getCookie('csrftoken'); // Token CSRF para solicitudes seguras.
        let screenWidth = window.innerWidth; // Ancho actual de la pantalla.
        const globalFilters = {
            year: '',
            months: [],
        }; // Objeto global para almacenar los filtros.
        let currentSearchText = ""; // Variable para almacenar el texto del filtro
        const initializedTables = new Set(); // Inicializa un Set para rastrear tablas inicializadas

        // Elementos del DOM
        const cardContainer = document.querySelector(".card-container"); // Contenedor del carrusel.
        const prevButton = document.querySelector(".carousel-prev"); // Botón para retroceder en el carrusel.
        const nextButton = document.querySelector(".carousel-next"); // Botón para avanzar en el carrusel.
        const arrowLeft = document.querySelector("#arrow-left"); // Flecha izquierda.
        const arrowRight = document.querySelector("#arrow-right"); // Flecha derecha.

        // Configuración del Carrusel
        const DUMMY_CARDS = 0; // Cantidad de tarjetas dummy para testing.
        const cardWidth = 390; // Ancho de cada tarjeta en el carrusel.

        {% if banks and banks|length >= 1 %}
            const cardsTotal = 1 + {{banks|length}} + DUMMY_CARDS; // Cantidad de tarjetas en total
        {% else %}
            const cardsTotal = 1 + DUMMY_CARDS; // Cantidad de tarjetas en total
        {% endif %}

        let cardsToShow = 3; // Cantidad de tarjetas visibles en el carrusel según el ancho de pantalla.
        const cardMargin = 20; // Margen entre tarjetas en el carrusel.
        let scrollValue = 0; // Valor de desplazamiento del carrusel.

        // Variables para Inputs
        const inputs = []; // Inputs de búsqueda.
        const icons = []; // Íconos asociados a los inputs.

        // Variables para DataTables
        let mode = 'movements'; // Modo actual: 'movements' o 'conciliate'.
        const dataTableInstances = {}; // Objeto para almacenar instancias de DataTables por banco.

        // Obtiene el valor de una cookie específica almacenada en el navegador
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        function handleBankUpdateFromResponse(response) {
          debugLog("Ejecutando handleBankUpdateFromResponse - act saldo" )
          if (response.bank_id) {
              updateBankBalance(response.bank_id);
          } else {
              console.warn("No se pudo actualizar saldo: bank_id no disponible en la respuesta.");
          }
        }


        // Activar o desactivar el botón de limpiar
        const toggleClearButton = (bankId) => {
            // Intenta obtener los elementos de año y meses
            const yearElement = document.getElementById(`year-${bankId}`);
            const monthsElement = document.getElementById(`multiple-month-${bankId}`);

            // Verifica si los elementos existen
            const year = yearElement ? yearElement.value : ""; // Si no existe, usa una cadena vacía
            const monthsSelected = monthsElement?.getAttribute("data-value") || ""; // Maneja la inexistencia de monthsElement

            // Depuración
            debugLog(`toggleClearButton | Año: ${year}`);
            debugLog(`toggleClearButton | Meses seleccionados: ${monthsSelected}`);

            // Obtener el botón de limpiar
            const clearButton = document.getElementById(`clearFiltersButton-${bankId}`);
            if (clearButton) {
                // Habilitar/deshabilitar el botón según si hay filtros seleccionados
                clearButton.disabled = !(year || monthsSelected);
            } else {
                debugLog(`toggleClearButton | Botón de limpiar no encontrado para bankId: ${bankId}`);
            }
        };

        // Escuchar cambios en los filtros
        document.querySelectorAll('[id^="year-"], [id^="multiple-month-"]').forEach(element => {
            element.addEventListener('change', () => {
                const bankId = element.id.split('-')[1]; // Extraer bankId del ID del elemento
                toggleClearButton(bankId);
            });
        });

        // Función opcional para actualizar las insignias de filtro (si aplica)
        const updateFiltersBadge = (bankId) => {
            const badge = document.querySelector(`#id-filter-notification-${bankId}`);
            const filterCount = (globalFilters.year ? 1 : 0) + (globalFilters.months.length > 0 ? 1 : 0);

            if (filterCount > 0) {
                badge.classList.remove('d-none');
                badge.textContent = filterCount;
            } else {
                badge.classList.add('d-none');
                badge.textContent = '';
            }
        };

        // Alterna la visibilidad del formulario de filtros asociado a un banco específico.
        const toggleFilterForm = (bankId) => {
            const dropdownFiltersForm = document.getElementById(`dropdownFiltersForm-${bankId}`);
            const dropdownButton = document.getElementById(`dropdownButton-${bankId}`);

            if (!dropdownFiltersForm) {
                console.error(`No se encontró el formulario de filtros para bank_id=${bankId}`);
                return;
            }

            const isVisible = dropdownFiltersForm.style.display === "block";

            if (isVisible) {
                // Ocultar formulario
                dropdownFiltersForm.style.display = "none";
                dropdownFiltersForm.style.opacity = "0";
                dropdownFiltersForm.classList.remove("d-block");
                dropdownFiltersForm.classList.add("d-none");
                debugLog(`Formulario de filtro cerrado para bank_id=${bankId}`);
            } else {
                // Mostrar formulario
                dropdownFiltersForm.style.display = "block";
                dropdownFiltersForm.style.opacity = "1";
                dropdownFiltersForm.classList.remove("d-none");
                dropdownFiltersForm.classList.add("d-block");
                dropdownFiltersForm.scrollIntoView({ behavior: "smooth", block: "center" });
                debugLog(`Formulario de filtro abierto para bank_id=${bankId}`);

                // Configurar cierre al hacer clic fuera
                setupClickOutsideHandler(dropdownFiltersForm, dropdownButton, bankId);
            }
        };

        const setupClickOutsideHandler = (dropdownFiltersForm, dropdownButton, bankId) => {
            const closeOnClickOutside = (event) => {
                // Verificar si el clic ocurrió fuera del formulario
                if (!dropdownFiltersForm.contains(event.target) && !dropdownButton.contains(event.target)) {
                    // Ocultar el formulario
                    dropdownFiltersForm.style.display = "none";
                    dropdownFiltersForm.style.opacity = "0";
                    dropdownFiltersForm.classList.remove("d-block");
                    dropdownFiltersForm.classList.add("d-none");
                    debugLog(`Formulario de filtro cerrado al hacer clic fuera de bank_id=${bankId}`);

                    // Eliminar el listener para evitar fugas de memoria
                    document.removeEventListener("click", closeOnClickOutside);
                }
            };
            // Agregar el listener global
            document.addEventListener("click", closeOnClickOutside);

            // Evitar el cierre si se hace clic dentro del formulario
            dropdownFiltersForm.addEventListener("click", (event) => {
                event.stopPropagation();
            });
        };

        // Función para expandir el input
        function expandInput(input) {
            const inputContainer = input.parentElement;
            inputContainer.style.width = "50%"; // Expande el contenedor
        }

        // Función para colapsar el input
        function collapseInput(input) {
            const inputContainer = input.parentElement;
            inputContainer.style.width = "140px"; // Colapsa el contenedor
            input.value = ""; // Limpia el contenido del input
        }

        // Función para manejar el evento "blur"
        function handleBlur(input) {
            setTimeout(() => {
                if (input.value.trim() === "") {
                    collapseInput(input);
                } else {
                    // Mantener el input expandido si tiene texto
                    expandInput(input);
                }
            }, 100); // Retraso para evitar conflictos al hacer clic
        }

        // Función para manejar el evento "input"
        function handleInput(input) {
            const filtro = input.value.trim().toLowerCase();
            currentSearchText = filtro;
            if (filtro === "") {
              expandInput(input);
              const bankId = selectedCard;
              if (bankId) {
                  const dataTableMovements = $(`#movements_bank_${bankId}`).DataTable();
                  const dataTableConciliations = $(`#conciliate_bank_${bankId}`).DataTable();
                  dataTableMovements.search('').draw();        // Limpia búsqueda de movimientos
                  dataTableConciliations.search('').draw();    // Limpia búsqueda de conciliaciones
              }
          }
            debugLog("Texto de búsqueda:", filtro);
            debugLog("Banco seleccionado:", selectedCard);
            aplicarFiltro(filtro, selectedCard);
        }

        // Aplica los filtros del imput de busqueda
        function aplicarFiltro(filtro, bankId = null) {
            const selectedBankId = bankId || selectedCard;
            const dataTableMovements = $(`#movements_bank_${selectedBankId}`).DataTable();
            const dataTableConciliations = $(`#conciliate_bank_${selectedBankId}`).DataTable();
            const movementsTableVisible = document.querySelector(`#table_movements_${selectedBankId}`)?.style.display !== "none";
            const conciliateTableVisible = document.querySelector(`#table_conciliate_${selectedBankId}`)?.style.display !== "none";
            if (movementsTableVisible) {
                debugLog(`Buscando en tabla de movimientos del banco ${selectedBankId}`);
                dataTableMovements.search(filtro).draw();
            } else if (conciliateTableVisible) {
                debugLog(`Buscando en tabla de conciliación del banco ${selectedBankId}`);
                dataTableConciliations.search(filtro).draw();
            }
        }

        // Aplica los filtros seleccionados AÑO | MESES
        function applyFilters(bankId) {
            // Obtener el valor del año seleccionado
            const year = document.getElementById(`year-${bankId}`).value;

            // Elemento del filtro de meses
            const monthsElement = document.getElementById(`multiple-month-${bankId}`);
            let monthsValue = [];

            if (monthsElement) {
                // Verificar si el componente tiene un Shadow DOM
                const shadowRoot = monthsElement.shadowRoot;
                if (shadowRoot) {
                    // Obtener los valores del atributo 'data-value' del componente
                    const dataValue = monthsElement.getAttribute('data-value');
                    if (dataValue) {
                        const values = dataValue.split(',').map(v => v.trim());
                        monthsValue.push(...values);
                    }
                }
            }

            // Actualizar los filtros globales
            globalFilters.year = year;
            globalFilters.months = monthsValue;

            debugLog(
                `applyFilters - Antes de aplicar filtros: globalFilters =`,
                JSON.stringify(globalFilters)
            );

            // Aplicar los filtros a la tabla visible
            applyFiltersToVisibleTable(bankId);

            // Cerrar el formulario de filtros tras 1 segundo
            setTimeout(() => toggleFilterForm(bankId), 1000);
        };

        // Función para manejar el clic en el ícono asociado al input
        function handleIconClick(input) {
            expandInput(input);
            input.focus(); // Coloca el foco en el input
        }

        window.checkFilterState = function() {
            // Lógica de la función
            debugLog("Verificando el estado de los filtros.");
        };

        // Procesa los datos iniciales enviados desde Django y los almacena en dj
        function getDjangoData(djObj = null) {
            debugLog("Obteniendo datos iniciales de Django...");

            try {
                // Si no se proporciona djObj, inicializar desde Django
                if (!djObj) {
                    djObj = {{ json | safe }};
                }

                // Crear un nuevo objeto procesado
                const djProcessed = {};

                // Iterar sobre las claves y procesar los datos
                for (const [key, value] of Object.entries(djObj)) {
                    const parsedData = JSON.parse(value); // Asegura que value sea JSON válido
                    djProcessed[key] = parsedData.map(obj => ({
                        ...obj?.fields,
                        pk: obj?.pk,
                    }));
                }

                // Asegurarse de que 'seller' sea un único objeto
                djProcessed.seller = Array.isArray(djProcessed?.seller) && djProcessed.seller.length > 0
                    ? djProcessed.seller[0]
                    : {};

                // Asignar los datos procesados a la variable global dj
                dj = djProcessed;

                debugLog("Datos procesados correctamente:", djProcessed);
            } catch (error) {
                console.error("Error en getDjangoData:", error);
                dj = {}; // Reiniciar en caso de error
            }
            // Imprimir los datos procesados para depuración
            debugLog("DJ:", dj);
        }

        // Función para aplicar los filtros a la tabla visible
        const applyFiltersToVisibleTable = (bankId) => {
            debugLog(`Ejecutando applyFiltersToVisibleTable para bankId: ${bankId}`);
            debugLog(`Antes de aplicar applyFiltersToVisibleTable: globalFilters =`, JSON.stringify(globalFilters));

            // Actualizar el campo de año desde los globalFilters
            const yearElement = document.getElementById(`year-${bankId}`);
            if (yearElement) {
                if (globalFilters.year) {
                    yearElement.value = globalFilters.year;
                    debugLog(`Año actualizado en el campo: ${yearElement.value}`);
                } else {
                    yearElement.value = '';
                    debugLog(`Campo de año limpiado.`);
                }
                // Llama a la función onChangeYear para manejar el estado del filtro de meses
                onChangeYear(bankId);
            } else {
                debugLog(`Elemento year-${bankId} no encontrado.`);
            }

            // Actualizar los meses seleccionados desde los globalFilters
            const monthsElement = document.getElementById(`multiple-month-${bankId}`);
            if (monthsElement) {
                const shadowRoot = monthsElement.shadowRoot;
                debugLog(`Elemento monthsElement encontrado:`, monthsElement);

                if (shadowRoot) {
                    debugLog(`ShadowRoot encontrado para monthsElement:`, shadowRoot);

                    // Actualizar el atributo 'data-value' del componente
                    const dataValue = globalFilters.months.join(','); // Convertir los valores globales a una cadena compatible
                    monthsElement.setAttribute('data-value', dataValue);
                    debugLog(`Atributo 'data-value' actualizado en monthsElement:`, dataValue);

                    // Obtener el slot y sus elementos asignados
                    const slot = shadowRoot.querySelector('slot[name="check-values"]');
                    if (slot) {
                        debugLog(`Slot 'check-values' encontrado en ShadowRoot.`);
                        const assignedElements = slot.assignedElements();
                        debugLog(`Elementos asignados al slot:`, assignedElements);

                        // Verificar si el primer elemento es <ul> y procesar sus hijos
                        const ulElement = assignedElements.find(el => el.tagName === 'UL');
                        if (ulElement) {
                            debugLog(`Elemento <ul> encontrado en asignados:`, ulElement);

                            // Procesar los elementos <li> hijos del <ul>
                            const listItems = ulElement.querySelectorAll('li');

                            const selectedTitles = []; // Guardar los títulos seleccionados
                            listItems.forEach((li, index) => {
                                const checkbox = li.querySelector('input[type="checkbox"]');
                                const value = li.getAttribute('value');
                                const multiTitle = li.getAttribute('multi-title');

                                if (globalFilters.months.includes(value)) {
                                    li.classList.add('selected'); // Marcar como seleccionado
                                    li.setAttribute('aria-selected', 'true');
                                    if (checkbox) checkbox.checked = true; // Marcar checkbox como seleccionado
                                    if (multiTitle) selectedTitles.push(multiTitle);
                                    debugLog(`[${index}] Marcado como seleccionado. Checkbox checked=${checkbox?.checked}`);
                                } else {
                                    li.classList.remove('selected'); // Desmarcar si no está en los globalFilters
                                    li.setAttribute('aria-selected', 'false');
                                    if (checkbox) checkbox.checked = false; // Desmarcar checkbox
                                }
                            });

                            // Actualizar visualmente el contenido del `multi-month` input field
                            const inputField = shadowRoot.querySelector('input#multiple-selector');
                            if (inputField) {
                                inputField.value = selectedTitles.join(', '); // Muestra los títulos seleccionados
                                debugLog(`Contenido del inputField actualizado:`, inputField.value);
                            } else {
                                debugLog(`No se encontró inputField con id 'multiple-selector' en ShadowRoot.`);
                            }
                        } else {
                            debugLog(`Elemento <ul> no encontrado entre los asignados al slot.`);
                        }
                    } else {
                        debugLog(`Slot 'check-values' no encontrado en ShadowRoot.`);
                    }
                } else {
                    debugLog(`No se encontró ShadowRoot en monthsElement.`);
                }
            } else {
                debugLog(`Elemento 'multiple-month-${bankId}' no encontrado.`);
            }

            // Actualizar la insignia de filtros
            updateFiltersBadge(bankId);

            // Activar o desactivar el botón de limpiar si hay filtros aplicados
            const clearButton = document.getElementById(`clearFiltersButton-${bankId}`);
            if (clearButton) {
                clearButton.disabled = !(globalFilters.year || globalFilters.months.length > 0);
                debugLog(`Estado del botón de limpiar para bankId: ${bankId} => ${clearButton.disabled ? 'Desactivado' : 'Activado'}`);
            }

            // Construir la URL con los filtros globales
            let ajaxUrl = `/sellers/${dj.seller.shortname}/reconciliation/get/movement/DT/?bank_id=${bankId}`;
            if (globalFilters.year) ajaxUrl += `&year=${globalFilters.year}`;
            if (globalFilters.months.length > 0) ajaxUrl += `&months=${globalFilters.months.join(",")}`;
            debugLog(`URL AJAX generada:`, ajaxUrl);

            // Detectar la tabla visible (movimientos o conciliación)
            const movementsTableVisible = document.querySelector(`#table_movements_${bankId}`)?.style.display !== "none";
            const tableId = movementsTableVisible ? `#movements_bank_${bankId}` : `#conciliate_bank_${bankId}`;
            debugLog(`Tabla visible identificada: ${tableId}, movementsTableVisible=${movementsTableVisible}`);

            // Actualizar la URL de la tabla visible
            try {
                $(tableId).DataTable().ajax.url(ajaxUrl).load();
                debugLog(`URL AJAX actualizada en la tabla ${tableId}.`);
            } catch (error) {
                console.error(`Error al actualizar la URL AJAX para la tabla ${tableId}:`, error);
            }
        };

        async function updateAllBankBalances() {
            for (const bank of dj.banks) {
                try {
                    debugLog(`Inicializando tabla para banco ID: ${bank.pk}`);
                    await initializeTables(bank);
                    await updateBankBalance(bank.pk);
                } catch (err) {
                    console.error(`Error al inicializar el banco ID: ${bank.pk}`, err);
                }
            }
        }

        // NOTE:(cambiada) Calcula y actualiza el saldo del banco.
        async function updateBankBalance(bankId) {
            debugLog(`Calculando y actualizando saldo para banco ID: ${bankId}`);

            if (!bankId) {
                console.error("El bankId es nulo o indefinido. Abortando.");
                return;
            }

            const url = `/sellers/${dj.seller.shortname}/banks/${bankId}/total-amount/`;
            debugLog("URL generada:", url);

            try {
                const response = await fetch(url, {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": csrftoken, // Incluye el token CSRF
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP Error: ${response.status}`);
                }

                const data = await response.json();

                if (data.result === "ok") {
                    const totalAmount = parseFloat(data.total_amount || 0);
                    const bank = dj.banks.find((bank) => bank.pk === bankId);
                    const initialAmount = bank ? parseFloat(bank.bank_initial_amount || 0) : 0;
                    const totalBalance = initialAmount + totalAmount;

                    debugLog(`Saldo calculado para banco ${bankId}: ${totalBalance}`);

                    // Actualizar en el DOM
                    const cardElement = document.querySelector(`#bank_${bankId}`);
                    if (cardElement) {
                        const balanceElement = cardElement.querySelector(".card-balance");
                        if (balanceElement) {
                            balanceElement.textContent = `SALDO: ${totalBalance.toFixed(2)}€`;
                        } else {
                            const newBalanceElement = document.createElement("div");
                            newBalanceElement.className = "card-balance";
                            newBalanceElement.textContent = `SALDO: ${totalBalance.toFixed(2)}€`;
                            cardElement.appendChild(newBalanceElement);
                        }
                    } else {
                        console.warn(`No se encontró la tarjeta del banco con ID: ${bankId}`);
                    }
                } else {
                    console.error("Error al calcular la suma total:", data.message);
                }
            } catch (error) {
                console.error("Error en la solicitud de suma total:", error);
            }
        }

        // Función para gestionar el cambio en el selector de año
        function onChangeYear(bankId) {
            const year = document.getElementById(`year-${bankId}`); // Selector del año
            const period = document.getElementById(`multiple-month-${bankId}`); // Selector de meses

            debugLog("EL AÑO ES: ", year?.value || "No seleccionado");

            if (year && year.value) {
                // Si se selecciona un año específico, habilitar el selector de meses
                period.removeAttribute("disabled");
                debugLog(`Selector de meses habilitado para bankId: ${bankId}`);
            } else {
                // Si no hay año seleccionado, deshabilitar el selector de meses
                period.setAttribute("disabled", "disabled");
                debugLog(`Selector de meses deshabilitado para bankId: ${bankId}`);

                // Limpiar el valor visual y cerrar el dropdown
                const shadowRoot = period.shadowRoot;
                if (shadowRoot) {
                    // Cerrar el dropdown si está abierto
                    const dropdown = shadowRoot.querySelector('.checkbox-container');
                    if (dropdown) {
                        dropdown.style.transform = 'scale(1, 0)'; // Ocultar dropdown
                        debugLog(`Dropdown cerrado para selector de meses en bankId: ${bankId}`);
                    }

                    // Limpiar el campo de entrada del selector
                    const inputField = shadowRoot.querySelector('input#multiple-selector');
                    if (inputField) {
                        inputField.value = ''; // Limpia la vista del selector
                        debugLog(`InputField del selector de meses limpiado para bankId: ${bankId}`);
                    }

                    // Quitar selección de todos los meses
                    const checkboxes = shadowRoot.querySelectorAll('li');
                    checkboxes.forEach(li => {
                        li.classList.remove('selected'); // Quitar selección visual
                        li.setAttribute('aria-selected', 'false'); // Marcar como no seleccionado
                        const checkbox = li.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = false; // Desmarcar checkbox
                        }
                    });
                    debugLog(`Selección de meses eliminada para bankId: ${bankId}`);

                    // Actualizar el atributo 'data-value' del componente
                    period.setAttribute('data-value', '');
                    debugLog(`Atributo 'data-value' del selector de meses limpiado para bankId: ${bankId}`);
                }

                // Limpiar la selección global de meses en los filtros
                if (globalFilters && globalFilters.months) {
                    globalFilters.months = [];
                    debugLog(`globalFilters.months limpiado para bankId: ${bankId}`);
                }
            }

            // Actualizar el badge de los filtros
            updateFiltersBadge(bankId);
        }

        // Función para limpiar los filtros
        const resetFilters = (bankId) => {
            debugLog(`Ejecutando resetFilters para bankId: ${bankId}`);
            // Limpia los filtros globales
            globalFilters.year = ""; // Limpia el año
            globalFilters.months = []; // Limpia los meses
            debugLog(`globalFilters después de limpiar:`, JSON.stringify(globalFilters));

            onChangeYear(bankId)
            // Llama a la función para actualizar el contador de filtros
            updateFiltersBadge(bankId);
            // Aplica los filtros limpios a la tarjeta actual
            applyFiltersToVisibleTable(bankId);

            // Desactiva el botón de limpiar
            const clearButton = document.getElementById(`clearFiltersButton-${bankId}`);
            if (clearButton) {
                clearButton.disabled = true;
                debugLog(`Botón de limpiar desactivado para bankId: ${bankId}`);
            } else {
                debugLog(`Botón de limpiar no encontrado para bankId: ${bankId}`);
            }
            // Cierra el formulario tras limpiar
            setTimeout(() => toggleFilterForm(bankId), 500);
            debugLog(`resetFilters completado para bankId: ${bankId}`);
        };

        const onClickChangeMode = (newmode) => {
            debugLog("onClickChangeMode: ", newmode);
            debugLog("Selected Card: ", selectedCard)

            // Verificar si hay texto en el buscador
            const searchInput = document.querySelector("#search-input-" + selectedCard);
            const filtro = searchInput.value.trim().toLowerCase();

            if (newmode=="conciliate") {
                debugLog('conciliate');
                // SHOW MOVEMENTS
                document.querySelector("#table_movements_" + selectedCard).style.display = "none";
                // HIDE CONCILIATE
                document.querySelector("#table_conciliate_" + selectedCard).style.display = "block";
                // SHOW CHANGE TO MOVEMENTS
                document.querySelector("#change_movements_" + selectedCard).style.display = "block";
                // HIDE CHANGE TO CONCILIATE
                document.querySelector("#change_conciliate_" + selectedCard).style.display = "none";
                // SHOW TITLE
                document.querySelector("#title_table_" + selectedCard).textContent = " - Conciliación";
                // Resetear el botón de conciliación masiva (deshabilitado por defecto)
                $(`#btnMassConciliate-${selectedCard}`).prop('disabled', true);
            } else if (newmode=="movements") {
                debugLog('movements');
                // HIDE MOVEMENTS
                document.querySelector("#table_movements_" + selectedCard).style.display = "block";
                // SHOW CONCILIATE
                document.querySelector("#table_conciliate_" + selectedCard).style.display = "none";
                // HIDE CHANGE TO MOVEMENTS
                document.querySelector("#change_movements_" + selectedCard).style.display = "none";
                // SHOW CHANGE TO CONCILIATE
                document.querySelector("#change_conciliate_" + selectedCard).style.display = "block";
                // SHOW TITLE
                document.querySelector("#title_table_" + selectedCard).innerHTML = " - Movimientos";
            }

            // Aplicar el filtro
            aplicarFiltro(filtro, selectedCard);
            // Aplicar filtros a la tabla visible
            applyFiltersToVisibleTable(selectedCard);
        };

        // Función para actualizar el estado de los botones
        const updateArrowButtons = () => {
            const visibleWidth = cardsToShow * (cardWidth + cardMargin); // Ancho visible del carrusel
            const totalWidth = cardsTotal * (cardWidth + cardMargin); // Ancho total de las tarjetas
            const maxScroll = Math.max(0, totalWidth - visibleWidth); // Máximo desplazamiento permitido, asegurando que no sea negativo

            // Habilitar o deshabilitar botones según el desplazamiento
            arrowLeft.disabled = scrollValue <= 0;
            arrowRight.disabled = scrollValue >= maxScroll; // Corregido: deshabilitar solo al alcanzar el final

            // Actualizar estilos de opacidad
            arrowLeft.style.opacity = arrowLeft.disabled ? "0.15" : "1";
            arrowRight.style.opacity = arrowRight.disabled ? "0.15" : "1";
        };

        // Manejo del clic en el botón "next"
        const onClickNext = () => {
            const visibleWidth = cardsToShow * (cardWidth + cardMargin); // Ancho visible del carrusel
            const totalWidth = cardsTotal * (cardWidth + cardMargin); // Ancho total de las tarjetas
            const maxScroll = totalWidth - visibleWidth; // Máximo desplazamiento permitido

            scrollValue += cardWidth + cardMargin;

            // Ajustar el desplazamiento para no exceder el máximo
            scrollValue = Math.min(scrollValue, maxScroll);
            cardContainer.style.transform = `translateX(-${scrollValue}px)`;
            updateArrowButtons(); // Actualizamos el estado de los botones
        };

        // Manejo del clic en el botón "prev"
        const onClickPrev = () => {
            scrollValue -= cardWidth + cardMargin;

            // Ajustar el desplazamiento para no ir más allá del inicio
            scrollValue = Math.max(scrollValue, 0);
            cardContainer.style.transform = `translateX(-${scrollValue}px)`;
            updateArrowButtons(); // Actualizamos el estado de los botones
        };

        // Redimensionar la pantalla
        const onResize = () => {
            screenWidth = window.innerWidth;
            cardsToShow = screenWidth >= 1900 ? 4 : screenWidth >= 1444 ? 3 : 3;

            const visibleWidth = cardsToShow * (cardWidth + cardMargin); // Ancho visible del carrusel
            const totalWidth = cardsTotal * (cardWidth + cardMargin); // Ancho total de las tarjetas
            let maxScroll = totalWidth - visibleWidth; // Máximo desplazamiento permitido
            maxScroll = Math.max(0, maxScroll); // Asegurar que maxScroll no sea negativo

            // Ajustamos el desplazamiento si excede el máximo
            scrollValue = Math.min(scrollValue, maxScroll);
            cardContainer.style.transform = `translateX(-${scrollValue}px)`;
            updateArrowButtons(); // Actualizamos el estado de los botones
        };

        const onClickCardBank = (bank_id) => {
            // Establecer la tarjeta seleccionada
            selectedCard = bank_id;
            // Imprimir el ID de la tarjeta seleccionada
            debugLog("selectedCard actualizado:", selectedCard);
            debugLog(`Antes de cambiar tarjeta: globalFilters =`, JSON.stringify(globalFilters));

            // Iterar sobre todas las tarjetas bancarias
            dj.banks.forEach((bank) => {
              // Obtener el elemento HTML de la tarjeta
              const card = document.querySelector("#bank_" + bank.pk);
              if (card) {
              const cbody = card.getElementsByClassName("card-body"); // Cuerpo de la tarjeta
              const cedit = document.querySelector("#bank_" + bank.pk + "_edit"); // Botón de edición
              const move = document.querySelector("#move_bank_" + bank.pk); // Movimientos asociados
              const dropdownFiltersForm = document.getElementById(`dropdownFiltersForm-${bank.pk}`); // Formulario de filtros
              const dropdownButton = document.getElementById(`dropdownButton-${bank.pk}`); // Botón de filtros

              // Siempre cerrar todos los formularios de filtros antes de gestionar la nueva selección
              if (dropdownFiltersForm) {
                  dropdownFiltersForm.style.display = "none"; // Ocultar formulario
                  dropdownFiltersForm.style.opacity = "0"; // Asegurar que sea invisible
                  dropdownFiltersForm.classList.remove("d-block"); // Eliminar clase de visible
                  dropdownFiltersForm.classList.add("d-none"); // Asegurar clase de oculto
                  debugLog(`Formulario de filtro inicia cerrado para bank_id=${bank.pk}`);

                  // Configurar el evento del botón para alternar el formulario de filtros
                  if (dropdownButton) dropdownButton.onclick = () => toggleFilterForm(bank.pk);
              }

              // Gestionar estilos y visibilidad según si la tarjeta está seleccionada
              if (cbody) {
                  if (bank.pk.toString() === bank_id.toString()) {
                  // Tarjeta seleccionada
                  cbody.item(0).classList.remove("bg-secondary");
                  cbody.item(0).classList.add("border", "border-3", "border-white", "rounded", "shadow");
                  // Excluir estilos específicos para tarjetas con nombres "amazon" o "accounts"
                  if (bank.bank_name.toLowerCase() !== "amazon" && bank.bank_name.toLowerCase() !== "accounts") cbody.item(0).classList.add("bg-success");
                  // Mostrar movimientos bancarios
                  move.style.display = "block";
                  // Mostrar botón de edición si existe
                  if (cedit) {
                      cedit.style.display = "block";
                  }
                  } else {
                  // Tarjetas no seleccionadas
                  cbody.item(0).classList.remove("border", "border-3", "border-white", "rounded", "shadow", "bg-success");
                  // Excluir estilos específicos para tarjetas con nombres "amazon" o "accounts"
                  if (bank.bank_name.toLowerCase() !== "amazon" && bank.bank_name.toLowerCase() !== "accounts") cbody.item(0).classList.add("bg-secondary");
                  // Ocultar movimientos bancarios
                  move.style.display = "none";
                  // Ocultar botón de edición si existe
                  if (cedit) cedit.style.display = "none";
                  }
              }
              // Actualizar el input de búsqueda para cada tarjeta
              const input = document.querySelector("#search-input-" + bank.pk);
              if (!input) return;

              // Si es la tarjeta seleccionada
              if (bank.pk.toString() === bank_id.toString()) {
                  // Si hay texto guardado en currentSearchText, lo aplicamos
                  if (currentSearchText && currentSearchText.trim() !== "") {
                      input.value = currentSearchText;
                      expandInput(input);
                      aplicarFiltro(currentSearchText, bank_id);
                  } else {
                      input.value = "";
                      currentSearchText = "";
                      collapseInput(input);
                      // Limpia el filtro visual si la tabla seguía filtrada
                      const dataTableMovements = $(`#movements_bank_${bank_id}`).DataTable();
                      const dataTableConciliations = $(`#conciliate_bank_${bank_id}`).DataTable();
                      dataTableMovements.search('').draw();
                      dataTableConciliations.search('').draw();
                  }
              } else {
                  input.value = "";
                  collapseInput(input);
              }

              }
            });
            // Aplicar filtros a la tabla visible

            if (selectedCard) {
                debugLog(`IF1 Desde onClickCardBank verificamos globalFilters = ${JSON.stringify(globalFilters)} comparando con globalFilters.year = ${globalFilters.year}`)
                applyFiltersToVisibleTable(selectedCard);
                onChangeYear(selectedCard)
                debugLog(`IF2 Desde onClickCardBank verificamos globalFilters = ${JSON.stringify(globalFilters)} comparando con globalFilters.year = ${globalFilters.year}`)
            } else {

                debugLog(`ELSE Desde onClickCardBank selectedCard `)
            }
            updateFiltersBadge(selectedCard)
        };

        const onClickNewBank = (type) => {
            debugLog("onClickNewBank");
            window.location.href = `./new/${type}`;
        };

        const onClickModalConciliateInvoicePartialAmount = (movement_id, target_id, amount) => {
            const modal = $("#modalConciliateInvoicePartialAmount");
            const input = $("#modalConciliateInvoicePartialAmountInput");
            const btn = $("#modalConciliateInvoicePartialAmountBtn");

            // Store data for later use
            modal.data('movement_id', movement_id);
            modal.data('target_id', target_id);

            // Store the reconciliation type (invoice or transfer)
            // Determine if this is an invoice or transfer based on the currently visible table
            const isTransfer = $('#transfersToConciliate').is(':visible');
            modal.data('is_transfer', isTransfer);

            // Set proper amount handling
            if (amount == null) {
                amount = 0;
            }
            amount = parseFloat(amount);

            // Update UI
            input.val(amount);

            // Set constraints
            if (amount > 0) {
                input.attr("min", 0.01);
                input.attr("max", amount);
            } else {
                input.attr("min", amount);
                input.attr("max", -0.01);
            }

            input.attr("step", 0.01);
            input.attr("disabled", false);
            btn.attr("disabled", false);

            // Remove previous click events
            btn.off("click");

            // Add new click event handler that checks if it's an invoice or transfer
            btn.on("click", function() {
                input.attr("disabled", true);
                btn.attr("disabled", true);
                const partialAmount = input.val();
                const currentMovementId = modal.data('movement_id');
                const currentTargetId = modal.data('target_id');
                const isTransfer = modal.data('is_transfer');

                // Choose which function to call based on whether it's an invoice or transfer
                if (isTransfer) {
                    // Handle transfer partial reconciliation
                    onClickNewConciliate2Transfer(currentMovementId, currentTargetId, partialAmount);
                } else {
                    // Handle invoice partial reconciliation
                    onClickNewConciliate2Invoice(currentMovementId, currentTargetId, partialAmount);
                }

                modal.modal("hide");

                // Update the main modal to reflect the partial reconciliation
                const modalType = isTransfer ? 'transfer' : 'invoice';
                onClickConciliateInvoiceModalUpdateData(currentMovementId, modalType, false);
            });

            // Input validation handler
            input.on("input", function() {
                debugLog("input change: ", input.val());
                // Enable/disable button based on valid input
                if (amount >= 0) {
                    if (input.val() > 0 && input.val() <= amount) {
                        btn.attr("disabled", false);
                    } else {
                        btn.attr("disabled", true);
                    }
                } else {
                    if (input.val() >= amount && input.val() < 0) {
                        btn.attr("disabled", false);
                    } else {
                        btn.attr("disabled", true);
                    }
                }
            });

            modal.modal("show");
        };

        const onClickConciliateInvoiceModal = (movement_id) => {
            // Show Modal
            modal = new bootstrap.Modal(document.getElementById("modalConciliateInvoice"));
            document.querySelector("#modalConciliateInvoiceContent").style.display = "none";
            document.querySelector("#modalConciliateInvoiceSpinner").style.display = "block";
            document.querySelector("#modalConciliateInvoiceTitle").textContent = "Cargando...";
            document.querySelector("#modalConciliateInvoiceSubTitle").textContent = "";
            modal.show();

            // Update Modal Data
            onClickConciliateInvoiceModalUpdateData(movement_id, 'invoice');
            };

            const onClickConciliateInvoiceModalUpdateData = (movement_id, modal_type='invoice', update_table=true) => {
            debugLog("onClickConciliateInvoiceModal: ", movement_id);
            const url = "/sellers/"+dj.seller.shortname+"/reconciliation/get/movement/" + movement_id + "/";
            // Make AJAX Call to REST API ENDPOINT url
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                debugLog(response);
                const data = response && response.data && response.data.length > 0 ? response.data[0] : {};

                if (data.movement.amount == null) {
                    data.movement.amount = 0;
                }

                if (data.movement.amount_euros == null) {
                    data.movement.amount_euros = 0;
                }

                // Set Modal Title
                document.querySelector("#modalConciliateInvoiceTitle").textContent = "Movimiento " + data.movement.movement_number;
                document.querySelector("#modalConciliateInvoiceSubTitle").textContent = data.movement.concept;
                // Set Modal Movement Concept
                document.querySelector("#modalConciliateInvoiceMovementConcept").textContent = data.movement.concept;
                // Set Modal Movement Amount
                document.querySelector("#modalConciliateInvoiceMovementAmount").textContent = data.movement.amount_euros;
                // Set Modal Movement Observation
                document.querySelector("#modalConciliateInvoiceMovementObservation").textContent = data.movement.observation;
                // Set Modal Movement Date
                document.querySelector("#modalConciliateInvoiceMovementDate").textContent = data.movement.movement_date;
                // Set Modal Movement Amount Pending
                document.querySelector("#modalConciliateInvoiceMovementAmountPending").textContent = parseFloat(data.movement.amount_euros).toFixed(2);

                if (data.reconciliations && data.reconciliations.length > 0) {
                    document.querySelector("#firstconcilation").style.display = "flex";
                    document.querySelector("#modalConciliateInvoiceNotConciliated").style.display = "none";
                } else {
                    document.querySelector("#firstconcilation").style.display = "none";
                    document.querySelector("#modalConciliateInvoiceNotConciliated").style.display = "flex";
                }

                const firstconcilation = document.getElementById("firstconcilation");
                while (firstconcilation.firstChild) {
                    firstconcilation.removeChild(firstconcilation.firstChild);
                }

                const concilations = document.getElementById("concilations");
                while (concilations.firstChild) {
                    concilations.removeChild(concilations.firstChild);
                }

                let countReconcilations = 0;
                for (const reconciliation of data.reconciliations) {
                    debugLog("reconciliation: ", reconciliation);

                    let reconciliationTotalEuros = 0;
                    let reconciliationPendingAmount = reconciliation.amount;
                    let reconciliationPendingAmountText = reconciliationPendingAmount + '';

                    if (reconciliation.amount != null) {
                    // Get Pending Amount
                    if (reconciliation.invoice__total_euros != null) {
                        reconciliationTotalEuros = reconciliation.invoice__total_euros;
                    } else if (reconciliation.transfer__total_euros) {
                        reconciliationTotalEuros = reconciliation.transfer__total_euros;
                    } else if (reconciliation.type_id=='account') {
                        reconciliationTotalEuros = reconciliation.amount;
                    } else if (reconciliation.bank__total_euros) {
                        reconciliationTotalEuros = reconciliation.bank__total_euros;
                    }

                    if (!reconciliationTotalEuros) {
                        reconciliationTotalEuros = 0;
                    }

                    if (reconciliationTotalEuros >= reconciliation.amount) {
                        reconciliationPendingAmount = reconciliationTotalEuros - reconciliation.amount;
                    } else {
                        reconciliationPendingAmount = reconciliation.amount - reconciliationTotalEuros;
                    }

                    // Round to 2 decimals
                    reconciliationPendingAmount = parseFloat(reconciliationPendingAmount.toFixed(2));

                    // Set Pending Amount Text
                    if (reconciliationPendingAmount == 0) {
                        reconciliationPendingAmountText =  parseFloat(reconciliationTotalEuros).toFixed(2);
                    } else {
                        reconciliationPendingAmountText = parseFloat(reconciliation.amount).toFixed(2) + ' de ' + parseFloat(reconciliationTotalEuros).toFixed(2);
                    }

                    }

                    let text_first = '';
                    let text_second = '';
                    let href_second = '#';
                    let text_date = '';
                    let text_type = '';
                    if (reconciliation) {
                    if (reconciliation.type_id == 'invoice') {
                        text_type = 'Factura';
                        if (reconciliation.invoice__invoice_category) {
                        text_type += ' ' + reconciliation.invoice__invoice_category.toString().replace('sales', 'Ventas').replace('expenses', 'Gastos');
                        if (reconciliation.invoice__is_rectifying == true || reconciliation.invoice__is_rectifying == 'true') {
                            text_type += ' Rect.';
                        }
                        }
                        text_first = reconciliation.invoice__concept;
                        text_second = reconciliation.invoice__reference;
                        href_second = "/sellers/" + dj.seller.shortname + "/invoice/" + reconciliation.invoice_id + "/";
                        text_date = reconciliation.invoice__accounting_date;
                    } else if (reconciliation.type_id == 'account') {
                        text_type = 'Cuenta Contable';
                        text_first = reconciliation.account__description;
                        text_second = reconciliation.account__id;
                        href_second ='#';
                        text_date = '';
                    } else if (reconciliation.type_id == 'transfer') {
                        text_type = 'Transferencia';
                        text_first = reconciliation.transfer__concept;
                        text_second = reconciliation.transfer__observation;
                        href_second ='#';
                        text_date = reconciliation.transfer__date;
                    } else if (reconciliation.type_id == 'bank') {
                        text_type = 'Banco';
                        text_first = reconciliation.bank__concept;
                        text_second = reconciliation.bank__reference;
                        href_second ='#';
                        text_date = reconciliation.bank__accounting_date;
                    }
                    }

                    const rightDiv = document.createElement("div");
                    rightDiv.className = "col my-0 ms-1 me-3 p-0 d-flex align-items-center border-double";

                    const rowDiv  = document.createElement("div");
                    rowDiv.className="row m-0 p-0 w-100";

                    const colDiv1 = document.createElement("div");
                    colDiv1.className = "col-9 m-0 d-flex align-items-center justify-content-start";
                    const colDiv1H6 = document.createElement("h6");
                    colDiv1H6.className = "mx-0 my-1";
                    const colDiv1H6Span = document.createElement("span");
                    colDiv1H6Span.className = "text-limit-concept";
                    const colDiv1H6SpanText = document.createTextNode(text_first);
                    colDiv1H6Span.appendChild(colDiv1H6SpanText);
                    colDiv1H6.appendChild(colDiv1H6Span);
                    colDiv1.appendChild(colDiv1H6);
                    rowDiv.appendChild(colDiv1);

                    const colDiv2 = document.createElement("div");
                    colDiv2.className="col-3 m-0 d-flex align-items-center justify-content-end";
                    const colDiv2H6 = document.createElement("h6");
                    colDiv2H6.className = "mx-0 my-1";
                    const colDiv2H6B = document.createElement("b");
                    const colDiv2H6BSpan = document.createElement("span");
                    const colDiv2H6BSpanText = document.createTextNode(reconciliationPendingAmountText);
                    const colDiv2H6BText =  document.createTextNode("€ ");
                    colDiv2H6BSpan.appendChild(colDiv2H6BSpanText);
                    colDiv2H6B.appendChild(colDiv2H6BSpan);
                    colDiv2H6B.appendChild(colDiv2H6BText);
                    colDiv2H6.appendChild(colDiv2H6B);
                    colDiv2.appendChild(colDiv2H6);
                    rowDiv.appendChild(colDiv2);

                    const colDiv3 = document.createElement("div");
                    colDiv3.className="col-12 m-0 d-flex align-items-center justify-content-start";
                    const colDiv3H6 = document.createElement("h6");
                    colDiv3H6.className = "mx-0 my-1";
                    const colDiv3H6Span = document.createElement("span");
                    colDiv1H6Span.className = "text-limit-concept";
                    const colDiv3H6SpanText = document.createTextNode(text_type + ': ');
                    const colDiv3H6A = document.createElement("a");
                    colDiv3H6A.textContent  = text_second;
                    colDiv3H6A.href = href_second;
                    colDiv3H6Span.appendChild(colDiv3H6SpanText);
                    colDiv3H6.appendChild(colDiv3H6Span);
                    colDiv3H6.appendChild(colDiv3H6A);
                    colDiv3.appendChild(colDiv3H6);
                    rowDiv.appendChild(colDiv3);

                    const colDiv4 = document.createElement("div");
                    colDiv4.className="col-4 m-0 d-flex align-items-center justify-content-start";
                    const colDiv4H7 = document.createElement("h7");
                    colDiv4H7.className = "mx-0 my-1"
                    const colDiv4H7Span = document.createElement("span");
                    const colDiv4H7SpanText = document.createTextNode(text_date);
                    const colDiv4H7Text = document.createTextNode(' ');
                    colDiv4H7Span.appendChild(colDiv4H7SpanText);
                    colDiv4H7.appendChild(colDiv4H7Span);
                    colDiv4H7.appendChild(colDiv4H7Text);
                    colDiv4.appendChild(colDiv4H7);
                    rowDiv.appendChild(colDiv4);

                    const colDiv5 = document.createElement("div");
                    colDiv5.className="col-8 m-0 d-flex align-items-center justify-content-end";
                    const colDiv5H6 = document.createElement("h6");
                    colDiv5H6.className = "mx-0 my-1";
                    const colDiv5H6B = document.createElement("b");
                    const colDiv5H6BSpan = document.createElement("span");
                    const colDiv5H6BSpanText = document.createTextNode(reconciliationPendingAmount);
                    const colDiv5H6Text = document.createTextNode("€ por Conciliar");
                    colDiv5H6BSpan.appendChild(colDiv5H6BSpanText);
                    colDiv5H6B.appendChild(colDiv5H6BSpan);
                    colDiv5H6.appendChild(colDiv5H6B);
                    colDiv5H6.appendChild(colDiv5H6Text);
                    colDiv5.appendChild(colDiv5H6);
                    rowDiv.appendChild(colDiv5);

                    rightDiv.appendChild(rowDiv);

                    if (countReconcilations == 0) {
                    firstconcilation.appendChild(rightDiv);
                    } else {
                    // 2ND AND OTHER RECONCILATIONS
                    const sepDiv   = document.createElement("div");
                    sepDiv.className = "col-12";

                    const leftDiv  = document.createElement("div");
                    leftDiv.className = "col my-0 ms-3 me-2 p-0 d-flex align-items-center";

                    concilations.appendChild(sepDiv);
                    concilations.appendChild(leftDiv);
                    concilations.appendChild(rightDiv);
                    }

                    countReconcilations++;
                }

                // Set Modal Movement Amount Pending
                let movementPendingAmount = data.movement.amount_euros;
                if (data.movement.amount_euros != null && data.reconciliations_amount != null) {
                console.log("data.movement.amount_euros: ", data.movement.amount_euros);
                console.log("data.reconciliations_amount: ", data.reconciliations_amount);
                if (parseFloat(data.movement.amount_euros) < 0) {
                    if (parseFloat(data.reconciliations_amount) < 0) {
                    console.log("NEGATIVE - NEGATIVE => REST")
                    movementPendingAmount = parseFloat(data.movement.amount_euros) - parseFloat(data.reconciliations_amount);
                    } else {
                    console.log("NEGATIVE - POSITIVE => SUM")
                    movementPendingAmount = parseFloat(data.movement.amount_euros) + parseFloat(data.reconciliations_amount);
                    }
                } else {
                    if (parseFloat(data.reconciliations_amount) < 0) {
                    console.log("POSITIVE - NEGATIVE => SUM")
                    movementPendingAmount = parseFloat(data.movement.amount_euros) + parseFloat(data.reconciliations_amount);
                    } else {
                    console.log("POSITIVE - POSITIVE => REST")
                    movementPendingAmount = parseFloat(data.movement.amount_euros) - parseFloat(data.reconciliations_amount);
                    }
                  }
                }

                // Update the pending amount in the modal header
                const pendingAmountElement = document.querySelector("#modalConciliateInvoiceMovementAmountPending");
                if (pendingAmountElement) {
                    pendingAmountElement.textContent = parseFloat(movementPendingAmount).toFixed(2);
                    // Add a visual indicator that the value has been updated
                    pendingAmountElement.classList.add("text-info");
                    setTimeout(() => {
                        pendingAmountElement.classList.remove("text-info");
                    }, 2000);
                }

                debugLog("movementPendingAmount updated: ", movementPendingAmount);

                if (update_table == true) {
                    const divtable = document.querySelector("#table_invoices_modal");
                    while (divtable.firstChild) {
                    divtable.removeChild(divtable.firstChild);
                    }

                    const movement_id = data.movement.id;
                    const table = document.createElement("table");
                    table.className = 'w-100';
                    let dt_new = null;
                    const options = {
                    paging: true,
                    lengthMenu: [[-1], ["Todos"]],
                    pageLength: -1, // Paginator Default Value
                    searching: true,
                    ordering: true,
                    truncation: true,
                    info: true,
                    footer: true,
                    processing: true,
                    language: {
                        lengthMenu: "_MENU_",
                        zeroRecords: "No se han encontrado movimientos.",
                        info: "_TOTAL_ resultados. ",
                        search: "Buscar:",
                        infoEmpty: "No hay resultados que coincidan con su búsqueda.",
                        loadingRecords: "Cargando...",
                        processing: `
                        <div class="text-center bg-white">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        </div>`,
                        paginate: {
                        "first": "Primero",
                        "last": "Último",
                        "next": "Siguiente",
                        "previous": "Anterior"
                        },
                    },
                    dom: 'lfrtip',
                    // serverSide: true,
                    }
                    if (modal_type == 'invoice') {
                    // Create NEW Invoices DataTable
                    const dataTableInvoicesToConciliateOptions = {
                        ...options,
                        select: false,
                        ajax: {
                        url: '/sellers/'+dj.seller.shortname+'/reconciliation/get/invoices/?movement_id='+movement_id,
                        dataSrc: function(json) {
                            // Procesar los datos para agregar campos de ordenación
                            json.data.forEach(row => {
                                // Crear un campo para ordenar por proveedor/cliente
                                row.provider_sort = '';

                                // Verificar si hay alguna factura conciliada de este proveedor
                                let hasProviderConciliated = false;

                                // Obtener el nombre del proveedor o cliente
                                const providerName = row.provider_name ? row.provider_name.toLowerCase() :
                                                    (row.customer_name ? row.customer_name.toLowerCase() : '');

                                // Si esta factura está conciliada, marcar el proveedor
                                if (row.conciliated === true) {
                                    hasProviderConciliated = true;
                                }

                                // Buscar otras facturas conciliadas del mismo proveedor
                                if (!hasProviderConciliated && providerName) {
                                    for (const otherRow of json.data) {
                                        if (otherRow.conciliated === true) {
                                            const otherProviderName = otherRow.provider_name ? otherRow.provider_name.toLowerCase() :
                                                                    (otherRow.customer_name ? otherRow.customer_name.toLowerCase() : '');

                                            if (providerName === otherProviderName) {
                                                hasProviderConciliated = true;
                                                break;
                                            }
                                        }
                                    }
                                }

                                // Asignar valor para ordenación: primero proveedores conciliados, luego por nombre
                                row.provider_sort = (hasProviderConciliated ? '0_' : '1_') + providerName;
                            });

                            return json.data;
                        }
                        },
                        columns: [
                        { data: 'id', title: 'ID', visible: false },
                        { data: 'reference', title: 'Factura', visible: true, render: function(data, type, row, meta) {
                            let html = '<b>Factura:</b> <a href="/sellers/' + dj.seller.shortname + '/invoice/' + row.id + '/">' + data + '</a>';
                            html += '<br><span>' + row.concept + '</span>';

                            if (row.partially_reconciled) {
                                const reconciled = parseFloat(row.total_euros_reconciled).toFixed(2);
                                const total = parseFloat(row.total_euros).toFixed(2);
                                html += '<br><span class="badge bg-warning text-dark">Parcialmente Conciliada: ' + reconciled + '€ de ' + total + '€</span>';
                            }
                            if (row.provider_name != null && row.provider_name != '') {
                            html += '<br><span><b>Proveedor:</b> ' + row.provider_name + '</span>';
                            if (row.provider_cif != null && row.provider_cif != '') {
                                html += ' (' + row.provider_cif + ')';
                            }
                            } else if (row.customer_name != null && row.customer_name != '') {
                            html += '<br><span><b>Cliente:</b> ' + row.customer_name + '</span>';
                            if (row.customer_cif != null && row.customer_cif != '') {
                                html += ' (' + row.customer_cif + ')';
                            }
                            }
                            return html;
                        } },
                        { data: 'total_euros', title: 'Total (€)', visible: true },
                        { data: 'total_euros_pending', title: 'Pendiente (€)', visible: true },
                        { data: 'dif_total_euros_pending', title: 'Diff (€)', visible: true },
                        { data: 'invoice_date', title: 'Fecha Factura', visible: true, render: function(data, type, row, meta) {
                            let html = data;
                            if (data != null && data != '') {
                            try{
                                const date = new Date(data);
                                const isoDate = date.toISOString().split('T')[0];
                                const spainDate = date.toLocaleDateString('es-ES');
                                html = '<span style="display:none;">' + isoDate + '</span><span>' + spainDate + '</span>';
                            } catch (error) {
                                console.error("Error in parse date:" + error);
                            }
                            }
                            return html;
                        } },
                        { data: 'dif_days', title: 'Diff Dias', visible: false },
                        { data: 'conciliated', title: 'Conciliada', visible: false },
                        { data: 'provider_sort', title: 'Proveedor (ordenación)', visible: false },
                        {
                        data: 'actions',
                        title: 'Acciones',
                        visible: true,
                        orderable: false,
                        render: function(data, type, row, meta) {
                            // Add this at the top of your actions render function
                            let btn_new, btn_new_partial, btn_remove, btn_continue, html = '';

                            // Determine if we're in invoice or transfer mode based on modal_type
                            if (modal_type === 'transfer') {
                                btn_new = '<button type="button" class="btn btn-success btn-sm w-100" onclick="onClickNewConciliate2Transfer('+movement_id+','+row.id+',\'auto\')">Conciliar</button>';
                                btn_new_partial = '<button type="button" class="btn btn-warning btn-sm w-100" onclick="onClickModalConciliateInvoicePartialAmount('+movement_id+','+row.id+','+row.total_euros_pending+')">Parcial</button>';
                                btn_remove = '<button type="button" class="btn btn-danger btn-sm w-100" onclick="onClickRemoveConciliateByMovementTransfer('+movement_id+','+row.id+')">Eliminar</button>';
                            } else {
                                // Invoice mode
                                btn_new = '<button type="button" class="btn btn-success btn-sm w-100" onclick="onClickNewConciliate2Invoice('+movement_id+','+row.id+',\'auto\')">Conciliar</button>';
                                btn_new_partial = '<button type="button" class="btn btn-warning btn-sm w-100" onclick="onClickModalConciliateInvoicePartialAmount('+movement_id+','+row.id+','+row.total_euros_pending+')">Parcial</button>';
                                btn_remove = '<button type="button" class="btn btn-danger btn-sm w-100" onclick="onClickRemoveConciliateByInvoice('+movement_id+','+row.id+')">Eliminar</button>';
                            }

                            // Check if this invoice has a reconciliation with the current movement
                            // This is used to determine whether to show the delete button
                            let hasReconciliationWithCurrentMovement = false;

                            // If the invoice is partially or fully reconciled, check if it's with this movement
                            if (row.partially_reconciled || row.fully_reconciled || row.conciliated) {
                                // Check if this invoice has a reconciliation with this movement
                                if (row.reconciliations_with_movements && Array.isArray(row.reconciliations_with_movements)) {
                                    // The invoice has a list of movements it's reconciled with
                                    hasReconciliationWithCurrentMovement = row.reconciliations_with_movements.includes(parseInt(movement_id));
                                } else {
                                    // Fallback: if the invoice is reconciled and we don't have specific movement info,
                                    // we'll show the delete button (this is the original behavior)
                                    hasReconciliationWithCurrentMovement = true;
                                }
                            }

                            // Fix to handle string values in total_euros_reconciled
                            let reconciledAmount = 0;
                            if (row.total_euros_reconciled !== undefined && row.total_euros_reconciled !== null) {
                                reconciledAmount = Math.abs(parseFloat(row.total_euros_reconciled));
                            }

                            // Render the appropriate buttons based on the row's state
                            if (row.fully_reconciled) {
                                // Fully reconciled - show delete button if it's reconciled with this movement
                                html = hasReconciliationWithCurrentMovement ? btn_remove : '';

                                // Debug log
                                console.log('Fully reconciled invoice ID: ' + row.id +
                                    ' - Has reconciliation with current movement: ' + hasReconciliationWithCurrentMovement);

                            } else if (row.partially_reconciled ||
                                    (reconciledAmount > 0 && Math.abs(parseFloat(row.total_euros_pending || 0)) > 0.01)) {
                                // Partially reconciled - show continue and remove buttons
                                console.log('Found partially reconciled invoice with ID: ' + row.id);

                                // Calculate the actual remaining amount
                                let totalAmount, remaining;

                                if (modal_type === 'transfer') {
                                    totalAmount = Math.abs(parseFloat(row.amount_euros || 0));
                                } else {
                                    // Invoice
                                    totalAmount = Math.abs(parseFloat(row.total_euros || 0));
                                }

                                // Use total_euros_pending if available (more accurate)
                                remaining = Math.abs(parseFloat(row.total_euros_pending || 0));

                                // Fallback to calculated remaining if pending is zero or invalid
                                if (remaining === 0 || isNaN(remaining)) {
                                    remaining = totalAmount - reconciledAmount;
                                }

                                // Ensure remaining is positive and valid
                                remaining = Math.max(0.01, remaining);
                                const remainingFormatted = remaining.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

                                const btn_continue = '<button type="button" class="btn btn-info btn-sm w-100" onclick="onClickModalConciliateInvoicePartialAmount('+movement_id+','+row.id+','+remaining+')">Continuar ('+remainingFormatted+'€)</button>';

                                // Only show the delete button if this invoice has a reconciliation with this movement
                                html = btn_continue;
                                if (hasReconciliationWithCurrentMovement) {
                                    html += ' ' + btn_remove;
                                }
                            } else if (row.conciliated === true) {
                                if (modal_type === 'transfer') {
                                    exists_conciliated = true;
                                }

                                // Only show the delete button if this invoice has a reconciliation with this movement
                                html = hasReconciliationWithCurrentMovement ? btn_remove : '';

                                // Debug log
                                console.log('Conciliated invoice ID: ' + row.id +
                                    ' - Has reconciliation with current movement: ' + hasReconciliationWithCurrentMovement);
                            } else {
                                // Not reconciled - show standard options
                                html = btn_new + ' ' + btn_new_partial;
                            }

                            return html;
                        }
                    }
                        ],
                        columnDefs: [
                        { targets: [0], width: '0%' },
                        { targets: [1], width: '40%' },
                        { targets: [2], width: '15%' },
                        { targets: [3], width: '15%' },
                        { targets: [4], width: '15%' },
                        { targets: [5], width: '15%' },
                        { targets: [6], width: '0%' },
                        { targets: [7], width: '0%' },
                        { targets: [8], width: '0%' },
                        { targets: [9], width: '15%' },
                        ],
                        order: [[8, "asc"], [9, "desc"], [7, "desc"], [4, "asc"], [6, "asc"]],
                    };
                    table.id = 'invoicesToConciliate';
                    dt_new = $(table).DataTable(dataTableInvoicesToConciliateOptions);


                    } else if (modal_type == 'transfer') {
                    let exists_conciliated = false;
                    const dataTableTransfersToConciliateOptions = {
                        ...options,
                        ajax: {
                        url: '/sellers/'+dj.seller.shortname+'/reconciliation/get/movements/?movement_id='+movement_id,
                        dataSrc: 'data'
                        },
                        columns: [
                        { data: 'id', title: 'ID', visible: false },
                        { data: 'movement_number', title: 'Nº Movimiento', visible: true, render: function(data, type, row, meta) {
                            let html = '<b>Movimiento:</b> ' + data;
                            html += '<br><span>' + row.concept + '</span>';
                            if (row.observation != null && row.observation != '') {
                            html += '<br><span><b>' + row.observation + '</span>';
                            }
                            return html;
                        } },
                        { data: 'amount_euros', title: 'Total (€)', visible: true },
                        { data: 'amount_euros_pending', title: 'Pendiente (€)', visible: true },
                        { data: 'dif_total_euros_pending', title: 'Diff (€)', visible: true },
                        { data: 'movement_date', title: 'Fecha Movimiento', visible: true, render: function(data, type, row, meta) {
                            let html = data;
                            if (data != null && data != '') {
                            try{
                                const date = new Date(data);
                                const isoDate = date.toISOString().split('T')[0];
                                const spainDate = date.toLocaleDateString('es-ES');
                                html = '<span style="display:none;">' + isoDate + '</span><span>' + spainDate + '</span>';
                            } catch (error) {
                                console.error("Error in parse date:" + error);
                            }
                            }
                            return html;
                        } },
                        { data: 'dif_days', title: 'Diff Dias', visible: false },
                        { data: 'conciliated', title: 'Conciliada', visible: false },
                        { data: 'actions', title: 'Acciones', visible: true, orderable:false, render: function(data, type, row, meta) {
                            const btn_new = '<button type="button" class="btn btn-success btn-sm w-100" onclick="onClickNewConciliate2Transfer('+movement_id+','+row.id+',\'auto\')">Conciliar</button>';
                            // const btn_new_partial = '<button type="button" class="btn btn-warning btn-sm w-100" onclick="onClickModalConciliateInvoicePartialAmount('+movement_id+','+row.id+','+row.total_euros_pending+')">Parcial</button>';
                            const btn_remove = '<button type="button" class="btn btn-danger btn-sm w-100" onclick="onClickRemoveConciliateByMovementTransfer('+movement_id+','+row.id+')">Eliminar</button>';
                            let html = '';
                            if (row.conciliated == true) {
                            exists_conciliated = true;
                            html = btn_remove;
                            } else if (exists_conciliated != true) {
                            html = btn_new;
                            }
                            return html;
                        } },
                        ],
                        columnDefs: [
                        { targets: [0], width: '0%' },
                        { targets: [1], width: '40%' },
                        { targets: [2], width: '12%' },
                        { targets: [3], width: '12%' },
                        { targets: [5], width: '12%' },
                        { targets: [4], width: '12%' },
                        { targets: [6], width: '0%' },
                        { targets: [7], width: '0%' },
                        { targets: [8], width: '12%' },
                        ],
                        order: [[7, "desc"], [4, "asc"], [6, "asc"]],
                    };
                    table.id = 'transfersToConciliate';
                    dt_new = $(table).DataTable(dataTableTransfersToConciliateOptions);
                    }
                    divtable.appendChild(table);

                    // Search Input
                    const searchInput = $("#searchModalInvoice");
                    if (searchInput) {
                    searchInput.off("input");
                    if (dt_new != null) {
                        // Search Input
                        searchInput.on("input", function(){
                        const filtro = $(this).val();
                        debugLog(filtro)
                        dt_new.search(filtro).draw();
                        });
                    }
                    }
                }

                // Hide Spinner and Show Content
                const modalContent = document.querySelector("#modalConciliateInvoiceContent");
                const modalSpinner = document.querySelector("#modalConciliateInvoiceSpinner");
                modalContent.style.display = "flex";
                modalSpinner.style.display = "none";
                },
                error: function(xhr, status, error) {
                console.error(xhr.responseText);
                // Handle any errors that occur during the AJAX call
                }
            });
        };

        const onClickConciliateTransferModal = (movement_id) => {
            // Show Modal
            modal = new bootstrap.Modal(document.getElementById("modalConciliateInvoice"));
            document.querySelector("#modalConciliateInvoiceContent").style.display = "none";
            document.querySelector("#modalConciliateInvoiceSpinner").style.display = "block";
            document.querySelector("#modalConciliateInvoiceTitle").textContent = "Cargando ...";
            document.querySelector("#modalConciliateInvoiceSubTitle").textContent = "";
            modal.show();

            // Update Modal Data
            onClickConciliateInvoiceModalUpdateData(movement_id, 'transfer');
        };

        const onClickConciliateAccountModal = (movement_id, amount = 0) => {
            debugLog("onClickConciliateAccountModal: ", movement_id);
            const account_modal =  $("#modalConciliateAccount");
            const prev = $("#modalConciliateAccountPrev");
            const prev_div = $("#modalConciliateAccountPrevDiv");
            const select = $("#modalConciliateAccountInputBank");
            const input = $("#modalConciliateAccountInputAmount");
            const btn =  $("#modalConciliateAccountBtn");
            const content = $("#modalConciliateAccountContent");

            if (amount == null) {
                amount = 0;
            } else {
                amount = parseFloat(amount);
            }

            input.val(amount);

            if (amount >= 0) {
                input.attr("min", 0);
                input.attr("max", amount);
            } else {
                input.attr("min", amount);
                input.attr("max", 0);
            }
            input.attr("step", 0.01);

            // Hide Prev div
            prev.hide();

            // Hide Content
            content.hide();

            // Enable all from select values
            select.find("option").attr('disabled', false);

            // Set bank_id = 0 from select values
            select.val(null).trigger('change');

            // Disable bank_id from select values
            select.find("option[value='0']").attr('disabled', true);

            // Enable Input, Select (disabled = false)
            select.attr("disabled", false);
            input.attr("disabled", false);

            // Disable Btn (disabled = true)
            btn.attr("disabled", true);

            // Remove all previous events
            btn.off("click");
            input.off("input");

            // Add new click event => Disable Inputs, Conciliate and Hide Modal.
            btn.on("click", function() {
                // Disable Input, Select, Btn
                select.attr("disabled", true);
                input.attr("disabled", true);
                btn.attr("disabled", true);
                debugLog("Input Value: ", input.val());
                debugLog("select Value: ", select.val());

                // Call to Conciliate Banks
                onClickNewConciliate2Account(movement_id, select.val(), input.val());

                // Hide Modal
                account_modal.modal("hide");
            });

            // Add new input event => Enable or Disable Btn
            input.on("input", function() {
                debugLog("input change: ", input.val());
                if (select.val() != null && select.val() != undefined && select.val() != 0 && select.val() != selectedCard) {
                if (amount >= 0){
                    if (input.val() > 0 && input.val() <= amount) {
                    btn.attr("disabled", false);
                    } else {
                    btn.attr("disabled", true);
                    }
                } else {
                    if (input.val() < 0 && input.val() >= amount) {
                    btn.attr("disabled", false);
                    } else {
                    btn.attr("disabled", true);
                    }
                }
                } else {
                debugLog("select.val():", select.val());
                btn.attr("disabled", true);
                }
            });

            select.on("change", function() {
                debugLog("change select: ", select.val());
                if (select.val() != null && select.val() != undefined && select.val() != 0 && select.val() != selectedCard) {
                if (amount >= 0){
                    if (input.val() > 0 && input.val() <= amount) {
                    btn.attr("disabled", false);
                    } else {
                    btn.attr("disabled", true);
                    }
                } else {
                    if (input.val() < 0 && input.val() >= amount) {
                    btn.attr("disabled", false);
                    } else {
                    btn.attr("disabled", true);
                    }
                }
                } else {
                debugLog("select.val():", select.val());
                btn.attr("disabled", true);
                }
            });

            // Update Modal Data
            updateConciliateAccountModalPrevious(movement_id);

            // Show Modal
            account_modal.modal("show");
        };

        const updateConciliateAccountModalPrevious = (movement_id) => {
            const url = "/sellers/"+dj.seller.shortname+"/reconciliation/get/movement/"+movement_id+"/";
            const prev = $("#modalConciliateAccountPrev");
            const prev_div = $("#modalConciliateAccountPrevDiv");
            const prev_spinner = $("#modalConciliateAccountPrevSpinner");
            const content = $("#modalConciliateAccountContent");
            debugLog("modalConciliateAccountPrevSpinner: " + prev_spinner);

            // Hide Prev div
            prev.hide();

            // Remove all previous
            prev_div.empty();

            // Show Spinner
            prev_spinner.show();
            debugLog('previ_spinner show');

            // Make AJAX Call to REST API ENDPOINT url
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                debugLog(response);
                const data = response && response.data && response.data.length > 0 ? response.data[0] : {};
                debugLog(data);

                // Remove all previous
                prev_div.empty();

                // Hide Spinner
                prev_spinner.hide();

                if (data && data.reconciliations && data.reconciliations.length > 0) {
                    rec_fileted = data.reconciliations.filter(function(rec) { return rec.type_id == 'account'; });
                    if (rec_fileted && rec_fileted.length > 0) {
                    for (const rec of rec_fileted) {
                        const text_first  = rec.account__id + " - " + rec.account__description;
                        const text_amount = rec.amount;
                        const text_date   = rec.modified_at.toString().substring(0, 9);
                        // Create Previous Div
                        const previous = document.createElement("div");
                        previous.className = "col my-0 d-flex align-items-center border-double";

                        const rowDiv  = document.createElement("div");
                        rowDiv.className="row m-0 p-0 w-100";

                        const colDiv1 = document.createElement("div");
                        colDiv1.className = "col-9 m-0 d-flex align-items-center justify-content-start";
                        const colDiv1H6 = document.createElement("h6");
                        colDiv1H6.className = "mx-0 my-1";
                        const colDiv1H6Span = document.createElement("span");
                        colDiv1H6Span.className = "text-limit-concept";
                        const colDiv1H6SpanText = document.createTextNode(text_first);
                        colDiv1H6Span.appendChild(colDiv1H6SpanText);
                        colDiv1H6.appendChild(colDiv1H6Span);
                        colDiv1.appendChild(colDiv1H6);
                        rowDiv.appendChild(colDiv1);

                        const colDiv2 = document.createElement("div");
                        colDiv2.className="col-3 m-0 d-flex align-items-center justify-content-end";
                        const colDiv2H6 = document.createElement("h6");
                        colDiv2H6.className = "mx-0 my-1";
                        const colDiv2H6B = document.createElement("b");
                        const colDiv2H6BSpan = document.createElement("span");
                        const colDiv2H6BSpanText = document.createTextNode(text_amount);
                        const colDiv2H6BText =  document.createTextNode("€ ");
                        colDiv2H6BSpan.appendChild(colDiv2H6BSpanText);
                        colDiv2H6B.appendChild(colDiv2H6BSpan);
                        colDiv2H6B.appendChild(colDiv2H6BText);
                        colDiv2H6.appendChild(colDiv2H6B);
                        colDiv2.appendChild(colDiv2H6);
                        rowDiv.appendChild(colDiv2);

                        const colDiv4 = document.createElement("div");
                        colDiv4.className="col-4 m-0 d-flex align-items-center justify-content-start";
                        const colDiv4H7 = document.createElement("h7");
                        colDiv4H7.className = "mx-0 my-1"
                        const colDiv4H7Span = document.createElement("span");
                        const colDiv4H7SpanText = document.createTextNode(text_date);
                        const colDiv4H7Text = document.createTextNode(' ');
                        colDiv4H7Span.appendChild(colDiv4H7SpanText);
                        colDiv4H7.appendChild(colDiv4H7Span);
                        colDiv4H7.appendChild(colDiv4H7Text);
                        colDiv4.appendChild(colDiv4H7);
                        rowDiv.appendChild(colDiv4);


                        const colDiv5 = document.createElement("div");
                        colDiv5.className="col-8 m-0 d-flex align-items-center justify-content-end";
                        const colDiv5Btn = document.createElement("button");
                        colDiv5Btn.className = "btn btn-link m-1 p-1";
                        colDiv5Btn.setAttribute("data-bs-toggle", "tooltip");
                        colDiv5Btn.setAttribute("data-bs-placement", "top");
                        colDiv5Btn.setAttribute("title", "Deshacer Conciliacion");
                        colDiv5Btn.onclick = function() { onClickRemoveConciliateByConciliateId(rec.movement_id, rec.id); };
                        const colDiv5BtnImg = document.createElement("img");
                        colDiv5BtnImg.src = "{% static 'assets/images/banks/undo.svg' %}";
                        colDiv5BtnImg.alt = "Deshacer";
                        colDiv5Btn.appendChild(colDiv5BtnImg);
                        colDiv5.appendChild(colDiv5Btn);
                        rowDiv.appendChild(colDiv5);

                        // Add to prev_div
                        previous.appendChild(rowDiv);
                        prev_div.append(previous);
                    }
                    prev.show();
                    }
                }

                // Show Content
                content.show();
                }
            });

            }

        const onClickNewConciliate2Invoice = (movement_id, invoice_id, amount) => {
            const url = "/sellers/"+dj.seller.shortname+"/reconciliation/new/?movement_id="+movement_id+"&invoice_id="+invoice_id+"&amount="+amount;
            debugLog("onClickNewConciliate2Invoice URL: ", url);
            hideOneInTableConcilations(movement_id);

            // Make AJAX Call to REST API ENDPOINT url
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    debugLog("Respuesta de conciliación:", response);
                    const data = response && response.data && response.data.length > 0 ? response.data[0] : {};

                    // Actualizar la tabla de conciliaciones
                    updateOneInTableConcilations(movement_id, invoice_id);

                    // Recargar la tabla para actualizar la ordenación por proveedor
                    const dtInvoices = $('#invoicesToConciliate').DataTable();
                    dtInvoices.ajax.reload(function() {
                        // Aplicar la ordenación: primero por provider_sort, luego por conciliated, etc.
                        dtInvoices.order([[8, "asc"], [9, "desc"], [7, "desc"], [4, "asc"], [6, "asc"]]).draw();
                        debugLog('Tabla reordenada después de conciliar');
                    }, false);

                    // Actualizar los datos del modal para reflejar la conciliación parcial
                    onClickConciliateInvoiceModalUpdateData(movement_id, 'invoice', false);

                    // Actualizar el saldo del banco
                    if (response.bank_id) {
                        updateBankBalance(response.bank_id);
                    } else {
                        console.warn("No se pudo actualizar saldo tras conciliación: bank_id no disponible en la respuesta.");
                    }

                    // Only close the modal if this is not a partial conciliation
                    // For partial conciliations, we keep the modal open so users can continue conciliating
                    if (amount !== 'auto' && amount !== null && amount !== undefined && amount !== '') {
                        // This is a partial conciliation, don't close the modal
                        debugLog('Partial conciliation detected, keeping modal open');
                    } else {
                        // This is a full conciliation, close the modal
                        const modalElement = document.getElementById('modalConciliateInvoice');
                        if (modalElement) {
                            const modalInstance = bootstrap.Modal.getInstance(modalElement);
                            if (modalInstance) {
                                modalInstance.hide();
                                debugLog('Modal closed after full conciliation');
                            }
                        }
                    }

                    // Show a success toast notification
                    const Toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: (toast) => {
                            toast.addEventListener('mouseenter', Swal.stopTimer);
                            toast.addEventListener('mouseleave', Swal.resumeTimer);
                        }
                    });

                    Toast.fire({
                        icon: 'success',
                        title: 'Conciliación realizada correctamente'
                    });
                },
                error: function(error) {
                    console.error("Error en conciliación:", error);

                    // Show error notification
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la conciliación',
                        text: 'No se pudo completar la conciliación. Por favor, inténtelo de nuevo.'
                    });
                }
            });
        };

        const onClickNewConciliate2Account = (movement_id, account_id, amount) => {
            const url = "/sellers/"+dj.seller.shortname+"/reconciliation/new/?movement_id="+movement_id+"&account_id="+account_id+"&amount="+amount;
            debugLog("onClickNewConciliate2Bank URL: ", url);
            hideOneInTableConcilations(movement_id);
            // Make AJAX Call to REST API ENDPOINT url
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                debugLog(response);
                const data = response && response.data && response.data.length > 0 ? response.data[0] : {};
                debugLog(data);
                updateOneInTableConcilations(movement_id, null);
                handleBankUpdateFromResponse(response);
                }
            });
        };

        const onClickNewConciliate2Transfer = (movement_id, transfer_id, amount = 'auto') => {
            const url = "/sellers/"+dj.seller.shortname+"/reconciliation/new/?movement_id="+movement_id+"&movement_transfer_id="+transfer_id+"&amount="+amount;
            debugLog("onClickNewConciliate2Transfer URL: ", url);
            hideOneInTableConcilations(movement_id);

            // Make AJAX Call to REST API ENDPOINT url
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    debugLog("Respuesta de conciliación de transferencia:", response);
                    const data = response && response.data && response.data.length > 0 ? response.data[0] : {};

                    // Actualizar la tabla de conciliaciones
                    updateOneInTableConcilations(movement_id, null, transfer_id);

                    // Recargar la tabla de transferencias en el modal
                    const dtTransfers = $('#transfersToConciliate').DataTable();
                    if (dtTransfers) {
                        dtTransfers.ajax.reload(function() {
                            // Aplicar la ordenación
                            dtTransfers.order([[7, "desc"], [4, "asc"], [6, "asc"]]).draw();
                            debugLog('Tabla de transferencias reordenada después de conciliar');
                        }, false);
                    }

                    // Actualizar los datos del modal para reflejar la conciliación
                    onClickConciliateInvoiceModalUpdateData(movement_id, 'transfer', false);

                    // Actualizar el saldo del banco
                    if (response.bank_id) {
                        updateBankBalance(response.bank_id);
                    } else {
                        console.warn("No se pudo actualizar saldo tras conciliación: bank_id no disponible en la respuesta.");
                    }

                    // Solo cerrar el modal si no es una conciliación parcial
                    if (amount !== 'auto' && amount !== null && amount !== undefined && amount !== '') {
                        // Es una conciliación parcial, mantener el modal abierto
                        debugLog('Conciliación parcial de transferencia detectada, manteniendo modal abierto');
                    } else {
                        // Es una conciliación completa, cerrar el modal
                        const modalElement = document.getElementById('modalConciliateInvoice');
                        if (modalElement) {
                            const modalInstance = bootstrap.Modal.getInstance(modalElement);
                            if (modalInstance) {
                                modalInstance.hide();
                                debugLog('Modal cerrado después de conciliación completa de transferencia');
                            }
                        }
                    }

                    // Mostrar notificación de éxito
                    const Toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: (toast) => {
                            toast.addEventListener('mouseenter', Swal.stopTimer);
                            toast.addEventListener('mouseleave', Swal.resumeTimer);
                        }
                    });

                    Toast.fire({
                        icon: 'success',
                        title: 'Conciliación de transferencia realizada correctamente'
                    });
                },
                error: function(error) {
                    console.error("Error en conciliación de transferencia:", error);

                    // Mostrar notificación de error
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la conciliación',
                        text: 'No se pudo completar la conciliación de transferencia. Por favor, inténtelo de nuevo.'
                    });
                }
            });
        };

        const onClickRemoveAllConciliate = (movement_id) => {
            // Llamar a onClickRemoveConciliate con un callback para manejar errores
            onClickRemoveConciliate(movement_id, null, null, null, function() {
                // Recargar la tabla de conciliaciones para reflejar los cambios
                const bankId = selectedCard;
                if (bankId) {
                    const dtConciliate = $(`#conciliate_bank_${bankId}`).DataTable();
                    if (dtConciliate) {
                        dtConciliate.ajax.reload(null, false);
                        debugLog('Tabla de conciliaciones recargada después de deshacer todas las conciliaciones');
                    }
                }

                // Cerrar el modal si está abierto
                const modalConciliateInvoice = document.getElementById('modalConciliateInvoice');
                if (modalConciliateInvoice && modalConciliateInvoice.classList.contains('show')) {
                    const modal = bootstrap.Modal.getInstance(modalConciliateInvoice);
                    if (modal) {
                        modal.hide();
                    }
                }
            });
        };

        const onClickRemoveConciliateByInvoice = (currentMovementId, invoiceId, originalMovementId = null) => {
            // Show a loading indicator
            Swal.fire({
                title: 'Procesando...',
                text: 'Eliminando conciliación',
                allowOutsideClick: false,
                customClass: {
                    container: 'swal-overlay-z-index'
                },
                didOpen: () => {
                    Swal.showLoading();
                    // Ensure the Swal container has a higher z-index than the modal
                    document.querySelector('.swal-overlay-z-index').style.zIndex = 2000;
                }
            });

            // If originalMovementId is provided, use it instead of currentMovementId
            const movementIdToUse = originalMovementId || currentMovementId;

            onClickRemoveConciliate(movementIdToUse, null, invoiceId, null, function(response) {
                // Close the loading indicator
                Swal.close();

                // Get the tables to refresh
                const currentBankId = selectedCard;
                const dtMovements = $(`#movements_bank_${currentBankId}`).DataTable();
                const dtConciliate = $(`#conciliate_bank_${currentBankId}`).DataTable();
                const dtInvoices = $('#invoicesToConciliate').DataTable();

                // Reload the invoices table with proper sorting
                if (dtInvoices) {
                    dtInvoices.ajax.reload(function() {
                        dtInvoices.order([[8, "asc"], [9, "desc"], [7, "desc"], [4, "asc"], [6, "asc"]]).draw();
                    }, false);
                }

                // Update the current movement's UI and tables
                if (dtMovements) dtMovements.ajax.reload(null, false);
                if (dtConciliate) dtConciliate.ajax.reload(null, false);

                // Update the current movement in the modal (if open)
                onClickConciliateInvoiceModalUpdateData(currentMovementId, 'invoice', false);

                // Update the bank balance for the current bank
                updateBankBalance(currentBankId);

                // If the response contains data about an original movement from a different bank
                if (response && response.original_movement_id && response.original_bank_id) {
                    // If it's a different bank than the current one, update that bank's tables and balance
                    if (response.original_bank_id !== currentBankId) {
                        const origBankId = response.original_bank_id;
                        const origMovementId = response.original_movement_id;

                        // Update the other bank's tables if they're initialized
                        const origDtMovements = $(`#movements_bank_${origBankId}`).DataTable();
                        const origDtConciliate = $(`#conciliate_bank_${origBankId}`).DataTable();

                        if (origDtMovements) origDtMovements.ajax.reload(null, false);
                        if (origDtConciliate) origDtConciliate.ajax.reload(null, false);

                        // Update the other bank's balance
                        updateBankBalance(origBankId);

                        // Show a notification that another bank was updated
                        const Toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true
                        });

                        Toast.fire({
                            icon: 'info',
                            title: `Se actualizó también el saldo del banco #${origBankId}`
                        });
                    }
                }

                // Check if we need to update the status of the original movement
                if (originalMovementId && originalMovementId !== currentMovementId) {
                    // Update the UI for the original movement if it's displayed
                    const originalMovRow = document.querySelector(`#bank_${currentBankId}_movement_${originalMovementId}_status_dtcon`);
                    if (originalMovRow) {
                        // Refresh this row's status
                        updateOneInTableConcilations(originalMovementId, null, null, false);
                    }
                }
            });
        };

        const onClickRemoveConciliateByMovementTransfer = (movement_id, movement_transfer_id) => {
            onClickRemoveConciliate(movement_id, null, null, movement_transfer_id);
        };

        const onClickRemoveConciliateByConciliateId = (movement_id, conciliate_id) => {
            onClickRemoveConciliate(movement_id, conciliate_id, null, null);
        };

        const onClickRemoveConciliate = async (movement_id, conciliate_id = null, invoice_id = null, movement_transfer_id = null, callback = null) => {
            try {
                let url = `/sellers/${dj.seller.shortname}/reconciliation/remove/?movement_id=${movement_id}`;
                if (conciliate_id) {
                    url += `&reconcilation_id=${conciliate_id}`;
                }
                if (invoice_id) {
                    url += `&invoice_id=${invoice_id}`;
                }
                if (movement_transfer_id) {
                    url += `&movement_transfer_id=${movement_transfer_id}`;
                    movement_id = movement_transfer_id;
                }

                // Oculta la fila correspondiente
                hideOneInTableConcilations(movement_id);

                // Realiza la llamada AJAX
                const response = await $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json'
                });

                debugLog("Respuesta al deshacer conciliación:", response);



                // Verificar si la respuesta contiene datos válidos
                if (response && response.result === 'ok') {
                    const data = response.data && response.data.length > 0 ? response.data : [];
                    debugLog("Datos de conciliación eliminada:", data);

                    if (data.length > 1) {
                        data.forEach(function (d) {
                            if (d.movement !== null && d.movement === movement_id && d.type === 'transfer') {
                                movement_transfer_id = d.movement_transfer;
                            }
                        });
                    }

                    // Actualiza la tabla y el modal solo si hay datos válidos
                    try {
                        updateOneInTableConcilations(movement_id, invoice_id, movement_transfer_id);
                        updateConciliateAccountModalPrevious(movement_id);
                    } catch (updateError) {
                        console.warn("No se pudo actualizar la tabla de conciliaciones:", updateError);
                        // No interrumpir el flujo por errores en la actualización de la UI
                    }

                    // Actualiza solo el banco afectado
                    if (response.bank_id) {
                        try {
                            await updateBankBalance(response.bank_id);
                        } catch (bankError) {
                            console.warn("Error al actualizar el saldo del banco:", bankError);
                        }
                    } else {
                        console.warn("No se pudo actualizar saldo: bank_id no disponible en la respuesta.");
                    }
                } else {
                    // Si la respuesta indica error, mostrar mensaje
                    console.warn("Error al deshacer conciliación:", response.message || 'Error desconocido');

                    // Mostrar notificación al usuario
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'info',
                            title: 'Conciliación eliminada',
                            text: 'Las facturas han sido liberadas y pueden ser conciliadas nuevamente.',
                            timer: 3000
                        });
                    }
                }

            } catch (error) {
                console.error("Error al eliminar la conciliación:", error);
            } finally {
                // Ejecutar callback si existe, independientemente del resultado
                if (typeof callback === 'function') {
                    callback();
                }
            }
        };

        const hideOneInTableConcilations = (movement_id, movement_bank=null) => {
            if (movement_bank == null) {
                movement_bank = selectedCard;
            }

            // Remove content from status
            const dtmv_id = "#bank_" + movement_bank + "_movement_" + movement_id + "_status_dtmv";
            const div_status_dtmv = document.querySelector("#bank_" + movement_bank + "_movement_" + movement_id + "_status_dtmv");
            const div_status_dtcon = document.querySelector("#bank_" + movement_bank + "_movement_" + movement_id + "_status_dtcon");


            if (div_status_dtmv != null) {
                while (div_status_dtmv.firstChild) {
                div_status_dtmv.removeChild(div_status_dtmv.firstChild);
                }
            }

            if (div_status_dtcon != null) {
                while (div_status_dtcon.firstChild) {
                div_status_dtcon.removeChild(div_status_dtcon.firstChild);
                }
            }

            // Remove content from Concilation
            const div_concilations = document.querySelector("#bank_" + movement_bank + "_movement_" + movement_id + "_conciliations");
            if (div_concilations != null) {
                while (div_concilations.firstChild) {
                div_concilations.removeChild(div_concilations.firstChild);
                }
            }

            // Hide Acctions
            const div_notpending = document.querySelector("#div_not_pending_" + movement_id);
            if (div_notpending != null) {
                div_notpending.style.display = "none";
            }
            const div_notconciliated = document.querySelector("#div_not_conciliated_" + movement_id);
            if (div_notconciliated != null) {
                div_notconciliated.style.display = "none";
            }

            // Make and Add spinner <div class="spinner-grow text-primary" role="status"></div>
            const div_spinner_status= document.createElement("div");
            if (div_spinner_status != null) {
                div_spinner_status.className = "spinner-border";
                div_spinner_status.role = "status";
                // div_status_dtcon.appendChild(div_spinner_status);
            }
            const div_spinner_concilations= document.createElement("div");
            if (div_spinner_concilations != null) {
                div_spinner_concilations.className = "spinner-border";
                div_spinner_concilations.role = "status";
                // div_concilations.appendChild(div_spinner_concilations);
            }
            // div_status_dtmv.appendChild(div_spinner_status);
        };

        const updateOneInTableConcilations = (movement_id, invoice_id = null, transfer_id = null, hide = true) => {
            // Maeke Ajax Call to Endpoint url
            const url1 = "/sellers/" + dj.seller.shortname + "/reconciliation/get/movement/" + movement_id + "/";
            const url2 = "/sellers/" + dj.seller.shortname + "/reconciliation/get/invoices/?movement_id=" + movement_id + "&invoice_id=" + invoice_id;

            if (hide) {
                hideOneInTableConcilations(movement_id);
            }

            $.ajax({
                url: url1,
                type: 'GET',
                dataType: 'json',
                success: function (response) {
                    const data = response && response.data && response.data.length > 0 ? response.data[0] : {};
                    debugLog("URL1 Data: ", data);

                    // Update the reconciliation information in the modal if it's open
                    const modalElement = document.getElementById('modalConciliateInvoice');
                    if (modalElement && modalElement.classList.contains('show')) {
                        // Update the modal data to reflect the current state of reconciliation
                        onClickConciliateInvoiceModalUpdateData(movement_id, 'invoice', false);
                    }

                    if (data && data != null && data != {} && data.movement != null) {

                        if (invoice_id) {
                            // UPDATE TABLE INVOICES IN MODAL
                            $.ajax({
                                url: url2,
                                type: 'GET',
                                dataType: 'json',
                                success: function (response2) {
                                    const data2 = response2 && response2.data && response2.data.length > 0 ? response2.data[0] : {};
                                    debugLog("URL2 Data: ", data2);

                                    // Calc Movement Pending Amount
                                    let movementPendingAmount = data.movement.amount_euros;
                                    if (data.movement.amount_euros != null && data.reconciliations_amount != null) {
                                    console.log("data.movement.amount_euros: ", data.movement.amount_euros);
                                    console.log("data.reconciliations_amount: ", data.reconciliations_amount);
                                    if (parseFloat(data.movement.amount_euros) < 0) {
                                        if (parseFloat(data.reconciliations_amount) < 0) {
                                        console.log("NEGATIVE - NEGATIVE => REST")
                                        movementPendingAmount = parseFloat(data.movement.amount_euros) - parseFloat(data.reconciliations_amount);
                                        } else {
                                        console.log("NEGATIVE - POSITIVE => SUM")
                                        movementPendingAmount = parseFloat(data.movement.amount_euros) + parseFloat(data.reconciliations_amount);
                                        }
                                    } else {
                                        if (parseFloat(data.reconciliations_amount) < 0) {
                                        console.log("POSITIVE - NEGATIVE => SUM")
                                        movementPendingAmount = parseFloat(data.movement.amount_euros) + parseFloat(data.reconciliations_amount);
                                        } else {
                                        console.log("POSITIVE - POSITIVE => REST")
                                        movementPendingAmount = parseFloat(data.movement.amount_euros) - parseFloat(data.reconciliations_amount);
                                        }
                                    }
                                    }
                                    movementPendingAmount = parseFloat(parseFloat(movementPendingAmount).toFixed(2));

                                    // Update Invoice Modal Row Table
                                    const dtInvoices = $('#invoicesToConciliate').DataTable();

                                    const rows_eq0 = dtInvoices.rows().eq(0);
                                    if (rows_eq0) {
                                        // UPDATE ALL ROWS
                                        for (const index of dtInvoices.rows().eq(0).toArray()) {
                                            const rowData = dtInvoices.row(index).data();

                                            let diff = movementPendingAmount - rowData.total_euros_pending;
                                            if (movementPendingAmount != null && rowData.total_euros_pending != null) {
                                                if (movementPendingAmount < 0) {
                                                    if (rowData.total_euros_pending < 0) {
                                                        diff = movementPendingAmount - rowData.total_euros_pending;
                                                    } else {
                                                        diff = movementPendingAmount + rowData.total_euros_pending;
                                                    }
                                                } else {
                                                    if (rowData.total_euros_pending < 0) {
                                                        diff = movementPendingAmount + rowData.total_euros_pending;
                                                    } else {
                                                        diff = movementPendingAmount - rowData.total_euros_pending;
                                                    }
                                                }
                                            }

                                            if (diff < 0) {
                                                diff = diff * -1;
                                            }
                                            diff = diff.toFixed(2);

                                            if (rowData.conciliated == true) {
                                                rowData.dif_total_euros_pending = 0;
                                            } else {
                                                rowData.dif_total_euros_pending = diff;
                                            }

                                            dtInvoices.row(index).data(rowData);
                                        }

                                        // Get all row nodes
                                        const rowNodes = dtInvoices.rows().nodes();

                                        // Find the index of the row with the correct id
                                        let rowIndex = -1;
                                        rowIndex = dtInvoices.rows().eq(0).filter(function (index) {
                                            return dtInvoices.cell(index, 0).data() == invoice_id; // Assuming the ID is in the first column
                                        });

                                        if (rowIndex !== -1) {
                                            const rowData = dtInvoices.row(rowIndex).data();

                                            if (rowData && data2) {
                                                rowData.conciliated = data2.conciliated;
                                                rowData.total_euros_pending = data2.total_euros_pending;
                                                rowData.dif_total_euros_pending = data2.dif_total_euros_pending;
                                                rowData.dif_days = data2.dif_days;
                                            }

                                            dtInvoices.row(rowIndex).data(rowData); //.draw(false);
                                        } else {
                                            console.error("Row not found");
                                        }

                                        dtInvoices.draw(false);
                                    }
                                }
                            });
                        }

                        // Remove content from status
                        const div_status_dtmv = document.querySelector("#bank_" + data.movement.bank + "_movement_" + movement_id + "_status_dtmv");
                        debugLog("div_status_dtmv: ", div_status_dtmv);

                        const div_status_dtcon = document.querySelector("#bank_" + data.movement.bank + "_movement_" + movement_id + "_status_dtcon");
                        debugLog("div_status_dtcon: ", div_status_dtcon);

                        if (div_status_dtmv != null) {
                            while (div_status_dtmv.firstChild) {
                                div_status_dtmv.removeChild(div_status_dtmv.firstChild);
                            }
                        }

                        if (div_status_dtcon != null) {
                            while (div_status_dtcon.firstChild) {
                                div_status_dtcon.removeChild(div_status_dtcon.firstChild);
                            }
                        }

                        // Remove content from Concilation
                        const div_concilations = document.querySelector("#bank_" + data.movement.bank + "_movement_" + movement_id + "_conciliations");
                        debugLog("div_concilations: ", div_concilations);
                        if (div_concilations != null) {
                            while (div_concilations.firstChild) {
                                div_concilations.removeChild(div_concilations.firstChild);
                            }
                        }

                        // Add content to status
                        let html = '';
                        switch (data.movement.status) {
                            case 'pending':
                                html = `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color:#e9d412;">
                                        <b>Pendiente</b>
                                    </div>
                                `;
                                break;
                            case 'partially-conciliated':
                                html = `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color:#FF9103;">
                                        <b>Parcialmente Conciliado</b>
                                    </div>
                                `;
                                break;
                            case 'conciliated':
                                html = `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color:#198754;">
                                        <b>Conciliado</b>
                                    </div>
                                `;
                                break;
                            case 'over-conciliated':
                                html = `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color:#FC482B;">
                                        <b>Sobre Conciliado</b>
                                    </div>
                                `;
                                break;
                            default:
                                html = `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color:#dc3545;">
                                        ${data.movement.status}
                                    </div>
                                `;
                        }
                        if (div_status_dtmv != null) div_status_dtmv.innerHTML = html;
                        if (div_status_dtcon != null) div_status_dtcon.innerHTML = html;

                        // Add content to Concilation
                        if (data.reconciliations && data.reconciliations.length > 0) {
                            for (const con of data.reconciliations) {
                                debugLog("conciliation: ", con);

                                const div_col9 = document.createElement("div");
                                div_col9.className = "col-9 m-0 d-flex align-items-center justify-content-start";
                                const h6 = document.createElement("h6");
                                h6.className = "mx-0 my-1";
                                const span = document.createElement("span");
                                span.className = "text-limit-concept";
                                const spanb = document.createElement("b");

                                let text = document.createTextNode("Conciliacion " + con.type_id);
                                if (con.type_id == 'invoice') {
                                    let new_text = 'Factura';
                                    if (con.invoice__invoice_category) {
                                        new_text += ' ' + con.invoice__invoice_category.toString().replace('sales', 'Ventas').replace('expenses', 'Gastos');
                                        if (con.invoice__is_rectifying == true || con.invoice__is_rectifying == 'true') {
                                            new_text += ' Rect.';
                                        }
                                    }
                                    new_text += ': ';
                                    elem_text = document.createTextNode(new_text);
                                    elem_text_a = document.createTextNode(con.invoice__reference);
                                    elem_a = document.createElement("a");
                                    elem_a.href = "/sellers/" + dj.seller.shortname + "/invoice/" + con.invoice_id + "/";
                                    elem_a.target = "_blank";
                                    elem_a.appendChild(elem_text_a);
                                    elem_span = document.createElement("span");
                                    elem_span.appendChild(elem_text);
                                    elem_span.appendChild(elem_a);
                                    text = elem_span;
                                } else if (con.type_id == 'bank') {
                                    text = document.createTextNode("Transferencia Banco " + con.bank__name);
                                } else if (con.type_id == 'transfer') {
                                    text = document.createTextNode("Transferencia Movimiento " + con.movement_transfer_id);
                                } else if (con.type_id == 'account') {
                                    if (con.accounting_account_id && con.accounting_account_id != null) {
                                        text = document.createTextNode("Cuenta Contable: " + con.accounting_account_id);
                                    } else if (con.accounting_account_detail && con.accounting_account_detail.length > 0) {
                                        text = document.createTextNode("Cuenta Contable: " + con.accounting_account_detail);
                                    } else {
                                        text = document.createTextNode("Cuenta Contable: - ");
                                    }
                                }
                                spanb.appendChild(text);
                                span.appendChild(spanb);
                                h6.appendChild(span);
                                div_col9.appendChild(h6);
                                if (div_concilations != null) {
                                    div_concilations.appendChild(div_col9);
                                }

                                const div_col3 = document.createElement("div");
                                div_col3.className = "col-3 bg-white";
                                const b = document.createElement("b");
                                const span_2 = document.createElement("span");
                                const text_2 = document.createTextNode(con.amount);
                                const text_3 = document.createTextNode(" EUR");
                                span_2.appendChild(text_2);
                                span_2.appendChild(text_3);
                                if (con.amount && con.amount >= 0) {
                                    span_2.className = "text-success";
                                } else if (con.amount && con.amount < 0) {
                                    span_2.className = "text-danger";
                                }
                                b.appendChild(span_2);
                                div_col3.appendChild(b);
                                if (div_concilations != null) {
                                    div_concilations.appendChild(div_col3);
                                }
                            }
                        } else {
                            const div_col12 = document.createElement("div");
                            div_col12.className = "col-12 m-0 d-flex align-items-center justify-content-center";
                            const h6 = document.createElement("h6");
                            h6.className = "mx-0 my-1";
                            const span = document.createElement("span");
                            span.className = "text-limit-concept";
                            const text = document.createTextNode(" - No Conciliado - ");
                            span.appendChild(text);
                            h6.appendChild(span);
                            div_col12.appendChild(h6);
                            if (div_concilations != null) {
                                div_concilations.appendChild(div_col12);
                            }
                        }

                        // Calc Movement Pending Amount
                        let movement_amount_euros_pending = parseFloat(data.movement.amount_euros);
                        if ( data.movement.amount_euros < 0) {
                            if (data.reconciliations_amount < 0) {
                            movement_amount_euros_pending = parseFloat(data.movement.amount_euros) - parseFloat(data.reconciliations_amount);
                            } else {
                            movement_amount_euros_pending = parseFloat(data.movement.amount_euros) + parseFloat(data.reconciliations_amount);
                            }
                        } else {
                            if (data.reconciliations_amount < 0) {
                            movement_amount_euros_pending = parseFloat(data.movement.amount_euros) + parseFloat(data.reconciliations_amount);
                            } else {
                            movement_amount_euros_pending = parseFloat(data.movement.amount_euros) - parseFloat(data.reconciliations_amount);
                            }
                        }
                        movement_amount_euros_pending = parseFloat(movement_amount_euros_pending) * -1;
                        movement_amount_euros_pending = parseFloat(movement_amount_euros_pending).toFixed(2);
                        console.log("NEW_movement_amount_euros_pending: ", movement_amount_euros_pending);

                        const div_notpending = document.querySelector("#div_not_pending_" + movement_id);
                        const div_notconciliated = document.querySelector("#div_not_conciliated_" + movement_id);
                        const btn_conciliate_account = $("#btn_conciliate_account_" + movement_id);
                        const div_delete = document.querySelector("#div_delete_" + movement_id);

                        btn_conciliate_account.off("click");
                        btn_conciliate_account.on("click", function () {
                            onClickConciliateAccountModal(movement_id, movement_amount_euros_pending);
                        });
                        // Manejo de visibilidad de botones
                        if (data.movement.status != 'pending') {
                            div_notpending.style.display = "block";
                            div_delete.style.display = "none"; // Ocultar el botón "Eliminar"
                        } else {
                            div_notpending.style.display = "none";
                            div_delete.style.display = "block"; // Mostrar el botón "Eliminar"
                        }

                        if (data.movement.status != 'conciliated') {
                            if (div_notconciliated != null) {
                                div_notconciliated.style.display = "block";
                            }
                        } else {
                            if (div_notconciliated != null) {
                                div_notconciliated.style.display = "none";
                            }
                        }

                        // Aquí sigue la lógica original para actualizar el estado, reconciliaciones, etc.
                        debugLog("Actualización completada para movimiento:", movement_id);

                        onClickConciliateInvoiceModalUpdateData(movement_id, 'invoice', false);

                        if (transfer_id) {
                            debugLog('transferrrrr IDDDD: ', transfer_id);
                            updateOneInTableConcilations(transfer_id, null, null, false);
                        }
                    } else {
                        console.error("Movement not found");
                        for (const bank of dj.banks) {
                            const bank_name = bank.bank_name.toString().trim().toLowerCase().replaceAll(" ", "");
                            if (bank_name == 'amazon' || bank_name == 'accounts') {
                                const div_status_dtmv = document.querySelector("#bank_" + bank.pk + "_movement_" + movement_id + "_status_dtmv");
                                debugLog("div_status_dtmv: ", div_status_dtmv);

                                if (div_status_dtmv != null) {
                                    const parent = div_status_dtmv.parentElement.parentElement;
                                    while (parent.firstChild) {
                                        parent.removeChild(parent.firstChild);
                                    }
                                    parent.style.display = "none";
                                }

                                const div_status_dtcon = document.querySelector("#bank_" + bank.pk + "_movement_" + movement_id + "_status_dtcon");
                                debugLog("div_status_dtcon: ", div_status_dtcon);

                                if (div_status_dtcon != null) {
                                    const parent = div_status_dtcon.parentElement.parentElement;
                                    while (parent.firstChild) {
                                        parent.removeChild(parent.firstChild);
                                    }
                                    parent.style.display = "none";
                                }
                            }
                        }
                    }
                }
            });
        };

        // Function to process mass conciliation with correct handling of existing conciliations
        const processMassConciliate = (conciliationIds, accountId, totalAmount) => {

            // Show spinner
            $('#modalMassConciliateContent').hide();
            $('#modalMassConciliateSpinner').show();

            // Counters for tracking progress
            let processedCount = 0;
            let successCount = 0;
            const seller = dj.seller.shortname;

            // Process each movement sequentially
            const processMovements = async () => {
                for (const movementId of conciliationIds) {
                    try {
                        // First, get the current state of the movement including all existing conciliations
                        const movementResponse = await $.ajax({
                            url: `/sellers/${seller}/reconciliation/get/movement/${movementId}/`,
                            type: 'GET',
                            dataType: 'json'
                        });

                        if (movementResponse.result !== 'ok' || !movementResponse.data || !movementResponse.data.length) {
                            debugLog(`Error getting movement ${movementId} data`);
                            processedCount++;
                            continue;
                        }

                        const movementData = movementResponse.data[0];

                        // Skip if already fully conciliated
                        if (movementData.movement.status_id === 'conciliated') {
                            debugLog(`Movement ${movementId} is already fully conciliated, skipping`);
                            processedCount++;
                            successCount++;
                            continue;
                        }

                        const originalAmount = parseFloat(movementData.movement.amount_euros);

                        // Check if there's already a conciliation with this account
                        let existingConciliation = null;
                        let existingAmount = 0;
                        if (movementData.reconciliations && movementData.reconciliations.length > 0) {
                            existingConciliation = movementData.reconciliations.find(rec =>
                                rec.type_id === 'account' &&
                                (rec.accounting_account_id === accountId || rec.accounting_account_detail === accountId)
                            );

                            if (existingConciliation) {
                                existingAmount = parseFloat(existingConciliation.amount);
                                debugLog(`Found existing conciliation ID: ${existingConciliation.id} with account ${accountId}, current amount: ${existingAmount.toFixed(2)}`);
                            }
                        }

                        // Calculate the total conciliated amount, EXCLUDING the existing conciliation with this account
                        let totalConciliatedWithoutExisting = parseFloat(movementData.reconciliations_amount || 0);
                        if (existingConciliation) {
                            totalConciliatedWithoutExisting -= existingAmount;
                        }

                        // Calculate the true remaining amount needed
                        let remainingAmount;
                        if (originalAmount < 0) {
                            // For expenses (negative amounts)
                            if (totalConciliatedWithoutExisting < 0) {
                                remainingAmount = originalAmount - totalConciliatedWithoutExisting;
                            } else {
                                remainingAmount = originalAmount + totalConciliatedWithoutExisting;
                            }
                        } else {
                            // For incomes (positive amounts)
                            if (totalConciliatedWithoutExisting < 0) {
                                remainingAmount = originalAmount + totalConciliatedWithoutExisting;
                            } else {
                                remainingAmount = originalAmount - totalConciliatedWithoutExisting;
                            }
                        }

                        // Convert sign based on movement type
                        let conciliationAmount;
                        if (originalAmount < 0) {
                            // Expense: The conciliation amount should be positive (absolute value)
                            conciliationAmount = Math.abs(remainingAmount);
                        } else {
                            // Transfer: The conciliation amount should be negative (negative of absolute value)
                            conciliationAmount = -Math.abs(remainingAmount);
                        }

                        // If there's an existing conciliation, remove it first
                        if (existingConciliation) {
                            try {
                                // Remove the existing conciliation
                                const removeResponse = await $.ajax({
                                    url: `/sellers/${seller}/reconciliation/remove/?movement_id=${movementId}&reconcilation_id=${existingConciliation.id}`,
                                    type: 'GET',
                                    dataType: 'json'
                                });

                                if (removeResponse.result !== 'ok') {
                                    debugLog(`Error removing existing conciliation: ${removeResponse.message}`);
                                    processedCount++;
                                    continue;
                                }

                                debugLog(`Successfully removed existing conciliation, proceeding with new amount: ${conciliationAmount.toFixed(2)}`);
                            } catch (error) {
                                debugLog(`Error removing existing conciliation: ${error}`);
                                processedCount++;
                                continue;
                            }
                        }

                        // Create the new conciliation with the calculated amount
                        try {
                            const response = await $.ajax({
                                url: `/sellers/${seller}/reconciliation/new/?movement_id=${movementId}&account_id=${accountId}&amount=${conciliationAmount}`,
                                type: 'GET',
                                dataType: 'json'
                            });

                            processedCount++;

                            if (response && response.result === 'ok') {
                                successCount++;
                                debugLog(`Successfully conciliated movement ${movementId} with account ${accountId}, amount: ${conciliationAmount.toFixed(2)}`);
                            } else {
                                debugLog(`Error conciliating movement ${movementId}:`, response);
                            }
                        } catch (error) {
                            processedCount++;
                            debugLog(`Error creating new conciliation: ${error}`);
                        }
                    } catch (error) {
                        processedCount++;
                        console.error(`Error processing movement ${movementId}:`, error);
                    }
                }

                // Complete the process
                $('#modalMassConciliateSpinner').hide();
                $('#modalMassConciliateContent').show();
                $('#modalMassConciliate').modal('hide');

                // Show result message
                Swal.fire({
                    icon: successCount === conciliationIds.length ? 'success' : 'info',
                    title: 'Conciliación masiva completada',
                    text: `Se han conciliado correctamente ${successCount} de ${conciliationIds.length} movimientos.`
                });

                // Update tables and UI
                if (selectedCard) {
                    const dtMovements = $(`#movements_bank_${selectedCard}`).DataTable();
                    const dtConciliate = $(`#conciliate_bank_${selectedCard}`).DataTable();

                    if (dtMovements) dtMovements.ajax.reload(null, false);
                    if (dtConciliate) dtConciliate.ajax.reload(null, false);

                    updateBankBalance(selectedCard);
                    $('.conciliate-checkbox').prop('checked', false);
                    $('#select-all-conciliate-checkbox').prop('checked', false);
                    $(`#btnMassConciliate-${selectedCard}`).prop('disabled', true);
                }
            };

            // Start the processing
            processMovements();
        };

        // Function to update the onClickMassConciliate to use the new implementation
        const onClickMassConciliate = (bankId) => {

            // Update the selected bank ID
            selectedCard = bankId;

            // Get selected reconciliations via checkboxes
            const selectedConciliations = [];
            const selectedAmounts = [];

            $('.conciliate-checkbox:checked').each(function() {
                const conciliationId = $(this).data('id');
                const amount = $(this).data('amount');
                if (conciliationId) {
                    selectedConciliations.push(conciliationId);
                    selectedAmounts.push(parseFloat(amount));
                }
            });

            // Update information in the modal
            const count = selectedConciliations.length;
            let total = 0;
            selectedAmounts.forEach(amount => {
                if (!isNaN(amount)) {
                    total += Math.abs(amount);
                }
            });

            $('#selectedConciliateCount').text(count);
            $('#selectedConciliateTotal').text(total.toFixed(2));

            // Enable/disable conciliation button based on selections
            $('#btnProcessMassConciliate').prop('disabled', count === 0);

            // Configure the mass conciliation button
            $('#btnProcessMassConciliate').off('click').on('click', function() {
                const accountId = $('#massConciliateAccountSelect').val();
                if (!accountId || accountId === '0') {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Seleccione una cuenta contable',
                        text: 'Debe seleccionar una cuenta contable para continuar.'
                    });
                    return;
                }

                // Disable button to prevent multiple clicks
                $(this).prop('disabled', true);

                // Process the mass conciliation
                processMassConciliate(selectedConciliations, accountId, total);
            });

            // Show the modal
            const massConciliateModal = new bootstrap.Modal(document.getElementById('modalMassConciliate'));
            massConciliateModal.show();
        };

        const onClickConciliateModalAmz = (movementID, concept, total) => {
            debugLog('onClickConciliateModalAmz');
            let show = false;

            // Set Invoice Reference
            document.getElementById("modalConciliateAmzSpanReference1").innerHTML = '';
            document.getElementById("modalConciliateAmzSpanReference2").innerHTML = concept;

            // Set Movement Amount
            document.getElementById("modalConciliateAmzSpanAmount").innerHTML = total * -1;

            // Set Amz Movement Amount
            document.getElementById("modalConciliateAmzSpanAmountMovement").innerHTML = total;

            // Set Invoice Bank
            document.getElementById("modalConciliateAmzSpanBank").innerHTML = 'amazon';

            // Set Button Action and Show Modal to true/false
            document.getElementById("modalConciliateAmzButton").onclick = () => { onClickConciliateAmazon(movementID, total); };

            // Show Modal
            $('#modalConciliateAmz').modal('show');
        }

        const onClickConciliateAmazon = async (movementID, total) => {
            debugLog('onClickConciliateAmazon');

            const modal = document.getElementById("modalConciliateAmz");
            const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;
            debugLog('CSRF:' + csrfToken);

            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer);
                    toast.addEventListener('mouseleave', Swal.resumeTimer);
                }
            });

            // Oculta la fila correspondiente
            hideOneInTableConcilations(movementID);

            try {
                // Realiza la llamada AJAX
                const response = await $.ajax({
                    type: "POST",
                    url: "{% url 'app_banks:bank_reconciliation_transfer_amz_new' seller.shortname %}",
                    data: {
                        'movement_id': movementID,
                        'bank_name': 'amazon'
                    },
                    headers: {
                        'X-CSRFToken': csrfToken // Agrega el token CSRF
                    }
                });

                let transferId = null;

                if (response.result === 'ok') {
                    debugLog('SUCCESS:', response);
                    Toast.fire({
                        icon: 'success',
                        title: 'Conciliación realizada correctamente.'
                    });

                    if (response.data) {
                        for (const d of response.data) {
                            if (d.id != null && d.id != movementID) {
                                transferId = d.id;
                                break;
                            }
                        }
                    }
                } else {
                    debugLog('ERROR:', response);
                    Toast.fire({
                        icon: 'error',
                        title: 'Error al generar la conciliación.'
                    });
                }

                // Actualiza la tabla y saldos
                await updateAllBankBalances(); // Espera a que se completen las actualizaciones de saldos
                updateOneInTableConcilations(movementID, null, null, false);

                // Oculta el modal
                $('#modalConciliateAmz').modal('hide');
            } catch (error) {
                debugLog('ERROR:', error);
                Toast.fire({
                    icon: 'error',
                    title: 'Error al generar la conciliación.'
                });
                // Oculta el modal en caso de error
                $('#modalConciliateAmz').modal('hide');
            }
        };


        // Configura eventos dinámicamente al cargar el DOM.
        document.addEventListener("DOMContentLoaded", async function () {
            debugLog("DOM completamente cargado, inicializando...");

            await setupPage(); // Configura la página al cargar.
            initializeSelect2(); // Inicializa select2 para los elementos necesarios.
            window.addEventListener("resize", onResize); // Ajusta elementos al redimensionar.
            updateArrowButtons(); // Inicializa el estado de los botones
        });

        async function setupPage() {
            debugLog("Configurando la página...");

            try {
                await getDjangoData();
                onResize();

                if (!dj.banks?.length) {
                    console.warn("No se encontraron bancos en los datos de Django.");
                    return;
                }

                for (const bank of dj.banks) {
                    try {
                        debugLog(`Inicializando tabla para banco ID: ${bank.pk}`);
                        await initializeTables(bank);
                        await updateBankBalance(bank.pk);
                    } catch (err) {
                        console.error(`Error al inicializar el banco ID: ${bank.pk}`, err);
                    }
                }

                onClickCardBank(dj.banks[0].pk); // Seleccionar la primera tarjeta.
                setupSearchInputs();
            } catch (err) {
                console.error("Error durante la configuración de la página:", err);
            }
        }

        // Elimina un movimiento pendiente. (async porque puede realizar una llamada al backend)
        async function deletePendingMovement(movementId, bankName, tableId) {
            debugLog(`Eliminando movimiento pendiente ID: ${movementId}`);

            if (!movementId || !tableId) {
                console.error("Faltan parámetros necesarios para eliminar el movimiento.");
                return;
            }

            if (bankName && (bankName.toLowerCase() === "amazon" || bankName.toLowerCase() === "accounts")) {
                console.warn("Eliminación no permitida para movimientos de Amazon o Accounts.");
                Swal.fire({
                    icon: 'warning',
                    title: 'Eliminación no permitida',
                    text: 'No puedes eliminar movimientos de los bancos Amazon o Accounts.',
                });
                return;
            }

            const confirmDelete = await Swal.fire({
                title: '¿Estás seguro?',
                text: 'Esto eliminará el movimiento pendiente y todas sus conciliaciones asociadas. Esta acción no se puede deshacer.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar',
            }).then(result => result.isConfirmed);

            if (!confirmDelete) {
                return;
            }

            try {
                // Agregar un log para verificar el cuerpo enviado
                debugLog("Enviando datos al servidor:", { movement_id: movementId });

                const response = await fetch(`/sellers/${dj.seller.shortname}/movement/remove/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken,
                    },
                    body: JSON.stringify({ movement_id: movementId }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.result === 'ok') {
                    await Swal.fire({
                        icon: 'success',
                        title: 'Movimiento eliminado',
                        text: 'El movimiento y sus conciliaciones asociadas se han eliminado correctamente.',
                    });

                    $(`#${tableId}`).DataTable().ajax.reload(null, false);

                    if (result.bank_id) {
                        updateBankBalance(result.bank_id);
                    }
                } else {
                    await Swal.fire({
                        icon: 'error',
                        title: 'Error al eliminar',
                        text: result.message || 'Ocurrió un error desconocido al intentar eliminar el movimiento.',
                    });
                }
            } catch (error) {
                console.error("Error al eliminar el movimiento:", error);
                await Swal.fire({
                    icon: 'error',
                    title: 'Error al eliminar',
                    text: 'Ocurrió un error al intentar eliminar el movimiento. Por favor, inténtalo de nuevo.',
                });
            }
        }

        // Configura inputs de búsqueda dinámicamente.
        function setupSearchInputs() {
            debugLog("Configurando los inputs de búsqueda...");

            // Selecciona todos los inputs y sus íconos asociados
            const inputs = document.querySelectorAll('[id^="search-input-"]');
            const icons = document.querySelectorAll(".search-icon");

            // Itera sobre todos los inputs encontrados
            inputs.forEach((input, index) => {
                const icon = icons[index]; // Relaciona el ícono con el input correspondiente

                // Inicializa el input en estado colapsado
                collapseInput(input);

                // Asigna eventos al input
                input.addEventListener("focus", () => expandInput(input));
                input.addEventListener("blur", () => {
                    handleBlur(input);

                    // Limpia el texto si el input está vacío al perder el foco
                    if (input.value.trim() === "") {
                        currentSearchText = ""; // Resetea el valor global
                    }
                });
                input.addEventListener("input", () => {
                    currentSearchText = input.value.trim().toLowerCase(); // Actualiza el texto global
                    debugLog(`Texto de búsqueda actualizado: ${currentSearchText}`);
                    handleInput(input);

                    // Limpia el texto global si el input queda vacío
                    if (currentSearchText === "") {
                        currentSearchText = "";
                    }
                });

                // Asigna eventos al ícono asociado (si existe)
                if (icon) {
                    icon.addEventListener("click", () => handleIconClick(input));
                } else {
                    console.warn(`No se encontró un ícono para el input con ID: ${input.id}`);
                }
            });
        }

        // Inicializa DataTables para cada banco
        function initializeTables(obj) {
            const bankId = obj.pk;
            const name = obj.bank_name;

            debugLog(`Inicializando tablas para banco ID: ${bankId}`);
            const dataTableMovementsSelector = `#movements_bank_${bankId}`;
            const dataTableConciliationsSelector = `#conciliate_bank_${bankId}`;

            // Verifica si ambas tablas ya están inicializadas
            if (initializedTables.has(dataTableMovementsSelector) && initializedTables.has(dataTableConciliationsSelector)) {
                debugLog(`Tablas ya inicializadas para banco ID: ${bankId}`);
                console.warn(`Las tablas para el banco ID ${bankId} ya están inicializadas.`);
                return;
            }

            // Destruir tablas existentes para evitar conflictos


            // Inicializar tabla de movimientos
            if (!initializedTables.has(dataTableMovementsSelector)) {
                destroyTable(dataTableMovementsSelector);
                // Generar y aplicar configuraciones específicas
                const dataTableMovementsOptions = dataTableSpecificOptions('movements', bankId, name);

                $(dataTableMovementsSelector).DataTable(dataTableMovementsOptions); // Inicializa la tabla

                initializedTables.add(dataTableMovementsSelector);
                debugLog(`Tabla "movements_bank_${bankId}" inicializada.`);
            }

            // Inicializar tabla de conciliaciones
            if (!initializedTables.has(dataTableConciliationsSelector)) {
                destroyTable(dataTableConciliationsSelector);
                // Generar y aplicar configuraciones específicas
                const dataTableConciliationsOptions = dataTableSpecificOptions('conciliations', bankId, name);
                debugLog(`[Conciliations Init] Options for conciliate_bank_${bankId}:`);
                debugLog("[Conciliations Init] Columns:", JSON.stringify(dataTableConciliationsOptions.columns));
                debugLog("[Conciliations Init] ColumnDefs:", JSON.stringify(dataTableConciliationsOptions.columnDefs));
                $(dataTableConciliationsSelector).DataTable(dataTableConciliationsOptions); // Inicializa la tabla

                initializedTables.add(dataTableConciliationsSelector); // Actualiza marca de Tabla inicializada
                debugLog(`Tabla "conciliate_bank_${bankId}" inicializada.`);
            }
        }

        // Genera opciones específicas para DataTables según el tipo (movements o conciliations)
        function dataTableSpecificOptions(type, bankId, name) {
            const baseUrl = `/sellers/${dj.seller.shortname}/reconciliation/get/movement/DT/?bank_id=${bankId}`;

            // Configuración común
            const commonOptions = {
                paging: true,
                lengthMenu: [[25, 50, 100], [25, 50, 100]],
                pageLength: 50,
                searching: true,
                ordering: true,
                truncation: true,
                info: true,
                footer: true,
                processing: true,
                language: {
                    lengthMenu: "_MENU_",
                    zeroRecords: "No se han encontrado movimientos.",
                    info: "_TOTAL_ resultados. ",
                    search: "Buscar:",
                    infoEmpty: "No hay resultados que coincidan con su búsqueda.",
                    loadingRecords: "Cargando...",
                    processing: `
                        <div class="text-center bg-white">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                        </div>`,
                    paginate: {
                        "first": "Primero",
                        "last": "Último",
                        "next": "Siguiente",
                        "previous": "Anterior"
                    },
                },
                dom: 'rtip',
                fixedHeader: true,
                serverSide: true,
                order: [[0, "desc"]],
            };

            // Opciones específicas para movimientos
            if (type === 'movements') {
                return {
                    ...commonOptions,
                    ajax: {
                        url: baseUrl,
                        dataSrc: function (json) {
                            debugLog("=> [movementsTable] - Datos recibidos del backend:", json);
                            // Añade el nombre del banco al objeto de cada fila
                            json.data.forEach(row => {
                                row.bank_name = name; // Agrega el nombre del banco
                                // Asegurar que 'reconciliations' sea siempre un array
                                if (!Array.isArray(row.reconciliations)) {
                                    row.reconciliations = [];
                                }
                            });
                            return json.data;
                        }
                    },
                    columns: [
                        { data: 'id', title: 'ID'},
                        { data: 'status_id', title: 'Estado',  render: function(data, type, row) {
                            let html = `<div id="bank_${row.bank_id}_movement_${row.id}_status_dtmv">`;
                            if (data == 'pending') {
                                html += `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color:#e9d412;">
                                    <b>Pendiente</b>
                                    </div>
                                `;
                            } else if (data == 'partially-conciliated') {
                                html += `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color: #FF9103;">
                                    <b>Parcialmente Conciliado</b>
                                    </div>
                                `;
                            } else if (data == 'over-conciliated') {
                                html += `
                                    <div class="rounded text-white text-center p-2 w-100" style="background-color: #FC482B;">
                                    <b>Sobreconciliado</b>
                                    </div>
                                `;
                            } else if (data == 'proposal') {
                                html += `
                                    <div class="rounded bg-info text-white text-center p-2 w-100" style="background-color:#11CAF0;">
                                    <b>Propuesta</b>
                                    </div>
                                `;
                            } else if (data == 'conciliated') {
                                html += `
                                    <div class="rounded bg-success text-white text-center p-2 w-100">
                                    <b>Conciliado</b>
                                    </div>
                                `;
                            } else {
                                html += `
                                    <div class="rounded bg-secondary text-white text-center p-2 w-100">
                                    <b>${data}</b>
                                    </div>
                                `;
                            }
                            html += '</div>';
                            return html;
                        } },
                        { data: 'movement_number', title: 'Num Movimiento'},
                        { data: 'concept', title: 'Concepto'},
                        { data: 'observation', title: 'Observación',},
                        { data: 'amount', title: 'Importe',  render: function(data, type, row) {
                            let html = `<div style="display: flex; align-items: center; justify-content: center; text-align: center;">`;
                            if (data > 0) {
                                html += `
                                    <span class="text-success">
                                        <b>${data}</b>
                                    </span>
                                `;
                            } else if (data < 0) {
                                html += `
                                    <span class="text-danger">
                                        <b>${data}</b>
                                    </span>
                                `;
                            } else {
                                html += `
                                    <span>
                                        <b>${data}</b>
                                    </span>
                                `;
                            }
                            html += `</div>`;
                            return html;
                        } },
                        { data: 'currency', title: 'Moneda' },
                        { data: 'amount_euros', title: 'Importe (€)',  render: function(data, type, row) {
                            let html = `<div class="col-3 bg-white">`;
                            if (data > 0) {
                                html += `
                                    <span class="text-success">
                                    <b>${data}€</b>
                                    </span> <br>
                                `;
                            } else if (data < 0) {
                                html += `
                                    <span class="text-danger">
                                    <b>${data}€</b>
                                    </span> <br>
                                `;
                            } else {
                                html += `
                                    <span><b>${data}€</b></span> <br>
                                `;
                            }
                            html += `</div>`;
                            return html;
                        } },
                        { data: 'movement_date', title: 'Fecha', render: function (data, type) {
                            if (type === 'display') {
                                return data ? new Date(data).toLocaleDateString('es-ES') : '-';
                            }
                            return data;
                        }}
                        ],
                        columnDefs: [
                            {
                                targets: '_all', // Aplica a todas las columnas
                                className: 'text-center'
                            },
                            { targets: [0, 7], visible: false }, // Estas columnas permanecen ocultas
                            { targets: [1], visible: true, orderable: true, width: '5%' },  // Estado
                            { targets: [2], visible: true, orderable: false, width: '30%' }, // Num Movimiento
                            { targets: [3], visible: true, orderable: true, width: '35%' }, // Concepto
                            { targets: [4], visible: true, orderable: true, width: '5%' },  // Observación
                            { targets: [5], visible: true, orderable: true, width: '10%' }, // Importe
                            { targets: [6], visible: true, orderable: true, width: '5%' },  // Moneda
                            { targets: [8], visible: true, type: 'date', orderable: true, width: '10%' } // Fecha
                        ],
                    initComplete: function () {
                        debugLog(`Tabla de movimientos inicializada para banco ID: ${bankId}`);
                        updateBankBalance(bankId);
                    },
                    drawCallback: function () {
                        const tableData = this.api().rows().data().toArray();
                        tableData.forEach(row => {
                            debugLog('Fecha:', row.movement_date); // Verifica que las fechas sean válidas
                        });
                    },
                };
            }

            // Opciones específicas para conciliaciones
            if (type === 'conciliations') {
                let columns = [
                        { data: null, title: '', render: function(data, type, row) {
                                // Solo mostrar checkbox para estados 'pending' y 'partially-conciliated'
                                if (row.status_id === 'pending' || row.status_id === 'partially-conciliated') {
                                    // Calcular el monto pendiente para conciliación
                                    let pendingAmount = parseFloat(row.amount_euros);
                                    if (row.reconciliations_amount) {
                                        // Si ya hay conciliaciones, calcular el monto pendiente correctamente
                                        // basado en el signo del monto original y el monto ya conciliado
                                        const originalAmount = parseFloat(row.amount_euros);
                                        const reconciliationsAmount = parseFloat(row.reconciliations_amount);

                                        // Para movimientos negativos (gastos)
                                        if (originalAmount < 0) {
                                            if (reconciliationsAmount < 0) {
                                                // Si ambos son negativos, restamos (porque estamos sumando valores absolutos)
                                                pendingAmount = originalAmount - reconciliationsAmount;
                                            } else {
                                                // Si el monto conciliado es positivo, sumamos (porque estamos restando)
                                                pendingAmount = originalAmount + reconciliationsAmount;
                                            }
                                        }
                                        // Para movimientos positivos (transferencias recibidas)
                                        else {
                                            if (reconciliationsAmount < 0) {
                                                // Si el monto conciliado es negativo, sumamos (porque estamos restando)
                                                pendingAmount = originalAmount + reconciliationsAmount;
                                            } else {
                                                // Si ambos son positivos, restamos (porque estamos sumando valores absolutos)
                                                pendingAmount = originalAmount - reconciliationsAmount;
                                            }
                                        }
                                    }
                                    // Para transferencias (montos positivos), el monto pendiente debe ser negativo para la conciliación
                                    return '<input type="checkbox" class="conciliate-checkbox" data-id="' + row.id + '" data-amount="' + pendingAmount.toFixed(2) + '">';
                                } else {
                                    return '';
                                }
                            }
                        },
                        { data: 'id', title: 'ID' },
                        { data: 'bank_id', title: 'Banco (ID)' },
                        { data: 'status_id', title: 'Estado', render: function ( data, type, row ) {
                            let html = `<div id="bank_${row.bank_id}_movement_${row.id}_status_dtcon">`;
                            if (data == 'pending') {
                            html += `
                                <div class="rounded text-white text-center p-2 w-100" style="background-color:#e9d412;">
                                <b>Pendiente</b>
                                </div>
                            `;
                            } else if (data == 'partially-conciliated') {
                            html += `
                                <div class="rounded text-white text-center p-2 w-100" style="background-color: #FF9103;">
                                <b>Parcialmente Conciliado</b>
                                </div>
                            `;
                            } else if (data == 'over-conciliated') {
                            html += `
                                <div class="rounded text-white text-center p-2 w-100" style="background-color: #FC482B;">
                                <b>Sobreconciliado</b>
                                </div>
                            `;
                            } else if (data == 'proposal') {
                            html += `
                                <div class="rounded bg-info text-white text-center p-2 w-100" style="background-color:#11CAF0;">
                                <b>Propuesta</b>
                                </div>
                            `;
                            } else if (data == 'conciliated') {
                            html += `
                                <div class="rounded bg-success text-white text-center p-2 w-100">
                                <b>Conciliado</b>
                                </div>
                            `;
                            } else {
                            html += `
                                <div class="rounded bg-secondary text-white text-center p-2 w-100">
                                <b>${data}</b>
                                </div>
                            `;
                            }
                            html += '</div>';
                            return html;
                        } },
                        { data: 'concept', title: 'Concepto' },
                        { data: 'observation', title: 'Observacion' },
                        { data: 'movement_date', title: 'Fecha Movimiento' },
                        { data: 'amount', title: 'Importe' },
                        { data: 'currency', title: 'Moneda' },
                        { data: 'amount_euros', title: 'Importe (€)' },
                        { data: 'movement', title: 'Movimientos Bancarios', render: function ( data, type, row ) {
                            data = row;
                            let html = `
                            <div class="row text-start">
                                <div class="col-9">
                            `;

                            if (data.concept) {
                                html += `
                                <span><b>${data.concept}</b></span> <br>
                                `;
                            }

                            if (data.observation) {
                                html += `
                                <span>${data.observation}</span> <br>
                                `;
                            }

                            html += `
                                <span>${data.movement_date}</span> <br>
                            </div>
                            <div class="col-3 bg-white">
                            `;

                            if (data.amount > 0) {
                                html += `
                                <span class="text-success">
                                    <b>${data.amount} ${data.currency}</b>
                                </span> <br>
                                `;
                            } else if (data.amount < 0) {
                                html += `
                                <span class="text-danger">
                                    <b>${data.amount} ${data.currency}</b>
                                </span> <br>
                                `;
                            } else {
                                html += `
                                <span><b>${data.amount} ${data.currency}</b></span> <br>
                                `;
                            }
                            if (data.currency != 'Euro' || data.currency != 'EUR') {
                                html += `
                                <span class="text-secondary">
                                    <b>${data.amount_euros} EUR</b>
                                </span> <br>
                                `;
                            }

                            html += `
                                </div>
                            </div>
                            `;

                            return html;
                        } },
                        { data: 'reconciliations_amount', title: 'REC AMOUNT' },
                        { data: 'reconciliations_quantity', title: 'REC QTY' },
                        { data: 'reconciliations', title: 'Conciliación', render: function (data, type, row) {
                            let html = `<div class="row" id="bank_${row.bank_id}_movement_${row.id}_conciliations">`;
                            if (data && data.length > 0) {
                                for (const rec of data) {
                                    html += `<div class="col-9">`;

                                        if (rec.type_id === 'invoice') {
                                        let text = "Factura";
                                        if (rec.invoice__invoice_category) {
                                            text += ' ' + rec.invoice__invoice_category.toString().replace('sales', 'Ventas').replace('expenses', 'Gastos');
                                            if (rec.invoice__is_rectifying === true) {
                                                text += ' Rect.';
                                            }
                                        }

                                        // Verificar que `invoice_id` y `invoice__reference` existan
                                        if (rec.invoice_id && rec.invoice__reference) {
                                            html += `
                                                <span><b>${text}:
                                                    <a href="/sellers/${dj.seller.shortname}/invoice/${rec.invoice_id}/" target="_blank">
                                                        ${rec.invoice__reference}
                                                    </a>
                                                </b></span> <br>
                                            `;
                                        } else {
                                            html += `
                                                <span><b>${text}: Sin referencia</b></span> <br>
                                            `;
                                        }
                                    } else if (rec.type_id === 'account') {
                                        if (rec.accounting_account_id) {
                                            html += `<span><b>Cuenta Contable: ${rec.accounting_account_id}</b></span> <br>`;
                                        } else if (rec.accounting_account_detail) {
                                            html += `<span><b>Cuenta Contable: ${rec.accounting_account_detail}</b></span> <br>`;
                                        } else {
                                            html += `<span><b>Cuenta Contable: -</b></span> <br>`;
                                        }
                                    } else if (rec.type_id === 'bank') {
                                        html += `<span><b>Transferencia Banco ID ${rec.bank_id}</b></span> <br>`;
                                    } else if (rec.type_id === 'transfer') {
                                        html += `<span><b>Transferencia Movimiento ${rec.movement_transfer_id}</b></span> <br>`;
                                    }

                                    html += `</div><div class="col-3 bg-white">`;

                                    if (rec.amount > 0) {
                                        html += `<span class="text-success"><b>${rec.amount} EUR</b></span> <br>`;
                                    } else if (rec.amount < 0) {
                                        html += `<span class="text-danger"><b>${rec.amount} EUR</b></span> <br>`;
                                    } else {
                                        html += `<span><b>${rec.amount} EUR</b></span> <br>`;
                                    }

                                    html += `</div>`;
                                }
                            } else {
                                html += `
                                    <div class="col-12 m-0 d-flex align-items-center justify-content-center">
                                        - No Conciliado -
                                    </div>
                                `;
                            }
                            html += `</div>`;
                            return html;
                        }}
                ];

                let columnDefs = [
                        {
                            targets: '_all', // Todas las columnas
                            className: 'text-center'
                        },
                        { targets: [0], visible: true, orderable: false, width: '3%', className: 'select-checkbox-column' }, // Checkbox
                        { targets: [1, 2, 4, 5, 6, 7, 8, 9, 11, 12], visible: false }, // Estas columnas permanecen ocultas
                        { targets: [3], visible: true, orderable: true, width: '10%' },  // Estado
                        { targets: [10], visible: true, orderable: true, width: '27%' }, // Movimientos Bancarios
                        { targets: [13], visible: true, orderable: false, width: '10%', className: 'text-center' }  // Conciliación
                ];

                {% if user.role == 'manager' %}
                columns.push(
                    { data: 'actions',title: 'Acciones',render: function (data, type, row, meta) {
                        let not_pending_style = 'display:none;';
                        let not_conciliated_style = 'display:none;';
                        let used_in_entry = 'display:none;';
                        let deleteStyle = 'display:none;';

                        // Obtener el nombre del banco directamente desde los datos
                        const bankName = row.bank_name ? row.bank_name.toLowerCase() : "";
                        debugLog("Nombre del banco (bankName):", bankName);

                        movement_amount_euros_pending = parseFloat(row.amount_euros);
                        if (row.amount_euros < 0) {
                            if (row.reconciliations_amount < 0) {
                            movement_amount_euros_pending = parseFloat(row.amount_euros) - parseFloat(row.reconciliations_amount);
                            } else {
                            movement_amount_euros_pending = parseFloat(row.amount_euros) + parseFloat(row.reconciliations_amount);
                            }
                        } else {
                            if (row.reconciliations_amount < 0) {
                            movement_amount_euros_pending = parseFloat(row.amount_euros) + parseFloat(row.reconciliations_amount);
                            } else {
                            movement_amount_euros_pending = parseFloat(row.amount_euros) - parseFloat(row.reconciliations_amount);
                            }
                        }
                        movement_amount_euros_pending = parseFloat(movement_amount_euros_pending) * -1;
                        movement_amount_euros_pending = parseFloat(movement_amount_euros_pending).toFixed(2);

                        if (row.status_id != 'pending') {
                            not_pending_style = 'display:flex;';
                        } else {
                            not_pending_style = 'display:none;';
                            deleteStyle = (bankName !== 'amazon' && bankName !== 'accounts') ? 'display:flex;' : 'display:none;'
                        }

                        not_conciliated_style = (row.status_id != 'conciliated') ? 'display:flex;' : 'display:none;';

                        if (row.used_in_entry == true) {
                            not_pending_style = 'display:none;';
                            not_conciliated_style = 'display:none;';
                            used_in_entry = 'display:flex;';
                        }

                        // Contenedor principal para todos los botones
                        let html = `<div class="d-flex align-items-center justify-content-center" style="gap: 20px;">`;

                        html += `
                            <div class="align-items-center" id="div_used_in_entry_${row.id}" name="div_used_in_entry_${row.id}" style="${used_in_entry}">
                                <a href="/sellers/${dj.seller.shortname}/entry/" style="color:black;"><i class="fa-solid fa-arrow-up-right-from-square"></i> &nbsp; &nbsp; <b>Asientos</b></a>
                            </div>
                        `;

                        html += `


                            <div id="div_not_conciliated_${row.id}" name="div_not_conciliated_${row.id}" v-show="dj.movement.status.pk != 'conciliated'" style="${not_conciliated_style}">
                                <btn id="btn_conciliate_invoice_${row.id}" name="btn_conciliate_invoice_${row.id}" class="btn btn-link m-1 p-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Facturas" onclick="onClickConciliateInvoiceModal('${row.id}')">
                                    <img src="{% static 'assets/images/banks/conciliate-doc.svg' %}" alt="Facturas">
                                </btn>

                                <btn id="btn_conciliate_account_${row.id}" name="btn_conciliate_account_${row.id}" class="btn btn-link m-1 p-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Cuentas Contables" onclick="onClickConciliateAccountModal('${row.id}', '${movement_amount_euros_pending}')">
                                    <img src="{% static 'assets/images/banks/conciliate-account.svg' %}" alt="Cuentas Contables">
                                </btn>

                                <btn id="btn_conciliate_transfer_${row.id}" name="btn_conciliate_transfer_${row.id}" class="btn btn-link m-1 p-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Transferencias" onclick="onClickConciliateTransferModal('${row.id}')">
                                    <img src="{% static 'assets/images/banks/conciliate-transfer.svg' %}" alt="Transferencia">
                                </btn>

                                <btn id="btn_conciliate_transfer_amz_${row.id}" name="btn_conciliate_transfer_amz_${row.id}" class="btn btn-link m-1 p-1" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="Conciliar Amazon" onclick="onClickConciliateModalAmz('${row.id.trim().replace('\n','')}', '${row.concept.trim().replace('\n','')}', '${movement_amount_euros_pending.trim().replace('\n','')}')">
                                        <i class="fa-brands fa-xl fa-amazon text-warning m-0 p-0" alt="Conciliar Amazon"></i>
                                    </btn>
                                </div>
                            `;
                            // Botón "Eliminar movimiento"
                            html += `
                                <div id="div_not_pending_${row.id}" name="div_not_pending_${row.id}" v-show="dj.movement.status.pk != 'pending'" style="${not_pending_style}">
                                    <btn
                                        id="btn_conciliate_undo_${row.id}"
                                        name="btn_conciliate_undo_${row.id}"
                                        class="btn btn-link p-1"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="top"
                                        title="Deshacer Conciliación"
                                        onclick="onClickRemoveAllConciliate('${row.id}')"
                                    ><img src="{% static 'assets/images/banks/undo.svg' %}" alt="Deshacer"></btn>
                                </div>
                                <div id="div_delete_${row.id}" name="div_delete_${row.id}" v-show="dj.movement.status.pk == 'pending'" style="${deleteStyle}">
                                    <btn
                                        id="btn_delete_${row.id}"
                                        name="btn_delete_${row.id}"
                                        class="btn btn-link p-1"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="top"
                                        title="Eliminar movimiento pendiente"
                                        onclick="deletePendingMovement(${row.id}, '${row.bank_name}', 'conciliate_bank_${row.bank_id}')"
                                    ><i class="fa fa-trash text-danger" style="font-size: 1rem;"></i></btn>
                                </div>
                            `;

                            html += `
                                </div> <!-- Cierra el contenedor principal -->
                            `;
                            return html;
                        }}
                );
                columnDefs.push(
                    { targets: [14], visible: true, orderable: false, width: '10%', className: 'text-center' }  // Acciones
                );
                {% endif %}


                return {
                    ...commonOptions,
                    ajax: {
                        url: baseUrl,
                        dataSrc: function (json) {
                            json.data.forEach((row, index) => {
                                row.bank_name = name; // Agrega el nombre del banco
                                // Asegurar que 'reconciliations' sea siempre un array
                                if (!Array.isArray(row.reconciliations)) {
                                    row.reconciliations = [];
                                }
                            });

                            return json.data;
                        }
                    },
                    columns: columns,
                    columnDefs: columnDefs,
                    initComplete: function() {
                        const api = this.api();
                        debugLog("Tabla inicializada con los siguientes datos:", api.rows().data());

                        // Inicializar funcionalidad de checkbox para conciliaciones
                        const table = this;

                        // Manejar el checkbox de seleccionar todos
                        $(table).on('click', '#select-all-conciliate-checkbox', function() {
                            const isChecked = $(this).prop('checked');
                            $('.conciliate-checkbox').prop('checked', isChecked);
                            updateSelectedConciliateInfo();
                        });

                        // Manejar los checkboxes individuales
                        $(table).on('click', '.conciliate-checkbox', function() {
                            updateSelectedConciliateInfo();

                            // Actualizar el estado del checkbox "seleccionar todos"
                            const allChecked = $('.conciliate-checkbox:checked').length === $('.conciliate-checkbox').length;
                            $('#select-all-conciliate-checkbox').prop('checked', allChecked);
                        });

                        // Función para actualizar la información de conciliaciones seleccionadas
                        function updateSelectedConciliateInfo() {
                            const selectedCheckboxes = $('.conciliate-checkbox:checked');
                            const count = selectedCheckboxes.length;
                            let total = 0;

                            selectedCheckboxes.each(function() {
                                const amount = parseFloat($(this).data('amount'));
                                if (!isNaN(amount)) {
                                    // Usamos el valor absoluto para mostrar el total
                                    total += Math.abs(amount);
                                }
                            });

                            // Actualizar la información en el modal de conciliación masiva
                            $('#selectedConciliateCount').text(count);
                            $('#selectedConciliateTotal').text(total.toFixed(2));

                            // Habilitar/deshabilitar el botón de conciliación masiva según si hay selecciones
                            $('#btnProcessMassConciliate').prop('disabled', count === 0);

                            // Habilitar/deshabilitar el botón de conciliación masiva en la interfaz principal
                            const bankId = $(table).closest('table').attr('id').split('_')[2];
                            $(`#btnMassConciliate-${bankId}`).prop('disabled', count === 0);
                        }
                    },
                    drawCallback: function () {
                        const api = this.api();
                        const tableData = api.rows().data().toArray();
                        {% comment %} tableData.forEach(row => {
                            debugLog(`Estado de movimiento ${row.id}: ${row.status_id}`);
                        }); {% endcomment %}
                    }
                };
            }

            throw new Error(`Tipo de DataTable no reconocido: ${type}`);
        }

        // Destruye instancias previas de DataTables para evitar conflictos
        function destroyTable(selector) {
            if ($.fn.DataTable.isDataTable(selector)) {
                $(selector).DataTable().clear().destroy(); // Limpia y destruye la instancia DataTables
                debugLog(`Tabla destruida: ${selector}`);
            }
        }




        // Función para inicializar select2
        function initializeSelect2() {
            $('#modalConciliateAccountInputBank').select2({
                dropdownParent: $('#modalConciliateAccount'),
                placeholder: "Seleccione una opción",
                allowClear: true,
                theme: 'bootstrap-5',
            });

            $('#massConciliateAccountSelect').select2({
                dropdownParent: $('#modalMassConciliate'),
                placeholder: "Seleccione Cuenta Contable",
                allowClear: true,
                theme: 'bootstrap-5',
            });

            // Inicializar select2 para el select de cuentas contables en el modal de reglas bancarias
            $('#bankRuleAccountSelect').select2({
                dropdownParent: $('#modalBankRules .modal-content'),
                placeholder: "Seleccione Cuenta Contable",
                allowClear: true,
                theme: 'bootstrap-5',
                width: '100%' // Asegurar que tenga el mismo ancho que el campo de tipo de parámetro
            });
        }

        // Variables globales para las reglas
        let bankRules = [];
        let editingRuleId = null;

        // Función para abrir el modal de reglas bancarias
        const onClickBankRules = (bankId) => {
            debugLog('onClickBankRules para banco ID:', bankId);

            // Primero, asegurarse de que cualquier modal existente esté cerrado correctamente
            const existingModal = bootstrap.Modal.getInstance(document.getElementById('modalBankRules'));
            if (existingModal) {
                existingModal.hide();
                // Dar tiempo para que se complete la animación de cierre
                setTimeout(() => {
                    openBankRulesModal(bankId);
                }, 300);
            } else {
                openBankRulesModal(bankId);
            }

            // Configurar un intervalo para verificar y corregir el estado del modal periódicamente
            // Esto ayudará a mantener el modal visible sin problemas de backdrop
            const modalCheckInterval = setInterval(() => {
                const modalElement = document.getElementById('modalBankRules');
                if (modalElement && modalElement.classList.contains('show')) {
                    // Si el modal está visible, asegurarse de que no haya problemas de backdrop
                    if ($('.modal-backdrop').length > 1) {
                        fixModalState();
                    }
                } else {
                    // Si el modal ya no está visible, detener el intervalo
                    clearInterval(modalCheckInterval);
                }
            }, 500); // Verificar cada 500ms
        };

        // Función auxiliar para abrir el modal de reglas bancarias
        const openBankRulesModal = (bankId) => {
            // Limpiar cualquier backdrop residual
            cleanupModalBackdrops();

            // Resetear el estado del modal
            const modalElement = document.getElementById("modalBankRules");
            modalElement.classList.remove('show');
            modalElement.style.display = 'none';
            modalElement.setAttribute('aria-hidden', 'true');
            modalElement.removeAttribute('aria-modal');
            modalElement.removeAttribute('role');

            // Crear una nueva instancia del modal con las opciones correctas
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,  // Permitir cerrar al hacer clic fuera
                keyboard: true   // Permitir cerrar con ESC
            });

            // Ocultar el contenido y mostrar el spinner
            document.querySelector("#modalBankRulesContent").style.display = "none";
            document.querySelector("#modalBankRulesSpinner").style.display = "block";

            // Establecer el ID del banco en el formulario
            document.querySelector("#bankRulesBankId").value = bankId;

            // Mostrar el modal
            modal.show();

            // Cargar reglas existentes para este banco
            loadBankRules(bankId);
        };


        // Función para cargar las reglas existentes del banco
        const loadBankRules = (bankId) => {
            debugLog('Cargando reglas para banco ID:', bankId);

            // Limpiar el formulario
            resetRuleForm();
            document.getElementById('bankRulesBankId').value = bankId;

            // Asegurarse de que el modal permanezca visible y no haya backdrops adicionales
            fixModalState();

            // Obtener el token CSRF
            const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;

            // Realizar la petición AJAX para obtener las reglas
            $.ajax({
                url: `/sellers/${dj.seller.shortname}/banks/rules/get/`,
                type: 'GET',
                data: { bank_id: bankId },
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    debugLog('Reglas obtenidas:', response);
                    console.log('Datos completos de reglas recibidas:', response);

                    if (response.result === 'ok') {
                        // Guardar las reglas en la variable global
                        bankRules = response.rules || [];

                        // Actualizar la tabla con las reglas
                        updateRulesTable();

                        // Mostrar el contenido
                        document.querySelector("#modalBankRulesSpinner").style.display = "none";
                        document.querySelector("#modalBankRulesContent").style.display = "block";
                    } else {
                        // Obtener el modal actual
                        const modalElement = document.getElementById('modalBankRules');
                        // Guardar el z-index original
                        const originalZIndex = modalElement.style.zIndex;
                        // Temporalmente reducir el z-index del modal para que el SweetAlert aparezca encima
                        modalElement.style.zIndex = '1040';

                        // Mostrar error
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Error al cargar las reglas',
                            customClass: {
                                container: 'swal-higher-z-index'
                            }
                        }).then(() => {
                            // Restaurar el z-index original del modal
                            modalElement.style.zIndex = originalZIndex;
                        });

                        // Mostrar el formulario vacío de todos modos
                        document.querySelector("#modalBankRulesSpinner").style.display = "none";
                        document.querySelector("#modalBankRulesContent").style.display = "block";
                    }
                },
                error: function(xhr, status, error) {
                    debugLog('Error al cargar reglas:', error);

                    // Obtener el modal actual
                    const modalElement = document.getElementById('modalBankRules');
                    // Guardar el z-index original
                    const originalZIndex = modalElement.style.zIndex;
                    // Temporalmente reducir el z-index del modal para que el SweetAlert aparezca encima
                    modalElement.style.zIndex = '1040';

                    // Mostrar error
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Error al cargar las reglas del banco',
                        customClass: {
                            container: 'swal-higher-z-index'
                        }
                    }).then(() => {
                        // Restaurar el z-index original del modal
                        modalElement.style.zIndex = originalZIndex;
                    });

                    // Mostrar el formulario vacío de todos modos
                    document.querySelector("#modalBankRulesSpinner").style.display = "none";
                    document.querySelector("#modalBankRulesContent").style.display = "block";
                }
            });
        };

        // Función para actualizar la tabla de reglas
        const updateRulesTable = () => {
            const tableBody = document.getElementById('bankRulesTableBody');

            // Limpiar la tabla
            tableBody.innerHTML = '';

            // Mostrar mensaje si no hay reglas
            if (bankRules.length === 0) {
                tableBody.innerHTML = `
                    <tr id="noRulesRow">
                        <td colspan="4" class="text-center">No hay reglas configuradas para este banco.</td>
                    </tr>
                `;
                return;
            }

            // Ordenar reglas por prioridad (mayor a menor)
            const sortedRules = [...bankRules].sort((a, b) => {
                // Si la prioridad es la misma, ordenar por ID
                if (b.priority === a.priority) {
                    return a.id - b.id;
                }
                return b.priority - a.priority;
            });

            // Agregar cada regla a la tabla
            sortedRules.forEach(rule => {
                const row = document.createElement('tr');
                row.setAttribute('data-rule-id', rule.id);
                row.classList.add('cursor-move');
                row.innerHTML = `
                    <td>${rule.rule_match_type_display || getMatchTypeDisplay(rule.rule_match_type)}</td>
                    <td>${rule.pattern}</td>
                    <td>${rule.accounting_account_id ? rule.accounting_account_id + (rule.accounting_account_description ? ' - ' + rule.accounting_account_description : '') : 'No asignada'}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editRule(${rule.id})"><i class="fa fa-edit"></i></button>
                        <button class="btn btn-sm btn-danger" onclick="deleteRule(${rule.id})"><i class="fa fa-trash"></i></button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        };

        // Función para obtener el texto de visualización del tipo de regla
        const getTypeDisplay = (type) => {
            const types = {
                'auto_categorize': 'Categorización Automática',
                'auto_match': 'Coincidencia Automática',
                'auto_split': 'División Automática'
            };
            return types[type] || type;
        };

        // Función para obtener el texto de visualización de la acción
        const getActionDisplay = (action) => {
            const actions = {
                'assign_category': 'Asignar Categoría',
                'assign_account': 'Asignar Cuenta',
                'mark_reviewed': 'Marcar como Revisado'
            };
            return actions[action] || action;
        };

        // Función para obtener el texto de visualización del tipo de parámetro
        const getMatchTypeDisplay = (matchType) => {
            const matchTypes = {
                'equals': 'Igual que',
                'starts_with': 'Comienza con',
                'ends_with': 'Finaliza con',
                'contains': 'Contiene'
            };
            return matchTypes[matchType] || matchType;
        };

        // Función para editar una regla
        const editRule = (ruleId) => {
            debugLog('Editando regla ID:', ruleId);

            // Obtener el modal actual
            const modalElement = document.getElementById('modalBankRules');
            // Guardar el z-index original
            const originalZIndex = modalElement.style.zIndex;
            // Temporalmente reducir el z-index del modal para que el SweetAlert aparezca encima
            modalElement.style.zIndex = '1040';

            // Buscar la regla en el array
            const rule = bankRules.find(r => r.id === ruleId);
            if (!rule) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'No se encontró la regla',
                    customClass: {
                        container: 'swal-higher-z-index'
                    }
                }).then(() => {
                    // Restaurar el z-index original del modal
                    modalElement.style.zIndex = originalZIndex;
                });
                return;
            }

            // Restaurar el z-index original del modal
            modalElement.style.zIndex = originalZIndex;

            // Llenar el formulario con los datos de la regla
            document.getElementById('ruleId').value = rule.id;
            document.getElementById('ruleType').value = rule.rule_type;
            // Establecer el tipo de parámetro si existe, o dejarlo vacío para que el usuario seleccione
            document.getElementById('ruleMatchType').value = rule.rule_match_type || '';
            document.getElementById('rulePattern').value = rule.pattern || '';
            document.getElementById('ruleAction').value = rule.action || 'assign_category';
            document.getElementById('ruleValue').value = rule.value || 'default';

            // Establecer la cuenta contable si existe
            if (rule.accounting_account_id) {
                $('#bankRuleAccountSelect').val(rule.accounting_account_id).trigger('change');
            } else {
                $('#bankRuleAccountSelect').val('0').trigger('change');
            }

            // Mostrar el botón de cancelar
            document.getElementById('cancelEditBtn').style.display = 'block';

            // Cambiar el texto del botón de guardar
            document.getElementById('saveBankRuleFormBtn').textContent = 'Actualizar Regla';

            // Guardar el ID de la regla que se está editando
            editingRuleId = ruleId;

            // Hacer scroll al formulario
            document.getElementById('bankRulesForm').scrollIntoView({ behavior: 'smooth' });
        };

        // Función para cancelar la edición
        const cancelEditRule = () => {
            resetRuleForm();
        };

        // Función para resetear el formulario
        const resetRuleForm = () => {
            document.getElementById('bankRulesForm').reset();
            document.getElementById('ruleId').value = '';
            document.getElementById('cancelEditBtn').style.display = 'none';
            document.getElementById('saveBankRuleFormBtn').textContent = 'Guardar Regla';
            editingRuleId = null;

            // Asegurarse de que el select de tipo de parámetro tenga seleccionada la opción por defecto
            const ruleMatchTypeSelect = document.getElementById('ruleMatchType');
            if (ruleMatchTypeSelect) {
                ruleMatchTypeSelect.value = '';
            }

            // Resetear el select de cuenta contable
            $('#bankRuleAccountSelect').val('0').trigger('change');
        };

        // Función para eliminar una regla
        const deleteRule = (ruleId) => {
            debugLog('Eliminando regla ID:', ruleId);

            // Obtener el modal actual
            const modalElement = document.getElementById('modalBankRules');

            // Guardar el z-index original
            const originalZIndex = modalElement.style.zIndex;

            // Temporalmente reducir el z-index del modal para que el SweetAlert aparezca encima
            modalElement.style.zIndex = '1040';

            Swal.fire({
                title: '¿Está seguro?',
                text: 'Esta acción no se puede deshacer',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar',
                // Asegurar que SweetAlert tenga un z-index mayor
                customClass: {
                    container: 'swal-higher-z-index'
                }
            }).then((result) => {
                // Restaurar el z-index original del modal
                modalElement.style.zIndex = originalZIndex;
                if (result.isConfirmed) {
                    // Obtener el token CSRF
                    const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;

                    // Realizar la petición AJAX para eliminar la regla
                    $.ajax({
                        url: `/sellers/${dj.seller.shortname}/banks/rules/delete/`,
                        type: 'POST',
                        data: JSON.stringify({
                            rule_id: ruleId
                        }),
                        contentType: 'application/json',
                        headers: {
                            'X-CSRFToken': csrfToken
                        },
                        success: function(response) {
                            if (response.result === 'ok') {
                                // Actualizar la lista de reglas
                                bankRules = bankRules.filter(r => r.id !== ruleId);
                                updateRulesTable();

                                // Mostrar mensaje de éxito
                                const Toast = Swal.mixin({
                                    toast: true,
                                    position: 'top-end',
                                    showConfirmButton: false,
                                    timer: 3000,
                                    timerProgressBar: true,
                                    didOpen: (toast) => {
                                        toast.addEventListener('mouseenter', Swal.stopTimer);
                                        toast.addEventListener('mouseleave', Swal.resumeTimer);
                                    }
                                });

                                Toast.fire({
                                    icon: 'success',
                                    title: response.message || 'Regla eliminada correctamente'
                                });

                                // Mantener el modal visible y eliminar backdrops
                                fixModalState();
                            } else {
                                // Mostrar error
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message || 'Error al eliminar la regla'
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            // Mostrar error
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Error al eliminar la regla bancaria'
                            });
                        }
                    });
                }
            });
        };

        // Función para enviar el formulario (guardar o actualizar regla)
        const submitRuleForm = () => {
            const bankId = document.getElementById('bankRulesBankId').value;
            const ruleId = document.getElementById('ruleId').value;
            const ruleType = document.getElementById('ruleType').value;
            const ruleMatchType = document.getElementById('ruleMatchType').value;
            const rulePattern = document.getElementById('rulePattern').value;
            const ruleAction = document.getElementById('ruleAction').value;
            const ruleValue = document.getElementById('ruleValue').value;
            const accountId = $('#bankRuleAccountSelect').val();

            // Obtener el modal actual
            const modalElement = document.getElementById('modalBankRules');
            // Guardar el z-index original
            const originalZIndex = modalElement.style.zIndex;
            // Temporalmente reducir el z-index del modal para que el SweetAlert aparezca encima
            modalElement.style.zIndex = '1040';

            // Validar que todos los campos estén completos
            if (!ruleMatchType || ruleMatchType === '') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Tipo de parámetro requerido',
                    text: 'Por favor seleccione un tipo de parámetro de la lista',
                    customClass: {
                        container: 'swal-higher-z-index'
                    }
                }).then(() => {
                    // Restaurar el z-index original del modal
                    modalElement.style.zIndex = originalZIndex;
                });
                return;
            }

            if (!rulePattern || rulePattern.trim() === '') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Concepto requerido',
                    text: 'Por favor introduzca un concepto para la regla',
                    customClass: {
                        container: 'swal-higher-z-index'
                    }
                }).then(() => {
                    // Restaurar el z-index original del modal
                    modalElement.style.zIndex = originalZIndex;
                });
                return;
            }

            if (!accountId || accountId === '0') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Cuenta contable requerida',
                    text: 'Por favor seleccione una cuenta contable',
                    customClass: {
                        container: 'swal-higher-z-index'
                    }
                }).then(() => {
                    // Restaurar el z-index original del modal
                    modalElement.style.zIndex = originalZIndex;
                });
                return;
            }

            // Obtener el token CSRF
            const csrfToken = document.getElementsByName('csrfmiddlewaretoken')[0].value;

            // Realizar la petición AJAX para guardar la regla
            $.ajax({
                url: `/sellers/${dj.seller.shortname}/banks/rules/save/`,
                type: 'POST',
                data: JSON.stringify({
                    bank_id: bankId,
                    rule_id: ruleId || null,
                    rule_type: ruleType,
                    rule_match_type: ruleMatchType,
                    rule_pattern: rulePattern,
                    rule_action: ruleAction,
                    rule_value: ruleValue,
                    accounting_account_id: accountId
                }),
                contentType: 'application/json',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    debugLog('Respuesta al guardar regla:', response);

                    if (response.result === 'ok') {
                        // Mostrar mensaje de éxito
                        const Toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer);
                                toast.addEventListener('mouseleave', Swal.resumeTimer);
                            }
                        });

                        Toast.fire({
                            icon: 'success',
                            title: response.message || 'Regla guardada correctamente'
                        });

                        // Resetear el formulario
                        resetRuleForm();

                        // Mantener el modal visible y eliminar backdrops
                        fixModalState();

                        // Recargar las reglas
                        loadBankRules(bankId);
                    } else {
                        // Mostrar error
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Error al guardar la regla',
                            customClass: {
                                container: 'swal-higher-z-index'
                            }
                        });
                    }
                },
                error: function(xhr, status, error) {
                    debugLog('Error al guardar regla:', error);

                    // Mostrar error
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Error al guardar la regla bancaria',
                        customClass: {
                            container: 'swal-higher-z-index'
                        }
                    });
                }
            });
        };
        // Función para limpiar backdrops y resetear el estado del modal
        function cleanupModalBackdrops() {
            // Remove all modal backdrops
            $('.modal-backdrop').remove();

            // Reset body styles
            $('body').removeClass('modal-open');
            $('body').css('overflow', '');
            $('body').css('padding-right', '');

            // Make sure no other modals are shown
            $('.modal').each(function() {
                if ($(this).hasClass('show') && this.id !== 'modalBankRules') {
                    $(this).modal('hide');
                    $(this).removeClass('show');
                    $(this).css('display', 'none');
                }
            });

            // Force removal of any inline styles on body
            $('body').attr('style', '');

            // Ensure the modal is properly reset
            $('#modalBankRules').attr('aria-hidden', 'true');
            $('#modalBankRules').removeAttr('aria-modal');
            $('#modalBankRules').removeAttr('role');
        }

        // Función para mantener el modal visible y eliminar backdrops
        function fixModalState() {
            // Eliminar cualquier backdrop existente
            $('.modal-backdrop').remove();

            // Configurar el modal correctamente
            const modalElement = document.getElementById('modalBankRules');
            modalElement.classList.add('show');
            modalElement.style.display = 'block';
            modalElement.style.paddingRight = '17px';
            modalElement.removeAttribute('aria-hidden');
            modalElement.setAttribute('aria-modal', 'true');
            modalElement.setAttribute('role', 'dialog');

            // Asegurarse de que el body tenga los estilos correctos
            $('body').addClass('modal-open');
            $('body').css('overflow', 'hidden');
            $('body').css('padding-right', '17px');
        }

        // Función para aplicar las reglas bancarias a los movimientos pendientes
        function applyBankRules() {
            // Obtener el ID del banco actual
            const bankId = document.getElementById('bankRulesBankId').value;

            // Mostrar un spinner mientras se procesan las reglas
            Swal.fire({
                title: 'Aplicando reglas',
                text: 'Procesando movimientos bancarios...',
                allowOutsideClick: false,
                allowEscapeKey: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Realizar la petición AJAX para aplicar las reglas
            $.ajax({
                url: `/sellers/${dj.seller.shortname}/banks/rules/apply/`,
                type: 'POST',
                data: JSON.stringify({
                    bank_id: bankId
                }),
                contentType: 'application/json',
                headers: {
                    'X-CSRFToken': document.getElementsByName('csrfmiddlewaretoken')[0].value
                },
                success: function(response) {
                    Swal.close();

                    if (response.result === 'ok') {
                        // Mostrar mensaje de éxito con detalles
                        Swal.fire({
                            icon: 'success',
                            title: 'Reglas aplicadas correctamente',
                            html: `
                                <p>Se han procesado ${response.total_movements || 0} movimientos.</p>
                                <p>Se han aplicado reglas a ${response.matched_movements || 0} movimientos.</p>
                            `,
                            confirmButtonText: 'Aceptar'
                        }).then(() => {
                            // Recargar las tablas de movimientos para mostrar los cambios
                            if (selectedCard) {
                                const dtMovements = $(`#movements_bank_${selectedCard}`).DataTable();
                                const dtConciliate = $(`#conciliate_bank_${selectedCard}`).DataTable();

                                if (dtMovements) dtMovements.ajax.reload(null, false);
                                if (dtConciliate) dtConciliate.ajax.reload(null, false);

                                // Actualizar el saldo del banco
                                updateBankBalance(selectedCard);
                            }
                        });
                    } else {
                        // Mostrar mensaje de error
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Error al aplicar las reglas bancarias',
                            confirmButtonText: 'Aceptar'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();

                    // Mostrar mensaje de error
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Error al comunicarse con el servidor. Por favor, inténtelo de nuevo.',
                        confirmButtonText: 'Aceptar'
                    });
                }
            });
        }


        // Inicializar Sortable.js para la tabla de reglas
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar Sortable cuando se muestre el modal de reglas
            $('#modalBankRules').on('shown.bs.modal', function () {
                const sortableList = document.querySelector('.sortable-list');
                if (sortableList) {
                    const sortable = new Sortable(sortableList, {
                        animation: 150,
                        handle: 'tr',
                        ghostClass: 'bg-light',
                        onEnd: function(evt) {
                            // Actualizar prioridades después de reordenar
                            updateRulesPriorities();
                        }
                    });
                }
            });
        });

        // Manejador para cuando se oculta el modal
        $('#modalBankRules').on('hidden.bs.modal', function () {
            // Usar la función de limpieza para asegurar consistencia
            cleanupModalBackdrops();

            // Eliminar los manejadores de eventos personalizados
            $(document).off('keydown.modal');
            $(document).off('click.modal');

            // Asegurarse de que el modal esté completamente cerrado
            $('#modalBankRules').removeClass('show');
            $('#modalBankRules').css('display', 'none');

            // Pequeño retraso para asegurar que todo se ha limpiado correctamente
            setTimeout(function() {
                // Verificar si aún hay backdrops y eliminarlos
                if ($('.modal-backdrop').length > 0) {
                    $('.modal-backdrop').remove();
                }

                // Verificar si el body aún tiene clases o estilos de modal
                if ($('body').hasClass('modal-open')) {
                    $('body').removeClass('modal-open');
                }

                // Forzar la eliminación de cualquier estilo inline
                $('body').attr('style', '');
            }, 100);
        });

        // Manejador para cuando se muestra el modal
        $('#modalBankRules').on('shown.bs.modal', function () {
            // Usar nuestra función para mantener el modal visible y eliminar backdrops
            fixModalState();

            // Asegurarse de que el backdrop tenga el z-index correcto
            $('.modal-backdrop').css('z-index', '1040');
            $('#modalBankRules').css('z-index', '1050');

            // Agregar un pequeño retraso para asegurar que todo se ha configurado correctamente
            setTimeout(fixModalState, 100);
        });

        // Función para actualizar las prioridades de las reglas después de reordenar
        function updateRulesPriorities() {
            const rows = document.querySelectorAll('#bankRulesTableBody tr:not(#noRulesRow)');
            if (rows.length === 0) return;

            const bankId = document.getElementById('bankRulesBankId').value;
            const priorities = [];

            // Recopilar IDs de reglas en el nuevo orden
            rows.forEach((row, index) => {
                const ruleId = row.getAttribute('data-rule-id');
                if (ruleId) {
                    priorities.push({
                        id: parseInt(ruleId),
                        priority: rows.length - index // Mayor prioridad para las primeras reglas
                    });
                }
            });

            console.log('Actualizando prioridades:', {
                bank_id: bankId,
                priorities: priorities
            });

            // Enviar actualización al servidor
            $.ajax({
                url: `/sellers/${dj.seller.shortname}/banks/rules/update-priorities/`,
                type: 'POST',
                data: JSON.stringify({
                    bank_id: bankId,
                    priorities: priorities
                }),
                contentType: 'application/json',
                headers: {
                    'X-CSRFToken': document.getElementsByName('csrfmiddlewaretoken')[0].value
                },
                success: function(response) {
                    if (response.result === 'ok') {
                        const Toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer);
                                toast.addEventListener('mouseleave', Swal.resumeTimer);
                            }
                        });

                        Toast.fire({
                            icon: 'success',
                            title: 'Prioridades actualizadas correctamente'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Error al actualizar las prioridades'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error al actualizar prioridades:', xhr.responseText);
                    let errorMsg = 'Error al actualizar las prioridades de las reglas';

                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.message) {
                            errorMsg = response.message;
                        }
                    } catch (e) {
                        // Si no podemos parsear la respuesta, usamos el mensaje por defecto
                        if (xhr.status === 404) {
                            errorMsg = 'URL no encontrada. Verifica la configuración del servidor.';
                        }
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMsg
                    });
                }
            });
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
{% endblock javascripts %}