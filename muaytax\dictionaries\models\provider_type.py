from django.db import models

class ProviderType(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON><PERSON><PERSON>",
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción",
    )

    class Meta:
        verbose_name = "Tipo de Proveedor"
        verbose_name_plural = "Tipos de Proveedores"
    
    def __str__(self):
        return self.description
    
# @admin.register(ProviderType)
# class ProviderTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
