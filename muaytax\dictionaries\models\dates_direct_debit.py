from django.db import models

class DatesDirectDebit(models.Model):
    period = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        choices = [
            ('0A', 'Anual'),
            ('Q1', 'Trimestre 1'),
            ('Q2', 'Trimestre 2'),
            ('Q3', 'Trimestre 3'),
            ('Q4', 'Trimestre 4'),
            ('M1', 'Enero'),
            ('M2', 'Febrero'),
            ('M3', '<PERSON><PERSON>'),
            ('M4', 'Abril'),
            ('M5', 'Mayo'),
            ('M6', 'Jun<PERSON>'),
            ('M7', 'Julio'),
            ('M8', 'A<PERSON><PERSON>'),
            ('M9', 'Septiembre'),
            ('M10', 'Octubre'),
            ('M11', 'Noviembre'),
            ('M12', 'Diciembre'),
        ],
        verbose_name="Periodo"
    )

    model =  models.CharField(
        max_length=500,
        blank=True,
        null=True,
        choices = [
            ('all', 'General'),
            ('["ES-202"]', 'Modelo 202'),
        ],
        verbose_name="Modelo/s"
    )

    year = models.PositiveIntegerField(
        verbose_name="Año",
        blank=True,
        null=True
    )

    date = models.DateTimeField(
        verbose_name="Fecha de límite de la domiciliación en la APP",
        blank=True,
        null=True
    )

    start_date_aeat = models.DateTimeField(
        verbose_name="Fecha de inicio en HACIENDA",
        blank=True,
        null=True
    )

    date_aeat = models.DateTimeField(
        verbose_name="Fecha límite en HACIENDA",
        blank=True,
        null=True
    )

    class Meta:
        verbose_name = "Fecha de domiciliación"
        verbose_name_plural = "Fechas de domiciliación"

    def __str__(self):
        return f"Fecha limite de domiciliación para {self.period} del año {self.year}: del {self.start_date_aeat.strftime('%d/%m/%Y %H:%M')} al {self.date.strftime('%d/%m/%Y %H:%M')} del modelo {self.model}"
