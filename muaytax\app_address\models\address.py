from django.db import models
from django.urls import reverse


class Address(models.Model):
    # id -> AutoGen

    address_name = models.Char<PERSON>ield(
        max_length=100,
        default="Dirección",
        verbose_name="Nombre",
    )

    address = models.CharField(
        blank=True,
        null=True,
        max_length=100,
        verbose_name="Dirección",
    )

    address_number = models.CharField(
        blank=True,
        null=True,
        max_length=10,
        verbose_name="Número",
    )

    address_continue = models.CharField(
        blank=True,
        null=True,
        max_length=100,
        verbose_name="Dirección (Continuación)",
    )

    address_zip = models.CharField(
        blank=True,
        null=True,
        max_length=10,
        verbose_name="Código Postal",
    )

    address_city = models.CharField(
        blank=True,
        null=True,
        max_length=50,
        verbose_name="Ciudad",
    )

    address_state = models.Char<PERSON>ield(
        blank=True,
        null=True,
        max_length=50,
        verbose_name="Estado / Provincia / Región",
    )

    address_country = models.ForeignKey(
        "dictionaries.Country",
        related_name="address_country",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        verbose_name="País",
    )

    address_catastral = models.CharField(
        blank=True,
        null=True,
        max_length=100,
        verbose_name="Referencia Catastral",
        help_text="Solo aplica a España",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Dirección"
        verbose_name_plural = "Direcciones"

    def __str__(self):
        return self.address_name

    def get_absolute_url(self):
        return reverse("app_providers:detail", kwargs={"pk": self.pk})



