from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.urls import reverse
from muaytax.signals import disable_for_load_data
from django.core.validators import FileExtensionValidator

class AmazonTxtEur(models.Model):

    # id -> AutoGen

    file = models.FileField(
        "document", upload_to="uploads/amazon_txt_eur/", validators=[FileExtensionValidator(["txt","TXT"])]
    )

    amz_id = models.CharField(
        max_length=50, 
        blank=True, 
        null=True, 
        default=None,
        verbose_name="Amazon ID"
    )

    month = models.PositiveIntegerField(
        blank=True,
        null=True,
        default=None,
        verbose_name="Mes"
    )

    year = models.PositiveIntegerField(
        blank=True,
        null=True,
        default=None,
        verbose_name="Año",
    )

    status = models.ForeignKey(
        "dictionaries.TXTStatus",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="status_txt_amz_eur",
        verbose_name="Estado",
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="seller_txt_amz_eur",
        verbose_name="Vendedor",
    )

    error_message = models.CharField(
        max_length=250, 
        blank=True, 
        null=True,
        verbose_name="Mensaje de error"
    )

    is_email_send = models.BooleanField(
        default=False,
        verbose_name="Email informe de ventas de Amazon enviado"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TXT Amazon (EUR)"
        verbose_name_plural = "TXT Amazon (EUR)"
    
    def __str__(self):
        if self.file and self.file.name:
            return self.file.name.replace("uploads/", "").replace("amazon_txt_eur/", "")
        else:
            return "TXT AMAZON N.{}".format(self.pk)
           
    def get_absolute_url(self):
        return reverse("app_importers:amz_txt_eur_detail", kwargs={"pk": self.pk})
    
    def get_file_url(self):
        # return f'/media/uploads/amazon_txt_eur/{self.file.name}'
        if self.file.name.startswith('uploads/amazon_txt_eur/'):
            return f'/media/{self.file.name}'
        else:
            return f'/media/uploads/amazon_txt_eur/{self.file.name}'

@receiver(post_save, sender=AmazonTxtEur)
@disable_for_load_data
def after_sellervat_save(sender, instance, created, **kwargs):
    seller = instance.seller
    if created:
        if seller is not None and seller.pk is not None:
            # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
            year = instance.year
            # update_and_create_seller_cached_lists(seller.pk, year)
            from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
            update_cached_seller_signal_task.delay(seller_id=seller.pk, year=year)

@receiver(post_delete, sender=AmazonTxtEur)
@disable_for_load_data
def after_sellervat_delete(sender, instance, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
        # update_and_create_seller_cached_lists(seller)
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk)