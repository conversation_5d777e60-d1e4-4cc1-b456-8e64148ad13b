from django.db import models
from django.core import validators

class Reconciliation(models.Model):

    # id -> AutoGen

    movement = models.ForeignKey(
        "banks.Movement",
        related_name="reconciliation_movement",
        on_delete=models.PROTECT,
        verbose_name="Movimiento",
    )

    amount = models.DecimalField(
        default=0,
        max_digits=15,
        decimal_places=2,
        verbose_name="Importe",
    )

    invoice = models.ForeignKey(
        "invoices.Invoice",
        related_name="reconciliation_invoice",
        on_delete=models.PROTECT,
        verbose_name="Factura",
        null=True,
        blank=True,
    )

    accounting_account = models.ForeignKey(
        "dictionaries.AccountingAccount",
        related_name="reconciliation_accounting_account",
        on_delete=models.PROTECT,
        verbose_name="Cuenta Contable",
        null=True,
        blank=True,
    )

    accounting_account_detail = models.CharField(
        verbose_name="Detalle Cuenta Contable",
        max_length=9,
        null=True,
        blank=True,
        # validators=[
        #     # Add Validation: Char quantity = 9 (9 digits), and only numbers.
        #     validators.RegexValidator(
        #         regex=r'^\d{9}$',
        #         message="El detalle de la cuenta contable debe tener 9 dígitos.",
        #         code='invalid_accounting_account_detail'
        #     ),
        # ],
    )

    bank = models.ForeignKey(
        "banks.Bank",
        related_name="reconciliation_bank",
        on_delete=models.PROTECT,
        verbose_name="Cuenta Bancaria / Tarjeta Credito",
        null=True,
        blank=True,
    )

    movement_transfer = models.ForeignKey(
        "banks.Movement",
        related_name="reconciliation_transfer",
        on_delete=models.PROTECT,
        verbose_name="Transferencia",
        null=True,
        blank=True,
    )

    type =  models.ForeignKey(
        "dictionaries.ReconciliationType",
        related_name="reconciliation_type",
        on_delete=models.PROTECT,
        verbose_name="Tipo de Conciliación",
    )

    used_in_entry = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Conciliación Bancaria"
        verbose_name_plural = "Conciliaciones Bancarias"
    
    def __str__(self):
        if (self.type.pk == "invoice" and self.invoice):
            return f"Conciliacion ID {self.id}: Movimiento {self.movement.movement_number} - Factura {self.invoice.reference}"
        elif (self.type.pk == "bank" and self.bank):
            return f"Conciliacion ID {self.id}: Movimiento {self.movement.movement_number} - Cuenta Bancaria {self.bank.id}"
        # elif (self.type.pk == "accounts" and (self.accounting_account or self.accounting_account_detail)):
        #     return f"Conciliacion ID {self.id}: Movimiento {self.movement.movement_number} - Cuenta Contable {self.account.id}"
        # elif (self.type.pk == "transfer" and self.movement_transfer):
        #     return f"Conciliacion ID {self.id}: Movimiento {self.movement.movement_number} - Transferencia {self.movement_transfer.movement_number}"
        else:
            return f"Conciliacion ID {self.id}: Movimiento {self.movement.movement_number}"
        
        