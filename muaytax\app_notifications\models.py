from django.db import models
from django_celery_beat.models import PeriodicTask

from muaytax.utils.mixins import CustomTimeStampedModel
from muaytax.app_notifications.choices import TaskStatus, NotificationMethod

class NotificationTask(CustomTimeStampedModel):
    title = models.CharField(
        max_length=255,
        verbose_name="Título de la notificación"
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="Descripción"
    )
    sender = models.ForeignKey(
        "dictionaries.MuaytaxDepartment",
        on_delete=models.CASCADE,
        related_name='notification_department_sender',
        verbose_name="Departamento emisor",
        help_text="Departamento que envía la notificación",
        blank=True,
        null=True,
    )
    scheduled_datetime = models.DateTimeField(
        verbose_name="Fecha programada",
        help_text="Fecha y hora en la que se enviará la notificación"
    )
    periodic_task = models.OneToOneField(
        PeriodicTask,
        on_delete=models.CASCADE,
        related_name='notification_task_periodic',
        null=True,
        blank=True,
    )
    notification_method = models.CharField(
        max_length=255,
        choices=NotificationMethod.choices,
        default=NotificationMethod.EMAIL,
        verbose_name="Método de notificación",
        help_text="Método o vía de la notificación"
    )
    status = models.CharField(
        max_length=255,
        choices=TaskStatus.choices,
        default=TaskStatus.PENDING,
        verbose_name="Estado de la notificación"
    )
    executed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Fecha de ejecución"
    )
    extra_arguments = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Argumentos extra",
        help_text="Argumentos adicionales para la tarea"
    )

    def __str__(self):
        return f"{self.title} ({self.sender.name if self.sender else 'No Sender'})"
    
    class Meta:
        verbose_name = "Notificación programada"
        verbose_name_plural = "Notificaciones programadas"
        ordering = ['-created_at']

class NotificationTaskReport(CustomTimeStampedModel):
    task = models.ForeignKey(
        NotificationTask,
        on_delete=models.CASCADE,
        related_name='notification_report_task',
        verbose_name="Notificación programada"
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="notification_report_seller",
        verbose_name="Vendedor",
    )
    notification_sent = models.BooleanField(
        default=False,
        verbose_name="¿Se envió la notificación?",
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name="Mensaje de error",
        help_text="Mensaje de error si la notificación no se envió"
    )

    def __str__(self):
        return f"{self.seller} - {self.task.title}"
    
    class Meta:
        verbose_name = "Reporte de notificación"
        verbose_name_plural = "Reportes de notificaciones"
        ordering = ['-created_at']