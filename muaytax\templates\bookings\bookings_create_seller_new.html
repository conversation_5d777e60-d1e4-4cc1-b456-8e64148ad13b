{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Soporte MuayTax
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" href="{% static 'assets/css/bookings/main.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <style>
    .prev-btn::before{
      content: " ";
      width: 20%;
      height: 100%;
      position: absolute;
      cursor: pointer;
      left: 0;
      top: 0;
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
    }
    .next-btn::before{
      content: " ";
      width: 20%;
      height: 100%;
      position: absolute;
      cursor: pointer;
      right: 0;
      top: 0;
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
    }
    .prev-btn:hover::before,
    .next-btn:hover::before{
      background-color: #90deff69;
    }

    .spinner-grow.animation-delay-1 {
        animation: spinner-grow .7s .1s linear infinite;
        color: #04ac64!important;
    }

    .spinner-grow.animation-delay-2 {
        animation: spinner-grow .7s .3s linear infinite;
        color: #36e093!important;
    }

    .spinner-grow.animation-delay-3 {
        animation: spinner-grow .7s .5s linear infinite;
        color: #04ac64!important;
    }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
             Soporte
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_bookings:list_bookings_seller' user.seller.shortname %}">Mis llamadas</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_bookings:new_booking' user.seller.shortname %}">Solicitar una llamada</a>
            </li>
            <li class="breadcrumb-item">
              <a href="">{{ subject }}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}

{% if messages %}
<div class="row">
  <div class="col-md-12">
    {% for message in messages %}
      <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    {% endfor %}
  </div>
</div>
{% endif %}

<div class="col-12">
  <div class="card info">
    <div class="header">
			<ol class="steps">
        <li class="active step-pick-subSubjects" id="step-pick-subSubjects" style="cursor: pointer;">
          <a href="" style="cursor: pointer;">Información</a>
        </li>
				<li class="step-pick-appointment" id="step-pick-appointment" style="cursor: default;">
          Elige el horario
        </li>
				<li class="step-confirmation" style="cursor: default;">
          Confirmación
        </li>
			</ol>
		</div>
    <div class="step-container">
      <div class="content_wrapper ">

        <!-- STEP 1 information begins -->
        <div class="information-step" id="information-step">
          {% if subject.code == 'contract-service' %}
          <div class="subjects-choices-area">
            <div class="title-subject">
              <span class="fw-bolder">Selecciona uno o varios temas que quieres tratar en la llamada</span>
            </div>
            <form class="row g-3" id="infoForm">
              <div class="subjects-box" id="topicSelect">
                <!-- <div class="subject-options">
                  <input type="checkbox" id="subject15" name="subjects" value="Dudas generales" >
                  <label class="cursor-pointer" for="subject15">Dudas generales</label>
                </div> -->
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject1" name="subjects" value="LLC de empresas (EEUU)" >
                  <label class="cursor-pointer text-center" for="subject1">LLC empresas (USA)</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject2" name="subjects" value="SL/Autónomo España">
                  <label class="cursor-pointer text-center" for="subject2">SL/Autónomo España</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject3" name="subjects" value="IVA España">
                  <label class="cursor-pointer text-center" for="subject3">IVA España</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject4" name="subjects" value="IVA Alemania">
                  <label class="cursor-pointer text-center" for="subject4">IVA Alemania</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject5" name="subjects" value="IVA Italia">
                  <label class="cursor-pointer text-center" for="subject5">IVA Italia</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject6" name="subjects" value="IVA Francia">
                  <label class="cursor-pointer text-center" for="subject6">IVA Francia</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject7" name="subjects" value="IVA Reino Unido">
                  <label class="cursor-pointer text-center" for="subject7">IVA Reino Unido</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject8" name="subjects" value="IVA Polonia">
                  <label class="cursor-pointer text-center" for="subject8">IVA Polonia</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject9" name="subjects" value="IVA Japón">
                  <label class="cursor-pointer text-center" for="subject9">IVA Japón</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject10" name="subjects" value="IVA Países Bajos">
                  <label class="cursor-pointer text-center" for="subject10">IVA Países Bajos</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject11" name="subjects" value="IVA República Checa">
                  <label class="cursor-pointer text-center" for="subject11">IVA República Checa</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject12" name="subjects" value="IVA Suecia">
                  <label class="cursor-pointer text-center" for="subject12">IVA Suecia</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject13" name="subjects" value="IVA USA">
                  <label class="cursor-pointer text-center" for="subject13">IVA USA</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject14" name="subjects" value="IVA EAU">
                  <label class="cursor-pointer text-center" for="subject14">IVA EAU</label>
                </div>
              </div>
              <label for="optionalComment"><span class="fw-bolder">Comentarios adicionales (mínimo 10 caracteres)</span></label>
              <textarea class="form-control" id="optionalComment" rows="3" placeholder="Escribe aquí más información para poder ayudarte mejor en la llamada..."></textarea>
              <div class="find-bookings">
                <button type="submit" class="btn btn-dark" id="loadCalendar" disabled>Buscar horarios disponibles</button>
              </div>
            </form>
          </div>
          {% endif %}
          {% if subject.code == 'llc-support' %}
          <div class="subjects-choices-area">
            <div class="title-subject">
              <span class="fw-bolder">Selecciona uno o varios temas que quieres tratar en la llamada</span>
            </div>
            <form class="row g-3" id="infoForm">
              <div class="subjects-box" id="topicSelect">
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject1" name="subjects" value="Dudas App" >
                  <label class="cursor-pointer" for="subject1">Dudas APP</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject2" name="subjects" value="Dudas generales" >
                  <label class="cursor-pointer" for="subject2">Dudas generales</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject3" name="subjects" value="Registro">
                  <label class="cursor-pointer" for="subject3">Registro</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject4" name="subjects" value="Contabilidad (LLC)">
                  <label class="cursor-pointer" for="subject4">Contabilidad</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject5" name="subjects" value="Mantenimiento (LLC)" >
                  <label class="cursor-pointer" for="subject5">Mantenimiento</label>
                </div>
                <div class="subject-options">
                  <input class="d-none" type="checkbox" id="subject6" name="subjects" value="Presentación de modelos (LLC)" >
                  <label class="cursor-pointer" for="subject6">Presentación de modelos</label>
                </div>
              </div>
              <label for="optionalComment"><span class="fw-bolder">Comentarios adicionales (mínimo 10 caracteres)</span></label>
              <textarea class="form-control" id="optionalComment" rows="3" placeholder="Escribe aquí más información para poder ayudarte mejor en la llamada..."></textarea>
              <div class="find-bookings">
                <button type="submit" class="btn btn-dark" id="loadCalendar" disabled>Buscar horarios disponibles</button>
              </div>
            </form>
          </div>
          {% endif %}
          {% if subject.code == 'form-5472-1120' %}
          <div class="subjects-choices-area">
            <form class="row g-3" id="infoForm">
              <label for="optionalComment"><span class="fw-bolder">Escribe tus dudas acerca del formulario 5472_1120 (mínimo 10 caracteres)</span></label>
              <textarea class="form-control" id="optionalComment" rows="3" placeholder="Escribe aquí tus dudas para poder ayudarte mejor en la llamada..."></textarea>
              <div class="find-bookings">
                <button type="submit" class="btn btn-dark" id="loadCalendar">Buscar horarios disponibles</button>
              </div>
            </form>
          </div>
          {% endif %}
          {% if subject.code == 'boir-support' %}
          <div class="subjects-choices-area">
            <form class="row g-3" id="infoForm">
              <label for="optionalComment"><span class="fw-bolder">Escribe tus dudas acerca del BOIR (mínimo 10 caracteres)</span></label>
              <textarea class="form-control" id="optionalComment" rows="3" placeholder="Escribe aquí tus dudas para poder ayudarte mejor en la llamada..."></textarea>
              <div class="find-bookings">
                <button type="submit" class="btn btn-dark" id="loadCalendar">Buscar horarios disponibles</button>
              </div>
            </form>
          </div>
          {% endif %}
          {% if subject.code == 'contracted-accounting' and not seller.legal_entity == 'llc' %}
          <div class="subjects-choices-area">
            <form class="row g-3" id="infoForm">
              <label for="topicSelect"><span class="fw-bolder">¿En qué tema necesitas que te ayudemos?</span></label>
              <select class="form-select" id="topicSelect" name="topics" required>
                <option value="" disabled selected>Selecciona sólo una opción</option>
                {% if seller.can_access_register_support %}
                  <option value="Soporte Alta/Migración">Soporte Alta/Migración</option>
                {% else %}
                  <option value="Dudas generales">Dudas generales</option>
                  <option value="Nóminas">Nóminas</option>
                  <option value="Presentación de modelos">Presentación de modelos</option>
                  <option value="Registro de autónomo/SL">Registro de autónomo/SL</option>
                {% endif %}
              </select>
              <label for="optionalComment"><span class="fw-bolder">Comentarios adicionales (mínimo 10 caracteres)</span></label>
              <textarea class="form-control" id="optionalComment" rows="3" placeholder="Escribe aquí más información para poder ayudarte mejor en la llamada..."></textarea>
              <div class="find-bookings">
                <button type="submit" class="btn btn-dark" id="loadCalendar" disabled>Buscar horarios disponibles</button>
              </div>
            </form>
          </div>
          {% endif %}
          {% if subject.code == 'vat-country' %}
            <div class="subjects-choices-area">
              <form class="row g-3" id="infoForm">
                <label for="topicSelect"><span class="fw-bolder">¿En qué tema necesitas que te ayudemos?</span></label>
                <select name="topics" id="topicSelect" class="form-select" required>
                  <option value="" disabled selected>Selecciona sólo una opción</option>
                  <option value="Dudas App">Dudas APP</option>
                  <option value="Registro de País IVA">Registro de País IVA</option>
                  {% if seller_vat_ES %}
                    <option value="Mantenimiento País IVA-España">Mantenimiento País IVA (España)</option>
                  {% endif %}
                  {% if seller_vat_rest %}
                    <option value="Mantenimiento País IVA-Resto de Países">Mantenimiento País IVA (Resto de Países)</option>
                  {% endif %}
                </select>
                <label for="optionalComment"><span class="fw-bolder">Comentarios adicionales (mínimo 10 caracteres)</span></label>
                <textarea class="form-control" id="optionalComment" rows="3" placeholder="Escribe aquí más información para poder ayudarte mejor en la llamada..."></textarea>
                <div class="find-bookings">
                  <button type="submit" class="btn btn-dark" id="loadCalendar" disabled>Buscar citas disponibles</button>
                </div>
              </form>
            </div>
          {% endif %}
          
        </div>

        <!-- STEP 2 pick appointment begins -->
        <div class="pick-appointment" id="pick-appointment" style="display: none;">
          <div class="info_box">
            <div class="duration_message_box">
              <label>
                <span class="fw-bolder">Duración de la llamada telefónica</span>
              </label>
              <div>
                <span>
                  <i class="far fa-clock"></i>
                  &nbsp;Ahora puedes elegir entre 15 o 30 minutos según la disponibilidad.
              </div>
            </div>
            <div class="time_zone_box">
              <label>
                <span class="fw-bolder">Zona horaria de la llamada</span>
              </label>
              <div>
                <span>
                  <i class="fa fa-user-clock"></i>
                  &nbsp;	Central European Time (GMT +02:00)
                </span>
              </div>
            </div>
          </div>
          <!-- booking area begins  -->
          <div class="booking-wrapper">
            <div class="booking-box">

              <!-- NEEEEEEEEW -->
              <div class="row" id="newCalendarArea">

                <!-- BLOQUE 1: CALENDARIO -->
                <div class="col-xl-6 mb-5">
                  <div class="max-area-calendar">
                    <!-- SELECTOR DE MES -->
                    <div class="col-12" style="border-radius: 0.25rem;">
                      <div class="card">
                        <div class="card-header" style="border-radius: 0.5rem; border-bottom: unset; background-color: var(--cal-header);">
                          <div class="row align-items-center ">
                            <div class="col-2">
                              <div class="prev-btn">
                                <i role="button" class="fas fa-chevron-left text-white" style="z-index: 9999;"></i>

                              </div>
                            </div>
                            <div class="col-8">
                              <h4 class="month mb-0 text-white"></h4>
                            </div>
                            <div class="col-2">
                              <div class="next-btn">
                                <i role="button" class="fas fa-chevron-right  text-white"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- FILA CON LOS DIAS DE LA SEMANA -->
                    <div class="col-12">
                      <div class="weekdays" style="font-size: clamp(12px, 14px, 16px)!important;">
                        <div class="day">Lun</div>
                        <div class="day">Mar</div>
                        <div class="day">Mie</div>
                        <div class="day">Jue</div>
                        <div class="day">Vie</div>
                        <div class="day">Sab</div>
                        <div class="day">Dom</div>
                      </div>
                    </div>
                    <!-- FILA CON LOS DIAS DEL MES -->
                    <div class="col-12">
                      <div class="days">
                        <!-- days are added with js -->
                      </div>
                    </div>
                  </div>
                </div>

                <!-- BLOQUE 2: HORAS -->
                <div class="col-xl-6 mb-5">
                  <div class="max-area-horarios">

                    <div class="col-12">
                      <div class="card">
                        <div class="card-header title-container bg-dark" style="border-radius: 0.5rem;">
                        </div>
                      </div>
                    </div>
                    <!-- HORARIOS -->
                    <div class="col-12">
                      <div class="time-options-area">
                        <!-- options are rendered with js -->
                        <div class="time-options time-options-content flex-row">
                        </div>
                        <hr class="wid-80 pt-1 mx-auto my-4" id="breakLine">

                        <!-- spinner -->
                        <div class="col-12">
                          <div class="" id="spinner">
                            <div class="spin-loader" aria-hidden="true">
                              <!-- loading animation -->
                            </div>
                          </div>
                        </div>

                      </div>
                    </div>
                    
                    <!-- EMPIEZA LA DURACIÓN -->
                    {% if subject.code == 'contract-service' %}
                    <div class="col-12 d-none" id="durationOption">
                      <label for="inputPassword" class="col-sm-12 col-md-12 col-lg-3 col-form-label">
                        <i class="fas fa-clock"></i>
                        <span class="fw-bolder">Duración:</span>
                      </label>
                      <div class="form-group row">
                        <div class="col-sm-12 col-md-12">
                          <div class="form-group d-inline">
                            <div class="radio radio-success d-inline">
                                <input type="radio" name="durationSelector" id="duration15"  value="1" checked>
                                <label for="duration15" class="cr">15 min</label>
                            </div>
                          </div>
                          <small class="form-text d-block text-info">
                            <i class="fas fa-info-circle" role="button" data-bs-toggle="tooltip" title="Solo se permiten llamadas de 15 minutos para este servicio"></i>
                            Se ha fijado la duración en 15 minutos
                          </small>
                        </div>
                        <div class="col-12 mt-3">
                          <button type="button" class="btn btn-dark ml-2 btn-continue" onclick="handleStepTransition(2)">Siguiente<i class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i></button>
                        </div>
                      </div>
                    </div>
                    {% else %}
                    <div class="col-12 d-none" id="durationOption">
                      <label for="inputPassword" class="col-sm-12 col-md-12 col-lg-3 col-form-label">
                        <i 
                        class="fas fa-clock"></i>
                        <span class="fw-bolder">Duración:</span>
                      </label>
                      <div class="form-group row">
                        
                        <div class="col-sm-12 col-md-12">
                          <div class="form-group d-inline">
                            <div class="radio radio-success d-inline">
                                <input type="radio" name="durationSelector" id="duration15"  value="1" onchange="handleDurationChange(this)">
                                <label for="duration15" class="cr">15 min</label>
                            </div>
                          </div>
                          <div class="form-group d-inline" id="extraDurationDiv">
                            <!-- extra opción depende de la hora seleccionada -->
                          </div>
                          <small class="form-text d-block text-info">
                            <i class="fas fa-info-circle" role="button" data-bs-toggle="tooltip" title="Indica entre las opciones, el tiempo de duración que necesitas para la llamada"></i>
                            Selecciona entre las opciones de duración disponibles
                          </small>
                        </div>
                        <div class="col-12 mt-3">
                          <button type="button" class="btn btn-dark ml-2 btn-continue" onclick="handleStepTransition(2)">Siguiente<i class="feather icon-arrow-right" style="margin-left: 12px; margin-right: 0;"></i></button>
                        </div>
                      </div>
                    </div>
                    {% endif %}
                    <!-- FINALIZA LA DURACIÓN -->
                  </div>
                </div>
                <!-- FIN BLOQUE 2 -->
              </div>
              
              <div class="spinner-container" id="connectingSpinner" style="gap: 10px;">
                <div class="spin-loader" aria-hidden="true">
                  <!-- loading animation -->
                </div>
                <p>Buscando horarios disponibles...</p>
              </div>
            </div>
          </div>
          <!-- booking area ends  -->

        </div>
        <!--  STEP 2 pick appointment ends -->

        <!-- STEP 3 confirmation begins  -->
        <div class="confirmation" id="confirmation" style="display: none;">
          <button class="back-button change-pick-appointment" type="button" aria-expanded="true" onclick="handleStepTransition(1)">
            <i class="fa fa-chevron-left" style="margin-right: 7px;"></i>
            Editar la información de la solicitud
          </button>
          <form id="bookForm" method="post" enctype="multipart/form-data" action="">
            {% csrf_token %}
            <div id="managers_input" class="d-none">
            </div>
            <input type="hidden" id="id_date" name="date" value="" required>
            <input type="hidden" id="id_topics" name="topics" value="" required>
            <textarea class="form-control d-none" name="comments" id="optionalCommentForm" rows="3" placeholder="Escribe aquí tus comentarios"></textarea>
            <div class="confirmation-area">
              <div class="confirmation-details">
                <div class="appointment-details">
                  <h3>DETALLES DE LA LLAMADA TELEFÓNICA</h3>
                  <div class="d-inline-flex">
                    <h4><b>Asunto:&nbsp;</b></h4>
                    <h4>{{ subject.description }}</h4>
                  </div>
                  <div class="d-inline-flex">
                    <h4><b>Fecha:&nbsp;</b></h4>
                    <h4 class="confirm-date-label"></h4>
                  </div>
                  <div class="d-inline-flex">
                    <h4><b>Hora:&nbsp;</b></h4>
                    <h4 class="confirm-time-label"></h4>
                    <i class="fas fa-info-circle" role="button" data-bs-toggle="tooltip" title="Recuerda que la hora de la llamada es en la zona horaria de Madrid, España"></i>
                  </div>
                  <div class="d-inline-flex">
                    <h4><b>Duración:&nbsp;</b></h4>
                    <input type="hidden" id="id_duration" name="duration" value="1" required>
                    <h4 id="confirmDuration">15 min</h4>
                  </div>
                  <span>
                    <i class="far fa-clock"></i>
                    &nbsp;Recuerda que ahora la duración de la llamada es variable y puedes elegir <b>entre 15 o 30 minutos</b> según la disponibilidad.
                  </span>
                </div>
                <div class="confirmation-buttons">
                  <button type="button" data-bs-toggle="modal" data-bs-target="#cancelModal" class="btn btn-outline-danger booking-button">Cancelar</button>
                  <button id="confirm-booking" type="submit" class="btn btn-primary booking-button">Confirmar</button>
                </div>
              </div>
            </div>
          </form>
          <!-- ... Confirmation content ... -->
        </div>
        <!-- confirmation ends -->

      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content check-booking">
      <div class="modal-body d-flex justify-content-center">
        <p>¿Estás seguro que quieres cancelar el proceso de solicitud?</p>
      </div>
      <div class="modal-footer d-flex justify-content-center">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modal" aria-label="Close">No
        </button>
        <a href="{% url 'app_bookings:new_booking'  user.seller.shortname %}" class="btn btn-danger">Sí, cancelar</a>
      </div>
    </div>
  </div>
</div>

<div class="modal fade modal-animate anim-blur " id="bookingModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
      <div class="modal-body d-flex flex-column justify-content-center align-items-center">

        <div class="modal-body">
          <div class="d-flex justify-content-center align-items-center text-center">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <p>&nbsp;</p>
              <img style="width:110px;" src="{% static 'assets/images/logo.png' %}"/>
          </div>
          <p class="text-white text-center mb-0"><b>Se está generando tu solicitud de llamada telefónica</b></p>
          <p class="text-white text-center mb-0"><b>Por favor espera...</b></p>
          
        </div>

      </div>
    </div>
  </div>
</div>

<script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
<script src="{% static 'assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js' %}"></script>
<script type="text/javascript">
  // declaro variable de SweetAlert
  const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
  });

  const shortname = "{{ user.seller.shortname }}";
  let holidays = [];
  let allDayAbsences = [];

  // Inicializar con la fecha actual
  const now = new Date();
  let firstAvailableDay = now.getDate();
  let firstMonthAvailable = now.getMonth();
  let firstYearAvailable = now.getFullYear();
  let currentMonth = now.getMonth();
  let currentYear = now.getFullYear();

  let limitDays = 10;
  let managers = "";
  let dateSelected = "";
  let previousDateSelected = "";

  const daysContainer = document.querySelector(".days");
  const monthLabel = document.querySelector(".month");
  const nextBtn = document.querySelector(".next-btn");
  const prevBtn = document.querySelector(".prev-btn");

  const months = [
  "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio",
  "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre",
  ];

  const days = ["Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"];

  const calendarArea = document.getElementById("newCalendarArea");
  const connectingAnimation = document.querySelector("#connectingSpinner");
  const bookingSubject = "{{ subject.code|safe }}";
  
  
  calendarArea.style.display = "none";

  // function to render days
  const renderCalendar = () => {
    // Verificar que tengamos valores válidos antes de renderizar
    if (currentYear === 0 || currentMonth === undefined || currentMonth === null) {
      console.error("Error: Año o mes no válidos para renderizar el calendario");
      monthLabel.innerHTML = "Error al cargar el calendario";
      daysContainer.innerHTML = "";
      return;
    }

    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const lastDayIndex = lastDay.getDay();
    const lastDayDate = lastDay.getDate();
    const prevLastDay = new Date(currentYear, currentMonth, 0);
    const prevLastDayDate = prevLastDay.getDate();
    let nextDays = 7 - lastDayIndex;
    if (lastDayIndex == 0) {
      nextDays = 0;
    }

    monthLabel.innerHTML = `${months[currentMonth]} ${currentYear}`;

    let daysHTML = "";

    if (currentMonth == firstMonthAvailable) {
      // prev days of the month
      for (let x = (firstDay.getDay() === 0 ? 7 : firstDay.getDay()); x > 1; x--) {
          const prevDay = prevLastDayDate - x + 2;
          const dateString = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(prevDay).padStart(2, '0')}`;
          daysHTML += `<div class="day bd-h-50 prev" data-date="${dateString}" >${prevLastDayDate - x + 2}</div>`;
      }
      // days before current date
      for (let i = 1; i <= firstAvailableDay; i++) {
        const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        if (i==firstAvailableDay){
          daysHTML += `<div class="day bd-h-50 first-day bookable" data-date="${dateString}">${i}</div>`;
        }else{
          daysHTML += `<div class="day bd-h-50 prev" data-date="${dateString}">${i}</div>`;
        }
      }
      // rest of the days of the month
      for (let i = firstAvailableDay + 1; i <= lastDayDate; i++) {
        const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        if (limitDays && new Date(currentYear, currentMonth, i).getDay() >= 1 && new Date(currentYear, currentMonth, i).getDay() <= 5 && !holidays.includes(dateString) && !allDayAbsences.includes(dateString) ) {
          daysHTML += `<div class="day bd-h-50 bookable" data-date="${dateString}">${i}</div>`;
          limitDays--;
        }else{
          daysHTML += `<div class="day bd-h-50 next" data-date="${dateString}">${i}</div>`;
        }
      }
      // next days of the month
      for (let j = 1; j <= nextDays; j++) {
          const dateString = `${currentYear}-${String(currentMonth + 2).padStart(2, '0')}-${String(j).padStart(2, '0')}`;
          daysHTML += `<div class="day bd-h-50 next" data-date="${dateString}">${j}</div>`;
      }
    }else{
      for (let x = (firstDay.getDay() === 0 ? 7 : firstDay.getDay()); x > 1; x--) {
            const prevDay = prevLastDayDate - x + 2;
            const dateString = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(prevDay).padStart(2, '0')}`;
            daysHTML += `<div class="day bd-h-50 prev" data-date="${dateString}" >${prevLastDayDate - x + 2}</div>`;
      }
      for (let i = 1; i <= lastDayDate; i++) {
        const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        if (limitDays && new Date(currentYear, currentMonth, i).getDay() >= 1 && new Date(currentYear, currentMonth, i).getDay() <= 5 && !holidays.includes(dateString) && !allDayAbsences.includes(dateString) ) {
          daysHTML += `<div class="day bd-h-50 bookable" data-date="${dateString}">${i}</div>`;
          limitDays--;
        }else{
          daysHTML += `<div class="day bd-h-50 next" data-date="${dateString}">${i}</div>`;
        }
      }
      // next days of the month
      for (let j = 1; j <= nextDays; j++) {
          const dateString = `${currentYear}-${String(currentMonth + 2).padStart(2, '0')}-${String(j).padStart(2, '0')}`;
          daysHTML += `<div class="day bd-h-50 next" data-date="${dateString}">${j}</div>`;
      }
    }
    // run this function with every calendar render
    daysContainer.innerHTML = daysHTML;
    prevBtn.children[0].classList.add("disable-arrow");
    nextBtn.children[0].classList.remove("disable-arrow");

  };

  //****** navigation of the calendar **********//
  let canNavigateNext = true;
  let canNavigatePrev = false;

  nextBtn.addEventListener("click", () => {
    if (canNavigateNext){
        // increase current month by one
        const nextMonth = currentMonth + 1;
        if (nextMonth <= 11) {
            currentMonth = nextMonth;
        } else {
            currentMonth = 0;
            currentYear++;
        }
        canNavigateNext = false;
        canNavigatePrev = true;
        renderCalendar();

        const selectedDay = document.querySelector(`[data-date="${dateSelected}"]`);
        if (selectedDay && selectedDay.classList.contains("bookable")) {
          selectedDay.classList.add("selected-day");
        }

        nextBtn.children[0].classList.add("disable-arrow");
        prevBtn.children[0].classList.remove("disable-arrow");
    }
  });

  prevBtn.addEventListener("click", () => {
    if (canNavigatePrev) {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        canNavigatePrev = false;
        canNavigateNext = true;
        limitDays = 10;
        renderCalendar();

        const selectedDay = document.querySelector(`[data-date="${dateSelected}"]`);
        if (selectedDay && selectedDay.classList.contains("bookable")) {
          selectedDay.classList.add("selected-day");
        } 

        nextBtn.children[0].classList.remove("disable-arrow");
        prevBtn.children[0].classList.add("disable-arrow");
    }
  });


  async function findFirstAvailableDay(bookingSubject, topics, shortname) {
    let baseUrl = `{% url 'app_bookings:find_first_available' 'SHORTNAME_PLACEHOLDER' %}`;
    let url = baseUrl.replace('SHORTNAME_PLACEHOLDER', shortname);
    url += `?subject=${encodeURIComponent(bookingSubject)}&topics=${encodeURIComponent(topics)}`;

    // Ocultar el área del calendario mientras se cargan los datos
    calendarArea.style.display = "none";
    connectingAnimation.style.display = "flex";

    try {
      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        Toast.fire({
          icon: 'error',
          title: errorData.error || 'Error al buscar horarios disponibles'
        });

        // Ocultar el spinner de carga
        connectingAnimation.style.display = "none";

        // Volver al paso anterior
        const infoArea = document.getElementById("information-step");
        if (!infoArea) {
          location.reload(); // Recargar la página si no podemos retroceder correctamente
        } else {
          handleStepTransition(0);
        }
        return;
      }

      const data = await response.json();

      // Solo inicializar las variables si tenemos datos válidos
      if (data && data.availableDay && data.availableMonth && data.availableYear) {
        firstAvailableDay = data.availableDay;
        firstMonthAvailable = data.availableMonth - 1;
        firstYearAvailable = data.availableYear;
        managers = data.managers;
        allDayAbsences = data.allDayAbsences;

        currentYear = firstYearAvailable;
        currentMonth = firstMonthAvailable;

        renderCalendar();
        selectFirstAvailableDay();

        // Mostrar el calendario
        calendarArea.style.display = "flex";
      } else {
        throw new Error('Datos de fechas disponibles incompletos');
      }

    } catch (error) {
      console.error("Error obteniendo fechas disponibles:", error);
      Toast.fire({
        icon: 'error',
        title: 'Error al buscar horarios disponibles. Por favor, inténtalo de nuevo.'
      });

      // Ocultar el spinner de carga
      connectingAnimation.style.display = "none";

      // Volver al paso anterior
      const infoArea = document.getElementById("information-step");
      if (!infoArea) {
        location.reload(); // Recargar la página si no podemos retroceder correctamente
      } else {
        handleStepTransition(0);
      }
    } finally {
      // Asegurarse de ocultar el spinner en todos los casos
      connectingAnimation.style.display = "none";
    }
  }

  fetch("/bookings/upcoming-holidays/")
    .then(response => response.json())
    .then(data => {
      holidays = data.map(holiday => holiday.date);
    })
    .catch(error => console.log("Error catching holidays", error));
  
  function selectFirstAvailableDay() {
    const firstAvailableDayElement = document.querySelector('.first-day');
    if (firstAvailableDayElement) {
      firstAvailableDayElement.click();
    }
  }

  daysContainer.addEventListener("click", async (event) => {
    const clickedDay = event.target;
    
    if (clickedDay.classList.contains("selected-day")){
        return;
    }

    if (clickedDay.classList.contains("bookable")) {
        const selectedDay = document.querySelector(".selected-day");

        if (selectedDay) {
          selectedDay.classList.remove("selected-day");
        }

        clickedDay.classList.add("selected-day");
        previousDateSelected = dateSelected;
        dateSelected = clickedDay.dataset.date;
        clearOptions();

        if (connectingAnimation.style.display === "none"){
          document.querySelector("#spinner").style.display = "block";
        }

        const date = clickedDay.dataset.date;
        try {
          let baseUrl = `{% url 'app_bookings:get_booking_spots' 'SHORTNAME_PLACEHOLDER' %}`;
          let url = baseUrl.replace('SHORTNAME_PLACEHOLDER', shortname);
          url += `?selectedDate=${encodeURIComponent(date)}&managers=${encodeURIComponent(managers)}&subject=${encodeURIComponent(bookingSubject)}`;

          const response = await fetch(url);

          if (!response.ok) {
            if (response.status === 400) {
              const errorData = await response.json();
              Toast.fire({
                icon: 'error',
                title: errorData.error || 'Error al obtener horarios disponibles'
              });
            } else {
              Toast.fire({
                icon: 'error',
                title: 'Error al obtener horarios para este día. Intenta con otro día'
              });
            }
            // Eliminar la selección del día
            clickedDay.classList.remove("selected-day");
            if (selectedDay) {
              selectedDay.classList.add("selected-day");
              dateSelected = previousDateSelected;
            }
            return;
          }

          const data = await response.json();
          const time_spots = data.time_spots;

          if (time_spots.length > 0){
              renderTimeAvailable(time_spots);
          }else{
              showNoTimeAvailableMessage();
          }
          updateDateTitle(clickedDay, currentMonth);

        } catch (error) {
          console.error('Error llamando a bookings api:', error);
          Toast.fire({
            icon: 'error',
            title: 'Error al cargar los horarios. Por favor, inténtalo de nuevo.'
          });
          // Eliminar la selección del día
          clickedDay.classList.remove("selected-day");
          if (selectedDay) {
            selectedDay.classList.add("selected-day");
            dateSelected = previousDateSelected;
          }
        } finally {
          document.querySelector("#spinner").style.display = "none";
          connectingAnimation.style.display = "none";
          calendarArea.style.display = "flex";
        }
    }
  });

  // Function to update the date title in the time options
  function updateDateTitle(clickedDay, currentMonth) {
      const dateTitle = document.querySelector(".title-container");
      dateTitle.innerHTML = "";
      const date = document.createElement("input"); //hidden date in the form used to pass the date to the view
      date.type = "hidden";
      date.name = "booking_date";
      date.value = clickedDay.dataset.date;
      dateTitle.appendChild(date);
      const titleLabel = document.createElement("h4");
      titleLabel.classList.add("mb-0", "text-white", "date-title");
      const dayIndex = new Date(clickedDay.dataset.date).getDay();
      titleLabel.textContent = days[dayIndex] + " " + clickedDay.textContent + " de " + months[currentMonth];
      dateTitle.appendChild(titleLabel);
  }

  // Function to render time options based on the data
  function renderTimeAvailable(data) {
      const timeOptionsContainer = document.querySelector(".time-options");
      timeOptionsContainer.innerHTML = "";
      const breakLine = document.querySelector("#breakLine");
      breakLine.classList.remove("d-none");

      data.forEach(slot => {
        const { time, is_available, managers } = slot;

        if (is_available) {
            const timeDiv = document.createElement("div");
            timeDiv.classList.add("btn-time");

            const input = document.createElement("input");
            input.classList.add("booking-time-input");
            input.type = "radio";
            input.id = time;
            input.name = "booking_time";
            input.value = time;
            input.dataset.managers = managers;
            input.required = true;

            const label = document.createElement("label");
            label.classList.add("time-label");
            label.setAttribute("for", time);
            label.textContent = time;

            // esta parte añade el atributo data-next a los horarios que tienen un horario siguiente disponible
            
            // checks if the next time slot available is 15 minutes after the current one
            const nextTime = new Date(`2022-01-01 ${time}`);
            nextTime.setMinutes(nextTime.getMinutes() + 15);
            const nextTimeStr = nextTime.toTimeString().slice(0, 5);
            const nextTimeAvailable = data.find(slot => slot.time == nextTimeStr && slot.is_available);
            if (nextTimeAvailable) {
              input.dataset.next = true;
            }
            

            timeDiv.appendChild(input);
            timeDiv.appendChild(label);
            timeOptionsContainer.appendChild(timeDiv);
        }
      });
  }
  
  function sortTimeDictionary(data) {
    const sortedData = {};
    Object.keys(data).sort().forEach(function(key) {
      sortedData[key] = data[key];
    });
    return sortedData;
  }

  function showNoTimeAvailableMessage() {
    const timeOptionsContainer = document.querySelector(".time-options");
    const noSpotsDiv = document.createElement("div");
    noSpotsDiv.classList.add("no-spots-container");
    const noSpotsLabel = document.createElement("p");
    noSpotsLabel.classList.add("no-spots-label");
    noSpotsLabel.textContent = "No hay horarios disponibles";
    noSpotsDiv.appendChild(noSpotsLabel);
    timeOptionsContainer.appendChild(noSpotsDiv);
  }

  // display day and time selected when clicking on a time option
  const timeOptionsContainer = document.querySelector(".time-options");
  let durationSelector = document.querySelectorAll('input[name="durationSelector"]');

  timeOptionsContainer.addEventListener("click", (event) => {
    const clickedTime = event.target;
    const timeLabel = document.querySelector(".confirm-time-label");

    // const dateLabel = document.querySelector(".selected-date-label");
    if (clickedTime.classList.contains("booking-time-input")) {

      previousTimeSelected = timeLabel.textContent.slice(0, -2);
      if (previousTimeSelected && previousTimeSelected == clickedTime.value && dateSelected == previousDateSelected) {
        return;
      }
      previousDateSelected = dateSelected;

      // condición para no mostrar el selector de duración si el horario seleccionado es de consultoría
      if (["emparlerin", "juandiego", "webmaster"].some(name => managers.includes(name))) {
        showDurationConsultoria(clickedTime);
      }
      else {
        showExtraDurationOption(clickedTime);
      }
      durationSelector = document.getElementsByName("durationSelector");
      timeLabel.textContent = clickedTime.value + " h - (GMT +2)";
      timeLabel.innerHTML += "&nbsp;";

      // adding the inputs of the managers to the form
      document.querySelector("#managers_input").innerHTML = "";
      const managersIds = clickedTime.dataset.managers.split(",");
      managersIds.forEach(manager => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = "managers";
        input.value = manager;
        console.log("manager", manager);
        document.querySelector("#managers_input").appendChild(input);
      });
    }
  });

  // function to change confirmDuration when durationselector changes
  function handleDurationChange (radio) {
    const confirmDuration = document.querySelector("#confirmDuration");
    confirmDuration.textContent = (parseInt(radio.value) * 15) + " min";
    const durationInput = document.querySelector("#id_duration");
    durationInput.value = radio.value;

  }

  function clearOptions() {
    // hideContinueBox();
    hideDurationOption();
    const timeOptionsContainer = document.querySelector(".time-options");
    timeOptionsContainer.innerHTML = "";
    const breakLine = document.querySelector("#breakLine");
    breakLine.classList.add("d-none");
    const dateTitle = document.querySelector(".date-title");
    if (dateTitle){
        // remove the date title and add a blank space to avoid the calendar to move
        dateTitle.innerHTML = "&nbsp;";
    }
  }

  function hideDurationOption() {
    const durationOption = document.querySelector("#durationOption");
    durationOption.classList.add("d-none");
  }

  function showExtraDurationOption(clickedTime) {
    // MUESTRO EL DIV PADRE DEL AREA DE OPCIONES
    const durationOption = document.querySelector("#durationOption");
    durationOption.classList.remove("d-none");

    // MUESTRO EL SELECTOR DE 30 MINUTOS SI EL HORARIO SELECCIONADO ES LO PERMITE
    const extraDurationDiv = document.querySelector("#extraDurationDiv");
    extraDurationDiv.innerHTML = "";
    if (clickedTime.dataset.next) {
      let radioDiv = document.createElement("div");
      radioDiv.classList.add("radio", "radio-success", "d-inline");
      
      let inputElement = document.createElement("input");
      inputElement.type = "radio";
      inputElement.name = "durationSelector";
      inputElement.id = "duration30";
      inputElement.value = "2";
      inputElement.setAttribute("onchange", "handleDurationChange(this)");

      let labelElement = document.createElement("label");
      labelElement.classList.add("cr");
      labelElement.setAttribute("for", "duration30");
      labelElement.textContent = "30 min";

      radioDiv.appendChild(inputElement);
      radioDiv.appendChild(labelElement);

      extraDurationDiv.appendChild(radioDiv);
    }
    $("#duration15").prop("checked", false);
    handleDurationChange(document.querySelector("#duration15"));
  }
  
  function showDurationConsultoria(clickedTime) {
    const durationOption = document.querySelector("#durationOption");
    durationOption.classList.remove("d-none");
    handleDurationChange(document.querySelector("#duration15"));
  }

</script>

<!-- handle steps script -->
<script>
  let currentStep = 0;
  let prevStep = 0;

  function updateStep(step) {
    const listItems = document.querySelectorAll('.steps li');
    listItems.forEach((item, index) => {
      item.classList.remove('active');
    });
    listItems[step].classList.add('active');
  }

  // Function to handle the step transition and update the list items.
  function handleStepTransition(nextStep) {
    const steps = document.querySelectorAll('.steps li');
    if (nextStep >= 0 && nextStep < steps.length) {
      // check if currentStep is different from nextStep
      if (currentStep !== nextStep) {
        prevStep = currentStep;
        currentStep = nextStep;
        updateStep(currentStep);
        const pickAppointment = document.getElementById("pick-appointment");
        const confirmation = document.getElementById("confirmation");
        
        if (currentStep == 1){
          if(prevStep == 2) {
            pickAppointment.style.display = "block";
            confirmation.style.display = "none";
          } else {
            const comments = document.querySelector("#optionalComment");
            const commentValid = comments.value.trim().length >= 10;

            if (!commentValid) {
              prevStep = 0;
              currentStep = 0;
              updateStep(currentStep);
              // set focus on the comments textarea and show "complete this field message"
              comments.focus();
              
              Toast.fire({
                icon: 'error',
                title: 'Por favor, escribe un comentario de al menos 10 caracteres'
              });
              return;
            }

            const infoArea = document.getElementById("information-step");
            const topics = document.querySelector("#id_topics");
            const commentsForm = document.querySelector("#optionalCommentForm");
            const topicSelect = document.getElementById("topicSelect");

            if (topicSelect) {
              topics.value = topicSelect.tagName === "SELECT" 
                ? topicSelect.value 
                : Array.from(document.querySelectorAll('input[name="subjects"]:checked'))
                    .map(cb => cb.value)
                    .join(', ');
            } else {
              topics.value = "";
            }

            if (comments) {
              commentsForm.value = comments.value;
            }
            
            infoArea.parentElement.removeChild(infoArea);

            pickAppointment.style.display = "block";
            connectingAnimation.style.display = "flex";
            findFirstAvailableDay(bookingSubject, topics.value, shortname);
          }
        }
        else if (currentStep === 0) {
          pickAppointment.style.display = "block";
          confirmation.style.display = "none";
        } 
        else if (currentStep === 2) {
          // check if one duration is selected
          const durationSelector = document.querySelectorAll('input[name="durationSelector"]');
          let oneChecked = false;
          durationSelector.forEach((radio) => {
            if (radio.checked) {
              oneChecked = true;
            }
          });
          if (!oneChecked) {
            currentStep = 1;
            prevStep = 1;
            updateStep(currentStep);
            Toast.fire({
              icon: 'error',
              title: 'Por favor, selecciona la duración'
            });
            
            return;
          }
          pickAppointment.style.display = "none";
          confirmation.style.display = "block";
          // update the confirmation date and time labels
          const dateLabel = document.querySelector(".confirm-date-label");
          const selectedDate = document.querySelector(".date-title");
          dateLabel.textContent = selectedDate.textContent;

          // update the crispy form inputs with the selected date and time togeter
          const clickedDay = document.querySelector(".selected-day");
          const timeSelected = document.querySelector(".booking-time-input:checked");
          const dateTimeObject = clickedDay.dataset.date + " " + timeSelected.value + ":00";

          const dateInput = document.querySelector("#id_date");
          dateInput.value = dateTimeObject;
        }
      }
    }
  }
  // updateStep(currentStep);
  function toggleCheckbox(div) {
    const checkbox = div.querySelector('input[type="checkbox"]');
    if (checkbox) {
      checkbox.checked = !checkbox.checked;
      div.classList.toggle('checked', checkbox.checked);
    }
  }

  // check the big boxes when clicking on the div
  const subjectBox = document.querySelectorAll('.subject-options');
  if (subjectBox) {
    subjectBox.forEach((div) => {
      div.addEventListener('click', (event) => {
        event.preventDefault();
        toggleCheckbox(div);
        checkLoadButton();
      });
    });
  };

  // function that checks at least one checkbox is selected
  function checkLoadButton() {
    const checkBoxes = document.querySelectorAll('input[name="subjects"]');
    const loadButton = document.querySelector("#loadCalendar");
    let oneChecked = false;
    // check if at least one checkbox is checked
    checkBoxes.forEach((checkbox) => {
      if (checkbox.checked) {
        oneChecked = true;
      }
    });

    if (oneChecked) {
      loadButton.disabled = false;
    } else {
      loadButton.disabled = true;
    }
  }

  const loadCalendarButton = document.querySelector("#loadCalendar");
  const topicSelect = document.getElementById("topicSelect");
  const infoForm = document.querySelector("#infoForm");

  infoForm.addEventListener('submit', function(event) {
    event.preventDefault();
    handleStepTransition(1);
  });

  infoForm.addEventListener('change', function() {
    if (topicSelect){
      if (topicSelect.tagName == "SELECT"){
        loadCalendarButton.disabled = !topicSelect.value;
      }else{
      const checkBoxes = document.querySelectorAll('input[name="subjects"]');
      const atLeastOneChecked = Array.from(checkBoxes).some(checkbox => checkbox.checked);
      loadCalendarButton.disabled = !atLeastOneChecked;
      }
    }
    else{
      loadCalendarButton.disabled = false;
    }
  });

  const confirmBookingBtn = document.getElementById('confirm-booking');
  confirmBookingBtn.addEventListener('click', function() {
    confirmBookingBtn.disabled = true;
    confirmBookingBtn.textContent = "Generando...";
    const bookingModal = document.querySelector("#bookingModal");
    const modal = new bootstrap.Modal(bookingModal);
    modal._config.backdrop = 'static';
    modal._config.keyboard = false;
    modal.show();
    const form = document.getElementById('bookForm');
    form.submit();
  });

  document.addEventListener('DOMContentLoaded', function () {

    let dateErrorForm = "{{ date_error }}";

    if (dateErrorForm == "True" ) {
      Toast.fire({
        icon: 'error',
        title: 'Error al generar la solicitud. Por favor, inténtalo de nuevo.'
      });
    }
  });
</script>

{% endblock content %}