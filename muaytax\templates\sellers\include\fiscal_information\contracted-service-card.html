<div
    id="{{service}}"
    class="col-md-4 col-lg-4 col-xl-3 d-flex align-items-stretch "
    style="width: 330px !important;"
    hx-target="#contractedServiceCard"
    hx-swap="outerHTML">
    <div class="card contracted-service-card" style="width: 300px !important;">
        <div class="card-header">
            <h5 id="{{service}}" title="{{ service_data.service_name }}" class="truncate-text-sidebar-link">{{ service_data.service_name }}</h5>
            <div class="card-header-right">
                <div class="btn-group card-option">
                    <button type="button" class="btn dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="feather icon-more-horizontal"></i>
                    </button>
                    <ul class="list-unstyled card-option dropdown-menu dropdown-menu-end">
                        <li class="dropdown-item edit-card">
                            <a id="edit-service" href="#!"
                                data-service-id="{{service}}" data-service-name="{{service_data.service_name}}"
                                data-service-start-date="{{service_data.start_date|date:"Y-m-d" }}"
                                data-service-end-date="{{ service_data.end_date|date:"Y-m-d" }}"
                                data-service-payment-date="{{ service_data.payment_date|date:"Y-m-d" }}"
                                {% if service == 'contracted_accounting' %}
                                data-tax-agency-accounting-date="{{ service_data.tax_agency_accounting_date|date:"Y-m-d" }}"
                                data-tax-agency-accounting-end-date="{{ service_data.tax_agency_accounting_end_date|date:"Y-m-d" }}"
                                {% endif %}
                                data-service-registration-purchase-date="{{ service_data.service_registration_purchase_date|date:"Y-m-d" }}"
                                data-service-cancellation-purchase-date="{{ service_data.service_cancellation_purchase_date|date:"Y-m-d" }}"
                            ><i class="feather icon-edit"></i> editar</a>
                        </li>
                        <li class="dropdown-item close-card">
                            <a id="delete-service-drop-btn" href="#!"
                                data-service-id="{{service}}">
                                <i class="feather icon-trash"></i>
                                eliminar
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card-body">
            {% if service != 'contracted_accounting' %}
            <!-- Diseño estándar para los demás servicios -->
            <div class="row">
                <div class="col-12">
                    <label for="start_date">Fecha inicio</label>
                    <h1 class="f-18 mt-2">{{service_data.start_date|date:"d/M/Y"|lower }}</h1>
                </div>
                <div class="col-12 mt-3">
                    <label for="end_date">Fecha fin</label>
                    <h1 class="f-18 mt-2">{{ service_data.end_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
                <div class="col-12 mt-3">
                    <label for="payment_date">Fecha inicio de cobro</label>
                    <h1 class="f-18 mt-2">{{ service_data.payment_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
            </div>
            {% else %}
            <!-- Diseño específico para "Contabilidad ES" -->
            <div class="row">
                <div class="col-6">
                    <label for="start_date">Fecha inicio</label>
                    <h1 class="f-18 mt-2">{{ service_data.start_date|date:"d/M/Y"|lower }}</h1>
                </div>
                <div class="col-6">
                    <label for="end_date">Fecha fin</label>
                    <h1 class="f-18 mt-2">{{ service_data.end_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-6">
                    <label for="tax_agency_accounting_date">Alta Hacienda</label>
                    <h1 class="f-18 mt-2">{{ service_data.tax_agency_accounting_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
                <div class="col-6">
                    <label for="tax_agency_accounting_end_date">Baja Hacienda</label>
                    <h1 class="f-18 mt-2">{{ service_data.tax_agency_accounting_end_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <label for="payment_date">Fecha inicio de cobro</label>
                    <h1 class="f-18 mt-2">{{ service_data.payment_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
            </div>
            {% endif %}

            <!-- FECHAS DE COMPRA: Solo mostrar para servicios específicos -->
            {% if service == 'contracted_accounting' %}
            <div class="row mt-3">
                <div class="col-6">
                    <label for="service_registration_purchase_date">Fecha de compra del servicio</label>
                    <h1 class="f-18 mt-2">{{ service_data.service_registration_purchase_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
                <div class="col-6">
                    <label for="service_cancellation_purchase_date">Fecha de compra de la baja</label>
                    <h1 class="f-18 mt-2">{{ service_data.service_cancellation_purchase_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
            </div>
            {% elif service == 'contracted_maintenance_llc'%}
            <div class="row mt-3">
                <div class="col-6">
                    <label for="service_registration_purchase_date">Fecha de compra del servicio LLC</label>
                    <h1 class="f-18 mt-2">{{ service_data.service_registration_purchase_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
                <div class="col-6">
                    <label for="service_cancellation_purchase_date">Fecha de compra de la baja LLC</label>
                    <h1 class="f-18 mt-2">{{ service_data.service_cancellation_purchase_date|date:"d/M/Y"|lower|default:"---" }}</h1>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
