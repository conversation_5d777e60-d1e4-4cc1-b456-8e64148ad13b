# 📘 Guía de Convenciones para Nombres de Archivos

Esta guía define una convención estandarizada para nombrar archivos de estilos (`.css`), plantillas (`.html`), scripts (`.js`), configuraciones (`.json`), SQL (`.sql`) y módulos de Python (`.py`) en el proyecto.

## 📐 Estructura General

### Archivos `.py`, `.html`, `.js`, `.sql`, `.json`, etc.

```
modulo_contexto([ACRONIMO]*)[_contexto2*(ACRONIMO*)]_alcance[_etiqueta*][_v1*].ext
```

- `modulo`: nombre del módulo principal. Usar estilo *camelCase*.
- `contexto`: el propósito o función principal. Usar *camelCase*, los contextos múltiples se separan con `_`.
- `ACRONIMO`: siglas especiales como `IVA`, `LLC`, `M184`, en mayúscula, sin separación.
- `alcance`: indica el tipo de funcionalidad. Usar minúscula sin separador.
- `etiqueta` (opcional): describe características adicionales (`start`, `end`, `custom`, etc.).
- `v1`, `v2` (opcional): versión específica.
- * " que es opcional

### Archivos `.css`

```
modulo-contexto(ACRONIMO*)[-contexto2*(ACRONIMO*)][].alcance[-etiqueta*][_v1*].css
```

- `modulo`: igual que en otros.
- `contexto`: se usa `-` como separador entre contextos.
- `ACRONIMO`: se agrega pegado y en mayúscula.
- `alcance`: se separa con un punto (`.`) antes de la extensión.
- `etiqueta`, `v1` igual que en los demás.
- * " que es opcional

## ✅ Ejemplos

| Tipo de archivo  | Nombre válido                           | Descripción                                       |
|------------------|-----------------------------------------|---------------------------------------------------|
| Python           | `vat_formIVA_review_logic.py`           | Lógica del formulario de revisión de IVA          |
| HTML             | `vat_formIVA_review_panel.html`         | Panel de revisión para formulario IVA             |
| CSS              | `vat-formIVA-review.style.css`          | Estilos generales de revisión de formulario IVA   |
| CSS v2           | `vat-formIVA-review.style_v2.css`       | Versión 2 de los estilos                          |
| JS               | `vat_validation_formIVA_submit.js`      | JS de validación para envío de formulario IVA     |
| JSON             | `vat_formIVA_review_config.json`        | Configuración del contexto de revisión IVA        |
| SQL              | `vat_formIVA_review_query.sql`          | Consulta específica para revisión IVA             |

---

# 📚 Tabla de Alcances (`scope`) – Convención de Nombres de Archivos

Esta tabla define los diferentes alcances posibles (`scope`) para el nombrado estructurado de archivos en un proyecto. Cada fila incluye el nombre técnico, su equivalente en inglés, su traducción al español y una breve descripción de uso.

| Alcance     | Inglés                       | Español                | Descripción y uso típico                                           |
|------------------|-------------------------|------------------------|--------------------------------------------------------------------|
| `base`      | Base structure               | base                   | Estructura común reutilizable entre múltiples módulos              |
| `form`      | Form or form logic           | formulario             | Formularios de entrada de datos, validación y control              |
| `view`      | View or UI render            | vista                  | Vista lógica o plantilla visual principal                          |
| `modal`     | Modal window                 | emergente / diálogo    | Componentes modales de interacción o información                   |
| `card`      | UI card element              | tarjeta visual         | Caja de información agrupada por tema                              |
| `table`     | Data or UI table             | tabla                  | Representación tabular de datos                                    |
| `config`    | Configuration file           | configuración          | Parámetros, ajustes, settings personalizados                       |
| `summary`   | Summary or dashboard logic   | resumen                | Vista de resumen o dashboard general                               |
| `review`    | Review or validation view    | revisión               | Revisión de datos o validación de formularios                      |
| `submit`    | Submit logic                 | envío                  | Lógica para confirmación, acciones de guardar o enviar             |
| `filter`    | Filtering logic              | filtro                 | Búsqueda o selección dinámica de registros                         |
| `serializer`| DRF/Django serializer        | serializador           | Adaptación de modelos a estructuras planas o API                   |
| `logic`     | Business logic               | lógica                 | Funciones internas o procesos de negocio                           |
| `style`     | Styling rules (CSS)          | estilos                | Reglas visuales o de diseño                                        |
| `partial`   | Template partial or fragment | parcial / fragmento    | Fragmentos HTML reutilizables o subcomponentes                     |
| `alert`     | Alert or warning block       | alerta                 | Avisos visuales para errores, éxito o advertencias                 |
| `validator` | Data validation logic        | validador              | Validaciones especiales adicionales                                |
| `dropdown`  | Dropdown menu                | menú desplegable       | Lista de opciones seleccionables                                   |
| `tab`       | Navigation tab               | pestaña                | Navegación por secciones                                           |
| `stepper`   | Step-by-step flow            | pasos / flujo          | Flujos en varias etapas o formularios secuenciales                 |
| `template`  | Base layout or structure     | plantilla base         | Maqueta o esqueleto de página base                                 |
| `api`       | API endpoint logic           | punto de API           | Controlador de lógica expuesta por API                             |
| `hook`      | Signals or event triggers    | conectada / señal      | Señales de Django, hooks personalizados                            |
| `schema`    | Database schema or structure | esquema de DB          | Migraciones, modelos estructurales                                 |
| `test`      | Unit or integration test     | prueba / test          | Pruebas automatizadas o unitarias                                  |
| `task`      | Asynchronous / scheduled task| tarea programada / asíncrona| Procesos en segundo plano, tareas Celery                      |
| `panel`     | Dashboard or visual container| panel                  | Contenedor visual con widgets o resumen de datos                   |
| `endpoint`  | API route or handler         | manejador de endpoint  | Ruta y controlador de acceso a API                                 |
| `query`     | SQL or ORM query             | consulta               | Consultas complejas a base de datos                                |
| `service`   | External/internal service    | servicio               | Servicio funcional reutilizable (correo, validaciones, etc.)       |

---

## 🔖 Etiquetas opcionales (tags)

| Etiqueta              | Uso previsto                            |
|-----------------------|------------------------------------------|
| `_start`              | Lógica inicial                           |
| `_end`                | Proceso o finalización                   |
| `_new`                | Nueva versión o estructura limpia        |
| `_modal`              | Aplicación dentro de una ventana modal  |
| `_panel`              | Componente de panel visual               |
| `_core`               | Parte esencial del sistema               |
| `_draft`              | Borrador o en fase experimental          |
| `_custom`             | Personalización                          |
| `_extended`           | Versión extendida o avanzada             |
| `_warning`            | Versión extendida o avanzada             |
| `_step1`, `_step2`    | Fase o paso de un proceso multi-etapa |

---

## 📌 Notas adicionales

- Todas las palabras clave en nombres de archivos están en inglés.
- Las siglas (IVA, SL, LLC, M184...) se escriben siempre en mayúscula y pegadas al contexto.
- El nombre del módulo debe ser consistente entre archivos del mismo apartado.
- El contexto puede contener múltiples bloques si tiene sentido semántico.
- El uso de `_v1`, `_v2` es útil para mantener versiones paralelas.

---

## 🧪 Ejemplos adicionales de nombres válidos

| Archivo    | Descripción breve                                 |
|----------------------------------|----------------------------------------------------|
| `country_migration_formLLC_start.html` | Formulario de inicio de migración LLC             |
| `invoice_summary_query.sql`         | Consulta SQL para resumen de facturas             |
| `seller_card_summary.js`           | Tarjeta resumen visual de un vendedor             |
| `vat-formIVA-review.style_draft.css`| Estilos en borrador para revisión de formulario   |
| `client_dataLLC_view_end.py`       | Vista final de datos de cliente para LLC          |