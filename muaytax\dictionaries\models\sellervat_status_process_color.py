from django.db import models

class SellerVatStatusProcessColor(models.Model):

    code = models.CharField(
        blank=True,
        primary_key=True, 
        max_length=50,
        verbose_name="Código"
    )

    description = models.Char<PERSON>ield(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )


    class Meta:
        verbose_name = "Color del estado del proceso del Pais IVA"
        verbose_name_plural = "Colores de los estados de procesos de los Paises IVA"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatStatusProcessColor)
# class SellerVatStatusProcessColorAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]