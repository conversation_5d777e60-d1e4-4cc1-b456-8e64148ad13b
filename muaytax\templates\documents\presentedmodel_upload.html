{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}

{% block stylesheets %}
    <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
    <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/dropzone/dropzone.min-v5.css' %}" type="text/css" />
    <link rel="stylesheet" crossorigin href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}" />
    <style>
      .dropzone-container {
        height: 100% !important;
      }
      .dropzone {
        position: relative;
        width: 100%;
        height: 100% !important;
        background-image: url("https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png");
        background-size: 85px;
        background-repeat: no-repeat;
        background-position: center 25%;
      }
      .dropzone .dz-message {
        display: block;
        position: absolute;
        top: 33%;
        right: 50%;
        transform: translateX(50%);
        text-align: center;
      }
      .dropzone .dz-preview.dz-error .dz-error-message {
        display: none !important;
      }
    </style>
{% endblock stylesheets %}

{% block title %}
  Cargador de Modelos
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Modelos</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{ seller.name|title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_documents:presented_model_list' seller.shortname %}">Modelos</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Cargador Modelos</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <!-- Cargar Documentos | START -->
    <div class="col-12" id="uploads">
      <div class="card">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>Cargador Modelos</h5>
          </div>
        </div>
        <div class="card-body border">
          <div class="row">
            <div class="col-12 col-xl-6 mb-3">
              <div class="row justify-content-start">
                <div class="col-12">
                  <label class="form-label" for="inputPaisIva">
                    País de la Declaración:
                  </label>
                  <select
                    class="form-select form-control"
                    id="inputPaisIva"
                    v-model="inputPaisIva"
                  >
                    <option :value="null" selected>Selecciona</option>
                    <option v-for="vat in dj.seller_vat" :key="vat.vat_country" :value="vat.vat_country">
                      [[ getCountryNameByCode(vat.vat_country) ]]
                    </option>
                  </select>
                </div>
                <div class="col-12 mt-3">
                  <label class="form-label" for="inputModel">
                    Modelo de la Declaración:
                  </label>
                  <select
                    class="form-select form-control"
                    id="inputModel"
                    v-model="inputModel"
                  >
                    <option :value="null" selected>Selecciona</option>
                    <option v-for="model in getModelsByCountry(inputPaisIva)" :key="model.pk" :value="model.pk">
                      [[ model.description ]]
                    </option>
                  </select>
                </div>
                <div class="col-12 mt-3">
                  <label class="form-label" for="inputPresentationType">
                    Tipo de declaración:
                  </label>
                  <select
                    class="form-select form-control"
                    id="inputPresentationType"
                    v-model="inputPresentationType"
                  >
                    <option :value="null">Selecciona</option>
                    <option v-for="type in getPresentationTypeByCountry(inputPaisIva)" :key="type.pk" :value="type.pk">
                      [[ type.description ]]
                    </option>
                  </select>
                </div>
              </div>
              <div class="row justify-content-start">
                <div class="col-6 mt-3">
                  <label class="form-label" for="inputPeriod">
                    Periodo de la Declaración:
                  </label>
                  <select
                    class="form-select form-control"
                    id="inputPeriod"
                    v-model="inputPeriod"
                  >
                    <option :value="null" selected>Selecciona</option>
                    <option v-for="period in dj.period" :key="period.pk" :value="period.pk">
                      [[ period.description ]]
                    </option>
                  </select>
                </div>
                <div class="col-6 mt-3">
                  <label class="form-label" for="inputYear">
                    Año de la Declaración:
                  </label>
                  <select
                    class="form-select form-control"
                    id="inputYear"
                    v-model="inputYear"
                  >
                    <option :value="null">Selecciona</option>
                    <option value="2025" selected>2025</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                    <option value="2020">2020</option>
                  </select>
                </div>
                <div class="col-12 mt-3" v-if="inputPaisIva=='ES' && inputPresentationType=='general-presentation' && ['ES-111', 'ES-115', 'ES-130', 'ES-180', 'ES-190', 'ES-202', 'ES-303', 'ES-309', 'ES-347', 'ES-349', 'ES-390'].includes(inputModel)">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="inputOcrProcess"
                      v-model="inputOcrProcess"
                    />
                    <label class="form-check-label" for="inputOcrProcess">
                      ¿Deseas procesar esta declaración con el OCR?
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-xl-6 mb-3">
              <div style="border: 2px solid rgba(0, 0, 0, .3); border-radius: 5px; height: 100%;" v-show="!(inputPaisIva && inputModel && inputPeriod && inputPresentationType && inputYear)">
                <div class="swal2-icon swal2-warning swal2-icon-show" style="display: flex;">
                  <div class="swal2-icon-content">!</div>
                </div>
                <div class="container mt-3">
                  <div class="alert alert-warning align-content-center" style="text-align: center;">
                    <h4>Seleccione primero los campos de la declaración a cargar.</h4>
                  </div>
                </div>
              </div>
              <div class="dropzone-container" v-show="(inputPaisIva && inputModel && inputPeriod && inputPresentationType && inputYear)" style="display: none;">
                <form method="post" enctype="multipart/form-data" action="{% url 'app_documents:presented_model_create' seller.shortname %}" id="my-dropzone" class="dropzone ">
                  {% csrf_token %}
                  <div class="d-none d-print-none">
                    <input
                      type="hidden"
                      id="id"
                      name="{{ form_create.seller.name }}"
                      value="{{ seller.pk }}"
                    />
                    <input
                      type="hidden"
                      id="country"
                      name="country"
                      v-model="inputPaisIva"
                    />
                    <input
                      type="hidden"
                      id="model"
                      name="model"
                      v-model="inputModel"
                    />
                    <input
                      type="hidden"
                      id="period"
                      name="period"
                      v-model="inputPeriod"
                    />
                    <input
                      type="hidden"
                      id="year"
                      name="year"
                      v-model="inputYear"
                    />
                    <input
                      type="hidden"
                      id="presentation_type"
                      name="presentation_type"
                      v-model="inputPresentationType"
                    />
                    <input
                      type="hidden"
                      id="status"
                      name="status"
                      v-model="inputStatus"
                    />
                    <input
                      type="hidden"
                      id="process_with_ocr"
                      name="process_with_ocr"
                      v-model="inputOcrProcess"
                    />
                  </div>
                  <div class="fallback">
                    <input v-if="inputPresentationType != 'attach_doc' && inputPresentationType != 'receipt_file'"
                      type="file"
                      id="file"
                      name="form_create.file.name"
                      multiple
                    />
                    <input v-else
                      type="file"
                      id="another_file"
                      name="another_file"
                      multiple
                    />
                  </div>            
                </form>
              </div> 
            </div>
          </div>
          
          <br>
        </div>
      </div>
    </div>
    <!-- Cargar Documentos | END -->
  </div>
{% endblock content %}

{% block javascripts %}
  <script src="{% static 'assets/cdns_locals/js/axios/axios.min-v1.2.6.js' %}"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  
  <!-- DROPZONE JS -->
  <script src="{% static 'assets/cdns_locals/js/dropzone/dropzone.min-v5.js' %}"></script>
  <script>
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    Dropzone.options.myDropzone = {
      init: function() {
        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function(file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function(file) {
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            if (numFilesToUpload == numFilesErrored) {
              // All errors
            } else if (numFilesToUpload == numFilesUploaded) {
              // All OK
              // location.reload();
            } else {
              // Mixed
              // location.reload();
            }
            
            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });

        this.on("success", function(file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          Toast.fire({
            icon: 'success',
            title: 'Declaración cargada con éxito.'
          });
        });

        this.on("error", function(file, errorMessage) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          if (errorMessage == "invalid_presentation_subs_comp_reso") {
            Toast.fire({
              icon: 'error',
              title: 'No se puede cargar una presentación Sustitutiva o Complementaria sin que exista un modelo generado previamente.',
            });
          }
        });

        this.on("addedfile", function(file) {
          const presentationType = document.getElementById('inputPresentationType').value;
          if ((presentationType === 'substitutive-presentation' || presentationType === 'complementary-presentation' || presentationType === 'general-presentation'  || presentationType === 'resolution-presentation') && !file.type.includes('pdf')) {
            this.removeFile(file);
            let message = '';
            if (presentationType === 'substitutive-presentation') {
              message = 'Solo se pueden subir archivos PDF para presentaciones sustitutivas.';
            } else if (presentationType === 'complementary-presentation') {
              message = 'Solo se pueden subir archivos PDF para presentaciones complementarias.';
            } else if (presentationType === 'general-presentation') {
              message = 'Solo se pueden subir archivos PDF para presentaciones generales.';
            } else if (presentationType === 'resolution-presentation') {
              message = 'Solo se pueden subir archivos PDF para resoluciones de aplazamiento/fraccionamiento.';
            }
            Toast.fire({
              icon: 'error',
              title: message
            });
          } else if( presentationType == 'receipt_file' && !['application/pdf', 'image/png', 'image/jpeg'].some(type => file.type === type)) {
            console.log("file.type: ", file.type);
            this.removeFile(file);
            Toast.fire({
              icon: 'error',
              title: 'Solo se pueden subir archivos PDF, PNG, JPEG o JPG para justificantes.'
            });
          }
        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10, // Maximum file size in MB
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png,application/x-rar-compressed, application/zip,application/x-zip-compressed,application/xml,text/xml',
      dictDefaultMessage: "Da click o arrastra los archivos aquí",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
  </script>
  <!-- DROPZONE JS -->

  <!-- VUE3 JS -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    // IMPORTS
    const { ref, watch } = Vue;
    const currentYear = new Date().getFullYear();

    // VARIABLES
    const inputId = ref(null);
    const inputPaisIva = ref(null);
    const inputYear = ref(currentYear);
    const inputModel = ref(null);
    const inputPeriod = ref(null);
    const inputPresentationType = ref(null);
    const inputFile = ref(null);
    const inputStatus = ref("presented");
    const inputOcrProcess = ref(false);
    const dj = ref({});

    // METHODS or FUNCTIONS
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{ json | safe }}
          ));
        }
        if (djObj != null) {
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({ ...obj?.fields, "pk": obj?.pk })
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
    };

    // WATCHERS
    watch([inputPaisIva, inputPresentationType], ([newPaisIva, newPresentationType]) => {
      if (!(newPaisIva == 'ES' && newPresentationType == 'general-presentation')) {
        inputOcrProcess.value = false;
      }
    });

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const getModelsByCountry = (country) => {
      let models = [];
      if (dj.value.model && dj.value.model != null && dj.value.model.length > 0) {
        if (country && country != null && country != "") {
          models = dj.value.model.filter(m => m.pk.toString().toUpperCase().startsWith(country.toUpperCase()));
        }
      }
      return models;
    }

    const getPresentationTypeByCountry = (country) => {
      let declarationTypes = [];
      if (country && country != null && country != "ES") {
        declarationTypes = [{ pk: "general-presentation", description: "Declaración General" },
                            { pk: "attach_doc", description: "Archivo adjunto" },
                            { pk: "receipt_file", description: "Justificante" }
                            ];
      } else {
        declarationTypes = [
          { pk: "general-presentation", description: "Declaración General" },
          { pk: "substitutive-presentation", description: "Declaración Sustitutiva" },
          { pk: "complementary-presentation", description: "Declaración Complementaria" },
          { pk: "resolution-presentation", description: "Resolución aplazamiento/fraccionamiento" },
          { pk: "attach_doc", description: "Archivo adjunto" },
          { pk: "receipt_file", description: "Justificante" }
        ];
      }
      return declarationTypes;
    }

    // INITIALIZE
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS
    const data_export = {
      dj,
      inputId,
      inputPaisIva,
      inputYear,
      inputModel,
      inputPeriod,
      inputPresentationType,
      inputFile,
      inputStatus,
      inputOcrProcess,
      getCountryNameByCode,
      getModelsByCountry,
      getPresentationTypeByCountry,
    };

    // CREATE VUE 3
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const { createApp } = VUE3;
      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() { return { ...data_export } }
      });
      app.mount(target);
    };
    createVue3('#uploads', data_export);
  </script>
  <!-- VUE3 JS -->
{% endblock javascripts %}
