from django.db import models

class Currency(models.Model):

    code = models.Char<PERSON>ield(
        primary_key=True,
        max_length=4,
        verbose_name="Código",
        help_text="Codigo ISO de 3 Caracteres"
    )

    description = models.Char<PERSON>ield(
        max_length=250,
        verbose_name="Des<PERSON>rip<PERSON>",
    )

    class Meta:
        verbose_name = "<PERSON><PERSON>"
        verbose_name_plural = "Mon<PERSON><PERSON>"
    
    def __str__(self):
        return self.description
    
# @admin.register(Currency)
# class CurrencyAdmin(admin.ModelAdmin):
#     list_display = ["code", "description"]
#     search_fields = ["code", "description"]
