from django.core.validators import FileExtensionValidator, MinValueValidator, MaxValueValidator
from django.db import models
from django.urls import reverse
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from muaytax.signals import disable_for_load_data
from muaytax.app_documents.constants import *
import time
import threading


class PresentedModel(models.Model):
    # id -> AutoGen
    file = models.FileField(
        "Modelo",
        upload_to="uploads/presented_models/",
        validators=[FileExtensionValidator(["pdf", "PDF", "jpg", "JPG", "png", "PNG"])],
        max_length=255,
    )
    receipt_file = models.FileField(
        "Recibo de Pago",
        upload_to="uploads/presented_models_recepipt/",
        validators=[FileExtensionValidator(["pdf", "PDF", "jpg", "JPG","jpeg","JPEG", "png", "PNG"])],
        null=True,
        blank=True,
        max_length=255,
    )
    attach_files = models.FileField(
        "Archivos Adjuntos",
        upload_to="uploads/presented_models_attach_files/",
        validators=[FileExtensionValidator(["pdf", "PDF", "jpg", "JPG","jpeg","JPEG", "png", "PNG", "xml", "XML", "zip", "ZIP", "rar", "RAR"])],
        null=True,
        blank=True,
        max_length=255,
    )
    country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        related_name="presented_model_country",
        verbose_name="País de la Declaración",
    )
    year = models.PositiveIntegerField(
        verbose_name="Año de la Declaración",
    )
    period = models.ForeignKey(
        "dictionaries.Period",
        on_delete=models.PROTECT,
        related_name="period_presented_model",
        verbose_name="Periodo de la Declaración",
    )
    model = models.ForeignKey(
        "dictionaries.Model",
        on_delete=models.PROTECT,
        related_name="model_presented_model",
        verbose_name="Modelo de la Declaración",
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="seller_presented_model",
        verbose_name="Vendedor",
    )
    status = models.ForeignKey(
        "dictionaries.ModelStatus",
        on_delete=models.PROTECT,
        default="pending",
        related_name="status_approve_model",
        verbose_name="Estado del Modelo",
    )

    is_signed = models.BooleanField(
        default=False,
        verbose_name="¿El documento está firmado?",
    )

    # Archivo original sin firmas
    original_file = models.FileField(
        "Documento Original",
        upload_to="uploads/presented_models/original/",
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        null=True,
        blank=True,
        help_text="Documento original sin ninguna firma",
        max_length=255,
    )
    
    send_to_external_manager = models.BooleanField(
        default=False,
        verbose_name="¿Modelo se envía a Gestor Externo?",
    )

    email_sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Fecha de envío del correo",
        help_text="Fecha en la que se notifica al cliente la creación del modelo"
    )

    confirmation_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Fecha de Confirmación",
        help_text="Fecha en la que el Vendedor ha confirmado el modelo",
    )
    presentation_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Fecha de Presentación",
        help_text="Fecha en la que el modelo ha sido presentado por el Gestor",
    )
    result = models.ForeignKey(
        "dictionaries.PresentedModelResults",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name="result_presented_model",
        verbose_name="Resultado de la Declaración",
    )
    nrc = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="NRC",
    )
    nrc_file = models.FileField(
        "Recibo de NRC",
        upload_to="uploads/nrc_receipt/",
        validators=[FileExtensionValidator(["pdf", "PDF", "jpg", "JPG","jpeg","JPEG", "png", "PNG"])],
        null=True,
        blank=True,
        max_length=255,
    )
    comment = models.TextField(
        null=True,
        blank=True,
        verbose_name="Motivo de disconformidad",
    )
    iban = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="IBAN",
    )
    iban8 = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="IBAN8",
    )
    swift = models.CharField(
        null=True,
        blank=True,
        max_length=50,
        verbose_name="SWIFT",
    )
    count_split_payments = models.IntegerField(
        validators=[MinValueValidator(2), MaxValueValidator(12)],
        null=True,
        blank=True,
        verbose_name="Cantidad de meses a fraccionar",
    )
    amount = models.FloatField(
        null=True,
        blank=True,
        verbose_name="Cantidad",
    )
    is_paid = models.BooleanField(
        default=False,
        verbose_name="¿Está pagado?",
    )
    json_pdf = models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON PDF",
    )
    json_pdf_backup = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Respaldo JSON PDF",
        help_text="Estos son los valores originales del JSON PDF, antes de ser modificado por el usuario",
    )
    processed_by_ocr = models.BooleanField(
        default=False,
        verbose_name="¿Se ha procesado con OCR?",
    )
    appointment = models.ForeignKey(
        "sellers.Appointments",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="appointment_presented_model",
        verbose_name="Cita con Gestor",
    )
    disagree_appointment = models.ForeignKey(
        "bookings.Bookings",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="booking_presented_model",
        verbose_name="Cita por rechazo de modelo",
    )
    choice_seller = models.TextField(
        max_length=100,
        choices=CHOICE_MODEL_303,
        null=True,
        blank=True,
        verbose_name=" Elección del seller ",
    )
    is_direct_debit= models.BooleanField(
        default=False,
        verbose_name="¿Tiene domiciliación bancaria?",
    )
    
    sign_required = models.BooleanField(
        default=False,
        verbose_name="¿Requiere firma electrónica?",
    )
    zoho_request_id = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="ID del documento en Zoho",
    )
    zoho_doc_is_sent = models.BooleanField(
        default=False,
        verbose_name="¿Se ha enviado el documento LIPE para firmar?",
    )
    confirmation_lipe_signed = models.FileField(
        "Confirmación de LIPE firmado",
        upload_to="uploads/confirmation_lipe_signed/",
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        null=True,
        blank=True,
        max_length=255,
    )

    # Alternative files
    substitute_presentation = models.FileField(
        _("Presentación sustitutiva"),
        upload_to="uploads/presented_models/substitute_presentation/",
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        null=True,
        blank=True,
        max_length=255,
    )
    complementary_presentation = models.FileField(
        _("Presentación complementaria"),
        upload_to="uploads/presented_models/complementary_presentation/",
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        null=True,
        blank=True,
        max_length=255,
    )
    resolution_presentation = models.FileField(
        _("Presentación resolución aplazamiento/fraccionamiento"),
        upload_to="uploads/presented_models/resolution_presentation/",
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        null=True,
        blank=True,
        max_length=255,
    )

    # USA form fields
    m5472_signed = models.FileField(
        _("Modelo 5472-1120 firmado"),
        upload_to="uploads/presented_models/models_usa_signed/",
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        null=True,
        blank=True,
        help_text="Solo se admiten archivos PDF",
        max_length=255,
    )
    fax_destination_id = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name=_("ID de destino de fax"),
    )
    report_fax = models.FileField(
        _("Informe de envío de fax"),
        upload_to="uploads/presented_models/report_fax/",
        validators=[FileExtensionValidator(["pdf"])],
        null=True,
        blank=True,
        max_length=255,
    )
    aeat_json_response= models.TextField(
        null=True,
        blank=True,
        verbose_name="JSON respuesta AEAT",
    )

    presentation_response = models.JSONField(
        null=True,
        blank=True,
        verbose_name="Respuesta de la presentación",
        help_text="Respuesta de la presentación via api. Se guarda en formato JSON",
    )

    is_notify_to_management = models.BooleanField(
        default=False,
        verbose_name="¿Ha sido notificado a la gestoría?",
    )

    model_sent_by = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        choices = [
            ('manager', 'Gestor'),
            ('app', 'App'),
        ],
        verbose_name="Modelo enviado por",
    )

    historial_change_json = models.JSONField(
        null=True,
        blank=True,
        verbose_name=" JSON Historial de cambios",
        help_text="Historial de cambios en el modelo, edición y envío del modelo. Se guarda en formato JSON",
    )

    payment_day = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="Día de pago de cada mes",
        choices = [
            ('5', 'Día 5'),
            ('20', 'Día 20'),
        ],
    )

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)


    class Meta:
        verbose_name = "Modelo"
        verbose_name_plural = "Modelos"

    def __str__(self):
        if self.file and self.file.name:
            return self.file.name.replace("uploads/", "").replace("presented_models/", "")
        else:
            return "Presented Model ID:{}".format(self.pk)

    def get_absolute_url(self):
        return reverse("app_documents:presented_model_detail", kwargs={"pk": self.pk, "shortname": self.seller.shortname})

    def get_file_url(self):
        return f'/media/uploads/presented_models/{self.file.name.split("/")[-1]}'

    def convert_json_pdf_to_dict(self):
        import json
        return json.loads(self.json_pdf) if self.json_pdf else {}

    def clean(self):
        super().clean()
        errors = {}

        if self.seller.legal_entity == "self-employed-outside" and self.model.pk.startswith("ES-"):
            if self.is_direct_debit:
                errors['is_direct_debit'] = _("Autónomos fuera de España no pueden domiciliar el pago. Solo se permite transferencia bancaria (IBAN/SWIFT).")
            if self.nrc:
                errors['nrc'] = _("Autónomos fuera de España no pueden pagar con NRC. Solo se permite transferencia bancaria (IBAN/SWIFT).")
        
        if errors:
            raise ValidationError(errors)
            
@receiver(post_save, sender=PresentedModel)
@disable_for_load_data
def after_presentedmodel_save(sender, instance, created, **kwargs):
    
    # este if es para que no se ejecute el signal cuando se guarda con este atributo auxiliar
    if getattr(instance, "_skip_pm_signal", False):
        return
    
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        # print("Updating seller lists")
        # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
        # update_and_create_seller_cached_lists(seller)
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk, year=instance.year)


@receiver(post_delete, sender=PresentedModel)
@disable_for_load_data
def after_presentedmodel_delete(sender, instance, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
        # update_and_create_seller_cached_lists(seller)
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk, year=instance.year)


@receiver(pre_save, sender=PresentedModel)
@disable_for_load_data  
def pre_save_presentedmodel(sender, instance, **kwargs):
    from muaytax.users.middleware import LastActivityMiddleware
    from muaytax.app_documents.utils.utils import setHistorialPresentedModel
    
    user = LastActivityMiddleware.get_current_user()
    user = user or 'APP' #Si la acción no la realiza un usuario, se entiende que la realiza la APP

    try:
        if instance.pk:  
            try:
                prev_instance = PresentedModel.objects.get(pk=instance.pk)
            except PresentedModel.DoesNotExist:
                prev_instance = None

            if prev_instance:
                instance = setHistorialPresentedModel(instance, prev_instance, user) #Si la instancia existe actualizamos el historial
        else:  
            instance = setHistorialPresentedModel(instance, None, user) #Si la instancia no existe creamos el historial
    except Exception as e:
        print("Error al intentar crear el historial: ", e)
        
