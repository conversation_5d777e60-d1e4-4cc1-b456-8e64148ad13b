import uuid

from datetime import datetime
from django.utils import timezone
from django import forms
from django.forms.widgets import DateInput, TimeInput
from django.db.models import Q
from django.contrib.auth import get_user_model

from muaytax.app_bookings.models.bookings import Bookings
from muaytax.app_bookings.models.schedule_manager import Schedule
from muaytax.app_bookings.models.absence import Absence
from muaytax.app_sellers.models import Seller

User = get_user_model()

class BookingsChangeFormSeller(forms.ModelForm):
    date = forms.DateTimeField(required=True, widget=forms.DateTimeInput(attrs={"type": "datetime-local"}))

    class Meta:
        model = Bookings
        fields = ['date', 'duration', 'topics', 'comments']

    def __init__(self, *args, **kwargs):
        self.seller = kwargs.pop('seller', None)
        self.subject = kwargs.pop('subject', None)
        self.managers = kwargs.pop('managers', []) # managers available for the selected date and time
        super().__init__(*args, **kwargs)

    def cleaned_date(self):
        date = self.cleaned_data['date']
        if date is None or date <= datetime.now():
            raise forms.ValidationError("La fecha de la llamada debe ser mayor a la fecha actual")
        return date
    
    def clean(self):
        cleaned_data = super().clean()
        if not self.managers:
            raise forms.ValidationError("Error: No hay gestores disponibles para la fecha seleccionada")
        
        for manager_id in self.managers:
            try:
                manager = User.objects.get(pk=manager_id.strip())
                cleaned_data['manager'] = manager
                break
            except User.DoesNotExist:
                continue

        if 'manager' not in cleaned_data:
            raise forms.ValidationError("Error: No hay gestores disponibles para la fecha seleccionada")

        # Validar que el vendedor no tiene citas para ese día con el mismo gestor
        date = cleaned_data.get('date')
        if date and self.seller:
            existing_bookings = Bookings.objects.filter(
                seller=self.seller,
                date__date=date.date(),
                manager=cleaned_data['manager'],
                status="pending"
            )

            # Si estamos editando, excluimos el booking actual
            if self.instance and self.instance.pk:
                existing_bookings = existing_bookings.exclude(pk=self.instance.pk)

            if existing_bookings.exists():
                raise forms.ValidationError(
                    "Ya tienes una cita programada con este gestor para el día seleccionado. "
                    "Solo puedes tener una cita por día con el mismo gestor."
                )

        return cleaned_data
    
    def save(self, commit=True):
        booking = super().save(commit=False)
        booking.idcal_event = str(uuid.uuid4().hex)
        booking.seller = self.seller
        booking.subject = self.subject
        booking.manager = self.cleaned_data.get('manager')
        booking.created_by = 'seller'

        # Ejecutar la validación completa del modelo antes de guardar
        booking.full_clean()

        if commit:
            booking.save()
        return booking

class BookingsChangeFormManager(forms.ModelForm):

    date = forms.DateField(
        widget=forms.TextInput(attrs={'type': 'date', 'required': 'required'}),
        label="Fecha de la llamada",
        
    )
    time = forms.TimeField(
        widget=TimeInput(attrs={'type': 'time', 'class': 'time-input', 'required': 'required', 'step': '900'}),
        label="Hora de la llamada",
        help_text="Solo se permiten intervalos de 15 minutos. Formato: 24h",
    )
    seller = forms.ModelChoiceField(
        queryset=Seller.objects.order_by('shortname'),
        label="Vendedor",
    )

    def __init__(self, *args, **kwargs):
        super(BookingsChangeFormManager, self).__init__(*args, **kwargs)
        self.fields['seller'].widget.attrs.update({'class': 'form-control'})
        self.fields['subject'].widget.attrs.update({'class': 'form-control'})
        self.fields['duration'].widget.attrs.update({'class': 'form-control'})
        self.fields['topics'].widget.attrs.update({'rows': '3'})
        self.fields['comments'].widget.attrs.update({'rows': '3'})

    class Meta:
        model = Bookings
        exclude = ["manager","created_at", "idcal_event", "daily_report", "status", "created_by"]

    #NOTE: Para mi por el tema de horas

    def clean(self):
        cleaned_data = super().clean()
        
        date = cleaned_data.get('date')
        time = cleaned_data.get('time') #this time doesn't have a timezone stamp. It's set to be Europe/Madrid
        seller = cleaned_data.get('seller')
        manager_instance = self.initial['manager']
        if date and time:
            current_time = datetime.now().time()
            current_date = datetime.now().date()
            time_hourmin = current_time.strftime('%H:%M')
            
            combined_datetime = datetime.combine(date, time).astimezone(timezone.utc) #se convierte a utc para poder comparar con la hora de la base de datos
            combined_datetime_end_time = combined_datetime + timezone.timedelta(minutes=(int(self.cleaned_data['duration'])+ 1) * 15)
            cleaned_data['date'] = combined_datetime
            if time < current_time and date == current_date:
                print(f'Hora selc: {time}, Hora act: {current_time}')
                raise forms.ValidationError(f'Revisa la hora seleccionada, esta debe ser mayor que la actual: {time_hourmin}')
            if seller:
                self.validate_booking_for_seller(seller, combined_datetime, combined_datetime_end_time)

            if manager_instance:
                self.validate_booking_for_manager(manager_instance, combined_datetime, combined_datetime_end_time, time_selected=time)
            
        return cleaned_data
    
    def validate_booking_for_seller(self, seller, combined_datetime, combined_datetime_end_time):
        bookings = Bookings.objects.filter(Q(seller=seller) & Q(date__date=combined_datetime.date()), status="pending").order_by('date')

        for booking in bookings:

            if booking.date.time() <= combined_datetime.time() < booking.get_end_time():
                raise forms.ValidationError("Ya existe una llamada asignada para este vendedor en esta fecha y hora")
            
            if combined_datetime.time() < booking.date.time() and combined_datetime_end_time.time() > booking.date.time():
                raise forms.ValidationError("La duración de la llamada se superpone con otra llamada asignada para este vendedor en esta fecha y hora. Por favor, selecciona otra hora o duración de la llamada")
    
    def validate_booking_for_manager(self, manager, combined_datetime, combined_datetime_end_time, time_selected):
        bookings =  Bookings.objects.filter(Q(manager=manager) & Q(date__date=combined_datetime.date()), status="pending").order_by('date')
        schedule = Schedule.objects.filter(manager=manager).first()
        absences = Absence.objects.filter(manager= manager)
        forze_booking = self.data.get('forze_booking', None)

        if forze_booking and forze_booking == 'on':
            return
        for absence in absences:
            if absence.absence_type == 'bloq_schedule':
                if absence.is_all_day and (absence.date == combined_datetime.date()):
                    raise forms.ValidationError("Tienes una ausencia programada para este día")
                elif absence.is_all_day == False and absence.date == combined_datetime.date() and (absence.start_time <= time_selected < absence.end_time):
                    raise forms.ValidationError("Tienes una ausencia programada para esta fecha y esta hora")

        for booking in bookings:

            if booking.date.time() <= combined_datetime.time() < booking.get_end_time():
                raise forms.ValidationError("Ya tienes una llamada telefónica asignada durante esta fecha y hora. Revisa que la hora no esté dentro del tiempo de una llamada agendada.")
            
            if combined_datetime.time() < booking.date.time() and combined_datetime_end_time.time() > booking.date.time():
                raise forms.ValidationError("La duración de la llamada se superpone con otra llamada que tienes en esta fecha y hora. Por favor, selecciona otra hora o ajusta la duración de la llamada")
            
        combined_datetime_end_time_local = combined_datetime_end_time.astimezone(timezone.get_current_timezone()) # se re-convierte a la zona horaria del servidor para poder comparar con el horario de trabajo del manager
        if schedule.start_time and schedule.end_time:
            if not (schedule.start_time <= time_selected < schedule.end_time):
                raise forms.ValidationError("La hora seleccionada no está dentro de tu horario de trabajo")
            if combined_datetime_end_time_local.time() > schedule.end_time:
                raise forms.ValidationError("La duración de la llamada supera tu horario de trabajo")
        else:
            raise forms.ValidationError("No tienes un horario de trabajo asignado")
        
        if schedule.start_break_time and schedule.end_break_time:
            if schedule.start_break_time <= time_selected < schedule.end_break_time:
                raise forms.ValidationError("La hora seleccionada está dentro de tu horario de descanso")
            if schedule.start_break_time < combined_datetime_end_time_local.time() < schedule.end_break_time:
                raise forms.ValidationError("La duración de la llamada se superpone con tu horario de descanso")

class BookingsChangeNewUserFormManager(forms.ModelForm):
    date = forms.DateField(
        widget=forms.TextInput(attrs={'type': 'date', 'required': 'required'}),
        label="Fecha de la llamada",
        
    )
    time = forms.TimeField(
        widget=TimeInput(attrs={'type': 'time', 'class': 'time-input', 'required': 'required', 'step': '900'}),
        label="Hora de la llamada",
        help_text="Solo se permiten intervalos de 15 minutos. Formato: 24h",
    )
    new_user_name = forms.CharField(label="Nombre/s del nuevo usuario")
    new_user_last_name = forms.CharField(label="Apellido/s del nuevo usuario")
    new_user_phone = forms.CharField(label="Teléfono de contacto")
    new_user_email = forms.EmailField(label="Correo electrónico")

    def __init__(self, *args, **kwargs):
        super(BookingsChangeNewUserFormManager, self).__init__(*args, **kwargs)
        self.fields['new_user_name'].widget.attrs.update({'class': 'form-control'})
        self.fields['new_user_last_name'].widget.attrs.update({'class': 'form-control'})
        self.fields['new_user_phone'].widget.attrs.update({'class': 'form-control'})
        self.fields['new_user_email'].widget.attrs.update({'class': 'form-control'})
        self.fields['subject'].widget.attrs.update({'class': 'form-control'})
        self.fields['duration'].widget.attrs.update({'class': 'form-control'})
        self.fields['topics'].widget.attrs.update({'rows': '3'})
        self.fields['comments'].widget.attrs.update({'rows': '3'})

    class Meta:
        model = Bookings
        exclude = ["seller", "guest_user", "manager", "created_at", "idcal_event", "daily_report", "status", "created_by"]

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')
        time = cleaned_data.get('time')
        manager_instance = self.initial['manager']
        forze_booking = self.data.get('forze_booking', None)

        if date and time:
            current_datetime = datetime.now().astimezone(timezone.utc)
            combined_datetime = datetime.combine(date, time).astimezone(timezone.utc)
            combined_datetime_end_time = combined_datetime + timezone.timedelta(minutes=(int(self.cleaned_data['duration'])+ 1) * 15)
            cleaned_data['date'] = combined_datetime

            if combined_datetime < current_datetime:
                raise forms.ValidationError('Revisa la hora seleccionada, esta debe ser mayor que la actual')
            
            if not forze_booking and manager_instance:
                self.validate_booking_for_manager(manager_instance, combined_datetime, combined_datetime_end_time, time_selected=time)

        return cleaned_data
    
    def validate_booking_for_manager(self, manager, combined_datetime, combined_datetime_end_time, time_selected):
        bookings =  Bookings.objects.filter(Q(manager=manager) & Q(date__date=combined_datetime.date()), status="pending").order_by('date')
        schedule = Schedule.objects.filter(manager=manager).first()
        absences = Absence.objects.filter(manager= manager)

        for absence in absences:
            if absence.absence_type == 'bloq_schedule':
                if absence.is_all_day and (absence.date == combined_datetime.date()):
                    raise forms.ValidationError("Tienes una ausencia programada para este día")
                elif absence.is_all_day == False and absence.date == combined_datetime.date() and (absence.start_time <= time_selected < absence.end_time):
                    raise forms.ValidationError("Tienes una ausencia programada para esta fecha y esta hora")

        for booking in bookings:

            if booking.date.time() <= combined_datetime.time() < booking.get_end_time():
                raise forms.ValidationError("Ya tienes una llamada telefónica asignada durante esta fecha y hora. Revisa que la hora no esté dentro del tiempo de una llamada agendada.")
            
            if combined_datetime.time() < booking.date.time() and combined_datetime_end_time.time() > booking.date.time():
                raise forms.ValidationError("La duración de la llamada se superpone con otra llamada que tienes en esta fecha y hora. Por favor, selecciona otra hora o ajusta la duración de la llamada")
            
        combined_datetime_end_time_local = combined_datetime_end_time.astimezone(timezone.get_current_timezone()) # se re-convierte a la zona horaria del servidor para poder comparar con el horario de trabajo del manager
        if schedule.start_time and schedule.end_time:
            if not (schedule.start_time <= time_selected < schedule.end_time):
                raise forms.ValidationError("La hora seleccionada no está dentro de tu horario de trabajo")
            if combined_datetime_end_time_local.time() > schedule.end_time:
                raise forms.ValidationError("La duración de la llamada supera tu horario de trabajo")
        else:
            raise forms.ValidationError("No tienes un horario de trabajo asignado")
        
        if schedule.start_break_time and schedule.end_break_time:
            if schedule.start_break_time <= time_selected < schedule.end_break_time:
                raise forms.ValidationError("La hora seleccionada está dentro de tu horario de descanso")
            if schedule.start_break_time < combined_datetime_end_time_local.time() < schedule.end_break_time:
                raise forms.ValidationError("La duración de la llamada se superpone con tu horario de descanso")

class ScheduleManagerForm(forms.ModelForm):
    start_time = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'step': '1800', 'required': 'required','min': '07:00', 'max': '18:30'}),
        help_text="Solo se permiten intervalos de 30 minutos. Formato: 24h",
        label="Hora inicial para toma de llamadas"
    )
    end_time = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'step': '1800', 'required': 'required','min': '07:00', 'max': '18:30'}),
        help_text="Solo se permiten intervalos de 30 minutos. Formato: 24h",
        label="Hora final para toma de llamadas"
    )
    start_break_time = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time','min': '07:00', 'max': '18:30'}),
        # help_text="Solo se permiten intervalos de 30 minutos. Formato: 24h",
        label="Hora inicial de descanso",
        required=False
    )
    end_break_time = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time','min': '07:00', 'max': '18:30'}),
        # help_text="Solo se permiten intervalos de 30 minutos. Formato: 24h",
        label="Hora final de descanso",
        required=False
    )

    class Meta:
        model = Schedule
        fields = '__all__'
        exclude = ['manager']

class BookingsDeleteForm(forms.Form):
    class Meta:
        model = Bookings
        fields = "__all__"
        exclude = ["seller", "manager"]