{% extends "layouts/base.html" %}
{% load static %}
{% load gravatar %}
{% load crispy_forms_tags %}

{% block title %} Perfil Usuario {% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/loading.css"/>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
              <h5 class="m-b-10">Perfil Usuario</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">
                Perfil Usuario
                {% if object == request.user %}
                : {{object.seller.name}}
                {% endif %}
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">

    {% comment %}
    <div class="container">
      {% if object == request.user %}
        <div class="row">
          <div class="col-sm-12">
            <h2>
              {% if object.name %}
                <p>{{ object.name }}</p>
              {% else %}
                {{ object.username }}
              {% endif %}
            </h2>
            <p>{{ object.email }}</p>
          </div>
        </div>
        <!-- Action buttons -->
        <div class="row">
          <div class="col-sm-12">
            <!-- <a class="btn btn-primary" href="{% url 'account_email' %}" role="button">E-Mail</a> -->
            <a class="btn btn-primary" href="{% url 'app_sellers:detail' object.seller.shortname %}" role="button">Datos empresa</a>
            <a class="btn btn-primary" href="{% url 'app_sellers:selleradmin_update' %}" role="button">Datos administrador</a>
            <a class="btn btn-primary" href="{% url 'app_sellers:partner_list' %}" role="button">Socios</a>
            <!-- Your Stuff: Custom user template urls -->
          </div>
        </div>
        <!-- End Action buttons -->
      {% endif %}
    </div>
    {% endcomment %}

    <!-- [ Left Panel ] start -->
    <div class="col-lg-4">
        <div class="card user-card user-card-1">
            <div class="card-body pb-0">
              <div class="media user-about-block align-items-center mt-0 mb-3">
                <!-- User Profile Image 1 -->
                <div class="position-relative d-inline-block">
                    {% if request.user.seller.logo %}
                      <img style="width:50px;" src="{{ request.user.seller.logo.url }}" alt="Seller Logo">
                    {% elif request.user.email %}
                        {% gravatar request.user.email 80 %}
                    {% else %}
                      <img style="width:50px;" src="{% static 'assets/images/logo.png' %}"/>
                    {% endif %}
                    {% comment %}
                    <div class="certificated-badge">
                        <i class="fas fa-certificate text-primary bg-icon"></i>
                        <i class="fas fa-check front-icon text-white"></i>
                    </div>
                    {% endcomment %}
                </div>
                <!-- User Profile Name (Username) + Role -->
                <div class="media-body ms-3">
                    <h6 class="mb-1">
                      {% if request.user.name %}
                        {{ request.user.name }}
                        {% if request.user.username %}
                          ( {{ request.user.username }} )
                        {% endif %}
                      {% elif request.user.username %}
                        {{ request.user.username }}
                      {% else %}
                        Usuario
                      {% endif %}
                    </h6>
                    <p class="mb-0 text-muted">
                      Último Login: &nbsp; &nbsp; &nbsp; {{ request.user.last_login }} <br>
                      Última Actividad: {{ request.user.last_activity }} <br>
                    </p>
                    {% comment %}
                    <p class="mb-0 text-muted">
                      {{ request.user.role | title }}
                    </p>
                    {% endcomment %}
                </div>
              </div>
            </div>
            <ul class="list-group list-group-flush">
                <!-- Email -->
                {% if request.user.email %}
                  <li class="list-group-item">
                      <span class="f-w-500"><i class="feather icon-mail m-r-10"></i>Email</span>
                      <a  class="float-end text-body">
                          {{ request.user.email }}
                      </a>
                  </li>
                {% endif %}
                <!-- Phone -->
                {% if request.user.phone %}
                  <li class="list-group-item">
                      <span class="f-w-500"><i class="feather icon-phone-call m-r-10"></i>Teléfono</span>
                      <a href="#" class="float-end text-body">
                        {{ request.user.phone }}
                      </a>
                  </li>
                {% endif %}
                <!-- Country -->
                {% if request.user.country %}
                  <li class="list-group-item border-bottom-0">
                      <span class="f-w-500"><i class="feather icon-map-pin m-r-10"></i>País</span>
                      <span class="float-end">{{ request.user.country }}</span>
                  </li>
                {% endif %}
            </ul>
            <div class="nav flex-column nav-pills list-group list-group-flush list-pills" id="user-set-tab" role="tablist" aria-orientation="vertical">

                <a class="nav-link list-group-item list-group-item-action active"
                    data-bs-toggle="pill" role="tab" aria-selected="true"
                    id="user-set-profile-tab"  href="#user-set-profile" aria-controls="user-set-profile"
                >
                    <span class="f-w-500">
                      <i class="feather icon-user m-r-10 h5 "></i>
                      Resumen Información
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a>

                <!-- <a class="nav-link list-group-item list-group-item-action"
                  data-bs-toggle="pill" role="tab" aria-selected="false"
                  id="user-set-account-tab"  href="#user-set-account" aria-controls="user-set-account"
                >
                    <span class="f-w-500">
                      <i class="feather icon-book m-r-10 h5 "></i>
                      Editar Información del usuario
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a> -->

                <a class="nav-link list-group-item list-group-item-action"
                    data-bs-toggle="pill" role="tab" aria-selected="false"
                    id="user-set-seller-tab"  href="#user-set-seller" aria-controls="user-set-seller"
                >
                    <span class="f-w-500">
                      <i class="feather icon-file-text m-r-10 h5 "></i>
                      Editar Información del Vendedor
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a>

                <a class="nav-link list-group-item list-group-item-action"
                    data-bs-toggle="pill" role="tab" aria-selected="false"
                    id="user-set-address-tab"  href="#user-set-address" aria-controls="user-set-address"
                >
                    <span class="f-w-500">
                      <i class="feather icon-file-text m-r-10 h5 "></i>
                      Editar Dirección
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a>

                <a class="nav-link list-group-item list-group-item-action"
                    data-bs-toggle="pill" role="tab" aria-selected="false"
                    id="user-set-bank-tab"  href="#user-set-bank" aria-controls="user-set-bank"
                >
                    <span class="f-w-500">
                    <i class="feather icon-file-text m-r-10 h5 "></i>
                    Editar Información Bancaria
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a>

                <a class="nav-link list-group-item list-group-item-action"
                    data-bs-toggle="pill" role="tab" aria-selected="false"
                    id="user-set-password-tab"  href="#user-set-password" aria-controls="user-set-password"
                >
                    <span class="f-w-500">
                      <i class="feather icon-shield m-r-10 h5 "></i>
                      Cambiar Contraseña
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a>

                <a class="nav-link list-group-item list-group-item-action"
                    data-bs-toggle="pill" role="tab" aria-selected="false"
                    id="user-set-signature-tab" href="#user-set-signature" aria-controls="user-set-signature"
                >
                    <span class="f-w-500">
                      <i class="feather icon-edit m-r-10 h5 "></i>
                      Firma
                    </span>
                    <span class="float-end"><i class="feather icon-chevron-right"></i></span>
                </a>

            </div>
        </div>
    </div>
    <!-- [ Left Panel ] end -->

    <!-- [ Rigth Panel ] start -->
    <div class="col-lg-8">
        <div class="tab-content bg-transparent p-0 shadow-none" id="user-set-tabContent">
            <div class="tab-pane fade show active" role="tabpanel" id="user-set-profile" aria-labelledby="user-set-profile-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>
                            <i class="feather icon-user text-c-blue wid-20"></i>
                            <span class="p-l-5">Mi Información</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <h5 class="mt-0 mb-3">Datos del Usuario</h5>
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td class="">Nombre</td>
                                    <td class="">{{ request.user.name }}</td>
                                </tr>
                                <tr>
                                    <td class="">Nombre Corto</td>
                                    <td class="">{{ request.user.username }}</td>
                                </tr>
                                <tr>
                                    <td class="">Email</td>
                                    <td class="">{{ request.user.email }}</td>
                                </tr>
                            </tbody>
                        </table>

                        <h5 class="mt-5 mb-3">Datos del Vendedor</h5>
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td class="">Nombre Comercial</td>
                                    <td class="">{{ request.user.seller.trade_name}}</td>
                                </tr>
                                <tr>
                                    <td class="">Razón Social</td>
                                    <td class="">{{ request.user.seller.name }}</td>
                                </tr>
                                <tr>
                                    <td class="">Nombre Corto</td>
                                    <td class="">{{ request.user.seller.shortname|lower }}</td>
                                </tr>
                                <tr>
                                    <td class="">Entidad Jurídica</td>
                                    <td class="">{{ request.user.seller.legal_entity|default:''|upper }}</td>
                                </tr>
                                <tr>
                                    <td class="">Teléfono</td>
                                    <td class="">{{ request.user.seller.contact_phone|default:'' }}</td>
                                </tr>
                                <tr>
                                    <td class="">Dirección Fiscal</td>
                                    <td class="">{{ request.user.seller.adress }}</td>
                                </tr>
                            </tbody>
                        </table>

                        <h5 class="mt-5 mb-3">Contactos</h5>
                        <div class="row align-items-center mb-3">
                            <div class="col-2 border-start">
                                <h5>Clientes</span>
                            </div>
                            <div class="col-1">
                                <h6>{{ customers_count }}</h6>
                            </div>

                            <div class="col-2 border-start">
                                <h5>Proveedores</span>
                            </div>
                            <div class="col-1">
                                <h6>{{ providers_count }}</h6>
                            </div>

                            <div class="col-2 border-start">
                                <h5>Socios</span>
                            </div>
                            <div class="col-1">
                                <h6>{{ partners_count }}</h6>
                            </div>

                            <div class="col-2 border-start">
                                <h5>Empleados</span>
                            </div>
                            <div class="col-1 border-end">
                                <h6>{{ workers_count }}</h6>
                            </div>
                        </div>

                        {% if seller_vat is not None %}
                        <h5 class="mt-5 mb-3">Paises IVA Contratados</h5>
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                        <th class="">País </td>
                                        <th class="">Número IVA</td>
                                        <th class="">VIES</td>
                                </tr>
                                {% for vat in seller_vat %}
                                    <tr>
                                        <td class="">{{ vat.vat_country|title }}</td>
                                        <td class="">{{ vat.vat_number|upper }}</td>
                                        <td class="">
                                        {%  if vat.vat_vies == True %}
                                            Si
                                        {% else %}
                                            No
                                        {% endif %}
                                    </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% endif %}


                        <h5 class="mt-5 mb-4 pb-3 border-bottom">
                            <div class="row">
                                <div class="col">
                                    {{ invoice_stats_period_label|default:"Facturas" }}
                                </div>
                            </div>
                        </h5>

                        <!-- Facturas de Venta -->
                        {% if invoice_data.num_sales_total > 0 %}
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">Facturas de Venta <span class="text-muted">(Total: {{ invoice_data.num_sales_total }})</span></h6>
                                <div class="legend d-flex align-items-center">
                                    <span class="legend-item"><span class="legend-indicator bg-warning"></span> Pendientes ({{ invoice_data.num_sales_pending }})</span>
                                    <span class="legend-item ms-3"><span class="legend-indicator bg-success"></span> Revisadas ({{ invoice_data.num_sales_revised }})</span>
                                    <span class="legend-item ms-3"><span class="legend-indicator bg-danger"></span> Descartadas ({{ invoice_data.num_sales_discarded }})</span>
                                </div>
                            </div>
                            <div class="progress" style="height: 24px;">
                                {% if invoice_data.percentage_sales_pending > 0 %}
                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_sales_pending }}%"
                                    aria-valuenow="{{ invoice_data.percentage_sales_pending }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Pendientes: {{ invoice_data.num_sales_pending }} ({{ invoice_data.percentage_sales_pending }}%)">
                                    {% if invoice_data.percentage_sales_pending > 10 %}{{ invoice_data.percentage_sales_pending }}%{% endif %}
                                </div>
                                {% endif %}

                                {% if invoice_data.percentage_sales_revised > 0 %}
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_sales_revised }}%"
                                    aria-valuenow="{{ invoice_data.percentage_sales_revised }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Revisadas: {{ invoice_data.num_sales_revised }} ({{ invoice_data.percentage_sales_revised }}%)">
                                    {% if invoice_data.percentage_sales_revised > 10 %}{{ invoice_data.percentage_sales_revised }}%{% endif %}
                                </div>
                                {% endif %}

                                {% if invoice_data.percentage_sales_discarded > 0 %}
                                <div class="progress-bar bg-danger" role="progressbar" style="width: {{ invoice_data.percentage_sales_discarded }}%"
                                    aria-valuenow="{{ invoice_data.percentage_sales_discarded }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Descartadas: {{ invoice_data.num_sales_discarded }} ({{ invoice_data.percentage_sales_discarded }}%)">
                                    {% if invoice_data.percentage_sales_discarded > 10 %}{{ invoice_data.percentage_sales_discarded }}%{% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <div class="mb-4">
                            <h6 class="mb-2">Facturas de Venta</h6>
                            <div class="alert alert-light mb-0">No hay facturas de venta registradas.</div>
                        </div>
                        {% endif %}

                        <!-- Facturas de Gasto -->
                        {% if invoice_data.num_expenses_total > 0 %}
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">Facturas de Gasto <span class="text-muted">(Total: {{ invoice_data.num_expenses_total }})</span></h6>
                                <div class="legend d-flex align-items-center">
                                    <span class="legend-item"><span class="legend-indicator bg-warning"></span> Pendientes ({{ invoice_data.num_expenses_pending }})</span>
                                    <span class="legend-item ms-3"><span class="legend-indicator bg-success"></span> Revisadas ({{ invoice_data.num_expenses_revised }})</span>
                                    <span class="legend-item ms-3"><span class="legend-indicator bg-danger"></span> Descartadas ({{ invoice_data.num_expenses_discarded }})</span>
                                </div>
                            </div>
                            <div class="progress" style="height: 24px;">
                                {% if invoice_data.percentage_expenses_pending > 0 %}
                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_expenses_pending }}%"
                                    aria-valuenow="{{ invoice_data.percentage_expenses_pending }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Pendientes: {{ invoice_data.num_expenses_pending }} ({{ invoice_data.percentage_expenses_pending }}%)">
                                    {% if invoice_data.percentage_expenses_pending > 10 %}{{ invoice_data.percentage_expenses_pending }}%{% endif %}
                                </div>
                                {% endif %}

                                {% if invoice_data.percentage_expenses_revised > 0 %}
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_expenses_revised }}%"
                                    aria-valuenow="{{ invoice_data.percentage_expenses_revised }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Revisadas: {{ invoice_data.num_expenses_revised }} ({{ invoice_data.percentage_expenses_revised }}%)">
                                    {% if invoice_data.percentage_expenses_revised > 10 %}{{ invoice_data.percentage_expenses_revised }}%{% endif %}
                                </div>
                                {% endif %}

                                {% if invoice_data.percentage_expenses_discarded > 0 %}
                                <div class="progress-bar bg-danger" role="progressbar" style="width: {{ invoice_data.percentage_expenses_discarded }}%"
                                    aria-valuenow="{{ invoice_data.percentage_expenses_discarded }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Descartadas: {{ invoice_data.num_expenses_discarded }} ({{ invoice_data.percentage_expenses_discarded }}%)">
                                    {% if invoice_data.percentage_expenses_discarded > 10 %}{{ invoice_data.percentage_expenses_discarded }}%{% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <div class="mb-4">
                            <h6 class="mb-2">Facturas de Gasto</h6>
                            <div class="alert alert-light mb-0">No hay facturas de gasto registradas.</div>
                        </div>
                        {% endif %}

                        <!-- Total de Facturas -->
                        {% if invoice_data.num_grand_total > 0 %}
                        <div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">Total Facturas <span class="text-muted">(Total: {{ invoice_data.num_grand_total }})</span></h6>
                                <div class="legend d-flex align-items-center">
                                    <span class="legend-item"><span class="legend-indicator bg-warning"></span> Pendientes ({{ invoice_data.num_grand_total_pending }})</span>
                                    <span class="legend-item ms-3"><span class="legend-indicator bg-success"></span> Revisadas ({{ invoice_data.num_grand_total_revised }})</span>
                                    <span class="legend-item ms-3"><span class="legend-indicator bg-danger"></span> Descartadas ({{ invoice_data.num_grand_total_discarded }})</span>
                                </div>
                            </div>
                            <div class="progress" style="height: 24px;">
                                {% if invoice_data.percentage_grand_total_pending > 0 %}
                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ invoice_data.percentage_grand_total_pending }}%"
                                    aria-valuenow="{{ invoice_data.percentage_grand_total_pending }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Pendientes: {{ invoice_data.num_grand_total_pending }} ({{ invoice_data.percentage_grand_total_pending }}%)">
                                    {% if invoice_data.percentage_grand_total_pending > 10 %}{{ invoice_data.percentage_grand_total_pending }}%{% endif %}
                                </div>
                                {% endif %}

                                {% if invoice_data.percentage_grand_total_revised > 0 %}
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ invoice_data.percentage_grand_total_revised }}%"
                                    aria-valuenow="{{ invoice_data.percentage_grand_total_revised }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Revisadas: {{ invoice_data.num_grand_total_revised }} ({{ invoice_data.percentage_grand_total_revised }}%)">
                                    {% if invoice_data.percentage_grand_total_revised > 10 %}{{ invoice_data.percentage_grand_total_revised }}%{% endif %}
                                </div>
                                {% endif %}

                                {% if invoice_data.percentage_grand_total_discarded > 0 %}
                                <div class="progress-bar bg-danger" role="progressbar" style="width: {{ invoice_data.percentage_grand_total_discarded }}%"
                                    aria-valuenow="{{ invoice_data.percentage_grand_total_discarded }}" aria-valuemin="0" aria-valuemax="100"
                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Descartadas: {{ invoice_data.num_grand_total_discarded }} ({{ invoice_data.percentage_grand_total_discarded }}%)">
                                    {% if invoice_data.percentage_grand_total_discarded > 10 %}{{ invoice_data.percentage_grand_total_discarded }}%{% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <div>
                            <h6 class="mb-2">Total Facturas</h6>
                            <div class="alert alert-light mb-0">No hay facturas registradas.</div>
                        </div>
                        {% endif %}

                        <!-- Estilos para las leyendas de las barras -->
                        <style>
                            .legend-item {
                            display: flex;
                            align-items: center;
                            font-size: 0.875rem;
                            }
                            .legend-indicator {
                            display: inline-block;
                            width: 14px;
                            height: 14px;
                            margin-right: 8px;
                            border-radius: 2px;
                            }
                            .legend-indicator.bg-warning {
                            background-color: #ffc107;
                            }
                            .legend-indicator.bg-success {
                            background-color: #28a745;
                            }
                            .legend-indicator.bg-danger {
                            background-color: #dc3545;
                            }
                            .progress {
                            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                            border-radius: 6px;
                            overflow: hidden;
                            }
                            .progress-bar {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 600;
                                                        transition: width 0.5s;
                            }
                        </style>

                        <!-- Script para inicializar los tooltips -->
                        <script>
                            document.addEventListener("DOMContentLoaded", function() {
                              var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                              var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                                return new bootstrap.Tooltip(tooltipTriggerEl);
                              });
                            });
                        </script>
                    </div>
                </div>
            </div>

            <!-- <div class="tab-pane fade" role="tabpanel" id="user-set-account" aria-labelledby="user-set-account-tab">
                <div class="card">
                  <form class="form-horizontal" method="post" enctype="multipart/form-data" action=".">
                    {% csrf_token %}

                    <div class="card-header">
                        <h5>
                            <i class="feather icon-target text-primary wid-20"></i>
                            <span class="p-l-5">Información del Usuario</span>
                            <small class="text-muted d-block m-l-25 m-t-5">
                              Cambien su información aquí:
                            </small>
                        </h5>
                    </div>
                    <div class="card-body">
                      {{ formUser |crispy }}
                    </div>
                    <div class="card-footer text-end">
                      <button type="submit" class="btn btn-danger" name="submit_user">
                        Actualizar Datos del Usuario
                      </button>
                    </div>
                  </form>
                </div>
            </div> -->

            <div class="tab-pane fade" role="tabpanel" id="user-set-seller" aria-labelledby="user-set-seller-tab">
              <div class="card">
                <form id="set-seller-form" class="form-horizontal" method="post" enctype="multipart/form-data" action=".">
                  {% csrf_token %}
                  <div class="card-header">
                      <h5>
                          <i class="feather icon-target text-primary wid-20"></i>
                          <span class="p-l-5">Información del Vendedor</span>
                          <small class="text-muted d-block m-l-25 m-t-5">
                            Cambia tu información aquí:
                          </small>
                      </h5>
                  </div>
                  <div class="card-body">
                    {{ formSeller |crispy }}
                  </div>
                  <input type="hidden" name="next" value="{{ next }}">
                  <input type="hidden" name="current_tab" value="seller">
                  <div class="card-footer text-end">
                    <button type="submit" class="btn btn-primary" name="submit_seller">
                      Actualizar Datos del Vendedor
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <div class="tab-pane fade" role="tabpanel" id="user-set-address" aria-labelledby="user-set-address-tab">
                <div class="card">
                    <form class="form-horizontal" method="post" enctype="multipart/form-data" action=".">
                        {% csrf_token %}
                        <div class="card-header">
                            <h5>
                                <i class="feather icon-target text-primary wid-20"></i>
                                <span class="p-l-5">Dirección</span>
                                <small class="text-muted d-block m-l-25 m-t-5">
                                Cambia tu dirección aqui:
                                </small>
                            </h5>
                        </div>
                        <div class="card-body">
                        {{ formSellerAddress |crispy }}
                        </div>
                        <div class="card-footer text-end">
                        <button type="submit" class="btn btn-primary" name="submit_address">
                            Actualizar Datos
                        </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="tab-pane fade" role="tabpanel" id="user-set-bank" aria-labelledby="user-set-bank-tab">
                <div class="card">
                    <form class="form-horizontal" method="post" enctype="multipart/form-data" action=".">
                        {% csrf_token %}
                        <div class="card-header">
                            <h5>
                                <i class="feather icon-target text-primary wid-20"></i>
                                <span class="p-l-5">Información Bancaria</span>
                                <small class="text-muted d-block m-l-25 m-t-5">
                                Cambia tu información aquí:
                                </small>
                            </h5>
                        </div>
                        <div class="card-body">
                        {{ formSellerBank|crispy }}
                        </div>
                        <div class="card-footer text-end">
                        <button type="submit" class="btn btn-primary" name="submit_bank">
                            Actualizar Datos
                        </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="tab-pane fade" role="tabpanel" id="user-set-password" aria-labelledby="user-set-password-tab">
                <div class="alert alert-warning" role="alert">
                    <h5 class="alert-heading">
                      <i class="feather icon-alert-circle me-2"></i>Alerta!
                    </h5>
                    <p class="mb-0">No Compartas tu Contraseña!</p>
                </div>
                <form method="post" action=".">
                  {% csrf_token %}
                  <div class="card">
                      <div class="card-header">
                          <h5>
                            <i data-feather="lock" class="icon-svg-primary wid-20"></i>
                            <span class="p-l-5">Cambiar Contraseña</span>
                          </h5>
                      </div>
                      <div class="card-body">
                        {{ formPassword|crispy }}
                        {% comment %}
                          <div class="row">
                              <div class="col-sm-6">
                                  <div class="form-group">
                                      <label class="form-label">
                                        Contraseña Actual
                                        <span class="text-danger">*</span>
                                      </label>
                                      <input type="password" class="form-control" placeholder="Introduce tu contraseña actual">
                                      <small class="form-text text-muted">
                                        No recuerdas tu contraseña? <a href="#!">Pulse aqui</a>
                                      </small>
                                  </div>
                              </div>
                          </div>
                          <div class="row">
                              <div class="col-sm-6">
                                  <div class="form-group">
                                      <label class="form-label">
                                        Nueva Contraseña
                                        <span class="text-danger">*</span>
                                      </label>
                                      <input type="password" class="form-control" placeholder="Introduzca una nueva contraseña.">
                                  </div>
                              </div>
                              <div class="col-sm-6">
                                  <div class="form-group">
                                      <label class="form-label">
                                        Confirme la Contraseña
                                        <span class="text-danger">*</span>
                                      </label>
                                      <input type="password" class="form-control" placeholder="Repita la contraseña">
                                  </div>
                              </div>
                          </div>
                        {% endcomment %}
                      </div>
                      <div class="card-footer text-end">
                          <button type="submit" class="btn btn-danger" name="submit_password">
                            Cambiar Contraseña
                          </button>
                      </div>
                  </div>
                </form>
            </div>

            <div class="tab-pane fade" role="tabpanel" id="user-set-signature" aria-labelledby="user-set-signature-tab">
              <div class="card">
                <div class="card-header">
                  <h5><i class="feather icon-edit text-primary wid-20"></i><span class="p-l-5">Gestión de Firma</span></h5>
                  <small class="text-muted d-block m-l-25 m-t-5">
                    Aquí puedes guardar o actualizar tu firma.
                  </small>
                </div>
                <div class="card-body">
                  <div id="signature-content-area">
                    <!-- El contenido se cargará dinámicamente con JS -->
                  </div>
                </div>
                <div class="card-footer text-end" id="signature-actions-area" style="display: none;">
                    <button class="btn btn-secondary" id="clearSignature">
                        <i class="feather icon-refresh-ccw"></i> Limpiar
                    </button>
                    <button class="btn btn-primary" id="saveSignature">
                        <i class="feather icon-save"></i> Guardar Firma
                    </button>
                    <button class="btn btn-danger" id="deleteSignature">
                        <i class="feather icon-trash"></i> Eliminar Firma
                    </button>
                </div>
              </div>
            </div>
        </div>
    </div>
    <!-- [ Rigth Panel ] end -->

  </div>

<div class="modal fade modal-animate anim-blur " id="loadingModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" >
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content check-booking" style="background-color: transparent; border: unset;">
      <div class="modal-body d-flex flex-column justify-content-center align-items-center">

        <div class="modal-body">
          <div class="d-flex justify-content-center align-items-center text-center">
              <div class="spinner-grow text-success animation-delay-1 " role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-2" role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <div class="spinner-grow text-success animation-delay-3" role="status">
                  <span class="sr-only">Loading...</span>
              </div>
              <p>&nbsp;</p>
              <img style="width:110px;" src="{% static 'assets/images/logo.png' %}"/>
          </div>
          <p class="text-white text-center mb-0"><b>Estás siendo redirigido de vuelta al apartado de citas telefónicas.</b></p>
          <p class="text-white text-center mb-0"><b>No cierres ni recargues la página. Por favor espera...</b></p>

        </div>

      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}
  <script>

    window.addEventListener('load', () => {
      const  trade_name= document.getElementById('id_trade_name');
      const name = document.getElementById('id_name');
      const first_name = document.getElementById('id_first_name');
      const last_name = document.getElementById('id_last_name');

      const sellerForm = document.getElementById('set-seller-form');
      const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'), {
        backdrop: 'static',
        keyboard: false
      });

      if('{{request.user.seller.legal_entity}}' == 'self-employed'){
        first_name.addEventListener('input', () => {
        name.value = `${first_name.value} ${last_name.value}`;
        });
        last_name.addEventListener('input', () => {
          name.value = `${first_name.value} ${last_name.value}`;
        });
      }

      // Verificar si hay un tab guardado en localStorage o un hash en la URL
      const savedTab = localStorage.getItem('activeTab');
      if (savedTab) {
        const tabTrigger = document.querySelector(`.nav-link[href="#${savedTab}"]`);
        if (tabTrigger) {
          const tab = new bootstrap.Tab(tabTrigger);
          tab.show();
          // Limpiar el localStorage después de usar la información
          localStorage.removeItem('activeTab');
        }
      } else {
        const current_tab = "{{ current_tab }}";
        if (current_tab) {
          const tabTrigger =  document.querySelector(`.nav-link[href="#user-set-${current_tab}"]`);
          if (tabTrigger) {
            const tab = new bootstrap.Tab(tabTrigger);
            tab.show();
          }
        }

        const hash = window.location.hash;
        if (hash) {
          const tabTrigger =  document.querySelector(`.nav-link[href="${hash}"]`);
          if (tabTrigger) {
            const tab = new bootstrap.Tab(tabTrigger);
            tab.show();
            if (hash === '#user-set-seller') {
              setTimeout(() => {
                const contactPhoneInput = sellerForm.querySelector('input[name="contact_phone"]');
                contactPhoneInput.focus();
              }, 1000);
            }
            // Solo limpiamos el hash si no es el tab de firma
            if (hash !== '#user-set-signature') {
              window.history.replaceState(null, null, window.location.pathname);
            }
          }
        }
      }

      sellerForm.addEventListener('submit', function (event) {
        const nextInput = sellerForm.querySelector('input[name="next"]');
        if (nextInput && nextInput.value) {
          loadingModal.show();
        }
      });

    });

    // ==========================================================
    // INICIO: Lógica de Gestión de Firma en el Perfil
    // ==========================================================
    document.addEventListener('DOMContentLoaded', () => {
        const signatureContentArea = document.getElementById('signature-content-area');
        const signatureActionsArea = document.getElementById('signature-actions-area');
        const clearButton = document.getElementById('clearSignature');
        const saveButton = document.getElementById('saveSignature');
        const deleteButton = document.getElementById('deleteSignature');

        // Comprobar de forma más robusta si hay firma - usando la URL directa
        // Verificar si seller tiene firma de forma directa, sin depender de la URL
        const sellerHasSignature = {% if seller.signature_image %}true{% else %}false{% endif %};
        console.log("¿El vendedor tiene firma según atributo?: ", sellerHasSignature);
        console.log("Valor directo del campo signature_image: {% if seller.signature_image %}Existe{% else %}No existe{% endif %}");

        const csrfToken = '{{ csrf_token }}';
        // URL del endpoint (ajustar si es necesario)
        const signatureEndpointUrl = '{% url "app_sellers:manage_signature" %}';

        let signaturePadInstance = null;
        let drawCanvas = null;
        let drawCtx = null;
        let isDrawing = false;

        function initializeSignaturePad() {
            signatureContentArea.innerHTML = `
                <div class="signature-creation-wrapper text-center">
                    <p class="mb-2">Dibuja tu firma en el recuadro:</p>
                    <canvas id="signaturePadCanvas"
                            width="400" height="200"
                            class="border rounded mx-auto d-block" style="cursor: crosshair;"></canvas>
                </div>`;

            drawCanvas = document.getElementById('signaturePadCanvas');
            if (!drawCanvas) return; // Salir si el canvas no se encontró

            drawCtx = drawCanvas.getContext('2d');
            drawCtx.strokeStyle = '#000000';
            drawCtx.lineWidth = 2;
            drawCtx.lineCap = 'round';
            drawCtx.lineJoin = 'round';

            signatureActionsArea.style.display = 'flex';
            saveButton.style.display = 'inline-block';
            clearButton.style.display = 'inline-block';
            deleteButton.style.display = 'none'; // Ocultar eliminar si no hay firma

            // Event Listeners para dibujar
            drawCanvas.addEventListener('mousedown', startDrawing);
            drawCanvas.addEventListener('mousemove', draw);
            drawCanvas.addEventListener('mouseup', stopDrawing);
            drawCanvas.addEventListener('mouseleave', stopDrawing);
            // Para pantallas táctiles
            drawCanvas.addEventListener('touchstart', startDrawingTouch, { passive: false });
            drawCanvas.addEventListener('touchmove', drawTouch, { passive: false });
            drawCanvas.addEventListener('touchend', stopDrawing);
        }

        function displayExistingSignature() {
            // Forzar recarga de la imagen para evitar caché
            const timestamp = new Date().getTime();
            // Obtener la URL de la firma
            let signatureUrl = "";
            {% if seller.signature_image %}
                signatureUrl = "{{ seller.signature_image.url }}";
                signatureUrl += "?t=" + timestamp;
            {% else %}
                // Intentar construir la URL alternativa
                signatureUrl = "/media/uploads/signatures/{{ seller.shortname }}_signature.png?t=" + timestamp;
            {% endif %}

            console.log("Mostrando firma existente:", signatureUrl);

            signatureContentArea.innerHTML = `
                <div class="text-center mb-4">
                    <p class="mb-2">Tu firma actual:</p>
                    <img src="${signatureUrl}"
                         class="img-fluid border rounded"
                         alt="Firma actual"
                         style="max-width: 300px; max-height: 150px; object-fit: contain;"
                         onerror="this.onerror=null; this.src='{% static 'assets/images/no-image.png' %}'; console.error('Error al cargar la imagen de firma');">
                </div>`;
            signatureActionsArea.style.display = 'flex';
            saveButton.style.display = 'none';
            clearButton.style.display = 'none';
            deleteButton.style.display = 'inline-block';
        }

        function getMousePosCanvas(evt) {
            const rect = drawCanvas.getBoundingClientRect();
            return { x: evt.clientX - rect.left, y: evt.clientY - rect.top };
        }
        function getTouchPosCanvas(evt) {
            const rect = drawCanvas.getBoundingClientRect();
            return { x: evt.touches[0].clientX - rect.left, y: evt.touches[0].clientY - rect.top };
        }

        function startDrawing(e) {
            if (e.button !== 0) return; // Solo botón izquierdo
            isDrawing = true;
            drawCtx.beginPath();
            const pos = getMousePosCanvas(e);
            drawCtx.moveTo(pos.x, pos.y);
        }
        function startDrawingTouch(e) {
            e.preventDefault();
            isDrawing = true;
            drawCtx.beginPath();
            const pos = getTouchPosCanvas(e);
            drawCtx.moveTo(pos.x, pos.y);
        }

        function draw(e) {
            if (!isDrawing) return;
            const pos = getMousePosCanvas(e);
            drawCtx.lineTo(pos.x, pos.y);
            drawCtx.stroke();
        }
        function drawTouch(e) {
            if (!isDrawing) return;
            e.preventDefault();
            const pos = getTouchPosCanvas(e);
            drawCtx.lineTo(pos.x, pos.y);
            drawCtx.stroke();
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function clearCanvas() {
            if (drawCtx && drawCanvas) {
                drawCtx.clearRect(0, 0, drawCanvas.width, drawCanvas.height);
            }
        }

        function saveSignatureToBackend() {
            if (!drawCanvas) return;
            // Comprobar si el canvas está vacío (todo transparente)
            const blank = document.createElement('canvas');
            blank.width = drawCanvas.width;
            blank.height = drawCanvas.height;
            if (drawCanvas.toDataURL() === blank.toDataURL()) {
                 Swal.fire('Atención', 'El lienzo de firma está vacío. Dibuja tu firma antes de guardar.', 'warning');
                return;
            }

            const signatureImage = drawCanvas.toDataURL('image/png');

            showLoadingSweetAlert('Guardando firma...');

            fetch(signatureEndpointUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    action: 'save_signature',
                    signature: signatureImage
                })
            })
            .then(response => response.json())
            .then(data => {
                Swal.close(); // Cerrar el loading
                if (data.success) {
                    Swal.fire('Éxito', 'Firma guardada correctamente.', 'success')
                    .then(() => {
                        // Actualizar el estado de la firma
                        if (data.hasSignature !== undefined) {
                            window.sellerHasSignature = data.hasSignature;
                        }
                        // Guardar información para mostrar el tab de firma al recargar
                        localStorage.setItem('activeTab', 'user-set-signature');
                        window.location.href = window.location.pathname + '#user-set-signature';
                        window.location.reload();
                    });
                } else {
                    Swal.fire('Error', data.error || 'No se pudo guardar la firma.', 'error');
                }
            })
            .catch(error => {
                Swal.close();
                console.error('Error en fetch save:', error);
                Swal.fire('Error', 'Ocurrió un problema de conexión al guardar la firma.', 'error');
            });
        }

        function deleteSignatureFromBackend() {
            Swal.fire({
                title: '¿Estás seguro?',
                text: "¡Esta acción eliminará tu firma permanentemente!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sí, eliminar firma',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    showLoadingSweetAlert('Eliminando firma...');
                    fetch(signatureEndpointUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken
                        },
                        body: JSON.stringify({ action: 'delete_signature' })
                    })
                    .then(response => response.json())
                    .then(data => {
                        Swal.close();
                        if (data.success) {
                            Swal.fire('Eliminada', 'La firma ha sido eliminada correctamente.', 'success')
                            .then(() => {
                                // Actualizar el estado de la firma
                                if (data.hasSignature !== undefined) {
                                    window.sellerHasSignature = data.hasSignature;
                                }
                                // Guardar información para mostrar el tab de firma al recargar
                                localStorage.setItem('activeTab', 'user-set-signature');
                                window.location.href = window.location.pathname + '#user-set-signature';
                                window.location.reload();
                            });
                        } else {
                            Swal.fire('Error', data.error || 'No se pudo eliminar la firma.', 'error');
                        }
                    })
                    .catch(error => {
                        Swal.close();
                        console.error('Error en fetch delete:', error);
                        Swal.fire('Error', 'Ocurrió un problema de conexión al eliminar la firma.', 'error');
                    });
                }
            });
        }

        function showLoadingSweetAlert(titleText) {
             Swal.fire({
                title: titleText,
                text: 'Por favor, espera...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // Inicialización al cargar la página o cambiar a la pestaña
        if (sellerHasSignature) {
            console.log("Inicializando vista con firma existente");
            displayExistingSignature();
        } else {
            console.log("Inicializando pad de firma (no hay firma guardada)");
            initializeSignaturePad();
        }

        // Event listeners para los botones de acción
        clearButton?.addEventListener('click', clearCanvas);
        saveButton?.addEventListener('click', saveSignatureToBackend);
        deleteButton?.addEventListener('click', deleteSignatureFromBackend);

        // Opcional: Reinicializar el canvas si se cambia a la pestaña y no había firma
        const signatureTabTrigger = document.getElementById('user-set-signature-tab');
        signatureTabTrigger?.addEventListener('shown.bs.tab', event => {
             // Verificar si ahora existe una firma (por si se guardó)
             // Esto requeriría una forma de saber el estado actual sin recargar
             // Por simplicidad, solo reinicializamos si originalmente no había firma
             // y el canvas no existe (puede haber sido eliminado por displayExistingSignature)
            if (!sellerHasSignature && !document.getElementById('signaturePadCanvas')) {
                 initializeSignaturePad();
             }
             // Si se necesita actualizar el estado dinámicamente (ej. después de guardar/eliminar)
             // la lógica de inicialización debería manejar ambos casos
             // o recargar esa sección específica del DOM.
        });
    });
    // ==========================================================
    // FIN: Lógica de Gestión de Firma en el Perfil
    // ==========================================================

  </script>
{% endblock javascripts %}
