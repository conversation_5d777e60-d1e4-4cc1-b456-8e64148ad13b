{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Listado de zips con excels de asientos
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    .dataTables_filter {
      display: none; 
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    .tooltip-inner a {
      color:white;
      text-decoration: none; 
    }

  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Zips de asientos</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name|title }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Zips de asientos</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-2 d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." oninput="search()"/>
              </div>
            </div>
          </div>
          <div class="dt-responsive table-responsive">
            <table id="list-table" class="table nowrap">
              <thead class="table-head">
              <tr>
                <th>Nombre Fichero</th>
                <th>Fecha Creación</th>
                <th style="width:5%;">Acciones</th>
              </tr>
              </thead>
              <tbody>
              {% for object in object_list %}
                <tr>
                  <td class="align-middle">
                    <span>{{ object.file }} </span>
                  </td>
                  <td class="align-middle">
                    {{ object.created_at|date:'Y-m-d H:i:s' }}
                  </td>
                  <td class="align-middle">
                    <div>
                      <a data-bs-toggle="tooltip" data-bs-placement="top" title="Descargar" class="btn btn-icon btn-info" href="{{ object.get_file_url }}" target="_blank" download>
                        <i class="fa-solid fa-download"></i>
                      </a>
                      <a data-bs-toggle="tooltip" data-bs-placement="top" title="Deshacer Exportación" class="btn btn-icon btn-danger" href="./delete/{{object.pk}}/">
                        <i class="feather icon-trash-2"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
  <link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"></script>
  <script>

    let table = null;

    $(document).ready(function () {

      table = $('#list-table').DataTable({
        "language": {
                "lengthMenu": "_MENU_",
                "zeroRecords": "No se han encontrado resultados.",
                "info": "_START_ a _END_ de un total de _TOTAL_",
                "search": "Buscar:",
                "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                "infoFiltered": "",
                "emptyTable": "No se han encontrado resultados",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "previous": "Anterior",
                    "next": "Siguiente"
                },
            },
            "autoWidth": true,
            "truncation": true,
            "paging": true,
            "searching": true,
            "lengthChange": false,
            "lengthMenu": [[50, 100, 200, -1], [50, 100, 200, 'Todos']],
      })

    });

    function search() {
        var tipo = $("#search").val();
        table.search(tipo).draw();
    }


  </script>
{% endblock javascripts %}
