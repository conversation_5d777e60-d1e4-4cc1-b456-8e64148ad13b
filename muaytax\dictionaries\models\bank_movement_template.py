from django.db import models
from django.core.validators import RegexValidator

class BankMovementTemplate(models.Model):

    code = models.CharField(
        primary_key=True,
        max_length=100,
        verbose_name="Código",
    )

    order = models.IntegerField(
        verbose_name="Orden",
    )

    name = models.CharField(
        max_length=200,
        verbose_name="Nombre de la plantilla",
    )

    bank_name = models.CharField(
        max_length=200,
        verbose_name="Nombre del Banco",
    )

    bank_name_col = models.CharField(
        blank=True,
        null=True,
        max_length=1,
        verbose_name="Columna del Nombre del Banco",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    bank_name_row = models.CharField(
        blank=True,
        null=True,
        max_length=2,
        verbose_name="Fila del Nombre del Banco",
    )

    header_row = models.CharField(
        max_length=2,
        verbose_name="Fila de Encabezado",
    )

    start_row = models.Char<PERSON>ield(
        max_length=2,
        verbose_name="Fila de Inicio",
        
    )

    row_order = models.IntegerField(
        default=-1,
        verbose_name="Orden de las Filas (ascendente/descendente)",
    )

    date_format = models.CharField(
        default="%d/%m/%Y",
        max_length=30,
        verbose_name="Formato de la Fecha",
    )

    date_col = models.CharField(
        max_length=1,
        verbose_name="Columna de la Fecha",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    date_header = models.CharField(
        max_length=100,
        verbose_name="Encabezado de la Fecha",
    )

    concept_col = models.CharField(
        max_length=1,
        verbose_name="Columna del Concepto",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    concept_header = models.CharField(
        max_length=100,
        verbose_name="Encabezado del Concepto",
    )

    observation_col = models.CharField(
        max_length=1,
        verbose_name="Columna de la Observación",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    observation_header = models.CharField(
        max_length=100,
        verbose_name="Encabezado de la Observación",
    )

    amount_col = models.CharField(
        null=True,
        blank=True,
        max_length=1,
        verbose_name="Columna del Monto",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    amount_header = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Encabezado del Monto",
    )

    amount_in_col = models.CharField(
        null=True,
        blank=True,
        max_length=1,
        verbose_name="Columna del Monto Entrante",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    amount_in_header = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Encabezado del Monto Entrante",
    )

    amount_out_col = models.CharField(
        null=True,
        blank=True,
        max_length=1,
        verbose_name="Columna del Monto Saliente",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    amount_out_header = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Encabezado del Monto Saliente",
    )

    currency_col = models.CharField(
        null=True,
        blank=True,
        max_length=5,
        verbose_name="Columna de la Divisa",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    currency_header = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Encabezado de la Divisa",
    )

    currency_in_col = models.CharField(
        null=True,
        blank=True,
        max_length=5,
        verbose_name="Columna de la Divisa Entrante",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    currency_in_header = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Encabezado de la Divisa Entrante",
    )

    currency_out_col = models.CharField(
        null=True,
        blank=True,
        max_length=5,
        verbose_name="Columna de la Divisa Saliente",
        validators=[RegexValidator(r'^[A-Z]$')]
    )

    currency_out_header = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Encabezado de la Divisa Saliente",
    )

    decimal_separator = models.CharField(
        default=",",
        max_length=1,
        verbose_name="Separador Decimal",
    ) 

    csv_separator = models.CharField(
        null=True,
        blank=True,
        max_length=1,
        verbose_name="Separador CSV",
    )

    csv_encoding = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="Codificación CSV",
    )

    multi_currency = models.BooleanField(
        default=False,
        verbose_name="Multidivisa",
    )
    backward_amount = models.BooleanField(
        default=False,
        verbose_name="Monto del revés",
    )

    class Meta:
        verbose_name = "Plantilla de Movimientos Bancarios"
        verbose_name_plural = "Plantillas de Movimientos Bancarios"
    
    def __str__(self):
        return self.name
    
# @admin.register(BankMovementTemplate)
# class BankMovementTemplateAdmin(admin.ModelAdmin):
#     list_display = ["code", "name", "bank_name"]
#     search_fields = ["code", "name", "bank_name"]