from cryptography.fernet import Fernet
from django.db import models
from django.db.models.signals import post_save, post_delete, pre_save
from django.conf import settings
from django.dispatch import receiver
from django.urls import reverse
from django.core.validators import FileExtensionValidator
from muaytax.signals import disable_for_load_data
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.dictionaries.models import DocumentType

def get_or_generate_key():
    """
    Obtener la clave de cifrado del entorno o generarla si no existe.
    """
    if not hasattr(settings, 'ENCRYPTION_KEY'):
        raise RuntimeError("La clave de cifrado no está configurada.")
    return settings.ENCRYPTION_KEY


class Document(models.Model):
    # id -> AutoGen
    
    PRIVACY_CHOICES = [
        ('private', 'Privado'),
        ('public', 'Público')
    ]

    file = models.FileField(
        "document",
        upload_to="uploads/documents/",
        validators=[FileExtensionValidator(["pdf", "PDF", "jpg", "JPG", "png", "PNG", "p12", "P12", "pfx"])],
    )

    year = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name="Año del documento",
        help_text="Año del documento si procede, si no, dejar en blanco",
    )

    documentType = models.ForeignKey(
        "dictionaries.DocumentType",
        on_delete=models.PROTECT,
        related_name="document_type",
        verbose_name="Tipo de documento",
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="seller_document",
        verbose_name="Vendedor",
    )

    sellerVat = models.ForeignKey(
        "sellers.SellerVat",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        related_name="document_seller_vat",
        verbose_name="País IVA del documento",
    )

    # 📌 Nueva relación con Partner
    partner = models.ForeignKey(
        "partners.Partner",
        on_delete=models.CASCADE,  # Borra el documento si el socio se elimina
        blank=True,
        null=True,
        related_name="partner_documents",
        verbose_name="Socio asociado al documento",
    )
    
    privacy = models.CharField(
        max_length=7,
        choices=PRIVACY_CHOICES,
        default='public',
        verbose_name='Privacidad del documento'
    )

    _encrypted_password = models.BinaryField(
        blank=True,
        null=True,
        verbose_name="Contraseña cifrada",
    )

    expiration_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha de caducidad",
    )

    is_notified_expiration = models.BooleanField(
        default=False,
        verbose_name="¿Se ha notificado la expiración?",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Documento"
        verbose_name_plural = "Documentos"

    def __str__(self):
        if self.file and self.file.name:
            return self.file.name.replace("uploads/", "").replace("documents/", "")
        else:
            return "Presented Model ID:{}".format(self.pk)

    def get_absolute_url(self):
        return reverse("app_documents:document_detail", kwargs={"pk": self.pk})

    def get_file_url(self):
        return f'/media/uploads/documents/{self.file.name}'
    
    def set_password(self, password):
        """Cifrar la contraseña y almacenarla."""
        fernet = Fernet(get_or_generate_key())
        self._encrypted_password = fernet.encrypt(password.encode())

    def get_password(self):
        """Descifrar y devolver la contraseña."""
        fernet = Fernet(get_or_generate_key())
        # Convertir memoryview a bytes
        if isinstance(self._encrypted_password, memoryview):
            encrypted_password_bytes = bytes(self._encrypted_password)
            decrypted_password = fernet.decrypt(encrypted_password_bytes)
            return decrypted_password.decode()

    def save(self, *args, **kwargs):
        # Asegúrate de que la contraseña se cifre antes de guardar
        if hasattr(self, 'password'):
            self.set_password(self.password)
        super().save(*args, **kwargs)

@receiver(post_save, sender=Document)
@disable_for_load_data
def after_sellervat_save(sender, instance, created, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
        # update_and_create_seller_cached_lists(seller)
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk)

@receiver(post_delete, sender=Document)
@disable_for_load_data
def after_sellervat_delete(sender, instance, **kwargs):
    seller = instance.seller
    if seller is not None and seller.pk is not None:
        # from muaytax.app_lists.utils import update_and_create_seller_cached_lists
        # update_and_create_seller_cached_lists(seller)
        from muaytax.app_lists.tasks.cachedlists import update_cached_seller_signal_task
        update_cached_seller_signal_task.delay(seller_id=seller.pk)

# Señal para cifrar la contraseña después de guardar
@receiver(post_save, sender=Document)
@disable_for_load_data
def encrypt_password(sender, instance, created, **kwargs):
    # Evitar un bucle infinito
    if not created and not getattr(instance, '_password_already_encrypted', False):
        return
    
    if hasattr(instance, 'password') and instance.password:
        fernet = Fernet(get_or_generate_key())
        instance._encrypted_password = fernet.encrypt(instance.password.encode())
        instance.password = None  # Limpiar el atributo 'password' para no almacenarlo en texto plano
        instance._password_already_encrypted = True  # Marcar como encriptada
        instance.save(update_fields=['_encrypted_password'])

# Señal para eliminar el ultimo certificado que exista en la APP antes de guardar uno nuevo
@receiver(pre_save, sender=Document)
@disable_for_load_data
def delete_prev_cert(sender, instance, **kwargs):
    if instance.pk is None:
        if instance.documentType.code == 'ES-SIGNATURE':
            perv_cert = Document.objects.filter(seller=instance.seller, documentType__code='ES-SIGNATURE').last()
            if perv_cert:
                perv_cert.delete()