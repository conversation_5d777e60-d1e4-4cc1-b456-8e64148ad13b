{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Asientos Contables
{% endblock title %}

<link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
<link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css"> 
<link href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2.min-v4.1.0.css" rel="stylesheet" />
<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/select/select2-bootstrap-5-theme.min-v5.css" />


{% block stylesheets %}
  <style> 
  .style-bold{
    font-weight:bold;
  }
  /* .grouped-row-first {
    border-top: 2px solid #000; 
    border-bottom: 2px solid #000; 
} 
*/

  #entry_table thead th {
    border-right: 1px solid #ccc;
    padding: 5px 10px; 
  }

  /* Estilo para ocultar el borde inferior de las filas con la clase 'no-bottom-border' */
#entry_table tbody tr.no-bottom-border {
    border-bottom: none !important;
}

/* Eliminar bordes de la tabla */
tbody, td, tfoot, th, thead, tr {
    border: none ; 
}



#entry_table .grouped-row-first th,
#entry_table .grouped-row-first td {
    border-top: 2px solid #ccc; /* Agrega un borde inferior a las celdas */
    padding: 10px; /* Añade un relleno a las celdas */
}

#entry_table th {
    background-color: #f2f2f2; /* Establece un color de fondo para las celdas del encabezado */
}

  
  </style>  
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Asientos Contables</h5>
          </div>
          <ul class="breadcrumb">
            {% if user.role == 'manager' %}
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Asientos Contables</a>
            </li>
            {% else %}
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Asientos Contables</a>
            </li>
            {% endif %}
          </ul>
        </div>

      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}


<div class="row">
    <div class="col-12" id="entry_row">
      <div class="row">
        <div class="mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
        <div class="col-auto">
          <div class="row justify-content-start">
            
          </div>
        </div>
        <div class="col my-1">
          <div class="row justify-content-end">
            
            <div class="col-auto d-none">
                <a class="btn btn-info me-1" href="{% url 'app_banks:entry_new_reconciliation' seller.shortname %}">
                    <span>Solo Conciliaciones Banco No Amazon</span>
                </a>
            </div>
            <div class="col-auto d-none">
                <a class="btn btn-info me-1" href="{% url 'app_banks:entry_new_reconciliation_amz' seller.shortname %}">
                    <span>Solo Conciliaciones Banco Amazon</span>
                </a>
            </div>
            <div class="col-auto d-none">
                <a class="btn btn-info me-1" href="{% url 'app_banks:entry_new_invoices' seller.shortname %}">
                    <span>Solo Facturas</span>
                </a>
            </div>

            <div class="col-auto align-self-end">
              <label for="yeargen" class="form-label"><b>Año de los Asientos a Generar:</b></label> &nbsp;
              <input type="text" class="form-control form-input w-100" id="yeargen" value="Todos" name="yeargen" style="width:100px;height:38px;margin-right: -5px;" readonly>
            </div>
            <div class="col-auto align-self-end">
              <label for="numero" class="form-label"><b>Cantidad Asientos a Generar:</b></label> &nbsp;
              <input type="text" class="form-control form-input w-100" id="numero" value="{{limit_entries}}" name="numero" oninput="formatoNumero(this)" style="width:100px;height:38px;margin-right: -5px;">
            </div>
            <div class="col-auto align-self-end">                
              <a id="generateEntryBtn2" type="button" class="btn btn-primary me-1" href="#" onclick="generateEntries()">
                  <span>Generar Asientos</span>
              </a> 
            </div>
            <div class="col-auto align-self-end" style="visibility: hidden;">                
              <a id="generateEntryBtn" type="button" class="btn btn-primary me-1" href="#" onclick="generarAsientos()">
                  <span>Generar Asientos</span>
              </a> 
              <div id="generating_spinner" class="d-none">
                <button class="btn btn-primary w-100" type="button" disabled="">
                  <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                  <b>&nbsp; Generando...</b>
                </button>
              </div>
            </div>
            <div class="col-auto align-self-end">
                <a class="btn btn-danger" href="{% url 'app_banks:entry_delete_all' seller.shortname %}">
                    <i class="feather icon-trash-2"></i><span>Elimina Todos Asientos</span>
                </a>
            </div>
          </div>          
        </div>
        <div class="mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
      </div>
    </div>

    <!-- Buttons | START -->
    <div class="col-12" id="entry_row">
      <div class="row">
        <div class="mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
        <div class="col my-2 mx-2">
          <div class="row justify-content-end">
            <div class="col-xl col-lg-4">
              <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."/>
            </div>
            <div class="col-xl col-lg-4">
              <select class="form-control form-select" name="year" id="year" onchange="filter(); onChangeYear();" >
                <option value="">Todos los años</option>
                <option value="2025">2025</option>
                <option value="2024">2024</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
                <option value="2020">2020</option>
              </select>
            </div>
            <div class="col-xl col-lg-4">
              <select class="form-control form-select" name="month" id="month" onchange="filter();" disabled>
                <option value="">Todos los meses</option>
                <option value="-1">Trimestre 1</option>
                <option value="-2">Trimestre 2</option>
                <option value="-3">Trimestre 3</option>
                <option value="-4">Trimestre 4</option>
                <option value="01">Enero</option>
                <option value="02">Febrero</option>
                <option value="03">Marzo</option>
                <option value="04">Abril</option>
                <option value="05">Mayo</option>
                <option value="06">Junio</option>
                <option value="07">Julio</option>
                <option value="08">Agosto</option>
                <option value="09">Septiembre</option>
                <option value="10">Octubre</option>
                <option value="11">Noviembre</option>
                <option value="12">Diciembre</option>
              </select>
            </div>
            <div class="col-auto">
              <select class="form-control form-select me-3" onchange="onChangeSelect(event)">
                <option value="all">Ver Todos los Asientos</option>
                <option value="balanced">Ver Asientos Cuadrados</option>
                <option value="disbalanced">Ver Asientos Descuadrados</option>
                <option value="pending" selected>Ver Asientos Pendientes Exportar</option>
                <option value="exported">Ver Asientos Exportados</option>
              </select>
            </div>
            {% comment %}
            <div class="col-auto">                
                <a id="generateEntryBtn" type="button" class="btn btn-primary me-1" href="#" onclick="generarAsientos()">
                    <span>Generar Asientos</span>
                </a> 
                <div id="generating_spinner" class="d-none">
                  <button class="btn btn-primary w-100" type="button" disabled="">
                    <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                    <b>&nbsp; Generando...</b>
                  </button>
                </div>
            </div>
            {% endcomment %}
            <div class="col-auto">
                <div id="export_excel">
                  <button class="btn btn-primary w-100" type="button" id="export-csv-btn" onclick="generateExcel()">
                    Exportar Excel
                  </button>
                </div>
                <div id="export_excel_spinner" style="display:none;">
                  <button class="btn btn-primary w-100" type="button" disabled="">
                    <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                    <b>Generando...</b>
                  </button>
                </div>
            </div>
          </div>          
        </div>
        <div class="mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
      </div>
    </div>
    <!-- Buttons | END-->

    <!-- Toasts/Alerts | START -->
    <div class="col-12">
        {% if qty_invoices_amz_not_reconciled is not None and qty_invoices_amz_not_reconciled > 0%}
        <div class="row">
          <div class="mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
          <div class="col my-1">
            <div class="toast w-100 text-white bg-secondary fade show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                      <b>Facturas TXT Amazon pendientes Conciliar: &nbsp;  {{qty_invoices_amz_not_reconciled}}</b>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
          </div>
          <div class="mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>    
        </div>      
        {% endif %}
        
        {% if invoices_pending is not None and invoices_pending > 0%}
        <div class="row">
          <div class="col-12 mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
          <div class="col my-1">
            <div class="toast w-100 text-white bg-secondary fade show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                      <b>Facturas pendientes generar asientos: &nbsp;  {{invoices_pending}} | {{time_pending}}</b>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
          </div>
          <div class="col-12 mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>     
        </div>     
        {% endif %}

        {% if qty_reconciliation_not_used is not None and qty_reconciliation_not_used > 0%}
        <div class="row">
          <div class="col-12 mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>
          <div class="col my-1">
            <div class="toast w-100 text-white bg-secondary fade show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                      <b>Conciliaciones sin Asiento: &nbsp; {{qty_reconciliation_not_used}}</b>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
          </div>
          <div class="col-12 mx-1 my-1" style="width:1%"> <!-- Empty Col --> </div>    
        </div>      
        {% endif %}
    </div>
    <!-- Toasts/Alerts | END -->

</div>

              
  
  <div class="row" >
      <div class="col-12"  id="entry_row">
        <div class="row">
          <!-- Empty Col -->
          <div class="mx-1" style="width:1%"></div>

          <div class="col"> 
            <!-- entry Movements Table -->
            <div id="table_entry" class="table-responsive">
              <table id="entry_table"  class="table nowrap" style="width:100%">
                <thead class="table-head">
                  <tr class="bg-white">
                    <th class="movement-width">ID</th>
                    <th class="movement-width">Balanced</th>
                    <th class="movement-width">Núm</th>
                    <th class="movement-width">Fecha</th>
                    <th class="movement-width">Documento</th>
                    <th class="movement-width">Concepto</th>
                    <th class="movement-width">Cuenta</th>
                    <th class="movement-width">Descripción cuenta</th>
                    <th class="movement-width">Debe</th>
                    <th class="movement-width">Haber</th>
                    <!-- <th class="movement-width">Acciones</th> -->
                  </tr>
                </thead>
                <tbody>

              </tbody>
              </table>
            </div>
          </div>

          <!-- Empty Col -->
          <div class="mx-1" style="width:1%"></div>
        </div>
      </div>
  
  </div>



{% endblock content %}

{% block javascripts %}

  <!-- DEBUG  -->
  <script type="text/javascript">
    const debug = true;
  </script>
  <!-- DEBUG  -->

  {% comment %} <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script> {% endcomment %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css"> 
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

  <script>

    // INITALIZE TOAST FOR ALERTS
    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });


    let dataTable = null;
    let timer;
    const ajaxData = (d) =>{
      if (debug) console.log('ajaxData | d: ', d);

      let year = $("#year").val();
      let month = $("#month").val();
      let input = searchInput.val();

      
      if(year){
        if (debug) console.log('filterDT | year ', year);
        d.year = year;
      }

      if(month){
        if (debug) console.log('filterDT | month ', month);
        d.period = month;
      }

      if(input){
        if(debug) console.log('filterDT | input ', input);
        d.search = input;
      }

      if (d.order.length > 0) {
        orderby = [];
        for (const o of d.order) {
          name = d.columns[o.column].data;
          if (name == 'entry_num') {
            orderby.push({"dir": o.dir, "name": "entry_num"});
          } else if (name == 'entry_date') {
            orderby.push({"dir": o.dir, "name": "entry_date"});
          } else if (name == 'entry_document') {
            orderby.push({"dir": o.dir, "name": "entry_document"});
          } else if (name == 'entry_concept') {
            orderby.push({"dir": o.dir, "name": "entry_concept"});
          } else if (name == 'entry_accounting_account') {
            orderby.push({"dir": o.dir, "name": "entry_accounting_account"});
          } else if (name == 'entry_accounting_account_description') {
            orderby.push({"dir": o.dir, "name": "entry_accounting_account_description"});
          } else if (name == 'entry_debit') {
            orderby.push({"dir": o.dir, "name": "entry_debit"});
          } else if (name == 'entry_credit') {
            orderby.push({"dir": o.dir, "name": "entry_credit"});
          } else if (name == 'is_entry_balanced_result' || name == 'is_entry_balanced') {
            console.log('is_entry_balanced: ', o.dir)
            orderby.push({"dir": o.dir, "name": "is_entry_balanced_result"});
          } else {
            orderby.push({"dir": o.dir, "name": "entry_num"});
          }
        }
        if (!d.order.some(order => order.name === "entry_num")) {
          orderby.push({"dir": "asc", "name": "entry_num"});
        }
        // orderby.push({ "dir": o.dir, "name": "status__order"});
        orderby.push({"dir": 'desc', "name": "pk"});
        if (debug) console.log('filterDT | orderBy: ', orderby);
        d.order = JSON.stringify(orderby);
      }


    }
  $(document).ready(function(){
        const dataTableOptions = {
          paging: true,
          lengthMenu: [[25, 50, 100], [25, 50, 100]],
          pageLength: 25, // Paginator Default Value
          searching: true,
          ordering: true,
          order: [[1, "asc"],[2, "asc"]], // Default Order by Document
          truncation: true,
          info: true,
          footer: true,
          processing: true,
          bLengthChange: true,
          bInfo: false,    
          language: {
            lengthMenu: "_MENU_",
            zeroRecords: "No se han encontrado asientos.",
            info: "_TOTAL_ resultados. ",
            search: "Buscar:",
            infoEmpty: "No hay resultados que coincidan con su búsqueda.",
            infoFiltered: ""
          },
          dom: 'lrtip',
          fixedHeader: true,
          serverSide: true,
          ajax: {
            url: '/sellers/{{seller.shortname}}/get/entry/DT/?used_in_export=false',
            dataSrc: 'data',
            data: function (d) {
                return ajaxData(d);
            }
          },
          columns: [
            { data: 'id', title: 'ID', visible: false, orderable: false },
            { data: 'is_entry_balanced_result',
              title: '🟢/🔴',
              render: function (data, type, row) {
                //  console.log("data", data);
                if (data == 'True') {
                    return '🟢';
                    //return 'B<span class="d-none">B</span><span>🟢</span>'; 
                } else {
                  return '🔴';
                    //return 'A<span class="d-none">A</span><span>🔴</span>'; 
                }
              }
            },
            { data: 'entry_num', title: 'Número', type: 'num' },
            { data: 'entry_date', title: 'Fecha', orderable: false},
            { data: 'entry_document', title: 'Documento', orderable: false},
            { data: 'entry_concept', title: 'Concepto', orderable: false},
            { data: 'entry_accounting_account', title: 'Cuenta', orderable: false, className: 'style-bold' },
            { data: 'entry_accounting_account_description', title: 'Descripción cuenta', orderable: false },
            { data: 'entry_debit', title: 'Debe', orderable: false, className: 'style-bold', render: function ( data, type, row ) {
              if (data == 0) return '';
              else return data;
            } },
            { data: 'entry_credit', title: 'Haber', orderable: false, className: 'style-bold', render: function ( data, type, row ) {
              const intraComunity = row.entry_accounting_account == '*********'
              if (data == 0 && !intraComunity) return '';
              else return data;
            } },
            // { data: 'entry_actions', title: 'Acciones', render: function ( data, type, row ) {
            //   let html = `
            //     <div>
            //       <a data-bs-toggle="tooltip" data-bs-placement="top" title="Eliminar" class="btn btn-icon btn-danger" href="{% url 'app_banks:entry_list' seller.shortname %}${row.id}/delete/">
            //         <i class="feather icon-trash-2"></i>
            //       </a>
            //     </div>
            //   `;
            //   return html;
            // } },
          ],
          // columnDefs: [
          //   { targets: 2, orderable: true}
          // ],
        };
        dataTable = $("#entry_table").DataTable(dataTableOptions);
    });

    const onChangeSelect = (e) => {
      console.log("onChangeSelect");
      if (e === undefined || e === null) return;
      const value = e.target.value ? e && e.target && e.target.value : null;

      if (value === 'all') {
        dataTable.ajax.url('/sellers/{{seller.shortname}}/get/entry/DT/').load();
      } else if (value === 'pending') {
        dataTable.ajax.url('/sellers/{{seller.shortname}}/get/entry/DT/?used_in_export=false').load();        
      } else if (value === 'exported') {
        dataTable.ajax.url('/sellers/{{seller.shortname}}/get/entry/DT/?used_in_export=true').load();        
      } else if (value === 'balanced') {
        dataTable.ajax.url('/sellers/{{seller.shortname}}/get/entry/DT/?is_entry_balanced=true').load();        
      } else if (value === 'disbalanced') {
        dataTable.ajax.url('/sellers/{{seller.shortname}}/get/entry/DT/?is_entry_balanced=false').load();        
      }

    }

    const onChangeYear = () => {
      const year = $("#year");
      const month = $("#month");
      const yeargen = $("#yeargen");
      if(year && year.val()){
        month.prop("disabled", false);
        yeargen.val(year.val());
      }else{
        month.val('');
        month.prop("disabled", true);
        yeargen.val('Todos');
      }
    }

    const searchInput = $("#search");
    searchInput.on("keydown", (e) => {
    if (e.key === "Enter") {
        clearTimeout(timer);
        filter();
          }
      });

      searchInput.on("input", () => {
          clearTimeout(timer);
          timer = setTimeout(filter, 500);
      });

    const filter = () =>{
      dataTable.draw();
    }

    async function generateExcel(){

      document.getElementById("export_excel_spinner").style.display = "block";
      document.getElementById("export_excel").style.display = "none";

      // PREPARAR DATOS DEL FILTRO PARA ENVIAR
      let searchInput = $("#search").val();
      let year = $("#year").val();
      let month = $("#month").val();
      let dataFiltered = {
        search: searchInput,
        year: year,
        month: month,
      }

      const generateAsientosCSV = await fetch("{% url 'app_banks:generate_export' seller.shortname %}", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': "{{ csrf_token }}"
        },
        body: JSON.stringify(dataFiltered)
      });
      if (generateAsientosCSV.ok){
        const json = await generateAsientosCSV.json();
        if (json.zip_filePath) {
          Toast.fire({
            icon: 'success',
            title: json.message
          });
          let downloadLink = document.createElement('a');
          downloadLink.href = json.zip_filePath;
          downloadLink.setAttribute('download', '');
          downloadLink.click();
        }
        else if (json.error){
          Toast.fire({
            icon: 'warning',
            title: json.error
          });
        }
      }
      else{
        console.log("Error en la solicitud: ", generateAsientosCSV);
        Toast.fire({
          icon: 'error',
          title: json.error
        });
      }
      document.getElementById("export_excel_spinner").style.display = "none";
      document.getElementById("export_excel").style.display = "block";
      
    }      
    
    const createEntryBtn = document.getElementById('generateEntryBtn');
    createEntryBtn.addEventListener('click', () => {
      createEntryBtn.classList.add('d-none');
      document.getElementById('generating_spinner').classList.remove('d-none');
    });

    
    function formatoNumero(input) {
      // Eliminar cualquier caracter que no sea dígito
      let numero = input.value.replace(/\D/g, '');
      // Agregar los puntos de miles utilizando expresiones regulares
      numero = numero.replace(/\B(?=(\d{3})+(?!\d))/g, '');
      // Asignar el número formateado de vuelta al campo de entrada
      input.value = numero;
  }

    function generarAsientos() {
      // Obtener el valor del input
      const shortname = "{{seller.shortname}}";
      let numero = document.getElementById("numero").value;
      let yeargen = document.getElementById("yeargen").value;
      if (year == 'Todos') year = 'all';
      console.log('yeargen: ', yeargen, 'numero: ', numero);

      // Construir la URL con shortname y el número como parte de la ruta
      const url = `/sellers/${shortname}/entry/new-all/${yeargen}/${numero}/`;

      // Redireccionar a la URL construida
      window.location.href = url;
    }

    function generateEntries() {
      const shortname = "{{seller.shortname}}";
      let yeargen = document.getElementById("yeargen").value;
      let max_entries = document.getElementById("numero").value;
      if (yeargen == 'Todos') yeargen = 'all';

      let url = `{% url 'app_banks:generate_entries' seller.shortname 'YEAR_PLACEHOOLDER' %}?max_entries=${max_entries}`;
      url = url.replace('YEAR_PLACEHOOLDER', yeargen);

      window.location.href = url;

    }


    document.addEventListener('DOMContentLoaded', function() {
      $('#entry_table').on('draw.dt', function () {

          // Obtener todas las filas de la tabla
          var rows = document.querySelectorAll('#entry_table tbody tr');

          // Inicializar variable para almacenar el valor de la primera columna anterior
          var prevFirstColumnValue = '';
          var rowCount = 1; // Inicializar el contador de filas agrupadas

          // Iterar sobre cada fila de la tabla
          rows.forEach(function(row, index) {
              // Obtener el valor de la primera columna de la fila actual
              if (row.cells[1] === undefined) return;

              var currentFirstColumnValue = row.cells[1].innerText.trim();
              
              
              // Verificar si el valor de la primera columna ha cambiado desde la fila anterior
              if (currentFirstColumnValue === prevFirstColumnValue) {
                  // Agregar una clase especial a la fila para marcar que no se debe mostrar el borde inferior
                  row.classList.add('grouped-row', 'no-bottom-border');
                  
              } else {
                  row.classList.add('grouped-row-first');
                  // Actualizar el valor previo de la columna y el contador de filas agrupadas
                  prevFirstColumnValue = currentFirstColumnValue;
                  rowCount++;
                
              }

              // Aplicar el color rojo si el valor de la última o penúltima columna es positivo
              if (parseFloat(row.cells[row.cells.length - 1].innerText.trim()) < 0 ) {
                  row.cells[row.cells.length - 1].style.color = 'red';
              }
              if (parseFloat(row.cells[row.cells.length - 2].innerText.trim()) < 0) {
                  row.cells[row.cells.length - 2].style.color = 'red';
              }
          
              // Añadir una clase única a cada conjunto de filas iguales
              row.classList.add('row-' + rowCount);
                
              if (row.classList.contains('grouped-row')) {
                  // Dejar vacío el contenido de la primera y segunda columna
                  {% comment %} 
                  row.cells[0].innerText = '';
                  row.cells[1].innerText = '';
                  row.cells[2].innerText = '';
                  row.cells[3].innerText = ''; 
                  {% endcomment %}
              }
          });
      });
    });


  </script>
{% endblock javascripts %}