{% if object.file %}
  {% if ".jpg" in object.file.name or ".JPG" in object.file.name or ".png" in object.file.name or ".PNG" in object.file.name %}
    <img src="{{ object.file.url }}" style="width:100% !important;">
  {% else %}
    <object type="application/pdf" class="pdf border" data="{{ object.file.url }}">
      <embed type="application/pdf" class="pdf border" src="{{ object.file.url }}">
      <a href="{{ object.file.url }}"> Descargar </a>
    </object>
  {% endif %}
{% elif invoice.is_generated == True %}
  <object type="application/pdf" class="pdf border"
          data="{% url 'app_invoices:seller_invoice_file' seller.shortname invoice.pk %}">
    <embed type="application/pdf" class="pdf border"
           src="{% url 'app_invoices:seller_invoice_file' seller.shortname invoice.pk %}">
    <a href="{% url 'app_invoices:seller_invoice_file' seller.shortname invoice.pk %}"> Descargar </a>
  </object>
{% endif %}
