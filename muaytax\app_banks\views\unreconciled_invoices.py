import json
from datetime import datetime

from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse
from django.shortcuts import get_object_or_404
from django.http import HttpResponseRedirect, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.core.serializers import serialize
from django.views.generic import ListView, View
from django.db.models import F, Q, Sum, Value, CharField, OuterRef, Subquery, Case, When, DecimalField, ExpressionWrapper, BooleanField
from django.forms.models import model_to_dict
from django.conf import settings

from muaytax.app_banks.models.movement import Movement
from muaytax.app_banks.models.reconciliation import Reconciliation
from muaytax.app_banks.models.bank import Bank
from muaytax.app_providers.models.provider import Provider
from muaytax.app_customers.models.customer import Customer
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.bank_type import BankType
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType
from muaytax.dictionaries.models.movement_status import MovementStatus
from muaytax.dictionaries.models.invoice_type import InvoiceType
from muaytax.dictionaries.models.transaction_type import TransactionType
from muaytax.dictionaries.models.countries import Country
from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission

debug = settings.DEBUG # Variable de depuración global

class UnreconciledInvoices(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Invoice
    template_name = "banks/unreconciled_invoices_list.html"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        invoices = Invoice.objects.filter(seller=seller)
        
        # Filtrar por tipo de transacción
        transaction_type = self.request.GET.get('transaction_type')
        if transaction_type and transaction_type != "all":
            invoices = invoices.filter(transaction_type__code=transaction_type)
            
        return invoices

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        banks = Bank.objects.filter(bank_seller=seller)
        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["debug"] = debug
        context["banks"] = banks
        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "banks": serialize("json", context["banks"]),
        }
        
        # Filtrar las facturas de este vendedor ordenadas por fecha de contabilización ascendente
        first_invoice = Invoice.objects.filter(seller=seller, accounting_date__isnull=False).order_by('accounting_date').first()

        # Obtener el año de la primera fecha de contabilización o el año actual si no hay facturas con fecha de contabilización
        current_year = datetime.now().year
        first_accounting_year = first_invoice.accounting_date.year if first_invoice else current_year

        # Crear una lista de años desde first_accounting_year hasta el año actual
        years_list = list(range(first_accounting_year, current_year + 1)) if first_accounting_year <= current_year else [current_year]
        
        context['years_list'] = years_list
        
        invoices_tax_country = Invoice.objects.filter(seller_id=seller.id).exclude(
            Q(tax_country=None) |
            Q(invoice_category__code__icontains='_copy') |
            Q(transaction_type__code__icontains='-transfer')
        ).values_list('tax_country__iso_code', flat=True).distinct()

        invoices_departure_country = Invoice.objects.filter(seller_id=seller.id).exclude(
            Q(departure_country=None) |
            Q(invoice_category__code__icontains='_copy') |
            Q(transaction_type__code__icontains='-transfer')
        ).values_list('departure_country__iso_code', flat=True).distinct()

        if seller and seller.oss and seller.oss == True:
            context["invoices_tax_country"] = Country.objects.filter(
                Q(iso_code__in=invoices_tax_country) | Q(is_european_union=True)
            )
            context["invoices_departure_country"] = Country.objects.filter(
                Q(iso_code__in=invoices_departure_country) | Q(is_european_union=True)
            )
        else:
            context["invoices_tax_country"] = Country.objects.filter(iso_code__in=invoices_tax_country)
            context["invoices_departure_country"] = Country.objects.filter(iso_code__in=invoices_departure_country)

        # Obtener invoice types distintos para este seller
        invoice_types = InvoiceType.objects.all()
        transaction_types = TransactionType.objects.exclude(category="transfer").order_by("description")
        
        context['invoice_types'] = invoice_types
        context["transaction_types"] = transaction_types

        # I want to gen all accounting accounts From Dictionary Accounting Account, and Add Providers with account_expenses to form new accounting_account 
        # Code: provider.account_expenses, Description: provider.name, Country: provider.country
        accounting_account = list(AccountingAccount.objects.all())

        providers = Provider.objects.filter(seller=seller).exclude(provider_number__isnull=True).exclude(country__isnull=True).exclude(name__isnull=True).distinct()
        provider_accounts = [
            AccountingAccount(
                code=f'400{prov.provider_number}',
                description=f'Proveedor {prov.name} ({prov.country.pk.upper()})',
                country=prov.country
            ) for prov in providers
        ]
        
        customers = Customer.objects.filter(seller=seller).exclude(customer_number__isnull=True).exclude(country__isnull=True).exclude(name__isnull=True).distinct()
        customer_accounts = [
            AccountingAccount(
                code=f'430{cust.customer_number}',
                description=f'Cliente {cust.name} ({cust.country.pk.upper()})',
                country=cust.country
            ) for cust in customers
        ]
        
        new_accounting_account = accounting_account # + provider_accounts + customer_accounts
        context["accounting_account"] = new_accounting_account
        
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class NewReconcilationForUnreconlicedInvoice(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    
    def get(self, request, *args, **kwargs):
        invoice_id = request.GET.get('invoice_id')
        account_id = request.GET.get('account_id')
        bank_name= request.GET.get('bank_name')
        return self._get_response(invoice_id, account_id, bank_name)
    
    def post(self, request, *args, **kwargs):
        invoice_id = request.POST.get('invoice_id')
        account_id = request.POST.get('account_id')
        bank_name = request.POST.get('bank_name')
        return self._get_response(invoice_id, account_id, bank_name)
    
    def _get_response(self, invoice_id, account_id, bank_name):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank_name = str(bank_name).lower().strip()
        print(f'invoice_id: {invoice_id}, bank_name: {bank_name}')

        try:
            if bank_name is not None:
                if invoice_id is not None:
                    invoice = Invoice.objects.filter(pk=invoice_id, seller = seller).first()
                    if invoice is not None:
                        print(invoice)

                        # GET AMOUNT | If Bank is Amazon Ignore Concept Quantity => Concept Quantity = 1
                        if (bank_name == 'amazon'):
                            amount_euros = invoice.concept_set.aggregate( 
                                total = Sum( 
                                    (F('amount_euros')) + 
                                    (F('amount_euros') * F('vat') / 100) +
                                    (F('amount_euros') * F('eqtax') / 100) -
                                    (F('amount_euros') * F('irpf') / 100) 
                                ) 
                            )['total'] or 0
                        else:
                            amount_euros = invoice.concept_set.aggregate( 
                                total = Sum( F('quantity') * (
                                        (F('amount_euros')) + 
                                        (F('amount_euros') * F('vat') / 100) +
                                        (F('amount_euros') * F('eqtax') / 100) -
                                        (F('amount_euros') * F('irpf') / 100) 
                                    )
                                ) 
                            )['total'] or 0
                        amount = amount_euros

                        if invoice.invoice_category.pk == 'sales':
                            amount = amount * -1
                            amount_euros = amount_euros * -1

                        rec_type = ReconciliationType.objects.filter(pk='invoice').first()
                        print('rec_type: ', rec_type)
                        rec_type_ac = ReconciliationType.objects.filter(pk='account').first()
                        print('rec_type_ac: ', rec_type_ac)
                        mov_stat = MovementStatus.objects.filter(pk='conciliated').first()
                        print('mov_stat: ', mov_stat)
                        bank = Bank.objects.filter(bank_name__iexact=bank_name, bank_seller = seller).first()
                        print('bank: ', bank)

                        if (bank is None and bank_name == 'amazon'):
                            try:
                                amz_bank = Bank()
                                amz_bank.bank_name = 'AMAZON'
                                amz_bank.bank_accounting_account = '*********'
                                amz_bank.bank_account_type = BankType.objects.get(code="bankaccount") or ''
                                amz_bank.bank_seller = seller
                                amz_bank.save()
                                bank = amz_bank
                            except Exception as e:
                                print(f'Error al crear el banco Amazon: {e}')

                        if (bank is None and bank_name == 'accounts'):
                            try:
                                acc_bank = Bank()
                                acc_bank.bank_name = 'Accounts'
                                acc_bank.bank_accounting_account = 0000 # Next Accounting Account
                                acc_bank.bank_account_type = BankType.objects.get(code="bankaccount") or ''
                                acc_bank.bank_seller = seller
                                acc_bank.save()
                                bank = acc_bank
                            except Exception as e:
                                print(f'Error al crear el banco Cuenta: {e}')
                        
                        if (bank is not None):
                            if (rec_type is not None or mov_stat is not None):
                                # GET YEAR AN MONTH OF THE INVOICE
                                year = invoice.expedition_date.year
                                month = invoice.expedition_date.month
                                print(year, month)
                                
                                # GET LAST MOVEMENT NUMBER
                                last_movement = Movement.objects.filter(bank = bank, bank__bank_seller = seller)
                                last_movement = last_movement.filter(movement_number__startswith = f'{year}-{month}-')
                                last_movement = last_movement.order_by('-movement_number').first()
                                if last_movement is not None:
                                    movement_number = last_movement.movement_number.split('-')[2]
                                    movement_number = int(movement_number) + 1
                                else:
                                    movement_number = 1
                                movement_number = f'{year:04}-{month:02}-{movement_number:06}'
                                print(movement_number)
                                
                                # Make New Movement
                                movement = Movement()
                                movement.movement_number = movement_number
                                movement.status = mov_stat
                                movement.concept = f"Conciliacion de la factura {invoice.reference}"
                                movement.amount = - amount
                                movement.amount_euros = - amount_euros
                                movement.movement_date = invoice.expedition_date
                                movement.bank = bank

                                if bank_name == 'accounts':
                                    movement.concept = f"Conciliacion de la factura {invoice.reference} con la cuenta contable {account_id}"
                                    movement.amount=0
                                    movement.amount_euros=0


                                print(f'MOVEMENT: {model_to_dict(movement)}')
                                movement.save()
                                print(f'{movement.id} saved')

                                if (movement is not None and movement.id is not None):
                                    # Make New Reconciliation
                                    new_rec = Reconciliation()
                                    new_rec.movement = movement
                                    new_rec.invoice = invoice
                                    new_rec.amount = amount_euros
                                    new_rec.type = rec_type
                                    new_rec.save()
                                    print(f'RECONCILIATION: {new_rec}')
                                    print(f'{new_rec.id} saved')

                                    if bank_name == 'accounts':
                                        # Make New Reconciliation Accounting Account
                                        account = AccountingAccount.objects.filter(pk=account_id).first()
                                        print(f'ACCOUNT: {account} - {account_id}')
                                        new_rec_ac = Reconciliation()
                                        new_rec_ac.movement = movement
                                        new_rec_ac.accounting_account = account
                                        new_rec_ac.accounting_account_detail = account_id
                                        new_rec_ac.amount = - amount_euros
                                        new_rec_ac.type = rec_type_ac
                                        new_rec_ac.save()
                                        print(f'RECONCILIATION AC: {new_rec_ac}')
                                        print(f'{new_rec.id} saved')

                                    if (new_rec is not None and new_rec.id is not None):
                                        data.append(model_to_dict(new_rec))
                                        if bank_name == 'accounts':
                                            if (new_rec_ac is not None and new_rec_ac.id is not None):
                                                data.append(model_to_dict(new_rec_ac))
                                            else:
                                                error = True
                                                error_message = "No se ha podido crear la conciliación de la cuenta contable."
                                    else:
                                        error = True
                                        error_message = "No se ha podido crear la conciliación."
                                        try:
                                            movement.delete()
                                        except:
                                            pass
                                else:
                                    error = True
                                    error_message = "No se ha podido crear el movimiento."
                            else:
                                error = True
                                error_message = "El tipo de conciliación o el estado del movimiento no existe."
                        else:
                            error = True
                            error_message = "El banco no existe."
                    else:
                        error = True
                        error_message = "La factura no existe."
                else:
                    error = True
                    error_message = "No se ha recibido el invoice_id o el movement_id."
            else:
                error = True
                error_message = "No se ha recibido el bank_name"
        except Exception as e:
            error = True
            error_message = str(e)

            
        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "result": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "result": "ok",
                "message": "Conciliación creada correctamente",
                "data": data,               
            }
            # update_movement_status(seller, movement_id)
            # if movement_transfer_id is not None:
            #     update_movement_status(seller, movement_transfer_id)

        print(f'data: {data}')
        # Devolver una respuesta en formato JSON
        return JsonResponse(data)
    
    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

class NewReconciliationsForUnreconciledAmzInvoices(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def get(self, request, *args, **kwargs):
        return self._get_response()
    
    def post(self, request, *args, **kwargs):
        return self._get_response()
    
    def _get_response(self):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank_name = 'Amazon'

        try:
            
            invoices = Invoice.objects.filter(seller = seller, status='revised')
            invoices = invoices.filter(reconciliation_invoice__isnull=True)
            invoices = invoices.filter(Q(provider__name__icontains=bank_name) | Q(customer__name__icontains=bank_name))
            invoices = invoices.exclude(is_generated_amz=True).exclude(is_txt_amz=True).exclude(invoice_category__code__icontains='_copy')

            if (invoices is not None):
                for invoice in invoices:
                    print(f"----{invoice.pk}")
                    try:
                        resp = NewReconcilationForUnreconlicedInvoice(kwargs={"shortname": seller.shortname})._get_response(invoice.pk, bank_name)
                        if resp is not None:
                            resp = json.loads( resp.content )
                            if resp['result'] == 'ok' and resp['data'] is not None:
                                for d in resp['data']:
                                    data.append( d )
                            elif resp['result'] == 'error':
                                error = True
                                error_message = resp['message']
                    except Exception as e:
                        print(f'Error al crear la conciliación: {e}')
                        error = True
                        error_message = str(e)

            if data is not None and len(data) > 0:
                error = False

        except Exception as e:
            error = True
            error_message = str(e)

        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "result": "error",
                "message": error_message,
                "data": data
            }
        else:
            data = {
                "result": "ok",
                "message": "Conciliaciónes creadas correctamente",
                "data": data,               
            }

        # Devolver una respuesta en formato JSON
        return JsonResponse(data)

class NewReconciliatioTransferForAmazon(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    def get(self, request, *args, **kwargs):
        movement_id = request.GET.get('movement_id') or None
        bank_name= request.GET.get('bank_name')
        return self._get_response(movement_id, bank_name)
    
    def post(self, request, *args, **kwargs):
        movement_id = request.POST.get('movement_id') or None
        bank_name = request.POST.get('bank_name')
        return self._get_response(movement_id, bank_name)
    
    def _get_response(self, movement_id, bank_name):
        error = False
        error_message = None
        data = []
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank_name = str(bank_name).lower().strip()
        print(f'movement_id: {movement_id}, bank_name: {bank_name}')

        try:
            if bank_name is not None:
                if movement_id is not None:
                    mv = Movement.objects.filter(pk=movement_id, bank__bank_seller = seller).first()

                    # GET AMOUNT | If Bank is Amazon Ignore Concept Quantity => Concept Quantity = 1
                    amount = mv.amount
                    amount_euros = mv.amount_euros

                    rec_type = ReconciliationType.objects.filter(pk='transfer').first()
                    print('rec_type: ', rec_type)
                    mov_stat = MovementStatus.objects.filter(pk='conciliated').first()
                    print('mov_stat: ', mov_stat)
                    bank = Bank.objects.filter(bank_name__iexact=bank_name, bank_seller = seller).first()
                    print('bank: ', bank)

                    if (bank is None and bank_name == 'amazon'):
                        try:
                            amz_bank = Bank()
                            amz_bank.bank_name = 'AMAZON'
                            amz_bank.bank_accounting_account = '*********'
                            amz_bank.bank_account_type = BankType.objects.get(code="bankaccount") or ''
                            amz_bank.bank_seller = seller
                            amz_bank.save()
                            bank = amz_bank
                        except Exception as e:
                            print(f'Error al crear el banco Amazon: {e}')
                    
                    if (bank is not None):
                        if (rec_type is not None or mov_stat is not None):
                            # GET YEAR AN MONTH OF THE MOVEMENT SOURCE
                            year = mv.movement_date.year
                            month = mv.movement_date.month
                            print(year, month)
                            
                            # GET LAST MOVEMENT NUMBER
                            last_movement = Movement.objects.filter(bank = bank, bank__bank_seller = seller)
                            last_movement = last_movement.filter(movement_number__startswith = f'{year}-{month}-')
                            last_movement = last_movement.order_by('-movement_number').first()
                            if last_movement is not None:
                                movement_number = last_movement.movement_number.split('-')[2]
                                movement_number = int(movement_number) + 1
                            else:
                                movement_number = 1
                            movement_number = f'{year:04}-{month:02}-{movement_number:06}'
                            print(movement_number)
                            
                            concept= f"Pago del movimiento {mv.id}"
                            if (amount_euros * -1) < 0:
                                concept = f"Cobro del movimiento {mv.id}"
                            
                            # Make New Movement
                            movement = Movement()
                            movement.movement_number = movement_number
                            movement.status = mov_stat
                            movement.concept = concept
                            movement.amount = - amount
                            movement.amount_euros = - amount_euros
                            movement.movement_date = mv.movement_date
                            movement.bank = bank
                            print(f'MOVEMENT: {model_to_dict(movement)}')
                            movement.save()
                            print(f'{movement.id} saved')

                            if (movement is not None and movement.id is not None):
                                # Make New Reconciliation: Source to Amz
                                new_rec = Reconciliation()
                                new_rec.movement = mv
                                new_rec.movement_transfer = movement
                                new_rec.amount = - amount_euros
                                new_rec.type = rec_type
                                print(new_rec)
                                new_rec.save()

                                # Make New Reconciliation Amz to Source
                                new_rec_amz = Reconciliation()
                                new_rec_amz.movement = movement
                                new_rec_amz.movement_transfer = mv
                                new_rec_amz.amount = amount_euros
                                new_rec_amz.type = rec_type
                                print(new_rec_amz)
                                new_rec_amz.save()

                                if (new_rec is not None and new_rec.id is not None and new_rec_amz is not None and new_rec_amz.id is not None):
                                    mv.status = mov_stat
                                    mv.save()
                                    data.append(model_to_dict(new_rec))
                                else:
                                    error = True
                                    error_message = "No se ha podido crear la conciliación."
                                    try:
                                        movement.delete()
                                    except:
                                        pass
                            else:
                                error = True
                                error_message = "No se ha podido crear el movimiento."
                        else:
                            error = True
                            error_message = "El tipo de conciliación o el estado del movimiento no existe."
                    else:
                        error = True
                        error_message = "El banco no existe."
                else:
                    error = True
                    error_message = "No se ha recibido el movement_id."
            else:
                error = True
                error_message = "No se ha recibido el bank_name"
        except Exception as e:
            error = True
            error_message = str(e)   

        # Si Existe un error => Devolver el Error
        if error and error_message:
            data = {
                "result": "error",
                "message": error_message,
                "data": []
            }
        else:
            data = {
                "result": "ok",
                "message": "Conciliación creada correctamente",
                "data": data,               
            }

        # Devolver una respuesta en formato JSON
        return JsonResponse(data)


    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))
