import os
import re
from collections import defaultdict

# === CONFIGURACIÓN ===
BASE_DIR = os.path.dirname(__file__)
CSS_FILE = os.path.join(BASE_DIR, "allstyle.css")
OUTPUT_FILE_USED = os.path.join(BASE_DIR, "style_formater.css")
OUTPUT_FILE_UNUSED = os.path.join(BASE_DIR, "style_restos.css")

TEMPLATE_PATHS = [
    "muaytax/templates/sellers/seller_vat_request_service_iva.html",
    "muaytax/templates/sellers/seller_vat_review_manager_service_iva.html"
]

INCLUDE_DIR = "muaytax/templates/sellers/include/service_iva"
TEMPLATE_PATHS += [
    os.path.join(INCLUDE_DIR, f)
    for f in os.listdir(INCLUDE_DIR) if f.endswith(".html")
]

# Bloques relevantes a conservar siempre
keywords_to_keep = [
    ":root", ".hidden", ".tooltip-icon", ".accordion-section", "@keyframes shake",
    ".shake", ".readonly-style", ".form-control[readonly]", ".invalid-feedback",
    ".swal-", ".modal-", ".btn_", ".back_btn", ".edit_btn", "#partnerTable",
    ".table-partners", ".select2-", ".choices__", ".form-group", "#deactivation_info",
    ".card-header-grey"
]

def extract_blocks(css_text):
    pattern = re.compile(r'(\/\*.*?\*/)?\s*([^{]+?)\s*\{([^}]+)\}', re.DOTALL)
    return [(m.group(2).strip(), m.group(3).strip()) for m in pattern.finditer(css_text)]

def block_matches_templates(selector, templates_content):
    for part in selector.split(','):
        part = part.strip()
        if not part:
            continue
        if part.startswith('.'):
            if any(part[1:] in c for c in templates_content):
                return True
        elif part.startswith('#'):
            if any(part[1:] in c for c in templates_content):
                return True
        elif any(part in c for c in templates_content):
            return True
    return False

def is_important_block(selector):
    return any(keyword in selector for keyword in keywords_to_keep)

def classify_block(selector):
    s = selector.lower()
    if ":root" in s:
        return "Variables globales"
    elif s.startswith("@keyframes"):
        return "Animaciones"
    elif any(k in s for k in ["modal", "swal"]):
        return "Modales y alertas"
    elif any(k in s for k in ["form", "input", "label", "fieldset", "select", "textarea"]):
        return "Formularios"
    elif any(k in s for k in ["btn", "button"]):
        return "Botones"
    elif any(k in s for k in ["table", "tr", "td", "th"]):
        return "Tablas"
    elif "card" in s:
        return "Tarjetas"
    elif any(k in s for k in ["row", "col"]):
        return "Grillas y columnas"
    elif any(k in s for k in ["nav", "tab"]):
        return "Navegación / Tabs"
    elif "icon" in s:
        return "Iconos"
    else:
        return "Otros"

def format_block(selector, body):
    lines = [line.strip() for line in body.splitlines() if line.strip()]
    corrected = []
    for line in lines:
        if not line.endswith(';') and not line.endswith('{') and not line.startswith('@'):
            corrected.append(line + ';')
        else:
            corrected.append(line)
    formatted_body = '\n  '.join(corrected)
    return f"{selector} {{\n  {formatted_body}\n}}\n"

# === CARGA CSS Y PLANTILLAS ===
with open(CSS_FILE, encoding="utf-8") as f:
    css_content = f.read()

template_contents = []
for path in TEMPLATE_PATHS:
    with open(path, encoding="utf-8") as f:
        template_contents.append(f.read())

# === PROCESAMIENTO ===
blocks = extract_blocks(css_content)
used_blocks = []
unused_blocks = []

for selector, body in blocks:
    if block_matches_templates(selector, template_contents) or is_important_block(selector):
        used_blocks.append((selector, body))
    else:
        unused_blocks.append((selector, body))

# === CLASIFICACIÓN Y FORMATEO ===
categories = defaultdict(list)
for selector, body in used_blocks:
    category = classify_block(selector)
    categories[category].append(format_block(selector, body))

# === ESCRITURA DE ARCHIVOS ===
with open(OUTPUT_FILE_USED, "w", encoding="utf-8") as f:
    f.write("/* CSS Generado automáticamente: estilos utilizados y esenciales */\n\n")
    for category in sorted(categories):
        f.write(f"/* === {category.upper()} === */\n\n")
        for block in categories[category]:
            f.write(block + "\n")
        f.write("\n")

with open(OUTPUT_FILE_UNUSED, "w", encoding="utf-8") as f:
    f.write("/* CSS no detectado como utilizado o crítico */\n\n")
    for selector, body in unused_blocks:
        f.write(format_block(selector, body) + "\n")

print("✅ CSS procesado sin errores de sintaxis.")
print("→ style_formater.css generado correctamente.")
print("→ style_restos.css generado con bloques no usados.")
