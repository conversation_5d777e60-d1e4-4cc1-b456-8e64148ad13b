{% extends "layouts/base.html" %}
{% load static %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/flag/flag-icons.min-v6.6.6.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/vuetify/vuetify.min-v3.1.5.css"/>
  
  <style scoped>

    #btnVerifactu:hover {
         background-color: #f8f9fa;
      }

    #show_more:focus,
    #show_more:active {
      background-color: #03ad65 !important;
      color: white !important;
      outline: none;
    }

    #show_more:not(.collapsed) {
      background-color: #03ad65 !important;
      color: white !important;
    }

    .pdf {
      width: 100%;
      height: 100vh;
      /* height: 1200px; */
    }

    .custom-icon-small {
      font-size: 12px; /* Tamaño más pequeño para el ícono */
    }

    #swal2-title{
      margin-bottom: 30px;
    }

    .hover-effect {
    transition: transform 0.3s ease; /* Transición suave */
  }

  .hover-effect:hover {
    transform: scale(1.1); /* Aumentar el tamaño al 120% */
  }

    .pdf.sticky {
      position: -webkit-sticky; /* Safari */
      position: sticky;
      top: 0;
    }

    .form-group {
      margin-bottom: 1rem !important;
    }

    .concept {
      background-color: #F4F7FA;
      border: 1px solid #E9ECEF;
      border-radius: 10px;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
    }

    .total {
      background-color: #F4F7FA;
      border: 1px solid #E9ECEF;
      border-radius: 10px;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
    }

    .totalKey {
      justify-content: flex-start;
      align-items: flex-start;
      text-align: left;
      padding-left: 4rem;
    }

    .totalValue {
      justify-content: flex-end;
      align-items: flex-end;
      text-align: right;
      padding-right: 4rem;
    }

    input {
      direction: ltr !important;
    }

    .modal-xl {
      max-width: 1500px;
    }

    .ir-arriba-abajo {
      padding: 10px;
      background: #024959;
      color: #fff;
      text-color: #fff;
      position: fixed;
      font-size: 20px;
      bottom: 20px;
      right: 20px;
      /* display: none; */
      cursor: pointer;
      border-radius: 0.25rem;
      z-index: 10;
    }

    .ir-arriba-abajo a {
      color: #fff;
    }

    .icono-contabilidad-espana {
      background-image: url("/static/assets/images/iconos/svg/Icono-contabilidad-españa.svg");
      width: 4em; 
      height: 4em;  
      display: inline-block;
      background-size: contain;
      background-repeat: no-repeat;
      vertical-align: middle; /* Opcional: para alinear verticalmente con el texto */
      margin-bottom: 10px;
      margin-left: 10px;
    }

    .icono-contabilidad-usa {
      background-image: url("/static/assets/images/iconos/svg/Icono-contabilidad-EEUU.svg");
      width: 4em;
      height: 4em;
      display: inline-block;
      background-size: contain;
      background-repeat: no-repeat;
      vertical-align: middle;
      margin-bottom: 10px;
      margin-left: 10px;
    }

    /* Espaciado entre iconos de contabilidad y banderas */
    .icon-block {
      display: inline-block;
      margin-right: 30px;
    }

    /* Espaciado entre banderas */
    .vat-block .vat-flag {
      margin-right: 10px;
    }

    /* Si la bandera no se muestra | No hay espacio*/
    .vat-block {
      display: inline-block;
    }
  </style>
{% endblock stylesheets %}

{% block title %}
  Facturas
{% endblock title %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas: Detalle Factura
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:summary' seller.shortname %}">
                    {% if seller.name is not None %}
                      {{ seller.name.capitalize }}
                    {% else %}
                      Resumen
                    {% endif %}
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Facturas</a>
                </li>
                <li class="breadcrumb-item">
                  <a href=".">Detalle Factura</a>
                </li>
              </ul>
            </div>
            <div class="col col-1" style="display: none;">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}uploadtxt">Subir
                    TXT Amazon</a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}upload">Subir
                    Facturas</a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'expenses' %}">
                    Facturas de Gasto
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'sales' %}">
                    Facturas de Venta
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                  <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">Todas las
                    Facturas</a> 
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div id="arrows" class="ir-arriba-abajo" style="display: none;">
    <a href='#' @click='irAbajo' id="irAbajoBoton">&nbsp;<span><i class="fa-solid fa-arrow-down"></i></span></a>
    &nbsp;
    <a href='#' @click='irArriba' id="irArribaBoton"><span><i class="fa-solid fa-arrow-up"></i></span>&nbsp;</a>
  </div>
  <div class="card row">
    <div id="card-header" class="card-header">
      <div class="row" style="display: none;" v-show="true">
        <div class="col-9 d-flex justify-content-start align-items-center text-left">
          <h3><b>Título:</b></h3> 
          &nbsp; &nbsp;
          <span v-show="editName != true">
            <h3> [[ inputInvoiceName ]] </h3>
          </span>
          <input
            type="text"
            class="form-control"
            v-model.trim="inputInvoiceName"
            @focusout="editName=false"
            @keyup.enter="editName=false"
            v-show="editName == true"
          />
          &nbsp;
          <btn
            class="btn btn-link text-green"
            style="border:0px"
            @click="editName=true"
            v-show="editName != true && inputInvoiceStatus != 'revised'"
          >
            <h3><span class="text-green"><i class="fa-solid fa-pen-to-square"></i></span></h3>
          </btn>
          <btn
            class="btn btn-link text-green"
            style="border:0px"
            @click="editName=false"
            v-show="editName == true"
          >
            <h3><span class="text-red"><i class="fa-solid fa-floppy-disk"></i></span></h3>
          </btn>
          <div 
            class="col text-right"
            v-show="dj.seller.legal_entity == 'self-employed' && dj.seller.percentage_affected_activity!=100">
            <h2 
              class=""
              style="color: #000000;"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              data-bs-original-title="Porcentaje de afectación de la actividad"
              :data-text="`${dj.seller.percentage_affected_activity}%`"
              color="black"
            >
              [[ dj.seller.percentage_affected_activity ]]%
            </h2>
          </div>
          <div 
            class="col text-right mt-5" 
            v-show="dj.seller.contracted_accounting || dj.seller_vat"
            style="display:none;">
            <div class="icon-block">
              <!-- Mostrar iconos de contabilidad | izquierda -->
              <template v-if="dj.seller.contracted_accounting_date || dj.seller.contracted_accounting_usa_date || dj.seller.contracted_accounting_usa_basic_date">
                <span 
                  v-if="shouldESCalculatorBeShown()"
                  class="copyableText icono-contabilidad-espana"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  data-bs-original-title="Contabilidad ES"
                  :data-text="dj.seller.nif_registration"
                  country="Contabilidad"
                ></span>
                <span 
                  v-if="dj.seller.contracted_accounting_usa_date || dj.seller.contracted_accounting_usa_basic_date"
                  class="copyableText icono-contabilidad-usa"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  data-bs-original-title="Contabilidad USA"
                  :data-text="dj.seller.nif_registration"
                  country="Contabilidad"
                ></span>
              </template>
            </div>
            <div class="vat-block">
              <span v-if="dj.vatFlagsContracted">
                <span v-for="sv in dj.vatFlagsContracted" :key="sv.pk">
                  <!-- Mostrar banderas solo si no hay contabilidad contratada para el país -->
                  <span 
                        {% comment %} v-if="!(sv.vat_country == 'ES' && dj.seller.contracted_accounting_date) && 
                                !(sv.vat_country == 'US' && dj.seller.contracted_accounting_usa_date)" {% endcomment %}
                        :class="'copyableText fs-1 fi fi-' + sv.vat_country.toLowerCase()"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        :data-bs-original-title="sv.vat_country"
                        :data-text="sv.vat_number"
                        :country="sv.vat_country"
                        class="vat-flag"
                  ></span>
                </span>
              </span>
            </div>
          </div>
        </div>
        <div class="col-3 d-flex justify-content-end align-items-center text-center">
          <div 
            class="w-100 badge badge-xl rounded-pill"
            :style="'background-color: ' + getStatusBg(dj.invoice.status) + ';'"
          >
            <h3><span id="invoiceStatus" class="badge badge-xl text-white">[[ getStatusByCode(dj.invoice.status) ]]</span></h3>
          </div>
        </div>
        <div class="col-12">
          <span>
            • Ultima Modificacion:
            [[ new Date(dj.invoice.modified_at).toLocaleDateString("es-ES") ]]
            [[ new Date(dj.invoice.modified_at).toLocaleTimeString() ]] 
            {% if last_modified %}
                {% include "invoices/include/historial_invoice.html" %}
            {% endif %}
          </span>
        </div>
        {% if invoice.is_generated is True %}
          <div class="col-12">
            <div class="d-flex justify-content-start align-items-center text-left" style="gap: 15px; padding-top: 15px;">
              <span style="margin:0; font-size: 14px;">• Estado en veriFactu:</span>
              <span v-if="veriFactuInstance && veriFactuInstance.operation_type == 'Alta'"> Alta</span>
              <span v-if ="veriFactuInstance && veriFactuInstance.operation_type == 'Anulacion'">Anulada</span>
              <span v-if ="veriFactuInstance == null"> No registrada</span>
            </div>
            <div class="d-flex justify-content-start align-items-center text-left" style="gap: 15px; padding-top: 15px; height: 60px;">
              <h6 style="margin:0; font-size: 14px;"><b>FACTURACIÓN ELECTRÓNICA:</b></h6> 
              <button 
                id="download_xml"
                class="btn"
                style="display: flex; height: 100%; padding: 5px 10px; margin:0;
                align-items: center; box-shadow: rgba(0, 0, 0, 0.25) 0px 0.0625em 0.0625em, rgba(0, 0, 0, 0.25) 0px 0.125em 0.5em, rgba(255, 255, 255, 0.1) 0px 0px 0px 1px inset;"
                onclick="generateInvoiceXML('{{seller.shortname}}', '{{invoice.pk}}')" 
                data-bs-toggle="tooltip" 
                data-bs-placement="top" 
                title="Descargar factura eléctronica">
                <img src="{% static 'assets/images/logos/png/logo-facturae.png' %}" alt="Descargar factura digital" style="height: 100%;"/>
              </button>
              <button v-show="['sl', 'self-employed'].includes(dj.seller.legal_entity) && inputTaxCountry == 'ES' && inputIsGeneratedAmz != true"
                type="button" 
                class="btn"
                id="btnVerifactu"  
                style="display: flex; height: 100%; align-items: center; margin:0; box-shadow: rgba(0, 0, 0, 0.25) 0px 0.0625em 0.0625em, rgba(0, 0, 0, 0.25) 0px 0.125em 0.5em, rgba(255, 255, 255, 0.1) 0px 0px 0px 1px inset;"
                data-bs-target="#modalVerifactuManualOptions"
                data-bs-toggle="modal"
                aria-label="Close">
                <img src="{% static 'assets/images/logos/png/logo_verifactu.png' %}" alt="Verifactu" style="height: 100%;" 
                data-bs-toggle="tooltip" 
                data-bs-placement="top" 
                title="Ver opciones VeriFactu"/>
              </button>
            </div>
            
          </div>
        {% endif %}
      </div>
    </div>
    <div class="card-body">
      {% if error %}
      <div class="col-12 alert alert-danger w-75 text-center mx-auto">
        {{ error |safe }}
      </div>
      {% endif %}
      <div class="row">
        
        <!-- PDF VIEWER  -->
        <div class="col-5">
          {% include "invoices/include/pdf_invoice.html" %}
        </div>
        <!-- PDF VIEWER  -->
        
        <!-- BODY -->
        <div class="col" id="formBody">
          {% if annotation_general.id %}
          <div class="col-12" v-if="inputInvoiceStatus == 'revision-pending' ">
            <div class="alert alert-info mx-0 pb-0" role="alert">
              <h5 class="alert-heading">¡Atención! Este vendedor tiene anotaciones generales</h5>
              <ul class="d-flex flex-column flex-md-row justify-content-md-start align-items-md-center">
                <li class="mb-2 text-justify text-md-left">
                  <b>{{ annotation_general.comment }}</b>
                </li>
              </ul>
            </div>
          </div>
          {% endif %}
          <div style="display: none;" v-show="true">

            <!-- Fieldset 1 -->
            <form id="form" method="post" action="." class="needs-validation">
              <input type="hidden" id="processing_time" name="processing_time" value="">
              <fieldset id="fieldset1">

                <!-- Invoice Category & Invoice Type -->
                <div class="row alert alert-warning" role="alert">
                  <div class="col-11">
                    <span v-if="inputInvoiceCategory">
                      <b>Categoría: </b> &nbsp; [[ getCategoryByCode(inputInvoiceCategory)?.description ]]
                      <span v-if="inputInvoiceType">
                        &nbsp; | &nbsp;
                        <b>Tipo: </b> &nbsp; [[ getTypeByCode(inputInvoiceType)?.description  ]]
                      </span>
                    </span>
                    <span v-if="!inputInvoiceCategory">
                      <b> Sin Categoría/Tipo Asignado </b>
                    </span>
                  </div>
                  <div class="col-1">
                    <a href="#" @click="showCategory=!showCategory">
                      <i class="fa-solid fa-xl fa-pen-to-square"></i>
                    </a>
                  </div>
                </div>
                <div class="row" v-show="!inputInvoiceCategory || !inputInvoiceType || showCategory == true">
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceCategory">
                      <b>Categoría de Factura * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputInvoiceCategory"
                      name="inputInvoiceCategory"
                      v-model="inputInvoiceCategory"
                      v-on:change="onChangeCategory"
                      required
                    >
                      <option :value="null" disabled selected>Seleccione la Categoría de la
                        Factura
                      </option>
                      <option 
                        :value="category.pk" 
                        v-for="category in dj.categories"
                        :key="category.pk"
                      >
                        [[ category.description ]]
                      </option>
                    </select>
                  </div>
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="invoice_type">
                      Tipo de Factura:
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputInvoiceType"
                      name="inputInvoiceType"
                      v-model="inputInvoiceType"
                      v-on:change="onChangeType"
                      v-on:focusout="onChangeType"
                      :disabled="!inputInvoiceCategory"
                      required
                    >
                      <option :value="null" disabled selected>Seleccione el Tipo de la Factura
                      </option>
                      <option 
                        :value="type.pk" 
                        v-for="type in getTypesForCategory()"
                        :key="type.pk">
                        [[ type.description ]]
                      </option>
                    </select>
                  </div>
                </div>
                <!-- Invoice Category & Invoice Type -->

                <!-- Invoice Relations -->
                {% if invoice_relations %}
                  {% for inv in invoice_relations %}
                    <div class="row alert alert-info">
                      <div class="col-2"><b>ID:</b> {{ inv.pk }}</div>
                      <div class="col"><b>Num. Factura:</b> {{ inv.reference }}</div>
                      <div class="col"><b>Tipo:</b> {{ inv.invoice_type }}</div>
                      <div class="col"><b>Estado:</b> {{ inv.status }}</div>
                      <div class="col-1">
                        <a href="{% url 'app_invoices:seller_invoice' seller.shortname inv.pk %}">
                          <i class="fa-solid fa-xl fa-arrow-right"></i>
                        </a>
                      </div>
                    </div>
                  {% endfor %}
                {% endif %}

                <!-- Form 1: Sales (Common) -->
                <div class="row" id="sales" v-if="inputInvoiceType== 'sales' && inputInvoiceStatus != 'discard'">

                  <!-- Is a AMZ invoice Generated ? -->
                  <div 
                    class="col-12 mx-0 alert alert-warning w-100 rounded" 
                    role="alert"
                    v-if="inputIsGeneratedAmz==true"
                  >
                    <div class="row mx-3">
                      <div class="col-auto m-0 p-0"><i class="fa-solid fa-xl fa-triangle-exclamation"
                        style=" vertical-align: bottom;"></i> &nbsp; &nbsp;
                      </div>
                      <div class="col">
                        <span class="text-start">
                          <b>Advertencia:</b> Esta factura es de una venta de Amazon (generada manualmente). <br>
                          <b>No se considerada para fines de facturación</b>, ya que su inclusión duplicaría los datos presentes en el informe de ventas de Amazon.
                        </span>
                      </div>
                      <div class="col-auto m-0 p-0">&nbsp; &nbsp; 
                        <i 
                          class="fa-solid fa-xl fa-triangle-exclamation"
                          style=" vertical-align: bottom;">
                        </i>
                      </div>
                    </div>
                  </div>

                  <!-- Customer -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="inputCustomer">
                          <b>Cliente * &nbsp; | &nbsp;</b>
                        </label>
                        <span>
                          <a @click="onclickTagA('setParticular')" href="#">Asignar "Cliente Particulares"</a>
                        </span>
                        <v-autocomplete
                          placeholder="Seleccione Cliente"
                          variant="solo"
                          item-title="longname2"
                          item-value="pk"
                          :items="dj.customers"
                          v-model="inputCustomer"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputCustomer"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Cliente</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputCustomer">
                            <a 
                              data-bs-toggle="modal" 
                              @click="onclickTagA('edit')"
                              data-bs-target="#newCostumer" 
                              href="#"
                            >Editar Cliente</a>
                            &nbsp; | &nbsp;
                          </span>
                          <span>
                            <a 
                              data-bs-toggle="modal" 
                              @click="onclickTagA('new')"
                              data-bs-target="#newCostumer" 
                              href="#">Crear Nuevo Cliente</a>
                            </br>
                          </span>
                        </div>
                        <div v-show="inputCustomer">
                          <span>
                            <b>[[ getCustomerById(inputCustomer)?.name ]]</b> <br/>
                            NIF: [[ getCustomerById(inputCustomer)?.nif_cif_iva ]] <br/>
                            VIES: [[ getCustomerById(inputCustomer)?.vies ]] <br/>
                            [[ getCustomerById(inputCustomer)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <div class="col-12" v-show="inputCustomer">                      
                      <div class="alert alert-danger" v-show="!getCustomerById(inputCustomer)?.nif_cif_iva && !getCustomerById(inputCustomer)?.is_individual_customer">
                        <span>
                          <b>Advertencia:</b> El cliente seleccionado no tiene un NIF. Edite el cliente o seleccione otro Cliente.
                        </span>
                      </div>
                    </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      v-if="inputIsDistanceSell != true"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      v-if="inputIsDistanceSell == true"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="country.pk" 
                        v-for="country in getEuropeanCountries()"
                        :key="country.pk"
                      >
                        [[ country.name ]] (OSS)
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-else
                      >
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de la Factura * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">

                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          @blur="show_accounting_date('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>
                      
                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Is Rectifying -->
                  <div class="col-6 form-group form-check">
                    <label 
                      class="form-label" 
                      for="inputTaxResponsibility"
                    >
                      <b>¿Es una factura rectificativa? * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsRectifying"
                      name="inputIsRectifying"
                      v-model="inputIsRectifying"
                      v-on:change="onChangeIsRectifying"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check">
                    <label 
                      class="form-label" 
                      for="inputCurrency"
                    >
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option 
                        v-for="cur in dj.currencies" 
                        :key="cur.pk" 
                        :value="cur.pk"
                      >[[ cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>

                  <!-- Is Equivalent Tax (eqtax) -->
                  <div class="col-6 form-group form-check eqtax">
                    <label class="form-label" for="is_eqtax">
                      ¿Tiene Recargo de Equivalencia?
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsEquivalentTax"
                      name="inputIsEquivalentTax"
                      v-model="inputIsEquivalentTax"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>

                  <!-- Is Distance Sell (OSS) -->
                  <div class="col-6 form-group form-check" >
                    <label 
                      class="form-label" 
                      for="inputIsDistanceSell"
                    >
                      <b v-if="dj.seller.oss == true" > ¿Es una venta a distancia? * </b>
                      <b v-else > ¿Es una venta a distancia?  </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsDistanceSell"
                      name="inputIsDistanceSell"
                      v-model="inputIsDistanceSell"
                      :required="inputIsDistanceSell==true"
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>

                  <!-- Departure Country -->
                  <div class="col-6 form-group form-check" v-if="dj.seller.oss == true && inputIsDistanceSell==true">
                    <label class="form-label" for="inputDepartureCountry">País de salida:</label>
                    <select
                      class="form-select form-control"
                      id="inputDepartureCountry"
                      name="inputDepartureCountry"
                      v-model="inputDepartureCountry"
                      :required="inputIsDistanceSell==true"
                    >
                      <option selected disabled> Seleccione el pais</option>
                      <option 
                        :value="country.pk" 
                        v-for="country in dj.countries"
                        :key="country.pk"
                      >[[ country?.name ]]</option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País de salida.</div>
                  </div>
                  
                  <!-- Tax Responsibility -->
                  <div class="col-6 form-group form-check">
                    <label 
                      class="form-label" 
                      for="inputTaxResponsibility"
                    >
                      <b>Responsable de las Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxResponsibility"
                      name="inputTaxResponsibility"
                      v-model="inputTaxResponsibility"
                      required
                    >
                      <option selected disabled>Seleccione el responsable</option>
                      <option 
                        :value="res.pk" 
                        v-for="res in dj.tax_responsability" 
                        :key="res.pk"
                      >
                        [[ res.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione quien es el responsable de pagar las tasas.</div>
                  </div>

                  <!-- Operación con inversión del sujeto pasivo -->
                  <div class="col-6 form-group form-check">
                    <label 
                      class="form-label" 
                      for="inputIsReverseCharge"
                    >
                      <b> ¿Es una operación con inversión del sujeto pasivo? * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsReverseCharge"
                      name="inputIsReverseCharge"
                      v-model="inputIsReverseCharge"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true" :disabled="getTotal().vatCurrency != 0">Si</option>
                    </select>
                  </div>

                </div>
                <!-- Form 1: Sales (Common) -->

                <!-- Form 2: Expenses (Common) -->
                <div 
                  class="row" 
                  id="expenses"
                  v-if="inputInvoiceType == 'expenses' && inputInvoiceStatus != 'discard'"
                >
                  <!-- Provider Name -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputProvider">
                            <a 
                              data-bs-toggle="modal" 
                              @click="onclickTagA('editProv')"
                              data-bs-target="#newCostumer"
                              href="#"
                            >
                              Editar Proveedor
                            </a>
                            &nbsp; | &nbsp;
                          </span>
                          <span>
                            <a 
                              data-bs-toggle="modal" 
                              @click="onclickTagA('newProv')"
                              data-bs-target="#newCostumer"
                              href="#"
                            >
                              Crear Nuevo Proveedor
                            </a>
                            </br>
                          </span>
                        </div>
                        <div v-show="inputProvider">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br/>
                            VIES: [[ getProviderById(inputProvider)?.vies ]] <br/>
                            [[ getProviderById(inputProvider)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de la Factura * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.
                    </div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">
                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>
                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Is Rectifying -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxResponsibility">
                      <b>¿Es una factura rectificativa? * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsRectifying"
                      name="inputIsRectifying"
                      v-model="inputIsRectifying"
                      v-on:change="onChangeIsRectifying"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[
                        cur.description ]] ([[ cur.pk ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>

                  <!-- Is Equivalent Tax (eqtax) -->
                  <div class="col-6 form-group form-check eqtax" v-if="{{ is_eqtax }}">
                    <label class="form-label" for="is_eqtax">
                      ¿Tiene Recargo de Equivalencia?
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsEquivalentTax"
                      name="inputIsEquivalentTax"
                      v-model="inputIsEquivalentTax"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>
                  
                  <!-- Operación con inversión del sujeto pasivo -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputIsReverseCharge">
                      <b> ¿Es operación con inversión del sujeto pasivo? * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsReverseCharge"
                      name="inputIsReverseCharge"
                      v-model="inputIsReverseCharge"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true" :disabled="getTotal().vatCurrency != 0">Si</option>
                    </select>
                  </div>
                </div>
                <!-- Form 2: Expenses (Common) -->

                <!-- Form 3: Tickets -->
                <div class="row" id="expenses"
                     v-if="inputInvoiceType == 'ticket' && inputInvoiceStatus != 'discard'">
                  <!-- Provider -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputProvider">
                            <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#">Editar Proveedor</a>
                            &nbsp; | &nbsp;
                          </span>
                          <span>
                            <a data-bs-toggle="modal" @click="onclickTagA('newProv')" data-bs-target="#newCostumer" href="#">Crear Nuevo Proveedor</a><br>
                          </span>
                        </div>
                        <div v-show="inputProvider">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br/>
                            VIES: [[ getProviderById(inputProvider)?.vies ]] <br/>
                            [[ getProviderById(inputProvider)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date">
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label class="btn btn-outline-warning" for="inputOutOfTime" v-if="inputOutOfTime">Factura <b>no emitida</b> en este periodo fiscal</label>
                      <label class="btn btn-outline-warning" for="inputOutOfTime" v-else>Factura <b>emitida</b> en este periodo fiscal</label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número del Ticket * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">
                      <!-- Invoice Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          @blur="show_accounting_date('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                      </div>
                    </div>
                  </div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check ml-auto">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>
                </div>

                <!-- Form 3: Tickets -->

                <!-- Form 4: Import Invoice -->
                <div 
                  class="row" 
                  id="import-invoice"
                  v-if="inputInvoiceType == 'import-invoice' && inputInvoiceStatus != 'discard'"
                >
                  <!-- Provider Name -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputProvider">
                            <a data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer" href="#">Editar Proveedor</a>
                            &nbsp; | &nbsp;
                          </span>
                          <span>
                            <a data-bs-toggle="modal" @click="onclickTagA('newProv')" data-bs-target="#newCostumer" href="#">Crear Nuevo Proveedor</a><br>
                          </span>
                        </div>
                        <div v-show="inputProvider">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br/>
                            VIES: [[ getProviderById(inputProvider)?.vies ]] <br/>
                            [[ getProviderById(inputProvider)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de la Factura * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">

                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          @blur="show_accounting_date('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Is Rectifying -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxResponsibility">
                      <b>¿Es una factura rectificativa? * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsRectifying"
                      name="inputIsRectifying"
                      v-model="inputIsRectifying"
                      v-on:change="onChangeIsRectifying"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>
                </div>

                <!-- Form 4: Import Invoice -->

                <!-- Form 5: Import DUA -->
                <div 
                  class="row" 
                  id="import-dua"
                  v-if="inputInvoiceType == 'import-dua' && inputInvoiceStatus != 'discard'"
                >
                  <!-- Provider Name -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div 
                        class="col-6 form-group form-check"
                        v-if="dj.seller.contracted_accounting"
                      >
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div 
                        class="col-6 form-group form-check"
                        v-if="!dj.seller.contracted_accounting"
                      >
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputProvider">
                            <a 
                              data-bs-toggle="modal" 
                              @click="onclickTagA('editProv')"
                              data-bs-target="#newCostumer"
                              href="#"
                            >
                              Editar Proveedor
                            </a>
                          </span>
                        </div>
                        <div v-show="inputProvider">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br/>
                            VIES: [[ getProviderById(inputProvider)?.vies ]] <br/>
                            [[ getProviderById(inputProvider)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <div class="row">
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputTaxCountry">
                          <b>País Tasas * </b>
                        </label>
                        <select
                          class="form-select form-control"
                          id="inputTaxCountry"
                          name="inputTaxCountry"
                          v-model="inputTaxCountry"
                          required
                        >
                          <option selected disabled>Seleccione el país</option>
                          <option 
                            :value="vat.vat_country" 
                            v-for="vat in dj.seller_vat" 
                            :key="vat.pk"
                            :data-vat-number="vat.vat_number"
                            :data-is-valid="vat.is_valid"
                            :data-activation-date="vat.activation_date"
                            :data-deactivation-date="vat.deactivation_date"
                          >
                            [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                          </option>
                        </select>
                        <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                      </div>

                      <!-- Is Postponed VAT (is_postponed_import_vat) -->
                      <div class="col form-group form-check" v-if="inputTaxCountry == 'GB'">
                        <label class="form-label" for="inputIsPostponedVat">
                          ¿Es un IVA Postpuesto?
                        </label>
                        <select
                          class="form-select form-control"
                          id="inputIsPostponedVat"
                          name="inputIsPostponedVat"
                          v-model="inputIsPostponedVat"
                          required
                        >
                          <option selected disabled>Seleccione el valor</option>
                          <option :value="false" selected>No</option>
                          <option :value="true">Si</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-else
                      >
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número del DUA * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>

                  <!-- Dates -->
                  <div class="col-6 form-group form-check">
                    <div class="row">
                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          @blur="show_accounting_date('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                      </div>
                    </div>
                  </div>

                  <!-- Is Equivalent Tax (eqtax) -->
                  <div class="col-6 form-group form-check eqtax" v-if="{{ is_eqtax }}">
                    <label class="form-label" for="is_eqtax">
                      ¿Tiene Recargo de Equivalencia?
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsEquivalentTax"
                      name="inputIsEquivalentTax"
                      v-model="inputIsEquivalentTax"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>
                  <div class="col-6 form-group form-check" v-else></div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>

                </div>
                <!-- Form 5: Import DUA -->

                <!-- Form 6: Import Expenses -->
                <div 
                  class="row" 
                  id="import-expenses"
                  v-if="inputInvoiceType == 'import-expenses' && inputInvoiceStatus != 'discard'">
                  <!-- Provider Name -->
                  <div class="col-12" >
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check" v-if="dj.seller.contracted_accounting">
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6 form-group form-check" v-if="!dj.seller.contracted_accounting">
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputProvider">
                            <a href="#" data-bs-toggle="modal" @click="onclickTagA('editProv')" data-bs-target="#newCostumer">Editar Proveedor</a>
                          </span>
                        </div>
                        <div v-show="inputProvider">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br/>
                            VIES: [[ getProviderById(inputProvider)?.vies ]] <br/>
                            [[ getProviderById(inputProvider)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que perteneceesta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-else
                      >
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de la Factura * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">

                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          @blur="show_accounting_date('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Currency -->
                  <div class="col-6 form-group form-check ml-auto">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>
                </div>
                <!-- Form 6: Import Expenses -->

                <!-- Form 7: Self-employment fee -->
                <div 
                  class="row" 
                  id="expenses"
                  v-if="inputInvoiceType == 'self-employment-fee' && inputInvoiceStatus != 'discard'"
                >
                  <!-- Provider -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="provider">
                          <b>Proveedor * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Proveedor"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.providers.filter(p => p.nif_cif_iva === 'tgss')"
                          v-model="inputProvider"
                          v-on:focusin="onChangeType"
                          :required="!inputProvider"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Proveedor.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <br>
                        </div>
                        <div v-show="inputProvider">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            NIF: [[ getProviderById(inputProvider)?.nif_cif_iva ]] <br/>
                            VIES: [[ getProviderById(inputProvider)?.vies ]] <br/>
                            [[ getProviderById(inputProvider)?.zip ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-else
                      >
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumberFee">
                      <b>Número de Recibo Cuota Seg.Social * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumberFee"
                      name="inputInvoiceNumberFee"
                      v-model="inputInvoiceNumber"
                      readonly
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>
                  <div class="col-6 form-group form-check">
                    <div class="row">
                      <!-- Invoice Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputMonths">
                          <b>Mes * </b>
                        </label>
                        <select
                          class="form-select form-control"
                          id="inputMonths"
                          name="inputMonths"
                          v-model="inputMonths"
                          v-on:change="genericTicketNumberFee"
                          required
                        >
                          <option disabled selected>Seleccione el mes</option>
                          <option :value="m.value" v-for="m in months" :key="m.value">[[ m.month ]]</option>
                        </select>
                        <div class="invalid-feedback">Seleccione el mes.</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputYears">
                          <b>Año * </b>
                        </label>
                        <select
                          class="form-select form-control"
                          id="inputYears"
                          name="inputYears"
                          v-model="inputYears"
                          v-on:change="genericTicketNumberFee"
                          required
                        >
                          <option disabled>Seleccione el año</option>
                          <option :value="y.value" v-for="y in years" :key="y.value">[[ y.year ]]</option>
                        </select>
                        <div class="invalid-feedback">Seleccione el año.</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                      </div>
                    </div>
                  </div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check ml-auto">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>

                  <div v-show="dj.seller.legal_entity == 'sl'" class="col-6 form-group form-check">
                    <label class="form-label" for="selfEmployedNameFee">
                      <b>Autónomo * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="selfEmployedNameFee"
                      name="selfEmployedNameFee"
                      v-model="inputNameSelfEmployed"
                      v-on:input="genericTicketNumberFee"
                      :required="dj.seller.legal_entity == 'sl'"
                    />
                    <div class="invalid-feedback">Introduzca el nombre del autónomo.</div>
                  </div>
                </div>

                <!-- Form 8: Rent -->
                <div 
                  class="row" 
                  id="rents"
                  v-if="inputInvoiceType == 'rent' && inputInvoiceStatus != 'discard'">
                  <!-- Provider Name -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="rent">
                          <b>Alquileres * </b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Alquiler"
                          variant="solo"
                          item-title="longname"
                          item-value="pk"
                          :items="dj.rent"
                          v-model="inputRent"
                          v-on:focusin="onChangeType"
                          :disabled="inputInvoiceStatus == 'revised'"
                          :required="!inputRent"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Alquiler.</div>
                      </div>
                      <div class="col-6">
                        <div>

                        </div>
                        <div v-show="inputRent">
                          <span>
                            <b>[[ getProviderById(inputProvider)?.name ]]</b> <br/>
                            Calle: [[ getRentByID(inputRent)?.type_road ]] [[ getRentByID(inputRent)?.name_road ]] [[ getRentByID(inputRent)?.portal ]]<br/>
                            Codigo Postal: [[ getRentByID(inputRent)?.postal_code ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-else
                      >
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de la Factura * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la factura.</div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">
                      <!-- Invoice Expedition Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputInvoiceDate">
                          <b>Fecha de la Factura * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceDate"
                          name="inputInvoiceDate"
                          v-model="inputInvoiceDate"
                          @change="handleInputChange(inputInvoiceDate)"
                          @blur="show_accounting_date('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">[[ invoiceInvalidFeedback ]]</div>
                      </div>
                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                      </div>
                    </div>
                  </div>

                  <!-- Is Rectifying -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxResponsibility">
                      <b>¿Es una factura rectificativa? * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputIsRectifying"
                      name="inputIsRectifying"
                      v-model="inputIsRectifying"
                      v-on:change="onChangeIsRectifying"
                      required
                    >
                      <option selected disabled>Seleccione el valor</option>
                      <option :value="false" selected>No</option>
                      <option :value="true">Si</option>
                    </select>
                  </div>

                  <!-- Currency -->
                  <div class="col-6 form-group form-check ml-auto">
                    <label class="form-label" for="inputCurrency">
                      <b>Moneda * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputCurrency"
                      name="inputCurrency"
                      v-model="inputCurrency"
                      required
                    >
                      <option selected disabled>Seleccione la moneda</option>
                      <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                    </select>
                    <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                  </div>
                </div>

                <!-- Form 9: Payroll -->
                <div 
                  class="row" 
                  id="payrolls"
                  v-if="inputInvoiceType == 'payroll' && inputInvoiceStatus != 'discard'">

                  <!-- Worker -->
                  <div class="col-12">
                    <div class="row">
                      <hr class="mt-0 mb-2"/>
                      <div class="col-6 form-group form-check">
                        <label class="form-label" for="idWorker">
                          <b>Trabajador *</b>
                        </label>
                        <v-autocomplete
                          placeholder="Seleccione Trabajador"
                          variant="solo"
                          item-title="full_name"
                          item-value="pk"
                          :items="dj.workers"
                          v-model="inputWorker"
                          v-on:focusin="onChangeType"
                          :required="!inputWorker"
                        ></v-autocomplete>
                        <div class="invalid-feedback">Seleccione el Trabajador.</div>
                      </div>
                      <div class="col-6">
                        <div>
                          <span v-show="inputWorker">
                            <a href="#" data-bs-toggle="modal" @click="onclickTagA('editWork')" data-bs-target="#newCostumer">Editar Trabajador</a>
                            &nbsp; | &nbsp;
                          </span>
                          <span>
                            <a href="#" data-bs-toggle="modal" @click="onclickTagA('newWork')"data-bs-target="#newCostumer">Crear Nuevo Trabajador</a><br>
                          </span>
                        </div>
                        <div v-show="inputWorker">
                          <span>
                            <b>[[ getWorkerById(inputWorker)?.full_name ]]</b> <br/>
                            NIF: [[ getWorkerById(inputWorker)?.nif_nie ]] <br/>
                            [[ getWorkerById(inputWorker)?.worker_type_name ]] <br/>
                          </span>
                        </div>
                      </div>
                      <hr class="mt-0"/>
                    </div>
                  </div>

                  <!-- Tax Country (VAT) -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputTaxCountry">
                      <b>País Tasas * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputTaxCountry"
                      name="inputTaxCountry"
                      v-model="inputTaxCountry"
                      required
                    >
                      <option selected disabled>Seleccione el país</option>
                      <option 
                        :value="vat.vat_country" 
                        v-for="vat in dj.seller_vat" 
                        :key="vat.pk"
                        :data-vat-number="vat.vat_number"
                        :data-is-valid="vat.is_valid"
                        :data-activation-date="vat.activation_date"
                        :data-deactivation-date="vat.deactivation_date"
                      >
                        [[ getCountryNameByCode(vat.vat_country) ]] ([[ vat.vat_number ]])
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el País IVA contratado al que pertenece esta factura.</div>
                  </div>

                  <!-- Out of Time -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label">&nbsp;</label>
                    <div class="btn-group w-100" role="group">
                      <input
                        type="checkbox"
                        class="btn-check"
                        id="inputOutOfTime"
                        name="inputOutOfTime"
                        v-model="inputOutOfTime"
                        autocomplete="off"
                        disabled:="inputInvoiceStatus == 'revised'"
                      />
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-if="inputOutOfTime"
                      >
                        Factura <b>no emitida</b> en este periodo fiscal
                      </label>
                      <label 
                        class="btn btn-outline-warning" 
                        for="inputOutOfTime" 
                        v-else
                      >
                        Factura <b>emitida</b> en este periodo fiscal
                      </label>
                    </div>
                  </div>

                  <!-- Invoice Number/Reference -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputInvoiceNumber">
                      <b>Número de Nómina * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputInvoiceNumber"
                      name="inputInvoiceNumber"
                      v-model="inputInvoiceNumber"
                      readonly
                      required
                    />
                    <div class="invalid-feedback">Introduzca el número/referencia de la nómina.</div>
                  </div>

                  <div class="col-6 form-group form-check">
                    <div class="row">

                      <!-- Invoice Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputMonths">
                          <b>Mes * </b>
                        </label>
                        <select
                          class="form-select form-control"
                          id="inputMonths"
                          name="inputMonths"
                          v-model="inputMonths"
                          v-on:change="setDatePayroll"
                          required
                        >
                          <option disabled selected>Seleccione el mes</option>
                          <option :value="m.value" v-for="m in months" :key="m.value">[[ m.month ]]</option>
                        </select>
                        <div class="invalid-feedback">Seleccione el mes.</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check">
                        <label class="form-label" for="inputYears">
                          <b>Año * </b>
                        </label>
                        <select
                          class="form-select form-control"
                          id="inputYears"
                          name="inputYears"
                          v-model="inputYears"
                          v-on:change="setDatePayroll"
                          required
                        >
                          <option disabled>Seleccione el año</option>
                          <option :value="y.value" v-for="y in years" :key="y.value">[[ y.year ]]</option>
                        </select>
                        <div class="invalid-feedback">Seleccione el año.</div>
                      </div>

                      <!-- Invoice Accounting Date -->
                      <div class="col form-group form-check" v-if="inputOutOfTime">
                        <label class="form-label" for="inputInvoiceAccountingDate">
                          <b>Fecha de Contabilización * </b>
                        </label>
                        <input
                          type="date"
                          class="form-control"
                          id="inputInvoiceAccountingDate"
                          name="inputInvoiceAccountingDate"
                          v-model="inputInvoiceAccountingDate"
                          min="{{ end_date }}"
                          @blur="final_date_period('{{ end_date }}')"
                          required
                        />
                        <div class="invalid-feedback">Introduzca la fecha de contaibilización.</div>
                      </div>
                    </div>
                  </div>

                  <div class="d-flex justify-content-end" style="padding: 0px">
                    <!-- Type payroll -->
                    <div 
                      class="col-6 form-group form-check" 
                      style="padding-right: 12px;"
                      v-if="inputWorkerType == true">
                      <label class="form-label" for="inputTypePayroll">
                        <b>Tipo de Nómina * </b>
                      </label>
                      <select
                        class="form-select form-control"
                        id="inputTypePayroll"
                        name="inputTypePayroll"
                        v-model="inputTypePayroll"
                        required
                      >
                        <option selected disabled>Seleccione el tipo de nómina</option>
                        {% for e in type_payroll %}
                          <option value="{{ e.0 }}">{{ e.1 }}</option>
                        {% endfor %}
                      </select>
                      <div class="invalid-feedback">Seleccione el tipo de nómina al que pertenece esta factura.</div>
                    </div>

                    <!-- Currency -->
                    <div class="col-6 form-group form-check" style="padding-right: 12px;">
                      <label class="form-label" for="inputCurrency">
                        <b>Moneda * </b>
                      </label>
                      <select
                        class="form-select form-control"
                        id="inputCurrency"
                        name="inputCurrency"
                        v-model="inputCurrency"
                        disabled
                        required
                      >
                        <option selected disabled>Seleccione la moneda</option>
                        <option v-for="cur in dj.currencies" :key="cur.pk" :value="cur.pk">[[ cur.description ]] ([[ cur.pk ]])</option>
                      </select>
                      <div class="invalid-feedback">Seleccione la divisa/moneda de la factura.</div>
                    </div>
                  </div>

                  <!-- Salario Base -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputSalaryBase">
                      <b>Salario base * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputSalaryBase"
                      name="inputSalaryBase"
                      v-model="inputSalaryBase"
                      pattern="[0-9.]+"
                      min="0"
                      required
                    />
                    {% comment %} <div class="invalid-feedback">Introduzca un total devengado correcto.</div> {% endcomment %}
                  </div>

                  <!-- Salario en especie -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputSalaryInKind">
                      <b>Salario en especie * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputSalaryInKind"
                      name="inputSalaryInKind"
                      v-model="inputSalaryInKind"
                      pattern="[0-9.]+"
                      min="0"
                      required
                    />
                    {% comment %} <div class="invalid-feedback">Introduzca un total devengado correcto.</div> {% endcomment %}
                  </div>

                  <!-- Total Accrued -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="inputTotalAccrued">
                      <b>Total devengado * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputTotalAccrued"
                      name="inputTotalAccrued"
                      v-model="inputTotalAccrued"
                      pattern="[0-9.]+"
                      min="0"
                      required
                    />
                    <div class="invalid-feedback">Introduzca un total devengado correcto.</div>
                  </div>

                  <div class="col-12 form-group form-check" v-if="inputWorkerType == true">
                    <label class="form-label" for="inputWorkerSocialSecurity">
                      <b>Cotización Seguridad Social del trabajador * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputWorkerSocialSecurity"
                      name="inputWorkerSocialSecurity"
                      v-model="inputWorkerSocialSecurity"
                      pattern="[0-9.]+"
                      min="0"
                      required
                    />
                    <div class="invalid-feedback">Introduzca un valor de la seguridad social deltrabajador correcto.</div>
                  </div>

                  <!-- Percentage irpf -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputPercentageIRPF">
                      <b>Porcentaje % * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputPercentageIRPF"
                      name="inputPercentageIRPF"
                      v-model="inputPercentageIRPF"
                      pattern="[0-9.]+"
                      min="0"
                      max="100"
                      required
                    />
                    <div class="invalid-feedback">Introduzca un porcentaje correcto.</div>
                  </div>

                  <!-- Income IRPF -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="inputIncomeIRPF">
                      <b>Retención IRPF * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputIncomeIRPF"
                      name="inputIncomeIRPF"
                      v-model="inputIncomeIRPF"
                      min="0"
                      readonly
                      required
                    />
                  </div>

                  <!-- Net pay -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="inputNetPay">
                      <b>Líquido a percibir * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputNetPay"
                      name="inputNetPay"
                      v-model="inputNetPay"
                      min="0"
                      readonly
                      required
                    />
                  </div>

                  <div class="col-12 form-group form-check" v-if="inputWorkerType == true">
                    <label class="form-label" for="inputCompanySocialSecurity">
                      <b>Cotización Seguridad Social de la empresa * </b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputCompanySocialSecurity"
                      name="inputCompanySocialSecurity"
                      v-model="inputCompanySocialSecurity"
                      pattern="[0-9.]+"
                      min="0"
                      required
                    />
                    <div class="invalid-feedback">Introduzca un valor de la seguridad social de la empresa correcto.</div>
                  </div>

                  <!-- Total -->
                  <div class="d-flex justify-content-end">
                    <div class="col-6 form-group form-check">
                      <label class="form-label" for="inputTotalPayroll">
                        <b>Coste de empresa * </b>
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        id="inputTotalPayroll"
                        name="inputTotalPayroll"
                        v-model="inputTotalPayroll"
                        min="0"
                        readonly
                        required
                      />
                    </div>
                  </div>
                  <div class="d-flex justify-content-end">
                    <div class="col-6 form-group form-check">
                      <label class="form-label" for="inputTotalTotalPayroll">
                        <b>Total * </b>
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        id="inputTotalTotalPayroll"
                        name="inputTotalTotalPayroll"
                        v-model="inputTotalTotalPayroll"
                        min="0"
                        readonly
                        required
                      />
                    </div>
                  </div>
                </div>
                <!-- Form 9: Payroll -->
              </fieldset>
              <!-- Fieldset 1 -->

              <!-- Concepts  -->
              <div class="accordion" id="accordionExample" v-if="inputInvoiceType != 'payroll'">
                <div
                  class="accordion-item concept row align-items-center mt-2 mb-2"
                  v-if="inputInvoiceType && inputInvoiceStatus != 'discard'"
                  v-for="(concept,i) in inputConcepts"
                  :key="i"
                  :id="'concept' + i"
                >
                  <h2 class="accordion-header" :id="'heading'+i">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                      :data-bs-target="'#collapse'+i" aria-expanded="false"
                      :aria-controls="'collapse'+i">
                      <span v-if="!concept.is_supplied">Concepto [[i+1]]</span>
                      <span v-else>Concepto [[i+1]] (Suplido)</span>
                    </button>
                  </h2>
                  <div 
                    :id="'collapse'+i" 
                    class="accordion-collapse collapse"
                    :aria-labelledby="'heading'+i" 
                    data-bs-parent="#accordionExample"
                  >
                    <!-- Fieldset 2 -->
                    <fieldset id="fieldset2">
                      <!-- Fieldset 2 (Concept Standard)-->
                      <fieldset v-if="!concept.is_supplied">
                        <div class="row">
                          <div class="col">
                            <div class="row">

                              <!-- Concepto -->
                              <div class="col-12 mt-0">
                                <div class="headers row">
                                  <div class="col-12 ml-0 mr-0">
                                    <label class="control-label" for="items__concept">
                                      <b>Concepto *</b>
                                    </label>
                                  </div>
                                </div>

                                <div class="fields row">
                                  <div class="col-12 ml-0 mr-0">
                                    <input
                                      type="text"
                                      class="form-control"
                                      :id="'inputConceptName' + i"
                                      v-model.trim="concept.concept"
                                      @focusin="conceptDefaultValues(concept)"
                                      @focusout="conceptDefaultValues(concept)"
                                      required
                                    />
                                    <div class="invalid-feedback">Introduzca el nombre del concepto.</div>
                                  </div>
                                </div>
                              </div>

                              <!-- Porcentaje y Cantidad -->
                              <div class="col-2 mt-2">
                                <div class="row">
                                  <div class="col-12">
                                    <label class="control-label">
                                      Porcentaje
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptPercentage' + i"
                                        min="0"
                                        max="100"
                                        pattern="[0-9.]+"
                                        v-model.trim="concept.percentage"
                                        @focusin="conceptDefaultValues(concept)"
                                        @focusout="conceptDefaultValues(concept)"
                                        required
                                      />
                                      <span class="input-group-text">%</span>
                                    </div>
                                    <div class="invalid-feedback">Introduzca el porcentaje del concepto.</div>
                                  </div>
                                  <div class="col-12 mt-2">
                                    <label class="control-label">
                                      <b>Cantidad *</b>
                                    </label>
                                    <input
                                      type="text"
                                      class="form-control"
                                      :id="'inputConceptQuantity' + i"
                                      v-model.trim="concept.quantity"
                                      min="1"
                                      pattern="[1-9][0-9]*"
                                      @focusin="conceptDefaultValues(concept)"
                                      @focusout="conceptDefaultValues(concept)"
                                      required
                                    />
                                  </div>
                                </div>
                              </div>

                              <div class="col-10 mt-2">
                                <!-- Fila 1 | IVA, Recargo y IRPF -->
                                <div class="fields-currency">
                                  <div 
                                    class="row"
                                    v-if="!['ticket','self-employment-fee','import-dua','import-expenses','import-invoice'].includes(inputInvoiceType)">
                                    <div class='col-4'>
                                      <label class="control-label">IVA</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptVat' + i"
                                          v-model.trim="concept.vat"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                    <div class='col-4'>
                                      <label class="control-label">IRPF</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptIrpf' + i"
                                          v-model.trim="concept.irpf"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                    <div class="col-4" v-if="inputIsEquivalentTax">
                                      <label class="control-label">Req.EQ</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptReqEq' + i"
                                          v-model.trim="concept.eqtax"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                  </div>


                                  <div class="row" v-if="inputInvoiceType == 'import-dua'">
                                    <div class='col-4'>
                                      <label class="control-label">IVA</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptVat' + i"
                                          v-model.trim="concept.vat"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>

                                    <div class="col-4" v-if="inputIsEquivalentTax">
                                      <label class="control-label">Req.EQ</label>
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptReqEq' + i"
                                          v-model.trim="concept.eqtax"
                                          min="0"
                                          pattern="-?\d+(\.\d*)?"
                                          @focusin="conceptDefaultValues(concept)"
                                          @focusout="conceptDefaultValues(concept)"
                                          required
                                        />
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>

                                  </div>
                                </div>


                                <!-- Fila 2 | Base Original, Base y Total -->
                                <div class="row mt-2">
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Original*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountOriginal' + i"
                                        v-model.trim="concept.amount_original"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Calculada</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountCurrency' + i"
                                        v-model.trim="concept.amount_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                        readonly
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Total *</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptTotalCurrency' + i"
                                        v-model.trim="concept.total_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                </div>
                                <!-- Fila 3 | conversiones a Euros (solo si la moneda no es EUR) -->
                                <div class="col-12" v-if="(concept.currency && concept.currency != 'EUR') || (inputCurrency && inputCurrency != 'EUR')">
                                  <div class="row">
                                    <div class="col-4">
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptAmountOriginalEuros' + i"
                                          v-model="concept.amount_original_euros"
                                          readonly
                                          required
                                        />
                                        <span class="input-group-text">EUR</span>
                                      </div>
                                    </div>
                                    <div class="col-4">
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptAmountEuros' + i"
                                          v-model="concept.amount_euros"
                                          readonly
                                          required
                                        />
                                        <span class="input-group-text">EUR</span>
                                      </div>
                                    </div>
                                    <div class="col-4">
                                      <div class="input-group">
                                        <input
                                          type="text"
                                          class="form-control"
                                          :id="'inputConceptTotalEuros' + i"
                                          v-model="concept.total_euros"
                                          readonly
                                          required
                                        />
                                        <span class="input-group-text">EUR</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                            </div>
                          </div>
                          <!-- Botón de eliminar -->
                          <div class="col-1 d-flex justify-content-center align-items-center text-center">
                            <btn type="button" class="btn btn-link" @click="removeConcept(i)">
                              <h3><i class="fa-solid fa-xl fa-xmark text-danger"></i></h3>
                            </btn>
                          </div>
                        </div>
                      </fieldset>
                      <!-- Fieldset 2 (Concept Standard)-->

                      <!-- Fieldset 2 (Concept supplied)-->
                      <fieldset v-if="concept.is_supplied">
                        <div class="row">
                          <div class="col">
                            <div class="row">
                              <!-- Concepto -->
                              <div class="col-12 mt-0">
                                <div class="headers row">
                                  <div class="col-10 ml-0 mr-0">
                                    <label class="control-label" for="items__concept">
                                      <b>Concepto *</b>
                                    </label>
                                  </div>
                                  <div class="col-2 ml-0 mr-0">
                                    <label class="control-label" for="items__concept">
                                      Porcentaje
                                    </label>
                                  </div>
                                </div>
                                <div class="fields row">
                                  <div class="col-10 ml-0 mr-0">
                                    <input
                                      type="text"
                                      class="form-control"
                                      :id="'inputConceptName' + i"
                                      v-model.trim="concept.concept"
                                      @focusin="conceptDefaultValues(concept)"
                                      @focusout="conceptDefaultValues(concept)"
                                      required
                                    />
                                    <div class="invalid-feedback">
                                      Introduzca el nombre del concepto.
                                    </div>
                                  </div>
                                  <div class="col-2">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptPercentage' + i"
                                        min="0"
                                        max="100"
                                        pattern="[0-9.]+"
                                        v-model.trim="concept.percentage"
                                        @focusin="conceptDefaultValues(concept)"
                                        @focusout="conceptDefaultValues(concept)"
                                        required
                                      />
                                      <span class="input-group-text">%</span>
                                    </div>
                                    <div class="invalid-feedback">Introduzca el porcentaje del concepto.</div>
                                  </div>
                                </div>
                              </div>
                              <!-- Cantidad -->
                              <div class="col-2 mt-2">
                                <label class="control-label">
                                  <b>Cantidad *</b>
                                </label>
                                <input
                                  type="text"
                                  class="form-control"
                                  :id="'inputConceptQuantity' + i"
                                  v-model.trim="concept.quantity"
                                  min="1"
                                  pattern="[1-9][0-9]*"
                                  @focusin="conceptDefaultValues(concept)"
                                  @focusout="conceptDefaultValues(concept)"
                                  required
                                />
                              </div>

                              <!-- Amount - Total -->
                              <div class="col-10 mt-2 mx-0">
                                <!-- Fila 2 | Base Original, Base y Total -->
                                <div class="row fields-currency">
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Original*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountOriginal' + i"
                                        v-model.trim="concept.amount_original"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Base Unitaria Calculada*</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountCurrency' + i"
                                        v-model.trim="concept.amount_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        required
                                        readonly
                                      />
                                      <span class="input-group-text">[[ concept?.currency || inputCurrency ]]</span>
                                    </div>
                                  </div>
                                  <div class="col-4">
                                    <label class="control-label">
                                      <b>Total *</b>
                                    </label>
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptTotalCurrency' + i"
                                        v-model.trim="concept.total_currency"
                                        pattern="^-?\d+(\.\d+)?$"
                                        @focusin="conceptDefaultValues(concept)"
                                        @focusout="conceptDefaultValues(concept)"
                                        required
                                      />
                                      <span class="input-group-text">
                                        [[ concept?.currency || inputCurrency ]]
                                      </span>
                                    </div>
                                  </div>
                                </div>

                                <!-- Fila 3 | conversiones a Euros (solo si la moneda no es EUR) -->
                                <div class="row fields-Euros"
                                  v-show="(concept.currency && concept.currency != 'EUR') || (inputCurrency && inputCurrency != 'EUR')"
                                >
                                  <div class="col-4">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountOriginalEuros' + i"
                                        v-model="concept.amount_original_euros"
                                        readonly
                                        required
                                      />
                                      <span class="input-group-text">EUR</span>
                                    </div>
                                  </div>
                                  <div class="col-4 ml-0 mr-0">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptAmountEuros' + i"
                                        v-model="concept.amount_euros"
                                        @input="onChangeAmountEuros(concept)"
                                        readonly
                                        required
                                      />
                                      <span class="input-group-text">
                                        EUR
                                      </span>
                                    </div>
                                  </div>
                                  <div class="col-4 ml-0 mr-0">
                                    <div class="input-group">
                                      <input
                                        type="text"
                                        class="form-control"
                                        :id="'inputConceptTotalEuros' + i"
                                        v-model="concept.total_euros"
                                        readonly
                                        required
                                      />
                                      <span class="input-group-text">
                                        EUR
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-1 d-flex justify-content-center align-items-center text-center">
                            <btn type="button" class="btn btn-link" @click="removeConcept(i)">
                              <h3><i class="fa-solid fa-xl fa-xmark text-danger"></i></h3>
                            </btn>
                          </div>
                        </div>
                      </fieldset>
                      <!-- Fieldset 2 (Concept supplied)-->
                    </fieldset>
                    <!-- Fieldset 2 -->
                  </div>
                </div>
              </div>
              <!-- Concepts  -->

              <!-- Fieldset 3 -->
              <fieldset id="fieldset3">
                <!-- Button: Add Concept  -->
                <div 
                  class="col-12 d-flex justify-content-end align-items-center text-rigth"
                  v-if="inputInvoiceType != 'payroll'">
                  <a 
                    class=" btn btn-outline-danger btn-plus mt-1 mb-2 " 
                    @click="deleteAllConcepts()"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard'">
                    <i class="fa-solid fa-x"></i>
                    <span>Eliminar Todos</span>
                  </a>
                  <select
                    class="btn btn-outline-primary mt-1 mb-2"
                    id="inputSelectVat"
                    @change="recalcAllIvaConcepts()"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard'"
                    required
                  >
                    <option selected disabled>Selecciona Todos Tipos IVA</option>
                    <option value="21">Todos los IVA al 21%</option>
                    <option value="10">Todos los IVA al 10%</option>
                    <option value="0">Todos los IVA al 0%</option>
                    <option disabled>---------------------------------------</option>
                    <option value="25">Todos los IVA al 25%</option>
                    <option value="24">Todos los IVA al 24%</option>
                    <option value="23">Todos los IVA al 23%</option>
                    <option value="22">Todos los IVA al 22%</option>
                    <option value="20">Todos los IVA al 20%</option>
                    <option value="19">Todos los IVA al 19%</option>
                    <option value="18">Todos los IVA al 18%</option>
                    <option value="17">Todos los IVA al 17%</option>
                    <option value="16">Todos los IVA al 16%</option>
                    <option value="15">Todos los IVA al 15%</option>
                    <option value="14">Todos los IVA al 14%</option>
                    <option value="13">Todos los IVA al 13%</option>
                    <option value="12">Todos los IVA al 12%</option>
                    <option value="11">Todos los IVA al 11%</option>
                    <option value="10">Todos los IVA al 10%</option>
                    <option value="9">Todos los IVA al 9%</option>
                    <option value="8">Todos los IVA al 8%</option>
                    <option value="7">Todos los IVA al 7%</option>
                    <option value="6">Todos los IVA al 6%</option>
                    <option value="5">Todos los IVA al 5%</option>
                    <option value="4">Todos los IVA al 4%</option>
                    <option value="3">Todos los IVA al 3%</option>
                    <option value="2">Todos los IVA al 2%</option>
                    <option value="1">Todos los IVA al 1%</option>
                  </select>
                  <a 
                    class="btn btn-outline-primary btn-plus mt-1 mb-2" 
                    @click="recalcTotalConcepts()"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard'"
                  >
                    <span>Recalcular Totales (Desde la Base)</span>
                  </a>
                  <a 
                    class="btn btn-outline-primary btn-plus mt-1 mb-2"
                    @click="recalcCurrencyConcepts()"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard'"
                  >
                    <span>Recalcular Cambio Divisas</span>
                  </a>
                  <a 
                    class="btn btn-outline-primary btn-plus mt-1 mb-2" 
                    @click="addConcept()"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard'"
                  >
                    <i class="fa-solid fa-plus"></i>
                    <span>Añadir Concepto</span>
                  </a>
                  <a 
                    class="btn btn-outline-primary btn-plus mt-1 mb-2" 
                    @click="addConceptSuplied()"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard'"
                  >
                    <i class="fa-solid fa-plus"></i>
                    <span>Añadir Suplido</span>
                  </a>
                </div>

                <!-- Resume: Total  -->
                <div class="d-flex justify-content-end align-items-end text-rigth">
                  <div 
                    class="col-6 total border" 
                    id="totalResumeCurrency"
                    v-show="inputInvoiceType && inputCurrency && inputCurrency != 'EUR' && inputInvoiceStatus != 'discard'"
                  >
                    <div class="row">
                      <div class="col-6 totalKey">
                        <span><b>Base Imponible:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().amountCurrencyWithoutSuplied) ]] [[ inputCurrency ]]</b></span>
                      </div>
                    </div>
                    <div class="row" v-show="inputHaveSupplies">
                      <div class="col-6 totalKey">
                        <span><b>Base Imponible Exenta:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().amountSupliedCurrency) ]] [[ inputCurrency ]]</b></span>
                      </div>
                    </div>
                    <div class="row mt-1"></div>
                    <div class="row" v-for="(value, key, index) in all_vats_currency">
                      <div class="col-6 totalKey" v-show="key!='0.00'">
                        <span>IVA [[key]]%:</span>
                      </div>
                      <div class="col-6 totalValue" v-show="key!='0.00'">
                        <span>[[ numberPrecision2(value) ]] [[ inputCurrency ]]</span>
                      </div>
                    </div>
                    <div class="row mt-1">
                      <div class="col-6 totalKey">
                        <span><b>IVA (Total):</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().vatCurrency) ]] [[ inputCurrency ]]</b></span>
                      </div>
                    </div>
                    <div class="row" v-show="inputIsEquivalentTax == true">
                      <div class="col-6 totalKey">
                        <span><b>Rec. Equivalencia:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().eqtaxCurrency) ]] [[ inputCurrency ]]</b></span>
                      </div>
                    </div>
                    <div class="row" v-show="parseInt(Number(getTotal().irpfCurrency)* 100, 10) / 100 != 0">
                      <div class="col-6 totalKey">
                        <span><b>IRPF:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().irpfCurrency) * -1 ]] [[ inputCurrency ]]</b></span>
                      </div>
                    </div>
                    <div class="row mt-1 justify-content-center">
                      <hr class="border border-primary border-3 align-center opacity-75"
                          style="width:90%;">
                    </div>
                    <div class="row">
                      <div class="col-6 totalKey">
                        <span><b>Total:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().totalCurrency) ]] [[ inputCurrency ]]</b></span>
                      </div>
                    </div>
                  </div>
                  <div 
                    class="col-6 total border" 
                    id="totalResumeEuros"
                    v-show="inputInvoiceType && inputInvoiceStatus != 'discard' && inputInvoiceType != 'payroll'"
                  >
                    <div class="row">
                      <div class="col-6 totalKey">
                        <span><b>Base Imponible:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().amountEurosWithoutSuplied) ]] €</b></span>
                      </div>
                    </div>
                    <div class="row" v-show="inputHaveSupplies">
                      <div class="col-6 totalKey">
                        <span><b>Base Imponible Exenta:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().amountSupliedEuros) ]] €</b></span>
                      </div>
                    </div>
                    <div class="row mt-1"></div>
                    <div class="row" v-for="(value, key, index) in all_vats_euros">
                      <div class="col-6 totalKey" v-show="key!='0.00'">
                        <span>IVA [[key]] %:</span>
                      </div>
                      <div class="col-6 totalValue" v-show="key!='0.00'">
                        <span>[[ numberPrecision2(value) ]] €</span>
                      </div>
                    </div>
                    <div class="row mt-1">
                      <div class="col-6 totalKey">
                        <span><b>IVA (Total):</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().vatEuros) ]] €</b></span>
                      </div>
                    </div>
                    <div class="row" v-show="inputIsEquivalentTax == true">
                      <div class="col-6 totalKey">
                        <span><b>Rec. Equivalencia:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().eqtaxEuros) ]] €</b></span>
                      </div>
                    </div>
                    <div class="row" v-show="parseInt(Number(getTotal().irpfEuros)* 100, 10) / 100 != 0">
                      <div class="col-6 totalKey">
                        <span><b>IRPF:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().irpfEuros) * -1 ]] €</b></span>
                      </div>
                    </div>
                    <div class="row mt-1 justify-content-center">
                      <hr class="border border-primary border-3 align-center opacity-75"
                          style="width:90%;">
                    </div>
                    <div class="row">
                      <div class="col-6 totalKey">
                        <span><b>Total:</b></span>
                      </div>
                      <div class="col-6 totalValue">
                        <span><b>[[ numberPrecision2(getTotal().totalEuros) ]] €</b></span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Form 1: Sales -->
                <div 
                  class="row mt-3" 
                  id="sales_bottom"
                  v-if="inputInvoiceType == 'sales' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de ingreso * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountSales"
                      name="inputAccountSales"
                      v-model="inputAccountSales"
                      :disabled="!inputCustomer"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_sales"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Ingreso.</div>
                  </div>
                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>

                  <!-- MarketPlace -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="marketplace">
                      <b>Marketplace * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputMarketplace"
                      name="inputMarketplace"
                      v-model="inputMarketplace"
                      placeholder="Selecciona el marketplace"
                      {% comment %} required {% endcomment %}
                      disabled
                    >
                      <option selected disabled> Selecciona el marketplace</option>
                      <option 
                        :value="market.pk" 
                        v-for="market in dj.marketplaces"
                        :key="market.pk"
                      >
                        [[ market.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                  </div>

                  <!-- Economic Activity -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="iae">
                      <b>Actividad económica *</b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputEconomicActivity"
                      name="inputEconomicActivity"
                      v-model="inputEconomicActivity"
                      required
                    >
                      <option selected disabled> Selecciona la actividad</option>
                      <option 
                        :value="item.pk" 
                        v-for="item in dj.economic_activity_filtered"
                        :key="item.pk">
                        [[ item.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la actividad económica.</div>
                  </div>
                </div>
                <!-- Form 1: Sales -->

                <!-- Form 2: Expenses -->
                <div class="row mt-3" id="expenses_bottom" v-if="inputInvoiceType == 'expenses' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de gastos * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountExpenses"
                      name="inputAccountExpenses"
                      v-model="inputAccountExpenses"
                      :disabled="!inputProvider"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_expenses"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                  </div>
                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>

                  <!-- MarketPlace -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="marketplace">
                      <b>Marketplace * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputMarketplace"
                      name="inputMarketplace"
                      v-model="inputMarketplace"
                      {% comment %} required {% endcomment %}
                      disabled
                    >
                      <option selected disabled> Selecciona el marketplace</option>
                      <option 
                        :value="market.pk" 
                        v-for="market in dj.marketplaces"
                        :key="market.pk">
                        [[ market.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                  </div>

                  <!-- Economic Activity -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="iae">
                      <b>Actividad económica * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputEconomicActivity"
                      name="inputEconomicActivity"
                      v-model="inputEconomicActivity"
                      required
                    >
                      <option selected disabled> Selecciona la actividad</option>
                      <option 
                        :value="item.pk" 
                        v-for="item in dj.economic_activity_filtered"
                        :key="item.pk"
                      >
                        [[ item.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la actividad económica.</div>
                  </div>
                </div>
                <!-- Form 2: Expenses -->

                <!-- Form 3: Tickets -->
                <div class="row mt-3" id="expenses_bottom"
                     v-if="inputInvoiceType == 'ticket' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de gastos * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountExpenses"
                      name="inputAccountExpenses"
                      v-model="inputAccountExpenses"
                      :disabled="!inputProvider"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_expenses"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                  </div>
                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>

                  <!-- MarketPlace -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="marketplace">
                      <b>Marketplace * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputMarketplace"
                      name="inputMarketplace"
                      v-model="inputMarketplace"
                      {% comment %} required {% endcomment %}
                      disabled
                    >
                      <option selected disabled> Selecciona el marketplace</option>
                      <option 
                        :value="market.pk" 
                        v-for="market in dj.marketplaces"
                        :key="market.pk"
                      >
                        [[ market.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                  </div>

                  <!-- Economic Activity -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="iae">
                      <b>Actividad económica * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputEconomicActivity"
                      name="inputEconomicActivity"
                      v-model="inputEconomicActivity"
                      required
                    >
                      <option selected disabled> Selecciona la actividad</option>
                      <option 
                        :value="item.pk" 
                        v-for="item in dj.economic_activity_filtered"
                        :key="item.pk"
                      >
                        [[ item.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la actividad económica.</div>
                  </div>
                </div>
                <!-- Form 3: Tickets -->

                <!-- Form 4: Import Invoice -->
                <div class="row mt-3" id="import-invoice_bottom"
                     v-if="inputInvoiceType == 'import-invoice' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de gastos * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountExpenses"
                      name="inputAccountExpenses"
                      v-model="inputAccountExpenses"
                      :disabled="!inputProvider"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_expenses"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                  </div>

                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>

                  <!-- MarketPlace -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="marketplace">
                      <b>Marketplace * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputMarketplace"
                      name="inputMarketplace"
                      v-model="inputMarketplace"
                      {% comment %} required {% endcomment %}
                      disabled
                    >
                      <option selected disabled> Selecciona el marketplace</option>
                      <option 
                        :value="market.pk" 
                        v-for="market in dj.marketplaces"
                        :key="market.pk"
                      >
                        [[ market.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                  </div>

                  <!-- Economic Activity -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="iae">
                      <b>Actividad económica * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputEconomicActivity"
                      name="inputEconomicActivity"
                      v-model="inputEconomicActivity"
                      required
                    >
                      <option selected disabled> Selecciona la actividad</option>
                      <option 
                        :value="item.pk" 
                        v-for="item in dj.economic_activity_filtered"
                        :key="item.pk"
                      >
                        [[ item.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la actividad económica.</div>
                  </div>
                </div>
                <!-- Form 4: Import Invoice -->

                <!-- Form 6: Import Expenses -->
                <div class="row mt-3" id="import-invoice_bottom"
                     v-if="inputInvoiceType == 'import-expenses' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de gastos * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountExpenses"
                      name="inputAccountExpenses"
                      v-model="inputAccountExpenses"
                      :disabled="!inputProvider"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_expenses"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                  </div>
                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>
                </div>
                
                <!-- Form 6: Import Expenses -->

                <!-- Form 7: Self-employment fee -->
                <div class="row mt-3" id="expenses_bottom"
                     v-if="inputInvoiceType == 'self-employment-fee' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de gastos * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountExpenses"
                      name="inputAccountExpenses"
                      v-model="inputAccountExpenses"
                      :disabled="!inputProvider"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_expenses"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                  </div>
                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>

                  <!-- MarketPlace -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="marketplace">
                      <b>Marketplace * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputMarketplace"
                      name="inputMarketplace"
                      v-model="inputMarketplace"
                      {% comment %} required {% endcomment %}
                      disabled
                    >
                      <option selected disabled> Selecciona el marketplace</option>
                      <option 
                        :value="market.pk" 
                        v-for="market in dj.marketplaces"
                        :key="market.pk"
                      >
                        [[ market.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                  </div>

                  <!-- Economic Activity -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="iae">
                      <b>Actividad económica * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputEconomicActivity"
                      name="inputEconomicActivity"
                      v-model="inputEconomicActivity"
                      required
                    >
                      <option selected disabled> Selecciona la actividad</option>
                      <option 
                        :value="item.pk" 
                        v-for="item in dj.economic_activity_filtered"
                        :key="item.pk"
                      >
                        [[ item.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la actividad económica.</div>
                  </div>
                </div>
                <!-- Form 7: Self-employment fee -->

                <!-- Form 8: Rent -->
                <div class="row mt-3" id="rent_bottom"
                     v-if="inputInvoiceType == 'rent' && inputInvoiceStatus != 'discard'">
                  <!-- Account -->
                  <div class="col-12 form-group form-check">
                    <label class="form-label" for="account_sales">
                      <b>Cuenta contable de gastos * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputAccountExpenses"
                      name="inputAccountExpenses"
                      v-model="inputAccountExpenses"
                      :disabled="!inputProvider"
                      required
                    >
                      <option selected disabled> Selecciona la cuenta</option>
                      <option 
                        :value="account.pk" 
                        v-for="account in dj.account_expenses"
                        :key="account.pk"
                      >
                        [[ account.description ]] ( [[ account.pk ]] )
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la Cuenta Contable de Gastos.</div>
                  </div>
                  <div class="col-12 form-check" v-if="annotations && annotations.length">
                    <div class="alert alert-warning" role="alert">
                      <h5 class="alert-heading">Atención! Esta cuenta contable tiene anotaciones</h5>
                        <li v-for="annotation in annotations" :key="annotation.id">
                          <b>[[annotation.comment]]</b>
                        </li>
                    </div>
                  </div>

                  <!-- MarketPlace -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="marketplace">
                      <b>Marketplace * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputMarketplace"
                      name="inputMarketplace"
                      v-model="inputMarketplace"
                      {% comment %} required {% endcomment %}
                      disabled
                    >
                      <option selected disabled> Selecciona el marketplace</option>
                      <option 
                        :value="market.pk" 
                        v-for="market in dj.marketplaces"
                        :key="market.pk"
                      >
                        [[ market.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el Marketplace / Canal de Ventas.</div>
                  </div>

                  <!-- Economic Activity -->
                  <div class="col-6 form-group form-check">
                    <label class="form-label" for="iae">
                      <b>Actividad económica * </b>
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputEconomicActivity"
                      name="inputEconomicActivity"
                      v-model="inputEconomicActivity"
                      required
                    >
                      <option selected disabled> Selecciona la actividad</option>
                      <option 
                        :value="item.pk" 
                        v-for="item in dj.economic_activity_filtered"
                        :key="item.pk"
                      >
                        [[ item.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione la actividad económica.</div>
                  </div>
                </div>

                <!-- Form 8: Rent -->

                <!-- Form: Common -->
                <div class="row mt-3" id="common_bottom">
                  <!-- Discard Reasons -->
                  <div class="col-12 form-group form-check" v-if="inputInvoiceStatus == 'discard'">
                    <label class="form-label" for="discard_reason">
                      Motivo de descarte:
                    </label>
                    <select
                      class="form-select form-control"
                      id="inputDiscardReason"
                      name="inputDiscardReason"
                      v-model="inputDiscardReason"

                    >
                      <option selected disabled>Seleccione el motivo de descarte</option>
                      <option 
                        :value="res.pk" 
                        v-for="res in dj.discard_reason" 
                        :key="res.pk"
                      >
                        [[ res.description ]]
                      </option>
                    </select>
                    <div class="invalid-feedback">Seleccione el motivo de descarte.</div>
                  </div>
                  <!-- Discard Notes -->
                  <div class="col form-group form-check p-3" v-if="inputInvoiceStatus == 'discard' && inputDiscardReason == 'other'">
                    <label class="form-label" for="discard_reason_notes">
                      Descripción del motivo de descarte:
                    </label>
                    <textarea
                      class="form-control"
                      id="inputDiscardReasonNotes"
                      name="inputDiscardReasonNotes"
                      v-model="inputDiscardReasonNotes"
                      rows="3"
                    ></textarea>
                  </div>

                  <!-- Transaction Type -->
                  <div class="col form-group form-check p-3" v-if="inputInvoiceStatus != 'discard'">
                    <label class="form-label" for="inputTransactionType">
                      <b>Tipo de Transacción: *</b>
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="inputTransactionType"
                      name="inputTransactionType"
                      v-model="inputTransactionType"
                      required
                      style="background-color: #e9ecef; opacity: 1;"
                      onmousedown="return false;"
                      onfocus="this.blur();"
                    />
                    <div class="invalid-feedback">El campo no puede estar vacío.</div>
                  </div>

                  <!-- Accordion: More Fields -->
                  <div class="accordion mb-3" id="accordionMoreFields">
                    <div class="accordion-item">

                      <h2 class="accordion-header" id="mfOne">
                        <button 
                          class="accordion-button" 
                          type="button" 
                          data-bs-toggle="collapse"
                          data-bs-target="#collapseMFOne" 
                          aria-expanded="true"
                          aria-controls="collapseMFOne"
                        >
                          Mas Campos
                        </button>
                      </h2>

                      <div id="collapseMFOne" class="accordion-collapse collapse" aria-labelledby="mfOne" data-bs-parent="#accordionMoreFields">
                        <div class="accordion-body">
                          <!-- Tags  -->
                          <div class="col-12 form-group form-check">
                            <label class="form-label" for="tags">
                              Etiquetas:
                            </label>
                            <input
                              type="text"
                              class="form-control"
                              id="inputInvoiceTags"
                              name="inputInvoiceTags"
                              v-model="inputInvoiceTags"
                            />
                          </div>
                          <!-- Notes  -->
                          <div class="col-12 form-group form-check" v-if="inputInvoiceStatus != 'discard'">
                            <label class="form-label" for="notes">
                              Notas / Observaciones:
                            </label>
                            <textarea
                              class="form-control"
                              id="inputInvoiceNotes"
                              name="inputInvoiceNotes"
                              v-model="inputInvoiceNotes"
                              rows="3"
                            ></textarea>
                          </div>
                          <!-- Notes private  -->
                          <div class="col-12 form-group form-check" v-if="inputInvoiceStatus != 'discard'">
                            <label class="form-label" for="notes">
                              Notas Privadas:
                            </label>
                            <textarea
                              class="form-control"
                              id="inputInvoiceNotesPrivate"
                              name="inputInvoiceNotesPrivate"
                              v-model="inputInvoiceNotesPrivate"
                              rows="3"
                            ></textarea>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>

                </div>
                <!-- Form: Common -->
              </fieldset>
              <!-- Fieldset 3 -->

              <!-- FORM -->

              {% csrf_token %}

              <!-- Hidden Inputs -->
              <div>
                <!-- Invoice Name -->
                <input type="hidden" id="name" name="name" v-model="inputInvoiceName" readonly/>

                <!-- Invoice Status -->
                <input type="hidden" id="status" name="status" v-model="inputInvoiceStatus" readonly/>

                <!-- Invoice Category -->
                <input type="hidden" id="invoice_category" name="invoice_category" v-model="inputInvoiceCategory" readonly/>

                <!-- Invoice Type -->
                <input type="hidden" id="invoice_type" name="invoice_type" v-model="inputInvoiceType" readonly/>

                <!-- Invoice Relation -->
                <input type="hidden" id="related_invoice" name="related_invoice" v-model="inputRelatedInvoice" readonly/>

                <!-- Customer -->
                <input type="hidden" id="customer" name="customer" v-model="inputCustomer" v-if="inputInvoiceCategory== 'sales'"/>

                <!-- Provider -->
                <input type="hidden" id="provider" name="provider" v-model="inputProvider" v-if="inputInvoiceCategory== 'expenses'"/>

                <!-- Rent -->
                <input type="hidden" id="seller_rental" name="seller_rental" v-model="inputRent" v-if="inputInvoiceType== 'rent'"/>

                <!-- TaxCountry -->
                <input type="hidden" id="tax_country" name="tax_country" v-model="inputTaxCountry" readonly/>

                <!-- TypePayroll -->
                <input type="hidden" id="typePayroll" name="typePayroll" v-model="inputTypePayroll" readonly/>


                <!-- TaxResponsability -->
                <input type="hidden" id="tax_responsibility" name="tax_responsibility" v-model="inputTaxResponsibility" readonly/>

                <!-- Invoice Reference -->
                <input type="hidden" id="reference" name="reference" v-model="inputInvoiceNumber" readonly/>
                <input type="hidden" id="reference_absolute" name="reference_absolute" v-model="inputInvoiceNumberAbsolute" readonly/>

                <!-- Invoice Expedition Date -->
                <input type="hidden" id="expedition_date" name="expedition_date" v-model="inputInvoiceDate" readonly/>
                <input type="hidden" id="invoice_date" name="invoice_date" v-model="inputInvoiceDate" readonly/>

                <!-- Is Rectifying -->
                <div v-if="inputInvoiceType == 'sales' || inputInvoiceType == 'expenses'">
                  <input type="hidden" id="is_rectifying" name="is_rectifying" v-model="inputIsRectifying" readonly/>
                </div>
                <div v-else>
                  <input type="hidden" id="is_rectifying" name="is_rectifying" :value="False" readonly/>
                </div>

                <!-- is postponed vat -->
                <input type="hidden" id="is_postponed_import_vat" name="is_postponed_import_vat" v-model="inputIsPostponedVat" readonly/>

                <!-- Currency -->
                <input type="hidden" id="currency" name="currency" v-model="inputCurrency" readonly/>

                <!-- IS OSS -->
                <div v-if="inputInvoiceCategory == 'sales'">
                  <input type="hidden" id="is_oss" name="is_oss" v-model="inputIsDistanceSell" readonly/>
                </div>
                <div v-else>
                  <input type="hidden" id="is_oss" name="is_oss" :value="False" readonly/>
                </div>

                <!-- IS EQTAX -->
                <input type="hidden" id="is_eqtax" name="is_eqtax" v-model="inputIsEquivalentTax" readonly/>

                <!-- IS reverse_charge -->
                <input type="hidden" id="is_reverse_charge" name="is_reverse_charge" v-model="inputIsReverseCharge" readonly/>

                <!-- Departure Country -->
                <input type="hidden" id="departure_country" name="departure_country" v-model="inputDepartureCountry" readonly/>

                <!-- Out of Time -->
                <div>
                  <input type="hidden" id="out_of_time" name="out_of_time" v-model="inputOutOfTime" readonly/>
                </div>

                <!-- Invoice Accounting Date -->
                <div v-if="inputOutOfTime">
                  <input type="hidden" id="accounting_date" name="accounting_date"   v-model="inputInvoiceAccountingDate" readonly/>
                </div>
                <div v-else>
                  <input type="hidden" id="accounting_date" name="accounting_date"   v-model="inputInvoiceDate" readonly/>
                </div>

                <!-- Account Sales -->
                <div v-if="inputInvoiceCategory == 'sales'">
                  <input type="hidden" id="account_sales" name="account_sales"   v-model="inputAccountSales" readonly/>
                </div>

                <!-- Account Expenses -->
                <div v-if="inputInvoiceCategory == 'expenses'">
                  <input type="hidden" id="account_expenses" name="account_expenses"   v-model="inputAccountExpenses" readonly/>
                  <input type="hidden" id="account_expenses" name="account_expenses_accrued"   v-model="inputAccountExpensesAccrued" readonly/>
                  <input type="hidden" id="account_expenses" name="account_expenses_worker"   v-model="inputAccountExpensesWorker" readonly/>
                  <input type="hidden" id="account_expenses" name="account_expenses_company"   v-model="inputAccountExpensesCompany" readonly/>
                </div>

                <!-- MarketPlace -->
                <input type="hidden" id="marketplace" name="marketplace" v-model="inputMarketplace" readonly/>

                <!-- IAE / Economic Activity -->
                <input type="hidden" id="iae" name="iae" v-model="inputEconomicActivity" readonly/>

                <!-- Tags -->
                <input type="hidden" id="tags" name="tags" v-model="inputInvoiceTags" readonly/>

                <!-- Discard Reasons -->
                <input type="hidden" id="discard_reason" name="discard_reason" v-model="inputDiscardReason" readonly>

                <!-- Notes Discard Reasons -->
                <input type="hidden" id="discard_reason_notes" name="discard_reason_notes" v-model="inputDiscardReasonNotes" readonly>

                <!-- Invoices Carrusel -->
                <input type="hidden" id="carrusel" name="carrusel">

                <!-- Invoices ReOpen -->
                <input type="hidden" id="reopen" name="reopen">

                <!-- Notes -->
                <input type="hidden" id="notes" name="notes" v-model="inputInvoiceNotes" readonly/>

                <!-- Notes Private -->
                <input type="hidden" id="notes_private" name="notes_private" v-model="inputInvoiceNotesPrivate" readonly/>

                <!-- Transaction Type -->
                <input type="hidden" id="transaction_type" name="transaction_type" v-model="inputTransactionType" readonly/>

                <!-- Operation Type -->
                <input type="hidden" id="operation_type" name="operation_type" v-model="inputOperationType" readonly/>

                <!-- Concepts -->
                <input type="hidden" id="concepts" name="concepts" v-model="inputConceptsJSON" readonly/>

                <!-- JSON VATS -->
                <input type="hidden" id="json_vat" name="json_vat" v-model="inputJsonVats" readonly/>

                <!-- Payroll -->
                <input type="hidden" id="worker" name="worker" v-model="inputWorker" readonly>
                <input type="hidden" id="totalAccrued" name="totalAccrued" v-model="inputTotalAccrued" readonly>
                <input type="hidden" id="baseSalary" name="baseSalary" v-model="inputSalaryBase" readonly>
                <input type="hidden" id="salaryInKind" name="salaryInKind" v-model="inputSalaryInKind" readonly>
                <input type="hidden" id="percentageIRPF" name="percentageIRPF" v-model="inputPercentageIRPF" readonly>
                <input type="hidden" id="incomeIRPF" name="incomeIRPF" v-model="inputIncomeIRPF" readonly>
                <input type="hidden" id="netPay" name="netPay" v-model="inputNetPay" readonly>
                <input type="hidden" id="workerSocialSecurity" name="workerSocialSecurity" v-model="inputWorkerSocialSecurity" readonly>
                <input type="hidden" id="companySocialSecurity" name="companySocialSecurity" v-model="inputCompanySocialSecurity" readonly>
                <input type="hidden" id="totalPayroll" name="totalPayroll" v-model="inputTotalPayroll" readonly>

                <!-- Totals -->
                <input type="hidden" id="total_eqtax_currency" name="total_eqtax_currency" v-model="getTotal().eqtaxCurrency" readonly/>
                <input type="hidden" id="total_eqtax_euros" name="total_eqtax_euros" v-model="getTotal().eqtaxEuros" readonly/>
                <input type="hidden" id="total_vat_currency" name="total_vat_currency" v-model="getTotal().vatCurrency" readonly/>
                <input type="hidden" id="total_vat_euros" name="total_vat_euros" v-model="getTotal().vatEuros" readonly/>
                <input type="hidden" id="total_irpf_currency" name="total_irpf_currency" v-model="getTotal().irpfCurrency" readonly/>
                <input type="hidden" id="total_irpf_euros" name="total_irpf_euros" v-model="getTotal().irpfEuros" readonly/>
                <input type="hidden" id="total_amount_currency" name="total_amount_currency" v-model="getTotal().amountCurrency" readonly/>
                <input type="hidden" id="total_amount_euros" name="total_amount_euros" v-model="getTotal().amountEuros" readonly/>
                <input type="hidden" id="total_currency" name="total_currency" v-model="getTotal().totalCurrency" readonly/>
                <input type="hidden" id="total_euros" name="total_euros" v-model="getTotal().totalEuros" readonly/>
              </div>
              <!-- Hidden Inputs -->
              <br>
                <div v-if="dj.invoice.is_generated && veriFactuInstance && veriFactuInstance.operation_type == 'Alta' " 
                  class="col-12"><div class="alert alert-danger">
                    <span style="font-size: large"><b><i class="fa-solid fa-triangle-exclamation"></i>&nbsp;&nbsp;Atención:</b> Esta factura no debe ser reabierta o modificada debido a que ha sido emitida por el cliente y ha sido enviada al sistema verifactu, <b>cualquier modificación será reemitada AUTOMÁTICAMENTE de nuevo a la AEAT como subsanación</b>.
                    </span></div>
                </div>
                <div v-if="dj.invoice.is_generated && veriFactuInstance && veriFactuInstance.operation_type == 'Anulacion' " 
                  class="col-12"><div class="alert alert-warning">
                    <span style="font-size: large"><b><i class="fa-solid fa-triangle-exclamation"></i>&nbsp;&nbsp;Atención:</b> Esta factura fue emitida al sistema veriFactu de la AEAT y posteriormente Anulada.
                    </span></div>
                </div>
              {% if invoice.used_in_entry is not True and invoice.used_in_reconciliation is not True %}
                {% if not lastVeriFactuInv or lastVeriFactuInv.operation_type == 'Anulacion' or invoice.status.code == "pending" or invoice.status.code == "revision-pending" %}
                  <!-- Save Buttons -->
                  <div v-if="inputInvoiceStatusOriginal == 'revised' || inputInvoiceStatusOriginal == 'discard'">
                    <div class="row" v-show="inputInvoiceStatus == 'revised' || inputInvoiceStatus == 'discard'">
                      <div class="col-4 form-group form-check">
                        <button
                          id="open"
                          type="submit"
                          class="btn btn-warning w-100 text-white"
                          {% comment %} v-show="inputInvoiceType" {% endcomment %}
                          style="background-color: #FFC107;"
                          @click.prevent="onClickSubmit('revision-pending'); inputReOpen();"
                        >
                          <b>Reabrir: Pendiente Revisión</b>
                        </button>
                        <input type="hidden" id="status" name="status" v-model="inputInvoiceStatus" readonly>
                      </div>
                    </div>
                  </div>
                  <div v-if="inputInvoiceStatusOriginal != 'revised' && inputInvoiceStatusOriginal != 'discard'" name="buttons" id="buttons">
                    <div class="row" v-show="inputInvoiceStatus != 'revised'">
                      <div class="col-4 form-group form-check">
                        <!-- Button trigger modal -->
                        <button id="discard" type="button" class="btn btn-danger w-100 text-white"
                                data-bs-toggle="modal" data-bs-target="#modal">
                          <b>Descartar</b>
                        </button>
                        <!-- Modal -->
                        <div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
                          <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="modalLabel">Motivo de descarte</h5>
                              </div>
                              <div class="modal-body">
                                <div class="col form-group form-check p-3">
                                  <label class="form-label" for="inputDiscardReason">
                                    <b>Seleccione un motivo de descarte:</b>
                                  </label>
                                  <select
                                    class="form-select form-control"
                                    id="inputDiscardReasonModal"
                                    name="inputDiscardReasonModal"
                                    v-model="inputDiscardReason"
                                  >
                                    <option selected disabled>Seleccione el motivo de
                                      descarte
                                    </option>
                                    <option 
                                      :value="res.pk" 
                                      v-for="res in dj.discard_reason"
                                      :key="res.pk">
                                      [[ res.description ]]
                                    </option>
                                  </select>
                                </div>

                                <div class="col form-group form-check p-3" v-if="inputDiscardReason == 'other'">
                                  <label class="form-label" for="discard_reason_notes">
                                    Escriba el motivo de descarte:
                                  </label>
                                  <textarea
                                    class="form-control"
                                    id="inputDiscardReasonNotes"
                                    name="inputDiscardReasonNotes"
                                    v-model="inputDiscardReasonNotes"
                                    rows="3"
                                  ></textarea>
                                </div>
                              </div>

                              <div class="modal-footer d-flex justify-content-center">
                                <button 
                                  type="button" class="btn btn-light"  
                                  data-bs-dismiss="modal"
                                  data-bs-target="#exampleModal"  
                                  aria-label="Close"
                                >
                                  <b>Cancelar</b>
                                </button>
                                <button 
                                  id="discard_m" 
                                  type="submit" 
                                  class="btn btn-danger  text-white"
                                  {% comment %} v-show="inputInvoiceType" {% endcomment %}  
                                  style="background-color: #DC3545;"  
                                  @click="onClickSubmit('discard')"
                                >
                                  <b>Descartar</b>
                                </button>
                                <button 
                                  id="discard_c" 
                                  type="submit" 
                                  class="btn btn-danger  text-white"
                                  {% comment %} v-show="inputInvoiceType" {% endcomment %}  
                                  style="background-color: #DC3545;" 
                                  @click="onClickSubmit('discard'); inputCarrusel()"
                                >
                                  <b>Descartar/Continuar</b>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-4 form-group form-check">
                        <button
                          id="pending"
                          type="submit"
                          class="btn btn-warning w-100 text-white"
                          v-show="inputInvoiceType"
                          style="background-color: #FFC107;"
                          @click.prevent="onClickSubmit('revision-pending')"
                        >
                          <b>Pendiente Revisión</b>
                        </button>
                      </div>
                      <div class="col-4 form-group form-check">
                          <fieldset id="fieldset_buttons" :disabled="isButtonConfirmDisabled()">
                            <button
                              id="revised"
                              type="submit"
                              class="btn btn-success w-100 text-white"
                              v-show="inputInvoiceType"
                              style="background-color: #1A8754;"
                              @click.prevent="onClickSubmit('revised')"
                            >
                              <b>Revisado</b>
                            </button>
                        </fieldset>
                      </div>
                      <div class="col-4 form-group form-check">
                        <a
                          id="next"
                          class="btn btn-secondary w-100 text-white"
                          v-show="inputInvoiceType"
                          style="background-color: #grey;"
                          href="{% url 'app_invoices:seller_invoice_next' seller.shortname invoice.pk %}"
                        >
                          <b>Siguiente Factura</b>
                        </a>
                      </div>
                      <div class="col-4 form-group form-check">
                        <button
                          id="pending_c"
                          type="submit"
                          class="btn btn-warning w-100 text-white"
                          v-show="inputInvoiceType"
                          style="background-color: #FFC107;"
                          @click.prevent="onClickSubmit('revision-pending'); inputCarrusel()"
                        >
                          <b>Pendiente Revisión/Continuar</b>
                        </button>
                      </div>
                      <div class="col-4 form-group form-check">
                          <fieldset id="fieldset_buttons" :disabled="isButtonConfirmDisabled()">
                            <button
                              id="revised_c"
                              type="submit"
                              class="btn btn-success w-100 text-white"
                              v-show="inputInvoiceType"
                              style="background-color: #1A8754;"
                              @click="onClickSubmit('revised'); inputCarrusel()"
                            >
                              <b>Revisado/Continuar</b>
                            </button>
                        </fieldset>
                        </div>
                    </div>
                  </div>
                  <!-- Save Buttons -->
                {% endif %}
              {% endif %}
            </form>
            <!-- FORM -->
          </div>
        </div>
        <!-- BODY -->
      </div>
    </div>
  </div>
  <div class="modal fade vue" id="newCostumer" role="dialog" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
      <div class="modal-content h-100">
        <div class="modal-body">
          <div class="container-fluid h-100">
            <div class="row h-100">
              <div class="col-md-6 ms-auto">
                {% include "invoices/include/pdf_invoice.html" %}
              </div>
              <div class="col-md-6 ms-auto">
                <div class="row">
                  <div class="d-flex justify-content-center">
                    <div id="iframeloading" style="display: block;">
                      <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                          <span class="visually-hidden">Loading...</span>
                        </div>
                      </div>
                      <br>
                      <h3>Cargando...</h3>
                    </div>
                  </div>
                </div>
                <iframe id="iframeModal" src="" class="w-100 h-100" @load="iframe()" style="display: none;"> "No es posible visualizar el contenido" </iframe>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal con las opciones manuales de veriFactu -->
    {% include "invoices/include/modal/modal_verifactu_manual_options.html" %}
  <!-- Modal con las opciones manuales de veriFactu -->
{% endblock content %}

{% block javascripts %}
  <script type="text/javascript" src="{{ STATIC_URL }}assets/js/plugins/vue/3.2.6/vue.global.prod.js"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/axios/axios.min-v1.2.6.js"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.3.min-v3.6.3.js" crossorigin="anonymous"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/cdns_locals/js/vuetify/vuetify.min-v3.1.5.js"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}assets/js/decimal.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

  <!-- JAVASCRIPT FACTURACIÓN DIGITAL -->
  <script type="text/javascript" src="{{ STATIC_URL }}assets/js/invoice/invoice_xml.js"></script>

  <!-- Incluir vat_validation_results -->
  <script type="application/json" id="vat-validation-results">
    {{ vat_validation_results|safe }}
  </script>

  <script type="module">
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch, computed} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputInvoiceCategory = ref(null);
    const inputInvoiceType = ref(null);
    const inputTaxCountry = ref(null);
    const inputTaxResponsibility = ref('seller');
    const inputOutOfTime = ref(false);
    const inputIsRectifying = ref(false);
    const inputInvoiceNumber = ref();
    const inputInvoiceNumberAbsolute = ref();
    const inputInvoiceDate = ref();
    const inputInvoiceAccountingDate = ref();
    const inputTypePayroll = ref(null);
    const inputCurrency = ref('EUR');
    const inputIsDistanceSell = ref(false);
    const inputIsEquivalentTax = ref(false);
    const inputIsReverseCharge = ref(false);
    const inputDepartureCountry = ref(null);
    const inputAccountSales = ref(null);
    const inputAccountExpenses = ref(null);
    const inputAccountExpensesAccrued = ref(null);
    const inputAccountExpensesWorker = ref(null);
    const inputAccountExpensesCompany = ref(null);
    const inputCustomer = ref();
    const inputNameSelfEmployed = ref();
    const inputProvider = ref();
    const inputRent = ref();
    const inputWorker = ref();
    const inputIsPostponedVat = ref(false);
    const invoiceInvalidFeedback = ref('Introduzca la fecha de la factura.');

    const inputWorkerType = ref(false);
    const inputTotalAccrued = ref(0);
    const inputSalaryBase = ref(0);
    const inputSalaryInKind = ref(0);
    const inputPercentageIRPF = ref(0);
    const inputIncomeIRPF = ref(0);
    const inputNetPay = ref(0);
    const inputWorkerSocialSecurity = ref(0);
    const inputCompanySocialSecurity = ref(0);
    const inputTotalPayroll = ref(0);
    const inputTotalTotalPayroll = ref(0);

    const inputMarketplace = ref(null);
    const inputEconomicActivity = ref(null);
    const inputInvoiceTags = ref(null);
    const inputInvoiceNotes = ref(null);
    const inputInvoiceNotesPrivate = ref(null);
    const inputTransactionType = computed(() => calcTransactionType());
    const inputOperationType = computed(() => calcOperationType());
    const inputInvoiceName = ref(null);
    const inputTotalCurrency = ref(0);
    const inputTotalEuros = ref(0);
    const inputInvoiceStatus = ref(null);
    const inputInvoiceStatusOriginal = ref(null);
    const inputIsGeneratedAmz = ref(false);
    const inputRelatedInvoice = ref();
    const inputDiscardReason = ref(null);
    const inputDiscardReasonNotes = ref(null);
    const inputMonths = ref(null);
    const inputYears = ref(null);
    const inputHaveSupplies = ref(false);
    const inputConcepts = ref([]);
    const inputConceptsPayroll = ref([]);
    const inputConceptsOld = ref([]);
    const inputConceptsJSON = computed(() => {
      if (inputInvoiceType.value == 'payroll') {
        return JSON.stringify(inputConceptsPayroll.value);
      } else {
        return JSON.stringify(inputConcepts.value);
      }
    });
    const inputJsonVats = ref("");
    const all_vats_currency = ref({});
    const all_vats_euros = ref({});

    const taxConversion = ref(1);
    const dj = ref({});
    const showCategory = ref(false);
    const editName = ref(false);

    const annotations = ref(null)
    const activeOffTime = ref(false)

    const months = ref([
      {"value": 1, "month": "Enero"},
      {"value": 2, "month": "Febrero"},
      {"value": 3, "month": "Marzo"},
      {"value": 4, "month": "Abril"},
      {"value": 5, "month": "Mayo"},
      {"value": 6, "month": "Junio"},
      {"value": 7, "month": "Julio"},
      {"value": 8, "month": "Agosto"},
      {"value": 9, "month": "Septiembre"},
      {"value": 10, "month": "Octubre"},
      {"value": 11, "month": "Noviembre"},
      {"value": 12, "month": "Diciembre"},
    ]);

    const currentYear = new Date().getFullYear();
    const years = ref(
      Array.from({ length: currentYear - 2022 + 1 }, (_, i) => {
        const year = currentYear - i;
        return { value: year, year: String(year) };
      })
    );

    const veriFactuInstance = ref(null);

    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const reloadPage = () => {
      console.log("reload");
      document.location.reload(true);
    }

    const getDjangoData = () => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          let dj2 = {};
          const djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
          
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }

          dj2.invoice = dj2?.invoice?.length > 0 ? dj2.invoice[0] : {};
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj2.seller_vat = dj2?.seller_vat?.length > 0 ? dj2.seller_vat : [{
            "pk": dj2.seller.pk,
            "seller": dj2.seller.pk,
            "vat_country": "ES",
            "vat_number": "ES" + dj2.seller.nif_registration,
            "vat_vies": false,
          }];

          dj2.providers.map((obj) => {
            obj['longname'] = obj.nif_cif_iva ? obj.name + " ( " + obj.nif_cif_iva + " ) " : obj.name;
            return obj;
          });

          dj2.customers.map((obj) => {
            obj['longname2'] = obj.nif_cif_iva ? obj.name + " ( " + obj.nif_cif_iva + " ) " : obj.name;
            return obj;
          })

          dj2.rent.map((obj) => {
            dj2.providers.filter((obj2) => {
              if (obj2.pk == obj.provider) {
                obj['longname'] = obj2.nif_cif_iva ? obj2.name + " ( " + obj2.nif_cif_iva + " ) " : obj2.name;
              }
            })
            return obj;
          })

          dj.value = dj2;
          console.log('DJ.value: ', dj.value);
          loadInputsData();
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      console.log("DJ values en catch", dj.value);
    };

    const setDefaultData = () => {
      const inv = dj?.value?.invoice ? dj.value.invoice : [];
      const status = inv.status ? inv.status : inputInvoiceStatus.value;
      if (status && status != 'revised') {

      }
    }

    const loadInputsData = () => {
      const seller = dj?.value?.seller ? dj.value.seller : [];
      const inv = dj?.value?.invoice ? dj.value.invoice : [];
      const concepts = dj?.value?.concepts ? dj.value.concepts : [];
      const payroll_w = dj?.value?.payroll_worker ? dj.value.payroll_worker[0] : [];

      inputInvoiceCategory.value = inv.invoice_category ? inv.invoice_category : inputInvoiceCategory.value;
      inputInvoiceType.value = inv.invoice_type ? inv.invoice_type : inputInvoiceCategory.value;
      inputCustomer.value = inv.customer ? inv.customer : inputCustomer.value;
      inputProvider.value = inv.provider ? inv.provider : inputProvider.value;
      inputRent.value = inv.seller_rental ? inv.seller_rental : inputRent.value;

      inputWorker.value = payroll_w.worker ? payroll_w.worker : inputWorker.value;
      inputWorkerType.value = getWorkerById(inputWorker.value)?.worker_type ? ['1', '2'].includes(getWorkerById(inputWorker.value).worker_type) : inputWorkerType.value;
      // Cargar valores de salario base y salario en especie
      inputSalaryBase.value = payroll_w.base_salary ? payroll_w.base_salary : 0;
      inputSalaryInKind.value = payroll_w.salary_in_kind ? payroll_w.salary_in_kind : 0;
      // Cargar total devengado (Este valor ahora se calculará automáticamente, pero es bueno cargar el valor guardado si existe)
      inputTotalAccrued.value = payroll_w.total_accrued ? payroll_w.total_accrued : 0;
      inputPercentageIRPF.value = payroll_w.percentage_irpf ? payroll_w.percentage_irpf : inputPercentageIRPF.value
      inputIncomeIRPF.value = payroll_w.income_tax_withholding ? payroll_w.income_tax_withholding : inputIncomeIRPF.value;
      inputNetPay.value = payroll_w.net_pay ? payroll_w.net_pay : inputNetPay.value;
      inputWorkerSocialSecurity.value = payroll_w.worker_social_security ? payroll_w.worker_social_security : inputWorkerSocialSecurity.value;
      inputCompanySocialSecurity.value = payroll_w.company_social_security ? payroll_w.company_social_security : inputCompanySocialSecurity.value;
      inputTotalPayroll.value = payroll_w.total ? payroll_w.total : inputTotalPayroll.value;
      inputTotalTotalPayroll.value = inputTotalPayroll.value - inputIncomeIRPF.value;
      inputTaxCountry.value = inv.tax_country ? inv.tax_country : 'ES';
      inputTaxResponsibility.value = inv.tax_responsibility ? inv.tax_responsibility : inputTaxResponsibility.value;
      inputOutOfTime.value = inv.out_of_time ?? false;
      inputInvoiceNumber.value = inv.reference ? inv.reference : inputInvoiceNumber.value;
      inputInvoiceNumberAbsolute.value = seller.pk && inv.reference ? seller.pk + ";" + inv.reference : inputInvoiceNumberAbsolute.value;
      inputInvoiceNumberAbsolute.value = inv.reference_absolute ? inv.reference_absolute : inputInvoiceNumberAbsolute.value;
      inputInvoiceDate.value = inv.invoice_date ? inv.invoice_date : inputInvoiceDate.value;
      inputInvoiceDate.value = inv.expedition_date ? inv.expedition_date : inputInvoiceDate.value;
      inputInvoiceAccountingDate.value = inputInvoiceDate.value ? inputInvoiceDate.value : inputInvoiceAccountingDate.value;
      inputInvoiceAccountingDate.value = inv.accounting_date ? inv.accounting_date : inputInvoiceAccountingDate.value;
      inputIsRectifying.value = inv.is_rectifying ? inv.is_rectifying : inputIsRectifying.value;
      inputTypePayroll.value = payroll_w.type_payroll ? payroll_w.type_payroll : inputTypePayroll.value;
      inputCurrency.value = inv.currency ? inv.currency : inputCurrency.value;
      inputIsDistanceSell.value = inv.is_oss ? inv.is_oss : inputIsDistanceSell.value;
      inputIsEquivalentTax.value = inv.is_eqtax ? inv.is_eqtax : inputIsEquivalentTax.value;
      inputIsReverseCharge.value = inv.is_reverse_charge ? inv.is_reverse_charge : inputIsReverseCharge.value;
      inputDepartureCountry.value = inv.departure_country ? inv.departure_country : inputDepartureCountry.value;
      inputAccountSales.value = inv.account_sales ? inv.account_sales : inputAccountSales.value;
      inputAccountExpenses.value = inv.account_expenses ? inv.account_expenses : inputAccountExpenses.value;
      inputAccountExpensesAccrued.value = '640';
      inputAccountExpensesWorker.value = '642';
      inputAccountExpensesCompany.value = '642';
      inputMarketplace.value = inv.marketplace ? inv.marketplace : inputMarketplace.value;
      inputEconomicActivity.value = inv.iae ? inv.iae : inputEconomicActivity.value;
      inputInvoiceTags.value = inv.tags ? inv.tags : inputInvoiceTags.value;
      inputInvoiceNotes.value = inv.notes ? inv.notes : inputInvoiceNotes.value;
      inputInvoiceNotesPrivate.value = inv.notes_private ? inv.notes_private : inputInvoiceNotesPrivate.value;
      inputInvoiceName.value = inv.name ? inv.name : (inv.file ? inv.file.replace("uploads/", "") : inputInvoiceName.value);
      inputInvoiceStatus.value = inv.status ? inv.status : inputInvoiceStatus.value;
      inputInvoiceStatusOriginal.value = inputInvoiceStatus.value ? inputInvoiceStatus.value : inputInvoiceStatusOriginal.value;
      inputIsGeneratedAmz.value = inv.is_generated_amz ? inv.is_generated_amz : inputIsGeneratedAmz.value;
      inputRelatedInvoice.value = inv.related_invoice ? inv.related_invoice : inputRelatedInvoice.value;
      inputDiscardReason.value = inv.discard_reason ? inv.discard_reason : inputDiscardReason.value;
      inputDiscardReasonNotes.value = inv.discard_reason_notes ? inv.discard_reason_notes : inputDiscardReasonNotes.value;
      // inputTransactionType.value = inv.transaction_type ? inv.transaction_type : inputTransactionType.value;
      // inputOperationType.value = inv.operation_type ? inv.operation_type : inputOperationType.value;
      showCategory.value = inputInvoiceCategory.value ? false : true;
      inputIsPostponedVat.value = inv.is_postponed_import_vat ? inv.is_postponed_import_vat : inputIsPostponedVat.value;
      veriFactuInstance.value = dj.value.verifactuInvoices[0] ? dj.value.verifactuInvoices[0] : null;
      if (inputInvoiceType.value == 'import-dua' && seller.contracted_accounting == true) {
        const invrelprov = getRelatedInvoiceProvider()?.length > 0 ? getRelatedInvoiceProvider()[0] : null;
        inputProvider.value = invrelprov && invrelprov.pk ? invrelprov.pk : inputProvider.value;
      }

      if (inputInvoiceType.value == 'import-expenses' && seller.contracted_accounting == true) {
        const invrel = getRelatedInvoiceById(inputRelatedInvoice.value);
        const invrelrel = invrel.related_invoice ? getRelatedInvoiceById(invrel.related_invoice) : null;
        const invrelprov = getRelatedInvoiceProvider()?.length > 0 ? getRelatedInvoiceProvider()[0] : null;
        inputProvider.value = invrelprov && invrelprov.pk ? invrelprov.pk : inputProvider.value;
        inputInvoiceDate.value = invrel && invrel.expedition_date ? invrel.expedition_date : inputInvoiceDate.value;
        inputInvoiceNumber.value = invrelrel && invrelrel.reference ? invrelrel.reference + "-PLUS" : inputInvoiceNumber.value;
        inputAccountExpenses.value = inv.account_expenses ? inv.account_expenses : '631';
      }

      if (inputInvoiceType.value == 'import-invoice') {
        inputAccountExpenses.value = '600';
      }

      if (inputInvoiceType.value == 'self-employment-fee' || inputInvoiceType.value == 'payroll') {
        if (inputInvoiceDate.value && inputInvoiceDate.value != null) {
          const split = inputInvoiceDate.value.split("-");
          if (split && split.length > 1) {
            inputMonths.value = parseInt(split[1]);
            inputYears.value = parseInt(split[0]);
          }
        }
      }

      for (const con of concepts) {
        console.log("Conceptos: ", con);
        if (inputInvoiceType.value == 'payroll') {
          addConceptPayroll({
            concept: con.concept || "",
            percentage: con.percentage || "100",
            quantity: con.quantity || "1",
            vat: con.vat || "0",
            vat_currency: con.vat_currency || "0",
            vat_euros: con.vat_euros || "0",
            irpf: con.irpf || "0",
            irpf_currency: con.irpf_curency || "0",
            irpf_euros: con.irpf_euros || "0",
            eqtax: con.eqtax || "0",
            eqtax_currency: con.eqtax_curency || "0",
            eqtax_euros: con.eqtax_euros || "0",
            amount_original: con.amount_original || "0",
            amount_currency: con.amount_currency || "0",
            amount_euros: con.amount_euros || "0",
            total_currency: con.total_currency || "0",
            total_euros: con.total_euros || "0",
          });
        } else {
          addConcept({
            concept: con.concept || "",
            percentage: con.percentage || "100",
            quantity: con.quantity || "1",
            vat: con.vat || "0",
            vat_currency: con.vat_currency || "0",
            vat_euros: con.vat_euros || "0",
            irpf: con.irpf || "0",
            irpf_currency: con.irpf_curency || "0",
            irpf_euros: con.irpf_euros || "0",
            eqtax: con.eqtax || "0",
            eqtax_currency: con.eqtax_curency || "0",
            eqtax_euros: con.eqtax_euros || "0",
            amount_original: con.amount_original || "0",
            amount_currency: con.amount_currency || "0",
            amount_euros: con.amount_euros || "0",
            total_currency: con.total_currency || "0",
            total_euros: con.total_euros || "0",
            is_supplied: con.is_supplied || false,
          });
        }

      }
      inputConceptsOld.value = [];
      for (const con of inputConcepts.value) {
        inputConceptsOld.value.push({...con});
      }
    }

    const getCountryByCode = (code = "") => {
      const country = dj.value.countries.filter(co => {
        if (code) {
          return co.pk.toUpperCase() == code.toUpperCase();
        } else {
          return false;
        }
      })[0];
      return country;
    }

    const getCountryNameByCode = (code = "") => {
      const country = getCountryByCode(code);
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    };

    const getStatusBg = (code = "") => {
      let bg = "#FFC107";
      if (code == "revised") {
        bg = "#4CAF50";
      } else if (code == "discard") {
        bg = "#B71C1C";
      }
      return bg;
    };

    const getStatusByCode = (code = "") => {
      const stat = dj.value.status.filter(st => st.pk.trim().toUpperCase() == code.trim().toUpperCase())[0];
      const description = stat?.description ? stat?.description : code;
      return description;
    };

    const getCategoryByCode = (code = "") => {
      const category = dj.value.categories.filter(cat => cat?.pk?.trim()?.toUpperCase() == code?.trim()?.toUpperCase())[0];
      // const description = category?.description ? category?.description : code;
      return category;
    };

    const getTypeByCode = (code = "") => {
      const type = dj.value.types.filter(type => type?.pk?.trim()?.toUpperCase() == code?.trim()?.toUpperCase())[0];
      return type;
    };

    const getTypesForCategory = () => {
      let types = [];
      if (inputInvoiceCategory) {
        const category = inputInvoiceCategory?.value?.trim()?.toUpperCase();
        types = dj.value.types.filter(type => type?.category?.trim()?.toUpperCase() == category);
      }
      return types;
    }

    const getCustomerById = (id) => {
      let r = {};
      if (id && id != null) {
        r = dj.value.customers.filter(element => element.pk == id)[0];
        r.is_individual_customer = r.name.trim().toUpperCase().includes("CLIENTES") &&  r.name.trim().toUpperCase().includes("PARTICULARES");
      }
      return r;
    };

    const getProviderById = (id) => {
      let r = {};
      if (id && id != null) {
        r = dj.value.providers.filter(element => element.pk == id)[0];
      }
      return r;
    };

    const getRentByID = (id) => {
      let r = {};
      if (id && id != null) {
        r = dj.value.rent.filter(element => element.pk == id)[0];
      }
      return r;
    };

    const getWorkerById = (id) => {
      let r = {};
      if (id && id != null) {
        r = dj.value.workers.filter(element => element.pk == id)[0];
      }
      return r;
    };

    const getRelatedInvoiceProvider = () => {
      let providers = [];
      const seller = dj.value.seller;
      if (seller.contracted_accounting == true) {
        console.log("inputRelatedInvoice: ", inputRelatedInvoice.value);
        const relinv = getRelatedInvoiceById(inputRelatedInvoice.value);
        console.log("relinv: ", relinv);
        const relprov = getProviderById(relinv.provider);
        console.log("relprov: ", relprov);
        if (relprov && relprov != null && relprov?.pk != null) {
          providers.push(relprov);
        }
      } else {
        providers = dj.value.providers;
      }
      console.log("getRelatedInvoiceProvider: ", providers);
      return providers;
    };

    const getRelatedInvoiceById = (id) => {
      let r = {};
      if (id && id != null) {
        const imports = dj.value.import_invoices_not_related.filter(element => element.pk == id);
        if (imports && imports.length > 0) {
          r = imports[0];
        } else {
          const duas = dj.value.dua_invoices_not_related.filter(element => element.pk == id);
          if (duas && duas.length > 0) {
            r = duas[0];
          } else {
            const related = dj.value.invoice_relations.filter(element => element.pk == id);
            if (related && related.length > 0) {
              r = related[0];
            }
          }
        }
      }
      return r;
    };

    const setCustomerAccount = () => {
      const customer = getCustomerById(inputCustomer.value);
      if (customer && customer?.account_sales) {
        inputAccountSales.value = customer.account_sales
      }
      calcTransactionType();
      calcOperationType();
    }

    const setProviderAccount = () => {
      const provider = getProviderById(inputProvider.value);
      if (provider && provider?.account_expenses) {
        inputAccountExpenses.value = provider.account_expenses;
      }
      console.log("provider: ", provider);
      console.log("inputAccountExpenses: ", inputAccountExpenses.provider);
      calcTransactionType();
      calcOperationType();
    }

    const calcTransactionType = () => {
      let transactionType = null;
      const customer = getCustomerById(inputCustomer.value);
      const customerCountry = getCountryByCode(customer?.country);

      const provider = getProviderById(inputProvider.value);
      const providerCountry = (provider && provider?.is_origin_country == false)
        ? getCountryByCode(provider?.nif_cif_iva_country)
        : getCountryByCode(provider?.country);

      const taxCountry = inputTaxCountry.value?.toString().toUpperCase().trim();
      const sellerTaxCountry = getCountryByCode(taxCountry);

      const vatCurrency = getTotal().vatCurrency || 0;
      const vatEuros = getTotal().vatEuros || 0;

      if (inputInvoiceCategory.value == "sales" && inputInvoiceType.value == "sales") {
        const is_concept_without_iva = inputConcepts.value.every(con => con.vat <= 0);
        const zipCodePrefixes = ['35', '38', '51', '52'];
        const is_zip_code_canaria_ceuta_melilla = zipCodePrefixes.some(prefix => customer?.zip?.startsWith(prefix));
        if (sellerTaxCountry?.pk == customerCountry?.pk) {
          // Caso Normal
          if (inputIsRectifying.value == true) {
            transactionType = "local-refund";
          } else {
            transactionType = "local-sale";
          }

          // Canarias, Ceuta, Melilla
          if (sellerTaxCountry.pk == 'ES' && is_zip_code_canaria_ceuta_melilla && is_concept_without_iva) {
            if (inputIsRectifying.value == true) {
              transactionType = "export-refund";
            } else {
              transactionType = "export-sale";
            }
          }
        }

        if (sellerTaxCountry?.pk != customerCountry?.pk && customerCountry?.is_european_union == true) {
          if (customer.vies && customer.vies.trim().length > 0 && vatCurrency == 0) {
            if (customer?.customer_type?.toString().toUpperCase() == 'B2B') {
              if (inputIsRectifying.value == true) {
                transactionType = "intra-community-refund";
              } else {
                transactionType = "intra-community-sale";
              }
            } else {
              if (inputIsRectifying.value == true) {
                transactionType = "local-refund";
              } else {
                transactionType = "local-sale";
              }
            }
          } else {
            if (inputIsRectifying.value == true) {
              transactionType = "local-refund";
            } else {
              transactionType = "local-sale";
            }
          }
          // if(customer?.customer_type.toString().toUpperCase() == 'B2C'){
          //   if (inputIsRectifying.value == true) {
          //     transactionType = "local-refund";
          //   } else {
          //     transactionType = "local-sale";
          //   }
          // }

          // if(customer?.customer_type.toString().toUpperCase() == 'B2B'){
          //   if (customer.vies && customer.vies.trim().length > 0 && vatCurrency == 0 ) {
          //     if (inputIsRectifying.value == true) {
          //       transactionType = "intra-community-refund";
          //     } else {
          //       transactionType = "intra-community-sale";
          //     }
          //   } else {
          //     if (inputIsRectifying.value == true) {
          //       transactionType = "local-refund";
          //     } else {
          //       transactionType = "local-sale";
          //     }
          //   }
          // }
        }

        if (sellerTaxCountry?.pk != customerCountry?.pk && customerCountry?.is_european_union == false) {
          if (inputIsRectifying.value == true) {
            transactionType = "export-refund";
          } else {
            transactionType = "export-sale";
          }
        }

        if (inputIsDistanceSell.value == true) {
          if (inputIsRectifying.value == true) {
            transactionType = "oss-refund";
          } else {
            transactionType = "oss";
          }
        }
      }

      if (inputInvoiceCategory.value == "expenses" && (inputInvoiceType.value == "expenses" || inputInvoiceType.value == "rent")) {
        if (sellerTaxCountry?.pk == providerCountry?.pk) {
          if (inputIsRectifying.value == true) {
            transactionType = "local-credit";
          } else {
            transactionType = "local-expense";
          }
        }

        if (sellerTaxCountry?.pk != providerCountry?.pk && providerCountry?.is_european_union == true) {
          if (provider.vies && provider.vies.trim().length > 0 && vatCurrency == 0 && providerCountry?.pk != 'GB') {
            if (inputIsRectifying.value == true) {
              transactionType = "intra-community-credit";
            } else {
              transactionType = "intra-community-expense";
            }
          } else if (providerCountry?.pk == 'GB') {
            if (inputIsRectifying.value == true) {
              transactionType = "extra-credit";
            } else {
              transactionType = "extra-expense";
            }
          } else {
            if (inputIsRectifying.value == true) {
              transactionType = "local-credit";
            } else {
              transactionType = "local-expense";
            }
          }
        }

        if (sellerTaxCountry?.pk != providerCountry?.pk && providerCountry?.is_european_union == false) {
          const nif_cif_iva = (provider && provider?.nif_cif_iva) ? provider.nif_cif_iva.trim().toUpperCase() : null;
          if (nif_cif_iva && (nif_cif_iva.startsWith('EU') || nif_cif_iva.startsWith('IM')) && providerCountry?.pk != 'GB') {
            if (inputIsRectifying.value == true) {
              transactionType = "local-credit";
            } else {
              transactionType = "local-expense";
            }
          } else {
            if (inputIsRectifying.value == true) {
              transactionType = "extra-credit";
            } else {
              transactionType = "extra-expense";
            }
          }
        }
      }

      if (inputInvoiceCategory.value == "expenses" && (inputInvoiceType.value == "ticket" ||
        inputInvoiceType.value == "self-employment-fee") || inputInvoiceType.value == "payroll") {
        transactionType = "local-expense";
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "import-invoice") {
        transactionType = "import-invoice";
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "import-dua") {
        transactionType = "import-dua";
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "import-expenses") {
        transactionType = "import-expenses";
      }

      transactionType = transactionType ? transactionType.toString().trim().toLowerCase() : transactionType;

      inputTransactionType.value = transactionType;
      return transactionType;
    }

    const calcOperationType = () => {
      let operationType = null;
      const transactionType = inputTransactionType.value?.toString().toUpperCase().trim();
      const taxCountry = inputTaxCountry.value?.toString().toUpperCase().trim();
      const accountExpenses = inputAccountExpenses.value?.toString().toUpperCase().trim();

      if (inputInvoiceCategory.value == "sales" && inputInvoiceType.value == "sales") {
        if (taxCountry && taxCountry == "ES") {
          if (transactionType == "LOCAL-SALE" || transactionType == "LOCAL-REFUND") {
            operationType = 1;
          }
          if (transactionType == "INTRA-COMMUNTY-SALE" || transactionType == "INTRA-COMMUNTY-REFUND") {
            operationType = 3;
          }
          if (transactionType == "EXPORT-SALE" || transactionType == "EXPORT-REFUND") {
            operationType = 6;
          }
        }

        if (taxCountry && taxCountry != "ES") {
          operationType = 7;
        }

        if (transactionType == "OSS" || transactionType == "OSS-REFUND") {
          operationType = 7;
        }
      }

      if (inputInvoiceCategory.value == "expenses" && inputInvoiceType.value == "expenses") {
        if (taxCountry && taxCountry == "ES") {
          if (transactionType == "LOCAL-EXPENSE" || transactionType == "LOCAL-CREDIT") {
            operationType = 1;
          }
          if (transactionType == "INTRA-COMMUNTY-EXPENSE" || transactionType == "INTRA-COMMUNTY-CREDIT") {
            if (accountExpenses && accountExpenses == '700') {
              operationType = 3;
            }
            if (accountExpenses && accountExpenses == '705') {
              operationType = 8;
            }
          }
        }

        if (taxCountry && taxCountry != "ES") {
          operationType = 7;
        }
      }

      if (inputInvoiceCategory.value == "expenses" && (inputInvoiceType.value == "ticket" ||
        inputInvoiceType.value == "self-employment-fee")) {
        operationType = 7;
      }

      return operationType;
    }

    const convertToEuros = (value, currency, date) => {
      let total = to_decimal_value(0);

      const dec_value = to_decimal_value(value);

      // Currency => Make Conversion
      if (!isNaN(value) && currency != 'EUR') {
        {#total = value * taxConversion.value;#}
        total = dec_value.times(to_decimal_value(taxConversion.value));
      }

      // EUROS => No Conversion
      if (!isNaN(value) && currency == 'EUR') {
        total = dec_value
      }

      total = total.toFixed(4);

      return total;
    };

    const getRectifyingAmount = (amount, force = false) => {
      let r = amount;
      if (inputIsRectifying.value == true && amount > 0) {
        r = amount * -1;
      } else if (inputIsRectifying.value == false && amount <= 0) {
        if (force) {
          r = amount * -1;
        }
      }
      return r;
    }

    const getConceptTotal = (amount, vatPercentage, irpfPercentage, conceptPecentage, quantity = 1, eqtaxPercentage = null) => {
      let total = to_decimal_value(0);

      const dec_amount = to_decimal_value(amount);
      const dec_vatPercentage = to_decimal_value(vatPercentage);
      const dec_irpfPercentage = to_decimal_value(irpfPercentage);
      const dec_conceptPecentage = to_decimal_value(conceptPecentage);
      const dec_quantity = to_decimal_value(quantity);
      const dec_hundred = to_decimal_value(100);

      // Amount
      if (!isNaN(Number(amount))) {
        if (inputInvoiceType.value != 'import-dua') {
          {#total += Number(amount);#}
          total = total.plus(dec_amount);
        }
      }

      // Amount VAT
      if (!isNaN(Number(amount)) && !isNaN(Number(vatPercentage))) {
        {#total += (Number(amount) * Number(vatPercentage) / 100);#}
        total = total.plus(dec_amount.times(dec_vatPercentage).dividedBy(dec_hundred));
      }

      // EqTax
      if (!isNaN(Number(amount)) && !isNaN(Number(vatPercentage))) {
        if (inputIsEquivalentTax.value == true) {
          let concepteqtax = 0;
          if (eqtaxPercentage == null) {
            const conceptvat = vatPercentage;
            if (conceptvat == 21) {
              concepteqtax = 5.2;
            } else if (conceptvat == 10) {
              concepteqtax = 1.4;
            } else if (conceptvat == 4) {
              concepteqtax = 0.5;
            } else {
              concepteqtax = 0;
            }
          } else {
            concepteqtax = eqtaxPercentage;
          }
          const dec_concepteqtax = to_decimal_value(concepteqtax);
          {% comment %} //total += (Number(amount) * Number(concepteqtax) / 100); {% endcomment %}
          total = total.plus(dec_amount.times(dec_concepteqtax).dividedBy(dec_hundred));
        }
      }

      // Amount IRPF
      if (!isNaN(Number(amount)) && !isNaN(Number(irpfPercentage) && irpfPercentage > 0 && irpfPercentage <= 100)) {
        {#total += (Number(amount) * Number(irpfPercentage) * -1 / 100);#}
        total = total.plus(dec_amount.times(dec_irpfPercentage).dividedBy(dec_hundred).times(to_decimal_value(-1)));
      }

      // Quantity
      if (!isNaN(Number(quantity))) {
        {#total *= Number(quantity);#}
        total = total.times(dec_quantity);
      } else {
        // total = 0;
      }

      // Round 2 Decimals
      total = total.toFixed(4);

      return total;
    };

    const getTotal = () => {
      let total = {
        amountCurrency: to_decimal_value(0),
        amountSupliedCurrency: to_decimal_value(0),
        amountCurrencyWithoutSuplied: to_decimal_value(0),
        vatCurrency: to_decimal_value(0),
        irpfCurrency: to_decimal_value(0),
        eqtaxCurrency: to_decimal_value(0),
        totalCurrency: to_decimal_value(0),

        amountEuros: to_decimal_value(0),
        amountSupliedEuros: to_decimal_value(0),
        amountEurosWithoutSuplied: to_decimal_value(0),
        vatEuros: to_decimal_value(0),
        irpfEuros: to_decimal_value(0),
        eqtaxEuros: to_decimal_value(0),
        totalEuros: to_decimal_value(0),
      };

      let concepts = inputConcepts.value;
      if (inputInvoiceType.value == 'payroll') {
        concepts = inputConceptsPayroll.value;
      }

      for (const concept of concepts) {
        const qty = to_decimal_value(concept.quantity);

        // CURRENCY
        {#total.amountCurrency += concept.amount_currency ? (Number(concept.amount_currency) * qty) : 0;#}
        if (concept.amount_currency) {
          total.amountCurrency = to_decimal_value(concept.amount_currency)
            .times(qty)
            .plus(total.amountCurrency);
          if (concept.is_supplied && concept.is_supplied == true) {
            total.amountSupliedCurrency = to_decimal_value(concept.amount_currency)
              .times(qty)
              .plus(total.amountSupliedCurrency);
          }
        }

        {#total.vatCurrency += concept.amount_currency && concept.vat ? (Number(concept.amount_currency) * Number(concept.vat) * qty / 100) : 0;#}
        if (concept.vat) {
          if (concept.amount_original && inputInvoiceType.value == 'import-dua') {
            total.vatCurrency = to_decimal_value(concept.amount_original)
            .times(to_decimal_value(concept.vat))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.vatCurrency)
          } else if (concept.amount_currency ) {
            total.vatCurrency = to_decimal_value(concept.amount_currency)
            .times(to_decimal_value(concept.vat))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.vatCurrency)
          }        
        }

        {#total.irpfCurrency += concept.amount_currency && concept.irpf ? (Number(concept.amount_currency) * Number(concept.irpf) * qty / 100) : 0;#}
        if (concept.amount_currency && concept.irpf) {
          total.irpfCurrency = to_decimal_value(concept.amount_currency)
            .times(to_decimal_value(concept.irpf))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.irpfCurrency)
        }

        {#total.eqtaxCurrency += concept.amount_currency && concept.eqtax ? (Number(concept.amount_currency) * Number(concept.eqtax) * qty / 100) : 0;#}
        if (concept.eqtax) {
          if (concept.amount_original && inputInvoiceType.value == 'import-dua') {
            total.eqtaxCurrency = to_decimal_value(concept.amount_original)
            .times(to_decimal_value(concept.eqtax))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.eqtaxCurrency)
          } else if (concept.amount_currency && concept.eqtax) {
            total.eqtaxCurrency = to_decimal_value(concept.amount_currency)
              .times(to_decimal_value(concept.eqtax))
              .times(qty)
              .dividedBy(to_decimal_value(100))
              .plus(total.eqtaxCurrency)
          }
        }

        // EUROS
        {#total.amountEuros += concept.amount_euros#}
        {#    ? (Number(concept.amount_euros) * qty) : 0;#}


        if (concept.amount_euros) {
          total.amountEuros = to_decimal_value(concept.amount_euros)
            .times(qty)
            .plus(total.amountEuros);
          if (concept.is_supplied && concept.is_supplied == true) {
            total.amountSupliedEuros = to_decimal_value(concept.amount_euros)
              .times(qty)
              .plus(total.amountSupliedEuros);
          }
        }

        {#total.vatEuros += concept.amount_euros && concept.vat#}
        {#    ? (Number(concept.amount_euros) * Number(concept.vat) * qty / 100) : 0;#}

        if (concept.amount_euros && concept.vat) {
          if (inputInvoiceType.value == 'import-dua') {
            total.vatEuros = to_decimal_value(concept.amount_original_euros)
            .times(to_decimal_value(concept.vat))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.vatEuros)   
          } else {
            total.vatEuros = to_decimal_value(concept.amount_euros)
            .times(to_decimal_value(concept.vat))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.vatEuros)   
          }       
  
        }

        {#total.irpfEuros += concept.amount_euros && concept.irpf#}
        {#    ? (Number(concept.amount_euros) * Number(concept.irpf) * qty / 100) : 0;#}


        if (concept.amount_euros && concept.irpf) {
          total.irpfEuros = to_decimal_value(concept.amount_euros)
            .times(to_decimal_value(concept.irpf))
            .times(qty)
            .dividedBy(to_decimal_value(100))
            .plus(total.irpfEuros)
        }

        {#total.eqtaxEuros += concept.amount_euros && concept.eqtax ? (Number(concept.amount_euros) * Number(concept.eqtax) * qty / 100) : 0;#}

        if (concept.eqtax) {
          if (concept.amount_original && inputInvoiceType.value == 'import-dua') {
            if (inputCurrency.value == 'EUR' && concept.amount_original != concept.amount_original_euros) {
              concept.amount_original_euros = concept.amount_original;
            }
            total.eqtaxEuros = to_decimal_value(concept.amount_original_euros)
              .times(to_decimal_value(concept.eqtax))
              .times(qty)
              .dividedBy(to_decimal_value(100))
              .plus(total.eqtaxEuros)
          } else if (concept.amount_euros && concept.eqtax) {
            total.eqtaxEuros = to_decimal_value(concept.amount_euros)
              .times(to_decimal_value(concept.eqtax))
              .times(qty)
              .dividedBy(to_decimal_value(100))
              .plus(total.eqtaxEuros)
          }
        }
      }
      total.amountCurrencyWithoutSuplied = total.amountCurrency.minus(total.amountSupliedCurrency);
      total.amountEurosWithoutSuplied = total.amountEuros.minus(total.amountSupliedEuros);

      {#total.totalCurrency = total.amountCurrency + total.vatCurrency + (total.irpfCurrency * -1) + total.eqtaxCurrency;#}
      {#total.totalEuros = total.amountEuros + total.vatEuros + (total.irpfEuros * -1) + total.eqtaxEuros;#}

      total.totalCurrency = total.amountCurrency
        .plus(total.vatCurrency)
        .plus(total.irpfCurrency.times(to_decimal_value(-1)))
        .plus(total.eqtaxCurrency);

      total.totalEuros = total.amountEuros
        .plus(total.vatEuros)
        .plus(total.irpfEuros.times(to_decimal_value(-1)))
        .plus(total.eqtaxEuros);


      {#total.totalCurrency = numberPrecision2(total.totalCurrency);#}
      {#total.amountCurrency = numberPrecision2(total.amountCurrency);#}
      {#total.vatCurrency = numberPrecision2(total.vatCurrency);#}
      {#total.irpfCurrency = numberPrecision2(total.irpfCurrency);#}
      {#total.eqtaxCurrency = numberPrecision2(total.eqtaxCurrency);#}
      {#total.totalEuros = numberPrecision2(total.totalEuros);#}
      {#total.amountEuros = numberPrecision2(total.amountEuros);#}
      {#total.vatEuros = numberPrecision2(total.vatEuros);#}
      {#total.irpfEuros = numberPrecision2(total.irpfEuros);#}
      {#total.eqtaxEuros = numberPrecision2(total.eqtaxEuros);#}

      total.totalCurrency = total.totalCurrency.toFixed(4);
      total.amountCurrency = total.amountCurrency.toFixed(4);
      total.vatCurrency = total.vatCurrency.toFixed(4);
      total.irpfCurrency = total.irpfCurrency.toFixed(4);
      total.eqtaxCurrency = total.eqtaxCurrency.toFixed(4);
      total.totalEuros = total.totalEuros.toFixed(4);
      total.amountEuros = total.amountEuros.toFixed(4);
      total.vatEuros = total.vatEuros.toFixed(4);
      total.irpfEuros = total.irpfEuros.toFixed(4);
      total.eqtaxEuros = total.eqtaxEuros.toFixed(4);

      return total;
    }

    const conceptDefaultValues = (concept) => {
      // Percentage
      if (concept?.percentage == null || concept?.percentage == undefined || concept?.percentage == "" || isNaN(concept.percentage)) {
        concept.percentage = 100;
      } else if (!isNaN(concept.percentage) && Number(concept.percentage) > 100) {
        concept.percentage = 100;
      } else if (!isNaN(concept.percentage) && Number(concept.percentage) < 0) {
        concept.percentage = Number(concept.percentage) * -1;
      }
      // Quantity
      if (concept?.quantity == null || concept?.quantity == undefined || concept?.quantity == "" || isNaN(concept.quantity)) {
        concept.quantity = 1;
      } else if (!isNaN(concept.quantity) && Number(concept.quantity) < 0) {
        concept.quantity = Number(concept.quantity) * -1;
      }
      // VAT
      if (concept?.vat == null || concept?.vat == undefined || concept?.vat == "" || isNaN(concept.vat)) {
        concept.vat = 0;
      } else if (!isNaN(concept.vat) && Number(concept.vat) < 0) {
        concept.vat = Number(concept.vat) * -1;
      }
      // IRPF
      if (concept?.irpf == null || concept?.irpf == undefined || concept?.irpf == "" || isNaN(concept.irpf)) {
        concept.irpf = 0;
      } else if (!isNaN(concept.irpf) && Number(concept.irpf) < 0) {
        concept.irpf = Number(concept.irpf) * -1;
      }
      // EQTAX
      if (concept?.eqtax == null || concept?.eqtax == undefined || concept?.eqtax == "" || isNaN(concept.eqtax)) {
        concept.eqtax = 0;
      } else if (!isNaN(concept.eqtax) && Number(concept.eqtax) < 0) {
        concept.eqtax = Number(concept.eqtax) * -1;
      }
      // Amount (Base Imponible)
      if (
        concept?.amount_currency == null ||
        concept?.amount_currency == undefined ||
        concept?.amount_currency == "" ||
        isNaN(concept.amount_currency)
      ) {
        concept.amount_currency = 0;
      }

      // Amount Original (Base Imponible)
      if (
        concept?.amount_original == null ||
        concept?.amount_original == undefined ||
        concept?.amount_original == "" ||
        concept?.amount_original == "-" ||
        isNaN(concept.amount_original)
      ) {
        concept.amount_original = 0;
      }
    }

    const addConceptSuplied = (concept = null) => {
      if (concept == null) {
          concept = {
          concept: "",
          percentage: 100,
          quantity: 1,
          vat: 0,
          vat_currency: 0,
          vat_euros: 0,
          irpf: 0,
          irpf_currency: 0,
          irpf_euros: 0,
          eqtax: 0,
          eqtax_currency: 0,
          eqtax_euros: 0,
          amount_original: 0,
          amount_currency: 0,
          amount_euros: 0,
          total_currency: 0,
          total_euros: 0,
          is_supplied: true
        };
      } else {
        addConcept(concept);
      }

      inputConcepts.value.push(concept);
      onChangeAmountCurrency(concept);
    }

    const addConcept = (concept = null) => {
      if (concept == null) {
        concept = {
          concept: "",
          percentage: 100,
          quantity: 1,
          vat: 0,
          vat_currency: 0,
          vat_euros: 0,
          irpf: 0,
          irpf_currency: 0,
          irpf_euros: 0,
          eqtax: 0,
          eqtax_currency: 0,
          eqtax_euros: 0,
          amount_original: 0,
          amount_currency: 0,
          amount_euros: 0,
          total_currency: 0,
          total_euros: 0
        };
      } 
      // si el seller es autonomo y la cuenta seleccionada es la 628, se le asigna el valor por defecto del porcentaje de afeccion
      if (dj.value.seller.legal_entity == "self-employed" && inputAccountExpenses.value == 628) {
        concept.percentage = dj.value.seller.percentage_affected_activity;
      }

      inputConcepts.value.push(concept);
      onChangeAmountCurrency(concept);
    };

    const addConceptPayroll = (concept = null) => {
      if (concept == null) {
        concept = {
          concept: "",
          percentage: 100,
          quantity: 1,
          vat: 0,
          vat_currency: 0,
          vat_euros: 0,
          irpf: 0,
          irpf_currency: 0,
          irpf_euros: 0,
          eqtax: 0,
          eqtax_currency: 0,
          eqtax_euros: 0,
          amount_original: 0,
          amount_currency: 0,
          amount_euros: 0,
          total_currency: 0,
          total_euros: 0,
        };
      }
      inputConceptsPayroll.value.push(concept);
      onChangeAmountCurrency(concept);
    };

    const removeConcept = (index) => {
      inputConcepts.value.splice(index, 1);
    };

    const recalcTotalConcepts = () => {
      for (const concept of inputConcepts.value) {
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcCurrencyConcepts = () => {
      for (const concept of inputConcepts.value) {
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcIVA0Concepts = () => {
      for (const concept of inputConcepts.value) {
        concept.vat = 0;
        concept.vat_currency = 0;
        concept.vat_euros = 0;
        concept.eqtax = 0;
        concept.eqtax_currency = 0;
        concept.eqtax_euros = 0;
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcIVA21Concepts = () => {
      for (const concept of inputConcepts.value) {
        concept.vat = 21;
        concept.vat_currency = concept.amount_currency * 0.21;
        concept.vat_euros = convertToEuros(concept.vat_currency, inputCurrency.value, inputInvoiceDate.value);
        if (inputIsEquivalentTax.value == true) {
          concept.eqtax = 5.2;
          concept.eqtax_currency = concept.amount_currency * 0.052;
          concept.eqtax_euros = convertToEuros(concept.eqtax_currency, inputCurrency.value, inputInvoiceDate.value);
        } else {
          concept.eqtax = 0;
          concept.eqtax_currency = 0;
          concept.eqtax_euros = 0;
        }
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcAllIvaConcepts = () => {
      let vat = parseInt(document.getElementById("inputSelectVat").value)
      for (const concept of inputConcepts.value) {
        concept.vat = vat;
        conceptDefaultValues(concept)
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    }

    const recalcAllConcepts = () => {
      for (const concept of inputConceptsOld.value) {
        concept.vat = -1;
      }

      let vat = 0;
      for (const concept of inputConcepts.value) {
        vat = concept.vat;
        concept.vat = -1;
        concept.vat = vat;
      }
    }

    const recalcJsonIVA = (concepts) => {
      let vats_euros = {};
      let vats_currency = {};
      let quantity = 0;
      if (concepts && concepts.length > 0)
        for (const concept of concepts) {
          if (concept.vat) {
            const vat_float = numberPrecision2(concept.vat);
            if (vat_float != 0.00) {
              if (!vats_euros[vat_float.toString()]) {
                vats_euros[vat_float.toString()] = 0.00;
              }
              if (!vats_currency[vat_float.toString()]) {
                vats_currency[vat_float.toString()] = 0.00;
              }
              if (concept.quantity) {
                quantity = parseFloat(concept.quantity.toString());
                if (isNaN(quantity)) {
                  quantity = 0.0;
                }
              }

              if (concept.vat && concept.amount_euros) {
                {#vats_euros[vat_float.toString()] += (parseFloat(concept.amount_euros.toString()) * vat_float * quantity / 100.0);#}
                vats_euros[vat_float.toString()] = to_decimal_value(concept.amount_euros)
                  .times(to_decimal_value(vat_float))
                  .times(to_decimal_value(quantity))
                  .dividedBy(to_decimal_value(100))
                  .plus(vats_euros[vat_float.toString()])
                  .toFixed(4)
              }

              if (concept.vat && concept.amount_currency) {
                {#vats_currency[vat_float.toString()] += (parseFloat(concept.amount_currency.toString()) * vat_float * quantity / 100.0);#}
                vats_currency[vat_float.toString()] = to_decimal_value(concept.amount_currency)
                  .times(to_decimal_value(vat_float))
                  .times(to_decimal_value(quantity))
                  .dividedBy(to_decimal_value(100))
                  .plus(vats_currency[vat_float.toString()])
                  .toFixed(4)
              }
            }
          }
        }

      // Redondear/Truncar los valores del array
      {% comment %}for (const key in vats_euros) {
                vats_euros[key] = numberPrecision2(vats_euros[key]);
            }
            for (const key in vats_currency) {
                vats_currency[key] = numberPrecision2(vats_currency[key]);
            }{% endcomment %}

      // Convertir el objeto en un array de pares clave-valor y ordenarlo por clave
      const arr_eur = Object.entries(vats_euros);
      const arr_cur = Object.entries(vats_currency);
      arr_eur.sort((a, b) => a[0] - b[0]);
      arr_cur.sort((a, b) => a[0] - b[0]);

      // Convertir el array ordenado de nuevo en un objeto
      vats_euros = Object.fromEntries(arr_eur);
      vats_currency = Object.fromEntries(arr_cur);

      // Actualizar los campos
      all_vats_euros.value = vats_euros;
      all_vats_currency.value = vats_currency;
      inputJsonVats.value = JSON.stringify(vats_euros);
      return vats_euros;
    }

    const deleteAllConcepts = () => {
      if (inputConcepts.value && inputConcepts.value.length > 0) {
        inputConcepts.value.splice(0, inputConcepts.value.length);
      }
      recalcJsonIVA();
    }

    const onChangeCategory = () => {
      inputInvoiceType.value = inputInvoiceCategory.value;
      inputAccountExpenses.value = null;
      inputAccountSales.value = null;
    }

    const findTgss = (list) => {
      if (list && list.length > 0) {
        return list.find((item) => item.nif_cif_iva === 'tgss')?.pk;
      }
      return 0;
    }

    const onChangeType = () => {
      if (inputInvoiceCategory && inputInvoiceType) {
        showCategory.value = false;
        if (inputInvoiceType.value == 'ticket' || inputInvoiceType.value == 'self-employment-fee') {
          for (let concept of inputConcepts.value) {
            concept.percentage = 100;
            concept.quantity = 1;
            concept.vat = 0;
            concept.irpf = 0;
            concept.eqtax = 0;
            concept.total_currency = concept.amount_currency;
            concept.total_euros = concept.amount_euros;
          }
          inputTotalCurrency.value = getTotal()?.totalCurrency || 0;
          inputTotalEuros.value = getTotal()?.totalEuros || 0;

          if (inputInvoiceType.value === 'self-employment-fee') {
            inputProvider.value = findTgss(dj.value.providers);
            if (dj.value.seller.legal_entity == 'sl') {
              inputAccountExpenses.value = 551;
            } else if (dj.value.seller.legal_entity == 'self-employed') {
              inputAccountExpenses.value = 642;
            }
          }
        }
        disable_tax_country(inputInvoiceAccountingDate.value);
        for (const concept of inputConcepts.value) {
          conceptDefaultValues(concept);
          onChangeAmountCurrency(concept);
        }
      }

    }

    const onChangeIsRectifying = () => {
      for (const concept of inputConcepts.value) {
        concept.amount_original = getRectifyingAmount(concept.amount_original, true);
        onChangeAmountCurrency(concept);
      }
    }

    const onChangeAmountCurrency = (concept) => {
      console.log("onChangeAmountCurrency", concept);
      // Si la factura es rectificativa, ajustamos `amount_original`
      if (inputIsRectifying.value) {
          // Invertir `amount_original` si es rectificativa
          concept.amount_original = getRectifyingAmount(concept.amount_original);
      }

      // Calculamos `amount_currency` en función de `amount_original`
      let amount_currency = concept.amount_original * (concept.percentage / 100);
      amount_currency = amount_currency.toFixed(4);
  
      // Recalculamos el total usando el valor actualizado de `amount_currency`
      concept.total_currency = getConceptTotal(amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
  
      // También actualizamos `total_euros` en base a `total_currency`
      concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);

      // Calculamos `amount_currency` en función de `amount_original`
      if (inputInvoiceType.value == 'import-dua') { 
        concept.amount_currency = 0;
      } else {
        concept.amount_currency = amount_currency;
      }

      // Convertimos `amount_currency` a euros
      concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);

      // Convertir también `amount_original` a euros
      concept.amount_original_euros = convertToEuros(concept.amount_original, inputCurrency.value, inputInvoiceDate.value);
    };

    const onChangeAmountEuros = (concept) => {
      concept.amount_euros = getRectifyingAmount(concept.amount_euros);
    }

    const onClickSubmit = (status) => {
      if (["pending", "revision-pending"].includes(inputInvoiceStatus.value)) {
        validateVatNumber();
        if (!validateOutOfPeriod("{{ end_date }}")) return;
      }

      if (status !== "revised") {
        handleNonRevisedStatus(status);
      } else {
        if (!validateConcepts()) return;
        document.getElementById("inputDiscardReasonModal").required = false;
      }
      
      inputInvoiceStatus.value = status;

      Vue.nextTick(() => {
        const form = document.getElementById("form");
        
        if (form.checkValidity()) {
          form.classList.add("was-validated");
          if (status == "discard") {
            generateDiscardInvoiceNumber();
          }
          form.submit();
        } else {
          showValidationError();
          inputInvoiceStatus.value = "";
          const invalidFields = form.querySelectorAll(":invalid");
          invalidFields.forEach((field) => {
            field.classList.add("is-invalid");
          });
        }
      });

    };

    const showValidationError = () => {
      Swal.fire({
        title: "Error en la validación",
        text: "Por favor, corrige los errores en el formulario antes de continuar.",
        icon: "error",
        confirmButtonText: "Cerrar"
      });
    };

    const handleNonRevisedStatus = (status) => {
      setAllFieldsNotRequired(status);

      if (!inputInvoiceType.value) {
        inputConcepts.value = [];
      } else {
        inputConcepts.value.forEach((concept) => {
          if (!concept.concept.trim()) {
            concept.concept = "Concepto";
          }
        });
      }

      const invalidFields = document.querySelectorAll(".is-invalid");
      invalidFields.forEach((field) => {
        field.classList.remove("is-invalid");
      });

    };

    /**
     * Validates invoice concepts for 'revised' status
     * @returns {boolean} - Returns true if validation passes, false otherwise
     */
    const validateConcepts = () => {
      if (inputInvoiceType.value === "payroll") return true;

      if (inputConcepts.value.length === 0) {
        addConcept();
        showToastError("Debes añadir al menos un concepto con valores. Revisa los conceptos.");
        return false;
      }

      if (inputConcepts.value.some(concept => !concept.concept.trim())) {
        showToastError("Todos los conceptos deben estar rellenados. Revisa los conceptos.");
        return false;
      }

      return true;
    };

    /**
     * Shows a toast error message
     * @param {string} message - Error message to display
     */
    const showToastError = (message) => {
      Toast.fire({
        icon: "error",
        title: message
      });
    };

    const generateDiscardInvoiceNumber = () => {
      const now = new Date();
      const isodate = new Date(now.getTime() - (now.getTimezoneOffset() * 60000)).toISOString();
      let newInvoiceNumber = (isodate.replaceAll("-", "").replaceAll("T", "").replaceAll(":", "")).split(".")[0];
      inputInvoiceNumber.value = newInvoiceNumber + "-DISCARD";
    };

    const setAllFieldsDisabled = () => {
      const fieldset1 = document.getElementById("fieldset1");
      fieldset1.disabled = true;
      const fieldset2 = document.getElementById("fieldset2");
      fieldset2.disabled = true;
      const fieldset3 = document.getElementById("fieldset3");
      fieldset3.disabled = true;
    }

    const setAllFieldsNotRequired = (status) => {
      const form = document.getElementById("form");
      const elements = form.getElementsByTagName("*");
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].nodeName === "INPUT" || elements[i].nodeName === "SELECT" || elements[i].nodeName === "TEXTAREA") {
          elements[i].required = false;
          if (status == 'discard') {
            if (elements[i].id === "inputDiscardReasonModal") {
              elements[i].required = true;
            }
          }
        }
      }
    }

    const numberPrecision2 = (number) => {
      {#let m = Number((Math.abs(number) * 100).toPrecision(15));#}
      {#return Math.round(m) / 100 * Math.sign(number);#}
      if (number instanceof Decimal) {
        return number.toFixed(2);
      } else {
        return to_decimal_value(number).toFixed(2);
      }
    }

    const findClienteParticular = (list) => {
      if (list && list.length > 0) {
        return list.find((item) => item.name === 'Clientes Particulares')?.pk;
      }
      return 0;
    }

    const onclickTagA = (direction) => {
      const iframe = document.getElementById("iframeModal");
      const popup = "?popup=true";
      console.log(direction);

      if (direction === "new") {
        iframe.style = "display: none;";
        iframe.src = "{% url 'app_customers:new' seller.shortname %}" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      } else if (direction === "edit") {
        iframe.style = "display: none;";
        iframe.src = "{% url 'app_customers:list' seller.shortname %}" + inputCustomer.value + "/" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      } else if (direction === "editProv") {
        iframe.style = "display: none;";
        iframe.src = "{% url 'app_providers:list' seller.shortname %}" + inputProvider.value + "/" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      } else if (direction === "newProv") {
        iframe.style = "display: none;";
        iframe.src = "{% url 'app_providers:new' seller.shortname %}" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      } else if (direction === "editWork") {
        iframe.style = "display: none;";
        iframe.src = "{% url 'app_workers:list' seller.shortname %}" + inputWorker.value + "/" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      } else if (direction === "newWork") {
        iframe.style = "display: none;";
        iframe.src = "{% url 'app_workers:new' seller.shortname %}" + popup;
        document.getElementById("iframeloading").style = "display: block;";
      } else if (direction == "setParticular") {
        inputCustomer.value = findClienteParticular(dj.value.customers);
      }
    }

    const iframe = () => {
      const iframe = document.getElementById("iframeModal");
      const form = iframe.contentDocument.getElementById("form");
      if (form) {
        iframe.style = "display: block;";
        document.getElementById("iframeloading").style = "display: none;";
        form.addEventListener("submit", (e) => {
          e.preventDefault();
          document.getElementById("iframeloading").style = "display: block;";
          fetch(form.action, {
            method: form.method,
            headers: {Accept: 'application/json'},
            body: new FormData(form)
          }).then(response => response.json())
            .then(json => {
              iframe.contentDocument.querySelectorAll(".is-invalid").forEach((element) => element.classList.remove("is-invalid"));
              iframe.contentDocument.querySelectorAll(".invalid-feedback").forEach((element) => element.remove());
              if (json.errors) {
                const errors = json.errors;
                for (const key in errors) {
                  const element = iframe.contentDocument.getElementById("id_" + key);
                  if (element) {
                    element.classList.add("is-invalid");
                    const span = document.createElement("span");
                    span.classList.add("invalid-feedback");
                    span.innerHTML = "<strong>" + errors[key] + "</strong>";
                    // Inserta el mensaje de error justo después del input
                    element.insertAdjacentElement('afterend', span);
                  }
                }
              } else {
                const type_dict = {
                  "customer": dj.value.customers,
                  "provider": dj.value.providers,
                  "worker": dj.value.workers,
                }
                let current_list = type_dict[json.type];
                let long_name = json.data.nif ? (json.data.name + " ( " + json.data.nif + " )") : json.data.name;
                if (json.is_created) {
                  if (json.type === "customer") {
                    current_list.push({
                      "pk": json.data.pk,
                      "name": json.data.name,
                      "longname2": long_name,
                      "country": json.data.country,
                      "nif_cif_iva": json.data.nif,
                      "nif_cif_iva_country": json.data.nif_cif_iva_country,
                      "zip": json.data.zip,
                      "vies": json.data.vies,
                    });
                    inputCustomer.value = json.data.pk;
                  } else if (json.type === "provider") {
                    current_list.push({
                      "pk": json.data.pk,
                      "name": json.data.name,
                      "longname": long_name,
                      "country": json.data.country,
                      "nif_cif_iva": json.data.nif,
                      "nif_cif_iva_country": json.data.nif_cif_iva_country,
                      "zip": json.data.zip,
                      "vies": json.data.vies,
                    });
                    inputProvider.value = json.data.pk;
                  } else if (json.type === "worker") {
                    current_list.push({
                      "pk": json.data.pk,
                      "full_name": json.data.name,
                      "nif_nie": json.data.nif,
                      "nuss_naf": json.data.naf,
                      "worker_type_name": json.data.type_name,
                      "worker_type": json.data.type,
                    });
                    setDatePayroll();
                    inputWorker.value = json.data.pk;
                  }
                } else {
                  const current = current_list.find(e => e.pk === json.data.pk);
                  if (json.type === "customer") {
                    current.name = json.data.name;
                    current.longname2 = long_name;
                    current.nif_cif_iva = json.data.nif;
                    current.zip = json.data.zip;
                    current.vies = json.data.vies;
                    current.country = json.data.country;
                    current.nif_cif_iva_country = json.data.nif_cif_iva_country;
                  } else if (json.type === "provider") {
                    current.name = json.data.name;
                    current.longname = long_name;
                    current.nif_cif_iva = json.data.nif;
                    current.zip = json.data.zip;
                    current.vies = json.data.vies;
                    current.country = json.data.country;
                    current.nif_cif_iva_country = json.data.nif_cif_iva_country;
                  } else if (json.type === "worker") {
                    current.full_name = json.data.name;
                    current.nif_nie = json.data.nif;
                    current.nuss_naf = json.data.naf;
                    current.worker_type_name = json.data.type_name;
                    current.worker_type = json.data.type;
                    setDatePayroll();
                  }
                }
                calcTransactionType();
                calcOperationType();
                iframe.style = "display: none;"
                $('#newCostumer').modal('hide');
              }
              document.getElementById("iframeloading").style = "display: none;";
            });
        });
      } else {
        iframe.style = "display: none;";
        document.getElementById("iframeloading").style = "display: block;";

        $(document).ready(function () {
          $('#newCostumer').modal('hide');
        });
      }
    }

    const inputCarrusel = () => {

      document.getElementById("carrusel").value = "carruselTrue";
    }

    const inputReOpen = () =>{
      document.getElementById("reopen").value = "reopenInvoice";
    }

    const final_date_period = (final_date) => {
      let date = inputInvoiceAccountingDate.value;

      if (date < final_date) {
        inputInvoiceAccountingDate.value = final_date;
      }
    }


    const checkOutOfTime = () => {
      if (inputInvoiceStatus.value == 'pending' || inputInvoiceStatus.value == 'revision-pending') {
        show_accounting_date("{{ end_date }}");
      }
    }

    const show_accounting_date = (final_date) => {
      let invoice_date = inputInvoiceDate.value;
      if (invoice_date && final_date) {
        if (invoice_date < final_date) {
          inputOutOfTime.value = true;
          final_date_period(final_date);
        }
      }
    }

    const handleInputChange = (date) => {
      const invoiceDateinput = document.querySelectorAll("input[id='inputInvoiceDate']");
      invoiceDateinput.forEach((element) => {
        if (element.classList.contains("is-invalid")) {
          element.classList.remove("is-invalid");
        }
      });

      inputInvoiceAccountingDate.value = inputInvoiceDate.value
      show_accounting_date("{{ end_date }}")
    }

    const getEuropeanCountries = () => {
      let eur = dj.value.countries.filter(country => country.is_european_union == true);
      return eur;
    }

    const irArriba = () => {
      console.log("irArriba");
      $('body,html').animate({scrollTop: '0px'}, 100);
    };

    const irAbajo = () => {
      console.log("irAbajo");
      const alturaPagina = $(document).height();
      $('body,html').animate({scrollTop: alturaPagina}, 100);
    };

    const irArribaAbajoInit = () => {
      $(window).scroll(function () {
        const scrollPos = $(window).scrollTop();
        const alturaVentana = $(window).height();
        const alturaPagina = $(document).height();

        // Ocultar o mostrar el botón de "ir arriba" según la posición de desplazamiento
        if (scrollPos > alturaVentana / 5) {
          $('#irArribaBoton').fadeIn();
        } else {
          $('#irArribaBoton').fadeOut();
        }

        // Ocultar o mostrar el botón de "ir abajo" según la posición de desplazamiento
        if (scrollPos + alturaVentana >= alturaPagina - 10) {
          $('#irAbajoBoton').fadeOut();
        } else {
          $('#irAbajoBoton').fadeIn();
        }
      });

      $(document).ready(function () {
        const scrollPos = $(window).scrollTop();
        const alturaVentana = $(window).height();
        const alturaPagina = $(document).height();

        $('#arrows').fadeIn();

        // Ocultar o mostrar el botón de "ir arriba" según la posición de desplazamiento
        if (scrollPos > alturaVentana / 5) {
          $('#irArribaBoton').fadeIn();
        } else {
          $('#irArribaBoton').fadeOut();
        }

        // Ocultar o mostrar el botón de "ir abajo" según la posición de desplazamiento
        if (scrollPos + alturaVentana >= alturaPagina - 10) {
          $('#irAbajoBoton').fadeOut();
        } else {
          $('#irAbajoBoton').fadeIn();
        }
      });
    }

    const onChangeDateFee = () => {
      inputInvoiceAccountingDate.value = inputInvoiceDate.value;
      genericTicketNumberFee();
    }

    const genericTicketNumberFee = () => {
      let name = '';
      const month = document.getElementById('inputMonths').value;
      const year = document.getElementById('inputYears').value;
      const lastDay = new Date(year, month, 0).getDate();

      if (month) {
        name += 'tgss-'
        // Formatea la fecha en el formato "mm-yyyy"
        const formatDate = `${year}-${month.toString().padStart(2, '0')}`;
        name += formatDate;
        const selfEmployed = document.getElementById('selfEmployedNameFee');
        const self_empl_value = selfEmployed.value;
        if (self_empl_value.trim() !== '') {
          name += '-' + self_empl_value;
        }
        inputInvoiceNumber.value = name;
      }
      if (year && month) {
        inputInvoiceDate.value = year + "-" + month + "-" + lastDay;
        inputInvoiceAccountingDate.value = year + "-" + month + "-" + lastDay;
      }
    }

    const setDatePayroll = () => {
      const month = inputMonths.value;
      const year = inputYears.value;
      const worker = inputWorker.value;
      let name = '';

      if (year && month) {
        if (worker) {
          name += 'nomina-';
          // Formatea la fecha en el formato "mm-yyyy"
          const formatDate = `${year}-${month.toString().padStart(2, '0')}`;
          name += formatDate + '-' + slugify(getWorkerById(worker).full_name);
        }
        inputInvoiceDate.value = year + "-" + month + "-01";
        inputInvoiceAccountingDate.value = year + "-" + month + "-01";
      }
      inputInvoiceNumber.value = name;
    }

    const slugify = (str) => {
      return String(str)
        .normalize('NFKD') // split accented characters into their base characters and diacritical marks
        .replace(/[\u0300-\u036f]/g, '') // remove all the accents, which happen to be all in the \u03xx UNICODE block.
        .trim() // trim leading or trailing whitespace
        .toLowerCase() // convert to lowercase
        .replace(/[^a-z0-9 -]/g, '') // remove non-alphanumeric characters
        .replace(/\s+/g, '-') // replace spaces with hyphens
        .replace(/-+/g, '-'); // remove consecutive hyphens
    }

    // Function to copy text to the clipboard
    const copyTextToClipboard = (event) => {
      const textToCopy = event.target.getAttribute('data-text');
      const textElement = document.createElement('textarea');
      textElement.value = textToCopy;
      document.body.appendChild(textElement);

      textElement.select();
      textElement.setSelectionRange(0, 99999);
      document.execCommand('copy');
      document.body.removeChild(textElement);


      $(event.target).attr("data-bs-original-title", "NIF Copiado!");
      $(event.target).tooltip('show');
      $(event.target).tooltip({trigger: 'hover'});

      // reset tooltip to copiar  after mouse leave
      $("span.copyableText").mouseleave(function () {
        $(this).attr("data-bs-original-title", $(this).attr("country") || "Copiar NIF");
        $(this).tooltip('show');
        $(this).tooltip({trigger: 'hover'});
      });
    }

    const validateVatNumber = () => {
      /* 
            NOTE: Solo si quiero que sea siempre para ('pending' o 'revision-pending')
            // Solo se verifica si el estado es 'pending' o 'revision-pending'
            if (inputInvoiceStatus.value !== 'pending' && inputInvoiceStatus.value !== 'revision-pending') {
              return; // No ejecutar la validación
            } 
      */
      
      // Selecciona todos los elementos inputTaxCountry (selects)
      const taxCountryElements = document.querySelectorAll("select[id='inputTaxCountry']");
      
      let isValid = true;
    
      // Obtener la lista de resultados de validación de VAT desde el contexto de Django
      const vatValidationResults = JSON.parse(document.getElementById("vat-validation-results").textContent);
    
      // Recorre cada select encontrado
      taxCountryElements.forEach(select => {
        if(!inputIsDistanceSell.value){
          const selectedOption = select.options[select.selectedIndex];
          if (selectedOption) {
            const vatCountry = selectedOption.value;
      
            // Buscar si el país seleccionado tiene un número de IVA válido en utilizando la property del modelo
            const validationResult = vatValidationResults.find(vat => vat.vat_country === getCountryNameByCode(vatCountry));
            const isVatNumberValid = validationResult ? validationResult.is_valid : false;
      
            const activationDate = selectedOption.getAttribute('data-activation-date');
            const deactivationDate = selectedOption.getAttribute('data-deactivation-date');
      
            console.log(`País: ${vatCountry}, Validez: ${isVatNumberValid}`);
      
            if (!isVatNumberValid) {
              // Mostrar mensaje de error usando SweetAlert2
              Swal.fire({
                title: 'Número de IVA no válido',
                text: `El número de IVA para ${vatCountry} no es válido. Por favor, corrige el número de IVA.`,
                icon: 'error',
                confirmButtonText: 'Cerrar'
              });
      
              // Marcar el select como inválido
              select.classList.add('is-invalid');
              isValid = false;
            } else {
              // Remover clase inválida si el VAT es válido
              select.classList.remove('is-invalid');
            }
          }
        }
      });
    
      // Habilitar o deshabilitar los botones según la validez de los VAT Numbers
      document.getElementById("revised").disabled = !isValid;
      document.getElementById("revised_c").disabled = !isValid;
    
      return isValid;
    };

    const validateOutOfPeriod = (limitDate) => {
      const invoiceDateinput = document.querySelectorAll("input[id='inputInvoiceDate']");

      const accountingDateObj = new Date(inputInvoiceAccountingDate.value);
      const limitDateObj = new Date(limitDate);

      //normalizar fecha eliminando horas, minutos y segundos
      let normalizedAccountingDate = new Date(accountingDateObj.getFullYear(), accountingDateObj.getMonth(), accountingDateObj.getDate());
      let normalizedLimitDate = new Date(limitDateObj.getFullYear(), limitDateObj.getMonth(), limitDateObj.getDate());

      let isValid = true;

      let auth_user = {{ auth_user_dates | safe }};
      let username = "{{ auth_user }}";
      
      if (normalizedAccountingDate < normalizedLimitDate && !auth_user.includes(username)) {
        Swal.fire({
          title: 'Factura fuera de plazo',
          text: `La fecha de la factura está fuera del plazo de contabilización.`,
          icon: 'error',
          confirmButtonText: 'Cerrar'
        });
        invoiceDateinput.forEach(input => {
          input.classList.add('is-invalid');
        });
        isValid = false;
        invoiceInvalidFeedback.value = 'La fecha de la factura está fuera del plazo de contabilización.';
      } else {
        invoiceDateinput.forEach(input => {
          input.classList.remove('is-invalid');
        });
        isValid = true;
        invoiceInvalidFeedback.value = 'Introduzca la fecha de la factura.';
      }

      return isValid;

    }

    const shouldESCalculatorBeShown = () => {

      if (!dj.value?.seller?.contracted_accounting_date) {
          return false;
      }
      
      const currentDate = new Date();
      const startDate = dj.value?.seller?.contracted_accounting_date;
      const startDateObj = new Date(startDate).setHours(0, 0, 0, 0);
      if (isNaN(startDateObj)) {
          return false;
      }

      if (currentDate < startDateObj) {
          return false;
      }

      const endDate = dj.value?.seller?.contracted_accounting_end_date;
      if (!endDate) {
          return true;
      }

      const endDateObj = new Date(endDate);
      if (isNaN(endDateObj)) {
          return false;
      }

      const endMonth = endDateObj.getMonth() + 1;
      const endYear = endDateObj.getFullYear()

      const limitMonth = endMonth <= 3 ? 4 : endMonth <= 6 ? 7 : endMonth <= 9 ? 10 : 1;
      const limitYear = endMonth <= 9 ? endYear : endYear + 1;

      const limitDate = new Date(limitYear, limitMonth, 1).setHours(0, 0, 0, 0);

      return currentDate < limitDate;
  };

    const isButtonConfirmDisabled = () => {
      if (inputInvoiceCategory.value == "sales") {
        if (inputCustomer.value && (getCustomerById(inputCustomer.value)?.nif_cif_iva || getCustomerById(inputCustomer.value)?.is_individual_customer) ) {
          return false;
        }
      } else if (inputInvoiceCategory.value == "expenses") {
        if ( inputInvoiceType.value == "payroll" ) {
          if (inputWorker.value && getWorkerById(inputWorker.value)?.nif_nie) {
            return false;
          }        
        } else if ( inputProvider.value ) {
          return false;
        }
      } 
      return true;
    }

    // WATCHERS ////////////////////////////////////////////////////////////////////////
    watch(inputCurrency, async (newValue) => {
      const response = await fetch(`/api/get-tax-conversion/?invoice_date=${inputInvoiceDate.value}&currency=${newValue}`);
      const data = await response.json();
      if (data.tax_conversion) {
        taxConversion.value = data.tax_conversion; 
      } else {
        console.error('Error fetching tax conversion:', data.error);
      }
  
      console.log('inputCurrency watcher');
      for (const concept of inputConcepts.value) {
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });
    
    watch(inputInvoiceDate, async (newValue) => {
      const response = await fetch(`/api/get-tax-conversion/?invoice_date=${newValue}&currency=${inputCurrency.value}`);
      const data = await response.json();
      
      if (data.tax_conversion) {
        taxConversion.value = data.tax_conversion; 
      } else {
        console.error('Error fetching tax conversion:', data.error);
      }
  
      for (const concept of inputConcepts.value) {
        concept.amount_euros = convertToEuros(concept.amount_currency, inputCurrency.value, inputInvoiceDate.value);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch(inputIsRectifying, (newValue) => {
      console.log('inputIsRectifying watcher');
      for (const concept of inputConcepts.value) {
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch(inputIsEquivalentTax, (newValue) => {
      console.log('inputIsEquivalentTax watcher');
      for (const concept of inputConcepts.value) {
        if (inputInvoiceStatus.value != 'revised') {
          if (inputIsEquivalentTax.value == true && concept.vat) {
            if (concept.vat == 21) {
              concept.eqtax = 5.2;
            } else if (concept.vat == 10) {
              concept.eqtax = 1.4;
            } else if (concept.vat == 4) {
              concept.eqtax = 0.5;
            } else {
              concept.eqtax = 0;
            }
          } else {
            concept.eqtax = 0;
          }
        }
        concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
        concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
      }
    });

    watch((inputConcepts, inputConcepts.value), (newValue) => {
      console.log('inputConcepts watcher');
      const revised_button = document.getElementById("revised");
      const revised_button2 = document.getElementById("revised_c");
      let i = 0;

      let is_invalid = false;
      inputHaveSupplies.value = false;
      for (const concept of newValue) {
        if (concept.vat != 0 && inputTransactionType.value == 'extra-expense') {
          is_invalid = true;
        }
        if (concept.is_supplied == true) {
          inputHaveSupplies.value = true;
        }
        if (i < inputConceptsOld.value.length) {
          const oldConcept = inputConceptsOld.value[i];

          if (
            oldConcept.quantity != concept.quantity ||
            oldConcept.percentage != concept.percentage ||
            oldConcept.vat != concept.vat ||
            oldConcept.irpf != concept.irpf ||
            oldConcept.amount_original != concept.amount_original ||
            oldConcept.amount_currency != concept.amount_currency ||
            oldConcept.amount_euros != concept.amount_euros ||
            oldConcept.total_currency != concept.total_currency ||
            oldConcept.total_euros != concept.total_euros ||
            oldConcept.eqtax != concept.eqtax
          ) {
            concept.quantity = concept.quantity || 1;
            concept.percentage = concept.percentage || 1;
            concept.vat = concept.vat || 0;
            concept.irpf = concept.irpf || 0;
            concept.amount_euros = concept.amount_euros || 0;

            if (
              concept.amount_original == "-" || 
              concept.amount_original == "" || 
              concept.amount_original == null || 
              concept.amount_original == undefined ||
              isNaN(concept.amount_original)
            ) {
              concept.amount_original = 0;
            } else {
              concept.amount_original = concept.amount_original || 0;
            }

            if (
              concept.amount_currency == "-" || 
              concept.amount_currency == "" || 
              concept.amount_currency == null || 
              concept.amount_currency == undefined ||
              isNaN(concept.amount_currency)
            ) {
              concept.amount_currency = 0;
            } else {
              concept.amount_currency = concept.amount_currency || 0;
            }           

            // EQTAX
            if (oldConcept.vat != concept.vat ) {
              if (inputInvoiceStatus.value != 'revised'){
                if (inputIsEquivalentTax.value == true && concept.vat) {
                  if (oldConcept.eqtax != concept.eqtax) {
                    concept.eqtax = oldConcept.eqtax;
                  }else{
                    if (concept.vat == 21) {
                      concept.eqtax = 5.2;
                    } else if (concept.vat == 10) {
                      concept.eqtax = 1.4;
                    } else if (concept.vat == 4) {
                      concept.eqtax = 0.5;
                    } else {
                      concept.eqtax = 0;
                    }
                  }
                }
              }
            }

            // CURRENCY
            if (oldConcept.total_currency == concept.total_currency) {
              concept.total_currency = getConceptTotal(concept.amount_currency, concept.vat, concept.irpf, concept.percentage, concept.quantity, concept.eqtax);
            }
            {#concept.vat_currency = (concept.amount_currency * concept.vat / 100.0);#}
            {#concept.irpf_currency = (concept.amount_currency * concept.irpf / 100.0);#}
            {#concept.eqtax_currency = (concept.amount_currency * concept.eqtax / 100.0);#}

            let ca = concept.amount_currency;
            if (inputInvoiceType.value == 'import-dua') { 
              ca = concept.amount_original;
            }

            concept.vat_currency = to_decimal_value(ca)
              .times(to_decimal_value(concept.vat))
              .dividedBy(to_decimal_value(100))
              .toFixed(4)
            concept.irpf_currency = to_decimal_value(ca)
              .times(to_decimal_value(concept.irpf))
              .dividedBy(to_decimal_value(100))
              .toFixed(4);
            concept.eqtax_currency = to_decimal_value(ca)
              .times(to_decimal_value(concept.eqtax))
              .dividedBy(to_decimal_value(100))
              .toFixed(4);

            // EUROS
            concept.total_euros = convertToEuros(concept.total_currency, inputCurrency.value, inputInvoiceDate.value);
            {#concept.vat_euros = (concept.amount_euros * concept.vat / 100.0);#}
            {#concept.irpf_euros = (concept.amount_euros * concept.irpf / 100.0);#}
            {#concept.eqtax_euros = (concept.amount_euros * concept.eqtax / 100.0);#}

            let cae = concept.amount_euros;
            if (inputInvoiceType.value == 'import-dua') { 
              cae = concept.amount_original_euros;
            }

            concept.vat_euros = to_decimal_value(cae)
              .times(to_decimal_value(concept.vat))
              .dividedBy(to_decimal_value(100))
              .toFixed(4);
            concept.irpf_euros = to_decimal_value(cae)
              .times(to_decimal_value(concept.irpf))
              .dividedBy(to_decimal_value(100))
              .toFixed(4);
            concept.eqtax_euros = to_decimal_value(cae)
              .times(to_decimal_value(concept.eqtax))
              .dividedBy(to_decimal_value(100))
              .toFixed(4);

            onChangeAmountCurrency(concept);
          }
        }
        i++;
      }

      if (is_invalid) {
        revised_button.disabled = true;
        revised_button2.disabled = true;
      } else {
        revised_button.disabled = false;
        revised_button2.disabled = false;
      }

      inputTotalCurrency.value = getTotal()?.totalCurrency || 0;
      inputTotalEuros.value = getTotal()?.totalEuros || 0;
      recalcJsonIVA(newValue);

      inputConceptsOld.value = [];
      for (const con of inputConcepts.value) {
        inputConceptsOld.value.push({...con});
      }

    });

    watch(inputInvoiceNumber, (newValue) => {
      inputInvoiceNumberAbsolute.value = dj.value.seller.pk + ";" + inputInvoiceNumber.value;
    });

    watch(inputProvider, (newValue) => {
      (console.log('inputProvider watcher'))

      const conc = inputConcepts.value
      const revised_button = document.getElementById("revised");
      const revised_button2 = document.getElementById("revised_c");

      // si en el listado de diccionarios de conc existe al menos un vat con un valor distinto de cero
      // y el tipo de transacción es extra-expense
      if (conc.some(concept => concept.vat != 0) && inputTransactionType.value == 'extra-expense') {
        revised_button.disabled = true;
        revised_button2.disabled = true;
      } else {
        revised_button.disabled = false;
        revised_button2.disabled = false;
      }

      if (inputInvoiceType.value) {
        if (inputInvoiceType.value == 'rent') {
          inputAccountExpenses.value = 621;
        } else if ( !dj.value.invoice.account_expenses ) {
          setProviderAccount();
        }
      }
    });

    watch(inputCustomer, (newValue) => {
      calcTransactionType();
      if ( !dj.value.invoice.account_sales ) {
        setCustomerAccount();
      }
    });

    watch(inputOutOfTime, (newValue) => {
      const invoiceDateInput = document.querySelector("input[id='inputInvoiceDate']");

      const invoiceData = dj?.value?.invoice || {};
      const invoiceAccountingDate = invoiceData.accounting_date || null;

      const limitDateString = "{{ end_date }}";
      const limitDate = new Date(limitDateString);
      const invoiceDate = invoiceAccountingDate ? new Date(invoiceAccountingDate) : null;
      
      activeOffTime.value = !activeOffTime.value;
      
      
      // Elimina clase de error si está presente
      if (invoiceDateInput?.classList.contains("is-invalid")) {
          invoiceDateInput.classList.remove("is-invalid");
      }

      // let selectedAccountingDate = limitDateString; // Default to limit date
      // if (invoiceDate && invoiceDate > limitDate) {
      //  selectedAccountingDate = invoiceAccountingDate;
      //}

      // Si existe una fecha contable previa, se mantiene. Si no, se pone end_date como valor por defecto.
      const selectedAccountingDate = invoiceAccountingDate || limitDateString;

      inputInvoiceAccountingDate.value = selectedAccountingDate;
      

    });

    watch(inputRent, (newValue) => {
      const filtered = dj.value.rent.filter(rent => rent.pk.toString() == inputRent.value.toString());
      if (filtered && filtered.length > 0) {
        inputProvider.value = filtered[0].provider;
      }
    });

    watch(inputAccountExpenses, async (newValue) => {
      // console.log('inputAccountExpenses watcher');

      if (dj.value.seller.legal_entity == 'self-employed' && inputAccountExpenses.value == 628) {
        for (const concept of inputConcepts.value) {
          concept.percentage = dj.value.seller.percentage_affected_activity;
        }
      }
      
      const fetchedAnnotations = await getAnnotationsByDictionary('account_expenses', newValue);
      annotations.value = fetchedAnnotations;

    });

    watch(inputAccountSales, async (newValue) => {
      const fetchedAnnotations = await getAnnotationsByDictionary('account_sales', newValue);
      annotations.value = fetchedAnnotations;
    });

    const calc_payroll = () => {
      console.log('calc_payroll');
      console.log('calculando');
      // Primero aseguramos que el total devengado es la suma del salario base y salario en especie
      const baseSalary = to_decimal_value(inputSalaryBase.value);
      const salaryInKind = to_decimal_value(inputSalaryInKind.value);
      const totalAccrued = baseSalary.plus(salaryInKind);
      const percentageIRPF = to_decimal_value(inputPercentageIRPF.value);
      const incomeIRPF = percentageIRPF.dividedBy(to_decimal_value(100)).times(totalAccrued);
      inputIncomeIRPF.value = incomeIRPF.toFixed(2);
      const condition = ['1', '2'].includes(getWorkerById(inputWorker.value).worker_type);
      if (condition) {
        const workerSocialSecurity = to_decimal_value(inputWorkerSocialSecurity.value);
        const companySocialSecurity = to_decimal_value(inputCompanySocialSecurity.value);
        const netPay = totalAccrued.minus(incomeIRPF).minus(workerSocialSecurity);
        const totalPayroll = totalAccrued.plus(companySocialSecurity);
        inputNetPay.value = netPay.toFixed(2);
        inputTotalPayroll.value = totalPayroll.toFixed(2);
        inputTotalTotalPayroll.value = totalPayroll.minus(incomeIRPF).toFixed(2);
      } else {
        const netPay = totalAccrued.minus(incomeIRPF);
        inputNetPay.value = netPay.toFixed(2);
        inputTotalPayroll.value = totalAccrued.toFixed(2);
        inputTotalTotalPayroll.value = totalAccrued.minus(incomeIRPF).toFixed(2);
      }
      console.log('calculado');

      inputConceptsPayroll.value = [];
      addConceptPayroll({
        concept: "NOMINA-6400",
        percentage: 100,
        quantity: 1,
        vat: 0,
        vat_currency: 0,
        vat_euros: 0,
        irpf: inputPercentageIRPF.value,
        irpf_currency: inputIncomeIRPF.value,
        irpf_euros: inputIncomeIRPF.value,
        eqtax: 0,
        eqtax_currency: 0,
        eqtax_euros: 0,
        amount_original: inputTotalAccrued.value,
        amount_currency: inputTotalAccrued.value,
        amount_euros: inputTotalAccrued.value,
        total_currency: parseFloat(inputTotalAccrued.value) - parseFloat(inputIncomeIRPF.value),
        total_euros: parseFloat(inputTotalAccrued.value) - parseFloat(inputIncomeIRPF.value),
      });
      if (inputWorkerType.value === true) {
        addConceptPayroll({
          concept: "NOMINA-64202",
          percentage: 100,
          quantity: 1,
          vat: 0,
          vat_currency: 0,
          vat_euros: 0,
          irpf: 0,
          irpf_currency: 0,
          irpf_euros: 0,
          eqtax: 0,
          eqtax_currency: 0,
          eqtax_euros: 0,
          amount_original: inputWorkerSocialSecurity.value,
          amount_currency: inputCompanySocialSecurity.value,
          amount_euros: inputCompanySocialSecurity.value,
          total_currency: inputCompanySocialSecurity.value,
          total_euros: inputCompanySocialSecurity.value,
        });
      }
    }

    const to_decimal_value = (value) => {
      if (value === null || isNaN(value) || String(value).trim() === '') {
        value = 0;
      }
      return new Decimal(value);
    }

    watch(inputWorker, (newValue) => {
      console.log('inputWorker watcher');
      inputWorkerType.value = ['1', '2'].includes(getWorkerById(newValue).worker_type);
      setDatePayroll();
      calc_payroll();
    });

    watch(inputTotalAccrued, (newValue) => {
      console.log('inputTotalAccrued watcher');
      calc_payroll();
    });

    watch(inputSalaryBase, (newValue) => {
      console.log('inputSalaryBase watcher');
      updateTotalDevengado();
    });

    watch(inputSalaryInKind, (newValue) => {
      console.log('inputSalaryInKind watcher');
      updateTotalDevengado();
    });

    // Función para actualizar el total devengado
    const updateTotalDevengado = () => {
      const baseSalary = to_decimal_value(inputSalaryBase.value);
      const salaryInKind = to_decimal_value(inputSalaryInKind.value);
      
      // Calcular el total devengado como la suma de los dos campos
      const totalAccrued = baseSalary.plus(salaryInKind);
      
      // Actualizar el valor del total devengado
      inputTotalAccrued.value = totalAccrued.toFixed(2);
      
      // Después de actualizar el total devengado, recalcular la nómina
      calc_payroll();
    };

    watch(inputPercentageIRPF, (newValue) => {
      console.log('inputPercentageIRPF watcher');
      calc_payroll();
    });

    watch(inputWorkerSocialSecurity, (newValue) => {
      console.log('inputWorkerSocialSecurity watcher');
      calc_payroll();
    });

    watch(inputCompanySocialSecurity, (newValue) => {
      console.log('inputCompanySocialSecurity watcher');
      calc_payroll();
    });

    watch(inputTaxCountry, (newValue) => {
      Vue.nextTick(() => {
        if (inputInvoiceStatus.value == 'pending' || inputInvoiceStatus.value == 'revision-pending') {
          validateVatNumber(); // Ejecutar después de que Vue haya actualizado el DOM
        }
      });
      dj.value.economic_activity_filtered = [];

      if (dj.value.economic_activity) {
        if (dj.value.invoice.is_oss != true) {
          dj.value.economic_activity.forEach((item) => {
            if (item.pk.includes(newValue)) {
              dj.value.economic_activity_filtered.push({
                description: item.description,
                pk: item.pk
              });
            }
          });
        } else {
          dj.value.economic_activity.forEach((item) => {
            if (item.pk.includes('ES')) {
              dj.value.economic_activity_filtered.push({
                description: item.description,
                pk: item.pk
              });
            }
          });
        }

      }

      if (inputEconomicActivity.value && newValue) {
        const pattern = /^[a-zA-Z-]{3}/;
        const iae = newValue + "-" + inputEconomicActivity.value.replace(pattern, '');
        if (dj.value.economic_activity_filtered.find((item) => item.pk === iae)) {
          inputEconomicActivity.value = iae;
        } else {
          inputEconomicActivity.value = null;
        }
      } else {
        inputEconomicActivity.value = null;
      }
    });

    watch(inputInvoiceAccountingDate, (newValue) => {
      console.log('inputInvoiceAccountingDate watcher');
      disable_tax_country(newValue);
    });

    const disable_tax_country = (value) => {
      let valueDateObj = new Date(value);
      document.querySelectorAll("#inputTaxCountry option[data-deactivation-date]").forEach((e) => {
        let activationDateObj = new Date(e.dataset.activationDate);
        let deactivationDateObj = new Date(e.dataset.deactivationDate);
        e.disabled = !(activationDateObj <= valueDateObj && valueDateObj <= deactivationDateObj);
        // e.disabled = e.dataset.deactivationDate && e.dataset.activationDate < value;
        if (e.disabled && e.selected) {
          e.selected = false;
          inputTaxCountry.value = null;
        }
      });
    }

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    if (inputConcepts?.value?.length < 1) {
      addConcept();
    }

    if (inputInvoiceStatus.value == 'revised' || inputInvoiceStatus.value == 'discard') {
      setAllFieldsDisabled();
    }

    recalcTotalConcepts();
    recalcAllConcepts();

    irArribaAbajoInit();

    async function getAnnotationsByDictionary(dictType, dictValue) {
      const seller = dj.value.seller;
      let url = "{% url 'app_annotations:list_annotations_by_dictionary' 'SELLER_SHORTNAME_PLACEHOLDER' 'DICTIONARY_PLACEHOLDER' %}"
        .replace('SELLER_SHORTNAME_PLACEHOLDER', encodeURIComponent(seller.shortname))
        .replace('DICTIONARY_PLACEHOLDER', encodeURIComponent(dictType));
      url += "?dict_value=" + dictValue;

      if (!dictValue) {
        return [];
      }

      try{
        const response = await fetch(url);

        if(!response.ok){
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();

        return data;

      } catch (error) {
        console.error('Error fetching annotations:', error);
        return [];
      }

    }

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      showCategory,
      editName,
      inputInvoiceCategory,
      inputInvoiceType,
      inputTaxCountry,
      inputTaxResponsibility,
      inputOutOfTime,
      inputIsRectifying,
      inputInvoiceNumber,
      inputNameSelfEmployed,
      inputInvoiceNumberAbsolute,
      inputInvoiceDate,
      inputInvoiceAccountingDate,
      inputTypePayroll,
      inputCurrency,
      inputIsDistanceSell,
      inputIsEquivalentTax,
      inputIsReverseCharge,
      inputDepartureCountry,
      inputAccountSales,
      inputAccountExpenses,
      inputAccountExpensesAccrued,
      inputAccountExpensesWorker,
      inputAccountExpensesCompany,
      inputCustomer,
      inputProvider,
      inputRent,
      inputWorker,
      inputWorkerType,
      inputTotalAccrued,
      inputSalaryBase,
      inputSalaryInKind,
      inputPercentageIRPF,
      inputIncomeIRPF,
      inputNetPay,
      inputWorkerSocialSecurity,
      inputCompanySocialSecurity,
      inputTotalPayroll,
      inputTotalTotalPayroll,
      inputMarketplace,
      inputEconomicActivity,
      inputInvoiceTags,
      inputInvoiceNotes,
      inputInvoiceNotesPrivate,
      inputTransactionType,
      inputOperationType,
      inputInvoiceName,
      inputInvoiceStatus,
      inputInvoiceStatusOriginal,
      inputIsGeneratedAmz,
      inputDiscardReason,
      inputDiscardReasonNotes,
      inputMonths,
      inputYears,
      inputRelatedInvoice,
      inputTotalCurrency,
      inputTotalEuros,
      inputHaveSupplies,
      inputConcepts,
      inputConceptsPayroll,
      inputConceptsJSON,
      inputJsonVats,
      all_vats_currency,
      all_vats_euros,
      months,
      years,
      inputIsPostponedVat,
      annotations,
      invoiceInvalidFeedback,
      veriFactuInstance,

      getRelatedInvoiceById,
      getRelatedInvoiceProvider,
      getProviderById,
      getRentByID,
      getWorkerById,
      setProviderAccount,
      getCustomerById,
      setCustomerAccount,
      getCountryNameByCode,
      getStatusBg,
      getStatusByCode,
      getCategoryByCode,
      getTypeByCode,
      getTypesForCategory,
      getTotal,
      convertToEuros,
      conceptDefaultValues,
      addConcept,
      addConceptPayroll,
      addConceptSuplied,
      removeConcept,
      recalcTotalConcepts,
      recalcCurrencyConcepts,
      recalcIVA0Concepts,
      recalcIVA21Concepts,
      deleteAllConcepts,
      onChangeCategory,
      onChangeType,
      onChangeIsRectifying,
      onChangeAmountCurrency,
      onChangeAmountEuros,
      onClickSubmit,
      numberPrecision2,
      iframe,
      onclickTagA,
      reloadPage,
      inputCarrusel,
      inputReOpen,
      final_date_period,
      show_accounting_date,
      recalcAllIvaConcepts,
      recalcAllConcepts,
      getEuropeanCountries,
      irArriba,
      irAbajo,
      onChangeDateFee,
      genericTicketNumberFee,
      setDatePayroll,
      handleInputChange,
      isButtonConfirmDisabled,
      validateOutOfPeriod,
      shouldESCalculatorBeShown,
      
    };

    // CREATE VUE 3
    const createVue3 = (target, data_export) => {
      const {createApp} = Vue;
      const {createVuetify} = Vuetify;

      const app = createApp({
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export}
        }
      });
      const vuetify = createVuetify();
      app.use(vuetify)
      app.mount(target);
    };
    createVue3('#card-header', data_export);
    createVue3('#formBody', data_export);
    createVue3('#arrows', data_export);
    createVue3('.vue', data_export);


    let taxCountry = document.getElementById("inputTaxCountry");
    if (taxCountry) {
      let eqtaxElements = document.querySelectorAll(".eqtax");

      if (taxCountry.value == "ES") {
        eqtaxElements.forEach(function (element) {
          element.style.display = "block";
        });
      } else {
        eqtaxElements.forEach(function (element) {
          element.style.display = "none";
        });
      }
    }


    window.addEventListener('scroll', function () {
      const header = document.querySelector('.pdf');
      if (header) {
        header.classList.toggle("sticky", window.scrollY > 0);
      }
    });

    const copyableElements = document.querySelectorAll('.copyableText');
    copyableElements.forEach((element) => {
      element.addEventListener('click', copyTextToClipboard);
    });
    document.addEventListener("DOMContentLoaded", function () {
      // Tiempo al cargar el formulario
      var tiempoInicio = new Date().getTime();
      // validateVatNumber();

      document.getElementById("form").addEventListener("submit", function (e) {
        // Tiempo al enviar el formulario
        var tiempoEnvio = new Date().getTime();

        // Calcular la diferencia en segundos
        var segundosTranscurridos = (tiempoEnvio - tiempoInicio) / 1000;

        // Establecer el valor del input oculto
        document.getElementById("processing_time").value = segundosTranscurridos;

        // El formulario se enviará con este valor incluido
      });

      // checkOutOfTime();

    });

  </script>
{% endblock javascripts %}
