{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Paises IVA Contratados
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Numeros IVA Registrados</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="#">Administración</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:vat_list' user.seller.shortname %}">Numeros IVA Registrados</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Registrar nuevo País IVA</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}

 <div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}

        <label for="id_vat_country" class="form-label requiredField">
          País del IVA:
        </label> 
          <select id="id_vat_country" name="vat_country" class="select form-select" required="" onchange="typeNif()">
            <option value="">---------</option>
            {% for country in countries %}
              <option value = "{{ country.iso_code }}">{{ country.name}}</option>
            {% endfor %}
          </select>

          <label for="id_vat_number" class="form-label requiredField mt-3">Número de IVA: </label>     
            <input 
              type="text" 
              maxlength="50"
              id="id_vat_number"
              name="vat_number"
              class="textinput textInput form-control"
              required="" 
            />

            <div class= "mt-3">
              <input 
                type="checkbox" 
                id="id_vat_vies"
                name="vat_vies"
                class="checkboxinput form-check-input "
              />
              <label for="id_vat_vies" class="form-label requiredField ">VIES </label>   
                <div id="hint_id_vat_vies" class="form-text">¿Esta el Vies Activo para el Nº IVA? Si:Activo | No:Inactivo</div>
            </div>


            <div id="id_div_siret" style="display: none;">
              <label for="id_siret" class="form-label requiredField mt-3">SIRET (Francia)</label>   
                <input 
                  type="text" 
                  maxlength="50"
                  id="id_siret" 
                  name="siret"
                  class="textinput textInput form-control" 
                />
                  <div id="hint_id_siret" class="form-text">(Solo rellenar en caso de Francia)</div>
            </div>


            <div id="id_div_steuernummer" style="display: none;">
              <label for="id_steuernummer" class="form-label requiredField mt-3"> STEUERNUMMER (Alemania) </label>   
                <input 
                  type="text"
                  maxlength="50"
                  id="id_steuernummer"
                  name="steuernummer"
                  class="textinput textInput form-control"                 
                /> 
                  <div id="hint_id_steuernummer" class="form-text">(Solo rellenar en caso de Alemania/Austria)</div>
            </div>


            <div class="control-group">
              <div class="controls">
                  <button type="submit" class="btn btn-primary mt-3">Guardar</button>
              </div>
      </form>
    </div>
  </div>
{% endblock content %}

{% block javascripts %}
<script>
  const typeNif = () =>{
    let nif = document.getElementById("id_vat_country").value;
    console.log(nif);

    if (nif == "DE"){
       document.getElementById("id_div_steuernummer").style.display="block";     
    }

    if (nif == "FR"){
      document.getElementById("id_div_siret").style.display="block";
    }

    if (nif != "DE"){
      document.getElementById("id_div_steuernummer").style.display="none";    
    }

    if (nif != "FR"){
      document.getElementById("id_div_siret").style.display="none";    
    }
  }
</script>
{% endblock javascripts %}
