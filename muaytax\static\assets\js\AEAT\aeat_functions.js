// ------------------- FUNCTIONS -------------------

// Function to generate txt file
async function generate_txt(rowData) {
  let model_id = ''
  let url = ''

  // Show loading message
  Swal.fire({
    toast: true,
    title: 'Generando TXT...',
    text: 'Por favor, espere un momento.',
    icon: 'info',
    position: 'top-end',
    showConfirmButton: false,
    timerProgressBar: true,
    didOpen: () => {
        Swal.showLoading()
    },
    allowOutsideClick: false, 
    allowEscapeKey: false, 
    allowEnterKey: false 
  });

  // Succes message
  const ToastSucc = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    // stop timer when hover
    didOpen: (toast) => {
      toast.addEventListener('mouseenter', Swal.stopTimer)
      toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
  });

  if (typeof rowData === 'object') {
    let row = rowData.closest('tr');
    let cells = row.getElementsByTagName('td');
    model_id = cells[0].getElementsByTagName('span')[0].getAttribute('data-model-id')
  } else if (typeof rowData === 'string' && rowData === 'txt_in_modal') {
    model_id = $('#modalid').text();
  }
  
  url = `/docs/presented_models/generate_txt/${model_id}/`;

  try {
    const response = await fetch(url);
    const data = await response.json();
    if (response.status == 200) {
      let href_url = data.replace('muaytax', '');
      const downloadLink = document.createElement('a');
      downloadLink.href = href_url;
      downloadLink.setAttribute('download', '');
      downloadLink.click();
      ToastSucc.fire({
        icon: 'success',
        title: 'TXT descargado correctamente'
      })
    } else if (response.status === 400 && Array.isArray(data) && data.length > 0 && 'error' in data[0]) {
      $('#modalSendAEAT').modal('hide'); 
      errorNotification(data);
    }
  } catch (error) {
    $('#modalSendAEAT').modal('hide'); 
    errorNotification(error);
  }
}

// async function to AEAT
async function sendToAEAT (action) {
  let model_id = $('#modalid').text();
  let url = `/docs/presented_models/directAEAT/${model_id}/${action}/`;

  // Hide info modal sending to AEAT
  $('#modalSendAEAT').modal('hide');

  // Show loading message
  Swal.fire({
    title: 'Enviando modelo a Hacienda...',
    text: 'Por favor, espere un momento.',
    timerProgressBar: true,
    didOpen: () => Swal.showLoading(),
    showCancelButton: false,
    showConfirmButton: false,
    allowOutsideClick: false,
    allowEscapeKey: false,
    allowEnterKey: false
  });

  //Sending request to the server
  try {
      const response = await fetch(url);
      const data = await response.json();
      if (Array.isArray(data) && data.length > 0 && 'error' in data[0]) { //JSON de errores de la APP (generación de TXT)
          errorNotification(data);}
      else{
        if (action == 'draft'){ //Solicitud de validación de borrarador
          Swal.close();
          $('#info_AEAT').empty();
          if (data.respuesta && 'pdf' in data.respuesta){
            draftModal(data);
            if (data.respuesta && 'avisos' in data.respuesta){
              $('#info_AEAT').html(data.respuesta.avisos.map(aviso => `<div class="alert alert-warning" role="alert">${aviso}</div>`).join(''));
            }
          }else if (data.respuesta && 'errores' in data.respuesta){
            errorNotification(data);
          }else{
            errorNotification(data);
          }
        }else if (action == 'send'){ //Solicitud de envío a la AEAT
          if (data.respuesta && 'correcta' in data.respuesta) { //JSON de respuesta correcta de la AEAT
            correctNotification(data);
          }else if (data.respuesta && 'errores' in data.respuesta) {  //JSON de errores de la AEAT
            errorNotification(data);
          }else {
            errorNotification(data);
          }
        } 
      }
  }catch(error){
    errorNotification(error);
  }
}

// Function open window with href to model in AEAT
function manualToAEAT  () {
  let url_json = JSON.parse(url_model);
  let model = 'ES-' + $('.modalmodel')[0].textContent;
  if (model in url_json){
    url = url_json[model]
    window.open(url, '_blank');
  }
}

//Show draft modal from AEAT
const draftModal = (data) => {

  const base64PDF = data.respuesta.pdf[0];
  const pdfData = atob(base64PDF); //Decode base64

  // Uint8Array to manage the PDF
  const pdfBytes = new Uint8Array(pdfData.length);
  for (let i = 0; i < pdfData.length; i++) {
      pdfBytes[i] = pdfData.charCodeAt(i);
  }

  // Blob to manage the PDF
  const pdfBlob = new Blob([pdfBytes], { type: 'application/pdf' });
  const pdfUrl = URL.createObjectURL(pdfBlob);

  const iframe = document.createElement('iframe');
  iframe.style.width = '100%';
  iframe.style.height = '600px';
  iframe.src = pdfUrl;

  // Add iframe to container
  const pdfContainer = document.getElementById('pdfContainer');
  pdfContainer.innerHTML = ''; 
  pdfContainer.appendChild(iframe);
  $('#modalDraft').modal('show');
}

//Sweet alert error
const errorNotification = (data) => {
  if ( data.respuesta && 'errores' in data.respuesta){ //JSON de errores de Hacienda
    let erroresHTML = data.respuesta.errores.map(function(error) {
        return '<div class="alert alert-danger" role="alert" style="text-align: left;">' + error + '</div>';
    }).join('');
    Swal.fire({
      icon: 'error',
      title: 'Error al enviar el modelo a Hacienda',
      width: 1200,
      html: erroresHTML 
    });
  }else if (Array.isArray(data) && data.length > 0 && 'error' in data[0]) { //JSON de errores de la APP (generación de TXT)
    let erroresHTML = data.map(function(errorObj) {
        return '<div style="text-align: left;"><li>' + errorObj.error + '</li></div><br>';
    }).join('');
    Swal.fire({
      icon: 'error',
      title: 'Error al generar el TXT',
      width: 1200,
      html: erroresHTML
    });
  }else{
    Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Error contacta con soporte.'
    });
  }
}

//Sweet alert correct 
const correctNotification = (data) => {
  let correcta = data.respuesta.correcta;
  let correctaHTML = Object.keys(correcta).map(function(key) {
  let value = correcta[key];
  
    if (key === 'Avisos') { // Mostrar los avisos en un warning
          value = value.map(function(aviso) {
            return '<div class="alert alert-warning" role="alert">' +
                '<b><i class="fa-solid fa-triangle-exclamation"></i> &nbsp;'+ 'Aviso' + '&nbsp;'  + aviso + '</b>' +
            '</div>';
          }).join('');
          return '<br><div style="text-align: left;"><b>' + value + '</div>';
    }else if (key === 'Advertencias') { // Mostrar los avisos en un warning
          value = value.map(function(advertencia) {
            return '<div class="alert alert-warning" role="alert">' +
                '<b><i class="fa-solid fa-triangle-exclamation"></i> &nbsp;'+ 'Advertencia' + '&nbsp;'  + advertencia + '</b>' +
            '</div>';
          }).join('');
          return '<br><div style="text-align: left;"><b>' + value + '</div>';
    }else if (key === 'new_pdf') { // Mostrar el enlace al justificante de presentación
      return `<br><div style="text-align: left;">Puedes ver el justificante de presentación en nuestra APP siguiendo el enlace <a href="${value}" target="_blank">Ver modelo</a></div>`;
    }else {
      return '<div style="text-align: left;"><b>' + key + '</b>: ' + value + '</div>';
    }
      }).join('');
    
    Swal.fire({
      icon: 'success',
      title: 'Modelo presentado y notificado al cliente correctamente.',
      width: 1200,
      html: correctaHTML,
      willClose: () => {
            window.location.reload();
        }
  });
}

// ------------------- FUNCTIONS -------------------



// ----------------- EVENTS LISTENER -----------------

//Send info to modal AEAT
document.querySelectorAll('#buttonSendAEAT').forEach(function (element) {
  element.addEventListener('click', function () {
    let model = element.getAttribute('data-model').replace('ES-', '');
    let period = element.getAttribute('data-period');
    let year = element.getAttribute('data-year');
    let url = element.getAttribute('data-url');
    let period_description = element.getAttribute('data-period-description');
    let model_id = element.getAttribute('data-model-id');
    let result = element.getAttribute('data-result');
    let amount = element.getAttribute('data-amount');
    
    //catch info in modal
    $('.modalmodel').text(model);
    $('#modalperiod').text(period);
    $('#modalyear').text(year);
    $('#modalperioddescription').text(period_description);
    $('#modalLink').attr('href', url);
    $('#modalid').text(model_id);
    $('#modalResult').text(result);
    if (result != 'Resultado 0/ Sin actividad' && result != 'Informativo'){
      $('#modalAmount').text(amount + ' €');
    }else{
      $('#modalAmount').text('');
    }

    //Hide buttons if model is not supported for direct sending to AEAT
    not_suported = ['180', '184', '190', '347', '349', '369']
    if (not_suported.includes(model)){
      $('#sendAEAT').addClass('d-none');
      $('#sendDraftAEAT').addClass('d-none');
      $('#confirmCheckbox').addClass('d-none');
    }else{
      $('#sendAEAT').removeClass('d-none');
      $('#sendDraftAEAT').removeClass('d-none');
      $('#confirmCheckbox').removeClass('d-none');
    }

    $('#modalSendAEAT').modal('show');
  });
});

//Close unchecked checkbox and disable button when modal is closed (AEAT)
$('#modalSendAEAT').on('hidden.bs.modal', function () {
  $('#checkAgreed').prop('checked', false); 
  $('#sendAEAT').prop('disabled', true);
});

//Disable button if checkbox is not checked (AEAT)
$('#checkAgreed').change(function() {
  if ($(this).is(':checked')) {
      $('#sendAEAT').prop('disabled', false);
  } else {
      $('#sendAEAT').prop('disabled', true);
  }
});
// ----------------- EVENTS LISTENER -----------------

