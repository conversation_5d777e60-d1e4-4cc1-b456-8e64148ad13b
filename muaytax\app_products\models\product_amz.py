from django.db import models
from django.urls import reverse

class ProductAmz(models.Model):

    # id -> AutoGen

    name = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name="Nombre",
    )

    asin = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        verbose_name="ASIN",
    )

    asin_absolute = models.CharField(
        max_length=60,
        blank=False,
        null=False,
        unique=True,
        verbose_name="ASIN Absolute",
        help_text="SellerID;ASIN",
    )

    price = models.FloatField(
        blank=True,
        null=True,
        verbose_name="Precio",
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.PROTECT,
        related_name="seller_product_amz",
        verbose_name="Vendedor",
    )

    created_at = models.DateTimeField(auto_now_add=True)

    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Producto Amazon"
        verbose_name_plural = "Productos Amazon"

    # def save(self, *args, **kwargs):
    #     self.asin_absolute = str(self.seller.pk) + ";" + str(self.asin)
    #     super().save(*args, **kwargs)
    
    def __str__(self):
        return self.asin
           
    def get_absolute_url(self):
        return reverse("app_products:amz_detail", kwargs={"pk": self.pk})
