from pathlib import Path

# Rutas
base_path = Path(__file__).resolve().parent
original_css_path = base_path / "allstyle.css"
generated_css_path = base_path / "styleFormIVA.css"
output_path = base_path / "styleFormIVA_enriched.css"

# Bloques relevantes a conservar siempre
keywords_to_keep = [
    ":root",
    ".hidden",
    ".tooltip-icon",
    ".accordion-section",
    "@keyframes shake",
    ".shake",
    ".readonly-style",
    ".form-control[readonly]",
    ".invalid-feedback",
    ".swal-",
    ".modal-",
    ".btn_",
    ".back_btn",
    ".edit_btn",
    "#partnerTable",
    ".table-partners",
    ".select2-",
    ".choices__",
    ".form-group",
    "#deactivation_info",
    ".card-header-grey",
]

def should_keep_block(block: str) -> bool:
    return any(keyword in block for keyword in keywords_to_keep)

# Leer archivos
try:
    original_css = original_css_path.read_text(encoding="utf-8")
    generated_css = generated_css_path.read_text(encoding="utf-8")
except FileNotFoundError as e:
    print(f"[ERROR] Archivo no encontrado: {e.filename}")
    exit(1)

# Extraer bloques
original_blocks = [b.strip() for b in original_css.split("\n\n") if b.strip()]
generated_blocks = [b.strip() for b in generated_css.split("\n\n") if b.strip()]
enriched_blocks = generated_blocks.copy()

# Añadir los que faltan pero deben conservarse
for block in original_blocks:
    if block not in generated_blocks and should_keep_block(block):
        enriched_blocks.append(block)

# Guardar el nuevo archivo combinado
output_path.write_text("\n\n".join(enriched_blocks), encoding="utf-8")
print(f"[✔] Archivo generado: {output_path}")
