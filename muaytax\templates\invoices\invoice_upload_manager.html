{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}">
  <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/dropzone/dropzone.min-v5.css' %}" type="text/css"/>
  <link rel="stylesheet" href="{% static 'assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css' %}">
  <link rel="stylesheet" href="{% static 'assets/css/plugins/datepicker-bs5.min.css' %}">
  <style>
    .dateinput[readonly] {
      background-color: white !important;
      color: #000; /* Cambia este color según prefieras */
      cursor: pointer; /* Opcional, para indicar que es interactivo */
    }
    .uploadzone {
      width: 100%;
      min-height: 50vh !important;
      border: 2px solid rgba(0, 0, 0, .3);
      box-sizing: border-box;
      padding: 20px 20px;
    }
    .dropzone {
      width: 100%;
      min-height: 50vh !important;
      background-image: url("https://www.iconpacks.net/icons/2/free-pdf-upload-icon-2619-thumb.png");
      background-size: 85px;
      background-repeat: no-repeat;
      background-position: center 40%;
    }
    .dropzone .dz-message {
      display: block;
      position: absolute;
      top: 53%;
      width: 95%;
      text-align: center;
    }
    .dropzone .dz-preview.dz-error .dz-error-message {
      /* display: none !important; */
      display: block !important;
    }
    .tooltip-inner a {
      color: white;
      text-decoration: none;
    }
    .modal-body {
      max-height: 350px;
      overflow-y: auto;
    }
    .error-detail-card{
      padding: 20px;
    }
    .padding-overflow{
      overflow: clip;
      overflow-clip-margin: content-box;
      text-overflow: ellipsis;
    }
  </style>
{% endblock stylesheets %}
{% block title %}
  Lector de Facturas
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              Facturas: Lector de Facturas
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:list' %}">Vendedores</a>
                </li>
                <li class="breadcrumb-item">
                  <a href="{% url 'app_sellers:summary' seller.shortname %}">
                    {% if seller.name is not None %}
                      {{ seller.name.capitalize }}
                    {% else %}
                      Resumen Usuario
                    {% endif %}
                  </a>
                </li>
                <li class="breadcrumb-item">
                  <a href="./upload">Lector de Facturas</a>
                </li>
              </ul>
            </div>
            <div class="col col-1" style="display: none;">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="./uploadtxt">Subir TXT Amazon</a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="./upload">Subir Facturas</a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'expenses' %}">
                    Facturas de Gasto
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'sales' %}">
                    Facturas de Venta
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                  <a href=".">Todas las Facturas</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <!-- Toast | START  -->
    <div
      id="toast"
      class="toast text-white bg-success w-100 fade border"
      data-bs-animation="true"
      data-bs-autohide="true"
      data-bs-delay="5000"
      role="alert"
    >
      <div class="d-flex">
        <div id="toast-body" class="toast-body">
          <span id="toast-text"></span>
        </div>
        <button
          type="button"
          class="btn-close btn-close-white me-2 m-auto"
          data-bs-dismiss="toast"
        ></button>
      </div>
    </div>
    <!-- Toast | END  -->

    <div class="vue" id="uploads">

      <!-- Errores | START  -->
      {% if error %}
        <div class="col-12 alert alert-danger text-center mx-auto">
          {{ error | safe }}
        </div>
      {% endif %}
      <!-- Errores | END  -->

      <!-- Cargador Facturas | START -->
      <div class="col-12" id="uploadbtn" v-show="true" style="display:none;">
        <div class="card buttons">
          <div class="card-header row">
            <div class="col-11 d-flex justify-content-start align-items-center text-left">
              <h5>Seleccione el Tipo de Documento</h5>
            </div>
          </div>
          <div class="card-body border">
            <div class="row">
              <div class="col-2 mt-2">
                <button class="btn btn-large btn-primary w-100" style="font-size: 16px;"
                     @click="clickInvoiceType('invoice')" v-if="inputUploadType=='invoice'">
                  Facturas
                </button>
                <button class="btn btn-large btn-outline-primary w-100 opacity-75" style="font-size: 16px;"
                     @click="clickInvoiceType('invoice')" v-if="inputUploadType!='invoice'">
                  Facturas
                </button>
              </div>
              <div class="col-2 mt-2">
                <a href="{% url 'app_invoices:seller_invoices_upload_import' seller.shortname %}"
                   class="btn btn-large btn-outline-primary w-100 opacity-75" style="font-size: 16px;">
                  Importaciones
                </a>
              </div>
              <div class="col-3 mt-2">
                <button class="btn btn-large btn-primary w-100" style="font-size: 16px;"
                     @click="clickInvoiceType('ticket')" v-if="inputUploadType=='ticket'">
                  Tickets / Recibos / Cuotas Autonomo
                </button>
                <button class="btn btn-large btn-outline-primary w-100 opacity-75" style="font-size: 16px;"
                     @click="clickInvoiceType('ticket')" v-if="inputUploadType!='ticket'">
                  Tickets / Recibos / Cuotas Autonomo
                </button>
              </div>
              <div class="col-3 mt-2">
                <button class="btn btn-large btn-primary w-100" style="font-size: 16px;"
                     @click="clickInvoiceType('amz-txt-eur')" v-if="inputUploadType=='amz-txt-eur'">
                  Informe de Ventas Amazon ( TXT )
                </button>
                <button class="btn btn-large btn-outline-primary w-100 opacity-75" style="font-size: 16px;"
                     @click="clickInvoiceType('amz-txt-eur')" v-if="inputUploadType!='amz-txt-eur'">
                  Informe de Ventas Amazon ( TXT )
                </button>
              </div>
              <div class="col-2 mt-2">
                <button class="btn btn-large btn-primary w-100" style="font-size: 16px;"
                     @click="clickInvoiceType('payroll')" v-if="inputUploadType=='payroll'">
                  Nóminas
                </button>
                <button class="btn btn-large btn-outline-primary w-100 opacity-75" style="font-size: 16px;"
                     @click="clickInvoiceType('payroll')" v-if="inputUploadType!='payroll'">
                  Nóminas
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-12" id="uploadinfo" v-show="inputUploadType==''" style="display:none;">
        <div class="card info">
          <div class="card-header row">
            <div class="col-11 d-flex justify-content-start align-items-center text-left">
              <h5>Info</h5>
            </div>
          </div>
          <div class="card-body border">
            <div class="row">
              <div class="col-12 mt-3 text-center alert alert-warning rounded">
                <br>
                <h3><b>Debe Seleccionar el tipo de Factura a cargar</b></h3>
                <br>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-12" id="uploadiae" v-show="inputUploadType!='' && !inputEconomicActivity" style="display:none;">
        <div class="card info">
          <div class="card-header row">
            <div class="col-11 d-flex justify-content-start align-items-center text-left">
              <h5>Seleccione la Actividad Económica</h5>
            </div>
          </div>
          <div class="card-body border">
            <div class="row" v-if="dj.economic_activity.length >= 1">
              <div class="col-12 mt-3 text-center">
                <h3><b>Debe Seleccionar el tipo de actividad economica.</b></h3>
              </div>
              <div class="col-4"></div>
              <div class="col-4 mt-3 text-center">
                <select class="form-select form-control" id="inputEconomicActivity" v-model="inputEconomicActivity">
                  <option :value="null" selected>Desconocida</option>
                  <option v-for="iae in dj.economic_activity" :key="iae.pk" :value="iae.pk"
                          :selected="dj.economic_activity.length === 1">
                    [[ iae.description ]]
                  </option>
                </select>
              </div>
              <div class="col-4"></div>
            </div>
            <div class="row" v-else>
              <div class="col-12 mt-3 text-center alert alert-warning rounded">
                <br>
                <h3><b>No hay actividades económicas disponibles.</b></h3>
                <br>
                <h4>No puede subir documentos porque no tienen ningun IAE asociado.</h4>
                <h4>Contacte con soporte para solucionar el problema.</h4>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Cargador Facturas | END -->

      <!-- Cargador Facturas 1: Invoices | START  -->
      <div class="col-12" id="upload1" v-show="inputUploadType=='invoice' && inputEconomicActivity">
        <div class="card invoice">
          <div class="card-header row">
            <div class="col-9 d-flex justify-content-start align-items-center text-left">
              <h5>Lector de Facturas</h5>
            </div>
            <div class="col-3 d-flex justify-content-end align-items-center text-right d-none">
              <b>IAE:</b> &nbsp; [[inputEconomicActivity]]
            </div>
          </div>
          <div class="card-body border">
            {% include 'includes/custom-dropzone/dropzone_component.html' %}
          </div>
          {% comment %} 
          <div class="card-body border">
            <form 
              id="myDropzoneInvoice"
              action="{% url 'app_invoices:seller_invoice_create' seller.shortname %}" 
              method="" enctype="multipart/form-data"
              class="dropzone">
              {% csrf_token %}

              <div class="d-none">
                <!-- change d-x-none to d-none -->
                <!-- change type text to hidden -->
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />
                <input
                  type="hidden"
                  id="iae"
                  name="iae"
                  v-model="inputEconomicActivity"
                />
              </div>

              <div class="fallback">
                <input
                  type="file"
                  id="file"
                  name="form_create.file.name"
                  multiple
                />
              </div>
            </form>
            <span id="myListContainerInvoice"></span>
          </div>
          {% endcomment %}
        </div>
      </div>
      <!-- Cargador Facturas 1: Invoices | END  -->

      <!-- Cargador Facturas 2: Imports | START  -->
      <div class="row" id="upload2" v-show="inputUploadType=='import' && inputEconomicActivity" style="display:none;">
        <!-- Cargar Documentos: INVOICE | START  -->
        {% if seller.contracted_accounting == True %}
          <div class="col">
            <div class="card import">
              <div class="card-header row">
                <div class="col d-flex justify-content-start align-items-center text-left">
                  <h5>Lector de Importaciones (Facturas)</h5>
                </div>
                <div class="col d-flex justify-content-end align-items-center text-right"
                     style="display:none !important;">
                  <b>IAE:</b> &nbsp; [[inputEconomicActivity]] &nbsp; &nbsp;
                  <b>Cat:</b> &nbsp; [[inputCategoriaExpenses]] &nbsp; &nbsp;
                  <b>Tipo:</b> &nbsp; [[inputTypeImport]]
                </div>
              </div>
              <div class="card-body border">
                <div class="col-12 mt-3 text-center alert alert-warning">
                  <h5>
                    Solo subir importaciones que vayan a tener DUA.
                    <br>
                    En caso contrario subirlas como facturas normales.
                  </h5>
                </div>
                <form method="post" enctype="multipart/form-data"
                      action="{% url 'app_invoices:seller_invoice_create_ticket' seller.shortname %}"
                      id="myDropzoneImportInvoice" class="dropzone" style="background-position: 52.5% 30% !important;">
                  {% csrf_token %}

                  <div class="d-none d-print-none">
                    <!-- change d-x-none to d-none -->
                    <!-- change type text to hidden -->
                    <input
                      type="hidden"
                      id="id"
                      name="{{ form_create.seller.name }}"
                      value="{{ seller.pk }}"
                    />
                    <input
                      type="hidden"
                      id="invoice_category"
                      name="invoice_category"
                      v-model="inputCategoriaExpenses"
                    />
                    <input
                      type="hidden"
                      id="iae"
                      name="iae"
                      v-model="inputEconomicActivity"
                    />
                    <input
                      type="hidden"
                      id="invoice_type"
                      name="invoice_type"
                      v-model="inputTypeImport"
                    />
                  </div>

                  <div class="fallback">
                    <input
                      type="file"
                      id="file"
                      name="form_create.file.name"
                      multiple
                    />
                  </div>
                </form>
                <span id="myListContainerImportInvoice"></span>
              </div>
            </div>
          </div>
        {% endif %}
        <!-- Cargar Documentos: INVOICE | END  -->

        <!-- Cargar Documentos: DUA | START  -->
        <div class="col">
          <div class="card import">
            <div class="card-header row">
              <div class="col d-flex justify-content-start align-items-center text-left">
                <h5>Lector de Importaciones (DUA)</h5>
              </div>
              <div class="col d-flex justify-content-end align-items-center text-right"
                   style="display:none !important;">
                <b>IAE:</b> &nbsp; [[inputEconomicActivity]] &nbsp; &nbsp;
                <b>Cat:</b> &nbsp; [[inputCategoriaExpenses]] &nbsp; &nbsp;
                <b>Tipo:</b> &nbsp; [[inputTypeDua]]
              </div>
            </div>
            <div class="card-body border">
              <div class="col-12 mt-3 text-center alert alert-warning">
                <h5>
                  Aqui solo subir el DUA.
                  <br>
                  Las importaciones que no tengan DUA subirlas como facturas normales.
                </h5>
              </div>
              <form method="post" enctype="multipart/form-data"
                    action="{% url 'app_invoices:seller_invoice_create_ticket' seller.shortname %}"
                    id="myDropzoneImportDua" class="dropzone"
                {% if seller.contracted_accounting == True %}
                    style="background-position:  52.5% 30% !important;"
                {% else %}
                    style="background-position:  50% 30% !important;"
                {% endif %}
              >
                {% csrf_token %}
                <div class="d-none d-print-none">
                  <!-- change d-x-none to d-none -->
                  <!-- change type text to hidden -->
                  <input
                    type="hidden"
                    id="id"
                    name="{{ form_create.seller.name }}"
                    value="{{ seller.pk }}"
                  />
                  <input
                    type="hidden"
                    id="iae"
                    name="iae"
                    v-model="inputEconomicActivity"
                  />
                  <input
                    type="hidden"
                    id="invoice_category"
                    name="invoice_category"
                    v-model="inputCategoriaExpenses"
                  />
                  <input
                    type="hidden"
                    id="invoice_type"
                    name="invoice_type"
                    v-model="inputTypeDua"
                  />
                </div>

                <div class="fallback">
                  <input
                    type="file"
                    id="file"
                    name="form_create.file.name"
                    multiple
                  />
                </div>
              </form>
              <span id="myListContainerImportDua"></span>
            </div>
          </div>
        </div>
        <!-- Cargar Documentos: DUA | END  -->
      </div>
      <!-- Cargador Facturas 2: Imports | END  -->

      <!-- Cargador Facturas 3: Tickets | START  -->
      <div class="col-12" id="upload3" v-show="inputUploadType=='ticket' && inputEconomicActivity"
           style="display:none;">
        <div class="card ticket">
          <div class="card-header row">
            <div class="col-9 d-flex justify-content-start align-items-center text-left">
              <h5>Lector de Tickets y Recibos Bancarios</h5>
            </div>
            <div class="col-3 d-flex justify-content-end align-items-center text-right"
                 style="display:none !important;">
              <b>IAE:</b> &nbsp; [[inputEconomicActivity]] &nbsp; &nbsp;
              <b>Categoria:</b> &nbsp; [[inputCategoriaExpenses]] &nbsp; &nbsp;
              <b>Tipo:</b> &nbsp; [[inputTypeTicket]]
            </div>
          </div>
          <div class="card-body border">
            <form method="post" enctype="multipart/form-data"
                  action="{% url 'app_invoices:seller_invoice_create_ticket' seller.shortname %}" id="myDropzoneTicket"
                  class="dropzone">
              {% csrf_token %}

              <div class="d-none d-print-none">
                <!-- change d-x-none to d-none -->
                <!-- change type text to hidden -->
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />
                <input
                  type="hidden"
                  id="invoice_category"
                  name="invoice_category"
                  v-model="inputCategoriaExpenses"
                />
                <input
                  type="hidden"
                  id="iae"
                  name="iae"
                  v-model="inputEconomicActivity"
                />
                <input
                  type="hidden"
                  id="invoice_type"
                  name="invoice_type"
                  v-model="inputTypeTicket"
                />
              </div>

              <div class="fallback">
                <input
                  type="file"
                  id="file"
                  name="form_create.file.name"
                  multiple
                />
              </div>
            </form>
            <span id="myListContainerTicket"></span>
          </div>
        </div>
      </div>
      <!-- Cargador Facturas 3: Tickets | END -->

      <!-- Cargador Facturas 4: AMZ TXT EUR | START  -->
      <div class="col-12 my-0" id="upload4" v-show="inputUploadType=='amz-txt-eur' && inputEconomicActivity"
           style="display:none;">
        <div class="card my-0">
          <div class="card-header row">
            <div class="col-9 d-flex justify-content-start align-items-center text-left">
              <h5>Lector de TXT Amazon</h5>
            </div>
            <div class="col-3 d-flex justify-content-end align-items-center text-right"
                 style="display:none !important;">
              <b>IAE:</b> &nbsp; [[inputEconomicActivity]] &nbsp; &nbsp;
              <b>Categoria:</b> &nbsp; [[inputCategoriaSales]] &nbsp; &nbsp;
              <b>Tipo:</b> &nbsp; [[inputTypeAmzTxt]]
            </div>
          </div>
          <div class="card-body">
            <div class="uploadzone rounded d-flex justify-content-center align-items-center text-center">
              <form method="post" class="d-flex justify-content-center align-items-center text-center w-100"
                    enctype="multipart/form-data"
                    action="{% url 'app_invoices:seller_invoice_create_txt' seller.shortname %}" id="form-uploadtxt"
                    style="text-align:center;">
                {% csrf_token %}
                <div class="d-none d-print-none">
                  <!-- change d-x-none to d-none -->
                  <!-- change type text to hidden -->
                  <input
                    type="hidden"
                    id="id"
                    name="{{ form_create.seller.name }}"
                    value="{{ seller.pk }}"
                  />
                  <input
                    type="hidden"
                    id="invoice_category"
                    name="invoice_category"
                    v-model="inputCategoriaSales"
                  />
                  <input
                    type="hidden"
                    id="iae"
                    name="iae"
                    v-model="inputEconomicActivity"
                  />
                  <input
                    type="hidden"
                    id="invoice_type"
                    name="invoice_type"
                    v-model="inputTypeAmzTxt"
                  />
                </div>
                <div class="w-50">
                  <p for="file">*Solo válido para TXT de Amazon Europa. <br> Contactad a soporte para otros reportes.
                  </p>
                  <br>
                  <div id="fallback" class="fallback">
                    <input type="file" id="id_file" name="file" class=" form-control text-center w-100 mx-auto"/>
                  <br>
                    <div class= "col"> Fecha de la factura: <br> <b>** Este campo NO ES NECESARIO**</b></div> <br>
                  <div style= "align-items: center; display: flex; justify-content: center;">
                        <p> 
                        <div class="col-8" >
                          <div class="input-group date" id="id_expiration_date">
                            <input type="text" name="date_input" class="form-control dateinput" id="date" placeholder="Pincha para seleccionar una fecha"/>
                            <span class="input-group-append">
                              <span class="input-group-text bg-light d-block">
                                <i class="fa fa-calendar"></i>
                              </span>
                            </span>
                          </div>
                        </div>
                    </p>
                  </div>
                  <br>
                    <br><br><br>
                    <input type="submit" id="submit-button" value="Continuar" class="btn btn-primary" disabled>
                  </div>
                  <div id="spinner" style="display: none;">
                    <div class="spinner-border m-3" role="status">
                      <span class="sr-only">Cargando...</span>
                    </div>
                    <div>
                      <h4>Tu TXT se está procesando, por favor espere.</h4>
                    </div>
                  </div>
                </div>
              </form>

              <div id="spinner" style="display: none;">
                <div class="spinner-border m-5" role="status">
                  <span class="sr-only">Cargando...</span>
                </div>
              </div>
            </div>

            {% comment %} {% endif %} {% endcomment %}
          </div>
        </div>
      </div>
      {% if amz_txt_eur %}
        <div class="col-12 my-0" v-show="inputUploadType=='amz-txt-eur' && inputEconomicActivity" style="display:none;">
          <div class="card user-profile-list">
            <div class="card-body">
              <div class="dt-responsive table-responsive">
                <table id="list-table" class="table nowrap">
                  <thead>
                  <tr>
                    <th>Nombre Fichero</th>
                    <th>Amazon ID</th>
                    <th>Mes</th>
                    <th>Año</th>
                    <th>Fecha Carga</th>
                    <th style="width:5%;">Estado</th>
                    <th style="width:5%;">Acciones</th>
                  </tr>
                  </thead>
                  <tbody>
                  {% for object in amz_txt_eur %}
                    <tr>
                      <td class="align-middle">
                        <span>{{ object.file }} </span>
                      </td>
                      <td class="align-middle">
                        <span>{{ object.amz_id }} </span>
                      </td>
                      <td class="align-middle">
                        <span>{{ object.month }} </span>
                      </td>
                      <td class="align-middle">
                        <span>{{ object.year }} </span>
                      </td>
                      <td class="align-middle">
                        <span>{{ object.created_at }}</span>
                      </td>
                      <td class="align-middle">
                        <h5 class="text-center">
                          <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-html="true"
                            {% if object.error_message != None %}
                                data-bs-title="{{ object.error_message }}"
                            {% endif %}
                            {% if object.error_message == None %}
                                data-bs-title="Sin información"
                            {% endif %}
                          >
                            {% if object.status.code == "processed" %}
                              <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                            {% elif object.status.code == "failed" %}
                              <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                            {% else %}
                              <i class="fa-regular fa-xl fa-circle-question" style="color: #ff9500;"></i>
                            {% endif %}
                          </span>
                        </h5>
                      </td>
                      <td class="align-middle">
                        <div>
                          <a class="btn btn-icon btn-info" href="{{ object.get_file_url }}" target="_blank" download>
                            <i class="fa-solid fa-download"></i>
                          </a>
                        </div>
                      </td>
                    </tr>
                  {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
      <!-- Cargador Facturas 4: AMZ TXT EUR | END  -->

      <!-- Cargador Facturas 5: Payrolls | START  -->
      <div class="col-12" id="upload5" v-show="inputUploadType=='payroll' && inputEconomicActivity"
           style="display:none;">
        <div class="card payroll">
          <div class="card-header row">
            <div class="col-9 d-flex justify-content-start align-items-center text-left">
              <h5>Lector de Nóminas</h5>
            </div>
            <div class="col-3 d-flex justify-content-end align-items-center text-right"
                 style="display:none !important;">
              <b>IAE:</b> &nbsp; [[inputEconomicActivity]] &nbsp; &nbsp;
              <b>Categoria:</b> &nbsp; [[inputCategoriaExpenses]] &nbsp; &nbsp;
              <b>Tipo:</b> &nbsp; [[inputTypePayroll]]
            </div>
          </div>
          <div class="card-body border">
            <!-- BLOCK UPLOAD MESSAGE-->
            {% comment %}
            <div class="alert alert-warning w-100 text-center mx-auto">
              <br/>
              <h2> FIN DE PERIODO </h2>
              <h2><i class="fas fa-duotone fa-triangle-exclamation"></i></h2>
              <br/>
              <h4> Temporalmente Deshabilitado. </h4>
              <br/>
            </div>
            {% endcomment %}

            <form method="post" enctype="multipart/form-data"
                  action="{% url 'app_invoices:seller_invoice_create_payroll' seller.shortname %}"
                  id="myDropzonePayroll"
                  class="dropzone">
              {% csrf_token %}

              <div class="d-none d-print-none">
                <!-- change d-x-none to d-none -->
                <!-- change type text to hidden -->
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />
                <input
                  type="hidden"
                  id="invoice_category"
                  name="invoice_category"
                  v-model="inputCategoriaExpenses"
                />
                <input
                  type="hidden"
                  id="invoice_type"
                  name="invoice_type"
                  v-model="inputTypePayroll"
                />
              </div>

              <div class="fallback">
                <input
                  type="file"
                  id="file"
                  name="form_create.file.name"
                  multiple
                />
              </div>
            </form>

            <span id="myListContainerPayroll"></span>
          </div>
        </div>
      </div>
      <!-- Cargador Facturas 5: Payrolls | END -->

      <!-- Modales -->
      <div 
        id="modalErrors" 
        class="modal fade"
        tabindex="-1" 
        role="dialog" aria-labelledby="modalErrorsLabel" aria-hidden="true"
        >
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title text-danger fw-bold" id="failedFilesModalLabel">
                  <i class="bi bi-exclamation-circle-fill me-2"></i>
                  Facturas fallidas (3)
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <p class="text-muted">Las siguientes facturas no pudieron ser procesadas:</p>
              <div class="failed-files-list" id="failedFilesList">

                <div class="alert alert-danger rounded padding-overflow">
                  <h6 class="card-title mb-0">document1.pdf</h6>
                  <p class="card-text text-danger"><small>File format not supported</small></p>
                </div>
                <div class="alert alert-danger rounded">
                  <h6 class="card-title mb-0">image1.png</h6>
                  <p class="card-text text-danger"><small>File size exceeds the maximum limit</small></p>
                </div>
                <div class="alert alert-danger rounded">
                  <h6 class="card-title mb-0">spreadsheet.xlsx</h6>
                  <p class="card-text text-danger"><small>Network error occurred during upload</small></p>
                </div>

              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/cdns_locals/js/axios/axios.min-v1.2.6.js' %}"></script>
  <script src="{% static 'assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js' %}"></script>
  <script src="{% static 'assets/js/plugins/datepicker-full.min.js' %}"></script>

  <!-- DROPZONE JS  -->
  {% comment %} <script src="{% static 'assets/js/plugins/dropzone-amd-module.min.js' %}"></script> {% endcomment %}
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dropzone/dropzone.min-v5.js"></script>
  <script>
    Dropzone.options.myDropzoneInvoice = {
      // previewTemplate: '<div class="dz-preview dz-file-preview">' +
      //                 '<div class="dz-details">' +
      //                   '<div class="dz-filename"><span data-dz-name></span></div>' +
      //                   '<div class="dz-size" data-dz-size></div>' +
      //                   '<div class="dz-status"><span class="status"></span></div>' +
      //                 '</div>' +
      //                 '<div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>' +
      //                 '<div class="dz-error-message"><span data-dz-errormessage></span></div>' +
      //             '</div>',
      init: function () {
        console.log("myDropzoneInvoice");
        var myList = document.createElement("ul");
        myList.classList.add("list-group");
        document.getElementById("myListContainerInvoice").appendChild(myList);

        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function (file, response) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function (file) {
          console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }
            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });

        this.on("success", function (file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          console.log('Success | response: ', response);
          $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Subido";
          listItem.classList.add("list-group-item", "list-group-item-success");
          myList.insertBefore(listItem, myList.firstChild);

        });

        this.on("error", function (file, errorMessage) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
          $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
          var listItem = document.createElement("li");
          listItem.innerHTML = "<b>" + file.name + "</b> - Error: " + errorMessage;
          listItem.classList.add("list-group-item", "list-group-item-danger");
          myList.insertBefore(listItem, myList.firstChild);

        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Haga Click <br>o</br> Arrastre los archivos",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
    Dropzone.options.myDropzoneImportInvoice = {
      init: function () {
        console.log("mydropzoneImportInvoice");
        var myList = document.createElement("ul");
        myList.classList.add("list-group");
        document.getElementById("myListContainerImportInvoice").appendChild(myList);
        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function (file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function (file) {
          // console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }

            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });

        this.on("success", function (file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          //console.log('Success | response: ', response);
          $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Subido";
          listItem.classList.add("list-group-item", "list-group-item-success");
          console.log(response.inputTipo1);
          myList.insertBefore(listItem, myList.firstChild);


        });

        this.on("error", function (file, errorMessage, response) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
          $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Error";
          listItem.classList.add("list-group-item", "list-group-item-danger");
          myList.insertBefore(listItem, myList.firstChild);

        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Haga Click <br>o</br> Arrastre los archivos",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
    Dropzone.options.myDropzoneImportDua = {
      init: function () {
        console.log("mydropzoneImportDua");
        var myList2 = document.createElement("ul");
        myList2.classList.add("list-group");
        document.getElementById("myListContainerImportDua").appendChild(myList2);
        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function (file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function (file) {
          // console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }

            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });

        this.on("success", function (file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          //console.log('Success | response: ', response);
          $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Subido";
          listItem.classList.add("list-group-item", "list-group-item-success");
          console.log(response.inputTipo1);
          myList2.insertBefore(listItem, myList2.firstChild);


        });

        this.on("error", function (file, errorMessage, response) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
          $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Error";
          listItem.classList.add("list-group-item", "list-group-item-danger");
          myList2.insertBefore(listItem, myList2.firstChild);

        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Haga Click <br>o</br> Arrastre los archivos",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
    Dropzone.options.myDropzoneTicket = {
      init: function () {
        console.log("myDropzoneTicket");
        var myList = document.createElement("ul");
        myList.classList.add("list-group");
        document.getElementById("myListContainerTicket").appendChild(myList);

        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function (file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function (file) {
          console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }
            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });

        this.on("success", function (file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          console.log('Success | response: ', response);
          $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Subido";
          listItem.classList.add("list-group-item", "list-group-item-success");
          myList.insertBefore(listItem, myList.firstChild);
        });

        this.on("error", function (file, errorMessage) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
          $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Error";
          listItem.classList.add("list-group-item", "list-group-item-danger");
          myList.insertBefore(listItem, myList.firstChild);
        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Haga Click <br>o</br> Arrastre los archivos",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
    Dropzone.options.myDropzonePayroll = {
      init: function () {
        console.log("myDropzonePayroll");
        var myList = document.createElement("ul");
        myList.classList.add("list-group");
        document.getElementById("myListContainerPayroll").appendChild(myList);

        let numFilesUploaded = 0;
        let numFilesErrored = 0;
        let numFilesToUpload = 0;

        this.on("addedfile", function (file) {
          // Incrementar el número de archivos a cargar cuando se agrega un archivo
          numFilesToUpload++;
        });

        this.on("complete", function (file) {
          console.log('Complete | file: ', file);
          if (numFilesToUpload == numFilesUploaded + numFilesErrored) {
            console.log('Multiple Upload Complete');
            if (numFilesToUpload == numFilesErrored) {
              // ALL ERRORS
            } else if (numFilesToUpload == numFilesUploaded) {
              // ALL OK
              //location.reload();
            } else {
              // MIXED
              //location.reload();
            }
            numFilesUploaded = 0;
            numFilesErrored = 0;
            numFilesToUpload = 0;
          }
        });

        this.on("success", function (file, response) {
          // Incrementar el número de archivos cargados con éxito
          numFilesUploaded++;
          console.log('Success | response: ', response);
          $(file.previewElement).find(".dz-filename").text("Subido").css("color", "green");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Subido"
          var listItem = document.createElement("li");
          listItem.innerText = file.name + " - Subido";
          listItem.classList.add("list-group-item", "list-group-item-success");
          myList.insertBefore(listItem, myList.firstChild);
        });

        this.on("error", function (file, errorMessage) {
          // Incrementar el número de archivos que fallaron en la carga
          numFilesErrored++;
          console.log('Error | errorMessage: ', errorMessage);
          $(file.previewElement).find(".dz-filename").text("Error").css("color", "red");
          // Aquí se agrega un elemento li a la lista para el archivo cargado con estado "Error"
          var listItem = document.createElement("li");
          listItem.innerHTML = "<b>" + file.name + "</b> - Error: " + errorMessage;
          listItem.classList.add("list-group-item", "list-group-item-danger");
          myList.insertBefore(listItem, myList.firstChild);
        });
      },
      parallelUploads: 4,
      maxFiles: 1000,
      maxFilesize: 10485760,
      acceptedFiles: 'application/pdf,image/jpeg,image/jpg,image/png',
      dictDefaultMessage: "Arrastre los archivos aquí",
      dictFallbackMessage: "Su navegador no soporta la carga de archivos mediante arrastrar y soltar.",
      dictFallbackText: "Por favor, utilice el siguiente formulario para subir sus archivos como en los viejos tiempos.",
      dictFileTooBig: "El archivo es demasiado grande ({{filesize}}MiB). Tamaño máximo de archivo: {{maxFilesize}}MiB.",
      dictInvalidFileType: "No puede subir archivos de este tipo.",
      dictResponseError: "El servidor respondió con el código {{statusCode}}",
      dictCancelUpload: "Cancelar Subida.",
      dictCancelUploadConfirmation: "¿Está seguro de que desea cancelar esta subida?",
      dictRemoveFile: "Eliminar archivo.",
      dictMaxFilesExceeded: "No puede subir más archivos.",
    };
  </script>
  <!-- DROPZONE JS  -->

  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputCategoriaExpenses = ref('expenses');
    const inputCategoriaSales = ref('sales');
    const inputEconomicActivity = ref(null);
    const inputTypeImport = ref('import-invoice');
    const inputTypeDua = ref('import-dua');
    const inputTypeTicket = ref('ticket');
    const inputTypeAmzTxt = ref('sales');
    const inputTypePayroll = ref('payroll');
    const inputFile1 = ref(null);
    const inputFile2 = ref(null);
    const inputFile = ref(null);
    const inputFilename1 = ref(null);
    const inputFilename2 = ref(null);
    const inputFilename = ref(null);
    const inputUploadType = ref("{{uploadType}}");
    const uploadTypes = [
      {"code": "invoice", "description": "Lector de facturas"},
      {"code": "import", "description": "Lector de importaciones"},
      {"code": "ticket", "description": "Lector de tickets y recibos bancarios"},
      {"code": "amz-txt-eur", "description": "Lector de TXT Amazon EUR"}
    ];
    const dj = ref({});


    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      console.log(dj.value);
    };

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
      console.log("fileList: ", fileList);
    }

    const clickInvoiceType = (type) => {
      inputUploadType.value = type;
      inputEconomicActivity.value = null;
      if (type == 'amz-txt-eur') {
        if (dj._rawValue.economic_activity_amz_txt.length === 1) {
          inputEconomicActivity.value = dj._rawValue.economic_activity_amz_txt[0].pk;
        } else {
          inputEconomicActivity.value = null;
        }
      } else {
        if (dj._rawValue.economic_activity.length === 1) {
          inputEconomicActivity.value = dj._rawValue.economic_activity[0].pk;
        } else {
          inputEconomicActivity.value = null;
        }
      }
      
    }

    // WATCHERS ////////////////////////////////////////////////////////////////////////

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    if (uploadTypes.filter(ut => ut.code == inputUploadType.value).length < 1) {
      {% comment %} inputUploadType.value = uploadTypes[0].code; {% endcomment %}
    }

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,

      inputId,
      inputCategoriaExpenses,
      inputCategoriaSales,
      inputEconomicActivity,
      inputTypeImport,
      inputTypeDua,
      inputTypeTicket,
      inputTypeAmzTxt,
      inputTypePayroll,
      inputFile,
      inputFile1,
      inputFile2,
      inputFilename,
      inputFilename1,
      inputFilename2,
      inputUploadType,
      uploadTypes,

      getCountryNameByCode,
      clickInvoiceType,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";

      if (data_export.dj._rawValue.economic_activity.length === 1) {
        data_export.inputEconomicActivity = data_export.dj._rawValue.economic_activity[0].pk;
      }

      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export};
        }

      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('#toast', data_export);
    createVue3('.vue', data_export);

    const form = document.getElementById('form-uploadtxt');
    const spinner = document.getElementById('spinner');
    const submitButton = document.getElementById('submit-button');
    const fileInput = document.getElementById('id_file');

    form.addEventListener('submit', (e) => {
      submitButton.disabled = true;
      document.getElementById("fallback").style.display = 'none';
      spinner.style.display = 'block';
    });

    fileInput.addEventListener('change', () => {
      if (fileInput.files.length > 0 && fileInput.files[0].name.endsWith('.txt')) {
        submitButton.disabled = false;
      } else {
        fileInput.value = ''
        submitButton.disabled = true;
        alert('El fichero debe ser un TXT de Amazon');
      }
    });

    (function () {
      const datepickerElements = document.querySelectorAll('.dateinput');
      datepickerElements.forEach(element => {
        element.setAttribute('readonly', true);
        const d_week = new Datepicker(element, {
          buttonClass: 'btn',
          format: 'yyyy-mm-dd',
          closeOnSelect: true,
          orientation: 'bottom',
          showOnFocus: true,
          
          autohide: true,

        });
      });
    })();

    
  </script>
  <!-- VUE3 JS  -->

  <!-- CUSTOM DROPZONE SCRIPTS START -->
  {% include 'includes/custom-dropzone/dropzone_scripts.html' %}
  <!-- CUSTOM DROPZONE SCRIPTS END-->

{% endblock javascripts %}
