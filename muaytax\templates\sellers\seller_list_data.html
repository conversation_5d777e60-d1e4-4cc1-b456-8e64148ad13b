{% extends "layouts/base.html" %}
{% load static crispy_forms_tags %}
{% block title %}
  Informe Paises IVA
{% endblock title %}
{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>

  <link rel="stylesheet" type="text/css"
        href="{{ STATIC_URL }}assets/cdns_locals/css/buttons/buttons.dataTables.min-v1.7.1.css">
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }

    #sellervat-list-table_filter {
      float: left;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }

    table.dataTable {
      width: 100%;
      margin: 0;
      border-collapse: collapse; /* Cambiado a 'collapse' para una apariencia más limpia */
      border-spacing: 0;
    }

    .contracting_date {
      width: 8%;
    }

    .dataTables_filter {
      display: none;
    }

    .close {
      border: none;
      background-color: transparent;
      text-align: end;
      position: absolute;
      top: 0;
      right: 7px;
    }

    .close span {
      font-size: 30px;
      color: #B82608
    }

  </style>
{% endblock stylesheets %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">Informe Datos Paises IVA</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              {% if type_list == 'all' %}
                <a href=".">Lista de Todos los procesos</a>
              {% elif type_list == 'finish' %}
                <a href=".">Lista de Procesos finalizados</a>
              {% elif type_list == 'active' %}
                <a href=".">Lista de Procesos activos</a>
              {% endif %}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}

  {% if error %}
    <div class="alert alert-danger w-50 text-center mx-auto">{{ error }}</div>
  {% endif %}

  <!-- Tabla y Filtros -->
  <div class="row" id="sellerstable">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">
          <div class="dt-responsive">
            <div class="row">
              <div class="col-3 d-flex align-items-end">
                <div class="input-group">
                  <input class="form-control" type="search" id="search" name="search"
                         placeholder="Buscar..." oninput="search()"/>
                </div>
              </div>
              <div class="col">
                <label>Entidad</label>
                <select class="form-select" name="lsa" id="lsa" onchange="filtrarLsa()">
                  <option value="">Todos</option>
                  <option value="Autónomo">Autónomo</option>
                  <option value="LLC">LLC</option>
                  <option value="SL">SL</option>
                </select>
              </div>
              <div class="col">
                <label>País</label>
                <select class="form-select" name="pais" id="pais" onchange="filtrarPais()">
                  <option value="">Todos</option>
                  {% for country in vat_countries %}
                    <option value="{{ country }}">{{ country }} </option>
                  {% endfor %}
                </select>
              </div>
              <div class="col">
                <label>Tipo</label>
                <select class="form-select" name="tipo" id="tipo" onchange="filtrarTipo()">
                  <option value="">Todos</option>
                  {% for status in vat_status_type %}
                    <option value="{{ status }}">{{ status }} </option>
                  {% endfor %}
                </select>
              </div>
              <div class="col d-flex align-items-end">
                <button type="button" class="btn btn-success mb-0" style="opacity: 0.0;" onclick="prioritys()">
                </button>
              </div>
            </div>
            <br>
            <div class="col-lg-12">
              <table id="sellervat-list-table" class="table nowrap">
                <thead class="table-head">
                <tr>
                  <th>Prioridad</th>
                  <th>Días</th>
                  <th>Persona jurídica</th>
                  <th>Persona contacto</th>
                  <th style="display:none;">Fecha de contratación</th>
                  <th style="display:none;">Fecha Alta Nº IVA</th>
                  <th>Entidad</th>
                  <th>País</th>
                  <th>Tipo</th>
                  <th style="display:none;">F. Contratación</th>
                  <th style="display:none;">shortname</th>
                </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal -->
  <div class="modal fade " id="modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl w-75 h-75" role="document">
      <div class="modal-content w-100 h-100">
        <div class="modal-header">
          <h3 class="modal-title " id="modalLabel"><b>País IVA</b></h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span class="close" aria-hidden="true"><b>&times; </b></span>
          </button>
        </div>
        <div class="modal-body h-100 border">
          <div
            class="form-group form-check p-3 h-100 d-flex text-center align-items-center justify-content-center mx-auto">
            <div id="iframeloading" style="display: block;">
              <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
              </div>
              <br>
              <h3>Cargando...</h3>
            </div>
            <iframe id="iframeNewClient" src="" class="w-100 h-100" style="display: none;">"No es posible
              visualizar el contenido"
            </iframe>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- ¿Upload? -->
  <div class="row" id="sellersupload" style="display:none">
    <!-- Cargar Documentos | START  -->
    <div class="col-12" id="uploadstxt">
      <div class="card">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>Importador CSV</h5>
          </div>
        </div>
        <div class="card-body border">
          <form method="post" enctype="multipart/form-data" action="{% url 'app_sellers:list_data_update' %}"
                id="form-uploadcsvsellers" style="text-align:center;">
            {% csrf_token %}
            <div id="submitbuttons" style="display: block;">
              <p for="file">*Solo válido para CSV usando la plantilla.</p> <br>
              <div class="fallback">
                <input
                  type="file"
                  id="id_file"
                  name="file"
                  class=" form-control w-25 text-center mx-auto"
                  accept=".csv"
                />
                <br><br><br>
                <div class="btn btn-secondary" id="cancel-import">Cancelar</div>
                <input type="submit" id="submit-button" value="Continuar" class="btn btn-primary"
                       onclick="importSubmitButton()"/>
              </div>
            </div>
            <div id="spinner" style="display: none;">
              <p>Espere a que termine de importar el CSV</p> <br>
              <div class="spinner-border" role="status"><span class="visually-hidden">Procesando...</span>
              </div>
              <br>
              <h3>Procesando...</h3>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <!-- JQUERY DATATABLES -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/bootstrap/bootstrap-icons-v1.10.5.css">
  <link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.buttons.min-v1.7.1.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/buttons/buttons.html5.min-v1.7.1.js"></script>
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script>
    //Evita que el modal quede blanco al clicar sobre el marco
    $(document).ready(function () {
      $("#modal").on("click", function (event) {
        event.stopPropagation();
      });

      $("#modal").modal({
        backdrop: 'static',
        keyboard: false
      });

      $(".close").on("click", function (event) {
        $('#modal').modal('hide');
      });

    });

    function user_id(elemento, url) {
      let countryElement = elemento.querySelector('td.align-middle.country span');
      let valorCountry = countryElement.innerHTML.trim();
      let name = elemento.querySelector('td.align-middle.contactName div.d-inline-block h6.m-b-0');
      let valorName = name.innerHTML.trim();

      let bElement = elemento.querySelector('td b');
      let valorUser = bElement.innerHTML.trim();

      let modalTitle = document.getElementById("modalLabel");

      let userButton = url;
      let link = `<a href="${url}" class="btn btn-secondary" ><b>Ir a Paises IVA Contratados </b></a>`;
      modalTitle.innerHTML = valorCountry + "  -  " + valorName + "  |  " + valorUser + " | " + link

    }

    const reloadPage = () => {
      document.location.reload(true);
    }

    function iframe(url) {
      const iframe_loading = document.getElementById("iframeloading");
      const iframe = document.getElementById("iframeNewClient");
      iframe_loading.style.display = "block";
      iframe.style.display = "none";
      iframe.onload = function () {
        iframe_loading.style.display = "none";
        iframe.style.display = "block";

        const form = iframe.contentDocument.getElementById("form");
        if (form) {
          form.addEventListener("submit", (e) => {
            e.preventDefault();
            iframe.style = "display: none;"
            document.getElementById("iframeloading").style = "display: block;";
            fetch(form.action, {
              method: form.method,
              headers: {accept: "application/json"},
              body: new FormData(form)
            }).then(json => json.json()).then(data => {
              if (data.valid) {
                dataTable.ajax.reload();
              }
              $('#modal').modal('hide');
            });
          });
        } else {
          iframe.style = "display: none;";
          document.getElementById("iframeloading").style = "display: block;";

          $(document).ready(function () {
            $('#modal').modal('hide');
          });
        }
      };
      iframe.src = url.toString();

      // Agregar evento de clic fuera del iframe para mostrar un mensaje de alerta
      document.onclick = function (event) {
        const iframe = document.getElementById("iframeNewClient");
        if (event.target !== iframe && !iframe.contains(event.target)) {
          const sellersupload = document.getElementById("iframeNewClient");
          const form = iframe.contentDocument.getElementById("form");
          sellersupload.style = "display: none;";
        }
      };
    }

    function filtrarLsa() {
      var tipo = $("#lsa").val();
      console.log(tipo);
      dataTable.column(6).search(tipo).draw();
    }

    function filtrarPais() {
      var tipo = $("#pais").val();
      console.log(tipo);
      dataTable.column(7).search(tipo).draw();
    }

    function filtrarTipo() {
      var tipo = $("#tipo").val();
      dataTable.column(8).search(tipo).draw();
    }

    const importSubmitButton = () => {
      const submitButton = document.getElementById("submit-button");
      const submitButtons = document.getElementById("submitbuttons");
      const spinner = document.getElementById("spinner");
      submitButton.style.display = "none";
      submitButtons.style.display = "none";
      spinner.style.display = "block";
    };

    const debug = true;
    const ajaxData = (d) => {
      if (debug) console.log('ajax ', d);
      return d
    }

    const search = () => {
      console.log("busqueda");
      var tipo = $("#search").val();
      dataTable.search(tipo).draw();
    }

    const prioritys = () => {
      var xhr = new XMLHttpRequest();
      type_list = "{{type_list}}"
      xhr.open('GET', '/sellers/data/update-priority/' + type_list, true); // Establece la URL y el método de solicitud (GET en este caso)
      xhr.send();

      // Escucha el evento 'load' para obtener la respuesta cuando se complete la solicitud
      xhr.onload = function () {
        if (xhr.status === 200) {
          // La solicitud se completó exitosamente
          var respuesta = xhr.responseText;
          // Procesa la respuesta como desees
          console.log(respuesta);
          alert("Se ha actualizado la tabla");
          location.reload();
        } else {
          // Hubo un error en la solicitud
          console.error('Error en la solicitud. Código de estado:', xhr.status);
          alert('Error en la solicitud. Código de estado:', xhr.status);
        }
      };
    }

    const dataTable = $("#sellervat-list-table").DataTable({
      "serverSide": false,
      "ajax": {
        "dataSrc": "data",
        "url": "{% url 'app_sellers:sellervat_list_dt' type_list %}",
        "data": function (d) {
          ajaxData(d);
        }
      },
      "createdRow": function (row, data, dataIndex) {
        $(row).css("cursor", "pointer");
        $(row).on("click", function () {
          $('#modal').modal('show');
          let modalTitle = document.getElementById("modalLabel");
          if (typeof data.name === 'string') {
            const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
            const words = data.name.split(' ').map(function (word) {
              const lowerWord = word.toLowerCase();
              if (lowerCaseSuffixes.includes(lowerWord)) {
                return word.toUpperCase();
              } else {
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
              }
            });
            data.name = words.join(' ');
          }

          let user_url = '/sellers/' + data.shortname + '/vat/';
          let link = `<a href="${user_url}" class="btn btn-secondary" ><b>Ir a Paises IVA Contratados </b></a>`;

          modalTitle.innerHTML = data.country + "  -  " + data.user_name + "  |  " + data.name + " | " + link;
          const url = "/sellers/" + data.shortname + "/vat/" + data.sellervat_id + '?popup=true';
          iframe(url);
        });
      },
      "columns": [
        {
          "data": "status_priority",
          "render": function (data, type, row) {
            let html = '';

            let color = row.color_process;
            let priority = row.status_priority;
            html += '<span style="display:none" id="status_process_priority">' + priority + '</span>'
            if (priority == 0) {
              html += '<i class="fa-solid fa-xl fa-circle-check" style="color: #03ad65 ;"></i>';
            } else if (color == 'grey' && priority == 4) {
              html += '<i class="fa-solid fa-xl fa-exclamation-circle" style="color: #666 ;"></i>';
            } else if (color == 'grey') {
              html += '<i class="fa-solid fa-xl fa-circle" style="color: #666 ;"></i>';
            } else if (color == 'blue' && priority == 4) {
              html += '<i class="fa-solid fa-xl fa-exclamation-circle" style="color: #0088ff ;"></i>';
            } else if (color == 'blue') {
              html += '<i class="fa-solid fa-xl fa-circle" style="color: #0088ff ;"></i>';
            } else if (priority == 1) {
              html += '<i class="fa-solid fa-xl fa-circle" style="color: #f4c22b;"></i>';
            } else if (priority == 2) {
              html += '<i class="fa-solid fa-xl fa-circle" style="color: #ff8000 ;"></i>';
            } else if (priority == 3) {
              html += '<i class="fa-solid fa-xl fa-circle" style="color: #f44236;"></i>';
            } else if (priority == 4) {
              html += '<i class="fa fa-xl fa-exclamation-circle" style="color: #f44236;"></i>';
            } else {
              html += '<span class="text-center">-</span>';
            }
            html = '<div style="text-align: center;">' + html + '</div>';
            return html;
          }
        },
        {"data": "promedy_days"},
        {
          "data": "name",
          "render": function (data, type, row) {
            let html = '';
            html += '<td class="align-middle">';
            html += '<div class="d-inline-block">';
            html += '<h6 class="m-b-0"><b>';

            let name = row.name;
            if (typeof name === 'string') {
              const lowerCaseSuffixes = ['sa', 'llc', 'sl', 's.l.', 's.l', 'sl.'];
              const words = name.split(' ').map(function (word) {
                const lowerWord = word.toLowerCase();
                if (lowerCaseSuffixes.includes(lowerWord)) {
                  return word.toUpperCase();
                } else {
                  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                }
              });
              html += words.join(' ');
            }
            html += '</b>';
            return html;
          }
        },
        {"data": "user_name"},
        {
          "data": "contracting_date", 'visible': false,
          "className": "contracting_date",
          "render": function (data, type, row) {
            if (data && (type === 'display' || type === 'filter')) {
              const date = new Date(data);

              const day = date.getDate().toString().padStart(2, '0');
              const month = date.toLocaleString('default', {month: 'short'});
              const year = date.getFullYear();
              const formattedDate = `${day}/${month}/${year}`;
              return formattedDate;
            }
            return data; // Para otros tipos, como 'sort'
          }
        },
        {
          "data": "activation_date", 'visible': false,
          "className": "contracting_date",
          "render": function (data, type, row) {
            if (data && (type === 'display' || type === 'filter')) {
              const date = new Date(data);

              const day = date.getDate().toString().padStart(2, '0');
              const month = date.toLocaleString('default', {month: 'short'});
              const year = date.getFullYear();
              const formattedDate = `${day}/${month}/${year}`;
              return formattedDate;
            }
            return data; // Para otros tipos, como 'sort'
          }
        },
        {"data": "legal_entity"},
        {"data": "country"},
        {"data": "type"},
        {"data": "shortname", 'visible': false}
      ],
      "responsive": {
        "breakpoints": [
          {"name": 'bigdesktop', "width": Infinity},
          {"name": 'meddesktop', "width": 1480},
          {"name": 'smalldesktop', "width": 1280},
          {"name": 'medium', "width": 1188},
          {"name": 'tabletl', "width": 1024},
          {"name": 'btwtabllandp', "width": 848},
          {"name": 'tabletp', "width": 768},
          {"name": 'mobilel', "width": 480},
          {"name": 'mobilep', "width": 320}
        ]
      },
      "language": {
        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json",
        "lengthMenu": "_MENU_",
        "zeroRecords": "No se han encontrado vendedores.",
        "info": "_START_ a _END_ de un total de _TOTAL_",
        "search": "Buscar:",
        "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
        "infoFiltered": "",
        "emptyTable": "Cargando tabla..."
      },
      "lengthChange": false,
      "lengthMenu": [[100, 200, 400, -1], [100, 200, 400, 'Todos']],
      "order": [[0, "desc"], [1, "desc"]],
      "orderable": true,
      "initComplete": function (settings, json) {
        {% comment %} console.log("Opciones completas de DataTables:",
        settings);
        console.log("Respuesta JSON recibida:", json); {% endcomment %}
        dataTable.settings()[0].nTBody.style.width = "100%";
        dataTable.settings()[0].nTable.style.width = "100%";
        dataTable.settings()[0].nTHead.style.width = "100%";
      }
    });
  </script>
  <!-- JQUERY DATATABLES -->
{% endblock javascripts %}
