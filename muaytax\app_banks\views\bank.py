from datetime import datetime

from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.serializers import serialize
from django.db.models import Q
from django.http import HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView, DeleteView, ListView, UpdateView, View

from muaytax.app_banks.forms.bank import BankChangeForm
from muaytax.app_banks.models.bank import Bank
from muaytax.app_banks.models.movement import Movement
from muaytax.app_banks.utils import get_iban_data
from muaytax.app_customers.models.customer import Customer
from muaytax.app_providers.models.provider import Provider
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.bank_type import BankType
from muaytax.dictionaries.models.currencies import Currency
from muaytax.users.permissions import IsSellerShortnamePermission


class BankListView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = Bank
    template_name_suffix = "_list"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        banks = Bank.objects.filter(bank_seller = seller)
        return banks

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        accounts_id = ['*********','*********','*********','*********','*********','*********','*********','*********','*********','*********','*********','*********','*********','*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********', '*********']
        accounting_account = list(AccountingAccount.objects.all().filter( Q(country = 'ES') | Q(code__in=accounts_id) ).order_by('code'))

        providers = Provider.objects.filter(seller=seller).exclude(provider_number__isnull=True).exclude(country__isnull=True).exclude(name__isnull=True).distinct()
        provider_accounts = [
            AccountingAccount(
                code=f'400{prov.provider_number}',
                description=f'Proveedor {prov.name} ({prov.country.pk.upper() if prov.country else ""})',
                country=prov.country
            ) for prov in providers
        ]

        customers = Customer.objects.filter(seller=seller).exclude(customer_number__isnull=True).exclude(country__isnull=True).exclude(name__isnull=True).distinct()
        customer_accounts = [
            AccountingAccount(
                code=f'430{cust.customer_number}',
                description=f'Cliente {cust.name} ({cust.country.pk.upper() if cust.country else ""})',
                country=cust.country
            ) for cust in customers
        ]

        new_accounting_account = accounting_account + provider_accounts + customer_accounts

        # Generar years_list desde 2022 hasta el año actual
        current_year = datetime.now().year
        years_list = list(range(2022, current_year + 1))

        # I want to get all Banks of seller. Order by PK, except the bank with name 'Amazon' that should be the last one
        bank_common = list(Bank.objects.filter(bank_seller = seller).exclude(bank_name__iexact='Amazon').exclude(bank_name__iexact='Accounts').order_by('pk'))
        bank_amz = list(Bank.objects.filter(bank_seller = seller, bank_name__iexact='Amazon'))
        bank_acc = list(Bank.objects.filter(bank_seller = seller, bank_name__iexact='Accounts'))
        banks = bank_common + bank_amz + bank_acc
        bank_movements = {}
        for bank in banks:
            bank_movements[bank.pk] = Movement.objects.filter(bank = bank)
        context = super().get_context_data(**kwargs)
        context['years_list'] = years_list
        context["seller"] = seller
        context["accounting_account"] = new_accounting_account
        context["banks"] = banks
        # context["bank_movements"] = bank_movements
        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "accounting_account": serialize("json", context["accounting_account"]),
            "banks": serialize("json", context["banks"]),
        }
        return context

    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

class BankNewView(LoginRequiredMixin, IsSellerShortnamePermission, CreateView):
    model = Bank
    form_class = BankChangeForm
    template_name_suffix = "_detail"

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        form.instance.bank_seller = seller
        return super().form_valid(form)

    def form_invalid(self, form):
        # Al usar form_invalid, Django automáticamente mantiene los datos del formulario
        # y muestra los errores en la plantilla
        return super().form_invalid(form)

    def get_context_data(self, **kwargs):
        bank_type_kwarg = self.kwargs.get("banktype")
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        banktype = BankType.objects.filter(code=bank_type_kwarg).first()
        currency = Currency.objects.all()

        # Recuperar las cuentas contables de los bancos asociados con este seller
        banco = Bank.objects.all().filter(bank_seller=seller, bank_accounting_account__startswith='572').order_by('-bank_accounting_account').first()
        if banco is None:
            accounting_account_code  = '*********'
        else:
            accounting_account_code  = str(int(banco.bank_accounting_account)+1)

        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["banktype"] = banktype
        context["currency"] = currency
        context["accounting_account_code"] = accounting_account_code

        # Preservar los datos del formulario para mantenerlos en el frontend
        # Si estamos en un POST con errores, form estará en el contexto
        if 'form' in context and hasattr(self, 'request') and self.request.method == 'POST':
            # Los datos ya estarán en el formulario, solo marcamos que hay errores
            context['has_form_errors'] = bool(context['form'].errors)

        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "banktype": serialize("json", [banktype]) if banktype is not None else '[]',
            "currency": serialize("json", context["currency"]),
        }

        return context

    def get_success_url(self) -> str:
        success_message = "Cuenta bancaria creada correctamente."

        # En CreateView, podemos acceder al objeto recién creado mediante self.object
        created_object = getattr(self, 'object', None)
        if created_object and hasattr(created_object, 'bank_account_type') and created_object.bank_account_type:
            if created_object.bank_account_type.code == 'creditcard':
                success_message = "Tarjeta de crédito creada correctamente."

            # Obtenemos el shortname del vendedor del objeto creado
            seller_shortname = created_object.bank_seller.shortname
        else:
            # Fallback: obtenemos el shortname del vendedor directamente de la URL
            seller_shortname = self.kwargs.get("shortname", "")

        base_url = reverse(
            "app_banks:bank_list",
            args=[seller_shortname],
        )
        return f"{base_url}?toast_message={success_message}&toast_type=success"

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class BankEditView(LoginRequiredMixin, IsSellerShortnamePermission, UpdateView):
    model = Bank
    form_class = BankChangeForm
    template_name_suffix = "_detail"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        form.instance.bank_seller = seller
        return super().form_valid(form)

    def form_invalid(self, form):
        # Al usar form_invalid, Django automáticamente mantiene los datos del formulario
        # y muestra los errores en la plantilla
        return super().form_invalid(form)

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        banks = Bank.objects.filter(bank_seller = seller)
        return banks

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank = Bank.objects.filter(bank_seller = seller, pk=self.kwargs["pk"]).first()
        currency = Currency.objects.all()
        context = super().get_context_data(**kwargs)

        banktype = None
        if bank and hasattr(bank, 'bank_account_type') and bank.bank_account_type:
            type = bank.bank_account_type.code
            banktype = BankType.objects.filter(code=type).first()

        context["seller"] = seller
        context["bank"] = bank
        if bank:
            context["accounting_account_code"] = bank.bank_accounting_account
        context["banktype"] = banktype
        context["currency"] = currency

        # Preservar los datos del formulario para mantenerlos en el frontend
        # Si estamos en un POST con errores, form estará en el contexto
        if 'form' in context and hasattr(self, 'request') and self.request.method == 'POST':
            # Los datos ya estarán en el formulario, solo marcamos que hay errores
            context['has_form_errors'] = bool(context['form'].errors)

        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "bank": serialize("json", [bank]) if bank is not None else '[]',
            "banktype": serialize("json", [banktype]) if banktype is not None else '[]',
            "currency": serialize("json", context["currency"]),
        }

        return context

    def get_success_url(self) -> str:
        success_message = "Cuenta bancaria actualizada correctamente."

        # En UpdateView, podemos acceder al objeto actualizado mediante self.object
        updated_object = getattr(self, 'object', None)
        if updated_object and hasattr(updated_object, 'bank_account_type') and updated_object.bank_account_type:
            if updated_object.bank_account_type.code == 'creditcard':
                success_message = "Tarjeta de crédito actualizada correctamente."

            # Obtenemos el shortname del vendedor del objeto actualizado
            seller_shortname = updated_object.bank_seller.shortname
        else:
            # Fallback: obtenemos el shortname del vendedor directamente de la URL
            seller_shortname = self.kwargs.get("shortname", "")

        base_url = reverse(
            "app_banks:bank_list",
            args=[seller_shortname],
        )
        return f"{base_url}?toast_message={success_message}&toast_type=success"

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class GetIbanDataView(LoginRequiredMixin, IsSellerShortnamePermission, View):

    def get(self, request, *args, **kwargs):
        iban = self.kwargs['iban']
        return get_iban_data(iban)

    def post(self, request, *args, **kwargs):
        iban = self.kwargs['iban']
        return get_iban_data(iban)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))