{% load static crispy_forms_filters crispy_forms_field crispy_forms_tags %}
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5>
            <i class="fas fa-list-ul text-c-blue wid-20"></i>
            <span class="p-l-5">Listado de servicios contratados</span>
        </h5>
        <button type="button"
            class="btn btn-primary btn-sm rounded m-0 float-end"
            data-bs-toggle="tooltip" data-bs-placement="top"
            title="Añadir servicio" id="addServiceBtn">
            <i class="fas fa-plus"></i>
        </button>
    </div>
</div>
<div class="row" id="contractedServiceCard">
    {% for service, service_data in contracted_services.items %}
        {% if service_data.start_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_accounting' and service_data.service_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_maintenance_llc' and service_data.service_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_accounting_usa' and service_data.service_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_accounting_usa_basic' and service_data.service_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_accounting_usa' and service_data.service_llc_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_accounting_usa_basic' and service_data.service_llc_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% elif service == 'contracted_maintenance_llc' and service_data.service_llc_registration_purchase_date %}
            {% include 'sellers/include/fiscal_information/contracted-service-card.html' %}
        {% endif %}
    {% endfor %}
</div>

<!-- Tabla países IVA contratados -->
<div class="card">
    <div class="card-header">
        <h5>
            <i class="fas fa-list-ul text-c-blue wid-20"></i>
            <span class="p-l-5">Listado de países contratados</span>
        </h5>
    </div>
    <div class="card-body d-none" id="skeletonBodyVatTable">
        <div id="" class="dt-container dt-empty-footer">
            <div class="d-flex gap-3">
                <div class="skeleton skeleton-text">&nbsp;</div>
                <div class="skeleton skeleton-text">&nbsp;</div>
            </div>
            <div class="dt-layout-row dt-layout-table">
                <div class="dt-layout-cell ">
                    <table class="display dataTable" style="width: 100%;"
                        aria-describedby="services-vat-country-list_info">
                        <thead class="table-head">
                            <tr role="row">
                                <th rowspan="1" colspan="1" >
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </th>
                                <th rowspan="1" colspan="1" >
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </th>
                                <th rowspan="1" colspan="1" >
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </th>
                                <th rowspan="1" colspan="1" >
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </th>
                                <th rowspan="1" colspan="1" >
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                                <td class="overflow-hidden sorting_1">
                                    <div class="skeleton skeleton-text">&nbsp;</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="dt-layout-row">
                <div class="d-flex gap-3">
                    <div class="skeleton skeleton-text">&nbsp;</div>
                    <div class="skeleton skeleton-text">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body" id="vatCountryTableBody">
        <table
            class="display table table-bordered table-hover nowrap dataTable-table"
            id="services-vat-country-list"
            style="width: 100%;"
            >
            <thead class="custom-table-head">
                <tr>
                    <th style="width: 25%;">País</th>
                    <th style="width: 25%;">Número IVA</th>
                    <th>STEUERNUMMER o SIRET</th>
                    <th>Contratación</th>
                    <th>Fin de Contratación</th>
                    <th>Inicio servicio</th>
                    <th>Fin de servicio</th>
                    <th>Inicio de Cobro</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody
                id="contracted-vat-country-rows"
                hx-swap="innerHTML"
                >
                {% for iva_country in iva_contracted %}
                <tr id="vat-row-{{ iva_country.pk }}">
                    <td class="overflow-hidden">
                        <span class="d-flex align-items-center">
                            <img class="rounded-circle img-thumbnail wid-20"
                                src="{% static 'assets/images/flags/' %}{{ iva_country.vat_country.iso_code|lower }}.svg"
                                style="padding: 0.1rem;"
                                alt="">
                            &nbsp;{{ iva_country.vat_country }}
                        </span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{% firstof iva_country.vat_number "No tiene" %}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{% firstof iva_country.steuernummer iva_country.siret "No tiene" %}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{{ iva_country.contracting_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{{ iva_country.contracting_discontinue|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{{ iva_country.start_contracting_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{{ iva_country.end_contracting_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{{ iva_country.collection_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td>
                    {% comment %} <td class="overflow-hidden">
                        <span>{{ iva_country.activation_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td>
                    <td class="overflow-hidden">
                        <span>{{ iva_country.deactivation_date|date:"d/M/Y"|lower|default:"--" }}</span>
                    </td> {% endcomment %}
                    <td class="overflow-hidden">
                        <a
                            id="editVatCountryButton"
                            class="text-warning f-14 me-2"
                            onclick="openAddUpdateCountryActivationDateModal(this)"
                            data-vat-pk="{{ iva_country.pk }}"
                            data-vat-name="{{ iva_country.vat_country.name }}"
                            data-vat-country-code="{{ iva_country.vat_country.iso_code }}"
                            data-is-contracted="{{ iva_country.is_contracted }}"
                            data-contracting-date="{{ iva_country.contracting_date|date:'Y-m-d'|lower }}"
                            data-contracting-end-date="{{ iva_country.contracting_discontinue|date:'Y-m-d'|lower }}"
                            data-activation-date="{{ iva_country.activation_date|date:'Y-m-d'|lower }}"
                            data-deactivation-date="{{ iva_country.deactivation_date|date:'Y-m-d'|lower }}"
                            data-start-contracting-date="{{ iva_country.start_contracting_date|date:'Y-m-d'|lower }}"
                            data-end-contracting-date="{{ iva_country.end_contracting_date|date:'Y-m-d'|lower }}"
                            data-collection-date="{{ iva_country.collection_date|date:'Y-m-d'|lower }}"
                            data-vat-number="{{ iva_country.vat_number }}"
                            data-steuernummer="{{ iva_country.steuernummer|default_if_none:'' }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            title="Editar"
                            role="button">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- modales -->

<!-- Modal para editar detalles de la tabla de servicios VAT -->
<div class="modal fade"
    id="addUpdateCountryContractedModal" tabindex="-1"
    aria-labelledby="animateModalLabel" aria-modal="true"
    role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form
                id="addUpdateCountryContractedForm"
                hx-post="{% url 'app_sellers:get_contracted_vat_country_list' shortname=object.shortname %}"
                hx-trigger="submit"
                hx-target="#contracted-vat-country-rows, #vat-row-{{ iva_country.pk }}"
                hx-swap="outerHTML"
                hx-on::after-request="this.reset(); document.getElementById('vatDateSubmitBtn').disabled = false;">
                {% csrf_token %}
                <div class="modal-header bg-dark">
                    <h5 id="countryContractedTitleModal" class="modal-title text-white">Editar detalles del VAT</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Indicador de carga -->
                    <div id="loading-indicator" class="d-none text-center">
                        <div class="custom-spinner"></div>
                        <p>Validando...</p>
                    </div>
                    <div id="modal-content">
                        <div class="text-center">
                            <div class="position-relative d-inline-block text-center">
                                <img
                                    id="countryContractedFLagModal" class="rounded-circle img-thumbnail wid-70"
                                    src="{% static 'assets/images/flags/es.svg' %}" data-src="{% static 'assets/images/flags/' %}"  alt="">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_contracting_date" class="col-form-label pb-0">Fecha de Contratación</label>
                                <input type="date" class="form-control" id="id_contracted_country_contracting_date" name="contracted_country_contracting_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_contracting_discontinue" class="col-form-label pb-0">Fecha fin de Contratación</label>
                                <input type="date" class="form-control" id="id_contracted_country_contracting_discontinue" name="contracted_country_contracting_discontinue">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_activation_date" class="col-form-label pb-0">Fecha de Alta en Hacienda</label>
                                <input type="date" class="form-control" id="id_contracted_country_activation_date" name="contracted_country_activation_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_deactivation_date" class="col-form-label pb-0">Fecha de Baja en Hacienda</label>
                                <input type="date" class="form-control" id="id_contracted_country_deactivation_date" name="contracted_country_deactivation_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_start_contracting_date" class="col-form-label pb-0">Fecha de Inicio de Servicio</label>
                                <input type="date" class="form-control" id="id_contracted_country_start_contracting_date" name="contracted_country_start_contracting_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_end_contracting_date" class="col-form-label pb-0">Fecha de Fin de Servicio</label>
                                <input type="date" class="form-control" id="id_contracted_country_end_contracting_date" name="contracted_country_end_contracting_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_collection_date" class="col-form-label pb-0">Fecha de Inicio de Cobro</label>
                                <input type="date" class="form-control" id="id_contracted_country_collection_date" name="contracted_country_collection_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_vat_number" class="col-form-label pb-0">VAT Number</label>
                                <input type="text" class="form-control" id="id_contracted_country_vat_number" name="contracted_country_vat_number">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_country_steuernummer" class="col-form-label pb-0">STEUERNUMMER o SIRET</label>
                                <input type="text" class="form-control" id="id_contracted_country_steuernummer" name="contracted_country_steuernummer">
                            </div>
                        </div>
                        <input type="hidden" name="contracted_country_seller_vat_pk" id="id_contracted_country_seller_pk">
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="countrySubmitBtn" type="submit" class="btn btn-primary">Actualizar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modales para eliminar detalles del apartado de servicio -->
<div class="modal fade" id="confirmDeleteServiceModal"
    tabindex="-1" aria-labelledby="animateModalLabel"
    aria-modal="true" role="dialog"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="deleteServiceForm"
                hx-post="{% url 'app_sellers:update_contracted_service' object.shortname %}"
                hx-trigger="submit">
                {% csrf_token %}
                <div class="modal-header bg-dark">
                    <h5 class="modal-title text-white">Eliminar servicio contratado</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>¿Estás seguro de que deseas eliminar este servicio contratado?</p>
                    <input type="hidden" id="deleting_service_id" name="deleting_service_id">
                </div>
                <div class="modal-footer">
                    <button type="submit" name="delete-service" class="btn btn-danger">Eliminar</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Modal para añadir/editar detalles del apartado de servicio -->
<div class="modal fade"
    id="addUpdateServiceModalService" tabindex="-1"
    aria-labelledby="animateModalLabel" aria-modal="true"
    role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form
                id="addUpdateServiceForm"
                hx-post="{% url 'app_sellers:update_contracted_service' object.shortname %}"
                hx-trigger="submit">
                {% csrf_token %}
                <div class="modal-header bg-dark">
                    <h5 id="addUpdateServiceTitle" class="modal-title text-white"></h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Indicador de carga -->
                    <div id="loading-indicator-service" class="d-none text-center">
                        <div class="custom-spinner"></div>
                        <p>Validando...</p>
                    </div>
                    <div id="modal-content-service">
                        <!-- Aquí van los campos del formulario -->
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_service_type" class="col-form-label pb-0">Tipo de servicio</label>
                                <select class="form-select form-control" id="id_service_type" name="service_type" required></select>
                            </div>
                        </div>
                        <!-- Fechas de compra (inicialmente ocultas) -->
                        <div class="row mb-2" id="purchase_dates_section" style="display: none;">
                            <div class="col-12">
                                <label for="id_service_registration_purchase_date" class="col-form-label pb-0" id="registration_purchase_label">Fecha de compra del servicio</label>
                                <input type="date" class="form-control" id="id_service_registration_purchase_date" name="service_registration_purchase_date">
                            </div>
                        </div>
                        <div class="row mb-2" id="cancellation_dates_section" style="display: none;">
                            <div class="col-12">
                                <label for="id_service_cancellation_purchase_date" class="col-form-label pb-0" id="cancellation_purchase_label">Fecha de compra de la baja</label>
                                <input type="date" class="form-control" id="id_service_cancellation_purchase_date" name="service_cancellation_purchase_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_date" class="col-form-label pb-0">Fecha de inicio</label>
                                <input type="date" class="form-control" id="id_contracted_date" name="contracted_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_end_date" class="col-form-label pb-0">Fecha de fin (Opcional)</label>
                                <input type="date" class="form-control" id="id_contracted_end_date" name="contracted_end_date">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-12">
                                <label for="id_contracted_payment_date" class="col-form-label pb-0">Fecha de inicio de cobro (Opcional)</label>
                                <input type="date" class="form-control" id="id_contracted_payment_date" name="contracted_payment_date">
                            </div>
                        </div>

                        <!-- Agregar fechas de Hacienda solo para Contabilidad ES -->
                        <div class="row mb-2" id="haciendaDates" style="display: none;">
                            <div class="col-12">
                                <label for="id_tax_agency_accounting_date" class="col-form-label pb-0">Fecha Alta en Hacienda</label>
                                <input type="date" class="form-control" id="id_tax_agency_accounting_date" name="tax_agency_accounting_date">
                            </div>
                            <div class="col-12 mt-2">
                                <label for="id_tax_agency_accounting_end_date" class="col-form-label pb-0">Fecha Baja en Hacienda</label>
                                <input type="date" class="form-control" id="id_tax_agency_accounting_end_date" name="tax_agency_accounting_end_date">
                            </div>
                        </div>
                        <input type="hidden" id="id_request_type" name="request_type" value="">
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="serviceSubmitBtn" type="submit" class="btn btn-primary"></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!--servicio RAPS-->
{% if has_service_rap %}
    {% include 'sellers/include/fiscal_information/services/raps/rap_information.html' %}
{% endif %}

{% block javascripts %}

    <script src="{{ STATIC_URL }}assets/cdns_locals/js/htmx/htmx.min-v1.6.0.js"></script>

    <script>
        //   --------------------
        //    Variables Globales
        //   --------------------

        const addServiceButton = document.querySelector('#addServiceBtn');
        const addEditForm = document.querySelector('#addUpdateServiceForm');
        const deleteForm = document.querySelector('#deleteServiceForm');
        const addUpdateServiceTitle = document.querySelector('#addUpdateServiceTitle');
        const countryUpdateform = document.getElementById('addUpdateCountryContractedForm');

        // Variables para los campos de fechas de compra
        const serviceRegistrationPurchaseDateField = document.getElementById('id_service_registration_purchase_date');
        const serviceCancellationPurchaseDateField = document.getElementById('id_service_cancellation_purchase_date');

        //   --------------------------
        //    Declaración de funciones
        //   -------------------------

        // ---> Objeto Literal de funciones (scope global)

        const HTMXServiceHandler = {

            // Muestra u oculta el mensaje de validación de un campo en función de errores.
            toggleEndDateValidationFeedback(input, error) {
                debugLog(`[toggleEndDateValidationFeedback] Ejecutando -> toggleEndDateValidationFeedback`);
                // Buscar y eliminar cualquier mensaje de error previo
                const parent = input.parentElement;
                let feedbackElement = input.nextElementSibling;

                if (parent.classList.contains('input-group')) {
                    feedbackElement = parent.nextElementSibling;
                }

                if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                    feedbackElement.remove();
                }

                // Agregar el nuevo mensaje de error si existe
                if (error) {
                    input.classList.add('is-invalid');
                    input.classList.add('shake-invalid');

                    setTimeout(() => {
                        input.classList.remove('shake-invalid');
                    }, 500);

                    const feedback = document.createElement('div');
                    feedback.classList.add('invalid-feedback');
                    feedback.textContent = error.message;

                    if (parent.classList.contains('input-group')) {
                        feedback.classList.add('d-block');
                        parent.insertAdjacentElement('afterend', feedback);
                    } else {
                        input.insertAdjacentElement('afterend', feedback);
                    }
                } else {
                    input.classList.remove('is-invalid');
                }
            },

            // Maneja la validación del formulario de servicios antes de enviarlo.
            async handleServiceFormValidation(event) {
                debugLog(`[HTMXServiceHandler] Ejecutando -> handleServiceFormValidation`);

                // Restablecer feedback de validación previo
                resetValidationFeedback();

                // Si todo es válido, iniciar indicador de carga y deshabilitar elementos
                // toggleModalLoading(true, 'loading-indicator-service', 'modal-content-service');
                return true;  // Continuar con el envío
            },

            // Actualiza los datos de la tabla de países, mostrando el skeleton mientras carga.
            updateTableData() {
                debugLog("updateTableData");

                const skeletonBodyVatTable = document.querySelector('#skeletonBodyVatTable');
                const vatTableBody = document.querySelector('#vatCountryTableBody');
                const tableId = '#services-vat-country-list';
                const url = "{% url 'app_sellers:get_contracted_vat_country_list' shortname=object.shortname %}";

                // Destruir DataTable antes de actualizar los datos (función del padre).
                destroyDataTable(tableId);

                // Mostrar el skeleton y ocultar la tabla mientras se carga.
                skeletonBodyVatTable.classList.remove('d-none');
                vatTableBody.classList.add('d-none');

                // Realizar la solicitud AJAX con HTMX.
                htmx.ajax('GET', url, {
                    target: '#contracted-vat-country-rows',
                    swap: 'innerHTML'
                }).then(() => {
                    // Restaurar la tabla después de recibir los datos (función del padre).
                    renderDataTable({id: tableId, type: 'países'});
                    skeletonBodyVatTable.classList.add('d-none');
                    vatTableBody.classList.remove('d-none');
                });
            },

            // Actualiza la tarjeta de un país tras una edición.
            updateCardCountryInServices(pk) {
                debugLog("updateCardCountryInServices");
                const url = "{% url 'app_sellers:get_contracted_vat_country_list' shortname=object.shortname %}";
                const cardCountryId = `#vat-country-card-${pk}`;

                fetch(`${url}?contracted_country_seller_vat_pk=${pk}`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    const cardContainer = document.querySelector(cardCountryId);
                    if (cardContainer) {
                        cardContainer.outerHTML = html;
                        console.log(`Tarjeta del país con PK ${pk} actualizada.`);
                    } else {
                        console.error(`No se encontró la tarjeta con el PK ${pk}`);
                    }
                })
                .catch(error => {
                    console.error(`Error al actualizar la tarjeta del país con PK ${pk}: `, error);
                });
            }
        };

        // ---> Otras de funciones (scope local)

        // Función para obtener los servicios disponibles
        async function getServicesAvailables() {
            let url = "{% url 'app_sellers:get_services_availables' object.shortname %}";
            try {
                const response = await fetch(url);
                if (response.ok) {
                    return await response.json();
                } else {
                    Toast.fire({
                        icon: 'error',
                        title: 'Error al conectar con el servidor'
                    });
                    return {};
                }
            } catch (error) {
                console.error(error);
            }
        }

        // Función para poblar el formulario con los servicios disponibles
        async function populateServicesAvailablesInForm(serviceData) {
            const serviceType = document.querySelector('#id_service_type');
            serviceType.style = 'pointer-events: auto;';
            serviceType.removeAttribute('readonly');
            serviceType.innerHTML = '';

            const placeholderOption = document.createElement('option');
            placeholderOption.value = '';
            placeholderOption.textContent = 'Selecciona el tipo de servicio';
            placeholderOption.disabled = true;
            placeholderOption.selected = true;
            placeholderOption.setAttribute('disabled', 'disabled');
            serviceType.appendChild(placeholderOption);

            for (const service in serviceData) {
                if (serviceData[service].show) {
                    const option = document.createElement('option');
                    option.value = service;
                    option.textContent = serviceData[service].description;
                    serviceType.appendChild(option);
                }
            }
        }

        // Función para abrir el modal de edición de país
        function openAddUpdateCountryActivationDateModal(element) {
            const modal = new bootstrap.Modal(document.getElementById('addUpdateCountryContractedModal'));
            const title = document.getElementById('countryContractedTitleModal');
            const flagImg = document.getElementById('countryContractedFLagModal');
            const countryFlagsURL = flagImg.dataset.src;
            const contractingDateInput = document.getElementById('id_contracted_country_contracting_date');
            const contractingEndDateInput = document.getElementById('id_contracted_country_contracting_discontinue');
            const activationDateInput = document.getElementById('id_contracted_country_activation_date'); // Nuevo
            const deactivationDateInput = document.getElementById('id_contracted_country_deactivation_date'); // Nuevo
            const startContractingDateInput = document.getElementById('id_contracted_country_start_contracting_date');
            const endContractingDateInput = document.getElementById('id_contracted_country_end_contracting_date');
            const collectionDateInput = document.getElementById('id_contracted_country_collection_date');
            const vatNumberInput = document.getElementById('id_contracted_country_vat_number');
            const steuernummerInput = document.getElementById('id_contracted_country_steuernummer');
            const vatPkInput = document.getElementById('id_contracted_country_seller_pk');

            countryUpdateform.reset();
            countryUpdateform.querySelector('#countrySubmitBtn').disabled = false;

            // Limpiar errores anteriores
            vatNumberInput.classList.remove('is-invalid');
            const feedback = vatNumberInput.nextElementSibling;
            if (feedback) feedback.textContent = '';

            const countryData = {
                pk: element.getAttribute('data-vat-pk'),
                name: element.getAttribute('data-vat-name'),
                countryCode: element.getAttribute('data-vat-country-code'),
                isContracted: element.getAttribute('data-is-contracted') === 'True',
                contractingDate: element.getAttribute('data-contracting-date'),
                contractingEndDate: element.getAttribute('data-contracting-end-date'),
                startContractingDate: element.getAttribute('data-start-contracting-date'),
                activationDate: element.getAttribute('data-activation-date'), // Nuevo
                deactivationDate: element.getAttribute('data-deactivation-date'), // Nuevo
                endContractingDate: element.getAttribute('data-end-contracting-date'),
                collectionDate: element.getAttribute('data-collection-date'),
                vatNumber: element.getAttribute('data-vat-number'),
                steuernummer: element.getAttribute('data-steuernummer')
            };

            // Obtener el PK y la URL del formulario
            vatPkInput.value = countryData.pk;
            countryUpdateform.setAttribute('hx-target', `#vat-row-${countryData.pk}, #vat-country-card-${countryData.pk}`);
            countryUpdateform.setAttribute('hx-swap', 'outerHTML');

            // Actualizar el contenido del modal
            flagImg.src = `${countryFlagsURL}${countryData.countryCode.toLowerCase()}.svg`;
            title.textContent = countryData.name;

            // Establecer los valores de los inputs de fecha
            contractingDateInput.value = countryData.contractingDate;
            contractingEndDateInput.value = countryData.contractingEndDate;
            activationDateInput.value = countryData.activationDate; // Nuevo
            deactivationDateInput.value = countryData.deactivationDate; // Nuevo
            startContractingDateInput.value = countryData.startContractingDate;
            endContractingDateInput.value = countryData.endContractingDate;
            collectionDateInput.value = countryData.collectionDate;
            vatNumberInput.value = countryData.vatNumber;
            steuernummerInput.value = countryData.steuernummer;

            // Resetear el feedback de validación
            resetValidationFeedback()

            // Actualizar los datepickers
            initializeDatePicker();

            modal.show();
        }

        //   -----------------------------------------------------------------------
        //    Manejadores de eventos ---> Eventos del navegador (JavaScript nativo)
        //   -----------------------------------------------------------------------

        // Ejecutar código para añadir un servicio
        addServiceButton.addEventListener('click', async () => {
            addServiceButton.disabled = true;
            const serviceSubmitBtn = document.querySelector('#serviceSubmitBtn');
            serviceSubmitBtn.textContent = 'Añadir';
            serviceSubmitBtn.disabled = false;
            try {
                const data = await getServicesAvailables();
                if (!Object.values(data).some(service => service.show)) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Todos los servicios disponibles ya han sido añadidos'
                    });
                    addServiceButton.disabled = false;
                    return;
                }
                addEditForm.reset();
                addEditForm.setAttribute('hx-target', '#contractedServiceCard');
                addEditForm.setAttribute('hx-swap', 'beforeend');
                populateServicesAvailablesInForm(data);

                const addOrUpdateInput = document.querySelector('#id_request_type');
                addOrUpdateInput.value = 'add';

                const contractedEndDateInput = document.querySelector('#id_contracted_end_date');
                HTMXServiceHandler.toggleEndDateValidationFeedback(contractedEndDateInput, false);

                const addUpdateServiceModalService = new bootstrap.Modal(document.getElementById('addUpdateServiceModalService'), {
                    backdrop: 'static',
                    keyboard: false
                });
                addUpdateServiceTitle.textContent = 'Añadir servicio contratado';
                addUpdateServiceModalService.show();
                addServiceButton.disabled = false;
            } catch (error) {
                debugLog(`Error ${error} al añadir un servicio`);
            }
        });

        // Ejecutar código para detectar cuando el modal se cierra y realizar acciones
        document.addEventListener('DOMContentLoaded', function() {
            const addUpdateServiceModalService = document.getElementById('addUpdateServiceModalService');
            const addEditForm = document.getElementById('addUpdateServiceForm');
            // Cuando el modal se cierra
            addUpdateServiceModalService.addEventListener('hidden.bs.modal', function () {
                // Restablecer el formulario
                addEditForm.reset();

                // Ocultar las fechas de Hacienda
                const haciendaDates = document.getElementById('haciendaDates');
                haciendaDates.style.display = 'none';

                // Habilitar de nuevo el selector de tipo de servicio
                const serviceTypeInput = document.querySelector('#id_service_type');
                serviceTypeInput.removeAttribute('readonly');
                serviceTypeInput.style.pointerEvents = 'auto';

                // Restablecer feedback de validación de errores
                resetValidationFeedback();
            });
        });

        // Ejecutar código para Mostrar/ocultar los campos de Hacienda y fechas de compra según el servicio seleccionado
        document.querySelector('#id_service_type').addEventListener('change', function () {
            const selectedService = this.value;
            const haciendaDates = document.getElementById('haciendaDates');
            const purchaseDatesSection = document.getElementById('purchase_dates_section');
            const cancellationDatesSection = document.getElementById('cancellation_dates_section');
            const registrationPurchaseLabel = document.getElementById('registration_purchase_label');
            const cancellationPurchaseLabel = document.getElementById('cancellation_purchase_label');

            // Ocultar todas las secciones de fechas específicas por defecto
            haciendaDates.style.display = 'none';
            purchaseDatesSection.style.display = 'none';
            cancellationDatesSection.style.display = 'none';

            // Limpiar los valores
            document.getElementById('id_tax_agency_accounting_date').value = '';
            document.getElementById('id_tax_agency_accounting_end_date').value = '';
            document.getElementById('id_service_registration_purchase_date').value = '';
            document.getElementById('id_service_cancellation_purchase_date').value = '';

            // Mostrar/ocultar fechas específicas según el servicio
            if (selectedService === 'contracted_accounting') {
                // Para Contabilidad ES: Mostrar fechas de Hacienda y fechas de compra estándar
                haciendaDates.style.display = 'block';
                purchaseDatesSection.style.display = 'block';
                cancellationDatesSection.style.display = 'block';

                // Actualizar etiquetas para fechas de compra
                registrationPurchaseLabel.textContent = 'Fecha de compra del servicio';
                cancellationPurchaseLabel.textContent = 'Fecha de compra de la baja';
            }
            else if (['contracted_maintenance_llc', 'contracted_accounting_usa', 'contracted_accounting_usa_basic'].includes(selectedService)) {
                // Para servicios LLC: Mostrar fechas de compra LLC
                purchaseDatesSection.style.display = 'block';
                cancellationDatesSection.style.display = 'block';

                // Actualizar etiquetas para fechas de compra
                registrationPurchaseLabel.textContent = 'Fecha de compra del servicio LLC';
                cancellationPurchaseLabel.textContent = 'Fecha de compra de la baja LLC';
            }
        });

        // Ejecutar código para manejar la edición y eliminación de servicios
        document.querySelector('#contractedServiceCard').addEventListener('click',async function(event) {
            if (event.target && event.target.matches('#edit-service')) {
                const editButton = event.target;
                const serviceId = editButton.getAttribute('data-service-id');
                const serviceTypeName = editButton.getAttribute('data-service-name');
                const serviceStartDate = editButton.getAttribute('data-service-start-date');
                const serviceEndDate = editButton.getAttribute('data-service-end-date');
                const servicePaymentDate = editButton.getAttribute('data-service-payment-date');
                const haciendaDatesSection = document.getElementById('haciendaDates');
                const purchaseDatesSection = document.getElementById('purchase_dates_section');
                const cancellationDatesSection = document.getElementById('cancellation_dates_section');
                const registrationPurchaseLabel = document.getElementById('registration_purchase_label');
                const cancellationPurchaseLabel = document.getElementById('cancellation_purchase_label');

                // Configurar el botón de envío en el modal
                const serviceSubmitBtn = document.querySelector('#serviceSubmitBtn');
                serviceSubmitBtn.textContent = 'Actualizar';
                serviceSubmitBtn.disabled = false;

                // Configurar el campo de tipo de servicio
                const serviceTypeInput = document.querySelector('#id_service_type');
                serviceTypeInput.innerHTML = '';
                const option = document.createElement('option');
                option.value = serviceId;
                option.textContent = serviceTypeName;
                option.selected = true;
                serviceTypeInput.appendChild(option);
                serviceTypeInput.setAttribute('readonly', 'readonly');
                serviceTypeInput.style.pointerEvents = 'none';

                // Establecer las fechas de inicio y fin
                const contractedDateInput = document.querySelector('#id_contracted_date');
                contractedDateInput.value = serviceStartDate;
                const contractedEndDateInput = document.querySelector('#id_contracted_end_date');
                contractedEndDateInput.value = serviceEndDate;
                const contractedPaymentDateInput = document.querySelector('#id_contracted_payment_date');
                contractedPaymentDateInput.value = servicePaymentDate;

                // Ocultar todas las secciones de fechas específicas por defecto
                haciendaDatesSection.style.display = 'none';
                purchaseDatesSection.style.display = 'none';
                cancellationDatesSection.style.display = 'none';

                // Limpiar valores de campos ocultos
                document.getElementById('id_tax_agency_accounting_date').value = '';
                document.getElementById('id_tax_agency_accounting_end_date').value = '';
                document.getElementById('id_service_registration_purchase_date').value = '';
                document.getElementById('id_service_cancellation_purchase_date').value = '';

                // Mostrar y configurar fechas específicas según el servicio
                if (serviceId === 'contracted_accounting') {
                    // Para Contabilidad ES: mostrar fechas de Hacienda y fechas de compra estándar
                    haciendaDatesSection.style.display = 'block';
                    purchaseDatesSection.style.display = 'block';
                    cancellationDatesSection.style.display = 'block';

                    // Actualizar etiquetas para fechas de compra
                    registrationPurchaseLabel.textContent = 'Fecha de compra del servicio';
                    cancellationPurchaseLabel.textContent = 'Fecha de compra de la baja';

                    // Recuperar y establecer fechas de Hacienda
                    const taxAgencyAccountingDate = editButton.getAttribute('data-tax-agency-accounting-date');
                    const taxAgencyAccountingEndDate = editButton.getAttribute('data-tax-agency-accounting-end-date');
                    document.querySelector('#id_tax_agency_accounting_date').value = taxAgencyAccountingDate || '';
                    document.querySelector('#id_tax_agency_accounting_end_date').value = taxAgencyAccountingEndDate || '';
                }
                else if (['contracted_maintenance_llc', 'contracted_accounting_usa', 'contracted_accounting_usa_basic'].includes(serviceId)) {
                    // Para servicios LLC: mostrar fechas de compra LLC
                    purchaseDatesSection.style.display = 'block';
                    cancellationDatesSection.style.display = 'block';

                    // Actualizar etiquetas para fechas de compra
                    registrationPurchaseLabel.textContent = 'Fecha de compra del servicio LLC';
                    cancellationPurchaseLabel.textContent = 'Fecha de compra de la baja LLC';
                }

                // Establecer valores de fechas de compra (solo si se están mostrando)
                if (purchaseDatesSection.style.display === 'block') {
                    document.querySelector('#id_service_registration_purchase_date').value =
                        editButton.getAttribute('data-service-registration-purchase-date') || '';
                    document.querySelector('#id_service_cancellation_purchase_date').value =
                        editButton.getAttribute('data-service-cancellation-purchase-date') || '';
                }

                // Resetear el feedback de validación
                resetValidationFeedback()
                addEditForm.setAttribute('hx-target', `#${serviceId}`);
                addEditForm.setAttribute('hx-swap', 'outerHTML');
                const addOrUpdate = document.querySelector('#id_request_type');
                addOrUpdate.value = 'update';

                const addUpdateServiceModalService = new bootstrap.Modal(document.getElementById('addUpdateServiceModalService'));
                addUpdateServiceTitle.textContent = 'Editar servicio contratado';
                addUpdateServiceModalService.show();
                initializeDatePicker();
            } else if (event.target && event.target.matches('#delete-service-drop-btn')) {
                const deleteButton = event.target;
                const serviceId = deleteButton.getAttribute('data-service-id');
                const serviceIdInput = document.querySelector('#deleting_service_id');
                serviceIdInput.value = serviceId;

                // Asegurarse de que no se envíen las fechas de Hacienda si no es "Contabilidad ES"
                const haciendaDatesSection = document.getElementById('haciendaDates');
                if (serviceId === 'contracted_accounting') {
                    // Verificar si hay una Tarifa Plana asociada
                    const formData = new FormData();
                    formData.append('deleting_service_id', serviceId);
                    formData.append('delete-service', 'true');

                    const response = await fetch("{% url 'app_sellers:update_contracted_service' object.shortname %}", {
                        method: 'POST',
                        headers: {'X-CSRFToken': '{{ csrf_token }}'},
                        body: formData
                    });

                    const result = await response.json();
                    console.log(result);

                    //26-03-25: Delete validation | 0x7xyp01
                    /*
                    if (result.confirm_flat_rate_removal) {
                        // Si hay Tarifa Plana, mostrar alerta SweetAlert
                        Swal.fire({
                            title: "¡Atención!",
                            text: result.message,
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonText: "Sí, eliminar todo",
                            cancelButtonText: "Cancelar",
                            reverseButtons: true
                        }).then(async (confirmResult) => {
                            if (confirmResult.isConfirmed) {
                                // Confirmar eliminación también de Tarifa Plana
                                formData.append('confirm_flat_rate', 'true');

                                const finalResponse = await fetch("{% url 'app_sellers:update_contracted_service' object.shortname %}", {
                                    method: 'POST',
                                    headers: {'X-CSRFToken': '{{ csrf_token }}'},
                                    body: formData
                                });

                                const finalResult = await finalResponse.json();

                                if (finalResult.success) {
                                    Swal.fire("¡Eliminado!", "Contabilidad ES y Tarifa Plana han sido eliminadas.", "success");
                                    setTimeout(() => location.reload(), 1500);
                                } else {
                                    Swal.fire("Error", "No se pudo eliminar el servicio.", "error");
                                }
                            }
                        });
                        return;
                    }
                    */
                } else {
                    haciendaDatesSection.style.display = 'none';
                }

                //deleteForm.setAttribute('hx-target', `#${serviceId}`);
                //deleteForm.setAttribute('hx-swap', 'outerHTML');

                const confirmDeleteServiceModal = new bootstrap.Modal(document.getElementById('confirmDeleteServiceModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                confirmDeleteServiceModal.show();
            }
        });
    </script>
{% endblock %}