$(document).ready(function () {
    const allSelectElements = document.querySelectorAll('select:not([multiple]):not([record-form]):not([phone_prefix])');
    let selectChoicesDict = {}
    for (let i = 0; i < allSelectElements.length; i++) {
        const element = allSelectElements[i];
        const choicesInstance = new Choices(element, {
            placeholderValue: 'Selecciona una opción',
            searchPlaceholderValue: 'Escribe para buscar',
            noResultsText: 'No se encontraron resultados',
            itemSelectText: 'Seleccionar',
            position: 'bottom',
            allowHTML: true,
        });
        selectChoicesDict[element.id] = choicesInstance;
        element.addEventListener('change', function() {
            console.log(`Se ha seleccionado: ${element.name}`);
            if(element.name == 'member_address_country'){
                selectChoicesDict['id_activity_country'].setChoiceByValue(element.value);
                $('#id_activity_country').trigger('change');
            }
        });
    }

    // select2 para los multiples selects
    const multipleCountrySelector = new Choices('select[multiple]:not([record-form])', {
        searchEnabled: true,
        placeholderValue: 'Selecciona las opciones',
        removeItemButton: true,
        itemSelectText: 'Seleccionar',
        allowHTML: true,
        position: 'bottom',
    });
    // $('select[multiple]:not([record-form])').select2({
    //     placeholder: 'Selecciona las opciones',
    //     multiple: true,
    //     allowClear: true,
    //     language: "es",
    // });

    const multiplePhoneCodeSelector = new Choices('select[phone_prefix]', {
        searchEnabled: true,
        itemSelectText: '',
        position: 'bottom',
        allowHTML: true,
    });
    // $('#id_contact_phone_0').select2({
    //     placeholder: 'Prefijo',
    //     allowClear: true,
    //     language: "es",
    // });

    $('select.is-invalid').each(function () {
        $(this).next('.select2-container').find('.select2-selection').addClass('is-invalid');
    });

    //MOSTRAR LOS SPINNERS DE CARGA
    const swapSpinnerZIP = () => {
        $('#spinner_zip').toggleClass('d-none');
        $('#spinner_city').toggleClass('d-none');
        $('#spinner_state').toggleClass('d-none');

        $('#id_seller_address_zip').attr('readonly') ? $('#id_seller_address_zip').removeAttr('readonly') : $('#id_seller_address_zip').attr('readonly', true);
    }

    //FUNCIÓN QUE RECIBE LA CIUDAD Y EL ESTADO Y LOS ESTABLECE EN LOS INPUTS CORRESPONDIENTES
    const setCityState = (city, state) => {
        if (is_processed !='True'){
            $('#id_seller_address_city').val(city);
            selectChoicesDict['id_seller_address_state'].setChoiceByValue(state);
            $('#id_seller_address_state').trigger('change');
            $('#id_seller_address_city').trigger('change');
        }
    }

    //MOSTRAR INVALID FEEDBACK EN EL CODIGO POSTAL SI NO ES CORRECTO
    const validateZIP = (isValid) => {
        let p = $('#id_seller_address_zip').nextAll('.invalid-feedback:first');
        if (isValid) {
            p.remove();
        } else {
                $('#id_seller_address_zip').addClass('is-invalid');
                p = $('<p class="invalid-feedback"></p>');
                p.text('El código postal no es válido');
                $('#id_seller_address_zip').parent().append(p);
        }
    }

    //ESTABLECER EL VALOR DE LA CIUDAD Y DEL ESTADO SEGÚN EL CÓDIGO POSTAL DE USA
    const inputElement = document.getElementById('id_seller_address_zip');
    async function fetchCityState(zip) {
        swapSpinnerZIP();
        try {
            const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
            let response = await fetch(url_zip, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                },
                body: JSON.stringify({ zip_code: zip }),
            });

            if (response.ok) {
                let data = await response.json();
                setCityState(data.city, data.state);
                validateZIP(true);
            } else {
                setCityState('', '');
                validateZIP(false);
            }
        } catch (error) {
            setCityState('', '');
            validateZIP(false);
        } finally {
            swapSpinnerZIP();
        }
    }

    if (inputElement && inputElement.value.length === 5) {
        fetchCityState(inputElement.value);
    }

    if (inputElement) {
        if (inputElement.value === '') {
            $('#id_seller_address_city').val('');
            selectChoicesDict['id_seller_address_state'].setChoiceByValue('');
        }
        inputElement.addEventListener('input', () => {
            if (inputElement.value.length > 5) {
                inputElement.value = inputElement.value.slice(0, 5); // Limita a 5 caracteres
            }

            if (inputElement.value.length === 5) {
                fetchCityState(inputElement.value);
            }
        });
    } 
    

    const updateProgressBar = () => {
        const activeTab = $('#myTab a.nav-link.active');
        const index = $('#myTab a.nav-link').index(activeTab);
        const progress = (index + 1) / $('#myTab a.nav-link').length * 100;

        $('.progress-bar').css('width', progress + '%');
        $('.progress-bar').attr('aria-valuenow', progress);
    }

    updateProgressBar();
    $('#myTab a').on('shown.bs.tab', e => updateProgressBar());


    //esto muestra el ícono de alerta en las tabs cuando faltan campos por completar
    removeHiddenInTab();

    const tabs = $('#myTab a.nav-link');
    tabs.each(function () {
        const tabId = $(this).attr('id');
        const tab = $('#' + tabId);
        const tabPaneId = tab.attr('aria-controls');
        const tabPane = $('#' + tabPaneId);
        const invalidElements = tabPane.find('.is-invalid');
        if (invalidElements.length > 0) {
            changeTab(tabId);
            setTimeout(function () {
                invalidElements.first()[0].focus();
                invalidElements.first()[0].scrollIntoView();
            }, 600);
            return false;
        }
    });

    const toogleShowReport1Tag = (radio) => {
        const tab1 = $('#report1-tab')
        const report1 = document.querySelector('#report1');
        const tab2 = $('#report2-tab');
        const report2 = document.querySelector('#report2');
        const tab1Parent = tab1.parent();
        const tab2Parent = tab2.parent();

        if (radio.checked && !limitRecords) {
            const showReport = radio.value === 'True';

            if (showReport) {
                tab1Parent.removeClass('d-none'); //muestro el tab1
                tab1.addClass('active'); //agrego la clase active al tab1
                report1.classList.add('show', 'active'); //agrego la clase show y active al report1

                if (!tab2Parent.hasClass('d-none')) {
                    tab2.removeClass('active');
                    report2.classList.remove('show', 'active');
                }

            } else {
                tab1Parent.addClass('d-none'); //oculto el tab1
                report1.classList.remove('show', 'active'); //quito la clase show y active del report1

                if (!tab2Parent.hasClass('d-none')) {
                    tab2.addClass('active');
                    report2.classList.add('show', 'active');
                }

            }

            // setInputsRequired(tab1, radio.value);
        }
        togglereportBody();
    };

    const toogleShowReport2Tag = (countrySelected) => {
        const tab1 = $('#report1-tab');
        const report1 = document.querySelector('#report1');
        const tab2 = $('#report2-tab');
        const report2 = document.querySelector('#report2');
        const tab2Parent = tab2.parent();
        const tab1Parent = tab1.parent();

        if (countrySelected) {
            if (countrySelected === 'US') {
                tab2Parent.addClass('d-none'); //oculto el tab2
                tab2.removeClass('active'); //quito la clase active del tab2
                report2.classList.remove('show', 'active'); //quito la clase show y active del report2

                if (!tab1Parent.hasClass('d-none')) {
                    tab1.addClass('active');
                    report1.classList.add('show', 'active');
                }

            } else {
                tab2Parent.removeClass('d-none');
                if (tab1Parent.hasClass('d-none')) {
                    tab2.addClass('active');
                    report2.classList.add('show', 'active');
                }
            }
        }
        togglereportBody();
    };

    const toogleShowCasilla4b3 = (radio) => {
        const casilla4b3 = document.getElementById('id_casilla_4b3');
        const casilla1f = document.getElementById('id_casilla_1F_all_previous_years');
        if (radio.checked) {
            const showCasilla4b3 = radio.value === 'True';
            if (showCasilla4b3) {
                casilla4b3.readOnly = true;
                casilla4b3.required = false;
                casilla4b3.value = null;

                casilla1f.readOnly = true;
                casilla1f.required = false;
                casilla1f.value = null;
            } else {
                casilla4b3.required = true;
                casilla4b3.readOnly = false;
                casilla1f.required = true;
                casilla1f.readOnly = false;

                casilla4b3.focus();
            }
        }else{
            casilla4b3.readOnly = true;
            casilla4b3.required = false;
            // casilla4b3.value = null;

            casilla1f.readOnly = true;
            casilla1f.required = false;
            // casilla1f.value = null;
        }
    };


    const togglereportBody = () => {
        const reportTabs = $('#secondTab a.nav-link');
        let isAnytabActive = false;
        reportTabs.each(function () {
            const tabId = $(this).attr('href');
            if ($(tabId).hasClass('active')) {
                isAnytabActive = true;
                return false;
            }
        });

        if (isAnytabActive) {
            $('#reportBody').removeClass('d-none');
            $('#noReporBody').addClass('d-none');
        } else {
            $('#noReporBody').removeClass('d-none');
            $('#reportBody').addClass('d-none');
        }
    }


    const setInputsRequired = (report, required) => {
        const tabPaneId = report.attr('aria-controls');
        const tabPane = $('#' + tabPaneId);
        // obtener los inputs del tabpane que no sea el checkbox de eliminar del formset ni campos hidden, tambien select y textarea
        const inputs = tabPane.find('input:not([name*="-DELETE"]):not([type="hidden"]), select, textarea');
        // ponerle required a los inputs si el radio button seleccionado es True y quitar la clase is-invalid
        inputs.each((i, input) => {
            input.required = required === 'True';
            $(input).removeClass('is-invalid');
            // Quitar la clase is-invalid del contenedor de Select2
            if ($(input).hasClass('select2-hidden-accessible')) {
                const container = $(this).next('.select2-container');
                container.find('.select2-selection').removeClass('is-invalid');
                container.next('.invalid-feedback').remove();
            }
            if ($(input).is(':radio')) {
                const parent = $(input).parent();
                parent.nextAll('.invalid-feedback').remove();

                const grandparent = $(input).parent().parent();
                grandparent.find('input:radio').removeClass('is-invalid');
            }
            $(input).next('.invalid-feedback').remove();
        });
    }

    const isSlSelfEmployed2 = document.querySelectorAll('input[name="is_sl_self_employed"]');
    const taxResidenceCountry = document.getElementById('id_tax_residence_country');
    const countryRegistration = document.getElementById('id_country_registration');
    const isFirtsReport = document.querySelectorAll('input[name="is_first_year"]');

    if (!countryRegistration.value) {
        selectChoicesDict['id_country_registration'].setChoiceByValue('US');
    }
    selectChoicesDict['id_country_registration'].disable();

    const itinElements = ['id_is_itin_0', 'id_is_itin_1'].map(id => document.getElementById(id));

    const toggleItinNumberDisplay = (value) => {
        const itinNumber = document.getElementById('itin_number');
        itinNumber.querySelector('input').readOnly = value === 'False' || value === '' ? true : false;
        if (value == 'True' && previous_pm5472 == 'False'){
            itinNumber.querySelector('input').required = value === 'True';
        }else if (value == ''){
            itinNumber.querySelector('input').readOnly = true;
            itinNumber.querySelector('input').required = false;
            itinNumber.querySelector('input').value = null;
        } else if (value == 'True' && previous_pm5472 == 'True' && itinNumber.value != '' && itinNumber.querySelector('input').value != ''){ 
            itinNumber.querySelector('input').readOnly = true;
            itinNumber.querySelector('input').required = true;
        }else{
            itinNumber.querySelector('input').required = false;
            itinNumber.querySelector('input').value = null;
        }
    }

    itinElements.forEach(element => {
        element.addEventListener('change', (e) => toggleItinNumberDisplay(e.target.value));
        if (itinElements[0].checked){
            toggleItinNumberDisplay('True');
        }else if (itinElements[0].checked == false && itinElements[1].checked == false){
            toggleItinNumberDisplay('');
        }else{
            toggleItinNumberDisplay('False');
        }
    });

    togglereportBody();

    toogleShowReport2Tag(taxResidenceCountry.value);

    isSlSelfEmployed2.forEach((radio) => {
        radio.addEventListener('change', () => toogleShowReport1Tag(radio));
        toogleShowReport1Tag(radio);
    });

    isFirtsReport.forEach((radio) => {
        radio.addEventListener('change', () => toogleShowCasilla4b3(radio));
        toogleShowCasilla4b3(radio);
    });


    $('#id_tax_residence_country').on('change', function () {
        const countrySelected = $(this).val();
        toogleShowReport2Tag(countrySelected)
    });

    const allInputs = document.querySelectorAll('input, select, textarea');
    const infoTab = document.getElementById('finish');
    if (infoTab) {
        allInputs.forEach(input => updateSummaryInformation(input.name));
    }

});

const checkErrors = () => {
    const form = $('form');
    const errorElements = form.find(':invalid');

    errorElements.each(function () {
        const tabId = $(this).closest('.tab-pane').attr('id') + '-tab';
        const tab = $('#' + tabId);
        tab.find('.feather').removeClass('hidden');
    });
}

function removeHiddenInTab() {
    const tabs = $('#myTab a.nav-link');
    tabs.each(function () {
        const tabId = $(this).attr('id');
        const tab = $('#' + tabId);
        const tabPaneId = tab.attr('aria-controls');
        const tabPane = $('#' + tabPaneId);
        const invalidElements = tabPane.find('.is-invalid');
        if (invalidElements.length > 0) {
            tab.find('.feather').removeClass('hidden');
        }
    });

    const secondTabs = $('#secondTab a.nav-link');
    secondTabs.each(function () {
        const tabId = $(this).attr('id');
        const tab = $('#' + tabId);
        const tabPaneId = tab.attr('aria-controls');
        const tabPane = $('#' + tabPaneId);
        const invalidElements = tabPane.find('.is-invalid');
        if (invalidElements.length > 0) {
            tab.find('.feather').removeClass('hidden');
        }
    });
}

function addHiddenInTab() {
    const tabs = $('#myTab a.nav-link');
    tabs.each(function () {
        const tabId = $(this).attr('id');
        const tab = $('#' + tabId);
        const tabPaneId = tab.attr('aria-controls');
        const tabPane = $('#' + tabPaneId);
        let invalidElements = tabPane.find('.is-invalid');
        // check if the first error element is a fieldset, and remove it from the list
        if (invalidElements.first().is('fieldset')) {
            invalidElements = invalidElements.slice(1);
        }
        if (invalidElements.length === 0) {
            tab.find('.feather').addClass('hidden');
        }
    });
}

const changeTab = (tabId) => {
    const elem = document.getElementById(tabId);
    // llamada recursiva para cambiar el tab si esta contenido en un tab padre
    if (elem.closest('.tab-pane')) {
        changeTab(elem.closest('.tab-pane').id + '-tab');
    }
    const tab = new bootstrap.Tab(elem);
    tab.show();
    elem.scrollIntoView();
}

const submitModel = () => {
    const form = $('#form5472-1120');

    if (!form[0].checkValidity()) {
        let errorElements = form.find(':invalid');

        errorElements.each(function () {
            $(this).addClass('is-invalid');
            // Añadir la clase is-invalid al contenedor de Select2
            if ($(this).hasClass('select2-hidden-accessible')) {
                $(this).next('.select2-container').find('.select2-selection').addClass('is-invalid');
            }
            // si es un radiobutton, añadir la clase is-invalid al contenedor
            if ($(this).is(':radio')) {
                const parent = $(this).parent();
                let p = parent.nextAll('.invalid-feedback:first');
                if (p.length === 0) {
                    p = $('<p class="invalid-feedback"></p>');
                    // si el validationMessage viene de no cumplir el pattern, se cambia el mensaje por el title
                    if (this.validity.patternMismatch) {
                        p.text(this.title);
                    } else {
                        p.text(this.validationMessage);
                    }
                    parent.parent().append(p);
                } else {
                    if (this.validity.patternMismatch) {
                        p.text(this.title);
                    } else {
                        p.text(this.validationMessage);
                    }
                }
            } else {
                let parentInputElement = '';
                let p = $(this).nextAll('.invalid-feedback:first');
                if ($(this).hasClass('choices__input')) {
                    $(this).parent().addClass('choices_is_invalid');
                    parentInputElement = $(this).closest('.col-lg-4');
                    parentInputElement.find('.invalid-feedback').remove();
                } else if ($(this).parent().hasClass('input-group')) {
                    parentInputElement = $(this).parent().parent();
                    parentInputElement.find('.invalid-feedback').remove();
                } else {
                    parentInputElement = $(this).parent();
                }

                if (p.length === 0) {
                    p = $('<p class="invalid-feedback"></p>');
                    // si el validationMessage viene de no cumplir el pattern, se cambia el mensaje por el title
                    if (this.validity.patternMismatch) {
                        p.text(this.title);
                    } else {
                        p.text(this.validationMessage);
                    }
                    parentInputElement.append(p);
                } else {
                    if (this.validity.patternMismatch) {
                        p.text(this.title);
                    } else {
                        p.text(this.validationMessage);
                    }
                }
            }
        });

        const firstErrorElement = errorElements.first();
        const tabId = firstErrorElement.closest('.tab-pane').attr('id') + '-tab';
        const currentTab = $('#myTab a.nav-link.active');
        const currentTabId = currentTab.attr('id');

        if (currentTabId !== tabId) {
            changeTab(tabId);
            setTimeout(function () {
                firstErrorElement[0].focus();
                firstErrorElement[0].scrollIntoView();
            }, 600);
        }
        removeHiddenInTab();
        return false;
    } else {
        if (!$('#reportBody').hasClass('d-none')) {
            const tabReport1 = !$('#report1-tab').parent().hasClass('d-none');
            const tabReport2 = !$('#report2-tab').parent().hasClass('d-none');
            // let report1Rows = $('#recordsTable1').find('tr').length;
            // let report2Rows = $('#recordsTable2').find('tr').length;
            let report1Rows = $('#recordsTable1').find('tbody tr').first().find('td').hasClass('dataTables_empty');
            let report2Rows = $('#recordsTable2').find('tbody tr').first().find('td').hasClass('dataTables_empty');

            if (tabReport1 && report1Rows == true || tabReport2 && report2Rows == true) {
                $('#reportAlert').removeClass('d-none');
                // changeTab('reportable-tab');
                // Swal.fire({
                //     icon: 'warning',
                //     title: 'Formulario Incompleto!',
                //     text: "Debes añadir al menos 1 registro en el/los reporte(s) de transacciones",
                // });
                // return;
            } else {
                $('#reportAlert').addClass('d-none');
            }
        } else {
            $('#reportAlert').addClass('d-none');
        }
        event.preventDefault();
        $('#confirmForm').modal('show');
    }
}

const submitForm = (e) => {
    $('#signModal').modal('hide');
    const loadingModal = document.getElementById('LoadingModal');
    const modal = new bootstrap.Modal(loadingModal);
    modal._config.backdrop = 'static';
    modal._config.keyboard = false;
    modal.show();
}

const saveForm = () => {
    // quitar todos los required del form antes de hacer el submit
    $('#form5472-1120').find(':input').removeAttr('required');
    $('<input />').attr('type', 'hidden')
        .attr('name', 'save_edit')
        .attr('value', 'true')
        .appendTo('#form5472-1120');
    const buttons = document.querySelectorAll('button[name="save-submit"]');
    buttons.forEach((button) => {
        button.disabled = true;
    });
    document.getElementById('finalizarButton').disabled = true;
    $('#form5472-1120').submit();
}

$('#form5472-1120').find(':input').on('input change', function () {
    const name = $(this).attr('name');
    updateSummaryInformation(name);
    if (this.checkValidity()) {
        $(this).removeClass('is-invalid');
        // Quitar la clase is-invalid del contenedor de Select2
        if ($(this).hasClass('select2-hidden-accessible')) {
            const container = $(this).next('.select2-container');
            container.find('.select2-selection').removeClass('is-invalid');
            container.next('.invalid-feedback').remove();
        }
        if ($(this).hasClass('choices__input')) {
            parentInputElement = $(this).closest('.col');
            parentInputElement.find('.invalid-feedback').remove();
            $(this).parent().removeClass('choices_is_invalid');
        }
        if ($(this).is(':radio')) {
            const parent = $(this).parent();
            parent.nextAll('.invalid-feedback').remove();

            const grandparent = $(this).parent().parent();
            grandparent.find('input:radio').removeClass('is-invalid');

            if(name.includes('is_first_year') && $(this).val() == 'True'){
                $('#id_casilla_4b3').removeClass('is-invalid');
                $('#id_casilla_1F_all_previous_years').removeClass('is-invalid');
                $('#id_casilla_4b3').next('p.invalid-feedback').remove();
                $('#id_casilla_1F_all_previous_years').next('p.invalid-feedback').remove();
            }
        }

        if ($(this).parent().hasClass('input-group')) {
            $(this).parent().next('.invalid-feedback').remove();
        }

        // si el name contiene la palabra contact_phone, se debe validar que el campo no esté vacío
        if (name.includes('contact_phone')) {
            const grandparent = $(this).parent().parent();
            grandparent.next('.invalid-feedback').remove();
        }
        $(this).next('.invalid-feedback').remove();
        addHiddenInTab();
    } else {
        $(this).addClass('is-invalid');
        // Añadir la clase is-invalid al contenedor de Select2
        if ($(this).hasClass('select2-hidden-accessible')) {
            $(this).next('.select2-container').find('.select2-selection').addClass('is-invalid');
        }
        let p = $(this).nextAll('.invalid-feedback:first');
        if (p.length === 0) {
            p = $('<p class="invalid-feedback"></p>');
            // si el validationMessage viene de no cumplir el pattern, se cambia el mensaje por el title
            if (this.validity.patternMismatch) {
                p.text(this.title);
            } else {
                p.text(this.validationMessage);
            }
            if ($(this).parent().hasClass('input-group')) {
                $(this).parent().parent().append(p);

            } else if ($(this).hasClass('choices__input')) {
                let parentInputElement = $(this).closest('.col');
                if (parentInputElement.find('.invalid-feedback').length === 0) {
                    parentInputElement.append(p); 
                }

                $(this).parent().addClass('choices_is_invalid');

            } else {
                $(this).parent().append(p);
            }

        } else {
            if (this.validity.patternMismatch) {
                p.text(this.title);
            } else {
                p.text(this.validationMessage);
            }
        }
        removeHiddenInTab();
    }
});

$('select').on('select2:select select2:unselect', () => {
    $(this).trigger('change');
});


const processtForm = (newSign) => {
    form = $('#form5472-1120');
    if (newSign) {
        if(isCanvasEmpty(650)){ // Comprobamos que la firma DIBUJADA no esté vacía y que tenga mal menos 650 pixeles
            Swal.fire({
                title: 'La firma es incorrecta o está vacía.',
                icon: 'error',
                showCancelButton: false,
                confirmButtonText: 'Aceptar',
                customClass: {
                confirmButton: 'btn_green',
                },
              });
            enableSignatureCanva(true);
            return;
        }
        saveSignature().then(send => { 
            if (send === true) {
                $('<input />').attr('type', 'hidden')
                    .attr('name', 'proccess-submit')
                    .attr('value', 'true')
                    .appendTo('#form5472-1120');
                form.submit();
                submitForm();
            } else {
                Swal.fire({
                    title: 'No se pudo guardar la firma.',
                    icon: 'error',
                    showCancelButton: false,
                    confirmButtonText: 'Aceptar',
                    customClass: {
                    confirmButton: 'btn_green',
                    },
                  });
            }
        });
    }else{
        isSavedSignatureEmpty(650) // Comprobamos que la firma guardada no esté vacía y que tenga mal menos 650 pixeles
            .then(result => {
                if (result){
                    Swal.fire({
                        title: 'La firma es incorrecta o está vacía.',
                        icon: 'error',
                        showCancelButton: false,
                        confirmButtonText: 'Aceptar',
                        customClass: {
                        confirmButton: 'btn_green',
                        },
                      });
                    enableSignatureCanva(true);
                    swapSignatureContainers();
                    return;
                }else{
                    $('<input />').attr('type', 'hidden')
                        .attr('name', 'proccess-submit')
                        .attr('value', 'true')
                        .appendTo('#form5472-1120');
                    form.submit();
                    submitForm();
                }
            });
        
    }
        
}

const updateSummaryInformation = (inputName) => {
    const fullName = document.getElementById('full_name_summ');
    const email = document.getElementById('email_summ');

    if (inputName.includes('first_name') || inputName.includes('last_name')) {
        const firstName = document.getElementById('id_first_name');
        const lastName = document.getElementById('id_last_name');
        fullName.innerText = `${firstName.value} ${lastName.value}`;
    }
    if (inputName.includes('email')) {
        const emailInput = document.getElementById('id_email');
        email.innerText = emailInput.value;
    }
    if (inputName.includes('member_country')) {
        const country = document.getElementById('id_member_country');
        let countrySelected = country.value;
        countrySelected = countrySelected.toLowerCase();
        if (!countrySelected) {
            countrySelected = 'un';
        }
        const countrySumm = document.getElementById('member_country_summ');
        const countrySummSrc = `${countrySumm.dataset.src}${countrySelected}.svg`
        countrySumm.src = countrySummSrc;
        countrySumm.onerror = function () {
            this.src = `${countrySumm.dataset.src}xx.svg`;
        };
    }
    if (inputName.includes('contact_phone')) {
        const prefix = document.getElementById('id_contact_phone_0');
        const selectedIndex = prefix.selectedIndex;
        const selectedOption = prefix.options[selectedIndex];
        const selectedText = selectedOption.innerText;
        const startParenIndex = selectedText.indexOf('(');
        const endParenIndex = selectedText.indexOf(')');
        const textInsideParentheses = selectedText.substring(startParenIndex + 1, endParenIndex);
        const phone = document.getElementById('id_contact_phone_1');
        const phoneSumm = document.getElementById('phone_summ');
        if (!phone.value && !prefix.value) {
            phoneSumm.innerText = '---------';
            return;
        }

        phoneSumm.innerText = `${textInsideParentheses} ${phone.value}`;
    }
    if (inputName.includes('passport')) {
        const passport = document.getElementById('id_passport');
        const passportSumm = document.getElementById('passport_summ');
        if (!passport.value) {
            passportSumm.innerText = '---------';
            return;
        }
        passportSumm.innerText = passport.value;
    }
    if (inputName.includes('member_address_first')) {
        const address = document.getElementById('id_member_address_first');
        const addressSumm = document.getElementById('member_address_summ');
        addressSumm.innerText = address.value;
    }
    if (inputName.includes('member_address_city') || inputName.includes('member_address_zip')) {
        const city = document.getElementById('id_member_address_city');
        const zip = document.getElementById('id_member_address_zip');
        const zipCitySumm = document.getElementById('member_zip_city_summ');
        if (!city.value && !zip.value) {
            zipCitySumm.innerText = '---------';
            return;
        }
        zipCitySumm.innerText = `${zip.value} ${city.value}`;
        return;
    }
    if (inputName.includes('member_address_state')) {
        const state = document.getElementById('id_member_address_state');
        const stateSumm = document.getElementById('member_state_summ');
        stateSumm.innerText = state.value;
    }
    if (inputName.includes('member_address_country')) {
        const country = document.getElementById('id_member_address_country');
        const countrySummtext = document.getElementById('address_country_summ');
        countrySummtext.innerText = country.options[country.selectedIndex].text;
    }
    if (inputName.includes('is_sl_self_employed')) {
        const isSlSelfEmployed = document.querySelectorAll('input[name="is_sl_self_employed"]');
        const isSlSelfEmployedSumm = document.getElementById('is_sl_self_employed_summ');
        let checked = false;
        isSlSelfEmployed.forEach((radio) => {
            if (radio.checked) {
                checked = true;
                isSlSelfEmployedSumm.innerText = radio.value === 'True' ? 'Has indicado que eres Freelance o que perteneces a otra empresa' : '';
            }
        });

        if (!checked) {
            isSlSelfEmployedSumm.innerText = '---------';
        }
    }
    if (inputName.includes('tax_residence_country')) {
        const country = document.getElementById('id_tax_residence_country');
        const countrySummtext = document.getElementById('tax_residence_country_summ');
        countrySummtext.innerText = country.options[country.selectedIndex].text;
    }
    if (inputName.includes('activity_country')) {
        const country = document.getElementById('id_activity_country');
        const countrySummtext = document.getElementById('activity_country_summ');
        countrySummtext.innerText = country.options[country.selectedIndex].text;
    }

    if (inputName.includes('name')) {
        const name = document.getElementById('id_name');
        const nameSumm = document.getElementById('seller_name_summ');
        nameSumm.innerText = name.value;
    }
    if (inputName.includes('ein')) {
        const ein = document.getElementById('id_ein');
        const einSumm = document.getElementById('seller_ein_summ');
        einSumm.innerText = `EIN: ${ein.value}`;
    }
    if (inputName.includes('type_of_activity')) {
        const typeActivity = document.getElementById('id_type_of_activity');
        const typeActivitySumm = document.getElementById('type_of_activity_summ');
        typeActivitySumm.innerText = typeActivity.options[typeActivity.selectedIndex].text;
    }
    if (inputName.includes('is_foreign_owned')) {
        const isFODE = document.querySelectorAll('input[name="is_foreign_owned"]');
        const isFODESum = document.getElementById('is_foreign_owned_summ');

        let checked = false;

        isFODE.forEach((radio) => {
            if (radio.checked) {
                checked = true;
                isFODESum.innerText = radio.value === 'True' ? 'No' : 'Sí';
            }
        });

        if (!checked) {
            isFODESum.innerText = '---------';
        }
    }

    if (inputName.includes('incorporation_date')){
        const incorporation_date = document.getElementById('id_incorporation_date');
        const incorporation_date_summ = document.getElementById('incorporation_date_summ');
        if (!incorporation_date.value){
            incorporation_date_summ.innerText = '---------';
            return;
        }
        const formatDate = (dateString) => {
            const [year, month, day] = dateString.split("-");
            return `${day}/${month}/${year}`;
        };
        incorporation_date_summ.innerText = formatDate(incorporation_date.value);
    }

    if (inputName.includes('is_first_year')) {
        const isFirstYear = document.querySelectorAll('input[name="is_first_year"]');
        const isFirstYearSum = document.getElementById('is_first_year_summ');
        const casilla4b3 = document.getElementById('casilla4bRow');
        const casilla1f = document.getElementById('casilla1fRow');
        let checked = false;

        isFirstYear.forEach((radio) => {
            if (radio.checked) {
                checked = true;
                isFirstYearSum.innerText = radio.value === 'True' ? 'Sí' : 'No';
            }
        });

        if (isFirstYearSum.innerText === 'No') {
            casilla4b3.classList.remove('d-none');
            casilla1f.classList.remove('d-none');
        } else {
            casilla4b3.classList.add('d-none');
            casilla1f.classList.add('d-none');
        }

        if (!checked) {
            isFirstYearSum.innerText = '---------';
        }
    }
    if (inputName.includes('casilla_4b3')) {
        const casilla_4b3 = document.getElementById('id_casilla_4b3');
        const casilla_4b3_summ = document.getElementById('casilla_4b3_summ');
        if (!casilla_4b3.value) {
            casilla_4b3_summ.innerText = '---------';
            return;
        }
        casilla_4b3_summ.innerText = casilla_4b3.value;
    }
    if (inputName.includes('casilla_1F_all_previous_years')) {
        const field = document.getElementById('id_casilla_1F_all_previous_years');
        const text_label = document.getElementById('casilla_1f_summ');
        if (!field.value) {
            text_label.innerText = '---------';
            return;
        }
        text_label.innerText = field.value;
    }
    if (inputName.includes('seller_address_first')) {
        const field = document.getElementById('id_seller_address_first');
        const text_label = document.getElementById('seller_address_summ');
        text_label.innerText = field.value;
    }
    if (inputName.includes('seller_address_city') || inputName.includes('seller_address_zip')) {
        const city = document.getElementById('id_seller_address_city');
        const zip = document.getElementById('id_seller_address_zip');
        const text_label = document.getElementById('seller_zip_city_summ');
        if (!city.value && !zip.value) {
            text_label.innerText = '---------';
            return;
        }
        text_label.innerText = `${zip.value} ${city.value}`;
        return;
    }
    if (inputName.includes('seller_address_state')) {
        const field = document.getElementById('id_seller_address_state');
        const text_label = document.getElementById('seller_state_summ');
        text_label.innerText = field.options[field.selectedIndex].text;
    }
    if (inputName.includes('main_activity_countries')) {
        const countries = $('#id_main_activity_countries').val();
        const countryList = document.getElementById('main_activity_countries_summ');
        const countryFlagsURL = countryList.dataset.src;

        countryList.innerHTML = '';

        if (countries && countries.length > 0) {
            countries.forEach(countryCode => {
                countryCode = countryCode.toLowerCase();
                const li = document.createElement('li');
                li.classList.add('list-inline-item');

                const img = document.createElement('img');
                img.classList.add('img-fluid', 'wid-20');
                img.setAttribute('data-bs-toggle', 'tooltip');
                img.setAttribute('title', `${countryCode}`);
                img.setAttribute('src', `${countryFlagsURL}${countryCode}.svg`);
                img.style.borderRadius = '3px';
                img.onerror = function () {
                    this.src = `${countryFlagsURL}xx.svg`;
                };
                img.setAttribute('alt', 'img');

                li.appendChild(img);

                countryList.appendChild(li);
                new bootstrap.Tooltip(img);

            });
        }
    }
    if (inputName.includes('total_assets')){
        const total_assets_resume = document.getElementById('total_assets_resume');
        const total_assets = document.getElementById('id_total_assets');
        total_assets_resume.value = total_assets.value;
    }



}


