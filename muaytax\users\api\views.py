import json

from django.contrib.auth import get_user_model
from muaytax.users.permissions import CanCreateUserPermission
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.mixins import ListModelMixin, RetrieveModelMixin, UpdateModelMixin, CreateModelMixin
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet , ModelViewSet
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from rest_framework_simplejwt.views import TokenObtainPairView

from .serializers import RegisterSerializer
from rest_framework import generics

from django.http import JsonResponse
from google.oauth2 import id_token
from google.auth.transport import requests
from django.contrib.auth import login
from django.conf import settings


#from .serializers import UserSerializer
from .serializers import MyTokenObtainPairSerializer

User = get_user_model()


class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, CanCreateUserPermission]
    # permission_classes = (AllowAny,) # Only for test in localhost
    serializer_class = RegisterSerializer

class MyObtainTokenPairView(TokenObtainPairView):
    permission_classes = (AllowAny,)
    serializer_class = MyTokenObtainPairSerializer


def google_login_token(request):
    if request.method == "POST":
        data = json.loads(request.body)
        token = data.get("token")

        if not token:
            print("No token provided")
            return JsonResponse({'error': ''}, status=400)

        try:
            # Validar el token con Google
            idinfo = id_token.verify_oauth2_token(
                token,
                requests.Request(),
                settings.GOOGLE_CLIENT_ID 
            )

            email = idinfo.get('email')

            # Buscar o crear el usuario en tu base de datos
            user = User.objects.filter(email=email).first()
            if user is not None:
                user.backend = 'allauth.account.auth_backends.AuthenticationBackend' #Especificamos el backend del login de usuario
                login(request, user)

                return JsonResponse({'message': 'Login successful'})
            else:
                return JsonResponse({'error': 'Usuario no encontrado en la base de datos. '}, status=404)
        except ValueError as e:
            print("Error: ", e)
            return JsonResponse({'error': ''}, status=400)

    return JsonResponse({'error': ''}, status=405)