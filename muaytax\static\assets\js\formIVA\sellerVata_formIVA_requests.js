// === sellerVata_formIVA_requests.js ===

// Asegurar que el namespace principal existe
window.FormIVA = window.FormIVA || {};

// Definir módulo de peticiones
FormIVA.Requests = {
    // Petición para recargar el bloque de socios desde el backend
    async reloadPartnersBlock() {
        const container = document.getElementById("partners-info-container");
        const url = `${window.location.pathname}?action=get_partners_block`;

        try {
            DebugLogger.log("[Requests] 🔁 Recargando bloque de socios desde:", url);

            const response = await fetch(url);
            const data = await response.json();

            if (data.html && container) {
                container.innerHTML = data.html;

                DebugLogger.log("[Requests] ✅ Bloque de socios recargado con éxito");

                // Inicializar eventos nuevamente si es necesario
                if (typeof FormIVA.Events?.attachToPartners === "function") {
                    FormIVA.Events.attachToPartners();
                }
            }
        } catch (error) {
            DebugLogger.error("[Requests] ❌ Error recargando bloque de socios:", error);
        }
    },

    // Petición para guardar el formulario IVA parcial
    async saveForm(formData) {
        try {
            DebugLogger.log("[Requests] 💾 Guardando formulario con FormData:", formData);

            const response = await fetch(window.location.href, {
                method: "POST",
                body: formData
            });

            const data = await response.json();
            DebugLogger.log("[Requests] ✅ Formulario guardado correctamente:", data);

            Swal.fire({
                icon: 'success',
                title: 'Guardado',
                text: 'El formulario se ha guardado correctamente.'
            });

            return data;

        } catch (err) {
            DebugLogger.error("[Requests] ❌ Error al guardar formulario:", err);

            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Hubo un problema al guardar el formulario.'
            });

            return null;
        }
    },

    // Petición para enviar el formulario final procesado
    async submitFinalForm(url, formData, onSuccessRedirect) {
        try {
            DebugLogger.log("[Requests] 🚀 Enviando formulario FINAL a:", url);

            const response = await fetch(url, {
                method: "POST",
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                DebugLogger.log("[Requests] ✅ Formulario enviado correctamente:", result);
                window.location.href = onSuccessRedirect;
            } else {
                throw new Error(result.error || "Error desconocido.");
            }

        } catch (error) {
            DebugLogger.error("[Requests] ❌ Error al enviar formulario final:", error);

            Swal.fire({
                icon: "error",
                title: "Error al enviar",
                text: error.message || "Algo falló al enviar el formulario."
            });
        }
    }
};
