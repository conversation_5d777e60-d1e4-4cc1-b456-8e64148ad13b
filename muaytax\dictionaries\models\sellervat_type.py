from django.db import models

class SellerVatType(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=50,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=50,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )

    class Meta:
        verbose_name = "Tipo del proceso"
        verbose_name_plural = "Tipos de los procesos"
    
    def __str__(self):
        return self.description
    
# @admin.register(SellerVatType)
# class SellerVatTypeAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]
