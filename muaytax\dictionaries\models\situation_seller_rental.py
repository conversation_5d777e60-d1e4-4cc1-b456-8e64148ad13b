from django.db import models

class SituationSellerRental(models.Model):

    code = models.CharField(
        primary_key=True, 
        max_length=10,
        verbose_name="<PERSON>ó<PERSON>"
    )

    description = models.CharField(
        max_length=150,
        verbose_name="Descripción"
    )

    order = models.IntegerField(
        blank=True,
        null=True,
        verbose_name="Orden"
    )

    class Meta:
        verbose_name = "Situación del alquiler"
        verbose_name_plural = "Situaciones del alquiler"
    
    def __str__(self):
        return self.description
    
# @admin.register(SituationSellerRental)
# class SituationSellerRentalAdmin(admin.ModelAdmin):
#     list_display = ["code", "description", "order"]
#     search_fields = ["code", "description"]