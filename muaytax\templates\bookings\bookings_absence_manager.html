{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Soporte MuayTax - Ausencias
{% endblock title %}

{% block stylesheets %}
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
<link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
<!-- Limit Characters in Table Span -->
<style>
    #list-table td span {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50vw;
    }

    .table-head {
        position: sticky;
        top: 0;
        background-color: #f2f2f2;
        z-index: 1;
    }

    strong {
        font-weight: 600;
    }
</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp; 
                        Soporte
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'app_bookings:list_bookings_manager' user.username %}">Mis llamadas</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="">Ausencias</a>
                    </li>
                </ul>
            </div>
            <div class="col d-flex justify-content-end">
                <div class="">
                    <a href="{% url 'app_bookings:manager_absence_new' user.username %}" class="btn btn-primary">
                        Nueva ausencia
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card user-profile-list">
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-10 d-flex justify-content-center align-items-start">
                        <div class="input-group">
                            <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..." />
                        </div>
                    </div>
                    <div class="ccol-xl col-lg-2">
                        <select class="form-control form-select" id="filter-date">
                            <option value="all">Todas las ausencias</option>
                            <option value="past">Ausencias pasadas</option>
                            <option value="future">Ausencias futuras</option>
                        </select>
                    </div>
                </div>
                <div class="dt-responsive table-responsive">
                    <table id="list-table" class="table nowrap">
                        <thead class="table-head">
                            <tr>
                                <th>Fecha de la ausencia</th>
                                <th>Hora inicial de ausencia</th>
                                <th>Hora final de ausencia</th>
                                <th>¿Ausente todo el día?</th>
                                <th>Tipo de ausencia</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for object in object_list %}
                            <tr>
                                <td class="align-middle">
                                    <span class="hidden-date" style="display: none;">{{ object.date|date:"Y-m-d" }}</span>
                                    <span>{{ object.date|date:"d/M/Y"|lower }}</span>
                                </td>
                                <td class="align-middle">
                                    <span>{{ object.start_time|default:'-' }}</span>
                                </td>
                                <td class="align-middle">
                                    <span>{{ object.end_time|default:'-'}}</span>
                                </td>
                                <td class="align-middle">
                                    {% if object.is_all_day %}
                                    <span><i class="fa-solid fa-check fa-xl" style="color: #02c018;"></i></span>
                                    {% else %}
                                    <span><i class="fa-solid fa-xmark fa-xl" style="color: #c01802;"></i></span>
                                    {% endif %}
                                </td>
                                <td class="align-middle">
                                    <span>{{ object.get_absence_type_display|default:'-'}}</span>
                                </td>
                                <td class="align-middle text-center">
                                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="Editar"
                                        class="btn btn-icon btn-success" href="{% url 'app_bookings:manager_absence_update' user.username object.id %}">
                                        <i class="feather icon-edit"></i></a>
                                    <a 
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="top"
                                        title="Eliminar"
                                        class="btn btn-icon btn-danger delete-button"
                                        data-absence-id="{{ object.id }}" 
                                    >
                                        <i class="feather icon-trash-2"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- alert text -->
<div class="alert alert-info">
    <strong>Nota:</strong> Las ausencias que se creen en este apartado servirán para que los usuarios no puedan solicitar llamadas en los horarios que se especifiquen.
</div>

{% comment %} modal for delete confirmation {% endcomment %}
<div class="modal fade" id="deleteAbsenceModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmar Eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                ¿Estás seguro que quieres eliminar esta ausencia?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <a href="#" id="deleteAbsenceButton" class="btn btn-danger">Eliminar</a>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
{% block javascripts %}
<script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">

<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>

<!-- dataTable -->
<script>
    $(document).ready(function () {
        const dataTableOptions = {
            paging: false,
            searching: true,
            ordering: true,
            truncation: true,
            info: true,
            footer: true,
            language: {
                lengthMenu: "_MENU_",
                zeroRecords: "No se han encontrado productos.",
                info: "_TOTAL_ resultados. ",
                search: "Buscar:",
                infoEmpty: "No hay resultados que coincidan con su búsqueda.",
                infoFiltered: ""
            },
            columnDefs: [
                { targets: 4, orderable: false }
            ],
            dom: 'lrtip',
            fixedHeader: true,
        };

        const dataTable = $("#list-table").DataTable(dataTableOptions);

        $("#search").on("input", function () {
            const filtro = $(this).val();
            dataTable.search(filtro).draw();
        });

        $("#filter-date").on("change", function () {
            const filtro = $(this).val();
            const currentDate = new Date().toISOString().split('T')[0];
            dataTable.rows().every(function (rowIdx, tableLoop, rowLoop) {
                const rowDateISO = $(this.node()).find('.hidden-date').text(); // tomar el span oculto
                if (filtro === "past" && rowDateISO >= currentDate) {
                    $(this.node()).hide();
                } else if (filtro === "future" && rowDateISO < currentDate) {
                    $(this.node()).hide();
                } else {
                    $(this.node()).show();
                }
            });
            dataTable.draw();
        });

    });

    document.querySelectorAll('.delete-button').forEach(function(button) {
        button.addEventListener('click', function() {
            var deleteAbsenceModal = new bootstrap.Modal(document.getElementById('deleteAbsenceModal'));
            deleteAbsenceModal.show();

            var absenceId = button.getAttribute('data-absence-id');
            var deleteAbsenceButton = document.getElementById('deleteAbsenceButton');
            var deleteUrl = "{% url 'app_bookings:manager_absence_delete' user.username 'absence_id' %}".replace('absence_id', absenceId);
            deleteAbsenceButton.setAttribute('href', deleteUrl);
            deleteAbsenceButton.addEventListener('click', function() {
                deleteAbsenceButton.classList.add('disabled');
                deleteAbsenceButton.setAttribute('disabled', 'disabled');
            });
        });
    });
</script>
{% endblock javascripts %}
