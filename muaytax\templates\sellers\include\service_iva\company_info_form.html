{% load crispy_forms_filters crispy_forms_field %}
{% load custom_filters %}

<div id="company_info_form">
    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group row">
                    <label class="col-form-label required-label">Nombre de la Empresa</label>
                    {{ company_form.name|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Tipo de Entidad</label>
                    {{ company_form.legal_entity|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <div class="col-lg-12">
                        <div class="row g-2 mb-2">
                            <div class="col-12">
                                <label class="col-form-label required-label">Direccion de la Empresa</label>
                                {{ company_form.company_address.address|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row g-2 mb-2">
                            <div class="col-6">
                                <label class="col-form-label required-label">Ciudad</label>
                                {{ company_form.company_address.address_city|as_crispy_field }}
                            </div>
                            <div class="col-6">
                                <label class="col-form-label required-label">Provincia / Estado</label>
                                {{ company_form.company_address.address_state|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row g-2 mb-2">
                            <div class="col-4">
                                <label class="col-form-label required-label">Código Postal</label>
                                {{ company_form.company_address.address_zip|as_crispy_field }}
                            </div>
                            <div class="col-8">
                                <label class="col-form-label required-label">País</label>
                                {{ company_form.company_address.address_country|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group row">
                    <div class="col-lg-12">
                        <div class="row g-2 mb-2 d-flex" id="phone-row-wrapper" style="display: flex;">
                            <div id="phone-country-wrapper">
                                <label class="col-form-label required-label">Prefijo telefónico</label>
                                {{ company_form.phone_country|as_crispy_field }}
                            </div>
                            <div id="phone-input-wrapper">
                                <label class="col-form-label required-label">Teléfono</label>
                                {{ company_form.phone_national|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-lg-6">
                <div class="form-group row">
                    <label class="col-form-label required-label">Tipo de actividad</label>
                    {{ company_form.activity_type|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Productos / Servicios</label>
                    {{ company_form.products_and_services|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label"></label>
                    {{ company_form.desc_main_activity|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Dirección de Amazon (VIES)</label>
                    {{ company_form.amazon_vies_screenshot|as_crispy_field }}
                    <p id="amazon_vies_screenshot_fileinfo" class="text-muted"></p>
                </div>
                <div class="form-group row">
                    <label class="col-form-label required-label">Escrituras de la Empresa</label>
                    {{ company_form.business_deeds|as_crispy_field }}
                    <p id="business_deeds_fileinfo" class="text-muted"></p>
                </div>

                {% if legal_entity == "sl" %}
                    <div class="form-group row">
                        <label class="col-form-label required-label">Registro Mercantil (SL)</label>
                        {{ company_form.mercantile_registry|as_crispy_field }}
                        <p id="mercantile_registry_fileinfo" class="text-muted"></p>
                    </div>
                {% elif legal_entity == "llc" %}
                    <div class="form-group row">
                        <label class="col-form-label required-label">Registro Mercantil / Certificado LLC*</label>
                        {{ company_form.business_registry|as_crispy_field }}
                        <p id="business_registry_fileinfo" class="text-muted"></p>
                    </div>

                    <div class="form-group row">
                        <label class="col-form-label required-label">Certificate of Comparison (LLC)</label>
                        {{ company_form.comparison_certificate|as_crispy_field }}
                        <p id="comparison_certificate_fileinfo" class="text-muted"></p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
    // === Ajusta el ancho de los inputs de teléfono según el prefijo seleccionado ===
    function adjustPrefixFlexWidth(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone) {
        const selectedOption = prefixSelect.options[prefixSelect.selectedIndex];
        if (!selectedOption) return;

        const span = document.createElement("span");
        span.style.visibility = "hidden";
        span.style.position = "absolute";
        span.style.whiteSpace = "nowrap";
        span.style.fontSize = getComputedStyle(prefixSelect).fontSize;
        span.style.fontFamily = getComputedStyle(prefixSelect).fontFamily;
        span.innerText = selectedOption.textContent;
        document.body.appendChild(span);
        const textWidth = span.offsetWidth + 80;
        document.body.removeChild(span);

        const fullRowWidth = inputPhone.offsetWidth;
        let percent = (textWidth / fullRowWidth) * 100;
        percent = Math.max(25, Math.min(percent, 80));
        const rest = 100 - percent;

        prefixWrapper.style.flexBasis = `${percent}%`;
        phoneWrapper.style.flexBasis = `${rest}%`;

        window.debugLog(`[DYNAMIC WIDTH] Total: ${fullRowWidth}px | Prefijo ocupa: ${percent.toFixed(1)}%`);
    }

    // === Habilita o deshabilita el input de número según prefijo ===
    function togglePhoneField(prefixSelect, phoneInput) {
        if (!phoneInput) return;
        phoneInput.disabled = !prefixSelect.value;
        if (!prefixSelect.value) phoneInput.value = "";
    }

    // === Filtra los productos según tipo de actividad ===
    function filterProductServices(productsSelect, allOptions, type, selectedValue = "") {
        productsSelect.innerHTML = `<option value="">-----</option>`;
        if (!type) {
            productsSelect.disabled = true;
            return;
        }

        const filtered = allOptions.filter(opt => opt.product_service_type === type);
        filtered.forEach(opt => {
            const option = document.createElement("option");
            option.value = opt.code;
            option.textContent = `${opt.code} | ${opt.description}`;
            if (opt.code === selectedValue) option.selected = true;
            productsSelect.appendChild(option);
        });
        productsSelect.disabled = false;
    }

    // === Debug visual de todos los inputs en consola ===
    function debugFormFields(formId) {
        const form = document.getElementById(formId);
        const inputs = form.querySelectorAll("input, select, textarea");
        window.debugLog("[DEBUG FORM] Valores actuales:");
        inputs.forEach(input => {
            const id = input.id || "(sin id)";
            const name = input.name || "(sin name)";
            const value = input.type === "checkbox" ? input.checked : input.value;
            window.debugLog(`>> ${id} [name="${name}"] → ${value}`);
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        const data = {{ company_info_data|safe }};
        const allOptions = {{ all_product_services_json|safe }};

        const activityTypeSelect = document.getElementById("id_activity_type");
        const productsSelect = document.getElementById("id_products_and_services");

        const prefixSelect = document.getElementById("id_phone_country");
        const phoneInput = document.getElementById("id_phone_national");
        const prefixWrapper = document.getElementById("phone-country-wrapper");
        const phoneWrapper = document.getElementById("phone-input-wrapper");
        const inputPhone = document.getElementById("phone-row-wrapper");

        const validationData = {{ validation_company_info|safe }};

        // === Mostrar errores de validación ===
        if (validationData) {
            const allowedFields = [];
            for (const [fieldName, validation] of Object.entries(validationData)) {
                if (validation.status === "incorrecto" && validation.pending) {
                    allowedFields.push(fieldName);
                    const input = document.querySelector(`[name="${fieldName}"], #id_${fieldName}`);
                    if (input) {
                        input.classList.add("error-coment");
                        if (!input.parentElement.querySelector(".manager-card-comment")) {
                            const container = document.createElement("div");
                            container.className = "manager-card-comment mt-2";
                            container.innerHTML = `<div class="manager-card-title">Comentario del gestor:</div><div class="manager-card-text">${validation.comment || "Campo con error"}</div>`;
                            const fileInfo = document.getElementById(`${fieldName}_fileinfo`);
                            (fileInfo || input.parentElement).appendChild(container);
                        }
                    }
                }
            }
            if (!isNewForm) {
                window.debugLog("Bloqueando campos con errores...");
                lockCompanyInfoFields(allowedFields);
            }
        }

        // === Aplicar valores recibidos desde el backend ===
        for (const [key, value] of Object.entries(data)) {
            if (value && typeof value === "object" && "file" in value) {
                const fileInfo = document.getElementById(`${key}_fileinfo`);
                if (fileInfo && value.value) {
                    fileInfo.innerHTML = `Documento cargado: <a href="${value.file}" target="_blank">${value.value}</a>`;
                    fileInfo.classList.remove("d-none");
                }
                continue;
            }

            const input = document.querySelector(`[name="${key}"], #id_${key}`);
            if (!input || typeof value === "object") continue;

            input.type === "checkbox" ? input.checked = Boolean(value) : input.value = value;
        }

        // === Inicializar productos por tipo ===
        const selectedType = activityTypeSelect?.value;
        const selectedProductCode = data["products_and_services"] || "";
        if (selectedType) {
            filterProductServices(productsSelect, allOptions, selectedType, selectedProductCode);
        }
        productsSelect.disabled = !selectedProductCode;

        activityTypeSelect?.addEventListener("change", function () {
            filterProductServices(productsSelect, allOptions, this.value);
        });

        // === Prefijo dinámico ===
        togglePhoneField(prefixSelect, phoneInput);
        adjustPrefixFlexWidth(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone);
        prefixSelect?.addEventListener("change", () => {
            togglePhoneField(prefixSelect, phoneInput);
            adjustPrefixFlexWidth(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone);
        });

        // === Debug de formulario ===
        debugFormFields("company_info_form");
    });

</script>
