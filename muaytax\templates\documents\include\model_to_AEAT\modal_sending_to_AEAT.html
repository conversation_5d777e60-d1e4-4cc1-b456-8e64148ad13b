<div class="modal fade " id="modalSendAEAT" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalLabel">Presentación directa en Hacienda  </h5>
      </div>
      <div class="modal-body">
        <div class="container">
          <div class="col form-group">
            <h5 class=" text-center">
              <div class="alert alert-warning" role="alert">
                <b> <i class="fa-solid fa-triangle-exclamation"></i> &nbsp; Atención se va a enviar el modelo a la Agencia Estatal de Administración Tributaria</b>
              </div>
              Confirmación <b>PRESENTACIÓN DEL MODELO <span class= "modalmodel"></span></b>, comprueba que los datos son correctos antes de continuar: <br><br>
            </h5>
            <div >
              <div class="row" style="font-size: 130%">
                <div class="col">
                  <dt>Presentador:</dt>
                  <dd> MUAY TAX ADVISORS SL</dd>
                  <dt>Declarante:</dt>
                  <dd>{{seller.name}}</dd>
                  <dt>PDF del modelo</dt>
                  <dd><a href="#" id="modalLink" target="_blank">Ver modelo</a></dd>
                  <dt>TXT del modelo</dt>
                  <dd><a href="#" id="modaltxt" onclick="generate_txt('txt_in_modal')" >Descargar TXT</a></dd>
                  <span id= "modalid" class="d-none">
                </div>
                <div class="col">
                  <dt>Modelo:</dt>
                  <dd><span class= "modalmodel"></span></dd>
                  <dt>Ejercicio:</dt>
                  <dd> <span id= "modalyear"></span></dd>
                  <dt>Período:</dt>
                  <dd><span id= "modalperioddescription"></span></dd>
                  <span id= "modalperiod" class="d-none"></span>
                  <dt>Resultado:</dt>
                  <dd><span id= "modalResult"></span> <span id= "modalAmount"></span></dd>
                </div>
              </div>
              <div id= 'confirmCheckbox'>
                <br><br>
                <h5>Para finalizar el proceso marca la casilla de "Conforme" y pulsa en "Firmar y Enviar" </h5>
                <div class="form-check" style="font-size: 120%">
                  <input class="form-check-input" type="checkbox" value="" id="checkAgreed">
                  <label class="form-check-label" for="checkAgreed" required>
                    Conforme
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer d-flex justify-content-center">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal" data-bs-target="#modalSendAEAT" aria-label="Close">Cancelar
        </button>
        <button type="button" id="sendAEAT" class="btn btn-success" onclick="sendToAEAT('send')" disabled><b>Firmar y Enviar</b></button>
        <button type="button" id="sendDraftAEAT" class="btn btn-warning" onclick="sendToAEAT('draft')"><b>Solicitar validación y borrador</b></button>
        <button type="button" id="sendManual" class="btn btn-info" onclick="manualToAEAT(); generate_txt('txt_in_modal');"><b>Presentación manual</b></button>
      </div>
    </div>
  </div>
</div>