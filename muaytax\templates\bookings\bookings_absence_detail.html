{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
    Soporte MuayTax - Ausencias
{% endblock title %}

{% block stylesheets %}
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
<link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
<!-- Limit Characters in Table Span -->
<style>
    #list-table td span {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50vw;
    }

    .table-head {
        position: sticky;
        top: 0;
        background-color: #f2f2f2;
        z-index: 1;
    }
    #errors ul {
        margin: 0;
    }
</style>
{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col">
                <div class="page-header-title">
                    <h5 class="m-b-10">
                        <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
                        Soporte
                    </h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'app_bookings:list_bookings_manager' user.username %}">Mis llamadas</a>
                      </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'app_bookings:manager_absence_list' user.username %}">Ausencias</a>
                    </li>
                    <li class="breadcrumb-item">
                        {% if object.pk is not None %} 
                            <a href="">Editar ausencia</a>
                        {% else %}
                            <a href="">Nueva ausencia</a>
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="card">
    <div class="card-body">
      <form class="form-horizontal" method="post" enctype="multipart/form-data" action="">
        {% csrf_token %}
        
        {% if form.errors %}
        <div class="alert alert-danger" id="errors">
            {{ form.non_field_errors }}
        </div>
        {% endif %}
        

        {{ form.manager|as_crispy_field }}
        <div class="row">
            <div class="col-md-6">
                {{ form.date|as_crispy_field }}
            </div>
            <div class="col-md-6">
                {{ form.absence_type|as_crispy_field }}
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                {{ form.is_all_day|as_crispy_field }}
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                {{ form.start_time|as_crispy_field }}
            </div>
            <div class="col-md-6">
                {{ form.end_time|as_crispy_field }}
            </div>
        </div>
        <div class="alert alert-info">
            <p><b>INFORMACIÓN:</b></p>
            <p>Ingresa aquí la fecha y hora que no estarás disponible para recibir solicitudes de llamadas telefónicas</p>
            <p>Recuerda que puedes marcar la casilla para indicar que la ausencia será el día completo, o indicar las horas que estarás ausente</p>
            <p>Si deseas tener la posibilidad de crear una llamada para un vendedor aunque te encuentres ausente, selecciona "Ausencia general", en caso contrario "Bloquear horario". 
                Ten en cuenta que en ninguno de los dos casos el vendedor tendrá posibilidad de solicitar una llamada.</p>
        </div>
        <div class="control-group">
            <div class="controls">
            {% if object.pk is not None %} 
                <button type="submit" class="btn btn-primary">Actualizar</button>
            {% else %}
                <button type="submit" class="btn btn-primary">Guardar</button>
            {% endif %}
            </div>
        </div>
      </form>
    </div>
</div>

{% endblock content %}
{% block javascripts %}

{% endblock javascripts %}