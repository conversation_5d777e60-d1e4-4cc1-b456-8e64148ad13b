{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
Vendedores
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}" />
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/all/all.min-v5.15.4.css"> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>
  <!-- Limit Characters in Table Span -->
  <style>
    #list-table2 td span {
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;

    }
   .dataTables_filter {
      display: none;
    }

    .table-head {
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    .head_name {
      width: 40%;
    }
     .login{
      width: 18%;
    }
    .actions{
      width: 5%;
    }

  </style>


{% endblock stylesheets %}

{% block breadcrumb %}
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <div class="page-header-title">
          <h5 class="m-b-10">Vendedores</h5>
        </div>
        <ul class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
          </li>
          <li class="breadcrumb-item">
            <a href=".">Vendedores</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="row">
  <div class="col-lg-12">
    <div class="card user-profile-list">
      <div class="card-body">
        <div class="dt-responsive">
           <div class="row d-flex mt-3 mb-3">
            <!-- Search + Pagination -->
            <div class="col d-flex justify-content-center align-items-start">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="Buscar..."  oninput="search()" />
              </div>
            </div>
            <!-- Year -->
            <div class="col-2">
              <select class="form-select form-control" name="year-input" id="year" onchange="onChangePeriodYear()">
                <option value="2022">2022</option>
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025">2025</option>
              </select>
            </div>
          </div>
          <div id="div_table2" style="display:block;">
            <table id="seller-list-pending-invoices" class="table nowrap">
              <thead class="table-head">
                <tr>
                  <th style="width:75%;">Nombre</th>
                  <th style="width:10%;">Entidad</th>
                  <th style="width:10%;">Facturas pendientes</th>
                  <th style="width:10%;">Último acceso</th>
                  <th style="width:5%;">Acciones</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
          <div id="div_table1" style="display:none;">
            <table id="seller-list-table2" class="table nowrap" >
              <thead class="table-head">
                <tr>
                  <th class="head_name">Nombre</th>
                  <th class="login">Último acceso</th>
                  <th class="actions">Acciones</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block javascripts %}
<!-- JQUERY DATATABLES -->
<link rel="stylesheet" type="text/css" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables-v1.10.25.css">
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables-v1.10.25.js"></script>
<script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"></script>
<script>
    let showTXT = false;

    const ajaxData = (d) => {
        let tParams = "";
        let year = document.getElementById("year").value;
        if (year) {
            d.year = year
            tParams += "&year=" + year;
        }
        getTotals(tParams);
        return d
    }
    let table = null;
    let table2 = null;
    $(document).ready(function () {
        //Tabla de sellers sin txt
        table2 = $('#seller-list-pending-invoices').DataTable({
            "serverSide": false,
            "ajax": {
                "dataSrc": "data",
                "url": "{% url 'app_sellers:seller_list_dt' %}",
                "data": function (d) {
                    ajaxData(d);
                }
            },
            "language": {
                "lengthMenu": "_MENU_",
                "zeroRecords": "No se han encontrado vendedores.",
                "info": "_START_ a _END_ de un total de _TOTAL_",
                "search": "Buscar:",
                "infoEmpty": "No hay resultados que coincidan con su búsqueda.",
                "infoFiltered": "",
                "emptyTable": "Cargando...",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "previous": "Anterior",
                    "next": "Siguiente"
                },
            },
            "responsive": {
                "breakpoints": [
                    {"name": 'bigdesktop', "width": Infinity},
                    {"name": 'meddesktop', "width": 1480},
                    {"name": 'smalldesktop', "width": 1280},
                    {"name": 'medium', "width": 1188},
                    {"name": 'tabletl', "width": 1024},
                    {"name": 'btwtabllandp', "width": 848},
                    {"name": 'tabletp', "width": 768},
                    {"name": 'mobilel', "width": 480},
                    {"name": 'mobilep', "width": 320}
                ]
            },
            "autoWidth": true,
            "truncation": true,
            "paging": true,
            "searching": true,
            "lengthChange": false,
            "lengthMenu": [[50, 100, 200, -1], [50, 100, 200, 'Todos']],
            "createdRow": function (row, data, dataIndex) {
                const shortname = data.shortname; // Obtén el valor de shortname desde los datos
                const link = '/sellers/' + shortname + '/';
                $(row).attr('style', 'cursor: pointer;');
                $(row).attr('onclick', "window.location.href = '" + link + "';");
            },
            "columns": [
                {
                    "data": "name", "width": "75%",
                    "render": function (data, type, row) {
                        let html = '';
                        html += '<td class="align-middle">';
                        html += '<div class="d-inline-block">';
                        html += '<h6 class="m-b-0"><b>';

                        let name = row.name;
                        if (typeof name === 'string') {
                            const upperCaseSuffixes = ['SA', 'LLC', 'SL'];
                            const words = name.split(' ').map(function (word) {
                                if (upperCaseSuffixes.includes(word.toUpperCase())) {
                                    return word.toUpperCase();
                                } else {
                                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                                }
                            });
                            html += words.join(' ');
                        }
                        html += '</b>';
                        if (row.name.toLowerCase() !== row.user_name.toLowerCase()) {
                            html += ' - ' + row.user_name.split(' ').map(function (word) {
                                return word.charAt(0).toUpperCase() + word.slice(1);
                            }).join(' ');
                        }

                        html += '</h6>';
                        html += '<p class="m-b-0">' + row.email.toLowerCase() + '</p>';
                        html += '</div>';
                        html += '</td>';

                        return html;
                    }
                },
                {
                  "data": "legal_entity", "orderable": true,  
                  "render": function (data, type, row) {
                    let html = '<td class="align-middle">';
                    if (data) {
                      if (data == 'llc') {
                        html += 'LLC'
                      } else if (data == 'sa') {
                        html += 'SA'
                      } else if (data == 'sl') {
                        html += 'SL'
                      } else if (data == 'self-employed') {
                        html += 'Autónomo'
                      } else if (data == 'self-employed-outside') {
                        html += 'Autónomo (no ES)';
                      } else if (data == 'other') {
                        html += '<b><span style="color: green;">Otro</span></b>';
                      } else if (data == '') {
                        html += '<b><span style="color: red;"> - Sin Entidad - </span></b>';
                      } else {
                        html += '<b><span style="color: red;"> - ' + data + '- </span></b>';
                      }
                    } else {
                      // Añadir un SPAN en rojo y bold que ponga "- Sin Entidad -"
                      html += '<b><span style="color: red;"> - Sin Entidad - </span></b>';
                    }
                    html += '</td>';
                    return html;
                  }
                },
                {
                    "data": "inv_count",
                    "render": function (data, type, row) {
                        let html = '<td class="align-middle">';
                        html += '<span >'+data+'</span>';
                        html += '</td>';
                        return html;
                    }
                },
                {
                    "data": "last_login",
                    "render": function(data, type, row) {
                          if (data && (type === 'display' || type === 'filter')) {
                            const date = new Date(data);

                            const day = date.getDate().toString().padStart(2, '0');
                            const month = date.toLocaleString('default', { month: 'short' });
                            const year = date.getFullYear();
                            const hours = date.getHours().toString().padStart(2, '0');
                            const minutes = date.getMinutes().toString().padStart(2, '0');

                            const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;
                            
                            return formattedDate;
                        }
                        return data; // Para otros tipos, como 'sort'
                      }
                },
                {
                    "data": null, "width": "5%",
                    "orderable": false,
                    "render": function (data, type, row) {
                        let html = '<td class="align-middle text-center">';
                        html += '<a href="/sellers/' + row.shortname + '/" class="btn btn-icon btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Resumen Vendedor">';
                        html += '<i class="feather icon-edit"></i>';
                        html += '</a>';
                        html += '</td>';
                        return html;
                    },
                }
            ],
            "initComplete": function (settings, json) {
                table2.settings()[0].nTable.style.width = "100%";
            },
            "drawCallback": function (settings) {
                $('[data-bs-toggle="tooltip"]').tooltip();
            },
        });

        table = table2;

        let syncingTables = false; // Para evitar ciclos infinitos

        //Cambiar pagina tabla2 y reflejar en tabla
        table2.on('page.dt', function () {
            const pageInfo = table2.page.info();
            const currentPage = pageInfo.page;
            syncingTables = true;
            syncingTables = false;
        });
        table2.on('order.dt', function () {
            if (!syncingTables) {
                const order = table2.order();
                syncingTables = true;
                syncingTables = false;
            }
        });

    });

    function search() {
        var tipo = $("#search").val();
        table.search(tipo).draw();
    }

    const getTotals = (params) => {
        let p = params;

        if (!p || p == undefined || p == null || p == "") {
            p = "";
        } else if (p.charAt(0) == "&") {
            p[0] = "?";
        }
    }

    const onChangePeriodYear = () => {
        const year = document.getElementById('year');
        const urlembed = "{% url 'app_sellers:list' %}";
        const newUrl = urlembed + '?year=' + year.value;
        window.location.href = newUrl;
    }

    const onload = () => {
        const year = document.getElementById('year');
        year.value = '{{year}}';
    }

    onload();
</script>
<!-- JQUERY DATATABLES -->
{% endblock javascripts %}
